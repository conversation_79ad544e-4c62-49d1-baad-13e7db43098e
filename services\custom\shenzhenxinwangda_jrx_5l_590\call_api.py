# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : call_api.py
# Time       ：2024/11/29 下午10:48
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import re
import time
from typing import Any

import requests


class RequestUtil(object):
    """
    请求相关操作
    """

    @staticmethod
    def log_common_param(url, headers, params, body_data, log_number: int):
        """
        请求日志输出
        :param url:
        :param headers:
        :param params:
        :param body_data:
        :param log_number: 打印多少参数
        :return:
        """
        # if type(body_data) is str:
        #     body_param = body_data
        # else:
        #     body_param = json.dumps(body_data, ensure_ascii=False)
        if type(body_data) is dict:
            body_data = json.dumps(body_data, ensure_ascii=False)

        if body_data is not str:
            body_data = str(body_data)

        print(f"-->请求URL：{url}  请求体参数：\n{body_data[:log_number]}")
        if headers:
            print(f"-->请求头：\n{headers}")

        if params:
            print(f"-->请求参数：\n{params}")

    @staticmethod
    def get(url, params, to_json=True, headers: dict = None, body_data=None,
            check_res_code=True) -> Any:
        """
        Get请求
        :param url: 请求API
        :param params: 请求参数
        :param to_json: 是否需要将返回参数转成 `python dict` 类型
        :param headers
        :param body_data
        :param check_res_code 检查状态码
        :return:
        """
        # print(f"-->GET 请求URL：{url}  Params 参数如下：")
        # for k, v in params.items():
        #     print(f"KEY: {k}  VALUE: {v} ")
        print(f"----get------")

        param_list = []
        for k, v in params.items():
            # print(f"KEY: {k}  VALUE: {v} ")
            param_list.append(f"{k}={v}")

        if param_list:
            param_str = f"?{'&'.join(param_list)}"
        else:
            param_str = ""

        print(f"-->GET 请求URL：{url}{param_str}")

        if body_data:
            print(f"请求体Form参数：{body_data}")

        if headers:
            print(f"-->请求头：\n{headers}")

        res = requests.get(url, params, timeout=5, headers=headers, data=body_data)
        print(f"get res code: {res} msg: {res.text}")

        if check_res_code:
            if not res:
                # log.warning(f"get code: {res}  error: {res.text}")
                raise Exception("接口调用出错2002，请检查接口服务是否正常！")

        if to_json:
            return res.json()
        else:
            return res.text

    @classmethod
    def post_json(cls,
                  url,
                  body_data,
                  headers: dict = None,
                  params: dict = None,
                  to_json=True,
                  log_number: int = 300000,
                  timeout=5,
                  auth=None,
                  files: dict = None,
                  check_res_code=True
                  ) -> Any:
        """
        post 接口，并默认返回json数据
        请求头：content-type: application/json
        :param url: 请求API
        :param headers: 请求头
        :param params: 请求参数
        :param body_data: 请求体参数
        :param to_json: 是否需要将返回参数转成 `python dict` 类型
        :param log_number: 打印多少参数
        :param auth: auth
        :param timeout: timeout
        :param check_res_code: 是否检查状态码
        :param files
        :return:
        """
        # cls.log_common_param(url, headers, params, body_data, log_number)
        print(f"----post_json------")

        body_data_str = ""
        if type(body_data) in [dict, list]:
            body_data_str = json.dumps(body_data, ensure_ascii=False, indent=4)

        param_list = []

        if params:
            for k, v in params.items():
                param_list.append(f"{k}={v}")

        req_param = ""
        if param_list:
            req_param = f"?{'&'.join(param_list)}"

        if log_number:
            print(f"-->请求URL：{url}{req_param}  【Body Json】参数：\n{body_data_str[:log_number]}")

        if headers:
            print(f"-->请求头：\n{headers}")

        if auth:
            res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, auth=auth,
                                files=files)
        else:
            res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, files=files)

        print(f"post_json 响应参数 code: {res} msg: {res.text}")

        if check_res_code:
            if not res:
                # log.warning(f"post_json code: {res}  error: {res.text}")
                raise Exception("接口调用出错2003，请检查接口服务是否正常！")

        if to_json:
            return res.json()
        else:
            return res.text

    @classmethod
    def post_json_with_retries(cls,
                               url,
                               body_data,
                               headers: dict = None,
                               params: dict = None,
                               to_json=True,
                               log_number: int = 300000,
                               timeout=5,
                               auth=None,
                               files: dict = None,
                               check_res_code=True,
                               max_retries: int = 1
                               ):
        retry_delay = 0.2
        for retry in range(max_retries + 1):
            print(f"retry={retry}")
            try:
                # 使用try-except块包装发送数据的操作
                ret = cls.post_json(url, body_data, headers, params, to_json, log_number, timeout,
                                    auth, files, check_res_code)
                print(f"数据发送成功（第{retry + 1}次尝试）")
                return ret
                # break  # 如果发送成功，则退出循环
            except Exception as err:
                if retry < max_retries:
                    # 如果还有重试机会，则等待一段时间后再试
                    time.sleep(retry_delay)
                    print(f"第{retry + 1}次重发失败，错误信息：{err}")
                else:
                    # 如果达到最大重试次数，则记录最终错误并退出
                    # log_func(f"达到最大重试次数（{max_retries + 1}次），最终错误信息：{err}")
                    raise Exception(f"达到最大重试次数（{max_retries}次），最终错误信息：{err}")

    @classmethod
    def post_form(cls,
                  url,
                  body_data: dict,
                  headers: dict = None,
                  params: dict = None,
                  to_json=True,
                  log_number: int = 300000,
                  timeout=5,
                  files: dict = None,
                  check_res_code=True
                  ) -> Any:
        """
        post 接口，并默认返回json数据
        请求头：content-type: application/x-www-form-urlencoded
        :param url: 请求API
        :param headers: 请求头
        :param params: 请求参数
        :param body_data: 请求体参数
        :param to_json: 是否需要将返回参数转成 `python dict` 类型
        :param log_number:
        :param timeout:
        :param files:
        :param check_res_code:
        :return:
        """
        print(f"----post_form------")
        cls.log_common_param(url, headers, params, body_data, log_number)

        res = requests.post(url, data=body_data, headers=headers, params=params, timeout=timeout, files=files)
        print(f"post_form 响应参数 code: {res} msg: {res.text}")

        if check_res_code and not res:
            # log.warning(f"post_form code: {res} error: {res.text}")
            raise Exception("接口调用出错2004，请检查接口服务是否正常！")

        if to_json:
            return res.json()
        else:
            return res.text

    @classmethod
    def post_xml(cls, url, body_data: str, log_number=300000, headers=None, timeout: int = 5,
                 check_res_code=True):
        """
        post 接口
        请求头：content-type: application/xml
        :param url: 请求API
        :param body_data: 请求体
        :param log_number: 打印多少参数
        :param headers
        :param timeout
        :param check_res_code
        """
        print(f"----post_xml------")
        cls.log_common_param(url, {}, {}, body_data, log_number)

        h = {"Content-Type": "application/xml"}
        if headers:
            h.update(headers)

            print(f"请求头：{json.dumps(h, ensure_ascii=False)}")

        body_data = body_data.encode("utf-8")
        res = requests.post(url, data=body_data, headers=h, timeout=timeout)
        print(f"post_xml 响应参数 code:{res} msg:{res.text}")

        if check_res_code and not res:
            # log.warning(f"post_form code: {res} error: {res.text}")
            raise Exception("接口调用出错2004，请检查接口服务是否正常！")

        return res.text

    @classmethod
    def post_soap(cls, url, body_data: str, soap_action, log_number=300000,
                  check_res_code=True):
        """
        post 接口
        请求头：content-type: text/xml; charset=utf-8
        """
        pat = r"http://(.*?)/"  # noqa

        host = re.match(pat, url)
        if host:
            host = host.group(1)
        else:
            host = ""

        print(f"----post_soap------")
        cls.log_common_param(url, {}, {}, body_data, log_number)
        headers = {
            "Content-Type": "text/xml; charset=utf-8",
            "Host": host,
            "Content-Length": str(len(body_data)),
            "SOAPAction": soap_action
        }

        body_data = body_data.encode("utf-8")
        res = requests.post(url, data=body_data, headers=headers, timeout=5)
        print(f"post_soap 响应参数 code:{res} msg:{res.text}")

        if check_res_code and not res:
            # log.warning(f"post_form code: {res} error: {res.text}")
            raise Exception("接口调用出错2004，请检查接口服务是否正常！")

        return res.text


if __name__ == '__main__':
    api_host = f"http://127.0.0.1:8081"

    # 1. 登录
    login_url = f"{api_host}/common/Login"
    RequestUtil.post_json(login_url, {
        "userId": "",
        "password": "",
        "deviceId": ""
    })

    # 2. 获取工厂
    get_factory = f"{api_host}/common/FactoryInfoDownLoadV1"
    RequestUtil.post_json(get_factory, {
        "userId": "",
        "deviceId": ""
    })

    # 3. 条码校验
    check_url = f"{api_host}/test/MesWebServer.asmx"
    check_param = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <BindCarrierAndPcbV2 xmlns="WWW.SUNWODA.COM">
      <CARRIER_ID>sn001</CARRIER_ID>
      <PCB_SN>sn002|sn003</PCB_SN>
      <M_MACHINCENO></M_MACHINCENO>
      <M_EMP></M_EMP>
      <M_MO>507-MO2307078664-1301</M_MO>
    </BindCarrierAndPcbV2>
  </soap:Body>
</soap:Envelope>"""
    RequestUtil.post_soap(check_url, check_param,
                          soap_action="WWW.SUNWODA.COM/BindCarrierAndPcbV2")

    # 4. 上传数据
    data_url = f"{api_host}/commonApi/TackProduct.ashx"
    data_param = {
        "deviceId": "",
        "stationId": "PCM-610C-072",
        "userId": "",
        "moNumber": "507-MO2307078664-1301",
        "pcbSN": "D3VZ15KBT4021000PO2050",
        "carrierSN": "D3VZ15KBT4021000PO2050",
        "Result": "NG",
        "bigPanelUrlList": [],
        "dataList": [
            {
                "smallPanelId": "",
                "smallPanelTestResult": "",
                "smallPanelUrlList": [],
                "extend1": "",
                "extend2": "",
                "extend3": "",
                "extend4": "",
                "testItemList": [
                    {
                        "testCode": "COMP1004",
                        "tetstItem": "InspSolderAi",
                        "testValue": "100.00",
                        "badInfoList": [
                            {
                                "badcode": "9",
                                "badDes": "ExposeCopper",
                                "badPostion": "COMP1004"
                            }
                        ]
                    },
                    {
                        "testCode": "COMP1379",
                        "tetstItem": "InspTemplateMatch",
                        "testValue": "96.54",
                        "badInfoList": [
                            {
                                "badcode": "10",
                                "badDes": "InsufficientSolder",
                                "badPostion": "COMP1379"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    RequestUtil.post_json(data_url, data_param)
