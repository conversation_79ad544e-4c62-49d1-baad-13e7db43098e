# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/4/11 上午11:16
# Author     ：sch
# version    ：python 3.8
# Description：优力普
"""
import json
from typing import Any

from common import xutil, xrequest
from common.xutil import x_response, log
from vo.mes_vo import DataVo, OtherVo, ComboVo, ButtonVo
from engine.MesEngine import BaseEngine

global_data = {}


class YouLiPuEngine(BaseEngine):
    version = {
        "title": "youlipu release v1.1.0.7",
        "device": "203,303",
        "author": "sunchangheng",
        "feature": ["登录", "获取界面参数", "工单校验", "条码校验", "上传数据"],
        "release": """
date: 2023-04-11 11:14  init
date: 2023-04-12 10:30  登录、获取界面参数、工单校验、条码校验、上传数据
date: 2023-04-17 09:10  parentSerialNumber传空
""",
    }

    button = {
        "get_order": {
            "ui_name": "校验工单"
        }
    }

    combo = {
        "workshop": {
            "item": [

            ],
            "value": "",
            "ui_name": "车间"
        },
        "line": {
            "item": [

            ],
            "value": "",
            "ui_name": "线体"
        },
        "gw": {
            "item": [

            ],
            "value": "",
            "ui_name": "工位"
        },
        "gx": {
            "item": [

            ],
            "value": "",
            "ui_name": "工序"
        },
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081"
        },
        "lot_name": {
            "ui_name": "工单号码",
            "value": "MO68155314211"
        },

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        login_username = global_data.get("login_username")

        if not login_username:
            return x_response("false", f"未登录，请先登录！")

        api_url = data_vo.get_value_by_cons_key("api_url")
        lot_name = data_vo.get_value_by_cons_key("lot_name")
        gw_name = data_vo.get_value_by_cons_key("gw")
        gx_name = data_vo.get_value_by_cons_key("gx")

        # cache_data = xutil.CacheUtil.get_cache_data()
        # login_username = cache_data.get("login_username")

        log.info(data_vo.pcb_entity)

        board_list = []
        pcb_sn = data_vo.pcb_entity.pcb_barcode
        for board_entity in data_vo.pcb_entity.yield_board_entity():
            log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            is_bad = False
            comp_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_ng_code == "25":
                    is_bad = True

                if not comp_entity.repair_result:
                    comp_ng_list.append({
                        "defectType": comp_entity.type,
                        "defectCode": comp_entity.repair_ng_code,
                        "defectLocation": comp_entity.designator,
                        "defectPartNumber": comp_entity.part,
                    })

            board_list.append({
                "serialNmuber": barcode,
                "parentSerialNumber": "",
                "index": int(board_no),
                "isX": is_bad,
                "wipID": "",
                "Defects": comp_ng_list
            })

        data_url = f"{api_url}/WIP/Complete2"
        data_param = {
            "lotName": lot_name,
            "station_name": gw_name,
            "userID": login_username,
            "stepName": gx_name,
            "SCAN_TYPE": 99,
            "inputSerials": board_list
        }

        ret = xrequest.RequestUtil.post_json(data_url, data_param)

        if ret.get("errorcode") != 0:
            return x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('content')}")

        return x_response()

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        log.info(f"登录中....")
        login_username, login_password = other_dao.get_login_info()

        api_url = other_dao.get_value_by_cons_key("api_url")
        login_url = f"{api_url}/Login/doAction"
        login_param = {
            "usercode": login_username,
            "password": login_password
        }
        ret = xrequest.RequestUtil.post_json(login_url, login_param)
        if ret.get("errorcode") != 0:
            return x_response("false", f"mes接口异常，登录失败，error：{ret.get('content')}")

        global_data["is_login"] = True
        global_data["login_username"] = login_username

        return x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, other_param: Any):
        combo_key = combo_vo.get_combo_key()
        combo_ui_name = self.combo.get(combo_key, {}).get("ui_name")
        log.info(f"[{combo_ui_name}]被点击，正在获取[{combo_ui_name}列表]...")

        is_login = global_data.get("is_login")
        if not is_login:
            return x_response("false", f"未登录，请先登录！")

        cache_data = xutil.CacheUtil.get_cache_data()
        login_username = cache_data.get("login_username")
        api_url = combo_vo.get_value_by_cons_key("api_url")

        if combo_key == "workshop":
            # 获取车间
            get_workshop_url = f"{api_url}/Content/passstation/Wip_Normal_panel.aspx?Oper=get_workshop"

            param1 = {
                "user": login_username,
            }

            ret = xrequest.RequestUtil.get(get_workshop_url, param1)
            if not ret:
                return x_response("false", f"未获取到车间，{ret}")

            workshop_map = {}
            for item in ret:
                workshop_name = item.get("FullName")
                workshop_map[workshop_name] = item.get("Code")

            cache_data["workshop_map"] = workshop_map
            ret_list = list(workshop_map.keys())

        elif combo_key == "line":
            # 获取线体
            workshop_name = combo_vo.get_value_by_cons_key("workshop")
            log.info(f"使用[{workshop_name}]去获取线体...")

            if not workshop_name:
                return x_response("false", f"请先选择车间，再选择线体！")

            cache_data = xutil.CacheUtil.get_cache_data()
            workshop_map = cache_data.get("workshop_map", {})
            workshop_code = workshop_map.get(workshop_name)

            get_line_url = f"{api_url}/Content/passstation/Wip_Normal_panel.aspx?Oper=get_scx_bywork"
            param2 = {
                "user": login_username,
                "work_code": workshop_code
            }

            ret = xrequest.RequestUtil.get(get_line_url, param2)

            if not ret:
                return x_response("false", f"未获取到线体，{ret}")

            line_map = {}
            for item in ret:
                line_name = item.get("FullName")

                line_map[line_name] = item.get("Code")

            cache_data["line_map"] = line_map
            ret_list = list(line_map.keys())

        elif combo_key == "gw":
            # 获取工位
            line_name = combo_vo.get_value_by_cons_key("line")
            log.info(f"使用[{line_name}]去获取工位...")

            if not line_name:
                return x_response("false", f"请先选择线体，再选择工位！")

            cache_data = xutil.CacheUtil.get_cache_data()
            line_map = cache_data.get("line_map", {})
            line_code = line_map.get(line_name)

            get_gw_url = f"{api_url}/Content/passstation/Wip_Normal_panel.aspx?Oper=get_gw"
            param3 = {
                "user": login_username,
                "sxc_code": line_code
            }

            ret = xrequest.RequestUtil.get(get_gw_url, param3)

            if not ret:
                return x_response("false", f"未获取到工位，{ret}")

            gw_map = {}
            for item in ret:
                gw_name = item.get("FullName")
                gw_map[gw_name] = item.get("Code")

            cache_data["gw_map"] = gw_map
            ret_list = list(gw_map.keys())

        elif combo_key == "gx":
            # 获取工序
            gw_name = combo_vo.get_value_by_cons_key("gw")
            log.info(f"使用[{gw_name}]去获取工序...")

            if not gw_name:
                return x_response("false", f"请先选择工位，再选择工序！")

            cache_data = xutil.CacheUtil.get_cache_data()
            gw_map = cache_data.get("gw_map", {})
            gw_code = gw_map.get(gw_name)  # 工位Id

            get_gx_url = f"{api_url}/Content/passstation/Wip_Normal_panel.aspx?Oper=get_gx"
            param4 = {
                "gw_code": gw_code
            }

            ret = xrequest.RequestUtil.get(get_gx_url, param4)

            if not ret:
                return x_response("false", f"未获取到工序，{ret}")

            gx_map = {}
            for item in ret:
                gx_name = item.get("FullName")
                gx_map[gx_name] = item.get("Code")

            cache_data["gx_map"] = gx_map
            ret_list = list(gx_map.keys())
        else:
            return x_response("false", "其他错误，未定义的下拉框")

        xutil.CacheUtil.save_cache_data(cache_data)

        ret_data = {
            "new_items": ret_list
        }

        return x_response("true", json.dumps(ret_data))

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "get_order":
            # 获取工单
            api_url = btn_vo.get_value_by_cons_key("api_url")
            lot_name = btn_vo.get_value_by_cons_key("lot_name")
            line_name = btn_vo.get_value_by_cons_key("line")  # 工位
            gw_name = btn_vo.get_value_by_cons_key("gw")  # 工位
            gx_name = btn_vo.get_value_by_cons_key("gx")  # 工序

            login_username = global_data.get("login_username")

            if not login_username:
                return x_response("false", f"请先登录再继续获取工单！")

            get_order_url = f"{api_url}/Content/passstation/Wip_Normal_panel.aspx?Oper=getLotInfo"
            get_order_param = {
                "lotNo": lot_name,
                "stationName": gw_name,
                "stepName": gx_name,
                "User": login_username,
                "line": line_name
            }

            ret = xrequest.RequestUtil.get(get_order_url, get_order_param)
            if ret.get("state") != 1:
                return x_response("false", f"mes接口响应异常，获取工单失败，{ret.get('msg')}")

        return x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        login_username = global_data.get("login_username")

        if not login_username:
            return x_response("false", f"未登录，请先登录！")

        api_url = other_vo.get_value_by_cons_key("api_url")
        lot_name = other_vo.get_value_by_cons_key("lot_name")
        gw_name = other_vo.get_value_by_cons_key("gw")
        gx_name = other_vo.get_value_by_cons_key("gx")

        check_url = f"{api_url}/WIP/check2"
        check_param = {
            "lotName": lot_name,
            "inputSerials": [{"serialNumber": sn} for sn in other_vo.list_sn()],
            "station_name": gw_name,
            "userID": login_username,
            "stepName": gx_name,
            "SCAN_TYPE": 99
        }

        ret = xrequest.RequestUtil.post_json(check_url, check_param)

        if ret.get("errorcode") != 0:
            return x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('content')}")

        return x_response()
