# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : new_test_s3.py
# Time       ：2024/12/2 上午10:55
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import boto3
from botocore.client import Config
from datetime import datetime


endpoint_url = "http://lxeds.sunwoda.com:12001"
access_key = '4EQCFXZ4MTTYSSYM8BHE'
secret_key = 'pYbgu0Kw7VV0G9T0RJHqlLygW9JE2dlpD1W4RTqT'

# 创建一个会话（如果你已经在环境变量中设置了凭证，这一步可以省略）
session = boto3.Session(
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key
)


if __name__ == '__main__':
    # 创建 S3 客户端，并指定自定义终端节点和其他配置选项
    s3_client = session.client(
        's3',
        endpoint_url=endpoint_url,  # 自定义终端节点
        config=Config(
            s3={'addressing_style': 'path'},  # 使用路径样式 URL
            # signature_version='s3v4'  # 如果适用，可以指定签名版本
        ),
        region_name='us-east-1'  # 如果适用，可以指定区域名称
    )
    print(s3_client)

    try:
        # 获取所有存储桶
        response = s3_client.list_buckets()

        # 打印存储桶信息
        print("Bucket List:")
        for bucket in response['Buckets']:
            bucket_name = bucket['Name']
            creation_date = bucket['CreationDate'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"{bucket_name}\t{creation_date}")
    except Exception as e:
        print(f"Error occurred: {e}")

    bucket_name = "ddb"

    s3_client.head_bucket(Bucket=bucket_name)
    print("done")