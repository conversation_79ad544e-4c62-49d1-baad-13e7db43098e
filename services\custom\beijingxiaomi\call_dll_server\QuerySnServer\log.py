import os
import shutil
import sys
import time
from datetime import datetime, timed<PERSON><PERSON>


def get_current_timestamp():
    """获取当前时间戳"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]


def get_cur_path():
    # 获取当前可执行文件的路径
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        cur_path = os.path.dirname(sys.executable)
    else:
        # 如果是脚本运行
        cur_path = os.path.dirname(os.path.abspath(__file__))
    return cur_path

def clean_expired_log_file(log_dir_path):
    '''
    15天清理一次日志
    '''
    # RouteCommunication_2025-04-10.1.log
    # 清理query_sn_server日志文件, 文件名：query_sn_server_2025-04-11.log
    fifteen_days_ago = datetime.now() - timedelta(days=15)
    for filename in os.listdir(log_dir_path):
        start_tag = 'query_sn_server_'
        end_tag = '.log'
        log_date = filename[len(start_tag): -len(end_tag)]
        if filename.startswith(start_tag) and filename.endswith(end_tag):
            file_date = datetime.strptime(log_date, '%Y-%m-%d')
            if file_date < fifteen_days_ago:
                os.remove(os.path.join(log_dir_path, filename))

    # 清理RouteCommunication日志文件，在【/线控路由信息】路径下, 文件名：RouteCommunication_2025-04-10.1.log
    route_log_dir = os.path.join(log_dir_path, '线控路由信息')
    for filename in os.listdir(route_log_dir):
        start_tag = 'RouteCommunication_'
        end_tag = '.log'
        # 取出2025-04-10.1.log
        log_date = filename[len(start_tag):]
        # 取出2025-04-10
        log_date = log_date.split('.')[0]
        if filename.startswith(start_tag) and filename.endswith(end_tag):
            file_date = datetime.strptime(log_date, '%Y-%m-%d')
            if file_date < fifteen_days_ago:
                os.remove(os.path.join(route_log_dir, filename))

def output_log(message, add_header=True):
    try:
        cur_path = get_cur_path()

        # 创建 LOG 文件夹
        log_folder_path = os.path.join(cur_path, "Log")
        os.makedirs(log_folder_path, exist_ok=True)  # 创建目录，如果已存在则不会报错

        # 创建日志文件
        today = datetime.today().strftime('%Y-%m-%d')
        log_file_path = os.path.join(log_folder_path, f"query_sn_server_{today}.log")
        if not os.path.exists(log_file_path):
            # 说明已经是新的一天，则创建新的一天的日志(open时会自动创建)，同时检查原有日志看是否要做清理
            clean_expired_log_file(log_folder_path)

        if add_header:
            log_header = get_current_timestamp() + " ---Info--- "
        else:
            log_header = ""

        # 打开日志文件（追加模式）
        with open(log_file_path, "a", encoding="utf-8") as log_file:
            log_message = f"{log_header}{message}"

            # 输出到控制台同时写入日志文件
            print(log_message)
            log_file.write(log_message + "\n")

    except Exception as e:
        print(f"日志记录失败: {e}", file=sys.stderr)
