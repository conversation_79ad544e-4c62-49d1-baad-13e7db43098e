# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/21 下午2:38
# Author     ：sch
# version    ：python 3.8
# Description：江西立讯
"""

from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "jiangxilixun release v1.0.0.4",
        "device": "401,430,630",
        "feature": ["获取条码", "上传数据", "切换板式"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-21 16:00  获取条码，上传数据
date: 2024-06-24 14:21  1.部分参数修改：equipmentNo,terminalId,customerSn,defectCode  2.兼容`SnOrders`参数 
date: 2024-06-24 17:48  参数修改为SerialNumber
date: 2024-06-25 11:19  上传数据时，把所有报错打印出来(兼容客户接口返回)
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(获取条码)",
            "value": "http://127.0.0.1:8081/MES/CheckPanelGetSnListFile",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/MES/SnListGoFile",
        }
    }

    form = {
        "equipment_no1": {
            "ui_name": "设备号(1轨)",
            "value": "",
        },
        "equipment_no2": {
            "ui_name": "设备号(2轨)",
            "value": "",
        },
        "terminal_id1": {
            "ui_name": "站点id(1轨)",
            "value": "",
        },
        "terminal_id2": {
            "ui_name": "站点id(2轨)",
            "value": "",
        },
        "emp_no": {
            "ui_name": "用户工号",
            "value": "",
        },
        "customer_sn": {
            "ui_name": "客户序号",
            "value": "",
        },
        "defect_code_ui": {
            "ui_name": "defectCode",
            "value": "NGCODE",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.custom_str_to_chinese()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        emp_no = other_vo.get_value_by_cons_key("emp_no")
        customer_sn = other_vo.get_value_by_cons_key("customer_sn")

        if other_vo.get_track_index() == 1:
            equipment_no = other_vo.get_value_by_cons_key("equipment_no1")
            terminal_id = other_vo.get_value_by_cons_key("terminal_id1")
        else:
            equipment_no = other_vo.get_value_by_cons_key("equipment_no2")
            terminal_id = other_vo.get_value_by_cons_key("terminal_id2")

        pcb_sn = other_vo.get_pcb_sn()

        get_param = {
            "equipmentNo": equipment_no,
            "terminalId": terminal_id,
            "serialNumber": pcb_sn,
            "customerSn": customer_sn,
            "empNo": emp_no,
        }

        ret = xrequest.RequestUtil.post_json(api_url_check, get_param)

        if str(ret.get("status")) != "0":
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('message')}")

        sn_orders = ret.get('SnOrders', [])

        if not sn_orders:
            sn_orders = ret.get('snOrders', [])

        ret_list = []
        for item in sn_orders:
            ret_list.append(item.get('SerialNumber'))

        return self.x_response("true", ",".join(ret_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        customer_sn = data_vo.get_value_by_cons_key("customer_sn")
        defect_code_ui = data_vo.get_value_by_cons_key("defect_code_ui")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if pcb_entity.get_track_index() == 1:
            equipment_no = data_vo.get_value_by_cons_key("equipment_no1")
            terminal_id = data_vo.get_value_by_cons_key("terminal_id1")
        else:
            equipment_no = data_vo.get_value_by_cons_key("equipment_no2")
            terminal_id = data_vo.get_value_by_cons_key("terminal_id2")

        pcb_sn = pcb_entity.pcb_barcode

        only_board_sn = ""

        # defect_code = ""

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        board_data = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            # board_no = board_entity.board_no

            if not only_board_sn and barcode:
                only_board_sn = barcode

            comp_data_list = []

            defect_desc = ""

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    # defect_code = comp_entity.repair_ng_code
                    defect_desc = comp_entity.repair_ng_str

                    comp_data_list.append({
                        "itemName": comp_entity.designator,
                        "itemType": comp_entity.type,
                        "itemValue": comp_entity.repair_ng_code,
                        "itemDescription": comp_entity.repair_ng_str,
                        "result": comp_entity.get_final_result("PASS", "PASS", "NG"),
                        "testTime": test_time,
                        "endTime": end_time,
                    })

            board_data.append({
                "equipmentNo": equipment_no,
                "terminalId": terminal_id,
                "serialNumber": barcode,
                "customerSn": customer_sn,
                "empNo": emp_no,
                "defectCode": board_entity.get_repair_result("N/A", defect_code_ui),
                "defectDesc": defect_desc,
                "fileName": "",
                "itemTests": comp_data_list,
            })

        ret = xrequest.RequestUtil.post_json(api_url_data, board_data)  # noqa

        err_msg_list = []

        ix = 0
        for item in ret:

            ix += 1
            if str(item.get("status")) != "0":
                msg = item.get("message")
                err_msg_list.append(f"No: {ix} {msg}")

        if err_msg_list:
            err_msg = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，error:{err_msg}")

        return self.x_response()
