<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:soas="http://schemas.datacontract.org/2004/07/Soas.Model.Entities">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:Save_Tr_Common_DataInfo>
         <!--Optional:-->
         <tem:tr_Common_DataInfos>
            <!--Zero or more repetitions:-->
            <soas:Tr_Common_DataInfo>
               <!--Optional:-->
               <soas:Header>
                  <!--Optional:-->
                  <soas:LineNum>testing</soas:LineNum>
                  <!--Optional:-->
                  <soas:Object1>testing</soas:Object1>
                  <!--Optional:-->
                  <soas:Object10>testing</soas:Object10>
                  <!--Optional:-->
                  <soas:Object2>testing</soas:Object2>
                  <!--Optional:-->
                  <soas:Object3>testing</soas:Object3>
                  <!--Optional:-->
                  <soas:Object4>testing</soas:Object4>
                  <!--Optional:-->
                  <soas:Object5>testing</soas:Object5>
                  <!--Optional:-->
                  <soas:Object6>testing</soas:Object6>
                  <!--Optional:-->
                  <soas:Object7>testing</soas:Object7>
                  <!--Optional:-->
                  <soas:Object8>testing</soas:Object8>
                  <!--Optional:-->
                  <soas:Object9>testing</soas:Object9>
                  <!--Optional:-->
                  <soas:Created_by>testing</soas:Created_by>
                  <!--Optional:-->
                  <soas:Creation_date>testing</soas:Creation_date>
                  <!--Optional:-->
                  <soas:Defect_code>testing</soas:Defect_code>
                  <!--Optional:-->
                  <soas:Eqp_code>testing</soas:Eqp_code>
                  <!--Optional:-->
                  <soas:Ext1>testing</soas:Ext1>
                  <!--Optional:-->
                  <soas:Ext10>testing</soas:Ext10>
                  <!--Optional:-->
                  <soas:Ext2>testing</soas:Ext2>
                  <!--Optional:-->
                  <soas:Ext3>testing</soas:Ext3>
                  <!--Optional:-->
                  <soas:Ext4>testing</soas:Ext4>
                  <!--Optional:-->
                  <soas:Ext5>testing</soas:Ext5>
                  <!--Optional:-->
                  <soas:Ext6>testing</soas:Ext6>
                  <!--Optional:-->
                  <soas:Ext7>testing</soas:Ext7>
                  <!--Optional:-->
                  <soas:Ext8>testing</soas:Ext8>
                  <!--Optional:-->
                  <soas:Ext9>testing</soas:Ext9>
                  <!--Optional:-->
                  <soas:Ict_defect_rawtext>testing</soas:Ict_defect_rawtext>
                  <!--Optional:-->
                  <soas:Ict_retestcount>testing</soas:Ict_retestcount>
                  <!--Optional:-->
                  <soas:Id>testing</soas:Id>
                  <!--Optional:-->
                  <soas:Ini_password>testing</soas:Ini_password>
                  <!--Optional:-->
                  <soas:Line_type>testing</soas:Line_type>
                  <!--Optional:-->
                  <soas:Location>testing</soas:Location>
                  <!--Optional:-->
                  <soas:Mr_power_begin>testing</soas:Mr_power_begin>
                  <!--Optional:-->
                  <soas:Mr_power_deviation>testing</soas:Mr_power_deviation>
                  <!--Optional:-->
                  <soas:Mr_power_end>testing</soas:Mr_power_end>
                  <!--Optional:-->
                  <soas:Mr_running_deviation>testing</soas:Mr_running_deviation>
                  <!--Optional:-->
                  <soas:Point_code>testing</soas:Point_code>
                  <!--Optional:-->
                  <soas:R_sn>testing</soas:R_sn>
                  <!--Optional:-->
                  <soas:Result>testing</soas:Result>
                  <!--Optional:-->
                  <soas:S_sn>testing</soas:S_sn>
                  <!--Optional:-->
                  <soas:Scheme_code>testing</soas:Scheme_code>
                  <!--Optional:-->
                  <soas:Station_code>testing</soas:Station_code>
                  <!--Optional:-->
                  <soas:T_sn>testing</soas:T_sn>
                  <!--Optional:-->
                  <soas:Wo_code>testing</soas:Wo_code>
               </soas:Header>
               <!--Optional:-->
               <soas:Lines>
                  <!--Zero or more repetitions:-->
                  <soas:Tr_common_lInfo>
                     <!--Optional:-->
                     <soas:LineNum>testing</soas:LineNum>
                     <!--Optional:-->
                     <soas:Object1>testing</soas:Object1>
                     <!--Optional:-->
                     <soas:Object10>testing</soas:Object10>
                     <!--Optional:-->
                     <soas:Object2>testing</soas:Object2>
                     <!--Optional:-->
                     <soas:Object3>testing</soas:Object3>
                     <!--Optional:-->
                     <soas:Object4>testing</soas:Object4>
                     <!--Optional:-->
                     <soas:Object5>testing</soas:Object5>
                     <!--Optional:-->
                     <soas:Object6>testing</soas:Object6>
                     <!--Optional:-->
                     <soas:Object7>testing</soas:Object7>
                     <!--Optional:-->
                     <soas:Object8>testing</soas:Object8>
                     <!--Optional:-->
                     <soas:Object9>testing</soas:Object9>
                     <!--Optional:-->
                     <soas:Actual_deviation>testing</soas:Actual_deviation>
                     <!--Optional:-->
                     <soas:Allow_deviation>testing</soas:Allow_deviation>
                     <!--Optional:-->
                     <soas:Created_by>testing</soas:Created_by>
                     <!--Optional:-->
                     <soas:Creation_date>testing</soas:Creation_date>
                     <!--Optional:-->
                     <soas:Ext1>testing</soas:Ext1>
                     <!--Optional:-->
                     <soas:Ext10>testing</soas:Ext10>
                     <!--Optional:-->
                     <soas:Ext2>testing</soas:Ext2>
                     <!--Optional:-->
                     <soas:Ext3>testing</soas:Ext3>
                     <!--Optional:-->
                     <soas:Ext4>testing</soas:Ext4>
                     <!--Optional:-->
                     <soas:Ext5>testing</soas:Ext5>
                     <!--Optional:-->
                     <soas:Ext6>testing</soas:Ext6>
                     <!--Optional:-->
                     <soas:Ext7>testing</soas:Ext7>
                     <!--Optional:-->
                     <soas:Ext8>testing</soas:Ext8>
                     <!--Optional:-->
                     <soas:Ext9>testing</soas:Ext9>
                     <!--Optional:-->
                     <soas:Fct_com_port>testing</soas:Fct_com_port>
                     <!--Optional:-->
                     <soas:Header_id>testing</soas:Header_id>
                     <!--Optional:-->
                     <soas:Ict_actual_value>testing</soas:Ict_actual_value>
                     <!--Optional:-->
                     <soas:Ict_deviation>testing</soas:Ict_deviation>
                     <!--Optional:-->
                     <soas:Ict_measured_value>testing</soas:Ict_measured_value>
                     <!--Optional:-->
                     <soas:Ict_part_name>testing</soas:Ict_part_name>
                     <!--Optional:-->
                     <soas:Ict_pin_negactiveid>testing</soas:Ict_pin_negactiveid>
                     <!--Optional:-->
                     <soas:Ict_pin_postiveid>testing</soas:Ict_pin_postiveid>
                     <!--Optional:-->
                     <soas:Id>testing</soas:Id>
                     <!--Optional:-->
                     <soas:Sequence>testing</soas:Sequence>
                     <!--Optional:-->
                     <soas:Test_name>testing</soas:Test_name>
                     <!--Optional:-->
                     <soas:Test_result>testing</soas:Test_result>
                     <!--Optional:-->
                     <soas:Test_seq>testing</soas:Test_seq>
                     <!--Optional:-->
                     <soas:Test_value>testing</soas:Test_value>
                  </soas:Tr_common_lInfo>
               </soas:Lines>
               <!--Optional:-->
               <soas:Links>
                  <!--Zero or more repetitions:-->
                  <soas:MeterSnInfo>
                     <!--Optional:-->
                     <soas:IsChangeSN>testing</soas:IsChangeSN>
                     <!--Optional:-->
                     <soas:SN>testing</soas:SN>
                     <!--Optional:-->
                     <soas:SNType>testing</soas:SNType>
                     <!--Optional:-->
                     <soas:Sequence>testing</soas:Sequence>
                  </soas:MeterSnInfo>
               </soas:Links>
            </soas:Tr_Common_DataInfo>
         </tem:tr_Common_DataInfos>
         <!--Optional:-->
         <tem:sys_user_id>testing</tem:sys_user_id>
      </tem:Save_Tr_Common_DataInfo>
   </soapenv:Body>
</soapenv:Envelope>
