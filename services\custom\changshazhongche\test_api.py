# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/12/7 下午4:52
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">{"CODE":"NG","MESSAGE":"INTERFACE_ID ERROR","DATA":""}</string>
"""


if __name__ == '__main__':
    root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    print(root.text)