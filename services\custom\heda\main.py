# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test.py
# Time       ：2023/4/7 上午9:04
# Author     ：sch
# version    ：python 3.8
# Description：核达
"""
from typing import Any

from common import xcons, xutil
from common.xutil import x_response
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

content_template = """{process}
{barcode}
{line}
{board_no}
{username}
{order_id}
{start_time}
{repair_time}
{final_result}
{board_side}
{comp_number}
{comp_ng_number}
{comp_ng_list}
"""


class HeDaEngine(BaseEngine):
    version = {
        "title": "heda release v1.1.0.6",
        "device": "203B",
        "feature": ["生成文档"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-14 17:52  init
date: 2023-04-15 11:06  生成本地文档
date: 2023-04-28 09:49  换行符修改为window换行符
date: 2023-05-09 10:46  机种名不传BOM号，单拼板时，拼板ID=1
""",
    }

    path = {
        "data_path": {
            "ui_name": "保存路径",
            "value": ""
        }
    }

    form = {
        "line": {
            "ui_name": "线别",
            "value": ""
        },
        # "process": {
        #     "ui_name": "机种名",
        #     "value": ""
        # },
        "username": {
            "ui_name": "作业员",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_path = data_vo.get_value_by_cons_key("data_path")
        line = data_vo.get_value_by_cons_key("line")
        username = data_vo.get_value_by_cons_key("username")

        order_id = data_vo.pcb_entity.order_id
        inspect_time = data_vo.pcb_entity.get_start_time()
        review_time = data_vo.pcb_entity.get_review_time()
        board_side = data_vo.pcb_entity.board_side
        project_name = data_vo.pcb_entity.project_name
        pcb_name = data_vo.pcb_entity.pcb

        if not data_path:
            return x_response("false", f"请先选择保存路径！")

        time_file = inspect_time.strftime(xcons.FMT_TIME_FILE)
        if not order_id:
            order_id = "0"

        self.log.info(data_vo.pcb_entity)

        board_count = data_vo.pcb_entity.board_count
        for board_entity in data_vo.pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_count == 1:
                board_no = "1"

            if not barcode:
                barcode = time_file

            comp_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if not comp_entity.robot_result:
                    comp_result = comp_entity.repair_ng_str

                    if comp_entity.repair_result:
                        comp_num = 0
                    else:
                        comp_num = 1

                    comp_ng_list.append(f"{comp_entity.designator};{comp_entity.part};{comp_result};{comp_num}")

            # pass
            file_name = f"{data_path}/{line}_{time_file}_{board_no}.txt"
            file_content = content_template.format(**{
                "process": pcb_name,
                "barcode": barcode,
                "line": line,
                "board_no": board_no,
                "username": username,
                "order_id": order_id,
                "start_time": inspect_time.strftime("%Y/%m/%d %H:%M:%S"),
                "repair_time": review_time.strftime("%Y/%m/%d %H:%M:%S"),
                "final_result": board_entity.get_final_result("Pass", "Rpass", "Fail"),
                "board_side": board_side,
                "comp_number": board_entity.comp_total_number,
                "comp_ng_number": board_entity.comp_repair_ng_number,
                "comp_ng_list": "\n".join(comp_ng_list),
            })

            xutil.FileUtil.write_content_to_file(file_name, file_content.replace("\n", "\r\n"))

        return x_response()
