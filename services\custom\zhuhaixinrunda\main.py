# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/12/31 下午3:26
# Author     ：sch
# version    ：python 3.8
# Description：珠海鑫润达
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

csv_template = """设备名称,拼板序号,拼板条码,工单号,轨道,面别,测试时间,测试耗时,程序名,检测结果,复判结果,最终结果,复判操作员,器件总数,器件检测NG总数,器件复判NG总数,器件误报总数,不良代码,不良描述
{header_content}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{comp_data_str}
"""

header_template = """{设备名称},{拼板序号},{拼板条码},{工单号},{轨道},{面别},{测试时间},{测试耗时},{程序名},{检测结果},{复判结果},{最终结果},{复判操作员},{器件总数},{器件检测NG总数},{器件复判NG总数},{器件误报总数},{不良代码},{不良描述}"""

row_template = """
{拼板号},{拼板条码},{拼板检测结果},{拼板复判结果},{拼板最终结果},{器件位号},{器件料号},{器件封装},{器件类型},{器件检测不良代码},{器件检测结果},{器件复判不良代码},{器件复判结果},{器件图片路径}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "zhuhaixinrunda release v1.0.0.1",
        "device": "AIS203、AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-12-31 15:26  生成csv文档
""", }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "AOI",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
    }

    path = {
        "csv_save_path": {
            "ui_name": "保存路径(CSV)",
            "value": "",
        }
    }

    def __init__(self):
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["1"]["custom_code"] = "AIS01"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["2"]["custom_code"] = "AIS02"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["3"]["custom_code"] = "AIS03"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["4"]["custom_code"] = "AIS04"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["5"]["custom_code"] = "AIS05"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["6"]["custom_code"] = "AIS06"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["7"]["custom_code"] = "AIS07"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["8"]["custom_code"] = "AIS08"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["9"]["custom_code"] = "AIS09"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["10"]["custom_code"] = "AIS10"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["11"]["custom_code"] = "AIS11"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["12"]["custom_code"] = "AIS12"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["13"]["custom_code"] = "AIS13"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["14"]["custom_code"] = "AIS14"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["15"]["custom_code"] = "AIS15"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["16"]["custom_code"] = "AIS16"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["17"]["custom_code"] = "AIS17"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["18"]["custom_code"] = "AIS18"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["19"]["custom_code"] = "AIS19"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["20"]["custom_code"] = "AIS20"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["21"]["custom_code"] = "AIS21"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["22"]["custom_code"] = "AIS22"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["23"]["custom_code"] = "AIS23"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["24"]["custom_code"] = "AIS24"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["25"]["custom_code"] = "AIS25"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["26"]["custom_code"] = "AIS26"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["27"]["custom_code"] = "AIS27"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["28"]["custom_code"] = "AIS28"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["29"]["custom_code"] = "AIS29"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["30"]["custom_code"] = "AIS30"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["31"]["custom_code"] = "AIS31"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["32"]["custom_code"] = "AIS32"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["33"]["custom_code"] = "AIS33"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["34"]["custom_code"] = "AIS34"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["35"]["custom_code"] = "AIS35"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["36"]["custom_code"] = "AIS36"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["37"]["custom_code"] = "AIS37"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["38"]["custom_code"] = "AIS38"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["39"]["custom_code"] = "AIS39"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["40"]["custom_code"] = "AIS40"

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        csv_save_path = data_vo.get_value_by_cons_key("csv_save_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        order_id = data_vo.get_value_by_cons_key("order_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        cycle_time = pcb_entity.get_cycle_time()
        project_name = pcb_entity.project_name

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = "NOREAD"

            comp_data_str = ""
            comp_only_one_ng_code = ""
            comp_only_one_ng_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += row_template.format(**{
                    "拼板号": board_no,
                    "拼板条码": barcode,
                    "拼板检测结果": board_entity.get_robot_result(),
                    "拼板复判结果": board_entity.get_repair_result(),
                    "拼板最终结果": board_entity.get_final_result("PASS", "PASS", "NG"),
                    "器件位号": comp_entity.designator,
                    "器件料号": comp_entity.part,
                    "器件封装": comp_entity.package,
                    "器件类型": comp_entity.type,
                    "器件检测不良代码": comp_entity.robot_ng_code,
                    "器件检测结果": comp_entity.robot_ng_str,
                    "器件复判不良代码": comp_entity.repair_ng_code,
                    "器件复判结果": comp_entity.repair_ng_str,
                    "器件图片路径": comp_entity.image_path,
                })

                if comp_entity.is_repair_ng():
                    comp_only_one_ng_code = comp_entity.repair_ng_code
                    comp_only_one_ng_str = comp_entity.repair_ng_str

            header_content = header_template.format(**{
                "设备名称": device_name,
                "拼板序号": board_no,
                "拼板条码": barcode,
                "工单号": order_id,
                "轨道": pcb_entity.track_index,
                "面别": pcb_entity.board_side,
                "测试时间": start_time,
                "测试耗时": cycle_time,
                "程序名": project_name,
                "检测结果": board_entity.get_robot_result(),
                "复判结果": board_entity.get_repair_result(),
                "最终结果": board_entity.get_final_result("PASS", "PASS", "NG"),
                "复判操作员": pcb_entity.repair_user,
                "器件总数": board_entity.comp_total_number,
                "器件检测NG总数": board_entity.comp_robot_ng_number,
                "器件复判NG总数": board_entity.comp_repair_ng_number,
                "器件误报总数": board_entity.get_repass_number(),
                "不良代码": comp_only_one_ng_code,
                "不良描述": comp_only_one_ng_str
            })

            csv_content = csv_template.format(**{
                "header_content": header_content,
                "comp_data_str": comp_data_str
            })

            file_name = f"{csv_save_path}/{barcode}_{time_file}.csv"
            xutil.FileUtil.write_content_to_file_pro(file_name, csv_content)

        return self.x_response()
