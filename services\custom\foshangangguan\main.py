# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/04/25 上午9:22
# Author     ：gyr
# version    ：python 3.8
# Description：佛山港冠
"""
import re
from datetime import datetime, timedelta
from typing import Any

from common import xrequest
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["佛山港冠", "foshangangguan"],
        "version": "release v1.0.0.5",
        "device": "AIS40x",
        "feature": ["获取条码,上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-04-25 09:16  jira:ATAOI_2019-39067,获取条码+发送mes
date: 2025-04-25 15:30  jira:ATAOI_2019-39067,修改复判时间
date: 2025-07-11 12:59  CPSNS取获取条码后的|分隔最后的数字
date: 2025-07-11 15:51  SMT数据的ZD_CODE取扫到条码里的
date: 2025-07-11 18:35  test_time取机器测试时间
""", }

    form = {
        "api_url": {
            "ui_name": "接口地址",
            "value": ""
        },
        "line": {
            "ui_name": "线体",
            "value": ""
        },
        "order_number": {
            "ui_name": "工单号",
            "value": ""
        },
        "pro_processes": {
            "ui_name": "工序",
            "value": ""
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        获取条码
        根据扫描到的条码自动生成拼版条码
        条码格式示例：4|SMT01|M0000167673|12025031100306
        """
        api_url = other_vo.get_value_by_cons_key("api_url")
        order_number = other_vo.get_value_by_cons_key("order_number")
        pro_processes = other_vo.get_value_by_cons_key("pro_processes")

        sn_list = other_vo.get_pcb_sn()
        if not sn_list:
            return self.x_response('false', f"未获取到条码！")
        self.log.info(f"sn_list: {sn_list}")

        base_sn = sn_list
        self.log.info(f"获取到原始条码: {base_sn}")
        parts = base_sn.split("|")
        self.log.info(f"parts: {parts}")
        pb_str, line_code, order_code, serial = parts
        self.log.info(f"pb_str: {pb_str}, line_code: {line_code}, order_code: {order_code}, serial: {serial}")

        # 获取条码第一位(也就是获取有多少拼版)
        pb_count = int(parts[0])
        if pb_count <= 0:
            return self.x_response("false", "条码首位数字不正确")
        # 获取条码最后一位
        last_part = parts[-1]
        match = re.search(r'(\d+)$', last_part)
        if not match:
            return self.x_response('false', "条码末尾必须包含数字")
        base_number = match.group(1)
        base_prefix = last_part[:match.start(1)]
        current_time = (datetime.now() - timedelta(seconds=2)).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        base_sns = []
        for i in range(pb_count):
            new_number = str(int(base_number) + i).zfill(len(base_number))
            new_sn = f"{pb_str}|{line_code}|{order_code}|{base_prefix}{new_number}"
            base_sns.append(new_sn)
            self.log.info(f"生成条码 {i + 1}/{pb_count}: {new_sn}")

        full_sns = [f"{sn}|{pro_processes}|{current_time}" for sn in base_sns]

        request_data = {
            "recordcount": pb_count,
            "data": []
        }

        for sn in base_sns:
            serial = sn.split("|")[-1]
            request_data["data"].append({
                "PB": parts[0],
                "ZD_CODE": line_code,
                "WO_CODE": order_number,
                "CPSNS": serial,
                "WP_NAME": "SMT",  # SMT过站
                "JQTS": current_time,
                "JQJG": "",
                "SGTS": "",
                "SGJG": "",
                "NT_CODE": "",
                "CJP_ID": ""
            })

        try:
            # 为上游SMT过站
            ret = xrequest.RequestUtil.post_json(api_url, request_data)
            if str(ret.get("code")) != "100":
                return self.x_response("false", f"接口调用失败：{ret.get('msg', '未知错误')}")
        except Exception as e:
            return self.x_response("false", f"接口调用异常：{str(e)}")

        return self.x_response("true", ",".join(base_sns))

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        发送mes
        """
        api_url = data_dao.get_value_by_cons_key("api_url")
        line = data_dao.get_value_by_cons_key("line")
        order_number = data_dao.get_value_by_cons_key("order_number")
        pro_processes = data_dao.get_value_by_cons_key("pro_processes")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)
        board_number = pcb_entity.board_count
        repair_time = pcb_entity.get_review_time()

        if isinstance(repair_time, datetime):
            repair_time = repair_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        request_data = {
            "recordcount": board_number,
            "data": []
        }

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            serial = barcode.split("|")[-1]

            robot_result = board_entity.get_robot_result()
            repair_result = board_entity.get_repair_result()
            test_time = pcb_entity.get_start_time().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            # 不良代码
            ng_codes = []
            # 不良位号
            ng_designator = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    ng_codes.append(comp_entity.repair_ng_code)
                    ng_designator.append(comp_entity.designator)

            board_data = {
                "PB": board_number,
                "ZD_CODE": line,
                "WO_CODE": order_number,
                "CPSNS": serial,
                "WP_NAME": pro_processes,
                "JQTS": test_time,
                "JQJG": robot_result,
                "SGTS": repair_time,
                "SGJG": repair_result,
                "NT_CODE": ",".join(ng_codes) if ng_codes else "",
                "CJP_ID": ",".join(ng_designator) if ng_designator else ""
            }

            request_data["data"].append(board_data)

        try:
            ret = xrequest.RequestUtil.post_json(api_url, request_data)
            if str(ret.get("code")) != "100":
                return self.x_response("false", f"接口调用失败：{ret.get('msg', '未知错误')}")
        except Exception as e:
            return self.x_response("false", f"接口调用异常{str(e)}")

        return self.x_response()
