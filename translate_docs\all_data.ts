<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en" sourcelanguage="zh">
<context>
    <name>ConfigDialog</name>
    <message>
        <location filename="config_dialog.py" line="88"/>
        <source>个性化配置项</source>
        <translation>GeneralSetting</translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="93"/>
        <source>条码校验</source>
        <translation>CheckBarcode</translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="90"/>
        <source>校验全部条码</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="91"/>
        <source>仅校验第一个条码</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="92"/>
        <source>不校验第一个条码</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="94"/>
        <source>校验失败发送数据到Mes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="95"/>
        <source>校验失败不发送数据到Mes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="108"/>
        <source>发送Mes</source>
        <translation>SendDataToMes</translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="97"/>
        <source>保存全部器件图</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="98"/>
        <source>保存检测NG器件图</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="99"/>
        <source>保存复判NG器件图</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="100"/>
        <source>不保存器件图</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="102"/>
        <source>保存全部器件列表</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="103"/>
        <source>仅保存检测NG器件列表</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="104"/>
        <source>仅保存复判NG器件列表</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="105"/>
        <source>不保存器件列表</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="106"/>
        <source>不发送坏板</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="107"/>
        <source>发送坏板</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CronSettingDialog</name>
    <message>
        <location filename="cron_setting.py" line="127"/>
        <source>定时设置</source>
        <translation>CronSetting</translation>
    </message>
    <message>
        <location filename="cron_setting.py" line="128"/>
        <source>开启定时清除功能</source>
        <translation>Enable timed clear function</translation>
    </message>
    <message>
        <location filename="cron_setting.py" line="129"/>
        <source>固定时间清除</source>
        <translation>Fixed time clearing</translation>
    </message>
    <message>
        <location filename="cron_setting.py" line="131"/>
        <source>HH:mm</source>
        <translation></translation>
    </message>
    <message>
        <location filename="cron_setting.py" line="132"/>
        <source>间隔时间清除</source>
        <translation>Interval clearing</translation>
    </message>
    <message>
        <location filename="cron_setting.py" line="133"/>
        <source>小时</source>
        <translation>hour</translation>
    </message>
    <message>
        <location filename="cron_setting.py" line="134"/>
        <source>修改定时设置后重启本软件生效！！！</source>
        <translation>After modifying the timing settings, restarting this software will take effect!!!</translation>
    </message>
</context>
<context>
    <name>ErrorCodeWidget</name>
    <message>
        <location filename="error_code_map_widget.py" line="118"/>
        <source>自定义Mes不良代码</source>
        <translation>Custom Mes Bad Code</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="119"/>
        <source>机型:</source>
        <translation>Model:</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="120"/>
        <source>AIS203/AIS303/AIS40X/AIS50x</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="121"/>
        <source>AIS301</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="122"/>
        <source>AIS201</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="123"/>
        <source>AIS63X</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="125"/>
        <source>机型</source>
        <translation>Model</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="127"/>
        <source>默认Code</source>
        <translation>DefaultCode</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="129"/>
        <source>默认描述</source>
        <translation>Default Description</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="131"/>
        <source>自定义Code</source>
        <translation type="unfinished">CustomeCode</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="133"/>
        <source>自定义描述</source>
        <translation type="unfinished">CustomeDescription</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="135"/>
        <source>整体上传Mes</source>
        <translation type="unfinished">Overall upload of Mes</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="137"/>
        <source>单个上传Mes</source>
        <translation type="unfinished">Single upload of Mes</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="139"/>
        <source>配置项3</source>
        <translation type="unfinished">Config3</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="140"/>
        <source>保存</source>
        <translation type="unfinished">Save</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="141"/>
        <source>导入配置文件</source>
        <translation type="unfinished">Input Config File</translation>
    </message>
    <message>
        <location filename="error_code_map_widget.py" line="142"/>
        <source>导出配置文件</source>
        <translation type="unfinished">Ouput Config File</translation>
    </message>
</context>
<context>
    <name>Form</name>
    <message>
        <location filename="fake_send.py" line="271"/>
        <source>模拟主软件触发</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="272"/>
        <source>从Mes获取条码</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="299"/>
        <source>条码:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="274"/>
        <source>pcb_barcode001</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="303"/>
        <source>模拟触发</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="276"/>
        <source>发送设备状态</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="277"/>
        <source>设备状态:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="278"/>
        <source>进板</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="279"/>
        <source>开始检测</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="280"/>
        <source>停止检查</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="281"/>
        <source>出板</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="282"/>
        <source>安全门</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="283"/>
        <source>调试</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="284"/>
        <source>数据超时</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="285"/>
        <source>板卡 NG</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="286"/>
        <source>紧急故障</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="287"/>
        <source>流程错误</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="288"/>
        <source>直通率告警</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="289"/>
        <source>Marker 错误</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="290"/>
        <source>其他错误</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="292"/>
        <source>发送数据    (数据包路径也可直接复制过来)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="293"/>
        <source>检测完发送</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="294"/>
        <source>复判完发送</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="295"/>
        <source>数据包路径:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="296"/>
        <source>选择文件夹</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="298"/>
        <source>条码校验</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="300"/>
        <source>sn001,sn002,sn003</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="302"/>
        <source>发送空闲状态</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="304"/>
        <source>日志打印</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="fake_send.py" line="305"/>
        <source>清除日志</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoginWidget</name>
    <message>
        <location filename="login_widget.py" line="71"/>
        <source>登录</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="login_widget.py" line="67"/>
        <source>账号</source>
        <translation>UserName</translation>
    </message>
    <message>
        <location filename="login_widget.py" line="68"/>
        <source>密码</source>
        <translation>Password</translation>
    </message>
    <message>
        <location filename="login_widget.py" line="69"/>
        <source>记住密码</source>
        <translation>Remember Password</translation>
    </message>
    <message>
        <location filename="login_widget.py" line="70"/>
        <source>下次启动自动登录    (勾选此设置必须记住密码)</source>
        <translation>Next time start automatic login (check this setting to remember password)</translation>
    </message>
</context>
<context>
    <name>MesMainWindow</name>
    <message>
        <location filename="main_window.py" line="390"/>
        <source>Mes System</source>
        <translation></translation>
    </message>
    <message>
        <location filename="main_window.py" line="391"/>
        <source>简要日志</source>
        <translation>Brief log</translation>
    </message>
    <message>
        <location filename="main_window.py" line="392"/>
        <source>详细日志</source>
        <translation>Detailed log</translation>
    </message>
    <message>
        <location filename="main_window.py" line="393"/>
        <source>清除日志</source>
        <translation>Clear log</translation>
    </message>
    <message>
        <location filename="main_window.py" line="394"/>
        <source>发送统计</source>
        <translation>Send statistics</translation>
    </message>
    <message>
        <location filename="main_window.py" line="395"/>
        <source>发送总次数</source>
        <translation>SendDataNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="410"/>
        <source>0</source>
        <translation></translation>
    </message>
    <message>
        <location filename="main_window.py" line="397"/>
        <source>发送失败次数</source>
        <translation>FailedSendDataNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="399"/>
        <source>设备状态总次数</source>
        <translation>SendStatusNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="401"/>
        <source>设备状态失败次数</source>
        <translation>FailedSendStatusNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="403"/>
        <source>校验总次数</source>
        <translation>CheckBarcodeNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="405"/>
        <source>校验失败次数</source>
        <translation>FailedCheckBarcodeNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="407"/>
        <source>获取条码总次数</source>
        <translation>GetBarcodeNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="409"/>
        <source>获取条码失败次数</source>
        <translation>FailedGetBarcodeNumber</translation>
    </message>
    <message>
        <location filename="main_window.py" line="411"/>
        <source>计数清零</source>
        <translation>Clear Statistics</translation>
    </message>
    <message>
        <location filename="main_window.py" line="412"/>
        <source>定时清除</source>
        <translation>Timed clear</translation>
    </message>
    <message>
        <location filename="main_window.py" line="413"/>
        <source>参数配置</source>
        <translation>CONFIGURATION</translation>
    </message>
    <message>
        <location filename="main_window.py" line="414"/>
        <source>保存</source>
        <translation>Save</translation>
    </message>
    <message>
        <location filename="main_window.py" line="415"/>
        <source>设置</source>
        <translation>Setting</translation>
    </message>
    <message>
        <location filename="main_window.py" line="416"/>
        <source>工具</source>
        <translation>Tool</translation>
    </message>
    <message>
        <location filename="main_window.py" line="417"/>
        <source>帮助</source>
        <translation>Help</translation>
    </message>
    <message>
        <location filename="main_window.py" line="418"/>
        <source>系统</source>
        <translation>System</translation>
    </message>
    <message>
        <location filename="main_window.py" line="419"/>
        <source>其他设置</source>
        <translation type="unfinished">Other setting</translation>
    </message>
    <message>
        <location filename="main_window.py" line="420"/>
        <source>版本</source>
        <translation>Version</translation>
    </message>
    <message>
        <location filename="main_window.py" line="421"/>
        <source>关于</source>
        <translation>About</translation>
    </message>
    <message>
        <location filename="main_window.py" line="422"/>
        <source>设置开机自启</source>
        <translation>Set up self start upon startup</translation>
    </message>
    <message>
        <location filename="main_window.py" line="423"/>
        <source>模拟主软件触发</source>
        <translation>Fake send tool</translation>
    </message>
    <message>
        <location filename="main_window.py" line="424"/>
        <source>创建桌面快捷方式</source>
        <translation>Createdesktop shortcut</translation>
    </message>
    <message>
        <location filename="main_window.py" line="425"/>
        <source>登录</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="main_window.py" line="426"/>
        <source>其他参数设置</source>
        <translation>Other parameter settings</translation>
    </message>
    <message>
        <location filename="main_window.py" line="427"/>
        <source>一键发送日志</source>
        <translation>One click send log</translation>
    </message>
    <message>
        <location filename="main_window.py" line="428"/>
        <source>个性化配置项</source>
        <translation>General seting</translation>
    </message>
    <message>
        <location filename="main_window.py" line="429"/>
        <source>自定义Mes不良代码</source>
        <translation>Custom Mes Bad Code</translation>
    </message>
</context>
<context>
    <name>OtherSettingDialog</name>
    <message>
        <location filename="other_setting.py" line="81"/>
        <source>其他设置</source>
        <translation type="unfinished">Other setting</translation>
    </message>
    <message>
        <location filename="other_setting.py" line="82"/>
        <source>去除重复数据(发送mes)</source>
        <translation type="unfinished">Remove duplicate data (send mes)</translation>
    </message>
    <message>
        <location filename="other_setting.py" line="83"/>
        <source>合并上下板面数据</source>
        <translation type="unfinished">Merge upper and lower panel data</translation>
    </message>
    <message>
        <location filename="other_setting.py" line="84"/>
        <source>异常弹窗提示</source>
        <translation type="unfinished">Abnormal pop-up prompt</translation>
    </message>
    <message>
        <location filename="other_setting.py" line="85"/>
        <source>发送结果给维修工站</source>
        <translation type="unfinished">Send the results to the repair station</translation>
    </message>
    <message>
        <location filename="other_setting.py" line="86"/>
        <source>维修工站IP</source>
        <translation type="unfinished">repair ip</translation>
    </message>
</context>
<context>
    <name>ParamDialog</name>
    <message>
        <location filename="other_param_setting.py" line="57"/>
        <source>其他参数设置</source>
        <translation type="unfinished">Other param setting</translation>
    </message>
</context>
</TS>
