# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xparse.py
# Time       ：2023/2/2 下午5:01
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import os
import sqlite3
import time
from typing import <PERSON>ple

from common import xutil
from common.xutil import log, XmlUtil
from entity.MesEntity import PcbEntity


# def parse_bad_board_info(report_path: str):
#     """
#     解析坏板信息
#     :return:
#     """
#     root = XmlUtil.get_xml_root_by_file(report_path)
#
#     boards = root.find("boards")
#     order = root.find("order")
#
#     if order:
#         order_id = order.text
#     else:
#         order_id = ""
#
#     ret_dict = {}
#     for board in boards:
#         board_no = board.attrib.get("id")
#
#         bad_board = board.find("BadBoard").text
#
#         ret_dict[board_no] = {
#             "bad_board": bad_board,  # ("1", 坏板)  ("0", 好板)
#         }
#
#     return ret_dict, order_id

def get_image_data_by_review_path(
        review_path: str = '/home/<USER>/ais/results/160-145.zzw/20250109/T_20250109092133_1'
):
    """
    获取431图像转换比，以及缩放比
    """
    db_file = ""
    for item in os.listdir(review_path):
        if item.endswith(".db") and item != "AlgorithmInfo.db":
            db_file = os.path.join(review_path, item)

    if db_file and os.path.exists(db_file):
        # 连接到数据库（如果不存在则会自动创建）
        conn = sqlite3.connect(db_file)

        # 创建游标对象
        cursor = conn.cursor()

        cursor.execute("""
                        SELECT name 
                        FROM sqlite_master 
                        WHERE type='table' 
                        AND name=?
                    """, ("Report",))
        is_exist = cursor.fetchone()

        if is_exist:
            # 查询数据
            cursor.execute("SELECT pixelSizeWidth, pixelSizeHeight, imageScale  FROM Report;")
            row = cursor.fetchone()
        else:
            print("Report 表不存在")
            row = None

        # 关闭连接
        conn.close()
        return row
    else:
        return None


def parse_mes_data(
        data_path: str,
        send_board_no: list = None,
        image_type: int = 0,
        error_code_map: dict = None,
        comp_pad_data: dict = None,
        save_comp_type: int = 0,
        skip_bad_board: bool = True,
) -> Tuple[dict, dict]:
    """
    解析mes数据包
    :param data_path: 数据包路径， 如：/home/<USER>/aoi/run/results/333.001/20230331/T_20230331122121927_1_NG
    :param send_board_no: 分板发送的拼板ID
    :param image_type: (0, 保存全部器件图)  (1, 保存检测NG器件图)  (2, 保存复判NG器件图)  (3, 不保存器件图)
    :param error_code_map: 不良代码映射
    :param comp_pad_data: 锡膏的数据，630才有. 有的话，需要重新映射一下630的不良代码
    :param save_comp_type: (0, 保存全部器件列表)  (1, 仅保存检测NG器件列表)  (2, 仅保存复判NG器件列表)  (3, 不保存器件列表)
    :param skip_bad_board: 是否保存坏板数据 （True,不保存） （False,保存）
    :return:
    """
    mes_path = f"{data_path}/mes"
    project_xml = f"{mes_path}/project.xml"

    t1 = time.time()

    # if "checked" in mes_path or "aoi_ec_result" in mes_path:
    #     all_files = os.listdir(mes_path)
    #     pcb_image = ""
    #     for file in all_files:
    #         if file.endswith(".jpg"):
    #             pcb_image = f"{mes_path}/{file}"
    #             break
    # else:
    #     pcb_image = f"{data_path}/thumbnail/0/thumb.jpg"

    pcb_image = f"{data_path}/thumbnail/0/thumb.jpg"

    if not os.path.exists(pcb_image):
        pcb_image = ""
        all_files = os.listdir(mes_path)
        for file in all_files:
            if file.endswith(".jpg"):
                pcb_image = f"{mes_path}/{file}"
                break

    data_info_431 = get_image_data_by_review_path(data_path)

    # 1. 先解析 project.xml
    log.info(f"project xml:{project_xml}")
    project_root = XmlUtil.get_xml_root_by_file(project_xml)

    board_side = "T" if project_root.attrib.get("BoardSide") == "0" else "B"
    pcb_info = {
        "order_id": project_root.attrib.get("Order"),
        "pcb": project_root.attrib.get("Pcb"),
        "project_name": project_root.attrib.get("ProjectName"),
        "comp_count": project_root.attrib.get("CompCount"),
        "pcb_barcode": project_root.attrib.get("PcbBarcode"),
        "pcb_image": [pcb_image],
        "bom": project_root.attrib.get("Bom"),
        "track_index": int(project_root.attrib.get("TrackIndex", 0)) + 1,
        "board_count": project_root.attrib.get("BoardCount"),
        "fixture_barcode": project_root.attrib.get("FixtureBarcode"),
        "image_path": project_root.attrib.get("wholeBoardImagePath", ""),  # 431的图片路径
        "image_path_401": project_root.attrib.get("wholeBoardImage", ""),  # 401的图片路径
        "board_side": board_side,
        "review_time": "",
        "repair_user": "",
        "inspect_time": "",
        "cycle_time": "",

        # 惠州金百泽新增字段：进出板时间，fov数量
        "carry_in_time": project_root.attrib.get("CarryInTime", ""),
        "carry_out_time": project_root.attrib.get("CarryOutTime", ""),  # 可能会没有出板时间，因为还没出板就发Mes了
        "fov_count": project_root.attrib.get("FovCount", ""),
        "pixel_size_431": data_info_431  # 431机型的 像素转换比、图像压缩比
    }

    # 2. 解析器件和板卡数据
    board_data = {}
    pcb_robot_result = True
    pcb_user_result = True
    mes_dir = os.listdir(mes_path)
    filter_board_list = [str(i) for i in range(1000)]
    board_no_list = [i for i in mes_dir if i in filter_board_list]
    board_no_list.sort(key=lambda i: int(i))

    all_barcode = []

    if pcb_info.get("pcb_barcode"):
        all_barcode.append(pcb_info.get("pcb_barcode"))

    report_path = f"{data_path}/report.xml"

    if os.path.exists(report_path):
        report_data = xutil.XmlUtil.get_report_data(report_path)
        bad_data_map = report_data.get("bad_data_map", {})
        comp_data_map = report_data.get("comp_data_map", {})
        order_id_report = report_data.get("order_id", "")

        log.info(f"bad info: {bad_data_map}")
        board_no_list = list(bad_data_map.keys())

        if not pcb_info["order_id"] and order_id_report:
            pcb_info["order_id"] = order_id_report

    else:
        board_no_list = board_no_list

        comp_data_map = {}
        bad_data_map = {}

    real_board_no_list = []
    for board_no in board_no_list:
        if send_board_no and board_no not in send_board_no:
            log.warning(f"拼板[{board_no}]不在发送的分板发送列表中！")
            continue

        real_board_no_list.append(board_no)

    log.info(f"all board list: {board_no_list}")
    log.info(f"send board list: {real_board_no_list}")

    # 如果检测/复判出某种不良，则无需上传数据到Mes
    upload_mes = True

    pcb_info['board_ids'] = real_board_no_list

    for board_no in real_board_no_list:
        mes_dir = f"{mes_path}/{board_no}"

        robot_xml_file = f"{mes_dir}/robot.xml"
        log.info(f"robot xml:{robot_xml_file}")

        if not os.path.exists(robot_xml_file):
            # 过滤坏板
            log.warning(f"文件：{robot_xml_file}不存在")

            if not skip_bad_board:
                board_data[board_no] = {
                    "barcode": "",
                    "board_uuid": "",
                    "board_no": board_no,
                    "robot_result": False,
                    "user_result": False,
                    "final_result": "BadBoard",
                    "comp_total_number": 0,
                    "comp_robot_ng_number": 0,
                    "comp_repair_ng_number": 0,
                    "comp_data": {}
                }  # 坏板的数据结构

            continue

        robot_root = XmlUtil.get_xml_root_by_file(robot_xml_file)

        man_path = f"{mes_dir}/man.xml"
        board_image_path = ""

        is_repair = True  # 是否复判了

        if os.path.exists(man_path):
            log.info(f"man xml:{man_path}")
            man_root = XmlUtil.get_xml_root_by_file(man_path)

            board_image_path = man_root.attrib.get("BoardImagePath", "")

            # if "BoardImagePath" in man_root.attrib:
            #     log.info(f"从man.xml获取到的图片路径: {board_image_path}")
            #
            #     if board_image_path:
            #         log.info(f"获取到拼版图片路径: {board_image_path}")
            #         if os.path.exists(board_image_path):
            #             log.info("图片文件存在")
            #         else:
            #             log.warning("图片文件不存在")
            #     else:
            #         log.warning("未获取到图片路径")

            repair_comp_results = man_root.find("CompResults")

            repair_comp_map = {}

            comp_repair_ng_number = 0
            for item in repair_comp_results:
                comp_uuid = item.attrib.get("CompID")
                repair_ng_code = item.attrib.get("CompNGType")

                if repair_ng_code != "0":
                    comp_repair_ng_number += 1

                repair_ng_str = item.attrib.get("CompNGTypeStr")
                comp_tag = item.attrib.get("CompDesignator")
                if error_code_map:
                    if repair_ng_code == "34":
                        # 获取细分项的不良代码
                        comp_uuid = comp_uuid
                        comp_uuid = comp_uuid.replace("{", "").replace("}", "")
                        if comp_pad_data:
                            pad_list = comp_pad_data.get(comp_uuid, [])

                            if pad_list:
                                pad1 = pad_list[0]

                                for _item in pad_list:
                                    if _item.get('result') != '0':
                                        pad1 = _item
                                        break

                                if pad1:
                                    repair_ng_code = pad1.get("result")
                                    log.info(f"user ng code had changed! {comp_uuid} {repair_ng_code}")
                    else:
                        pass
                        # log.warning(f"SPI不良代码不是34！：{repair_ng_code}")

                    custom_ng_str = error_code_map.get(repair_ng_code, {}).get('custom_str', repair_ng_str)
                    custom_ng_code = error_code_map.get(repair_ng_code, {}).get('custom_code', repair_ng_code)

                    if custom_ng_str == "RepairSkip":
                        upload_mes = False
                        r = f"CompId：{comp_uuid} 位号:{comp_tag} NGCode:{custom_ng_code} 已配置RobotSkip，无需上传数据到Mes！"
                        log.warning(r)

                else:
                    custom_ng_str = repair_ng_str
                    custom_ng_code = repair_ng_code

                repair_comp_map[comp_uuid] = {
                    "repair_ng_str": custom_ng_str,
                    "repair_ng_code": custom_ng_code,
                    "origin_repair_ng_code": repair_ng_code,
                    "repair_auto_ng": item.attrib.get("CompRepairAutoNGTypeStr"),
                    "repair_auto_ng_code": item.attrib.get("CompRepairAutoNGType"),
                    "repair_checked_flag": item.attrib.get("CompCheckedFlag"),
                }

            review_time1 = man_root.attrib.get("ReviewTime")

            repair_user1 = man_root.attrib.get("RepairUser")

            repair_data = {
                "barcode": man_root.attrib.get("Barcode"),
                "result": True if man_root.attrib.get("BoardResult") == "0" else False,
                "review_time": review_time1 if review_time1 else man_root.get("Review"),
                "repair_user": repair_user1,
                "comp_repair_ng_number": comp_repair_ng_number,
                "comp_data": repair_comp_map,
            }

        else:
            log.warning(f"warning：No:{board_no} man.xml不存在！！！")
            is_repair = False

            repair_data = {
                "barcode": "",
                "result": False,
                "review_time": "1970-01-01 00:00:00",
                "repair_user": "",
                "comp_repair_ng_number": 0,
                "comp_data": {},
            }
        if robot_root.attrib.get("BoardResult") == "0":
            # 机器PASS
            robot_result = True
            user_result = True
            final_result = "PASS"

        else:
            # 机器NG
            robot_result = False

            if repair_data.get("result"):
                # 复判OK
                user_result = True
                final_result = "REPASS"
            else:
                # 复判NG
                user_result = False
                final_result = "NG"

        repair_barcode = repair_data.get("barcode", "")

        bad_info = bad_data_map.get(board_no, {})

        # (1, 坏板)  (0, 好板)
        bad_flag_431 = robot_root.attrib.get("BadBoard", "0")  # AIS431 机型的坏板标识

        if bad_info.get("bad_board") == "1" or bad_flag_431 == "1":
            # 坏板,   （正常不会走到这里，这里只是多做一层防呆！）
            log.warning(f"检测到坏板：{board_no}")

            if not skip_bad_board:
                board_data[board_no] = {
                    "barcode": "",
                    "board_uuid": "",
                    "board_no": board_no,
                    "robot_result": False,
                    "user_result": False,
                    "final_result": "BadBoard",
                    "comp_total_number": 0,
                    "comp_robot_ng_number": 0,
                    "comp_repair_ng_number": 0,
                    "comp_data": {}
                }  # 坏板的数据结构

            log.warning(f"跳过该坏板发送！")
            continue

        board_info = {
            "barcode": repair_barcode if repair_barcode else robot_root.attrib.get("Barcode"),
            "board_uuid": robot_root.attrib.get('BoardID'),
            "board_no": board_no,
            "robot_result": robot_result,
            "user_result": user_result,
            "final_result": final_result,
            "board_image_path": board_image_path
        }

        if not robot_result:
            pcb_robot_result = False

        if not user_result:
            pcb_user_result = False

        if not pcb_info.get("inspect_time"):
            pcb_info["inspect_time"] = robot_root.attrib.get("InspectTime")
        if not pcb_info.get("review_time"):
            pcb_info["review_time"] = repair_data.get("review_time")
        if not pcb_info.get("cycle_time"):
            pcb_info["cycle_time"] = robot_root.attrib.get("InspectTimeSec")

        if not pcb_info.get("repair_user"):
            pcb_info["repair_user"] = repair_data.get("repair_user", "")

        # 解析器件数据 start-------------------------
        robot_comp_map = {}

        robot_comp_results = robot_root.find("CompResults")

        comp_robot_ng_count = 0
        cop_total_count = 0
        for item in robot_comp_results:
            cop_total_count += 1
            algorithm_list = item.find("AlgorithmList")
            comp_uuid = item.attrib.get("CompID")
            comp_uuid = comp_uuid.replace("{", "").replace("}", "")

            comp_tag = item.attrib.get("CompDesignator")

            if algorithm_list is None:
                # err_msg = f"No:{board_no} 检测点->位号:{comp_tag} CompId:{comp_id} 没有算法"
                # log.warning(err_msg)
                algorithm_list = []
                # raise Exception()

            alg_data = []
            for alg_item in algorithm_list:
                alg_data.append({
                    "test_name": alg_item.attrib.get("TestName"),
                    "test_val": alg_item.attrib.get("TestVal"),
                    "result": alg_item.attrib.get("Result"),
                    "mix_threshold": alg_item.attrib.get("MinThreshold"),  # 兼容以前接口，不可以删除！！！
                    "min_threshold": alg_item.attrib.get("MinThreshold"),
                    "max_threshold": alg_item.attrib.get("MaxThreshold"),
                })

            # comp_id = item.attrib.get("CompID")

            comp_robot_ng_code = item.attrib.get("CompNGType")

            if comp_robot_ng_code != "0":
                comp_robot_ng_count += 1

            comp_src_image = item.attrib.get("CompImagePath")
            robot_ng_str = item.attrib.get("CompNGTypeStr")

            check_flag2 = True
            check_flag3 = False
            if error_code_map:
                if comp_robot_ng_code == "34":
                    # 获取细分项的不良代码
                    # comp_id = comp_id
                    comp_uuid = comp_uuid.replace("{", "").replace("}", "")

                    if comp_pad_data:
                        pad_list = comp_pad_data.get(comp_uuid, [])

                        if pad_list:
                            pad1 = pad_list[0]
                            if pad1:
                                comp_robot_ng_code = pad1.get("result")
                                log.info(f"user ng code had changed! {comp_uuid} {comp_robot_ng_code}")
                else:
                    pass

                custom_ng_str_robot = error_code_map.get(comp_robot_ng_code, {}).get('custom_str', robot_ng_str)
                custom_ng_code_robot = error_code_map.get(comp_robot_ng_code, {}).get('custom_code',
                                                                                      comp_robot_ng_code)

                upload_mes_tmp = error_code_map.get(comp_robot_ng_code, {}).get('upload_mes',
                                                                                True)

                if upload_mes_tmp is False:
                    log.warning(
                        f"CompId：{comp_uuid} 位号:{comp_tag} NGCode:{comp_robot_ng_code} 已在界面配置整体数据不上传Mes，无需上传数据到Mes！")
                    upload_mes = upload_mes_tmp

                check_flag2 = error_code_map.get(comp_robot_ng_code, {}).get('check_flag2', True)

                if check_flag2 is False:
                    # 这个不良不需要上传至mes
                    log.warning(
                        f"CompId：{comp_uuid} 位号:{comp_tag} NGCode:{comp_robot_ng_code} 已在界面配置单个不良不上传Mes，无需上传数据到Mes！")
                    continue

                check_flag3 = error_code_map.get(comp_robot_ng_code, {}).get('check_flag3', False)

            else:
                custom_ng_str_robot = robot_ng_str
                custom_ng_code_robot = comp_robot_ng_code

            if custom_ng_str_robot == "RobotSkip":
                upload_mes = False
                log.warning(
                    f"CompId：{comp_uuid} 位号:{comp_tag} NGCode:{comp_robot_ng_code} 已配置RobotSkip，无需上传数据到Mes！")

            comp_geometry = comp_data_map.get(comp_uuid, None)

            if not comp_geometry:
                # 兼容431
                # 尝试去获取431的坐标

                if data_info_431:
                    pixel_width = data_info_431[0]
                    pixel_height = data_info_431[1]
                    image_scale = data_info_431[2]

                    comp_geometry = {
                        "comp_id": "",
                        "cx": int(float(item.attrib.get("GeometryCx", 0)) / pixel_width / image_scale),
                        "cy": int(float(item.attrib.get("GeometryCy", 0)) / pixel_height / image_scale),
                        "angle": int(float(item.attrib.get("GeometryAngle", 0))),
                        "width": int(float(item.attrib.get("GeometryWidth", 0)) / pixel_width / image_scale),
                        "height": int(float(item.attrib.get("GeometryHeight", 0)) / pixel_height / image_scale),
                    }
                else:
                    comp_geometry = {
                        "comp_id": "",
                        "cx": 0,
                        "cy": 0,
                        "angle": 0,
                        "width": 0,
                        "height": 0,
                    }

            robot_comp_item = {
                "board_side": board_side,
                "robot_ng_code": custom_ng_code_robot,
                "robot_ng_str": custom_ng_str_robot,
                "origin_robot_ng_code": comp_robot_ng_code,
                "part": item.attrib.get("CompPart"),
                "image_path": comp_src_image,
                "type": item.attrib.get("CompType"),
                "designator": comp_tag,
                "package": item.attrib.get("CompPackage"),

                "x_offset": item.attrib.get("Xoffset"),
                "y_offset": item.attrib.get("Yoffset"),
                "x_pos": item.attrib.get("CompXPos"),
                "y_pos": item.attrib.get("CompYPos"),
                "width": item.attrib.get("CompWidth"),
                "height": item.attrib.get("CompHeight"),
                "alg_data": alg_data,
                "upload_mes": upload_mes,
                "check_flag2": check_flag2,
                "check_flag3": check_flag3,
                "geometry_obj": comp_geometry,
                "comp_id": comp_geometry.get("comp_id", "")
            }

            repair_comp_item = repair_data.get("comp_data").get(comp_uuid)

            if repair_comp_item:
                robot_comp_item.update(repair_comp_item)
            else:
                robot_comp_item.update({
                    "repair_ng_str": robot_comp_item.get("robot_ng_str", "unknown 408"),
                    "repair_ng_code": robot_comp_item.get("robot_ng_code", "unknown 408"),
                    "origin_repair_ng_code": robot_comp_item.get("origin_robot_ng_code", "unknown 408"),
                    "repair_auto_ng": "Unknown",
                    "repair_auto_ng_code": "-1",
                    "repair_checked_flag": "true"
                })

            if robot_comp_item.get("robot_ng_code") == "0":
                comp_final_result = "PASS"
            else:
                if robot_comp_item.get("repair_ng_code") == "0":
                    comp_final_result = "REPASS"
                else:
                    comp_final_result = "NG"

            if image_type == 3:
                robot_comp_item["image_path"] = ""
            elif image_type == 2:
                if comp_final_result in ["PASS", "REPASS"]:
                    robot_comp_item["image_path"] = ""
            elif image_type == 1:
                if comp_final_result in ["PASS"]:
                    robot_comp_item["image_path"] = ""

            robot_comp_item["final_result"] = comp_final_result

            if save_comp_type == 0:
                robot_comp_map[comp_uuid] = robot_comp_item
            elif save_comp_type == 1 and comp_final_result not in ["PASS"]:
                robot_comp_map[comp_uuid] = robot_comp_item
            elif save_comp_type == 2 and comp_final_result not in ["PASS", "REPASS"]:
                robot_comp_map[comp_uuid] = robot_comp_item

        # if not board_info.get("barcode") and repair_data.get("barcode"):
        #     log.warning(f"robot.xml没有条码，man.xml有条码: {repair_data.get('barcode')}")
        #     board_info["barcode"] = repair_data.get("barcode")

        # board_info["comp_total_number"] = len(robot_comp_map)
        repair_ng_number2 = repair_data.get("comp_repair_ng_number") if is_repair else comp_robot_ng_count

        board_info["comp_total_number"] = cop_total_count
        board_info["comp_robot_ng_number"] = comp_robot_ng_count
        board_info["comp_repair_ng_number"] = repair_ng_number2
        board_info["comp_data"] = robot_comp_map

        board_data[board_no] = board_info

        board_sn = board_info.get("barcode")
        if board_sn and board_sn not in all_barcode:
            all_barcode.append(board_sn)
        # 解析器件数据 end-------------------------

    log.info(f"parse time cost: {time.time() - t1}")

    pcb_info["pcb_robot_result"] = pcb_robot_result
    pcb_info["pcb_user_result"] = pcb_user_result
    pcb_info["review_path"] = [data_path]
    pcb_info["all_barcode"] = all_barcode
    pcb_info["upload_mes"] = upload_mes
    pcb_info["bad_data_map"] = bad_data_map

    pcb_robot_result = pcb_info.get("pcb_robot_result")
    pcb_user_result = pcb_info.get("pcb_user_result")

    if pcb_robot_result:
        final_result = "PASS"

    else:
        if pcb_user_result:
            final_result = "REPASS"
        else:
            final_result = "NG"

    pcb_info["final_result"] = final_result

    return pcb_info, board_data


def merge_mes_data(
        t_path: str,
        b_path: str,
        send_board_no: list = None,
        image_type: int = 0,
        error_code_map: dict = None,
        select_device: str = None,
        save_comp_type: int = 0,
        skip_bad_board: bool = True,
) -> Tuple[dict, dict, list]:
    """
    合并TB面数据
    t_path t面数据路径
    b_path b面数据路径
    error_code_map: 不良代码映射
    select_device: 不良代码映射的机型
    skip_bad_board: 是否跳过坏板数据
    """

    if select_device == "AIS63X":
        t_comp_pad_data = xutil.XmlUtil.get_pad_test_data(f"{t_path}/report.xml")
        b_comp_pad_data = xutil.XmlUtil.get_pad_test_data(f"{b_path}/report.xml")
    else:
        t_comp_pad_data = None
        b_comp_pad_data = None

    t_pcb_info, t_board_data = parse_mes_data(
        t_path, send_board_no, image_type, error_code_map, t_comp_pad_data, save_comp_type,
        skip_bad_board=skip_bad_board)
    b_pcb_info, b_board_data = parse_mes_data(
        b_path, send_board_no, image_type, error_code_map, b_comp_pad_data, save_comp_type,
        skip_bad_board=skip_bad_board)

    # pcb_entity list
    t_pcb_entity = PcbEntity(t_pcb_info, t_board_data)
    b_pcb_entity = PcbEntity(b_pcb_info, b_board_data)
    pcb_entity_list = [t_pcb_entity, b_pcb_entity]

    t_board = t_pcb_info.get("board_count", 0)
    b_board = b_pcb_info.get("board_count", 0)

    if t_board < b_board:
        board_info1 = b_board_data
        board_info2 = t_board_data
    else:
        board_info1 = t_board_data
        board_info2 = b_board_data

    t_comp_count = t_pcb_info.get("comp_count")
    t_board_count = t_pcb_info.get("board_count")
    t_pcb_barcode = t_pcb_info.get("pcb_barcode")
    t_pcb_robot_result = t_pcb_info.get("pcb_robot_result")
    t_pcb_user_result = t_pcb_info.get("pcb_user_result")

    b_comp_count = b_pcb_info.get("comp_count")
    b_board_count = b_pcb_info.get("board_count")
    b_pcb_barcode = b_pcb_info.get("pcb_barcode")
    b_pcb_robot_result = b_pcb_info.get("pcb_robot_result")
    b_pcb_user_result = b_pcb_info.get("pcb_user_result")

    # 合并后的数据
    total_comp_count = int(t_comp_count) + int(b_comp_count)
    board_count = b_board_count if b_board_count > t_board_count else t_board_count
    pcb_barcode = t_pcb_barcode if t_pcb_barcode else b_pcb_barcode

    pcb_robot_result = True
    pcb_user_result = True

    if not t_pcb_robot_result or not b_pcb_robot_result:
        pcb_robot_result = False

    if not t_pcb_user_result or not b_pcb_user_result:
        pcb_user_result = False

    t_pcb_info["comp_count"] = str(total_comp_count)
    t_pcb_info["pcb_barcode"] = pcb_barcode
    t_pcb_info["board_count"] = board_count
    t_pcb_info["pcb_robot_result"] = pcb_robot_result
    t_pcb_info["pcb_user_result"] = pcb_user_result
    t_pcb_info["board_side"] = "T+B"

    # 合并拼板数据
    for board_no, board_data1 in board_info1.items():
        board_data2 = board_info2.get(board_no, {})

        if not board_data2:
            continue

        # 以 board_data1 为基准合并数据
        if not board_data1.get("barcode"):
            board_data1["barcode"] = board_data2.get("barcode")

        if board_data2.get("robot_result") is False:
            board_data1["robot_result"] = False

        if board_data2.get("user_result") is False:
            board_data1["user_result"] = False

        board_data1["b_barcode"] = board_data2.get("barcode")

        _final_result = "NG"

        final_result1 = board_data1.get("final_result")
        final_result2 = board_data2.get("final_result")

        if final_result1 in ["PASS", "REPASS"] and final_result2 in ["PASS", "REPASS"]:
            _final_result = "REPASS"

        if final_result1 == "PASS" and final_result2 == "PASS":
            _final_result = "PASS"

        board_data1["final_result"] = _final_result

        board_data1["comp_total_number"] += board_data2.get("comp_total_number", 0)
        board_data1["comp_robot_ng_number"] += board_data2.get("comp_robot_ng_number", 0)
        board_data1["comp_repair_ng_number"] += board_data2.get("comp_repair_ng_number", 0)

        board_data1["comp_data"].update(board_data2.get("comp_data", {}))

        board_info1[board_no] = board_data1

    t_pcb_info["review_path"] = [t_path, b_path]

    pcb_image1 = t_pcb_info.get("pcb_image", [])
    pcb_image2 = b_pcb_info.get("pcb_image")
    # t_pcb_info["pcb_image"] = [pcb_image1, pcb_image2]

    if pcb_image2:
        pcb_image1.append(pcb_image2[0])

    t_pcb_info["pcb_image"] = pcb_image1

    # 合并所有条码
    t_all_barcode = t_pcb_info.get("all_barcode", [])
    b_all_barcode = b_pcb_info.get("all_barcode", [])

    for sn in b_all_barcode:
        if sn not in t_all_barcode:
            t_all_barcode.append(sn)

    t_pcb_info["all_barcode"] = t_all_barcode

    # 合并`upload_mes`
    b_upload_mes = b_pcb_info.get("upload_mes", True)
    if b_upload_mes is False:
        t_pcb_info['upload_mes'] = False

    pcb_robot_result = t_pcb_info.get("pcb_robot_result")
    pcb_user_result = t_pcb_info.get("pcb_user_result")

    if pcb_robot_result:
        final_result = "PASS"

    else:
        if pcb_user_result:
            final_result = "REPASS"
        else:
            final_result = "NG"

    # 合并坏板信息
    for board_no, b_bad_info in b_pcb_info.get("bad_data_map", {}).items():

        if b_bad_info.get("bad_board") == "1":
            t_pcb_info["bad_data_map"][board_no] = b_bad_info

    t_pcb_info["final_result"] = final_result

    return t_pcb_info, board_info1, pcb_entity_list


# if __name__ == '__main__':
#     r1, r2 = parse_mes_data("/Users/<USER>/Downloads/B_20230614171615389_1_NG")
#     print(r1)

if __name__ == '__main__':
    # bad_info = parse_bad_board_info('/home/<USER>/Desktop/日志文件/立景/BADMARK/T_20240325151733880_2_NG/report.xml')
    # print(json.dumps(bad_info, ensure_ascii=False, indent=4))
    # print(list(bad_info.keys()))
    pass
