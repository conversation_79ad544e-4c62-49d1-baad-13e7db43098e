# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/24 下午2:18
# Author     ：sch
# version    ：python 3.8
# Description：知识城/视源创新/视源睿创/cvte五产    430机型，双轨双任务单
"""
import json
import os
from typing import Any

from common import xrequest, xutil, xcons, xenum
from common.xutil import x_response, LimitedDict
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo, ButtonVo, ComboVo

global_data = {}

limit_global_data = LimitedDict(300)


# checkout_data_list = CircularList(300)

# jiadong_data_map = LimitedDict(300)


# jiadong_filter_list = CircularList(300)

def is_man_xml_exists(review_paths: str, ng_board_no: str) -> bool:
    """检查 man.xml 是否存在"""

    for review_path in review_paths.split(";"):
        man_xml_path = f"{review_path}/mes/{ng_board_no}/man.xml"
        if not os.path.exists(man_xml_path):
            return True
    return False


def refresh_token(api_url_token: str, email: str, pwd_encrypt: str, timeout=5):
    """
    刷新Token
    :param api_url_token:
    :param email:
    :param pwd_encrypt:
    :param timeout:
    :return:
    """
    ret = xrequest.RequestUtil.get(
        api_url_token, {"Email": email, "Pwd": pwd_encrypt},
        check_res_code=False,
        timeout=timeout
    )
    if not ret.get("success"):
        msg = ret.get("error", {}).get("message")
        return x_response("false", f"mes接口异常，获取token失败，error：{msg}")

    global_data["token"] = ret.get("result")

    return None


def cvte_post_request(api_url, param, headers):
    """
    统一的请求入口
    :param api_url:
    :param param:
    :param headers:
    :return:
    """
    return xrequest.RequestUtil.post_json(
        api_url,
        param,
        headers=headers,
        check_res_code=False
    )


"""
1. 进站 MoveInByHand
2. 出站 MoveOutStd
3. 稼动 UploadProductionInfo
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "cvte_430 release v1.0.0.28",
        "device": "430",
        "feature": ["条码校验", "上传数据", "上传稼动数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-26 14:21  条码校验，上传数据，上传稼动数据
date: 2024-07-26 16:21  所有请求带上token
date: 2024-07-26 16:21  token头自定义
date: 2024-07-27 09:21  修改上传顺序
date: 2024-07-30 16:51  修改请求参数
date: 2024-07-31 16:41  根据 任务单的isSplit参数 判断是否分板了，未分板时，只取一个SN上传进站/出站/稼动
date: 2024-08-01 17:32  修改请求参数
date: 2024-08-05 14:48  未扫到条码，停机报警！
date: 2024-08-07 18:57  result fixed 
date: 2024-10-09 15:56  PcsPassPointCount传拼板误报的器件数
date: 2024-10-18 10:59  [optimized]修正B面数据在第一位，会造成无法发送稼动数据
date: 2024-11-11 10:59  [optimized]修复数据异常：PcsBadPointCount、BadPointCount、PassPointCount、ProductionOKCount、ProductionPassCount
date: 2024-11-20 10:39  [optimized]PcsNum改成传拼板号
date: 2024-11-28 17:56  冻结【仅发送检测NG器件列表】配置项
date: 2024-12-04 17:50  修复未分板时，PassPointCount参数不对的问题
date: 2024-12-17 17:59  兼容分板发送Mes+分板发送稼动数据
date: 2024-12-17 23:40  增加稼动模式，模式2将会在发送mes的时候顺便发送稼动数据
date: 2025-01-07 16:02  任务单根据双轨切换
date: 2025-01-16 10:01  上传过站数据时，dataCollectionInfoInputs 按元件传递，只传复判NG的数据
date: 2025-01-16 11:00  修改dataCollectionInfoInputs参数
date: 2025-05-14 16:27  ATAOI_2019-39432：需要从mes获取条码，故将该接口移到【从mes获取条码】,ng时判断man.xml是否存在
""",
    }

    other_form = {
        "api_url_token": {
            "ui_name": "接口URL(获取token)",
            "value": "https://rc-cmfg.gz.cvte.cn/api/Account/CreateToken",
        },
        "api_url_get_task_list": {
            "ui_name": "接口URL(获取任务单列表)",
            "value": "https://rc-cmesprod.gz.cvte.cn/api/services/bas/TaskOrder/GetTaskListByMo",
        },
        "api_url_checkin": {
            "ui_name": "接口URL(条码进站)",
            "value": "https://rc-cmesprod.gz.cvte.cn/api/services/mf/SmdManufacture/MoveInByHand",
        },
        "api_url_checkout": {
            "ui_name": "接口URL(条码出站)",
            "value": "https://rc-cmesprod.gz.cvte.cn/api/services/mf/SmdManufacture/MoveOutStd",
        },
        "api_url_production_info": {
            "ui_name": "接口URL(稼动)",
            "value": "https://rc-cmesprod.gz.cvte.cn/api/services/mf/SmdManufactureProduction/UploadProductionInfo",
        },
        "token_header": {
            "ui_name": "token请求头",
            "value": "x-auth-token",
        },
    }

    form = {
        "mo_lot_no1": {
            "ui_name": "生产批次(1轨)",
            "value": "RCBJ-SKZBU23080012",
        },
        "mo_lot_no2": {
            "ui_name": "生产批次(2轨)",
            "value": "RCBJ-SKZBU23080013",
        },
        "email": {
            "ui_name": "账号",
            "value": "<EMAIL>",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "machine_code": {
            "ui_name": "设备编码",
            "value": "RC01008932",
        },
        "work_operation_code": {
            "ui_name": "工序编码",
            "value": "SC360",
        },
        "staff_id": {
            "ui_name": "staffId",
            "value": "",
        },
        "stage": {
            "ui_name": "位置号",
            "value": "126",
        },
        "operator_code": {
            "ui_name": "操作员编号",
            "value": "admin",
        },
        "command_type": {
            "ui_name": "参数类型",
            "value": "LUQIANAOI",
        },
    }

    combo = {
        "task_no_list1": {
            "ui_name": "任务单列表(1轨)",
            "item": [],
            "value": "",
        },
        "task_no_list2": {
            "ui_name": "任务单列表(2轨)",
            "item": [],
            "value": "",
        },
    }

    button = {
        "get_token_btn": {
            "ui_name": "获取Token",
        }
    }

    def __init__(self):
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 6)

        main_window.config_data["common_config"]["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG
        main_window.save_config_data_to_file()

        getattr(main_window.common_config_views, "sendmes_setting2").setEnabled(False)

        api_url_token = other_vo.get_value_by_cons_key("api_url_token")
        email = other_vo.get_value_by_cons_key("email")
        password = other_vo.get_value_by_cons_key("password")
        pwd_encrypt = xutil.OtherUtil.get_md5_sign(password).lower()

        refresh_token(api_url_token, email, pwd_encrypt, timeout=1)
        self.log.info(f"自动获取Token成功！")

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_checkin = other_vo.get_value_by_cons_key("api_url_checkin")
        work_operation_code = other_vo.get_value_by_cons_key("work_operation_code")
        token_header = other_vo.get_value_by_cons_key("token_header")
        track_index = other_vo.get_track_index()

        if track_index == 1:
            task_no_select = other_vo.get_value_by_cons_key("task_no_list1")
        else:
            task_no_select = other_vo.get_value_by_cons_key("task_no_list2")

        sn = other_vo.get_pcb_sn()
        if not sn:
            return self.x_response("false", f"轨道{track_index} 未扫到条码！")

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"请先获取Token！")

        headers = {
            f"{token_header}": f"{token}"
        }

        checkin_param = {
            "barcode": sn,
            "taskOrderNo": task_no_select,
            "workOperationCode": work_operation_code,
        }

        ret = cvte_post_request(api_url_checkin, checkin_param, headers=headers)
        if not ret.get("success"):
            msg = ret.get("error", {}).get("message")
            return self.x_response("false", f"轨道{track_index} mes接口异常，条码进站失败，error：{msg}")

        result = ret.get("result", {})

        if not result:
            result = {}
        result_items = result.get("items", [])
        unique_id = result.get("uniqueId", "")
        barcode_list = []
        for item in result_items:
            barcode_ = item.get("barcode")
            barcode_list.append(barcode_)
            limit_global_data.add_item(barcode_, unique_id)
        limit_global_data.add_item(sn, unique_id)
        self.log.info(f"SN:{sn} --> uniqueId:{unique_id} 已缓存！")

        return self.x_response("true", ",".join(barcode_list))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_checkin = other_vo.get_value_by_cons_key("api_url_checkin")
        work_operation_code = other_vo.get_value_by_cons_key("work_operation_code")
        token_header = other_vo.get_value_by_cons_key("token_header")
        track_index = other_vo.get_track_index()

        if track_index == 1:
            task_no_select = other_vo.get_value_by_cons_key("task_no_list1")
            task_map = xutil.CacheUtil.get("task_map1", {})
        else:
            task_no_select = other_vo.get_value_by_cons_key("task_no_list2")
            task_map = xutil.CacheUtil.get("task_map2", {})

        is_split = task_map.get(task_no_select)
        self.log.info(f"is_split: {is_split}")

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", f"轨道{track_index} 未扫到条码！")

        ret_res = self.x_response()

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"请先获取Token！")

        headers = {
            f"{token_header}": f"{token}"
        }

        if not is_split:
            # 对应的is_split字段为false，表示当前任务单工序未分板，上传进站/出站/稼动时，仅取一个SN提交一次
            sn_list = sn_list[:1]
            self.log.info(f"该程序未分板，仅取一个SN提交一次！")
        else:
            self.log.info(f"该程序已做分板，按拼板上传！")

        for sn in sn_list:
            checkin_param = {
                "barcode": sn,
                "taskOrderNo": task_no_select,
                "workOperationCode": work_operation_code,
            }

            ret = cvte_post_request(api_url_checkin, checkin_param, headers=headers)
            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                ret_res = self.x_response("false", f"轨道{track_index} mes接口异常，条码进站失败，error：{msg}")

            result = ret.get("result", {})

            if not result:
                result = {}

            unique_id = result.get("uniqueId", "")
            limit_global_data.add_item(sn, unique_id)
            self.log.info(f"SN:{sn} --> uniqueId:{unique_id} 已缓存！")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_checkout = data_vo.get_value_by_cons_key("api_url_checkout")
        work_operation_code = data_vo.get_value_by_cons_key("work_operation_code")
        machine_code = data_vo.get_value_by_cons_key("machine_code")
        staff_id = data_vo.get_value_by_cons_key("staff_id")
        token_header = data_vo.get_value_by_cons_key("token_header")

        # jd_type = data_vo.get_value_by_cons_key("jd_type")
        command_type = data_vo.get_value_by_cons_key("command_type")
        operator_code = data_vo.get_value_by_cons_key("operator_code")
        stage = data_vo.get_value_by_cons_key("stage", to_int=True)
        api_url_production_info = data_vo.get_value_by_cons_key("api_url_production_info")

        inspect_type = data_vo.get_inspect_type()
        if inspect_type == "inspector":
            self.log.warning(f"检测完就发Mes！本次不发送，等到复判后再发Mes！")
            return self.x_response()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        for board_entity in pcb_entity.yield_board_entity():
            if board_entity.get_final_result() == "NG":
                if is_man_xml_exists(data_vo.get_review_path(), board_entity.board_no):
                    return self.x_response("false", "复判数据异常，请检查！")

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"请先获取Token！")

        headers = {
            f"{token_header}": f"{token}"
        }

        board_index_list = other_data.get("board_index_list", [])

        only_one_sn = pcb_entity.pcb_barcode
        only_one_cache_unique_id = ""

        pcb_comp_data_list = []

        pcb_sn = pcb_entity.pcb_barcode

        lane_id = pcb_entity.track_index
        self.log.info(f"轨道：{lane_id}")

        if lane_id == 1:
            task_no_select = data_vo.get_value_by_cons_key("task_no_list1")
            task_map = xutil.CacheUtil.get("task_map1", {})

        else:
            task_no_select = data_vo.get_value_by_cons_key("task_no_list2")
            task_map = xutil.CacheUtil.get("task_map2", {})

        is_split = task_map.get(task_no_select)
        self.log.info(f"is_split: {is_split}")

        if not pcb_sn:
            pcb_sn = xutil.DateUtil.get_datetime_now("%Y%m%d%H%M%S000")

        comp_robot_ng_number = 0
        comp_repair_ng_number = 0

        board_pass_count = 0
        board_repass_count = 0
        board_ng_count = 0

        for board_entity in pcb_entity.yield_board_entity():
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_repair_ng_number += board_entity.comp_repair_ng_number

            if board_entity.get_final_result() == "PASS":
                board_pass_count += 1
            elif board_entity.get_final_result() == "REPASS":
                board_repass_count += 1
            elif board_entity.get_final_result() == "NG":
                board_ng_count += 1

        comp_data_list_no_split_jd = []
        pcs_list_no_split_jd = []

        ret_res = self.x_response()

        start_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time1 = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        review_p = pcb_entity.get_pcb_pcb_t_review_path()

        prod_json_filepath = f"{review_p}/ProductionInfo.json"
        if os.path.exists(prod_json_filepath):
            # 从数据包获取稼动数据
            production_info = xutil.FileUtil.load_json_file(prod_json_filepath)
            jd_type = "模式2"
        else:
            production_info = {}
            jd_type = "模式1"

        self.log.info(f"jd_type: {jd_type}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_index_list and board_no not in board_index_list:
                self.log.warning(f"该拼板不在分板发送列表里，该拼板本次不上传数据！")
                continue

            if not only_one_sn and barcode:
                only_one_sn = barcode

            time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

            cache_unique_id = limit_global_data.get_value(barcode, "")
            self.log.info(f"获取到的缓存uniqueId：{cache_unique_id}")

            if not only_one_cache_unique_id and cache_unique_id:
                only_one_cache_unique_id = cache_unique_id

            comp_data = []

            comp_data_list_jd = []

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    item = {
                        "paramName": comp_entity.designator,
                        "paramValue": comp_entity.repair_ng_str,
                        "paramType": comp_entity.type,
                        "componentName": comp_entity.designator,
                        "testResult": comp_entity.get_final_result(1, 1, 2),
                        "puzzleNos": [board_no]
                    }
                    comp_data.append(item)
                    pcb_comp_data_list.append(item)

                if comp_entity.is_robot_ng():
                    item_jd = {
                        "Angle": 0,
                        "Area": 0,
                        "ComponentName": comp_entity.designator,
                        "ErrorDesc": comp_entity.repair_ng_str,
                        "ErrorName": comp_entity.repair_ng_str,
                        "ErrorNo": comp_entity.repair_ng_code,
                        "Height": 0,
                        "Inten": 0,
                        "Left": 0,
                        "MFaultName": comp_entity.robot_ng_str,
                        "PcsNum": int(board_no),
                        "PicPath": comp_entity.image_path,
                        "PinNumber": "0",
                        "Result": comp_entity.get_final_result("OK", "PASS", "NG"),
                        "Side": 1,
                        "SilkScreen": "",
                        "Size": 0,
                        "Sort": 0,
                        "Top": 0,
                        "Volume": 0,
                        "Weight": 0,
                        "Width": 0,
                        "XPosition": str(comp_entity.geometry.cx),  # 取report.xml里的坐标，并且转换成像素
                        "YPosition": str(comp_entity.geometry.cy),  # 取report.xml里的坐标，并且转换成像素
                    }
                    comp_data_list_jd.append(item_jd)
                    comp_data_list_no_split_jd.append(item_jd)

            pcs_item = {
                "PcsBadPointCount": board_entity.comp_repair_ng_number,  # 传真实NG的点数
                "PcsNum": int(board_no),
                "PcsPassPointCount": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,  # 改成传误报的数量
                "PcsPointCount": board_entity.comp_total_number,
                "PcsResult": board_entity.get_final_result("OK", "PASS", "NG"),
                "PcsSn": barcode,
                "Side": 1
            }

            addition_param = {
                "BasicInfo": {
                    "BadPointCount": comp_repair_ng_number,  # 传真实NG的点数
                    "BoardInspectionResult": pcb_entity.get_final_result("OK", "PASS", "NG"),
                    "FilePath": "",
                    "InspectionPointCount": pcb_entity.comp_count,
                    "PassPointCount": comp_robot_ng_number - comp_repair_ng_number,  # 传误报的点数
                    "ProductionNGCount": board_ng_count,
                    "ProductionOKCount": board_pass_count,
                    "ProductionPassCount": board_repass_count
                },
                "PcsList": [
                    pcs_item
                ],
                "PointList": comp_data_list_jd
            }

            pcs_list_no_split_jd.append(pcs_item)

            checkout_param = {
                "machineCode": machine_code,
                "barcode": barcode,
                "lane": lane_id,
                "taskOrderNo": task_no_select,
                "procedureCode": work_operation_code,
                "staffId": staff_id,
                "operationTime": time_now,
                "testResult": board_entity.get_repair_result(1, 2),
                "uniqueId": cache_unique_id,
                "dataCollectionInfoInputs": comp_data,
            }

            # 稼动参数
            production_param = {
                "LocalTime": time_now,
                "MachineCode": machine_code,
                "RecipeGroupName": pcb_entity.pcb,
                "recipeName": pcb_entity.pcb,
                "recipeVersion": pcb_entity.bom,
                "boardNumber": pcb_sn,
                "barcode": barcode,
                "Unit": 1,
                "Lane": int(lane_id),
                "Stage": stage,
                "OperatorCode": operator_code,
                "TotalRunTime": production_info.get("totalRunTime", 0),
                "ProductionStartTime": production_info.get("productionStartTime", start_time1),
                "ProductionEndTime": production_info.get("productionEndTime", end_time1),
                "ActualTime": production_info.get("actualTime", 0),
                "TransferTime": production_info.get("transferTime", 0),
                "DownTime": production_info.get("downTime", 0),
                "ErrorTime": production_info.get("errorTime", 0),
                "CycleTime": production_info.get("cycleTime", 0),
                "FrontWait": production_info.get("frontWait", 0),
                "RearWait": production_info.get("rearWait", 0),
                "OtherTime": production_info.get("otherTime", 0),
                "ErrorCount": production_info.get("ErrorCount", 0),
                "ProductionCount": 1,
                "UnitCount": board_entity.comp_total_number,
                "SkipCount": 0,
                "CommandType": command_type,
                "UniqueId": cache_unique_id,
                "Enabled": None,
                "IsExecuted": None,
                "Addition": json.dumps(addition_param, ensure_ascii=False, separators=(",", ":"))
            }

            if is_split:
                self.log.info(f"分板上传中...")
                self.log.info(f"1. 上传过站信息...")
                ret = cvte_post_request(api_url_checkout, checkout_param, headers=headers)
                if not ret.get("success"):
                    msg = ret.get("error", {}).get("message")
                    ret_res = self.x_response("false", f"轨道{lane_id} mes接口异常，条码出站失败，error：{msg}")

                self.log.info(f"条码：{barcode} 已过站！")

                if jd_type == "模式2":
                    self.log.info(f"2. 发送分板稼动数据...")
                    self.log.info(f"Addition: \n{json.dumps(addition_param, indent=4, ensure_ascii=False)}")

                    ret = cvte_post_request(api_url_production_info, production_param, headers=headers)
                    if not ret.get("success"):
                        msg = ret.get("error", {}).get("message")
                        ret_res = self.x_response("false", f"轨道{lane_id} mes接口异常，上传稼动信息失败，error：{msg}")

                    self.log.info(f"条码：{barcode} 已上传稼动数据！")

        if not is_split:
            # 对应的is_split字段为false，表示当前任务单工序未分板，上传进站/出站/稼动时，仅取一个SN提交一次
            self.log.info(f"未分板，仅取一个SN提交一次！")
            self.log.info(f"1. 上传过站信息...")
            time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

            not_split_param = {
                "machineCode": machine_code,
                "barcode": only_one_sn,
                "lane": lane_id,
                "taskOrderNo": task_no_select,
                "procedureCode": work_operation_code,
                "staffId": staff_id,
                "operationTime": time_now,
                "testResult": pcb_entity.get_repair_result(1, 2),
                "uniqueId": only_one_cache_unique_id,
                "dataCollectionInfoInputs": pcb_comp_data_list,
            }
            ret = cvte_post_request(api_url_checkout, not_split_param, headers=headers)
            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                ret_res = self.x_response("false", f"轨道{lane_id} mes接口异常，条码出站失败，error：{msg}")

            self.log.info(f"条码：{only_one_sn} 已过站！")

            if jd_type == "模式2":
                self.log.info(f"2. 发送分板稼动数据...")
                addition_param_no_split = {
                    "BasicInfo": {
                        "BadPointCount": comp_repair_ng_number,  # 真实NG的点数
                        "BoardInspectionResult": pcb_entity.get_final_result("OK", "PASS", "NG"),
                        "FilePath": "",
                        "InspectionPointCount": pcb_entity.comp_count,
                        "PassPointCount": comp_robot_ng_number - comp_repair_ng_number,  # 误报的点数
                        "ProductionNGCount": board_ng_count,
                        "ProductionOKCount": board_pass_count,
                        "ProductionPassCount": board_repass_count
                    },
                    "PcsList": pcs_list_no_split_jd,
                    "PointList": comp_data_list_no_split_jd
                }

                no_split_production_param = {
                    "LocalTime": time_now,
                    "MachineCode": machine_code,
                    "RecipeGroupName": pcb_entity.pcb,
                    "recipeName": pcb_entity.pcb,
                    "recipeVersion": pcb_entity.bom,
                    "boardNumber": pcb_sn,
                    "barcode": only_one_sn,
                    "Unit": 1,
                    "Lane": int(lane_id),
                    "Stage": stage,
                    "OperatorCode": operator_code,
                    "TotalRunTime": production_info.get("totalRunTime"),
                    "ProductionStartTime": production_info.get("productionStartTime"),
                    "ProductionEndTime": production_info.get("productionEndTime"),
                    "ActualTime": production_info.get("actualTime"),
                    "TransferTime": production_info.get("transferTime"),
                    "DownTime": production_info.get("downTime"),
                    "ErrorTime": production_info.get("errorTime"),
                    "CycleTime": production_info.get("cycleTime"),
                    "FrontWait": production_info.get("frontWait"),
                    "RearWait": production_info.get("rearWait"),
                    "OtherTime": production_info.get("otherTime"),
                    "ErrorCount": production_info.get("ErrorCount", 0),
                    "ProductionCount": 1,
                    "UnitCount": pcb_entity.comp_count,
                    "SkipCount": 0,
                    "CommandType": command_type,
                    "UniqueId": only_one_cache_unique_id,
                    "Enabled": None,
                    "IsExecuted": None,
                    "Addition": json.dumps(addition_param_no_split, ensure_ascii=False, separators=(",", ":"))
                }

                self.log.info(f"Addition: \n{json.dumps(addition_param_no_split, indent=4, ensure_ascii=False)}")
                ret = cvte_post_request(api_url_production_info, no_split_production_param, headers=headers)
                if not ret.get("success"):
                    msg = ret.get("error", {}).get("message")
                    ret_res = self.x_response("false", f"轨道{lane_id} mes接口异常，上传稼动信息失败，error：{msg}")

                self.log.info(f"条码：{only_one_sn} 已上传稼动数据！")

        # 先检查一下是否有缓存的稼动数据需要发送。如果有，则发送。如果没有，则cache一下过站信息
        # t_review_path = pcb_entity.get_pcb_pcb_t_review_path()
        # review_filename = os.path.basename(t_review_path)
        # checkout_data_list.add_item(review_filename)
        # self.log.info(f"该数据包的过站信息已缓存！")

        # if jiadong_data_map.is_exist_item(review_filename):
        #     self.log.info(f"捕获到该数据包的稼动信号，发送稼动数据....")
        #     jiadong_json_data = jiadong_data_map.get_value(review_filename)
        #     ret_data = other_param.socket_service.send_data_to_mes(jiadong_json_data)
        #     # print("ret_data", ret_data)
        #     #
        #     # ret_data01 = json.loads(ret_data)
        #     # if not ret_data01.get("result"):
        #     #     ret_res = self.x_response("false", ret_data01.get('string'))

        return ret_res

    def send_production_info_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any):
        api_url_production_info = data_vo.get_value_by_cons_key("api_url_production_info")
        machine_code = data_vo.get_value_by_cons_key("machine_code")
        stage = data_vo.get_value_by_cons_key("stage", to_int=True)
        operator_code = data_vo.get_value_by_cons_key("operator_code")
        command_type = data_vo.get_value_by_cons_key("command_type")
        token_header = data_vo.get_value_by_cons_key("token_header")

        production_info = data_vo.json_data.get("productionInfo")
        production_info = json.loads(production_info)

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT3)

        board_index_list = other_data.get("board_index_list", [])

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            if board_entity.get_final_result() == "NG":
                if is_man_xml_exists(data_vo.get_review_path(), board_entity.board_no):
                    return self.x_response("false", "复判数据异常，请检查！")

        lane_id = pcb_entity.track_index
        self.log.info(f"轨道： {lane_id}")

        if lane_id == 1:
            task_no_select = data_vo.get_value_by_cons_key("task_no_list1")
            task_map = xutil.CacheUtil.get("task_map1", {})
        else:
            task_no_select = data_vo.get_value_by_cons_key("task_no_list2")
            task_map = xutil.CacheUtil.get("task_map2", {})

        is_split = task_map.get(task_no_select)
        self.log.info(f"is_split: {is_split}")

        pcb_sn = pcb_entity.pcb_barcode

        review_path = pcb_entity.get_pcb_pcb_t_review_path()
        prod_json_filepath = f"{review_path}/ProductionInfo.json"
        if os.path.exists(prod_json_filepath):
            jd_type = "模式2"
        else:
            jd_type = "模式1"

        self.log.info(f"jd_type: {jd_type}")

        # if "T_" not in review_path:
        #     review_path = pcb_entity.get_pcb_pcb_b_review_path()  # 兼容501分开上下面发送mes

        # review_filename = os.path.basename(review_path)
        # json_data = data_vo.json_data

        if jd_type == "模式2":
            self.log.warning(f"type 2 and return!")
            return self.x_response()

        # # 先检查一下条码是否已经过站了，如果没有过站，则cache 该信号, 如果已经过站了，则直接发送数据
        # if not checkout_data_list.is_exist_item(review_filename):
        #     # 表示未过站, 并且cache该信号
        #     self.log.info(f"该数据包还未过站，将会在过站之后再发送稼动数据！")
        #
        #     jiadong_data_map.add_item(review_filename, json_data)
        #     self.log.info(f"[发送稼动数据]信号已cache，将会在过站之后再发送稼动数据！")
        #     return self.x_response()
        # else:
        #     self.log.info(f"获取到过站缓存信息，该数据包可以发送稼动数据！")

        # if jiadong_filter_list.is_exist_item(review_filename):
        #     self.log.warning(f"该稼动数据已发送过，本次不发送！")
        #     return self.x_response()

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"请先获取Token！")

        headers = {
            f"{token_header}": f"{token}"
        }

        if not pcb_sn:
            pcb_sn = xutil.DateUtil.get_datetime_now("%Y%m%d%H%M%S000")

        comp_robot_ng_number = 0
        comp_repair_ng_number = 0

        board_pass_count = 0
        board_repass_count = 0
        board_ng_count = 0

        for board_entity in pcb_entity.yield_board_entity():
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_repair_ng_number += board_entity.comp_repair_ng_number

            if board_entity.get_final_result() == "PASS":
                board_pass_count += 1
            elif board_entity.get_final_result() == "REPASS":
                board_repass_count += 1
            elif board_entity.get_final_result() == "NG":
                board_ng_count += 1

        only_one_sn = pcb_entity.pcb_barcode
        only_one_cache_unique_id = ""

        comp_data_list_no_split = []
        pcs_list_no_split = []

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not only_one_sn and barcode:
                only_one_sn = barcode

            if board_index_list and board_no not in board_index_list:
                self.log.warning(f"该拼板不在分板发送列表里，该拼板本次不上传数据！")
                continue

            cache_unique_id = limit_global_data.get_value(barcode, "")
            self.log.info(f"获取到的缓存uniqueId：{cache_unique_id}")

            if not only_one_cache_unique_id and cache_unique_id:
                only_one_cache_unique_id = cache_unique_id

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                item = {
                    "Angle": 0,
                    "Area": 0,
                    "ComponentName": comp_entity.designator,
                    "ErrorDesc": comp_entity.repair_ng_str,
                    "ErrorName": comp_entity.repair_ng_str,
                    "ErrorNo": comp_entity.repair_ng_code,
                    "Height": 0,
                    "Inten": 0,
                    "Left": 0,
                    "MFaultName": comp_entity.robot_ng_str,
                    "PcsNum": int(board_no),
                    "PicPath": comp_entity.image_path,
                    "PinNumber": "0",
                    "Result": comp_entity.get_final_result("OK", "PASS", "NG"),
                    "Side": 1,
                    "SilkScreen": "",
                    "Size": 0,
                    "Sort": 0,
                    "Top": 0,
                    "Volume": 0,
                    "Weight": 0,
                    "Width": 0,
                    "XPosition": str(comp_entity.geometry.cx),  # 取report.xml里的坐标，并且转换成像素
                    "YPosition": str(comp_entity.geometry.cy),  # 取report.xml里的坐标，并且转换成像素
                }
                comp_data_list.append(item)
                comp_data_list_no_split.append(item)

            pcs_item = {
                "PcsBadPointCount": board_entity.comp_repair_ng_number,  # 传真实NG的点数
                "PcsNum": int(board_no),
                "PcsPassPointCount": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,  # 改成传误报的数量
                "PcsPointCount": board_entity.comp_total_number,
                "PcsResult": board_entity.get_final_result("OK", "PASS", "NG"),
                "PcsSn": barcode,
                "Side": 1
            }

            addition_param = {
                "BasicInfo": {
                    "BadPointCount": comp_repair_ng_number,  # 传真实NG的点数
                    "BoardInspectionResult": pcb_entity.get_final_result("OK", "PASS", "NG"),
                    "FilePath": "",
                    "InspectionPointCount": pcb_entity.comp_count,
                    "PassPointCount": comp_robot_ng_number - comp_repair_ng_number,  # 传误报的点数
                    "ProductionNGCount": board_ng_count,
                    "ProductionOKCount": board_pass_count,
                    "ProductionPassCount": board_repass_count
                },
                "PcsList": [
                    pcs_item
                ],
                "PointList": comp_data_list
            }

            pcs_list_no_split.append(pcs_item)

            production_param = {
                "LocalTime": time_now,
                "MachineCode": machine_code,
                "RecipeGroupName": pcb_entity.pcb,
                "recipeName": pcb_entity.pcb,
                "recipeVersion": pcb_entity.bom,
                "boardNumber": pcb_sn,
                "barcode": barcode,
                "Unit": 1,
                "Lane": int(lane_id),
                "Stage": stage,
                "OperatorCode": operator_code,
                "TotalRunTime": production_info.get("totalRunTime"),
                "ProductionStartTime": production_info.get("productionStartTime"),
                "ProductionEndTime": production_info.get("productionEndTime"),
                "ActualTime": production_info.get("actualTime"),
                "TransferTime": production_info.get("transferTime"),
                "DownTime": production_info.get("downTime"),
                "ErrorTime": production_info.get("errorTime"),
                "CycleTime": production_info.get("cycleTime"),
                "FrontWait": production_info.get("frontWait"),
                "RearWait": production_info.get("rearWait"),
                "OtherTime": production_info.get("otherTime"),
                "ErrorCount": production_info.get("ErrorCount", 0),
                "ProductionCount": 1,
                "UnitCount": board_entity.comp_total_number,
                "SkipCount": 0,
                "CommandType": command_type,
                "UniqueId": cache_unique_id,
                "Enabled": None,
                "IsExecuted": None,
                "Addition": json.dumps(addition_param, ensure_ascii=False, separators=(",", ":"))
            }

            if is_split:
                self.log.info(f"Addition: \n{json.dumps(addition_param, indent=4, ensure_ascii=False)}")

                ret = cvte_post_request(api_url_production_info, production_param, headers=headers)
                if not ret.get("success"):
                    msg = ret.get("error", {}).get("message")
                    ret_res = self.x_response("false", f"轨道{lane_id} mes接口异常，上传稼动信息失败，error：{msg}")

                self.log.info(f"条码：{barcode} 已上传稼动数据！")

        if not is_split:
            self.log.info(f"未分板，仅取一个SN提交一次！")
            addition_param_no_split = {
                "BasicInfo": {
                    "BadPointCount": comp_repair_ng_number,  # 真实NG的点数
                    "BoardInspectionResult": pcb_entity.get_final_result("OK", "PASS", "NG"),
                    "FilePath": "",
                    "InspectionPointCount": pcb_entity.comp_count,
                    "PassPointCount": comp_robot_ng_number - comp_repair_ng_number,  # 误报的点数
                    "ProductionNGCount": board_ng_count,
                    "ProductionOKCount": board_pass_count,
                    "ProductionPassCount": board_repass_count
                },
                "PcsList": pcs_list_no_split,
                "PointList": comp_data_list_no_split
            }

            no_split_production_param = {
                "LocalTime": time_now,
                "MachineCode": machine_code,
                "RecipeGroupName": pcb_entity.pcb,
                "recipeName": pcb_entity.pcb,
                "recipeVersion": pcb_entity.bom,
                "boardNumber": pcb_sn,
                "barcode": only_one_sn,
                "Unit": 1,
                "Lane": int(lane_id),
                "Stage": stage,
                "OperatorCode": operator_code,
                "TotalRunTime": production_info.get("totalRunTime"),
                "ProductionStartTime": production_info.get("productionStartTime"),
                "ProductionEndTime": production_info.get("productionEndTime"),
                "ActualTime": production_info.get("actualTime"),
                "TransferTime": production_info.get("transferTime"),
                "DownTime": production_info.get("downTime"),
                "ErrorTime": production_info.get("errorTime"),
                "CycleTime": production_info.get("cycleTime"),
                "FrontWait": production_info.get("frontWait"),
                "RearWait": production_info.get("rearWait"),
                "OtherTime": production_info.get("otherTime"),
                "ErrorCount": production_info.get("ErrorCount", 0),
                "ProductionCount": 1,
                "UnitCount": pcb_entity.comp_count,
                "SkipCount": 0,
                "CommandType": command_type,
                "UniqueId": only_one_cache_unique_id,
                "Enabled": None,
                "IsExecuted": None,
                "Addition": json.dumps(addition_param_no_split, ensure_ascii=False, separators=(",", ":"))
            }

            self.log.info(f"Addition: \n{json.dumps(addition_param_no_split, indent=4, ensure_ascii=False)}")
            ret = cvte_post_request(api_url_production_info, no_split_production_param, headers=headers)
            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                ret_res = self.x_response("false", f"轨道{lane_id} mes接口异常，上传稼动信息失败，error：{msg}")

            self.log.info(f"条码：{only_one_sn} 已上传稼动数据！")

        # jiadong_data_map.remove_item(review_filename)
        # checkout_data_list.remove_item(review_filename)
        # jiadong_filter_list.add_item(review_filename)

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()

        api_url_token = btn_vo.get_value_by_cons_key("api_url_token")
        email = btn_vo.get_value_by_cons_key("email")
        password = btn_vo.get_value_by_cons_key("password")

        pwd_encrypt = xutil.OtherUtil.get_md5_sign(password).lower()

        if btn_key == "get_token_btn":
            ret_res = refresh_token(api_url_token, email, pwd_encrypt)
            if ret_res:
                return ret_res

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_token = other_vo.get_value_by_cons_key("api_url_token")
        email = other_vo.get_value_by_cons_key("email")
        password = other_vo.get_value_by_cons_key("password")
        pwd_encrypt = xutil.OtherUtil.get_md5_sign(password).lower()

        refresh_token(api_url_token, email, pwd_encrypt)

    def combo_mouse_press(self, combo_vo: ComboVo, other_param: Any):
        api_url_get_task_list = combo_vo.get_value_by_cons_key("api_url_get_task_list")
        work_operation_code = combo_vo.get_value_by_cons_key("work_operation_code")
        token_header = combo_vo.get_value_by_cons_key("token_header")

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"请先获取Token！")

        headers = {
            f"{token_header}": f"{token}"
        }

        combo_key = combo_vo.get_combo_key()
        if combo_key == "task_no_list1":
            mo_lot_no = combo_vo.get_value_by_cons_key("mo_lot_no1")

            ret = xrequest.RequestUtil.get(api_url_get_task_list, {
                "moLotNo": mo_lot_no,
                "workOperationCode": work_operation_code,
            }, headers=headers, check_res_code=False)

            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                return self.x_response("false", f"mes接口异常，获取任务单列表失败，error：{msg}")

            ret_task_list = ret.get('result')

            task_map = {}
            for item in ret_task_list:
                task_map[item.get('taskNo')] = item.get('isSplited', False)

            xutil.CacheUtil.set("task_map1", task_map)
            task_li = list(task_map.keys())

            ret_json = {
                "new_items": task_li
            }

            return self.x_response("true", json.dumps(ret_json, ensure_ascii=False))

        elif combo_key == "task_no_list2":
            mo_lot_no = combo_vo.get_value_by_cons_key("mo_lot_no2")

            ret = xrequest.RequestUtil.get(api_url_get_task_list, {
                "moLotNo": mo_lot_no,
                "workOperationCode": work_operation_code,
            }, headers=headers, check_res_code=False)

            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                return self.x_response("false", f"mes接口异常，获取任务单列表失败，error：{msg}")

            ret_task_list = ret.get('result')

            task_map = {}
            for item in ret_task_list:
                task_map[item.get('taskNo')] = item.get('isSplited', False)

            xutil.CacheUtil.set("task_map2", task_map)

            task_li = list(task_map.keys())

            ret_json = {
                "new_items": task_li
            }

            return self.x_response("true", json.dumps(ret_json, ensure_ascii=False))
