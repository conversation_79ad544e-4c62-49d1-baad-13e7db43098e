# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xenum.py
# Time       ：2025/1/17 下午7:26
# Author     ：sch
# version    ：python 3.8
# Description：枚举类
"""


class CheckSetting1(object):
    """
    个性化配置项：校验条码类型
    """
    CheckAll = "校验全部条码"
    CheckFirst = "仅校验第一个条码"
    NotCheckFirst = "不校验第一个条码"
    CheckPcbSn = "仅校验板边条码"


class CheckSetting2(object):
    """
    个性化配置项：校验失败是否发送数据到Mes
    """
    CheckFailSendData = "校验失败发送数据到Mes"
    CheckFailNotSendData = "校验失败不发送数据到Mes"


class SendMesSetting1(object):
    """
    个性化配置项：保存器件图设置
    """
    SaveAll = "保存全部器件图"
    SaveInspectNG = "保存检测NG器件图"
    SaveRepairNG = "保存复判NG器件图"
    NotSave = "不保存器件图"


class SendMesSetting2(object):
    """
    个性化配置项：保存器件列表
    """
    SaveAll = "保存全部器件列表"
    SaveInspectNG = "仅保存检测NG器件列表"
    SaveRepairNG = "仅保存复判NG器件列表"
    NotSave = "不保存器件列表"


class SendMesSetting3(object):
    """
    个性化配置项：是否发送坏板
    """
    NotSend = "不发送坏板"
    Send = "发送坏板"


if __name__ == '__main__':
    # 访问枚举成员及其属性
    pass
