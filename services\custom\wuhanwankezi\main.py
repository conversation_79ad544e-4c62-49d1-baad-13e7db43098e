# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/13 下午15:38
# Author     ：chencb
# version    ：python 3.8
# Description：武汉万颗子 https://jira.cvte.com/browse/ATAOI_2019-37586
"""
import time
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import log, x_response
from entity.MesEntity import PcbEntity
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "wuhanwankezi release v1.0.0.1",
        "device": "AIS20X, AIS50X",
        "feature": ["发送数据", "发送设备状态"],
        "author": "chenchongbing",
        "release": """
date: 2025-03-14 09:30  jira:37586 新增发送数据、发送设备状态需求
""",
    }
    # 接口地址、设备编码、员工编码
    form = {
        "mes_api": {
            "ui_name": "接口地址",
            "value": "",
        },
        "device_id": {
            "ui_name": "设备编码",
            "value": "",
        },
        "user_id": {
            "ui_name": "员工编码",
            "value": "",
        },
    }

    def __init__(self):
        self.cur_device_status = ''

    def _send_data(self, api_url: str, send_data: dict):
        """
        发送最终数据，MES返回响应格式如下：
        {
        "code": 0,
        "msg": "success",
        "data": "消息发送成功!",
        "success": true
        }
        """
        response = self.x_response()
        try:
            ret = xrequest.RequestUtil.post_json(api_url, send_data)
            if not ret.get("success"):
                code = ret.get('code')
                message = ret.get('msg')
                response = self.x_response("false", f"【服务器返回失败，code：{code}, message：{message}】")
        except Exception as e:
            response = self.x_response("false", f"【本地网络异常：{e}】")
        return response

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        # 进板、开始检测、停止检查过滤掉不发送，这个客户的主软件版本使用的是旧的设备状态接口
        status_code = other_vo.get_old_device_status_code()
        if status_code in ['01', '02', '03']:
            self.cur_device_status = status_code
            return x_response()

        if status_code == self.cur_device_status:
            return x_response()

        device_id = other_vo.get_value_by_cons_key('device_id')
        mes_api = other_vo.get_value_by_cons_key('mes_api')
        if not mes_api:
            return x_response('false', '未填写接口地址，无法正常发送设备状态信息')

        # 缓存当前设备状态，用于后续发送数据时填充使用
        self.cur_device_status = status_code

        project_name = other_vo.get_project_name()
        cur_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
        request_param = {"dataList": [{
            "data": {
                "method": "AutoCollect",
                "deviceMark": device_id,
                "runningState": status_code,
                "fty": "",
                "defectRate": "",
                "abnormalPoint": "",
                "barcode": "",
                "checkResult": "",
                "sendTime": cur_time,
                "defectCode": [],
                "programName": project_name,
                "testList": {}
            }
        }]}
        ret = self._send_data(mes_api, request_param)
        return ret

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_id = data_vo.get_value_by_cons_key('device_id')
        user_id = data_vo.get_value_by_cons_key('user_id')
        mes_api = data_vo.get_value_by_cons_key('mes_api')
        if not mes_api:
            return x_response('false', '未填写接口地址，无法正常发送检测结果')

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data_list = []
        start_time = pcb_entity.get_start_time().strftime("%Y-%m-%d %H:%M:%S:%f")[:-3]
        end_time = pcb_entity.get_end_time().strftime("%Y-%m-%d %H:%M:%S:%f")[:-3]
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)

            ng_comp_list = []
            defect_code_list = set()
            for comp_entity in board_entity.yield_comp_entity():
                if not comp_entity.is_robot_ng():
                    continue

                ng_code = comp_entity.robot_ng_code
                comp_id = comp_entity.comp_id_real
                defect_code_list.add(ng_code)
                for alg_entity in comp_entity.yield_alg_entity():
                    if alg_entity.result == ng_code:
                        title = comp_id + '_' + alg_entity.test_name
                        result = 'OK' if alg_entity.result == '0' else 'NG'
                        comp_data = {
                            "detailInfo": {
                                "title": title,
                                "value": alg_entity.test_val,
                                "itemResult": result,
                                "errorCode": ng_code,
                                "lowerLimit": alg_entity.min_threshold,
                                "upperLimit": alg_entity.max_threshold,
                                "itemBeginTime": start_time,
                                "itemEndTime": end_time
                            }
                        }
                        ng_comp_list.append(comp_data)
                        break

            cur_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
            board_data = {
                "data": {
                    "method": "AutoCollect",
                    "deviceMark": device_id,
                    "runningState": self.cur_device_status,
                    "fty": "",
                    "defectRate": "",
                    "abnormalPoint": board_entity.comp_repair_ng_number,
                    "barcode": board_entity.barcode,
                    "checkResult": board_entity.get_final_result("OK", "OK", "NG"),
                    "sendTime": cur_time,
                    "defectCode": list(defect_code_list),
                    "programName": pcb_entity.project_name,
                    "testList": {
                        "workStation": device_id,
                        "empCode": user_id,
                        "beginTime": start_time,
                        "endTime": end_time,
                        "details": ng_comp_list
                    }
                }
            }
            board_data_list.append(board_data)

        request_param = {
            "dataList": board_data_list
        }
        ret = self._send_data(mes_api, request_param)
        return ret
