# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/16 上午11:55
# Author     ：sch
# version    ：python 3.8
# Description：重庆盟讯630、SPI
"""
from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
测试时间,{pcb_test_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,体积,面积,高度,x偏移,y偏移,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_volume},{comp_area},{comp_height},{comp_x_offset},{comp_y_offset},{comp_user_result},{comp_image}"""

txt_pcb_panel_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
测试时间:{pcb_test_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
拼板检测NG数量:{pcb_board_robot_ng_number}
拼板复判NG数量:{pcb_board_user_ng_number}
拼板误报数量:{pcb_board_repass_number}
器件总数:{pcb_comp_number}
器件检测NG总数:{pcb_comp_robot_ng_number}
器件复判NG总数:{pcb_comp_user_ng_number}
器件误报总数:{pcb_comp_repass_number}

{BoardData}
"""

txt_board_panel_template = """
========================
拼板号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}{CompData}
"""

txt_comp_panel_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""

xml_pcb_panel_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_test_time="{pcb_test_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_robot_ng_number="{pcb_board_robot_ng_number}" pcb_board_user_ng_number="{pcb_board_user_ng_number}" pcb_board_repass_number="{pcb_board_repass_number}"
  pcb_comp_number="{pcb_comp_number}" pcb_comp_robot_ng_number="{pcb_comp_robot_ng_number}" pcb_comp_user_ng_number="{pcb_comp_user_ng_number}" pcb_comp_repass_number="{pcb_comp_repass_number}" >
    <BoardList>{BoardData}
    </BoardList>
</Panel>
"""

xml_board_panel_template = """
        <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}">{CompData}
        </Board>"""

xml_comp_panel_template = """
            <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""

txt_board_board_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
测试时间:{pcb_test_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
整板器件总数:{pcb_comp_number}
拼板序号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}
拼板器件数量:{board_comp_number}
拼板器件检测NG总数:{board_comp_robot_ng_number}
拼板器件复判NG总数:{board_comp_user_ng_number}
拼板器件误报总数:{board_comp_repass_number}

{CompData}
"""

txt_comp_board_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""

csv_board_board_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
测试时间,{pcb_test_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
整板器件总数,{pcb_comp_number}
拼板序号,{board_no}
拼板条码,{board_sn}
拼板检测结果,{board_robot_result}
拼板复判结果,{board_user_result}
拼板最终结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件检测NG总数,{board_comp_robot_ng_number}
拼板器件复判NG总数,{board_comp_user_ng_number}
拼板器件误报总数,{board_comp_repass_number}

器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,体积,面积,高度,x偏移,y偏移,器件复判结果,器件图片路径{CompData}
"""

csv_comp_board_template = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_volume},{comp_area},{comp_height},{comp_x_offset},{comp_y_offset},{comp_user_result},{comp_image}"""

xml_board_board_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_test_time="{pcb_test_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_number="{pcb_board_number}" pcb_comp_number="{pcb_comp_number}">
    <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}"
    board_comp_number="{board_comp_number}" board_comp_robot_ng_number="{board_comp_robot_ng_number}" board_comp_user_ng_number="{board_comp_user_ng_number}" board_comp_repass_number="{board_comp_repass_number}" >{CompData}
    </Board>
</Panel>
"""

xml_comp_board_template = """
        <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""


class Engine(BaseEngine):
    version = {
        "title": "chongqingmengxun630 release v1.0.0.5",
        "device": "AIS630",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-14 16:04  init
date: 2023-07-17 16:06  保存数据到本地
date: 2024-01-16 10:41  增加换行符配置项  
date: 2024-09-10 15:27  其他异常也需要发送错误信息到维修站
date: 2024-10-16 14:20  csv文档增加输出体积、面积、高度、x偏移、y偏移数据
""", }

    other_combo = {
        "is_upload_mes": {
            "ui_name": "上传json数据到Mes服务器",
            "item": [
                "是",
                "否"
            ],
            "value": "否"
        }
    }

    other_form = {
        "mes_api_url": {
            "ui_name": "Mes接口URL",
            "value": ""
        },
    }

    combo = {
        "is_save_local": {
            "ui_name": "保存数据到本地",
            "item": [
                "是",
                "否",
            ],
            "value": "是"
        },
        # "is_save_comp_image": {
        #     "ui_name": "保存NG器件图到本地",
        #     "item": [
        #         "是",
        #         "否",
        #     ],
        #     "value": "否"
        # },
        "file_type": {
            "ui_name": "文件类型",
            "item": [
                "csv",
                "txt",
                "json",
                "xml",
            ],
            "value": "csv"
        },
        "save_type": {
            "ui_name": "文件生成方式",
            "item": [
                "整板生成",
                "拼板生成",
            ],
            "value": "整板生成"
        },
        "panel_filename": {
            "ui_name": "整板文件名格式",
            "item": [
                "时间_整板条码",
                "整板条码_时间",
                "整板条码",
                "时间",
                "---",
            ],
            "value": "时间_整板条码"
        },
        "board_filename": {
            "ui_name": "拼板文件名格式",
            "item": [
                '时间_拼板条码',
                '拼板条码_时间',
                '拼板条码',
                '时间_拼板条码_拼板序号',
                '时间_拼板序号',
                '拼板序号_时间',
                "---",
            ],
            "value": "时间_拼板序号"
        },
        "save_path_type": {
            "ui_name": "保存数据路径格式",
            "item": [
                '数据路径',
                '数据路径/日期(yyyymmdd)',
                '数据路径/设备名称',
                '数据路径/设备名称/日期(yyyymmdd)',
                '数据路径/日期(yyyymmdd)/设备名称',
            ],
            "value": "数据路径/设备名称/日期(yyyymmdd)"
        },
        # "save_image_path_fmt": {
        #     "ui_name": "保存NG器件图路径格式",
        #     "item": [
        #         '图片路径/条码_拼板号_位号_NG描述.png',
        #         '图片路径/日期(yyyymmdd)/条码_拼板号_位号_NG描述.png',
        #         '图片路径/设备名称/条码_拼板号_位号_NG描述.png',
        #         '图片路径/设备名称/日期(yyyymmdd)/条码_拼板号_位号_NG描述.png',
        #         '图片路径/日期(yyyymmdd)/设备名称/条码_拼板号_位号_NG描述.png',
        #     ],
        #     "value": "图片路径/日期(yyyymmdd)/设备名称/条码_拼板号_位号_NG描述.png"
        # },
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "全部"
        },
        "newline_type": {
            "ui_name": "换行符格式",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        },
    }

    path = {
        "save_path": {
            "ui_name": "数据路径",
            "value": ""
        },
        # "save_image_path": {
        #     "ui_name": "图片路径",
        #     "value": ""
        # }
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    ERROR_MAP = {
        "AIS203/AIS303/AIS40X/AIS50x": {
            "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
            "2": {"standard": "错件", "custom_code": "2", "custom_str": "WrongPart"},
            "3": {"standard": "反件", "custom_code": "3", "custom_str": "ReversePart"},
            "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tomstone"},
            "5": {"standard": "偏移", "custom_code": "5", "custom_str": "ShiftPart"},
            "6": {"standard": "翻转", "custom_code": "6", "custom_str": "UpsideDown"},
            "7": {"standard": "浮高", "custom_code": "7", "custom_str": "LiftedPackage"},
            "8": {"standard": "损件", "custom_code": "8", "custom_str": "Broken"},
            "9": {"standard": "露铜", "custom_code": "9", "custom_str": "ExposeCopper"},
            "10": {"standard": "少锡", "custom_code": "10", "custom_str": "InsufficientSolder"},
            "11": {"standard": "多锡", "custom_code": "11", "custom_str": "ExcessSolder"},
            "12": {"standard": "未出脚", "custom_code": "12", "custom_str": "NoPin"},
            "13": {"standard": "孔洞", "custom_code": "13", "custom_str": "PinHole"},
            "14": {"standard": "连锡", "custom_code": "14", "custom_str": "Bridge"},
            "15": {"standard": "锡珠", "custom_code": "15", "custom_str": "SolderBall"},
            "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "LiftedLead"},
            "17": {"standard": "弯脚", "custom_code": "17", "custom_str": "ShiftedLead"},
            "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
            "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
            "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
            "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
            "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
            "23": {"standard": "虚焊", "custom_code": "23", "custom_str": "IncompleteWeld"},
            "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
            "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
            "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
            "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
            "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
            "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
            "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
            "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
            "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
            "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
            "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
            "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
            "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
        },
        "AIS301": {
            "1": {"standard": "不合格", "custom_code": "1", "custom_str": "NG"},
            "2": {"standard": "多锡", "custom_code": "2", "custom_str": "ExcessSolder"},
            "3": {"standard": "连锡", "custom_code": "3", "custom_str": "Bridge"},
            "4": {"standard": "少锡", "custom_code": "4", "custom_str": "InsufficientSolder"},
            "5": {"standard": "孔洞", "custom_code": "5", "custom_str": "PinHole"},
            "6": {"standard": "未出脚", "custom_code": "6", "custom_str": "NoPin"},
            "7": {"standard": "异常出脚", "custom_code": "7", "custom_str": "ExceptionPin"},
            "8": {"standard": "缺件", "custom_code": "8", "custom_str": "MissingPart"},
            "9": {"standard": "偏位", "custom_code": "9", "custom_str": "ShiftPart"},
            "10": {"standard": "露铜", "custom_code": "10", "custom_str": "ExposeCopper"},
            "11": {"standard": "错件", "custom_code": "11", "custom_str": "WrongPart"},
            "12": {"standard": "极性错误", "custom_code": "12", "custom_str": "ReversePart"},
            "13": {"standard": "条码识别错误", "custom_code": "13", "custom_str": "BarcodeRecognition"},
            "14": {"standard": "数据错误", "custom_code": "14", "custom_str": "CountError"},
            "15": {"standard": "定位错误", "custom_code": "15", "custom_str": "Locate"},
            "16": {"standard": "流程错误", "custom_code": "16", "custom_str": "ProcessError"},
            "17": {"standard": "锡珠", "custom_code": "17", "custom_str": "SolderBall"},
            "18": {"standard": "拼版特征不匹配", "custom_code": "18", "custom_str": "FeatureMismatch"},
        },
        "AIS201": {
            "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
            "-1": {"standard": "未检查", "custom_code": "-1", "custom_str": "NoCheck"},
            "4": {"standard": "反件", "custom_code": "4", "custom_str": "ReversePart"},
            "5": {"standard": "错件", "custom_code": "5", "custom_str": "WrongPart"},
            "20": {"standard": "引脚未插", "custom_code": "20", "custom_str": "Pin Not Found"},
            "21": {"standard": "不是引脚", "custom_code": "21", "custom_str": "NoPin"},
            "101": {"standard": "多件", "custom_code": "101", "custom_str": "多件"},
            "102": {"standard": "浮高", "custom_code": "102", "custom_str": "Part Lift"},
            "103": {"standard": "歪斜", "custom_code": "103", "custom_str": "Part Tilt"},
            "104": {"standard": "条码错误", "custom_code": "104", "custom_str": "Barcode Error"},
            "105": {"standard": "内部错误", "custom_code": "105", "custom_str": "Internal Error"},
            "80": {"standard": "多涂", "custom_code": "80", "custom_str": "MoreCoating"},
            "81": {"standard": "少涂", "custom_code": "81", "custom_str": "LessCoating"},
            "82": {"standard": "气泡", "custom_code": "82", "custom_str": "BubbleCoating"},
        },
        "AIS63X": {
            "1": {"standard": "锡型不良", "custom_code": "1", "custom_str": "锡型不良"},
            "2": {"standard": "水平偏移", "custom_code": "2", "custom_str": "水平偏移"},
            "3": {"standard": "竖直偏移", "custom_code": "3", "custom_str": "竖直偏移"},
            "4": {"standard": "连锡", "custom_code": "4", "custom_str": "连锡"},
            "5": {"standard": "面积偏小", "custom_code": "5", "custom_str": "面积偏小"},
            "6": {"standard": "面积偏大", "custom_code": "6", "custom_str": "面积偏大"},
            "7": {"standard": "高度偏低", "custom_code": "7", "custom_str": "高度偏低"},
            "8": {"standard": "高度偏高", "custom_code": "8", "custom_str": "高度偏高"},
            "9": {"standard": "少锡", "custom_code": "9", "custom_str": "少锡"},
            "10": {"standard": "多锡", "custom_code": "10", "custom_str": "多锡"},
            "11": {"standard": "无锡", "custom_code": "11", "custom_str": "无锡"},
            "12": {"standard": "共面性", "custom_code": "12", "custom_str": "共面性"},
            "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "条码识别"},
            "1000": {"standard": "Unknown", "custom_code": "1000", "custom_str": "Unknown"},
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        is_upload_mes = data_vo.get_value_by_cons_key("is_upload_mes")
        mes_api_url = data_vo.get_value_by_cons_key("mes_api_url")
        is_save_local = data_vo.get_value_by_cons_key("is_save_local")
        file_type = data_vo.get_value_by_cons_key("file_type")
        save_type = data_vo.get_value_by_cons_key("save_type")
        panel_filename = data_vo.get_value_by_cons_key("panel_filename")
        board_filename = data_vo.get_value_by_cons_key("board_filename")
        save_path = data_vo.get_value_by_cons_key("save_path")
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path_type = data_vo.get_value_by_cons_key("save_path_type")
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")
        newline_type = data_vo.get_value_by_cons_key("newline_type")

        if save_type == "整板生成" and panel_filename == "---":
            return self.x_response("false", "请选择整板文件名格式！")

        if save_type == "拼板生成" and board_filename == "---":
            return self.x_response("false", "请选择拼板文件名格式！")

        if file_type != "json" and is_upload_mes == "是":
            return self.x_response("false", "目前仅支持上传json格式到Mes服务器，请将文件类型切换成json！")

        if not save_path:
            return self.x_response("false", "请选择数据路径！")

        self.log.info(f"文件生成格式：{file_type}")

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        if save_type == "整板生成":
            board_data = []
            pcb_board_user_ng_number = 0
            pcb_board_robot_ng_number = 0
            pcb_comp_user_ng_number = 0
            pcb_comp_robot_ng_number = 0
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                barcode = board_entity.barcode
                board_no = board_entity.board_no

                if board_entity.is_repair_ng():
                    pcb_board_user_ng_number += 1

                if board_entity.is_robot_ng():
                    pcb_board_robot_ng_number += 1

                pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
                pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

                comp_data = []
                for comp_entity in board_entity.yield_comp_entity():
                    # print(comp_entity)

                    area_val = ""
                    volume_val = ""
                    height_val = ""
                    x_offset_val = ""
                    y_offset_val = ""
                    for alg_entity in comp_entity.yield_alg_entity():

                        if alg_entity.test_name == "Area":
                            area_val = alg_entity.test_val
                        elif alg_entity.test_name == "Volume":
                            volume_val = alg_entity.test_val
                        elif alg_entity.test_name == "Height":
                            height_val = alg_entity.test_val
                        elif alg_entity.test_name == "XOffset":
                            x_offset_val = alg_entity.test_val
                        elif alg_entity.test_name == "YOffset":
                            y_offset_val = alg_entity.test_val

                    if comp_data_output == "全部" or \
                            (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                        comp_data.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,

                            "comp_volume": volume_val,
                            "comp_area": area_val,
                            "comp_height": height_val,
                            "comp_x_offset": x_offset_val,
                            "comp_y_offset": y_offset_val,
                        })

                board_data.append({
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_data": comp_data,
                })
            pcb_data = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
                "pcb_board_user_ng_number": pcb_board_user_ng_number,
                "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
                "pcb_comp_number": pcb_entity.comp_count,
                "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
                "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
                "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
                "board_data": board_data
            }

            time_date = time_str[:8]
            if save_path_type == "数据路径":
                dst_path = f"{save_path}"
            elif save_path_type == "数据路径/日期(yyyymmdd)":
                dst_path = f"{save_path}/{time_date}"
            elif save_path_type == "数据路径/设备名称":
                dst_path = f"{save_path}/{device_name}"
            elif save_path_type == "数据路径/设备名称/日期(yyyymmdd)":
                dst_path = f"{save_path}/{device_name}/{time_date}"
            elif save_path_type == "数据路径/日期(yyyymmdd)/设备名称":
                dst_path = f"{save_path}/{device_name}/{time_date}"
            else:
                return self.x_response("false", f"不支持的数据路径格式：{save_path_type}")

            if is_save_local == "是":
                xutil.FileUtil.ensure_dir_exist(dst_path)

            if panel_filename == "时间_整板条码":
                dst_filename = f"{time_str}_{pcb_sn}"
            elif panel_filename == "整板条码_时间":
                dst_filename = f"{pcb_sn}_{time_str}"
            elif panel_filename == "整板条码":
                dst_filename = f"{pcb_sn}"
            elif panel_filename == "时间":
                dst_filename = f"{time_str}"
            else:
                return self.x_response("false", f"不支持的整板文件名格式：{panel_filename}")

            if file_type == "csv":
                dst_filename = f"{dst_filename}.csv"
                dst_filepath = f"{dst_path}/{dst_filename}"

                comp_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    for _comp_data in _board_data.get("comp_data", {}):
                        _comp_data.update(_board_data)
                        comp_data_str += csv_comp_panel_template.format(**_comp_data)

                pcb_data["CompData"] = comp_data_str
                pcb_content = csv_pcb_panel_template.format(**pcb_data)
                if is_save_local == "是":
                    if newline_type == 'window':
                        pcb_content = pcb_content.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

            elif file_type == "txt":
                dst_filename = f"{dst_filename}.txt"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += txt_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += txt_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = txt_pcb_panel_template.format(**pcb_data)
                if is_save_local == "是":
                    if newline_type == 'window':
                        pcb_content = pcb_content.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

            elif file_type == "json":
                dst_filename = f"{dst_filename}.json"
                dst_filepath = f"{dst_path}/{dst_filename}"

                if is_save_local == "是":
                    xutil.FileUtil.dump_json_to_file(dst_filepath, pcb_data)

                if is_upload_mes == "是":
                    ret = xrequest.RequestUtil.post_json(mes_api_url, pcb_data)
                    if str(ret.get("code")) != "200":
                        return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

            elif file_type == "xml":
                dst_filename = f"{dst_filename}.xml"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += xml_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += xml_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = xml_pcb_panel_template.format(**pcb_data)

                if is_save_local == "是":
                    if newline_type == 'window':
                        pcb_content = pcb_content.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)
            else:
                return self.x_response("false", f"不支持的文件类型：{file_type}")

        elif save_type == "拼板生成":
            for board_entity in pcb_entity.yield_board_entity():
                board_sn = board_entity.barcode
                board_no = board_entity.board_no

                comp_data_list = []
                for comp_entity in board_entity.yield_comp_entity():
                    area_val = ""
                    volume_val = ""
                    height_val = ""
                    x_offset_val = ""
                    y_offset_val = ""
                    for alg_entity in comp_entity.yield_alg_entity():

                        if alg_entity.test_name == "Area":
                            area_val = alg_entity.test_val
                        elif alg_entity.test_name == "Volume":
                            volume_val = alg_entity.test_val
                        elif alg_entity.test_name == "Height":
                            height_val = alg_entity.test_val
                        elif alg_entity.test_name == "XOffset":
                            x_offset_val = alg_entity.test_val
                        elif alg_entity.test_name == "YOffset":
                            y_offset_val = alg_entity.test_val

                    if comp_data_output == "全部" or \
                            (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                        comp_data_list.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,

                            "comp_volume": volume_val,
                            "comp_area": area_val,
                            "comp_height": height_val,
                            "comp_x_offset": x_offset_val,
                            "comp_y_offset": y_offset_val,
                        })

                board_data_fmt = {
                    "device_name": device_name,
                    "pcb_sn": pcb_sn,
                    "pcb_track_line": pcb_entity.track_index,
                    "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                    "pcb_project_name": pcb_entity.project_name,
                    "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                    "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                    "pcb_final_result": pcb_entity.get_final_result(),
                    "pcb_repair_user": pcb_entity.repair_user,
                    "pcb_board_number": pcb_entity.board_count,
                    "pcb_comp_number": pcb_entity.comp_count,

                    "board_sn": board_sn,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),

                    "board_comp_number": board_entity.comp_total_number,
                    "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                    "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                    "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                }

                if board_filename == "时间_拼板条码":
                    filename = f"{time_str}_{board_sn}"
                elif board_filename == "拼板条码_时间":
                    filename = f"{board_sn}_{time_str}"
                elif board_filename == "拼板条码":
                    filename = f"{board_sn}"
                elif board_filename == "时间_拼板条码_拼板序号":
                    filename = f"{time_str}_{board_sn}_{board_no}"
                elif board_filename == "时间_拼板序号":
                    filename = f"{time_str}_{board_no}"
                elif board_filename == "拼板序号_时间":
                    filename = f"{board_no}_{time_str}"
                else:
                    return self.x_response("false", f"不支持的拼板文件名格式！")

                time_date = time_str[:8]
                if save_path_type == "数据路径":
                    dst_path = f"{save_path}"
                elif save_path_type == "数据路径/日期(yyyymmdd)":
                    dst_path = f"{save_path}/{time_date}"
                elif save_path_type == "数据路径/设备名称":
                    dst_path = f"{save_path}/{device_name}"
                elif save_path_type == "数据路径/设备名称/日期(yyyymmdd)":
                    dst_path = f"{save_path}/{device_name}/{time_date}"
                elif save_path_type == "数据路径/日期(yyyymmdd)/设备名称":
                    dst_path = f"{save_path}/{device_name}/{time_date}"
                else:
                    return self.x_response("false", f"不支持的数据路径格式：{save_path_type}")

                if is_save_local == "是":
                    xutil.FileUtil.ensure_dir_exist(dst_path)

                comp_data = ""
                if file_type == "txt":
                    filename = f"{filename}.txt"
                    for item in comp_data_list:
                        comp_data += txt_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = txt_board_board_template.format(**board_data_fmt)

                    if is_save_local == "是":
                        if newline_type == 'window':
                            board_data = board_data.replace('\n', '\r\n')

                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)
                elif file_type == "xml":
                    filename = f"{filename}.xml"
                    for item in comp_data_list:
                        comp_data += xml_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = xml_board_board_template.format(**board_data_fmt)

                    if is_save_local == "是":
                        if newline_type == 'window':
                            board_data = board_data.replace('\n', '\r\n')

                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)
                elif file_type == "json":
                    filename = f"{filename}.json"
                    board_data_fmt["comp_data"] = comp_data_list

                    if is_save_local == "是":
                        xutil.FileUtil.dump_json_to_file(f"{dst_path}/{filename}", board_data_fmt)

                    if is_upload_mes == "是":
                        ret = xrequest.RequestUtil.post_json(mes_api_url, board_data_fmt)
                        if str(ret.get("code")) != "200":
                            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

                elif file_type == "csv":
                    filename = f"{filename}.csv"
                    for item in comp_data_list:
                        comp_data += csv_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = csv_board_board_template.format(**board_data_fmt)

                    if is_save_local == "是":
                        if newline_type == 'window':
                            board_data = board_data.replace('\n', '\r\n')

                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)
                else:
                    return self.x_response("false", "不支持的文件类型！")

        else:
            return self.x_response("false", f"不支持的文件生成方式！")

        return self.x_response()
