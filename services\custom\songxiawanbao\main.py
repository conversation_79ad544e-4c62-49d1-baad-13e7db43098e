# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/10 上午11:05
# Author     ：sch
# version    ：python 3.8
# Description：松下万宝
"""
import json
from typing import Any

from common import xrequest, xcons
from common.xutil import LimitedDict
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

carry_type_map = {
    "无载体": 0,
    "载体与产品绑定": 1,
    "通过载体码返回产品标识": 2,
    "载体与产品解绑": 3,
}

error_map = {
    "1": "可生产",
    "16": "生产线中设备不存在",
    "17": "工单对应工艺下无该设备",
    "18": "工单不存在",
    "19": "不符合时间锁",
    "20": "不符合物料锁",
    "21": "产品不存在",
    "22": "非混产模式",
    "240": "无型号",
    "241": "不符合工序锁",
    "242": "未处于返修模式下出现不合格",
    "243": "互锁表达式异常",
    "244": "产品已下线",
    "245": "产品已报废",
    "246": "无生产计划",
    "247": "条码校验失败",
    "248": "物料缺料",
    "249": "烧录次数达到 3 次",
    "255": "服务端未定义异常而不能生产",
}

error_map2 = {
    "1": "成功",
    "2": "未知异常",
    "3": "产品标识为空",
    "4": "产品信息不完整",
    "5": "该工序无质检单模板",
    "6": "生产线标识错误",
}

csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}
整板图路径,{pcb_src_path}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

limit_product_id_map = LimitedDict(500)


class Engine(ErrorMapEngine):
    version = {
        "title": "songxiawanbao release v1.0.0.5",
        "device": "AIS303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-10 18:06  条码校验，上传数据
date: 2024-10-11 16:24  增加ftp前缀拼接路径
date: 2024-10-11 17:02  bugfix: 响应结果未正确映射 
date: 2024-10-14 11:10  条码校验失败不需要停机
date: 2024-10-17 15:00  bugfix: carryType 传int 
""", }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
    }

    combo = {
        "line_code": {
            "ui_name": "产线通讯标识",
            "item": [str(i) for i in range(256)],
            "value": "1"
        },
        "station_code": {
            "ui_name": "工位通讯标识",
            "item": [str(i) for i in range(256)],
            "value": "1"
        },
        "carry_type": {
            "ui_name": "载体方式",
            "item": list(carry_type_map.keys()),
            "value": "无载体"
        },
    }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/api/services/app/BarCodeReady/execute"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/api/services/app/ResultReady/execute"
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
        "ftp_path_concat": {
            "ui_name": "FTP 拼接前缀路径",
            "ui_name_en": "FTP Path",
            "value": "F:/TestReport"
        },
    }

    path = {
        # "save_path_img": {
        #     "ui_name": "保存路径(图片)",
        #     "value": "",
        # },
        # "save_path_file": {
        #     "ui_name": "保存路径(文件)",
        #     "value": "",
        # }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        # api_url_check = other_dao.get_value_by_cons_key("api_url_check")
        # line_code = other_dao.get_value_by_cons_key("line_code", to_int=True)
        # station_code = other_dao.get_value_by_cons_key("station_code", to_int=True)
        # carry_type = other_dao.get_value_by_cons_key("carry_type")
        #
        # carry_type = carry_type_map.get(carry_type)
        #
        ret_res = self.x_response()
        # for ix, sn in enumerate(other_dao.list_sn()):
        #     ix += 1
        #     check_param = {
        #         "LineIntCode": line_code,
        #         "StationIntCode": station_code,
        #         "BarCode": sn,
        #         "CarrierCode": "",
        #         "CarrierType": carry_type
        #     }
        #
        #     ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
        #     result_str = ret.get("result")
        #     result_dict = json.loads(result_str)
        #
        #     query_result = str(result_dict.get("QueryResult", 0))
        #
        #     product_id = str(result_dict.get("ProductId", ""))
        #     if query_result != "1":
        #         err_str = error_map.get(query_result)
        #
        #         ret_res = self.x_response("false", f"mes接口异常，No:{ix} SN:{sn}，error：{err_str}")
        #
        #     self.log.info(f"SN:{sn} --> ProductId:{product_id}  已缓存！")
        #     limit_product_id_map.add_item(sn, product_id)

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        line_code = data_vo.get_value_by_cons_key("line_code", to_int=True)
        station_code = data_vo.get_value_by_cons_key("station_code", to_int=True)

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        device_name = data_vo.get_value_by_cons_key("device_name")
        ftp_path_concat = data_vo.get_value_by_cons_key("ftp_path_concat")

        carry_type = data_vo.get_value_by_cons_key("carry_type")
        carry_type = carry_type_map.get(carry_type)

        api_url_check = data_vo.get_value_by_cons_key("api_url_check")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        file_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        file_date = file_time[:8]

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        pcb_sn = pcb_entity.pcb_barcode

        comp_data_str = ""

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        ftp_img_full_path = f"{ftp_path}/整板图片/{file_date}"
        # 1. 先上传整板图
        ftp_client.cd_or_mkdir(ftp_img_full_path)

        only_sn = pcb_sn
        if not only_sn and pcb_entity.all_barcode:
            only_sn = pcb_entity.all_barcode[0]

        pcb_src_img = pcb_entity.get_unknown_t_pcb_image()
        ftp_filename = f"{file_time}_{only_sn}.jpg"
        ftp_client.upload_file(pcb_src_img, ftp_filename)

        pcb_src_path1 = f"{ftp_path_concat}/{ftp_img_full_path}/{ftp_filename}"

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += csv_comp_panel_template.format(**{
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

        pcb_data = {
            "pcb_src_path": pcb_src_path1,
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "CompData": comp_data_str
        }

        pcb_content = csv_pcb_panel_template.format(**pcb_data)

        ftp_img_full_path = f"{ftp_path}/整板csv/{file_date}"
        ftp_client.cd_or_mkdir(ftp_img_full_path)

        ftp_filename2 = f"{file_time}_{only_sn}.csv"
        ftp_client.upload_content(ftp_filename2, pcb_content)

        file_ftp_filename = f"{ftp_path_concat}/{ftp_img_full_path}/{ftp_filename2}"

        # ret_res = self.x_response()

        err_msg_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            # product_id = limit_product_id_map.get_value(barcode, "")
            # self.log.info(f"条码：{barcode} 获取到缓存的productId是：{product_id}")

            check_param = {
                "LineIntCode": line_code,
                "StationIntCode": station_code,
                "BarCode": barcode,
                "CarrierCode": "",
                "CarrierType": carry_type
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
            result_str = ret.get("result")
            result_dict = json.loads(result_str)

            query_result = str(result_dict.get("QueryResult", 0))

            if query_result != "1":
                err_str = error_map.get(query_result)

                # ret_res = self.x_response("false", f"mes接口异常，No:{board_no} SN:{barcode}，error：{err_str}")
                self.log.warning(f"No:{board_no} SN:{barcode} 条码校验失败，不需要上传数据到Mes！")

                err_msg_list.append(f"mes接口异常，条码校验失败，No:{board_no} SN:{barcode}，error：{err_str}")
                continue

            product_id = str(result_dict.get("ProductId", ""))

            board_result = board_entity.get_repair_result("1", "2")

            data_param = {
                "LineIntCode": line_code,
                "StationIntCode": station_code,

                "DataItem": "2",
                "ProductId": product_id,
                "PTLVItems": [
                    {
                        "PTLVFlag": "1",
                        "Result": board_result,
                        "DataType": "1",
                        "DataLength": "1",
                        "Value": board_result,
                    },
                    {
                        "PTLVFlag": "4",
                        "Result": "1",
                        "DataType": "1",
                        "DataLength": len(file_ftp_filename),
                        "Value": file_ftp_filename,
                    },
                ],
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            result_str = ret.get("result")
            result_dict = json.loads(result_str)

            query_result = str(result_dict.get("HandleResult", 0))
            if query_result != "1":
                err_str = error_map2.get(query_result)
                err_msg_list.append(f"mes接口异常，上传数据失败，No:{board_no} SN:{barcode}，error：{err_str}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", err_str)

        return self.x_response()
