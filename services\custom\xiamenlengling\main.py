# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/20 上午10:20
# Author     ：sch
# version    ：python 3.8
# Description：厦门崚凌/tsmt
"""
import json
from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    ERROR_MAP = {
        "40X": {
            "0": {"standard": "OK", "custom_code": "", "custom_str": "OK"},
            "1": {"standard": "漏件", "custom_code": "DIP005", "custom_str": "MissingPart"},
            "2": {"standard": "错件", "custom_code": "DIP007", "custom_str": "WrongPart"},
            "3": {"standard": "反件", "custom_code": "DIP008", "custom_str": "ReversePart"},
            "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tomstone"},
            "5": {"standard": "偏移", "custom_code": "5", "custom_str": "ShiftPart"},
            "6": {"standard": "翻转", "custom_code": "6", "custom_str": "UpsideDown"},
            "7": {"standard": "浮高", "custom_code": "DIP014", "custom_str": "LiftedPackage"},
            "8": {"standard": "损件", "custom_code": "8", "custom_str": "Broken"},
            "9": {"standard": "露铜", "custom_code": "PCB001", "custom_str": "ExposeCopper"},
            "10": {"standard": "少锡", "custom_code": "DIP003", "custom_str": "InsufficientSolder"},
            "11": {"standard": "多锡", "custom_code": "DIP025", "custom_str": "ExcessSolder"},
            "12": {"standard": "未出脚", "custom_code": "DIP029", "custom_str": "NoPin"},
            "13": {"standard": "孔洞", "custom_code": "DIP016", "custom_str": "PinHole"},
            "14": {"standard": "连锡", "custom_code": "DIP006", "custom_str": "Bridge"},
            "15": {"standard": "锡珠", "custom_code": "DIP015", "custom_str": "SolderBall"},
            "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "LiftedLead"},
            "17": {"standard": "弯脚", "custom_code": "17", "custom_str": "ShiftedLead"},
            "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
            "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
            "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
            "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
            "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
            "23": {"standard": "虚焊", "custom_code": "23", "custom_str": "IncompleteWeld"},
            "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
            "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
            "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
            "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
            "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
            "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
            "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
            "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
            "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
            "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
            "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
            "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
            "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
        },
        "201": {

        },
        "301": {

        },
        "630": {

        }
    }

    version = {
        "title": "xiamenlengling release v1.0.0.5",
        "device": "203,303",
        "feature": ["校验空条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-20 14:15  校验空条码，上传数据到mes
date: 2023-11-21 15:46  参数名修改
date: 2023-11-23 15:33  success返回非1的时候，需要把所有的报错信息都打印出来
date: 2023-11-24 17:22  格式化返回数据，以及修正`judge`参数
date: 2024-04-10 10:40  boardid传int类型
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://10.16.0.28:8585/mes/processpass",
        },
        "board_id": {
            "ui_name": "板面",
            "value": "",
        },
        "line_no": {
            "ui_name": "生产线体",
            "value": "",
        },
        "account": {
            "ui_name": "操作工号",
            "value": "",
        },
        "model": {
            "ui_name": "生产机种",
            "value": "",
        },
        "station": {
            "ui_name": "生产站位",
            "value": "",
        },
        "machine": {
            "ui_name": "机台编号",
            "value": "",
        },
        "test_type": {
            "ui_name": "测试类型",
            "value": "0",
        },

    }

    combo = {
        "board_type": {
            "ui_name": "条码类型",
            "item": ["0", "1", "2", "3"],
            "value": "0"
        },
        "repair_flag": {
            "ui_name": "是否复判",
            "item": ["0", "1"],
            "value": "1"
        },
        "repair_control": {
            "ui_name": "维修管控",
            "item": ["0", "1"],
            "value": "1"
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        sn_list = other_vo.list_sn()
        if not sn_list:
            return self.x_response("false", f"设备未扫到条码，请检查！")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        board_id = data_vo.get_value_by_cons_key("board_id")
        line_no = data_vo.get_value_by_cons_key("line_no")
        account = data_vo.get_value_by_cons_key("account")
        model = data_vo.get_value_by_cons_key("model")
        station = data_vo.get_value_by_cons_key("station")
        machine = data_vo.get_value_by_cons_key("machine")
        test_type = data_vo.get_value_by_cons_key("test_type")
        board_type = data_vo.get_value_by_cons_key("board_type")
        repair_flag = data_vo.get_value_by_cons_key("repair_flag")
        repair_control = data_vo.get_value_by_cons_key("repair_control")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        board_data = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    repair_ng_code = comp_entity.repair_ng_code
                    custom_code = self.ERROR_MAP.get("40X", {}).get(repair_ng_code,
                                                                    {}).get('custom_code',
                                                                            repair_ng_code)

                    comp_data.append({
                        "errorprosition": comp_entity.designator,
                        "errorcode": comp_entity.robot_ng_str,
                        "judge": comp_entity.get_final_result("pass", "pass", "fail"),
                        "remark": custom_code
                    })

            board_data.append({
                "barcode": barcode,
                "serialno": board_no,
                "testresult": board_entity.get_robot_result("pass", "fail"),
                "defectinfos": comp_data
            })

        data_param = {
            "projectname": pcb_entity.project_name,
            "boardid": int(board_id),
            "boardtype": board_type,
            "lineno": line_no,
            "account": account,
            "model": model,
            "station": station,
            "machine": machine,
            "repairflag": repair_flag,
            "repaircontrol": repair_control,
            "testtype": test_type,
            "imagepath": "0",
            "starttime": start_time,
            "endtime": end_time,
            "deviceresults": board_data
        }

        ret = xrequest.RequestUtil.post_json(api_url, data_param)
        if str(ret.get('success')) != '1':
            return self.x_response("false", f"mes接口异常，上传测试数据失败，error: {json.dumps(ret, ensure_ascii=False, indent=4)}")

        return self.x_response()
