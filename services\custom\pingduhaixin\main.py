# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/31 上午9:18
# Author     ：sch
# version    ：python 3.8
# Description：平度海信
"""
import json
from typing import Any

from common import xrequest, xutil
from vo.mes_vo import DataVo, OtherVo, ComboVo
from engine.MesEngine import ErrorMapEngine

i_type_map = {
    "SMT": "1",
    "THT": "2",
    "基板": "3",
}

process_code_map = {
    "SMT": "0",
    "灯条": "1",
    "THT": "2",
}


class Engine(ErrorMapEngine):
    version = {
        "title": "pingduhaixin release v1.0.0.4",
        "device": "203、303、630",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-31 16:00  条码校验，上传数据
date: 2024-08-06 17:19  修改请求参数
date: 2024-08-16 16:44  增加上传数据的配置项
date: 2024-09-27 09:21  修改请求参数
""", }

    other_form = {
        "api_url_get_area": {
            "ui_name": "接口URL(获取线体)",
            "value": "http://***********:8093/api/Ats/AtsGetArea",
        },
        "api_url_get_workstation": {
            "ui_name": "接口URL(获取工作中心)",
            "value": "http://***********:8093/api/Ats/AtsGetWorkStations",
        },
        "api_url_check": {
            "ui_name": "接口URL(途程检查)",
            "value": "http://***********:8093/api/Ats/AtsCheckRun",
        },
        "api_url_data": {
            "ui_name": "接口URL(过站)",
            "value": "http://***********:8093/api/Ats/AtsTestRun",
        },
    }

    combo = {
        "line_list": {
            "ui_name": "线体",
            "item": [],
            "value": "",
        },
        "workstation_list": {
            "ui_name": "工作中心",
            "item": [],
            "value": "",
        },
        "i_type": {
            "ui_name": "对接代码",
            "item": ["SMT", "THT", "基板"],
            "value": "SMT",
        },
        "process_code": {
            "ui_name": "工序代码",
            "item": ["SMT", "灯条", "THT"],
            "value": "SMT",
        },
        "send_type": {
            "ui_name": "直通pass板卡是否需要复判",
            "item": ["Yes", "No"],
            "value": "No",
        }
    }

    form = {
        "emp_no": {
            "ui_name": "员工编号",
            "value": "",
        },
        "pc_name": {
            "ui_name": "电脑名称",
            "value": "",
        },
        "pc_ip": {
            "ui_name": "电脑IP地址",
            "value": "",
        },
        "dev_name": {
            "ui_name": "设备名称",
            "value": "镭晨AOI",
        },
        "dev_model": {
            "ui_name": "设备型号",
            "value": "",
        },
        "dev_no": {
            "ui_name": "设备编号",
            "value": "",
        },
    }

    # def __init__(self):
    #     self.common_config["sendmes_setting2"] = "仅保存检测NG器件列表"

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        i_type = other_vo.get_value_by_cons_key("i_type")
        emp_no = other_vo.get_value_by_cons_key("emp_no")
        workstation_list_select = other_vo.get_value_by_cons_key("workstation_list")

        i_type_val = i_type_map.get(i_type, "0")

        sn_list = other_vo.list_sn()
        if not sn_list:
            return self.x_response("false", f"未扫到条码！")

        workstation_map = xutil.CacheUtil.get("workstation_map", {})
        workstation_sn = workstation_map.get(workstation_list_select)

        if not workstation_sn:
            return self.x_response("false", f"请先选择工作中心并保存！")

        err_msg_list = []
        for sn in sn_list:
            check_param = {
                "iType": i_type_val,
                "empNo": emp_no,
                "pcbCode": sn,
                "workStationSn": workstation_sn
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

            if ret.get('code') != "OK":
                err_msg_list.append(f"SN:{sn} Error:{ret.get('result')}")

        if err_msg_list:
            err_msg_str = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口异常，途程检查失败，{err_msg_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        i_type = data_vo.get_value_by_cons_key("i_type")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        workstation_list_select = data_vo.get_value_by_cons_key("workstation_list")
        pc_name = data_vo.get_value_by_cons_key("pc_name")
        pc_ip = data_vo.get_value_by_cons_key("pc_ip")
        dev_name = data_vo.get_value_by_cons_key("dev_name")
        dev_model = data_vo.get_value_by_cons_key("dev_model")
        dev_no = data_vo.get_value_by_cons_key("dev_no")
        send_type = data_vo.get_value_by_cons_key("send_type")

        i_type_val = i_type_map.get(i_type, "0")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        workstation_map = xutil.CacheUtil.get("workstation_map", {})
        workstation_sn = workstation_map.get(workstation_list_select)

        if not workstation_sn:
            return self.x_response("false", f"请先选择工作中心并保存！")

        pcb_result = pcb_entity.get_final_result()
        inspect_type = data_vo.get_inspect_type()

        error_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            ix = 0
            comp_data_list = []
            only_one_item = {}

            for comp_entity in board_entity.yield_comp_entity():
                ix += 1

                item = {
                    "itemId": str(ix),
                    "itemName": f"{comp_entity.designator}",
                    "itemL_Limit": "",
                    "itemH_Limit": "",
                    "itemTsValue": "",
                    "TestValue": "",
                    "itemLenght": "",
                    "itemRes": comp_entity.get_final_result("OK", "OK", "NG"),
                    "errCodes": comp_entity.repair_ng_str
                }

                if not only_one_item:
                    only_one_item = item

                if comp_entity.is_repair_ng():
                    comp_data_list.append(item)

            if not comp_data_list:
                comp_data_list = [only_one_item]

            data_param = {
                "iType": i_type_val,
                "empNo": emp_no,
                "pcbCode": barcode,
                "workStationSn": workstation_sn,
                "tsRes": board_entity.get_repair_result("OK", "NG"),
                "tsLength": str(pcb_entity.get_cycle_time()),
                "pcName": pc_name,
                "pcIp": pc_ip,
                "devName": dev_name,
                "devModel": dev_model,
                "devNo": dev_no,
                "toolNo": "",
                "toolBitSn": "",
                "logData": "",
                "softVer": "",
                "data": {
                    "imeiInfo": "",
                    "meidInfo": "",
                    "macInfo": "",
                    "keyInfo": "",
                    "ProgramName": pcb_entity.project_name,
                    "topEltNumber": str(board_entity.comp_total_number),
                    "botEltNumber": str(board_entity.comp_total_number),
                },
                "atsTsItemsModels": comp_data_list
            }

            if send_type == "No":
                if inspect_type == "inspector":
                    if pcb_result == "PASS":
                        self.log.info(f"直通的板卡，主软件 检测完直接发送MES...")
                        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

                        if ret.get('code') != "OK":
                            error_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('result')}")
                    else:
                        self.log.info(f"主软件，NG的数据不需要发送MES！")

                else:
                    if pcb_result == "PASS":
                        self.log.warning(f"直通的板卡，维修站 不发送MES...")
                    else:
                        self.log.info(f"NG的板卡，复判后发送MES...")
                        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

                        if ret.get('code') != "OK":
                            error_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('result')}")
            else:
                self.log.info(f"正常发送MES...")
                ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

                if ret.get('code') != "OK":
                    error_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('result')}")

        if error_msg_list:
            err_msg_str = "\n".join(error_msg_list)
            return self.x_response("false", f"mes接口异常，Mes过站失败，{err_msg_str}")

        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, other_param: Any):
        combo_key = combo_vo.get_combo_key()

        if combo_key == "line_list":
            api_url_get_area = combo_vo.get_value_by_cons_key("api_url_get_area")
            process_code = combo_vo.get_value_by_cons_key("process_code")

            process_code_val = process_code_map.get(process_code)

            ret = xrequest.RequestUtil.get(api_url_get_area, {
                "processCode": process_code_val
            })

            if ret.get("code") != "OK":
                return self.x_response("false", f"mes接口异常，获取线体失败，error：{ret.get('result')}")

            area_list = ret.get("data", [])

            area_map = {}
            for item in area_list:
                area_map[item.get("areaName")] = item.get("areaId")

            ret_json = {
                "new_items": list(area_map.keys())
            }

            xutil.CacheUtil.set("area_map", area_map)

            return self.x_response("true", json.dumps(ret_json, ensure_ascii=False))

        elif combo_key == "workstation_list":
            api_url_get_workstation = combo_vo.get_value_by_cons_key("api_url_get_workstation")
            line_list_select = combo_vo.get_value_by_cons_key("line_list")

            area_map = xutil.CacheUtil.get("area_map", {})
            area_id = area_map.get(line_list_select)

            if not area_id:
                return self.x_response("false", f"请先选择线体并保存！")

            ret = xrequest.RequestUtil.get(api_url_get_workstation, {
                "areaId": area_id
            })

            if ret.get("code") != "OK":
                return self.x_response("false", f"mes接口异常，获取线体失败，error：{ret.get('result')}")

            workstation_list = ret.get("data", [])

            workstation_map = {}
            for item in workstation_list:
                workstation_map[item.get("workStationName")] = item.get("workStationSn")

            ret_json = {
                "new_items": list(workstation_map.keys())
            }
            xutil.CacheUtil.set("workstation_map", workstation_map)

            return self.x_response("true", json.dumps(ret_json, ensure_ascii=False))
