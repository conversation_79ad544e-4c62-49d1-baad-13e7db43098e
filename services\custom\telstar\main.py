# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/29 下午5:35
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from typing import Any

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import log, x_response
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


def refresh_token(api_url, user_no, password):
    """
    刷新token
    """
    log.info(f"没有获取到token，开始去获取token...")

    param = {
        "userNo": user_no,
        "password": password
    }

    ret = xrequest.RequestUtil.post_json(api_url, param)

    if ret.get("msgId") != 0:
        return x_response("false", f"接口响应异常, 登录失败, error: {ret.get('msgStr')}")

    global_data["token"] = ret.get("token")


class Engine(ErrorMapEngine):
    version = {
        "title": "Telstar release v1.0.0.2",
        "device": "401B、401B-D、631M-D",
        "feature": ["从MES获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-30 16:35  登录，获取条码，条码校验，上传数据
date: 2024-09-27 16:36  部分参数需要改成双轨配置
""",
    }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(登录)",
            "ui_name_en": "APIURL(clientLogin)",
            "value": "http://127.0.0.1:8081/swiftmom/api/clientLogin",
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "ui_name_en": "APIURL(getRelationPcbSeq)",
            "value": "http://127.0.0.1:8081/swiftmom/api/getRelationPcbSeq",
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "ui_name_en": "APIURL(checkRoute)",
            "value": "http://127.0.0.1:8081/swiftmom/api/checkRoute",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "ui_name_en": "APIURL(createAOIData)",
            "value": "http://127.0.0.1:8081/swiftmom/api/createAOIData",
        },
        "type_ui": {
            "ui_name": "type",
            "value": "1",
        },
    }

    form = {
        "user_no": {
            "ui_name": "工号",
            "ui_name_en": "UserNo",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "ui_name_en": "Password",
            "value": "",
        },
        "prod_no1": {
            "ui_name": "工单号(1轨)",
            "ui_name_en": "prodNo(1)",
            "value": "",
        },
        "prod_no2": {
            "ui_name": "工单号(2轨)",
            "ui_name_en": "prodNo(2)",
            "value": "",
        },
        "org_id": {
            "ui_name": "公司名",
            "ui_name_en": "orgId",
            "value": "ZS",
        },
        "station_no1": {
            "ui_name": "站位编号(1轨)",
            "ui_name_en": "stationNo(1)",
            "value": "",
        },
        "station_no2": {
            "ui_name": "站位编号(2轨)",
            "ui_name_en": "stationNo(2)",
            "value": "",
        },
        "machine_no": {
            "ui_name": "机器编号",
            "ui_name_en": "machineNo",
            "value": "2-A.AOI",
        },
        "thread_no1": {
            "ui_name": "线别编号(1轨)",
            "ui_name_en": "threadNo(1)",
            "value": "2-A",
        },
        "thread_no2": {
            "ui_name": "线别编号(2轨)",
            "ui_name_en": "threadNo(2)",
            "value": "2-A",
        },
        "company_no": {
            "ui_name": "公司编号",
            "ui_name_en": "companyNo",
            "value": "ZS",
        },
    }

    combo = {
        "is_upload_mes_1": {
            "ui_name": "是否上传数据(1轨)",
            "ui_name_en": "isUploadData(1)",
            "item": ["true", "false"],
            "value": "true",
        },
        "is_upload_mes_2": {
            "ui_name": "是否上传数据(2轨)",
            "ui_name_en": "isUploadData(2)",
            "item": ["true", "false"],
            "value": "true",
        },
        "is_retest1": {
            "ui_name": "重复测试(1轨)",
            "ui_name_en": "retest(1)",
            "item": ["true", "false"],
            "value": "true",
        },
        "is_retest2": {
            "ui_name": "重复测试(2轨)",
            "ui_name_en": "retest(2)",
            "item": ["true", "false"],
            "value": "true",
        },
        "board_side_ui1": {
            "ui_name": "板面(1轨)",
            "ui_name_en": "BoardSide(1)",
            "item": ["T", "B", "T+B"],
            "value": "T",
        },
        "board_side_ui2": {
            "ui_name": "板面(2轨)",
            "ui_name_en": "BoardSide(2)",
            "item": ["T", "B", "T+B"],
            "value": "T",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登陆",
            "ui_name_en": "LoginUser",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 1)

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_no = other_vo.get_value_by_cons_key("user_no")
        password = other_vo.get_value_by_cons_key("password")

        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        type_ui = other_vo.get_value_by_cons_key("type_ui")

        track_index = other_vo.get_track_index()

        if track_index == 1:
            prod_no = other_vo.get_value_by_cons_key("prod_no1")
        else:
            prod_no = other_vo.get_value_by_cons_key("prod_no2")

        org_id = other_vo.get_value_by_cons_key("org_id")

        param = {
            "pcbSeq": other_vo.get_pcb_sn(),
            "prodNo": prod_no,
            "orgId": org_id,
            "type": type_ui
        }

        token = global_data.get("token")

        if not token:
            if x_res := refresh_token(api_url_login, user_no, password):
                return x_res

        token = global_data.get("token")

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, param, headers={"token": token})
        if ret.get("msgId") != 0:
            return x_response("false", f"接口响应异常, 获取条码失败, error: {ret.get('msgStr')}")

        data = ret.get("data")
        ret_sn = [item.get("pcbSeq") for item in data]

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_no = other_vo.get_value_by_cons_key("user_no")
        password = other_vo.get_value_by_cons_key("password")

        api_url_check = other_vo.get_value_by_cons_key("api_url_check")

        token = global_data.get("token")

        if not token:
            if x_res := refresh_token(api_url_login, user_no, password):
                return x_res

        token = global_data.get("token")

        sn_list = other_vo.list_sn()

        track_index = other_vo.get_track_index()

        if track_index == 1:
            prod_no = other_vo.get_value_by_cons_key("prod_no1")
            station_no = other_vo.get_value_by_cons_key("station_no1")
            is_retest = other_vo.get_value_by_cons_key("is_retest1")
        else:
            prod_no = other_vo.get_value_by_cons_key("prod_no2")
            station_no = other_vo.get_value_by_cons_key("station_no2")
            is_retest = other_vo.get_value_by_cons_key("is_retest2")

        err_msg_list = []

        for ix, sn in enumerate(sn_list):
            ix += 1
            param = {
                "pcbSeq": sn,
                "prodNo": prod_no,
                "stationNo": station_no,
                "retest": 1 if is_retest == "true" else 0
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, param, headers={"token": token})
            if ret.get("msgId") != 0:
                err_msg_list.append(f"No:{ix} SN:{sn} Error:{ret.get('msgStr')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return x_response("false", f"接口响应异常, 条码校验失败, {err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_login = data_vo.get_value_by_cons_key("api_url_login")
        user_no = data_vo.get_value_by_cons_key("user_no")
        password = data_vo.get_value_by_cons_key("password")

        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        company_no = data_vo.get_value_by_cons_key("company_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.get_track_index()

        if track_index == 1:
            prod_no = data_vo.get_value_by_cons_key("prod_no1")
            station_no = data_vo.get_value_by_cons_key("station_no1")
            board_side_ui = data_vo.get_value_by_cons_key("board_side_ui1")
            thread_no = data_vo.get_value_by_cons_key("thread_no1")
            is_upload_data = data_vo.get_value_by_cons_key("is_upload_mes_1")
        else:
            prod_no = data_vo.get_value_by_cons_key("prod_no2")
            station_no = data_vo.get_value_by_cons_key("station_no2")
            board_side_ui = data_vo.get_value_by_cons_key("board_side_ui2")
            thread_no = data_vo.get_value_by_cons_key("thread_no2")
            is_upload_data = data_vo.get_value_by_cons_key("is_upload_mes_2")

        token = global_data.get("token")

        if not token:
            if x_res := refresh_token(api_url_login, user_no, password):
                return x_res

        token = global_data.get("token")

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        if is_upload_data == "false":
            log.warning(f"[{track_index}轨]未开启上传数据，跳过上传数据到mes！")
            return self.x_response()

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            remark_list = []

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_des = comp_entity.designator
                    repair_ng_code = comp_entity.repair_ng_code

                    remark_list.append(f'{comp_des}_{repair_ng_code}')

            now = xutil.DateUtil.get_datetime_now("%Y-%m-%d %H:%M:%S")
            data_param = {
                "pcbSeq": barcode,
                "createdDateTime": now,
                "prodNo": prod_no,
                "result": board_entity.get_repair_result("PASS", "FAIL"),
                "oriResult": board_entity.get_robot_result("PASS", "FAIL"),
                "machineNo": machine_no,
                "threadNo": thread_no,
                "beginTime": start_time,
                "endTime": end_time,
                "board": board_side_ui,
                "remark": ",".join(remark_list),
                "companyNo": company_no,
                "mainPcbSeq": pcb_entity.pcb_barcode,
                "partSn": board_entity.board_no,
                "trackName": str(pcb_entity.track_index),
                "badPointQty": str(board_entity.comp_repair_ng_number),
                "pointQty": str(board_entity.comp_total_number),
                "siteNo": station_no
            }

            res = xrequest.RequestUtil.post_json(api_url_data, data_param, headers={"token": token})
            if res.get("result") != 0:
                err_msg_list.append(f"No:{board_entity.board_no} SN:{barcode} Error:{res.get('message')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return x_response("false", f"接口响应异常, 上传数据失败, {err_str}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):

        if btn_vo.get_btn_key() == "login_btn":
            api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
            user_no = btn_vo.get_value_by_cons_key("user_no")
            password = btn_vo.get_value_by_cons_key("password")

            if res := refresh_token(api_url_login, user_no, password):
                return res

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_no = other_vo.get_value_by_cons_key("user_no")
        password = other_vo.get_value_by_cons_key("password")

        refresh_token(api_url_login, user_no, password)
