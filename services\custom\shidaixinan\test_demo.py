# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/12/19 上午11:45
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import base64

from common import xutil


# ret_str = """<SOAP-ENV:Envelope xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
#    <SOAP-ENV:Body>
#       <ns2:getCellListByTrayIdResponse xmlns:ns2="http://celltest.ws.atlmes.com/">
#          <return>
#             <code>0</code>
#             <sfcList>C02020100033A00230920Z00063</sfcList>
#             <sfcList/>
#             <sfcList>C02020100033A00230920Z00064</sfcList>
#             <sfcList/>
#             <processlot>2H2L-0001</processlot>
#          </return>
#       </ns2:getCellListByTrayIdResponse>
#    </SOAP-ENV:Body>
# </SOAP-ENV:Envelope>"""
# root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
# res_item = root[0][0][0]
#
# sfc_list = []
# code = "1"
# for i in res_item:
#     i_str = i.text if i.text is not None else ""
#     if i.tag == "sfcList":
#         sfc_list.append(i_str)
#     elif i.tag == "code":
#         code = i_str
#
# print(sfc_list)
# print(code)

# ret_str = """<SOAP-ENV:Envelope xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
#    <SOAP-ENV:Body>
#       <ns2:processLotStartForSfcResponse xmlns:ns2="http://machine.ws.atlmes.com/">
#          <return>
#             <code>20982</code>
#             <message>There isn't any SFC in ProcessLot group</message>
#          </return>
#       </ns2:processLotStartForSfcResponse>
#    </SOAP-ENV:Body>
# </SOAP-ENV:Envelope>"""
#
# root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
# res_item = root[0][0][0]
#
# print(res_item.find('code').text)
# print(res_item.find('message').text)

# auth_string = f"admin:123456"
# encoded_auth = base64.b64encode(auth_string.encode()).decode()
#
# authorization = 'Basic ' + encoded_auth
# # print(encoded_auth)
# print(authorization)

# def f1(comp_str: str) -> bool:
#     return comp_str.endswith('复判OK')


data = ['T1漏铜', 'T2漏铜_复判OK', 'T3露铜_复判OK']

all_ok = all(map(lambda comp_str: comp_str.endswith('复判OK'), data))
print(all_ok)


all_error_code = ["SHAOXI", "WEICHUJIAO", "KONGDONG", "LUTONG", "QIAOLIAN"]
ng_error_code = ["WEICHUJIAO", "KONGDONG", "LUTONG", "QIAOLIAN"]

pass_category  = [i for i in all_error_code if i not in ng_error_code]
print(pass_category)


