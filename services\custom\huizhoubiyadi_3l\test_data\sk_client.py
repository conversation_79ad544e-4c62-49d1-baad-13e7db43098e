# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : sk_client.py
# Time       ：2025/5/16 上午9:59
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import socket
import struct
import json


class SKClient:
    """
    一个简单的 SK (Service Kernel) 客户端。
    """

    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None

    def connect(self):
        """
        连接到 SK 服务器。
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            print(f"成功连接到 SK 服务器 {self.host}:{self.port}")
        except socket.error as e:
            print(f"连接到 SK 服务器失败: {e}")
            self.socket = None
            return False
        return True

    def disconnect(self):
        """
        断开与 SK 服务器的连接。
        """
        if self.socket:
            self.socket.close()
            self.socket = None
            print("与 SK 服务器断开连接")

    def send_request(self, request):
        """
        向 SK 服务器发送请求。
        :return: 从服务器接收到的响应 (字典类型)，或者 None 如果发生错误。
        """
        if not self.socket:
            print("未连接到 SK 服务器")
            return None

        request_json = json.dumps(request).encode('utf-8')

        try:
            self.socket.sendall(request_json)

            # 接收响应数据
            response_json = self.socket.recv(4096)  # 每次最多接收 4096 字节

            return json.loads(response_json.decode('utf-8'))

        except socket.error as e:
            print(f"发送请求或接收响应时发生错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解码错误: {e}")
            return None


# 示例用法
if __name__ == "__main__":
    client = SKClient("localhost", 10004)  # 替换为你的 SK 服务器地址和端口

    if client.connect():
        # data = {
        #     "cmd": "afterConnected",
        #     "messageId": "20250426111011123423",
        #     "attr": ""
        # }
        data = {
            "cmd": "onChangeProgram",
            "messageId": "20250426111011123423",
            "sn": "sn1111",
            "attr": ""
        }

        # 发送一个示例请求
        response = client.send_request(data)

        if response:
            print("收到响应:")
            print(json.dumps(response, indent=4, ensure_ascii=False))  # 使用 indent 格式化输出

        client.disconnect()
    else:
        print("未能连接到服务器，程序退出。")
