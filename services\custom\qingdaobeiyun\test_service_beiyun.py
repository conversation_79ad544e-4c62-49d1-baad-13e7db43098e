from flask import Flask, request, jsonify
import json

app = Flask(__name__)


@app.route('/aoi/upload2', methods=['POST'])
def upload_data():
    # 获取请求数据
    data = request.get_json()

    # 打印接收到的完整数据
    print("Received data:", json.dumps(data, indent=2, ensure_ascii=False))

    # 验证数据格式
    required_fields = ['workstation', 'emp', 'jsonData']
    for field in required_fields:
        if field not in data:
            return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400

    try:
        json_data = json.loads(data['jsonData'])
        print("Parsed jsonData:", json.dumps(json_data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError:
        return jsonify({"success": False, "message": "Invalid JSON format in jsonData"}), 400

    # 返回成功响应
    return jsonify({"success": False, "message": "Data received and validated successfully"})


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8712, debug=True)