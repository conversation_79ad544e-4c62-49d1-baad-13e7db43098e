# -*- coding:UTF-8 -*-
# <AUTHOR> sunchang<PERSON>g
# time : 2022/5/23 下午12:00
"""
测试帮手
模拟AOI设备发送数据
1. 模拟发送[条码过站]数据
2. 模拟发送[保存测试]数据
"""
import json
import socket
import traceback

host = '127.0.0.1'
port = 9090
buffer_size = 4096


def _base_send_call_data(func_name, func_args, other_param=None):
    """

    :param func_name: 函数名称： （CheckBarcode，检查条码）（GetBadBoard，获取条码）（GetBarcodeList，获取条码列表）
    :param func_args:
    :return:
    """
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client.settimeout(3)

    try:
        base_param = {
            "funcName": func_name,
            "funcArgs": func_args,
        }
        if other_param and type(other_param) is dict:
            base_param.update(other_param)

        print('-----------请求参数-----------')
        print(base_param)

        client.connect((host, port))
        send_data = json.dumps(base_param)

        client.send(bytes(send_data, encoding="utf-8"))
        receive_data = client.recv(buffer_size)
        # ret_data: {'key': '2022.05.23 12:15:44', 'result': False, 'string': '找不到此序号讯息H065W232392173402'}
        ret_data = json.loads(receive_data)
        print("-------------响应参数------------")
        print(ret_data)

    except Exception as _:
        print(_)
        traceback.format_exc()

    finally:
        client.close()


def send_barcode_to_mes_config(barcode: str):
    """
    模拟发送[条码过站]数据
    :param barcode: 条码
    :return:
    """
    _base_send_call_data("CheckBarcode", [[barcode]])


def send_device_status_to_mes_config(device_status):
    """
    发送设备状态给配置器
    :param device_status 去 constants.py 取
    :return:
    """
    _base_send_call_data("SendDeviceStatus", [device_status, "admin"])


def send_test_data_to_mes_config(file_path: str):
    """
    发送测试数据
    :param file_path: T_20220523164745910_1_NG数据绝对路径，
    如：/home/<USER>/aoi/run/results/t.001/20220523/T_20220523164745910_1_NG
    :return:
    """
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client.settimeout(5)

    try:
        client.connect((host, port))
        send_data = json.dumps({
            "ReviewPath": file_path
        })
        client.send(bytes(send_data, encoding="utf-8"))
        receive_data = client.recv(buffer_size)
        ret_data = json.loads(receive_data)
        print("---------------ret data------------")
        print(ret_data)
        # b'{"Description":"","MesStatus":true,"SocketStatus":true}'
        print("---------------ret data------------")
        # return ret_data["MesStatus"] and ret_data["SocketStatus"], ret_data["Description"]

    except Exception as e:
        print(e)
        traceback.format_exc()
        # traceback.print_exc()
        # return False, "发送测试数据失败"
    finally:
        client.close()


def send_login_sign_to_mes_config(username, password):
    """
    发送登录信号给Mes
    :param username 用户名
    :param password 密码
    :return:
    """
    _base_send_call_data("CheckLonginPermission", [username, password])


def send_get_sn_list_to_mes_config(barcode):
    """
    获取条码列表
    :param barcode 条码
    :return:
    """
    _base_send_call_data("GetBarcodeList", [[barcode]])


def send_json_data_to_mes_config(json_data: dict):
    """
    发送mes数据给mes配置器
    """
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client.settimeout(5)

    try:
        client.connect((host, port))
        send_data = json.dumps(json_data)
        client.send(bytes(send_data, encoding="utf-8"))
        receive_data = client.recv(buffer_size)
        ret_data = json.loads(receive_data)
        print("---------------ret data------------")
        print(ret_data)
        print("---------------ret data------------")

    finally:
        client.close()


if __name__ == '__main__':
    # 1. 发送条码到Mes配置器
    # send_barcode_to_mes_config("fake_barcode_202206300918")

    # 2. 发送设备状态到Mes配置器
    # send_device_status_to_mes_config("安全门")

    # 3. 发送测试数据到Mes配置器
    # test_path = "/home/<USER>/Desktop/test_data/T_20220826102724534_1_NG"
    # # test_path = "/home/<USER>/aoi/run/results/t.001/20220802/T_20220802152547020_1_NG"
    # send_test_data_to_mes_config(test_path)

    # send_get_sn_list_to_mes_config("111")

    # send_login_sign_to_mes_config("admin", "111")

    # 8. 发送数据检查程序信息
    # _base_send_call_data("CheckProgramInfo", [], {"programName": "foo1",
    #                                               "orderID": "foo2",
    #                                               "otherInfo": "CX1-1,half solder;R1-1,Missing solder;CC,NNNN;"})

    # ---------------------------v2 版本的发送数据-------------------------------
    # 1. 条码校验
    # send_json_data_to_mes_config({
    #     "funcArgs": [
    #         [
    #             "D3VZ15KBT4021000PO2050",
    #         ]
    #     ],
    #     "funcName": "CheckBarcode",
    #     "trackIndex": 0
    # })

    # 1. 发送数据到mes
    send_json_data_to_mes_config(
        {
            "funcArgs": [
                "安全门",
                "admin"
            ],
            "funcName": "SendDeviceStatus",
            "userName": "admin",
            "deviceType": "AIS431",
            "statusCodeV3": "2043",
            "statusDescV3": "进板",
            "trackIndex": 1,
        }
    )

    # 3. 发送设备状态
    # send_json_data_to_mes_config({
    #     "funcArgs": [
    #         "安全门",
    #         "admin"
    #     ],
    #     "funcName": "SendDeviceStatus",
    #     "userName": "admin",
    #     "statusCode": "10",
    #     "statusDesc": "安全门"
    # })

    # 4. 获取条码
    # send_json_data_to_mes_config({
    #     "funcArgs": [
    #         "832378AE10000100"
    #     ],
    #     "funcName": "GetBarcodeList",
    #     "trackIndex": 0
    # })
    #
    # # 5. 从mes获取板式
    # send_json_data_to_mes_config({
    #     "funcArgs": [
    #         "832378AE10000100"
    #     ],
    #     "funcName": "GetProjectNameBySn",
    #     "barcode": "1111",
    # })
