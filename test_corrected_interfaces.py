#!/usr/bin/env python
# -*-coding:utf-8 -*-

"""
测试修正后的君胜MES接口参数格式
"""

import json

def test_corrected_interface_formats():
    """测试修正后的接口参数格式"""
    
    print("=== 修正后的君胜MES接口参数格式 ===\n")
    
    # 1. 零件进站接口 - customMaterialTrackIn
    print("1. 零件进站接口 (customMaterialTrackIn):")
    track_in_param = {
        "prebSnr": "008xxxxxxxxx1",
        "mesServer": "Stg/Prd",
        "mesStation": "SMT_Coating_xxx"
    }
    print(json.dumps(track_in_param, indent=2, ensure_ascii=False))
    print()
    
    # 2. 程式检查接口 - customVerifyMaterialRecipe
    print("2. 程式检查接口 (customVerifyMaterialRecipe):")
    verify_recipe_param = {
        "prebSnr": "008xxxxxxxxx1",
        "mesServer": "Stg/Prd",
        "mesStation": "SMT_Coating_xxx",
        "program": "13251-xxx-xxxx.mpp"
    }
    print(json.dumps(verify_recipe_param, indent=2, ensure_ascii=False))
    print()
    
    # 3. 上传测试结果接口 - customUploadTestResult (修正后)
    print("3. 上传测试结果接口 (customUploadTestResult) - 修正后:")
    upload_result_param = {
        "prebSnr": "Stencil#xxxxxxxxx1",
        "mesServer": "Stg/Prd",
        "mesStation": "SMT_Coating_xxx",
        "operator": "Operator123",
        "programName": "13251-xxxx_test",
        "testStartTime": "2024-11-11 08:30:00",
        "testEndTime": "2024-11-11 08:30:00",
        "parameter1": "40.00",
        "parameter2": "40.00",
        "parameter3": "40.00",
        "parameterX": "40.00",
        "result": "Pass",
        "reportPath": "M:\\abc\\ff.txt",
        "picturePath": "M:\\abc\\ff.jpg",
        "reportPath1": "M:\\abc\\ff.txt",
        "picturePath1": "M:\\abc\\ff.jpg"
    }
    print(json.dumps(upload_result_param, indent=2, ensure_ascii=False))
    print()
    
    # 4. 上传测试不良接口 - customMaterialEndWithDefects
    print("4. 上传测试不良接口 (customMaterialEndWithDefects):")
    end_with_defects_param = {
        "mesServer": "Prd",
        "UID": "30880000995038",
        "ResourceName": "AOI_Line4-1",
        "OperationResult": "Fail",
        "IsToPerformMoveNext": True,
        "DefectParameters": [
            {
                "Reason": "matching value",
                "ReferenceDesignator": "T101-2",
                "Remark": "7,9-11"
            },
            {
                "Reason": "matching value",
                "ReferenceDesignator": "R102", 
                "Remark": "12,9-11"
            }
        ],
        "Reason": "matching value",
        "ReferenceDesignator": "T101-2",
        "Remark": "7,9-11",
        "reportPath": "M:\\abc\\ff.txt",
        "picturePath": "M:\\abc\\ff.jpg"
    }
    print(json.dumps(end_with_defects_param, indent=2, ensure_ascii=False))
    print()
    
    # 5. 上传复判不良接口 - customMaterialFixDefects
    print("5. 上传复判不良接口 (customMaterialFixDefects):")
    fix_defects_param = {
        "mesServer": "Prd",
        "UID": "30880000995038",
        "ResourceName": "AOI_Line4-1",
        "OperationResult": "Fail",
        "IsToPerformMoveNext": True,
        "DefectParameters": [
            {
                "Reason": "matching value",
                "ReferenceDesignator": "T101-2",
                "Remark": "7,9-11"
            }
        ],
        "Reason": "matching value",
        "ReferenceDesignator": "T101-2",
        "Remark": "7,9-11",
        "reportPath": "",
        "picturePath": ""
    }
    print(json.dumps(fix_defects_param, indent=2, ensure_ascii=False))
    print()

def test_key_differences():
    """展示修正前后的关键差异"""
    print("=== 关键修正点 ===\n")
    
    print("customUploadTestResult 接口修正:")
    print("修正前 (错误):")
    print("- 使用了 UID, ResourceName, OperationResult, IsToPerformMoveNext, DefectParameters")
    print()
    print("修正后 (正确):")
    print("- prebSnr: 条码")
    print("- mesServer: 服务器")
    print("- mesStation: 工位")
    print("- operator: 操作员")
    print("- programName: 程式名")
    print("- testStartTime: 测试开始时间")
    print("- testEndTime: 测试结束时间")
    print("- parameter1-X: 工艺参数")
    print("- result: 测试结果")
    print("- reportPath/picturePath: 文件路径")

if __name__ == "__main__":
    test_corrected_interface_formats()
    print()
    test_key_differences()
