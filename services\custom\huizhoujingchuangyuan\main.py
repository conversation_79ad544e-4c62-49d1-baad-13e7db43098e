#!/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/12/6 下午14:40
# Author     ：gyr
# version    ：python 3.8
# Description：惠州精创源
"""
from typing import Any

from common import xrequest, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo


req_template = """<?xml version="1.0" encoding="utf-8"?>
        <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
          <soap:Body>
            <GetApiColl xmlns="http://tempuri.org/">
              <TASK_NO>{TASK_NO}</TASK_NO>
              <LINE_NO>{LINE_NO}</LINE_NO>
              <BOARD_BARCODE>{BOARD_BARCODE}</BOARD_BARCODE>
              <FLAG>{FLAG}</FLAG>
              <STRFLAG>{STRFLAG}</STRFLAG>
              <NUMBERFLAG>{NUMBERFLAG}</NUMBERFLAG>
              <IRETURN>{IRETURN}</IRETURN>
              <RETURN>{RETURN}</RETURN>
              <GET_QTY>{GET_QTY}</GET_QTY>
              <USER>{USER}</USER>
            </GetApiColl>
          </soap:Body>
        </soap:Envelope>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoujingchuangyuan release v1.0.0.1",
        "device": "AIS401",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2024-12-10 15:58  上传数据到mes
""", }

    # 渲染出前端界面给用户填写配置参数
    other_form = {
        "interface_address": {
            "ui_name": "接口地址",
            "value": "http://*************/WebService1.asmx"
        },
    }

    form = {
        "task_no": {
            "ui_name": "工单",
            "value": ""
        },
        "line_no": {
            "ui_name": "工序线体",
            "value": ""
        },
        "USER": {
            "ui_name": "用户",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        向MES系统发送数据的核心方法，包含构建请求、发送请求、处理响应等完整逻辑
        :param data_vo: 包含PCB相关数据的数据对象
        :param other_data: 其他可能需要的数据（当前未使用，可根据实际情况补充）
        :param other_param: 其他参数（当前未使用，可根据实际情况补充）
        :return: 包含操作结果状态及消息的字典
        """
        # 1. 把界面参数都取出来
        api_url = data_vo.get_value_by_cons_key("interface_address")
        task_no = data_vo.get_value_by_cons_key("task_no")
        line_no = data_vo.get_value_by_cons_key("line_no")
        user = data_vo.get_value_by_cons_key("USER")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            str_flag = ""
            repair_ng_code = ""
            comp_tag_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag_list.append(comp_entity.designator)

                    if not str_flag:
                        str_flag = comp_entity.repair_ng_str

                    if not repair_ng_code:
                        repair_ng_code = comp_entity.repair_ng_code

            # 2. 使用Python的format函数，把界面上的配置参数和检测出来的参数格式化到template
            data_param = req_template.format(**{
                "TASK_NO": task_no,
                "LINE_NO": line_no,
                "BOARD_BARCODE": barcode,
                "FLAG": repair_ng_code,
                "STRFLAG": str_flag,
                "NUMBERFLAG": ",".join(comp_tag_list),
                "IRETURN": board_entity.get_robot_result("OK", "NG"),
                "RETURN": board_entity.get_repair_result("OK", "NG"),
                "GET_QTY": "1",
                "USER": user,
            })

            # 3. 使用post_soap去请求客户接口
            ret_str = xrequest.RequestUtil.post_soap(
                api_url,
                data_param,
                soap_action="http://tempuri.org/GetApiColl"
            )

            # 4. 拿到客户的响应参数
            root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

            # 5 判断接口的响应，对对应响应

            # 找到GetApiCollResponse元素
            get_api_coll_response = root.find('.//{http://tempuri.org/}GetApiCollResponse')

            # 获取GetApiCollResult标签内容
            get_api_coll_result = get_api_coll_response.find('{http://tempuri.org/}GetApiCollResult').text

            # 获取StrReturn标签内容
            str_return = get_api_coll_response.find('{http://tempuri.org/}StrReturn').text

            if get_api_coll_result != 'true':
                error_msg = f"mes接口异常，上传数据失败，error：{str_return}"
                ret_res = self.x_response("false", error_msg)

        return ret_res


