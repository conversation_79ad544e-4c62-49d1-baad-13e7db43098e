# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/3 上午9:40
# Author     ：sch
# version    ：python 3.8
# Description：浙江博来
"""

from typing import Any

from common import xsql, xcons, xutil
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

pcb_template = """<?xml version="1.0" encoding="utf-8"?>
  <Panel projectName="{project_name}" pcbBarcode="{barcode}" pcbUserResult="{repair_result}" pcbFinalResult="{final_result}" testTime="{start_time}" repairUser="{repair_user}">
    <BoardList>{BoardData}
    </BoardList>
    <Data boardNumber="{board_count}" boardUserNgNumber="{pcb_board_user_ng_number}" ver=""/>
  </Panel>
"""

board_template = """
      <Board barcode="{barcode}" id="{board_no}" userResult="{repair_result}" finalResult="{final_result}">{CompData}
      </Board>"""

comp_template = """
        <Comp designator="{tag}" part="{part}" package="{package}" type="{type}" robotCode="{robot_ng_code}" robotResult="{robot_ng_str}" userCode="{robot_ng_code}" userResult="{repair_ng_str}" compImage="{image_path}"/>"""


v2_board_template = """<?xml version="1.0" encoding="utf-8"?>
  <Panel projectName="{pcb_project_name}" pcbBarcode="{pcb_sn}" pcbResult="{pcb_final_result}" testTime="{pcb_test_time}" repairUser="{pcb_repair_user}">
    <Board barcode="{board_sn}" id="{board_no}" result="{board_final_result}">{CompData}
    </Board>
    <Data compNumber="{board_comp_number}" compUserNgNumber="{board_comp_user_ng_number}" ver=""/>
  </Panel>
"""

v2_comp_template = """
      <Comp designator="{comp_designator}" part="{comp_part}" package="{comp_package}" type="{comp_type}" robotCode="{comp_robot_code}" robotResult="{comp_robot_result}" userCode="{comp_user_code}" userResult="{comp_user_result}" compImage="{comp_image}"/>"""


def list_board_sn_by_pcb_sn(conn, table_name, pcb_sn):
    """
    根据大板条码获取小板条码
    :param conn:
    :param table_name:
    :param pcb_sn:
    :return:
    """
    sql_str = f"""SELECT ID, 大板条码, 拼板条码, 拼板序号, 工单号, 程序名, 是否镭雕, 坏板标记, 雕刻时间
FROM {table_name} WHERE 大板条码 = '{pcb_sn}' order by 拼板序号 desc;"""

    log.info(f"查询拼板条码:\n{sql_str}")

    with conn.cursor() as cur:
        cur.execute(sql_str)
        result = cur.fetchall()

    log.info(f"select result: {result}")

    if result:
        return result
    else:
        return []


def get_pcb_sn_by_board_sn(conn, table_name, board_sn) -> str:
    """
    根据小板条码获取大板条码
    :param conn:
    :param table_name:
    :param board_sn:
    :return:
    """
    sql_str = f"""SELECT ID, 大板条码, 拼板条码, 拼板序号, 工单号, 程序名, 是否镭雕, 坏板标记, 雕刻时间
FROM {table_name} WHERE 拼板条码 = '{board_sn}';"""

    log.info(f"查询大板条码:\n{sql_str}")

    with conn.cursor() as cur:
        cur.execute(sql_str)
        result = cur.fetchall()

    log.info(f"select result: {result}")

    if result:
        return result[0][1]
    else:
        return ""


class Engine(BaseEngine):
    version = {
        "title": "bolai release v1.0.0.4",
        "device": "203,303",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-03 15:48  获取条码，上传数据
date: 2023-11-04 09:19  如果用条码找不到拼板条码，则先去获取大板条码再重新获取拼板条码
date: 2024-01-10 11:03  xml文档可以按拼板/整板生成
""", }

    form = {
        "db_host": {
            "ui_name": "数据库Ip",
            "value": "127.0.0.1"
        },
        "db_port": {
            "ui_name": "数据库端口号",
            "value": "1433"
        },
        "db_name": {
            "ui_name": "数据库名称",
            "value": "new_test"
        },
        "db_table": {
            "ui_name": "条码表名称",
            "value": "PcbBoardBarcode"
        },
        "db_username": {
            "ui_name": "数据库用户名",
            "value": "sa"
        },
        "db_password": {
            "ui_name": "数据库密码",
            "value": "Xx123456."
        },
    }

    password_style = ["db_password"]

    path = {
        "save_path": {
            "ui_name": "文件保存路径",
            "value": ""
        }
    }

    combo = {
        "xml_fmt": {
            "ui_name": "xml格式",
            "item": ["整板", "拼板"],
            "value": "整板"
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        db_host = other_vo.get_value_by_cons_key("db_host")
        db_port = other_vo.get_value_by_cons_key("db_port")
        db_name = other_vo.get_value_by_cons_key("db_name")
        db_username = other_vo.get_value_by_cons_key("db_username")
        db_password = other_vo.get_value_by_cons_key("db_password")
        db_table = other_vo.get_value_by_cons_key("db_table")

        pcb_sn = other_vo.get_pcb_sn()

        # 1. 去数据库获取
        conn = xsql.get_mssql_cursor(db_host, db_username, db_password, db_name, db_port)

        ret = list_board_sn_by_pcb_sn(conn, db_table, pcb_sn)

        if not ret:
            # 2. 如果用大板条码获取不到，则用小板条码去获取
            pcb_sn = get_pcb_sn_by_board_sn(conn, db_table, pcb_sn)
            self.log.info(f"大板条码已经被修正成：{pcb_sn}")

            if not pcb_sn:
                return self.x_response('false', f"从数据库获取不到条码！")

            ret = list_board_sn_by_pcb_sn(conn, db_table, pcb_sn)

            if not ret:
                return self.x_response('false', f"从数据库获取不到条码！")

        ret_sn = [i[2] for i in ret]

        conn.close()

        return self.x_response("true", ",".join(ret_sn))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        xml_fmt = data_vo.get_value_by_cons_key("xml_fmt")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if not save_path:
            return self.x_response("false", f"请先选择文件保存路径！")

        board_data_str = ""
        pcb_board_user_ng_number = 0

        pcb_sn = pcb_entity.pcb_barcode
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            comp_data_str = ""
            comp_data_str_v2 = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_json = comp_entity.to_json_data()
                standard_board_data = comp_entity.to_standard_board_data_v1()

                # comp_data_list.append(comp_json)
                comp_data_str += comp_template.format(**comp_json)

                comp_data_str_v2 += v2_comp_template.format(**standard_board_data)

            board_json = board_entity.to_json_data()

            board_json["CompData"] = comp_data_str

            board_data_str += board_template.format(**board_json)

            v2_board_data = v2_board_template.format(**{
                "pcb_project_name": pcb_entity.project_name,
                "pcb_sn": pcb_entity.pcb_barcode,
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_repair_user": pcb_entity.repair_user,
                "board_sn": barcode,
                "board_no": board_no,
                "board_final_result": board_entity.get_final_result(),
                "board_comp_number": board_entity.comp_total_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "CompData": comp_data_str_v2
            })

            if xml_fmt == "拼板":
                board_result = board_entity.get_repair_result("PASS", "FAIL")
                filepath = f"{save_path}/{board_no}_{time_file}_{barcode}_{board_result}.xml"
                xutil.FileUtil.write_content_to_file(filepath, v2_board_data)

        pcb_json = pcb_entity.to_json_data()
        pcb_json["BoardData"] = board_data_str
        pcb_json["pcb_board_user_ng_number"] = pcb_board_user_ng_number

        pcb_content = pcb_template.format(**pcb_json)

        pcb_result = pcb_entity.get_repair_result("PASS", "FAIL")
        xml_filename = f"{save_path}/{time_file}_{pcb_sn}_{pcb_result}.xml"

        if xml_fmt == "整板":
            xutil.FileUtil.write_content_to_file(xml_filename, pcb_content)

        return self.x_response()
