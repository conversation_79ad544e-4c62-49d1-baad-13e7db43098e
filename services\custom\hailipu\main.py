# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/25 上午9:39
# Author     ：sch
# version    ：python 3.8
# Description： 海利普
"""
import uuid
from typing import Any

from common import xutil, xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

get_sn_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                  xmlns:pdc="http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI">
    <soapenv:Header/>
    <soapenv:Body>
        <pdc:GET_TRAYInput>
            <pdc:P_IN-AOI_GET_TRAY_REQ-CIN>
                <pdc:AOI_GET_TRAY_REQ>
                    <pdc:MESSAGEID>{message_id}</pdc:MESSAGEID>
                    <pdc:TIMESTAMP>{timestamp}</pdc:TIMESTAMP>
                    <pdc:INTERFACEID>{face_id}</pdc:INTERFACEID>
                    <pdc:REQ_DATA>
                        <pdc:SITE>{site}</pdc:SITE>
                        <pdc:AREA>{area}</pdc:AREA>
                        <pdc:WORKSTATION>{work_station}</pdc:WORKSTATION>
                        <pdc:MAIN_PCB_ID>{pcb_sn}</pdc:MAIN_PCB_ID>
                    </pdc:REQ_DATA>
                </pdc:AOI_GET_TRAY_REQ>
            </pdc:P_IN-AOI_GET_TRAY_REQ-CIN>
            <pdc:P_OUT-AOI_GET_TRAY_RESP-COUT/>
        </pdc:GET_TRAYInput>
    </soapenv:Body>
</soapenv:Envelope>"""

test_status_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                  xmlns:pdc="http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI">
    <soapenv:Header/>
    <soapenv:Body>
        <pdc:TEST_RESULTInput>
            <pdc:P_IN-AOI_TST_RES_REQ-CIN>
                <pdc:AOI_TST_RES_REQ>
                    <pdc:MESSAGEID>{message_id}</pdc:MESSAGEID>
                    <pdc:TIMESTAMP>{timestamp}</pdc:TIMESTAMP>
                    <pdc:INTERFACEID>{face_id}</pdc:INTERFACEID>
                    <pdc:REQ_DATA>
                        <pdc:SITE>{site}</pdc:SITE>
                        <pdc:AREA>{area}</pdc:AREA>
                        <pdc:WORKSTATION>{workstation}</pdc:WORKSTATION>
                        <pdc:INSP_DATE>{start_time}</pdc:INSP_DATE>
                        <pdc:TOOL_ID>{project_name}</pdc:TOOL_ID>
                        <pdc:PCB_LIST>{board_data_str}
                        </pdc:PCB_LIST>
                    </pdc:REQ_DATA>
                </pdc:AOI_TST_RES_REQ>
            </pdc:P_IN-AOI_TST_RES_REQ-CIN>
            <pdc:P_OUT-AOI_TST_RES_RESP-COUT/>
        </pdc:TEST_RESULTInput>
    </soapenv:Body>
</soapenv:Envelope>"""

repair_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                  xmlns:pdc="http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI">
    <soapenv:Header/>
    <soapenv:Body>
        <pdc:INSPECTION_RESULTInput>
            <pdc:P_IN-AOI_INS_RES_REQ-CIN>
                <pdc:AOI_INS_RES_REQ>
                    <pdc:MESSAGEID>{message_id}</pdc:MESSAGEID>
                    <pdc:TIMESTAMP>{timestamp}</pdc:TIMESTAMP>
                    <pdc:INTERFACEID>{face_id}</pdc:INTERFACEID>
                    <pdc:REQ_DATA>
                        <pdc:SITE>{site}</pdc:SITE>
                        <pdc:AREA>{area}</pdc:AREA>
                        <pdc:PCB_LIST>
                            <!--Zero or more repetitions:-->{board_data_str}
                        </pdc:PCB_LIST>
                    </pdc:REQ_DATA>
                </pdc:AOI_INS_RES_REQ>
            </pdc:P_IN-AOI_INS_RES_REQ-CIN>
            <pdc:P_OUT-AOI_INS_RES_RESP-COUT/>
        </pdc:INSPECTION_RESULTInput>
    </soapenv:Body>
</soapenv:Envelope>"""

board_template = """
                            <pdc:AOI_TST_RES_PCB>
                                <pdc:PCB_ID>{barcode}</pdc:PCB_ID>
                                <pdc:STATUS>{status}</pdc:STATUS>
                            </pdc:AOI_TST_RES_PCB>"""

board_repair_template = """
                            <pdc:AOI_INS_RES_PCB>
                                <pdc:PCB_ID>{barcode}</pdc:PCB_ID>
                                <pdc:STATUS>{status}</pdc:STATUS>
                                <pdc:INSP_DATE>{test_time}</pdc:INSP_DATE>
                                <pdc:REP_DATE>{repair_time}</pdc:REP_DATE>
                                <pdc:WORKSTATION>{workstation}</pdc:WORKSTATION>
                                <pdc:TOOL_ID>{project_name}</pdc:TOOL_ID>
                                <pdc:USERNAME>{operator}</pdc:USERNAME>{comp_data_str}
                            </pdc:AOI_INS_RES_PCB>"""

comp_repair_template1 = """
                                <pdc:ERROR_LIST>
                                    <!--Zero or more repetitions:-->{comp_data_str}
                                </pdc:ERROR_LIST>"""

comp_repair_template = """
                                    <pdc:AOI_INS_RES_ERR>
                                        <pdc:ERROR_CODE>{repair_ng_code}</pdc:ERROR_CODE>
                                        <pdc:ERROR_DESCR>{comp_ng_str}</pdc:ERROR_DESCR>
                                        <pdc:COMPONENT>{barcode}</pdc:COMPONENT>
                                        <pdc:POSITION>{comp_tag}</pdc:POSITION>
                                        <pdc:TOOL_ID>{project_name}</pdc:TOOL_ID>
                                    </pdc:AOI_INS_RES_ERR>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "hailipu release v1.0.0.8",
        "device": "303",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-25 16:46  获取条码，上传数据
date: 2024-05-09 15:26  添加Authorization请求头
date: 2024-05-10 09:36  修改时间格式
date: 2024-11-15 14:57  未扫到条码时，不与mes交互！
date: 2024-12-10 10:48  [bugfix] 扫到条码了，但是没有上传数据
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/hailipu/test_api",
        },
        "site": {
            "ui_name": "生产站点",
            "value": "3001",
        },
        "area": {
            "ui_name": "生产区域",
            "value": "38HC",
        },
        "work_station": {
            "ui_name": "测试机名",
            "value": "VT-RNS-5311",
        },
        "operator": {
            "ui_name": "操作员ID",
            "value": "",
        },
        "board_side": {
            "ui_name": "面板",
            "value": "",
        },
        "rfid_sn": {
            "ui_name": "RFID序号",
            "value": "",
        },
    }

    other_form = {
        "authorization_header": {
            "ui_name": "Authorization Header",
            "value": "Basic cGRjX3dzOnBkY193cw=="
        }
    }

    password_style = ["authorization_header"]

    def __init__(self):
        self.custom_str_to_chinese()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        site = other_vo.get_value_by_cons_key("site")
        area = other_vo.get_value_by_cons_key("area")
        work_station = other_vo.get_value_by_cons_key("work_station")
        rfid_sn = other_vo.get_value_by_cons_key("rfid_sn")
        authorization_header = other_vo.get_value_by_cons_key("authorization_header")

        pcb_sn = other_vo.get_pcb_sn()

        if pcb_sn.upper() == "ERROR":
            # 客户条码未扫到条码时，会输出ERROR
            return self.x_response("false", f"获取条码失败，未扫到条码！")

        get_sn_param = get_sn_template.format(**{
            "message_id": str(uuid.uuid4()).upper(),
            "timestamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT8),
            "face_id": rfid_sn,
            "site": site,
            "area": area,
            "work_station": work_station,
            "pcb_sn": pcb_sn,
        })

        res_str = xrequest.RequestUtil.post_xml(api_url, get_sn_param, headers={
            "Authorization": authorization_header
        })

        root = xutil.XmlUtil.get_xml_root_by_str(res_str)
        aoi_get_tray_req = root[0][0][0][0]
        status_item = aoi_get_tray_req.find("{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PROCESSINGSTATUS")

        ret_code = status_item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNCODE').text
        ret_str = status_item.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNDESCRIPTION').text

        if ret_code != '0':
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret_str}")

        pcb_list = aoi_get_tray_req.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PCB_LIST')

        ret_sn_list = []
        for item in pcb_list:
            pcb_sn = item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PCB').text
            ret_sn_list.append(pcb_sn)

        return self.x_response("true", ",".join(ret_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        site = data_vo.get_value_by_cons_key("site")
        area = data_vo.get_value_by_cons_key("area")
        work_station = data_vo.get_value_by_cons_key("work_station")
        rfid_sn = data_vo.get_value_by_cons_key("rfid_sn")
        operator = data_vo.get_value_by_cons_key("operator")
        authorization_header = data_vo.get_value_by_cons_key("authorization_header")

        send_type = data_vo.get_inspect_type()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data_str = ""
        board_data_repair_str = ""

        is_upload_mes = True

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not barcode or barcode.upper() == "ERROR":
                self.log.warning(f"未扫到条码，该拼板数据不上传到mes！")

                is_upload_mes = False  # 不上传mes了
                continue

            board_data_str += board_template.format(**{
                "barcode": barcode,
                "status": board_entity.get_robot_result("PASS", "FAULT")
            })

            comp_repair_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_repair_data_str += comp_repair_template.format(**{
                        "repair_ng_code": comp_entity.repair_ng_code,
                        "comp_ng_str": comp_entity.repair_ng_str,
                        "barcode": barcode,
                        "comp_tag": comp_entity.designator,
                        "project_name": pcb_entity.project_name
                    })

            comp_data_str_tmp = comp_repair_template1.format(comp_data_str=comp_repair_data_str)
            board_data_repair_str += board_repair_template.format(**{
                "barcode": barcode,
                "status": board_entity.get_final_result("PASSED", "REPAIRED", "FAILED"),
                "test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT8),
                "repair_time": pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT8),
                "workstation": work_station,
                "project_name": pcb_entity.project_name,
                "operator": operator,
                "comp_data_str": comp_data_str_tmp
            })

        test_status_content = test_status_template.format(**{
            "message_id": str(uuid.uuid4()).upper(),
            "timestamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT8),
            "face_id": rfid_sn,
            "site": site,
            "area": area,
            "workstation": work_station,
            "start_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT8),
            "project_name": pcb_entity.project_name,
            "board_data_str": board_data_str
        })

        repair_content = repair_template.format(**{
            "message_id": str(uuid.uuid4()).upper(),
            "timestamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT8),
            "face_id": rfid_sn,
            "site": site,
            "area": area,
            "board_data_str": board_data_repair_str
        })

        if is_upload_mes:
            if send_type == 'inspector':
                res_str = xrequest.RequestUtil.post_xml(api_url, test_status_content, headers={
                    "Authorization": authorization_header
                })

                root = xutil.XmlUtil.get_xml_root_by_str(res_str)
                aoi_get_tray_req = root[0][0][0][0]

                status_item = aoi_get_tray_req.find("{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PROCESSINGSTATUS")
                ret_code = status_item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNCODE').text
                ret_str = status_item.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNDESCRIPTION').text

                if ret_code != '0':
                    return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret_str}")

            else:
                res_str = xrequest.RequestUtil.post_xml(api_url, repair_content, headers={
                    "Authorization": authorization_header
                })

                root = xutil.XmlUtil.get_xml_root_by_str(res_str)
                aoi_get_tray_req = root[0][0][0][0]

                status_item = aoi_get_tray_req.find("{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PROCESSINGSTATUS")
                ret_code = status_item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNCODE').text
                ret_str = status_item.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNDESCRIPTION').text

                if ret_code != '0':
                    return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret_str}")

        else:
            self.log.warning(f"未扫到条码，该次数据不上传到mes！")

        return self.x_response()
