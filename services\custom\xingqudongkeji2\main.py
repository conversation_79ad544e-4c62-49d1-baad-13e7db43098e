# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/10/26 上午10:24
# Author     ：sch
# version    ：python 3.8
# Description：无锡星驱动科技
"""
from typing import Any

from common import xcons
from vo.mes_vo import DataVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine

csv_template1 = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

csv_template2 = """程序名,{pcb_project_name}
测试时间,{pcb_test_time}
操作员,{pcb_repair_user}
大板条码,{pcb_sn}
整板结果,{pcb_final_result}
拼板条码,{board_sn}
拼板序号,{board_no}
拼板结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件复判NG数量,{board_comp_user_ng_number}

CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult,CompImage{CompData}
"""


class Engine(BaseEngine):
    version = {
        "title": "wuxixingqudong release v1.0.0.2",
        "device": "AIS501-C、AIS303、AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-10-26 10:37  init
date: 2023-10-26 17:27  上传图片，上传csv文件
date: 2023-11-13 15:46  兼容201上传整板图
""",
    }

    other_form = {
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "123456"
        },
        "ftp_path_csv": {
            "ui_name": "FTP CSV路径",
            "value": "/MES/SMT/CSV"
        },
        "ftp_path_img": {
            "ui_name": "FTP 图片路径",
            "value": "/MES/SMT/IMAGE"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path_csv = data_vo.get_value_by_cons_key("ftp_path_csv")
        ftp_path_img = data_vo.get_value_by_cons_key("ftp_path_img")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP 端口号必须为数字，error：{err}")

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        pcb_sn = pcb_entity.pcb_barcode
        start_datetime = pcb_entity.get_start_time()
        time_file = start_datetime.strftime(xcons.FMT_TIME_FILE)

        pcb_image = pcb_entity.pcb_image

        all_barcode = pcb_entity.all_barcode

        self.log.info(f"all barcode: {all_barcode}")

        the_first_barcode = time_file
        if all_barcode:
            the_first_barcode = all_barcode[0]

        self.log.info(f"第一个条码：{the_first_barcode}")

        ftp_path_img_full = f"{ftp_path_img}/{the_first_barcode}"

        ftp_client.cd_or_mkdir(ftp_path_img_full)

        if len(pcb_image) >= 2:
            # 501,双面合并发送
            t_src_img = pcb_image[0]
            b_src_img = pcb_image[1]
            self.log.info(f"上传双面整板图")

            # 1. 先拷贝t面数据
            ftp_client.upload_file(t_src_img, f"{the_first_barcode}_T.jpg")

            # 2. 再拷贝t面数据
            ftp_client.upload_file(b_src_img, f"{the_first_barcode}_B.jpg")

        elif len(pcb_image) == 1:
            t_src_img = pcb_image[0]

            self.log.info(f"上传整板图")

            board_side = pcb_entity.board_side
            ftp_client.upload_file(t_src_img, f"{the_first_barcode}_{board_side}.jpg")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = time_file

            ftp_path_img_full = f"{ftp_path_img}/{barcode}"
            ftp_client.cd_or_mkdir(ftp_path_img_full)

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_src_path = comp_entity.image_path
                comp_tag = comp_entity.designator

                if comp_src_path:
                    dst_filename = f"{barcode}_{comp_tag}.png"
                    ftp_client.upload_file(comp_src_path, dst_filename)

                    p1 = f"{ftp_path_img_full}/{dst_filename}"
                else:
                    p1 = ""

                comp_data_str += csv_template1.format(**{
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": p1,
                })

            csv_content = csv_template2.format(**{
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_test_time": start_datetime.strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "board_sn": barcode,
                "board_no": board_no,
                "board_comp_number": board_entity.comp_total_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_final_result": board_entity.get_final_result(),
                "CompData": comp_data_str
            })

            filename = f"{barcode}_{time_file}.csv"

            ftp_client.cd_or_mkdir(ftp_path_csv)
            ftp_client.upload_content(filename, csv_content)

        return self.x_response()
