# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/2/12 上午9:46
# Author     ：sch
# version    ：python 3.8
# Description：惠州福莱思    https://jira.cvte.com/browse/ATAOI_2019-37115
"""
import json
import os
import time
from typing import Any

from PyQt5.QtCore import QDate
from PyQt5.QtWidgets import QLabel, QDateEdit

from common import xcons, xrequest, xutil
from common.xcache import cache_upload_failed_data
from common.xutil import log, FileUtil
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, ButtonVo, ComboVo, OtherVo


def fls_re_upload_cache_data(
        bak_path,
        api_url,
        return_key="code",
        ret_ok_val="200",
        always_true: bool = False,
        expired_second=5 * 60 * 60 * 24,  # 5天
        action: int = 1,
        sheet_key="",
):
    """
    重新上传缓存数据: 福莱思定制
    :param bak_path:
    :param api_url:
    :param return_key:
    :param ret_ok_val:
    :param always_true: 如果不需要判断接口返回状态码，则把该参数设置为true
    :param expired_second: 过期时间，单位：秒
    :param action: 1: 重传数据  2: 强制入库
    :param sheet_key: 检验单ID
    :return:
    """
    time_now = int(time.time())

    file_data = [f for f in os.listdir(bak_path) if "offline_" in f]
    total_count = len(file_data)

    log.info(f"将要上传的数据量：{total_count}")

    error_count = 0
    ok_count = 0

    old_data_path = f"{bak_path}/old_data"
    FileUtil.ensure_dir_exist(old_data_path)

    for file in file_data:

        try:
            item_list = file.split("_")
            cache_time = int(item_list[2].replace(".json", ""))

            bak_file = f"{bak_path}/{file}"

            if time_now > cache_time + expired_second:
                # 该数据已过期，最后一次上传，如果还上传不成功，将移动到old_data目录内
                expired_data = True
            else:
                # 数据未过期
                expired_data = False

            try:
                cache_param = FileUtil.load_json_file(bak_file)
                cache_param["action"] = action

                if sheet_key:
                    cache_param["sheet_key"] = sheet_key

                ret = xrequest.RequestUtil.post_json(api_url, cache_param)

                if str(ret.get(return_key)) == ret_ok_val or always_true:
                    log.info(f"数据上传成功，缓存数据将会删除！")
                    os.remove(bak_file)

                    ok_count += 1
                else:
                    error_count += 1

                    if expired_data:
                        log.warning(f"数据上传失败，数据已过期，数据将移动到old_data目录！")
                        dst_filepath = f"{old_data_path}/{file}"
                        FileUtil.move_file(bak_file, dst_filepath)

                    break

            except Exception as err:
                log.warning(f"数据重传失败，err:{err} --> {file}")
                error_count += 1

                if expired_data:
                    log.warning(f"数据上传失败，数据已过期，数据将移动到old_data目录！")
                    dst_filepath = f"{old_data_path}/{file}"
                    FileUtil.move_file(bak_file, dst_filepath)

                break

        except Exception as err:
            log.warning(f"数据重传失败，err:{err} --> {file}")
            break

    return total_count, ok_count, total_count - ok_count


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoufulaisi release v1.0.0.5",
        "device": "AIS401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-02-18 09:07  上传不良图片，上传数据，列表数据获取
date: 2025-03-24 18:49  根据现场反馈修改
date: 2025-03-26 15:51  bugfix:校验单sheet_key是int类型的
date: 2025-04-01 09:26  条码为空时，生成的条码规则修改
date: 2025-04-02 10:50  bugfix: 条码规则少了#
""",
    }

    form = {
        "flow_no": {
            "ui_name": "工单",
            "value": ""
        },
        "section_id": {
            "ui_name": "工段",
            "value": ""
        },
        "shift_name": {
            "ui_name": "班制",
            "value": ""
        },
        "product_no": {
            "ui_name": "产品编号",
            "value": ""
        },
        "product_name": {
            "ui_name": "产品名称",
            "value": ""
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        },
        "operator": {
            "ui_name": "检验员",
            "value": ""
        },
    }

    other_form = {
        "access_token": {
            "ui_name": "认证token",
            "value": ""
        },
        "api_url": {
            "ui_name": "接口地址",
            "value": "http://127.0.0.1:8081"
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    button = {
        "create_sheet_key": {
            "ui_name": "创建校验单ID"
        },
        "delete_sheet_key": {
            "ui_name": "清除检验单ID"
        },
        "re_upload_data": {
            "ui_name": "重传数据"
        },
        "force_upload_data": {
            "ui_name": "强制入库"
        },
        "open_cache_dir": {
            "ui_name": "缓存数据目录"
        },
    }

    path = {
        "bak_path": {
            "ui_name": "上传失败数据备份路径",
            "value": "",
        }
    }

    combo = {
        "station_list": {
            "ui_name": "产线",
            "item": [],
            "value": "",
        },
        "test_type_list": {
            "ui_name": "测试类型",
            "item": [],
            "value": "",
        },
        "lot_list": {
            "ui_name": "校验批次",
            "item": [],
            "value": "",
        },
        "operation_list": {
            "ui_name": "检验工序",
            "item": [],
            "value": "",
        },
        "verity_key_list": {
            "ui_name": "校验项目",
            "item": [],
            "value": "",
        },
    }

    def __init__(self):
        self.dateEdit: QDateEdit = None  # noqa
        self.label: QLabel = None  # noqa

    def init_main_window(self, main_window, other_vo: OtherVo):
        config_date = xutil.CacheUtil.get("check_date", "2025-02-15")

        self.dateEdit = QDateEdit()
        self.dateEdit.setObjectName("dateEdit")
        self.dateEdit.setDisplayFormat("yyyy-MM-dd")

        default_date = QDate.fromString(config_date, "yyyy-MM-dd")
        self.dateEdit.setDate(default_date)

        self.label = QLabel(main_window)
        self.label.setText("校验日期")
        main_window.setting_param_layout.insertRow(3, self.label, self.dateEdit)

        lot_list_select = other_vo.get_value_by_cons_key("lot_list")

        lot_map = xutil.CacheUtil.get("lot_map", {})
        lot_item = lot_map.get(lot_list_select, {})

        def set_disable_form(ui_key: str):
            form_item = getattr(main_window, f"form_{ui_key}")
            form_item.setDisabled(True)

            if lot_item:
                form_item.setText(lot_item.get(ui_key, ""))

        set_disable_form("flow_no")
        set_disable_form("section_id")
        set_disable_form("shift_name")
        set_disable_form("product_no")
        set_disable_form("product_name")

    def save_btn_on_window(self, main_window):
        current_date = self.dateEdit.text()
        xutil.CacheUtil.set("check_date", current_date)

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        status_code_v3 = other_vo.get_status_code_v3()

        if status_code_v3 == "1002":
            sheet_key = xutil.CacheUtil.get("sheet_key")
            if not sheet_key:
                return self.x_response("false", f"未获取到检验单ID，请先创建检验单ID！")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        sheet_key = xutil.CacheUtil.get("sheet_key")
        if not sheet_key:
            return self.x_response("false", f"未获取到检验单ID，请先创建检验单ID！")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, main_window: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key("device_name")
        api_url = data_vo.get_value_by_cons_key("api_url", not_null=True)
        bak_path = data_vo.get_value_by_cons_key("bak_path", not_null=True)
        access_token = data_vo.get_value_by_cons_key("access_token")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        lot_list_select = data_vo.get_value_by_cons_key("lot_list")

        api_url_data = f"{api_url}/mes/api/auto/pcb/aoi/uploadTestResult"

        sheet_key = xutil.CacheUtil.get("sheet_key")
        if not sheet_key:
            return self.x_response("false", f"未获取到检验单ID，请先创建检验单ID！")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        if not pcb_sn:
            pcb_sn = f"#FS-{str(sheet_key)[-3:].zfill(3)}-{time_file}"

        file_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        comp_ix = 0

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        project_name = pcb_entity.project_name
        file_date = file_time[:8]

        full_path = f"{ftp_path}/{project_name}/{file_date}/{pcb_sn}"
        ftp_client.cd_or_mkdir(full_path)

        lot_map = xutil.CacheUtil.get("lot_map", {})
        lot_item = lot_map.get(lot_list_select, {})
        flow_no = lot_item.get("flow_no", "")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    full_filepath = ""
                    if comp_entity.is_repair_ng():
                        comp_src_img = comp_entity.image_path
                        comp_tag = comp_entity.designator

                        if comp_src_img:
                            comp_ix += 1
                            comp_dst_img = f"{comp_tag}_{file_time}_{comp_ix}.png"
                            ftp_client.upload_file(comp_src_img, comp_dst_img)
                            full_filepath = f"{full_path}/{comp_dst_img}"

                    comp_data_list.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": full_filepath,
                    })

            request_id = xutil.OtherUtil.get_uuid4_str()

            data_str = f"MainSN:{pcb_sn} NO:{board_no} 条码:{barcode} 校验单ID:{sheet_key} 工单:{flow_no}"
            main_window.log_info(data_str)

            board_data_fmt = {
                "request_id": request_id,
                "sheet_key": sheet_key,
                "action": 1,
                "token": access_token,
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "comp_data": comp_data_list
            }

            try:
                ret = xrequest.RequestUtil.post_json(api_url_data, board_data_fmt)
                ret_code = ret.get("code")
                if ret_code != "200":
                    if ret_code == "304":
                        self.log.warning(f"重复请求（直接忽略，不用提示用户）")
                        return self.x_response()

                    cache_upload_failed_data(bak_path, board_data_fmt)

                    self.log.warning(f"逐条上传，遇到失败则停止上传！")
                    return self.x_response(f"false", f"mes接口响应异常，error: {ret.get('msg')}")
            except Exception as err:
                self.log.warning(f"mes接口异常，error: {err}")
                cache_upload_failed_data(bak_path, board_data_fmt)

                self.log.warning(f"逐条上传，遇到失败则停止上传！")
                return self.x_response(f"false", f"mes接口网络异常，error: {err}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        bak_path = btn_vo.get_value_by_cons_key("bak_path")
        api_url = btn_vo.get_value_by_cons_key("api_url")
        api_url_data = f"{api_url}/mes/api/auto/pcb/aoi/uploadTestResult"

        btn_key = btn_vo.get_btn_key()

        if btn_key == "re_upload_data":
            file_data = [f for f in os.listdir(bak_path) if "offline_" in f]
            total_count = len(file_data)
            if total_count == 0:
                return self.x_response("false", f"无数据可重传！")

            # 重传, 用界面最新的检验单ID
            sheet_key = xutil.CacheUtil.get("sheet_key")
            if not sheet_key:
                return self.x_response("false", f"未获取到检验单ID，请先创建检验单ID！")

            total_count, ok_count, error_count = fls_re_upload_cache_data(
                bak_path,
                api_url_data,
                action=1,
                return_key="code",
                ret_ok_val="200",
                always_true=False,
                expired_second=5 * 60 * 60 * 24,
                sheet_key=sheet_key,
            )
        elif btn_key == "force_upload_data":
            total_count, ok_count, error_count = fls_re_upload_cache_data(
                bak_path,
                api_url_data,
                action=2,
                return_key="code",
                ret_ok_val="200",
                always_true=False,
                expired_second=5 * 60 * 60 * 24,
                sheet_key="",
            )
        elif btn_key == "open_cache_dir":
            os.system(f'xdg-open {bak_path}')
            return self.x_response()

        elif btn_key == "create_sheet_key":
            lot_list_select = btn_vo.get_value_by_cons_key("lot_list")
            operation_list_select = btn_vo.get_value_by_cons_key("operation_list")
            verity_key_list_select = btn_vo.get_value_by_cons_key("verity_key_list")
            test_type_list_select = btn_vo.get_value_by_cons_key("test_type_list")
            operator = btn_vo.get_value_by_cons_key("operator")

            check_date = self.dateEdit.text()
            cache_data = xutil.CacheUtil.get_cache_data()

            lot_map = cache_data.get("lot_map", {})
            operation_map = cache_data.get("operation_map", {})
            verify_map = cache_data.get("verify_map", {})
            test_type_map = cache_data.get("test_type_map", {})

            if not lot_map:
                return self.x_response("false", f"请先选择校验批次！")

            if not test_type_map:
                return self.x_response("false", f"请先选择测试类型！")

            if not operation_map:
                return self.x_response("false", f"请先选择检验工序！")

            if not verify_map:
                return self.x_response("false", f"请先选择校验项目！")

            lot_key = lot_map.get(lot_list_select, {}).get("lot_key")
            detail_key = operation_map.get(operation_list_select, {}).get("detail_key")
            verify_key = verify_map.get(verity_key_list_select)
            test_type = test_type_map.get(test_type_list_select)

            create_sheet_url = f"{api_url}/mes/api/auto/pcb/aoi/createInspectionSheet"

            ret = xrequest.RequestUtil.post_json(create_sheet_url, {
                "verifyDate": check_date,
                "lotKey": lot_key,
                "detailKey": detail_key,
                "verifyKey": verify_key,
                "testType": test_type,
                "operator": operator
            })

            if str(ret["code"]) != "200":
                return self.x_response("false", f"mes接口响应异常，error: {ret.get('msg')}")

            sheet_key = ret.get("data", {}).get("sheetKey")
            xutil.CacheUtil.set("sheet_key", sheet_key)
            return self.x_response()

        elif btn_key == "delete_sheet_key":
            xutil.CacheUtil.set("sheet_key", "")
            return self.x_response()

        else:
            return self.x_response()

        if error_count != 0:
            return self.x_response("false", f"上传总数：{total_count} 成功数量：{ok_count} 失败数量：{error_count}")

        if total_count == 0:
            return self.x_response("false", f"没有需要上传的缓存数据！")

        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()
        api_url = combo_vo.get_value_by_cons_key("api_url")

        if combo_key == "station_list":
            # 1.获取产线选项
            station_url = f"{api_url}/mes/api/auto/pcb/aoi/getStationOpts"

            ret = xrequest.RequestUtil.get(station_url, {})
            if ret.get("code") != "200":
                return self.x_response("false", f"mes接口响应异常，error: {ret.get('msg')}")

            station_map = {
                item.get("stationName"): item.get("stationKey") for item in ret.get("data", [])
            }
            ret_list = list(station_map.keys())
            xutil.CacheUtil.set("station_map", station_map)

        elif combo_key == "test_type_list":
            # 2.获取测试类型选项
            test_type_url = f"{api_url}/mes/api/auto/pcb/aoi/getTestTypeOpts"
            ret = xrequest.RequestUtil.get(test_type_url, {})
            if ret.get("code") != "200":
                return self.x_response("false", f"mes接口响应异常，error: {ret.get('msg')}")

            test_type_map = {
                item.get("description"): item.get("testType") for item in ret.get("data", [])
            }
            ret_list = list(test_type_map.keys())
            xutil.CacheUtil.set("test_type_map", test_type_map)

        elif combo_key == "lot_list":
            # 3.获取检验批次    ----   产品列表
            station_list_select = combo_vo.get_value_by_cons_key("station_list")
            station_map = xutil.CacheUtil.get("station_map", {})
            if not station_map:
                return self.x_response("false", "请先选择产线！")

            station_key = station_map.get(station_list_select)

            check_date = self.dateEdit.text()

            lot_url = f"{api_url}/mes/api/auto/pcb/aoi/getInspectionLots"
            ret = xrequest.RequestUtil.get(lot_url, {"stationKey": station_key, "verifyDate": check_date})
            if ret.get("code") != "200":
                return self.x_response("false", f"mes接口响应异常，error: {ret.get('msg')}")

            lot_map = {
                item.get("lotNo"): {
                    "lot_key": item.get("lotKey"),
                    "product_key": item.get("productKey"),

                    "flow_no": item.get("flowNo"),
                    "section_id": item.get("processSectionName"),
                    "shift_name": item.get("shiftName"),
                    "product_no": item.get("productNo"),
                    "product_name": item.get("productName"),
                } for item in ret.get("data", [])
            }
            ret_list = list(lot_map.keys())
            xutil.CacheUtil.set("lot_map", lot_map)

        elif combo_key == "operation_list":
            # 4. 获取检验批次明细
            lot_list_select = combo_vo.get_value_by_cons_key("lot_list")
            lot_map = xutil.CacheUtil.get("lot_map", {})
            if not lot_map:
                return self.x_response("false", "请先选择校验批次！")

            lot_key = lot_map.get(lot_list_select, {}).get("lot_key")
            operation_url = f"{api_url}/mes/api/auto/pcb/aoi/getInspectionLotItems"
            ret = xrequest.RequestUtil.get(operation_url, {"lotKey": lot_key})
            if ret.get("code") != "200":
                return self.x_response("false", f"mes接口响应异常，error: {ret.get('msg')}")

            operation_map = {
                item.get("operationName"):
                    {
                        "operation_key": item.get("operationKey"),
                        "detail_key": item.get("detailKey"),
                    } for item in ret.get("data", [])
            }
            ret_list = list(operation_map.keys())
            xutil.CacheUtil.set("operation_map", operation_map)

        elif combo_key == "verity_key_list":
            # 5. 获取检验项目
            operation_list_select = combo_vo.get_value_by_cons_key("operation_list")
            lot_list_select = combo_vo.get_value_by_cons_key("lot_list")

            operation_map = xutil.CacheUtil.get("operation_map", {})
            if not operation_map:
                return self.x_response("false", "请先选择检验工序！")

            operation_key = operation_map.get(operation_list_select, {}).get("operation_key")

            lot_map = xutil.CacheUtil.get("lot_map", {})

            if not lot_map:
                return self.x_response("false", "请先选择校验批次！")

            product_key = lot_map.get(lot_list_select, {}).get("product_key")

            verity_key_url = f"{api_url}/mes/api/auto/pcb/aoi/getVerifyItems"

            ret = xrequest.RequestUtil.get(
                verity_key_url,
                {"productKey": product_key, "operationKey": operation_key}
            )

            if ret.get("code") != "200":
                return self.x_response("false", f"mes接口响应异常，error: {ret.get('msg')}")

            verify_map = {
                item.get("verifyName"): item.get("verifyKey") for item in ret.get("data", [])
            }

            ret_list = list(verify_map.keys())
            xutil.CacheUtil.set("verify_map", verify_map)

        else:
            return self.x_response("false", "其他错误，未定义的下拉框")

        ret_data = {
            "new_items": ret_list
        }

        return self.x_response("true", json.dumps(ret_data))

    def combo_index_changed(self, combo_vo: ComboVo, main_window: Any):
        if combo_vo.get_combo_key() == "lot_list":
            lot_map = xutil.CacheUtil.get("lot_map", {})
            lot_item = lot_map.get(combo_vo.get_combo_value(), {})

            # 设置工单、工段、班制、产品编号、产品名称
            def set_label_text(ui_key: str, value: str):
                form_item = getattr(main_window, f"form_{ui_key}")
                form_item.setText(value)

            if lot_item:
                set_label_text("flow_no", lot_item.get("flow_no"))
                set_label_text("section_id", lot_item.get("section_id"))
                set_label_text("shift_name", lot_item.get("shift_name"))
                set_label_text("product_no", lot_item.get("product_no"))
                set_label_text("product_name", lot_item.get("product_name"))
