# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/21 下午3:27
# Author     ：sch
# version    ：python 3.8
# Description：深圳鑫达辉
"""
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "shenzhenxindahui release v1.0.0.2",
        "device": "43x,63x",
        "feature": ["从Mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-21 15:29  init  
date: 2023-10-14 11:01  bugfix
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081"
        },
        "org_id": {
            "ui_name": "公司名",
            "value": ""
        },
        "company_no": {
            "ui_name": "公司编号",
            "value": ""
        },
        "machine_no": {
            "ui_name": "机器编号",
            "value": ""
        },
        "line_no": {
            "ui_name": "线别编号",
            "value": ""
        },
        "station_no": {
            "ui_name": "站点编号",
            "value": "AOI"
        },
        "prod_no": {
            "ui_name": "工单",
            "value": ""
        },
    }

    combo = {
        "device_type": {
            "ui_name": " 设备类型",
            "value": "AOI",
            "item": ["AOI", "SPI"]
        },
        "board_side": {
            "ui_name": "板面",
            "value": "T",
            "item": ["T", "B", "T+B"]
        },
        "retest": {
            "ui_name": "允许重复测试",
            "value": "允许",
            "item": ["允许", "不允许"]
        },
        "ng_code_map": {
            "ui_name": "不良代码映射",
            "value": "AIS40X",
            "item": ["AIS40X", "AIS30X", "AIS20X", "AIS63X"]
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        prod_no = other_vo.get_value_by_cons_key("prod_no")
        org_id = other_vo.get_value_by_cons_key("org_id")

        get_sn_url = f"{api_url}/mrs/getRelationPcbSeq"

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "pcbSeq": pcb_sn,
            "prodNo": prod_no,
            "companyNo": org_id,
            "type": "1"
        }

        ret = xrequest.RequestUtil.post_json(get_sn_url, param)

        data = ret.get("data")

        # 兼容模式
        data_str = str(data)
        if '"msgId":1' in data_str or '没有子母码' in data_str:
            return self.x_response("false", f"mes接口异常，获取条码失败，error: {data}")

        if str(ret.get("msgId")) != "0":
            return self.x_response("false", f"mes接口异常, 获取条码失败, error: {ret.get('msgStr')}")

        ret_sn = [item.get("pcbSeq") for item in data]

        sn_str = ",".join(ret_sn)
        return self.x_response("true", sn_str)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        prod_no = other_vo.get_value_by_cons_key("prod_no")
        station_no = other_vo.get_value_by_cons_key("station_no")
        retest = other_vo.get_value_by_cons_key("retest")

        sn_list = other_vo.list_sn()
        check_url = f"{api_url}/mrs/checkRoute"

        for sn in sn_list:
            param = {
                "pcbSeq": sn,
                "prodNo": prod_no,
                "stationNo": station_no,
                "retest": 0 if retest != "允许" else 1
            }

            ret = xrequest.RequestUtil.get(check_url, params=param)
            if str(ret.get("msgId")) != "0":
                return self.x_response("false", f"接口响应异常, 条码校验失败, error: {ret.get('msgStr')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        device_type = data_vo.get_value_by_cons_key("device_type")
        line_no = data_vo.get_value_by_cons_key("line_no")
        station_no = data_vo.get_value_by_cons_key("station_no")
        prod_no = data_vo.get_value_by_cons_key("prod_no")
        board_side = data_vo.get_value_by_cons_key("board_side")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        company_no = data_vo.get_value_by_cons_key("company_no")
        ng_code_map = data_vo.get_value_by_cons_key("ng_code_map")

        if device_type == "AOI":
            func_name = "createAOIData"
        else:
            func_name = "createSPIData"

        api_url = f"{api_url}/mrs/{func_name}"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_datetime = pcb_entity.get_start_time()
        start_time = start_datetime.strftime(xcons.FMT_TIME_DEFAULT)

        time_file = start_datetime.strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        sn_list = []
        if pcb_sn and pcb_sn not in sn_list:
            sn_list.append(pcb_sn)

        for board_entity in pcb_entity.yield_board_entity():
            # self.log.info(board_entity)

            barcode = board_entity.barcode
            if barcode and barcode not in sn_list:
                sn_list.append(barcode)

        sn_count = len(sn_list)
        self.log.info(f"条码数量：{sn_count}")

        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        if ng_code_map == "AIS63X":
            comp_pad_data, _ = pcb_entity.get_pad_test_data()
        else:
            comp_pad_data = {}

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            remark = []

            now = xutil.DateUtil.get_datetime_now("%Y-%m-%d %H:%M:%S")

            if sn_count == 0:
                board_sn = f"{time_file}-{board_no}"
            else:
                board_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_des = comp_entity.designator
                    user_ng_code = comp_entity.repair_ng_code
                    robot_ng_code = comp_entity.robot_ng_code

                    if ng_code_map == "AIS63X" and robot_ng_code == "34":
                        # 获取细分项的不良代码
                        comp_id = comp_entity.comp_id
                        comp_uuid = comp_id.replace("{", "").replace("}", "")
                        pad_list = comp_pad_data.get(comp_uuid, [])

                        if pad_list:
                            pad1 = pad_list[0]
                            if pad1:
                                pad_result = pad1.get("result")
                                user_ng_code = pad_result

                                self.log.info(f"user ng code had changed! {comp_des} {pad_result}")
                            # custom_ng_str = xcons.AIS630_ERROR_MAP.get(pad_result, {}).get("standard", robot_ng_str)

                    remark.append(f"{comp_des}_{ng_code_map}{user_ng_code}")

            param = {
                "pcbSeq": board_sn,
                "createdDateTime": now,
                "prodNo": prod_no,
                "result": "PASS" if board_entity.repair_result else "FAIL",
                "oriResult": "PASS" if board_entity.robot_result else "FAIL",
                "machineNo": machine_no,
                "threadNo": line_no,
                "beginTime": start_time,
                "endTime": end_time,
                "board": board_side,
                "remark": ",".join(remark),
                "companyNo": company_no,
                "mainPcbSeq": pcb_sn,
                "partSn": board_no,
                "trackName": str(pcb_entity.track_index),
                "badPointQty": str(board_entity.comp_repair_ng_number),
                "pointQty": str(board_entity.comp_total_number),
                "siteNo": station_no
            }

            # 2. 调用接口上传数据
            res = xrequest.RequestUtil.post_json(api_url, param)
            if str(res.get("result")) != "0":
                return self.x_response("false", f"接口响应异常, 上传数据失败, error: {res.get('message')}")

        return self.x_response()
