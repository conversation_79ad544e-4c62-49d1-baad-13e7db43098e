# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/23 上午11:20
# Author     ：sch
# version    ：python 3.8
# Description：海红2，   珠海海红     ps：旧版本不要了，客户换mes系统，需要重新对接
"""
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine


def parse_res(ret_text):
    """
    解析响应参数
    """
    root = xutil.XmlUtil.get_xml_root_by_str(ret_text)
    return_data = xutil.XmlUtil.get_xml_root_by_str(root.text)
    status = return_data.find("RtnString").text
    msg = return_data.find("ErrMsg").text
    return status, msg


class Engine(ErrorMapEngine):
    version = {
        "title": "haihong2 release v1.0.0.4",
        "device": "203,303,501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-23 15:53  条码校验，上传数据
date: 2024-10-24 16:16  上传整板图到FTP服务器
date: 2024-11-13 14:16  [update] 兼容接口返回参数
date: 2025-02-24 17:40  jira:34129,误报不上传mes
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/WebService/BasalWebService.asmx/WS_BcValid"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/WebService/BasalWebService.asmx/WS_UpdUnitRecord"
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21",
            "is_int": True
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch",
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    form = {
        "station_name": {
            "ui_name": "工序名称",
            "value": ""
        },
        "rsc_name": {
            "ui_name": "资源名称",
            "value": ""
        },
        "username": {
            "ui_name": "用户账户",
            "value": ""
        },
    }

    combo = {
        "is_multi_plate": {
            "ui_name": "是否多联板",
            "item": ["No", "Yes"],
            "value": "No"
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station_name = other_vo.get_value_by_cons_key("station_name")
        rsc_name = other_vo.get_value_by_cons_key("rsc_name")
        is_multi_plate = other_vo.get_value_by_cons_key("is_multi_plate")
        username = other_vo.get_value_by_cons_key("username")

        sn_list = other_vo.list_sn()

        err_msg_list = []
        for ix, sn in enumerate(sn_list):
            param = {
                "Barcode": sn,
                "StationName": station_name,
                "RscName": rsc_name,
                "UserName": username,
                "IsMultiPlate": is_multi_plate == "Yes"
            }

            ret_str = xrequest.RequestUtil.post_form(api_url_check, param, to_json=False)

            root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str)
            root2 = xutil.XmlUtil.get_xml_root_by_str(root1.text)

            ret_code = root2[0].text
            ret_msg = root2[1].text

            if ret_code != "1":
                err_msg_list.append(f"No:{ix} SN:{sn} Error:{ret_msg}")

        if err_msg_list:
            err_str = f"\n".join(err_msg_list)
            return self.x_response("false", f"Mes接口响应异常，{err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        station_name = data_vo.get_value_by_cons_key("station_name")
        rsc_name = data_vo.get_value_by_cons_key("rsc_name")
        is_multi_plate = data_vo.get_value_by_cons_key("is_multi_plate")
        username = data_vo.get_value_by_cons_key("username")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        comp_total_number = 0
        comp_ng_number = 0

        err_msg_list = []

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no
            barcode = board_entity.barcode

            # 误报不上传mes
            if not board_entity.robot_result and board_entity.repair_result:
                self.log.info(f"误报板卡不上传mes, 板卡:{board_entity.barcode} 检测NG但复判OK")
                continue

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_total_number += board_entity.comp_total_number
            comp_ng_number += board_entity.comp_repair_ng_number

            defect_code = ""

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    repair_ng_code = comp_entity.repair_ng_code

                    if not defect_code:
                        defect_code = repair_ng_code

            param = {
                "Barcode": barcode,
                "Sn": pcb_entity.pcb_barcode,
                "StationName": station_name,
                "RscName": rsc_name,
                "IsMultiPlate": is_multi_plate == "Yes",
                "JudgeRsl": board_entity.repair_result,
                "DefectCode": defect_code,
                "ExtParameter": "",
                "UserName": username,
            }

            ret_str = xrequest.RequestUtil.post_form(api_url_data, param, to_json=False)

            root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str)
            root2 = xutil.XmlUtil.get_xml_root_by_str(root1.text)

            ret_code = root2[0].text
            ret_msg = root2[1].text

            if str(ret_code) != "1":
                err_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret_msg}")

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = time_file[:8]

        ftp_full_path = f"{ftp_path}/{date_file}"
        ftp_client.cd_or_mkdir(ftp_full_path)

        all_pcb_img_list = pcb_entity.list_all_pcb_image()
        project_name = pcb_entity.project_name

        for item in all_pcb_img_list:

            if "B_" in item:
                board_side = "B"
            else:
                board_side = "T"

            dst_img_filename = f"{project_name}_{pcb_sn}_{time_file}_{board_side}.jpg"
            ftp_client.upload_file(item, dst_img_filename)

        if err_msg_list:
            err_str = f"\n".join(err_msg_list)
            return self.x_response("false", f"Mes接口响应异常，{err_str}")

        return self.x_response()
