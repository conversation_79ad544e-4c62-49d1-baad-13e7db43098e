# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/16 下午2:39
# Author     ：sch
# version    ：python 3.8
# Description：深圳日月元
"""
from datetime import datetime
from typing import Any

import pymssql

from common import xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import BaseEngine

"""
数据库建表语句：
CREATE TABLE aoi_log
(
    id INT IDENTITY(1,1) PRIMARY KEY,
    pcb_sn VARCHAR(128),
    test_result VARCHAR(16),
    repair_result VARCHAR(16),
    create_time datetime default current_timestamp,
    update_time datetime default null,
);
"""

insert_template = """INSERT INTO {table_name} (
[PCBID],
[ProgName],
[MachineID],
[PcbBarcode],
[Ngsum],
[WpNgsum],
[OkNgSum],
[UserID],
[ClassID],
[LineID],
[ImgPath],
[CompQty],
[ShieldQty],
[SpcName],
[Inter],
[PcbSide],
[MarkQuoteLineID],
[AOIName],
[FDateTime],
[WorkingPaper],
[RepairFlag],
[AoiTestTime],
[UploadFlag],
[BigPCBCode],
[Station],
[FirstNgSum],
[FirstWpNgSum],
[FirstOkNgSum],
[SecondOkNgSum],
[CheckTime],
[FirstSpcName],
[FirstCheckDate] 
)
VALUES
(
    '{pcb_id}',
    '{program_name}',
    '{machine_id}',
    '{pcb_barcode}',
    {robot_ng_sum},
    {wp_ng_sum},
    {ok_ng_sum},
    '{user_id}',
    '',
    '',
    '',
    {comp_total_count},
    0,
    '{spc_name}',
    0,
    '{pcb_side}',
    0,
    '{aoi_name}',
    '{f_datetime}',
    '',
    {is_repair},
    {cycle_time},
    0,
    '',
    0,
    {robot_ng_sum},
    {wp_ng_sum},
    {ok_ng_sum},
    0,
    0,
    '',
'' 
);"""


class Engine(BaseEngine):
    version = {
        "title": "shengzhenriyueyuan release v1.0.0.8",
        "device": "40x",
        "feature": ["生成条码[从mes获取条码]", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-16 16:43  init
date: 2023-05-24 09:52  修改插入数据的表结构
date: 2023-05-24 15:11  上下半年数据表名自动切换
date: 2023-05-24 16:52  表名根据规则自动生成
date: 2023-05-25 16:35  按拼板上传
""", }

    form = {
        # "board_number_map": {
        #     "ui_name": "拼板数量映射",
        #     "value": "p1:1;p2:2;p3:4"
        # },
        "board_number": {
            "ui_name": "拼板数量",
            "value": "2"
        },
        "db_host": {
            "ui_name": "服务器IP",
            "value": "127.0.0.1"
        },
        "db_port": {
            "ui_name": "端口号",
            "value": "1433"
        },
        "database": {
            "ui_name": "数据库",
            "value": "ALDSPC"
        },
        # "db_table_1": {
        #     "ui_name": "上半年数据表",
        #     "value": "PCB20230M"
        # },
        # "db_table_2": {
        #     "ui_name": "下半年数据表",
        #     "value": "PCB20231M"
        # },
        "username": {
            "ui_name": "用户名",
            "value": "sa"
        },
        "password": {
            "ui_name": "密码",
            "value": "linshimima123"
        },
    }

    other_form = {
        "machine_id": {
            "ui_name": "MachineId",
            "value": "leichen",
        },
        "spc_name": {
            "ui_name": "SPCName",
            "value": "leichen"
        },
        "aoi_name": {
            "ui_name": "AOIName",
            "value": "leichen-aoi"
        },
        "user_id": {
            "ui_name": "UserId",
            "value": "leichen-test"
        },
    }

    button = {
        "test_connect": {
            "ui_name": "测试连接",
        }
    }

    def insert_test_row(self,
                        db_host,
                        db_port,
                        database,
                        username,
                        password,
                        param_map
                        ):
        """
        插入一条测试记录到数据库
        :return:
        """
        conn = pymssql.connect(server=db_host, user=username, password=password, database=database, port=db_port)
        self.log.info(f"conn: {conn}")
        cur = conn.cursor()

        # insert_sql = f"INSERT INTO {table_name} (PcbBarcode,PCBFirstRealResult) VALUES('{pcb_sn}', '{pcb_test_result}');"
        insert_sql = insert_template.format(**param_map)

        log.info(f"数据库插入语句：\n{insert_sql}")
        cur.execute(insert_sql)
        conn.commit()
        conn.close()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # 插入数据到数据库
        db_host = data_vo.get_value_by_cons_key("db_host")
        # db_table_1 = data_dao.get_value_by_cons_key("db_table_1")
        # db_table_2 = data_dao.get_value_by_cons_key("db_table_2")
        database = data_vo.get_value_by_cons_key("database")
        username = data_vo.get_value_by_cons_key("username")
        password = data_vo.get_value_by_cons_key("password")
        db_port = data_vo.get_value_by_cons_key("db_port")

        machine_id = data_vo.get_value_by_cons_key("machine_id")
        spc_name = data_vo.get_value_by_cons_key("spc_name")
        aoi_name = data_vo.get_value_by_cons_key("aoi_name")
        user_id = data_vo.get_value_by_cons_key("user_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        # comp_total_count = 0
        # comp_robot_ng_count = 0
        # comp_repair_ng_count = 0
        # comp_repass_count = 0

        now_month = pcb_entity.get_start_time().month
        now_year = pcb_entity.get_start_time().year

        if now_month in [1, 2, 3, 4, 5, 6]:
            db_table = f"PCB{now_year}0M"
        else:
            db_table = f"PCB{now_year}1M"

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # comp_total_count += board_entity.comp_total_number
            # comp_robot_ng_count += board_entity.comp_robot_ng_number
            # comp_repair_ng_count += board_entity.comp_repair_ng_number
            comp_repass_count = 0

            barcode = board_entity.barcode

            if not barcode:
                barcode = datetime.now().strftime(xcons.FMT_TIME_FILE)

            if not pcb_sn and board_entity.barcode:
                pcb_sn = board_entity.barcode

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.get_final_result() == 'REPASS':
                    comp_repass_count += 1

        # if not pcb_sn:
        #     pcb_sn = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        #
        # test_result = pcb_entity.get_robot_result("PASS", "FAIL")

            row_param = {
                "table_name": db_table,
                "pcb_id": datetime.now().strftime(xcons.FMT_TIME_DEFAULT2)[:-3],
                "program_name": pcb_entity.project_name,
                "machine_id": machine_id,
                "pcb_barcode": barcode,
                "robot_ng_sum": board_entity.comp_robot_ng_number,
                "wp_ng_sum": comp_repass_count,
                "ok_ng_sum": board_entity.comp_repair_ng_number,
                "user_id": user_id,
                "comp_total_count": board_entity.comp_total_number,
                "spc_name": spc_name,
                "pcb_side": pcb_entity.board_side,
                "aoi_name": aoi_name,
                "f_datetime": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT2)[:-3],
                "is_repair": board_entity.get_repair_result(0, 1),
                "cycle_time": pcb_entity.get_cycle_time(),
            }

            self.insert_test_row(db_host, db_port, database, username, password, row_param)

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        pcb_sn = other_vo.get_pcb_sn()

        main_part = pcb_sn[:-5]
        serial_number = pcb_sn[-5:]

        self.log.info(f"part1: {main_part}  part2:{serial_number}")

        board_number = other_vo.get_value_by_cons_key("board_number")

        # try:
        #     # cur_project_name = other_dao.get_project_name()
        #     board_number = other_dao.get_value_by_cons_key("board_number")

            # board_number_list = board_number_map.split(";")

            # board_map = {}
            # for i in board_number_list:
            #     project_name, board_count = i.split(":")
            #
            #     project_name = project_name.strip()
            #     board_count = board_count.strip()
            #
            #     board_map[project_name] = board_count
            #
            # self.log.info(f"拼板数量映射关系：{board_map}")
            # tmp = board_map.get(cur_project_name)
            # if tmp is None:
            #     return self.x_response("false", f"板式[{cur_project_name}]的拼板数量映射关系未填写！")

        #     board_number = int(tmp)
        #
        # except Exception as err:
        #     return self.x_response("false", f"板式与拼版数量映射解析出错，请检查拼板数量映射！err:{err}")

        try:
            board_number = int(board_number)
        except Exception as err:
            return self.x_response("false", f"拼板数必须为数字！error：{err}")

        try:
            serial_number = int(serial_number)
        except Exception as err:
            return self.x_response("false", f"生成条码失败，条码后5位必须为纯数字！error：{err}")

        ret_sn = []
        for ix in range(board_number):
            _part2 = str(serial_number + ix).rjust(5, "0")
            board_sn = f"{main_part}{_part2}"
            ret_sn.append(board_sn)

        return self.x_response("true", ",".join(ret_sn))

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        # self.log.info(btn_dao.all_config_json)

        if btn_vo.get_btn_key() == "test_connect":
            db_host = btn_vo.get_value_by_cons_key("db_host")
            database = btn_vo.get_value_by_cons_key("database")
            username = btn_vo.get_value_by_cons_key("username")
            password = btn_vo.get_value_by_cons_key("password")
            db_port = btn_vo.get_value_by_cons_key("db_port")

            self.log.info(f"{db_host=}, {username=}, {password=}, {database=}, {db_port=}")

            conn = pymssql.connect(server=db_host, user=username, password=password, database=database, port=db_port)

            self.log.info(f"conn: {conn}")
            conn.close()

            return self.x_response("true", "连接成功")

        return self.x_response()
