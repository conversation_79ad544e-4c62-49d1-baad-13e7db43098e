# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : api_mock.py
# Time       ：2024/10/18 下午2:08
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import socket
import struct

if __name__ == '__main__':
    sk = socket.socket()

    sk.bind(("0.0.0.0", 9099))
    sk.listen()

    PACKETSTART = b'PACKET_START'
    PACKETEND = b'PACKET_END'
    PACKETTYPE = b'02'

    # 包数据结构：  包头[12]+长度[8]+包类型[2]+数据[N]+包尾[10]

    print('waiting....')
    while True:
        conn, addr = sk.accept()
        print(f"recv from: {addr}")

        packet_start = conn.recv(12)
        if packet_start == b'':
            continue

        packet_len = conn.recv(8)
        print("pocket data len", packet_len)
        packet_type = conn.recv(2)
        data_len = struct.unpack("!q", packet_len)[0]
        print("data len", data_len)
        data = conn.recv(data_len)
        pack_end = conn.recv(10)

        data_str = data.decode("utf8")
        print(data_str)
        data_json = json.loads(data_str)

        data_type = data_json.get("type")

        print(f"{data_type=}")

        ret_str = "OK"
        if data_type == "1":
            ret_str = ""

        print("-------------开始向客户端发消息")
        ret_data = {"result": True, "string": ret_str}

        ret_data = json.dumps(ret_data, ensure_ascii=False).encode('utf8')
        ret_len = struct.pack("!q", len(ret_data))
        ret_full_param = PACKETSTART + ret_len + PACKETTYPE + ret_data + PACKETEND
        conn.send(ret_full_param)
        print("发送完毕")

        conn.close()
