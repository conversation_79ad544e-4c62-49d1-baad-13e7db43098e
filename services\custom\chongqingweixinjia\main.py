# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/7 上午9:05
# Author     ：sch
# version    ：python 3.8
# Description：重庆威鑫佳
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

txt_template = """{project_name}
{barcode}
1
2
{line_id}
0
{test_time}
{upload_time}
{test_result}
{board_face}
{comp_number}
{comp_ng_number}{comp_data_str}
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "chongqingweixinjia release v1.0.0.1",
        "device": "AIS630",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-07 09:55  生成txt文档
""", }

    path = {
        "txt_path": {
            "ui_name": "txt文档保存路径",
            "value": ""
        }
    }

    form = {
        "line_id": {
            "ui_name": "线体",
            "value": ""
        }
    }

    combo = {
        "board_face": {
            "ui_name": "面别",
            "item": ["T", "B", "T&B"],
            "value": "T"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        txt_path = data_vo.get_value_by_cons_key("txt_path", not_null=True)
        line_id = data_vo.get_value_by_cons_key("line_id")
        board_face = data_vo.get_value_by_cons_key("board_face")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        upload_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                comp_part = comp_entity.part
                repair_ng_str = comp_entity.repair_ng_str

                comp_result = comp_entity.get_final_result("0", "0", "1")

                comp_data_str += f"\n{comp_tag};{comp_part};{repair_ng_str};{comp_result}"

            test_result = board_entity.get_repair_result("PASS", "FAIL")

            txt_content = txt_template.format(**{
                "project_name": pcb_entity.project_name,
                "barcode": barcode,
                "line_id": line_id,
                "test_time": test_time,
                "upload_time": upload_time,
                "test_result": test_result,
                "board_face": board_face,
                "comp_number": board_entity.comp_total_number,
                "comp_ng_number": board_entity.comp_repair_ng_number,
                "comp_data_str": comp_data_str
            })

            if barcode:
                filename = f"{barcode}.txt"
            else:
                filename = f"{time_file}.txt"
                self.log.warning(f"未检测到条码，使用时间来命名txt文档！")

            filepath = f"{txt_path}/{filename}"

            xutil.FileUtil.write_content_to_file(filepath, txt_content)

        return self.x_response()
