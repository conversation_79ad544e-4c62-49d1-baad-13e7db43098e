# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/11/7 上午10:17
# Author     ：sch
# version    ：python 3.8
# Description：麦格米特2，重新对接的需求，和以前用C++版本对接的，接口有些许不同
"""
import json
import threading
import time
from typing import Any

import httpx

from common import xrequest, xutil
from common.xrequest import post_msg_to_repair
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

head_temp_str = """<head>
    <is_pass>{is_pass}</is_pass>
    <comp_num>{comp_num}</comp_num>
    <rpass_num>{rpass_num}</rpass_num>
    <ng_num>{ng_num}</ng_num>
    <pass_num>{pass_num}</pass_num>
    <fail_items>{comp_ng_str}</fail_items>
</head>"""

comp_ng_str_template = """<fail_items>{comp_ng_str}</fail_items>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "maigemite2 release v1.0.0.28",
        "device": "40x",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-11-07 10:20  条码校验,上传数据
date: 2024-11-14 10:33  新增从mes获取条码功能
date: 2024-11-14 16:07  增加请求头，以及修改参数：ObjectValue-->ObjecValue
date: 2024-11-15 10:51  details参数 OK的板卡，默认传一条OK的记录
date: 2024-11-15 16:11  所有异常都需要弹窗提示
date: 2024-11-20 17:01  将`条码校验`的接口移到【发送mes】的流程里调用
date: 2024-11-26 10:19  details参数改成传xml格式
date: 2024-11-26 16:21  增加content-type
date: 2024-11-26 16:38  修改请求方式
date: 2024-12-09 16:23  增加【维修站触发从mes获取条码】
date: 2024-12-10 10:02  条码校验按整板条码校验
date: 2024-12-10 10:38  没有板边条码则使用第一个拼板去校验
date: 2024-12-11 11:26  整板部分NG，只上传NG的拼板，PASS的拼板不上传
date: 2024-12-13 10:14  兼容维修站的条码校验
date: 2024-12-13 14:55  xml格式添加字段task_order_code
date: 2025-01-07 09:47  增加配置项：上传数据超时时间
date: 2025-01-21 11:11  jira:34860,增加xml格式段落
date: 2025-02-07 15:29  增加分板发送逻辑
date: 2025-03-14 00:32  use board sn check
date: 2025-03-21 20:34  增加配置项：getBarcodeType
date: 2025-03-22 11:30  mes数据发送增加异步发送配置
date: 2025-03-22 19:19  jira:38140,异步发送后获取到错误信息也要发送错误信息给维修站,让维修站显示弹窗
date: 2025-04-10 11:30  兼容维修站触发的条码校验
date: 2025-04-11 15:57  jira:38669,修改result字段参数
date: 2025-04-17 16:37  bugfix: 修复app_setting问题
""", }

    form = {
        "task_order": {
            "ui_name": "生产任务",
            "value": "",
        },
        "sequence": {
            "ui_name": "工序",
            "value": "",
        },
        "work_line": {
            "ui_name": "线体",
            "value": "",
        },
        "work_station": {
            "ui_name": "工站",
            "value": "3",
        },
        "work_device": {
            "ui_name": "设备ID",
            "value": "5",
        },
        "department": {
            "ui_name": "部门",
            "value": "",
        },
        "worker_leader": {
            "ui_name": "领班",
            "value": "",
        },
        "worker": {
            "ui_name": "作业人员",
            "value": "",
        },
        "mes_user": {
            "ui_name": "mes用户",
            "value": "",
        },
    }

    combo = {
        "work_shift_x": {
            "ui_name": "班次",
            "item": ["A", "B"],
            "value": "A",
        },
        "device_type": {
            "ui_name": "GetBarcodeType",
            "item": ["Inspector", "Repair"],
            "value": "Inspector",
        },
    }

    other_form = {
        "authorization": {
            "ui_name": "Authorization",
            "value": "Bearer 1",
        },

        "api_url_check_other": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data_other": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
        "api_url_get_sn_other": {
            "ui_name": "接口URL(获取条码)",
            "value": "",
        },

    }

    other_combo = {
        "data_api_timeout": {
            "ui_name": "上传数据超时时间(秒)",
            "item": ["1", "3", "5", "10", "15", "20", "30", "60", "120"],
            "value": "15",
        },
        "send_data_async": {
            "ui_name": "异步发送数据",
            "item": ["是", "否"],
            "value": "是",
        },
    }

    # app_setting = {
    #     "send_info_to_repair": False,
    #     "repair_ip": "",
    # }

    def __init__(self):
        self.main_window = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.main_window = main_window
        work_shift_x_item = getattr(main_window, "combo_work_shift_x")
        work_shift_x_item.setEditable(True)

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn_other")
        authorization = other_vo.get_value_by_cons_key("authorization")
        device_type = other_vo.get_value_by_cons_key("device_type")

        is_force_use_inspector = device_type == "Inspector"
        self.log.info(f"{is_force_use_inspector=}")

        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }

        barcode_map = other_vo.get_barcode_map()

        if barcode_map or is_force_use_inspector:
            self.log.info(f"主软件触发的【从mes获取条码】...")

            if is_force_use_inspector:
                barcode = other_vo.get_pcb_sn()
            else:
                barcode = barcode_map.get("-1", "")
        else:
            self.log.info(f"维修站触发的【从mes获取条码】...")
            barcode = other_vo.get_pcb_sn()

        self.log.info(f"barcode: {barcode}")

        get_sn_param = {
            "Data": [
                {
                    "Key": "lot_number",
                    "Value": barcode
                }
            ]
        }

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, get_sn_param, headers=headers)

        if str(ret.get("Code")) != "200":
            return self.x_response("false", f"mes接口异常，error：{ret.get('Message')}")

        if barcode_map or is_force_use_inspector:
            ret_sn = ret.get('ObjectData')
        else:
            ret_sn_list = ret.get('ObjectData', '').split(",")
            ret_sn = json.dumps({
                "barcodeList": ret_sn_list
            }, ensure_ascii=False)

        return self.x_response("true", ret_sn)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check_other")
        task_order = other_vo.get_value_by_cons_key("task_order")
        sequence = other_vo.get_value_by_cons_key("sequence")
        worker = other_vo.get_value_by_cons_key("worker")
        authorization = other_vo.get_value_by_cons_key("authorization")

        device_type = other_vo.get_value_by_cons_key("device_type")

        is_force_use_inspector = device_type == "Inspector"
        self.log.info(f"{is_force_use_inspector=}")

        sn_list = other_vo.list_sn()

        headers = {
            "Authorization": authorization,
        }

        barcode_map = other_vo.get_barcode_map()

        ret_res = self.x_response()
        #
        ret_msg_list = []

        if barcode_map or is_force_use_inspector:
            self.log.info(f"主软件触发的【条码校验】...")
            for sn in sn_list:
                check_param = {
                    "Data": [{
                        "Key": "json",
                        "Value": "",
                        "ObjecValue": {
                            "TaskOrder": task_order,
                            "Sequence": sequence,
                            "Worker": worker,
                            "BarCode": sn,
                        },
                        "ParaType": "1",
                    }]
                }

                ret = xrequest.RequestUtil.post_json(api_url_check, check_param, headers=headers)

                if str(ret.get("Code")) != "200":
                    ret_msg_list.append(f"mes接口异常，error：{ret.get('Message')}")

                    # ret_res = self.x_response("false", f"mes接口异常，error：{ret.get('Message')}")
        else:
            self.log.info(f"维修站触发的【条码校验】...")

            check_param = {
                "Data": [{
                    "Key": "json",
                    "Value": "",
                    "ObjecValue": {
                        "TaskOrder": task_order,
                        "Sequence": sequence,
                        "Worker": worker,
                        "BarCode": sn_list,
                    },
                    "ParaType": "1",
                }]
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param, headers=headers)

            if str(ret.get("Code")) != "200":
                ret_msg_list.append(f"mes接口异常，error：{ret.get('Message')}")

        if ret_msg_list:
            return self.x_response("false", f"\n".join(ret_msg_list))

        return ret_res

    def _send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # return self.x_response("false", "测试错误信息")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data_other")
        task_order = data_vo.get_value_by_cons_key("task_order")
        sequence = data_vo.get_value_by_cons_key("sequence")
        worker = data_vo.get_value_by_cons_key("worker")
        work_line = data_vo.get_value_by_cons_key("work_line")
        work_station = data_vo.get_value_by_cons_key("work_station")
        work_shift_x = data_vo.get_value_by_cons_key("work_shift_x")
        work_device = data_vo.get_value_by_cons_key("work_device")
        department = data_vo.get_value_by_cons_key("department")
        worker_leader = data_vo.get_value_by_cons_key("worker_leader")
        mes_user = data_vo.get_value_by_cons_key("mes_user")
        authorization = data_vo.get_value_by_cons_key("authorization")
        api_url_check = data_vo.get_value_by_cons_key("api_url_check_other")
        data_api_timeout = data_vo.get_value_by_cons_key("data_api_timeout", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }

        ret_msg_list = []

        # pcb_sn = pcb_entity.pcb_barcode

        check_sn = ""

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode

            if not check_sn and barcode:
                check_sn = barcode
                self.log.info(f"使用拼板条码去校验！")

        check_param = {
            "Data": [{
                "Key": "json",
                "Value": "",
                "ObjecValue": {
                    "TaskOrder": task_order,
                    "Sequence": sequence,
                    "Worker": worker,
                    "BarCode": check_sn,
                },
                "ParaType": "1",
            }]
        }

        ret1 = xrequest.RequestUtil.post_json(
            api_url_check,
            check_param,
            headers=headers,
            timeout=data_api_timeout
        )

        if str(ret1.get("Code")) != "200":
            msg1 = f"mes接口异常，条码校验失败，error：{ret1.get('Message')}"
            return self.x_response("false", msg1)

        pcb_result = pcb_entity.pcb_repair_result  # bool

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_ix = 0
            ng_count = 0
            pass_count = 0
            rpass_count = 0
            total_comp_count = 0

            # alg_max_ok = ""
            # alg_min_ok = ""
            # alg_val_ok = ""

            detail_list = []

            for comp_entity in board_entity.yield_comp_entity():
                total_comp_count += 1

                # if not alg_max_ok:
                #     for alg_entity in comp_entity.yield_alg_entity():
                #         if alg_entity.result == "0":
                #             alg_max_ok = alg_entity.max_threshold
                #             alg_min_ok = alg_entity.min_threshold
                #             alg_val_ok = alg_entity.test_val

                if comp_entity.is_repair_ng():
                    comp_ix += 1
                    ng_count += 1

                    # alg_max = ""
                    # alg_min = ""
                    # alg_val = ""
                    # for alg_entity in comp_entity.yield_alg_entity():
                    #     if alg_entity.result != "0":
                    #         alg_max = alg_entity.max_threshold
                    #         alg_min = alg_entity.min_threshold
                    #         alg_val = alg_entity.test_val

                    # detail_list.append(f"{comp_ix},{comp_entity.repair_ng_str},{alg_max},{alg_min},{alg_val},FAIL,/,/")
                    detail_list.append(
                        f'<item code="{comp_entity.repair_ng_code}" name="{comp_entity.type}" comment="{comp_entity.repair_ng_str}" comp_ref_dgr_code="{comp_entity.designator}" task_order_code="{task_order}" />')
                if comp_entity.final_result != "NG":
                    rpass_count += 1
                if comp_entity.final_result == "PASS":
                    pass_count += 1

            # if not detail_list:
            #     # OK的板卡，默认传一条OK的记录
            #     detail_list.append(f"{comp_ix},OK,{alg_max_ok},{alg_min_ok},{alg_val_ok},PASS,/,/")

            # detail_list_str = comp_ng_str_template.format(comp_ng_str="".join(detail_list))

            board_result = board_entity.get_repair_result("PASS", "FAIL")
            board_result3 = board_entity.get_final_result("PASS", "RPASS", "FAIL")

            board_result2 = board_entity.get_robot_result()
            if board_result2 == "NG":
                # 误报传2 NG传3
                is_pass = "2" if board_entity.repair_result else "3"
            else:
                # 直通传1
                is_pass = "1"

            detail_list_str = head_temp_str.format(**{
                "is_pass": is_pass,
                "comp_num": total_comp_count,
                "rpass_num": rpass_count,
                "ng_num": ng_count,
                "pass_num": pass_count,
                "comp_ng_str": "".join(detail_list)
            })

            data_param = {
                "Data": [{
                    "Key": "json",
                    "Value": "",
                    "ObjecValue": {
                        "TaskOrder": task_order,
                        "WorkLine": work_line,
                        "WorkStation": work_station,
                        "WorkShift": work_shift_x,
                        "WorkDevice": work_device,
                        "Department": department,
                        "Sequence": sequence,
                        "Worker": worker,
                        "WorkLeader": worker_leader,
                        "BarCode": barcode,
                        "Result": board_result3,
                        "details": detail_list_str,
                        "Data": "",
                        "mes_user": mes_user
                    },
                    "ParaType": "1",
                }]
            }

            # ret = xrequest.RequestUtil.post_json(api_url_data, data_param, headers=headers)
            # req_data = json.dumps(data_param, ensure_ascii=False)

            if not pcb_result and board_result == "PASS":
                self.log.warning(f"该整板部分NG，只上传NG的拼板，PASS的拼板不上传！")
            else:
                self.log.info(f"请求url：{api_url_data} 请求参数：\n{data_param}")
                t1 = time.time()
                ret = httpx.post(api_url_data, json=data_param, headers=headers, timeout=data_api_timeout).json()
                self.log.info(f"接口响应：{ret}")
                self.log.info(f"time cost: {time.time() - t1}")

                if str(ret.get("Code")) != "200":
                    ret_msg_list.append(f"mes接口异常，上传数据失败，error：{ret.get('Message')}")

        if ret_msg_list:
            return self.x_response("false", f"\n".join(ret_msg_list))

        return ret_res

    def _send_data_to_mes_sync(self, data_vo: DataVo, other_data: dict, other_param: Any):
        self.log.info('开始发送MES数据')

        try:
            resp = self._send_data_to_mes(data_vo, other_data, other_param)
        except Exception as e:
            resp = self.x_response("false", f"网络异常，error:{e}")

        # 如果是异步执行过来且数据发送异常，需要单独弹窗提示
        send_data_async = data_vo.get_value_by_cons_key("send_data_async")
        if send_data_async == '是' and not resp.get('result'):
            err_msg = '[async]' + resp.get('string')
            self.main_window.log_info(err_msg, False)

            config_data = xutil.FileUtil.load_config_file()
            app_setting_data = config_data.get('app_setting')

            send_info_to_repair = app_setting_data.get("send_info_to_repair", False)
            repair_ip = app_setting_data.get("repair_ip", "")

            # self.log.info(f"是否启用发送结果给维修站: {send_info_to_repair},维修站IP: {repair_ip}")

            #如果启用了维修站通知并且配置了维修站IP,则发送错误信息给维修站
            if send_info_to_repair and repair_ip:
                post_msg_to_repair(repair_ip,False,err_msg)

        return resp

    def _send_data_to_mes_async(self, data_vo: DataVo, other_data: dict, other_param: Any):
        self.log.info('===== 启动异步发送数据定时器 ====')
        timer = threading.Timer(0, self._send_data_to_mes_sync,
                                args=[data_vo, other_data, other_param])
        timer.start()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        send_data_async = data_vo.get_value_by_cons_key("send_data_async")
        if send_data_async == '是':
            self._send_data_to_mes_async(data_vo, other_data, other_param)
            return self.x_response()
        else:
            return self._send_data_to_mes_sync(data_vo, other_data, other_param)
