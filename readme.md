# MES配置器 (Python版本)

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15.9-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个功能强大的制造执行系统(MES)配置器，基于Python和PyQt5开发，支持多种AOI/SPI/SMT设备的数据采集和MES系统集成。

## 🚀 项目特性

### 核心功能
- 🔄 **数据同步**: 实时与MES系统进行数据交互
- 📊 **多设备支持**: 兼容AOI、SPI、SMT等多种检测设备
- 🎯 **条码管理**: 条码校验、获取和追溯功能
- 📈 **状态监控**: 设备状态实时监控和上报
- 🔧 **灵活配置**: 支持200+客户定制化配置

### 系统功能
- ⏰ **定时任务**: 定时清除界面计数和数据管理
- 🔍 **数据过滤**: 智能过滤重复发送的数据
- 📋 **数据合并**: 支持上下板面数据合并
- 🖥️ **系统集成**: 桌面快捷方式和开机自启动
- 🔐 **安全认证**: 密码确认和权限管理
- 🌐 **多语言**: 支持中英文界面切换

### MES交互功能
- 📥 **数据获取**: 从MES系统获取条码和工单信息
- ✅ **条码校验**: 实时条码有效性验证
- 📤 **数据上传**: 检测结果和设备数据上传
- 📊 **状态上报**: 设备运行状态实时上报


## 📂 项目架构

```
mesconfig_python/
├── common/              # 通用工具和配置
│   ├── xcache.py       # 缓存工具
│   ├── xconfig.py      # 配置管理
│   ├── xutil.py        # 通用工具函数
│   └── ...             # 其他通用模块
├── engine/             # 核心引擎
│   ├── DataEngine.py   # 数据处理引擎
│   ├── FtpEngine.py    # FTP通信引擎
│   └── MesEngine.py    # MES交互引擎
├── services/           # 服务层
│   ├── custom/         # 客户定制化实现
│   │   ├── standard/   # 标准实现
│   │   └── ...         # 200+客户定制实现
│   ├── http_service.py # HTTP服务
│   └── route.py        # 路由配置
├── views/              # 界面视图
├── mesconfig.py        # 主入口文件
└── ...                 # 其他文件和目录
```

## ⚙️ 配置和设置

### 配置文件位置

```shell
# 定制功能实现位置
services/custom/<客户名称>/main.py

# 界面参数值保存路径
~/.config/Leichen/mesconfig.json

# 与其他软件交互的部分设置
~/.config/Leichen/MES.ini
```

### 打包说明

打包后的文件将生成在 `dist` 目录中：

```shell
# 单文件打包
pyinstaller --onefile --hidden-import=somepackage mesconfig.py

# 目录打包
pyinstaller --name=mesconfig mesconfig.py
```

### 界面UI相关

**利用pyuic5将ui文件转换为py文件**

操作系统上正确配置python环境之后，pyuic5也是一个可以识别的命令行指令了

https://www.jianshu.com/p/43300f85af3e

将pyuic5配成pycharm的额外扩展工具

https://blog.csdn.net/gu_fcsdn/article/details/104701322

参考命令： `pyuic5 test.ui -o test.py`


### 翻译说明

1. 参考资料：https://www.cnblogs.com/siyun/p/13723440.html
2. 先使用命令 `pylupdate5` 使用py文件生成 `.ts` 文件，如下
   ```
   pylupdate5 *.py -ts all_data.ts
   # or,当然也可以直接转义ui文件
   pylupdate5 ./ui/*.ui -ts mes_translate.ts
   ```
3. 使用语言家 Linguist.exe 打开.ts 文件， 选择需要翻译字样，并将译文写入，最后发布程序 `.qm` 文件
4. 在程序中中加载 `.qm` 文件


## 🔧 开发指南

### 客户定制开发

1. **创建新客户配置**
   ```bash
   # 在 services/custom/ 目录下创建新的客户目录
   mkdir services/custom/<客户名称>
   cp services/custom/standard/main.py services/custom/<客户名称>/
   ```

2. **实现客户特定功能**
   ```python
   # 在 main.py 中继承 BaseEngine 类
   class Engine(BaseEngine):
       version = {
           "customer": ["客户中文名", "客户英文名"],
           "version": "release v1.0.0.1",
           "device": "设备型号",
           "feature": ["功能列表"],
           "author": "开发者",
       }
   ```

### 开发注意事项

- **配置冲突**: 本地调试时如遇到 `other_form`、`other_combo` 配置错误，删除 `~/.config/Leichen/mesconfig.json` 后重启
- **配置隔离**: 不同客户的配置key可能冲突，建议使用唯一的配置键名
- **版本管理**: 独立分支的打包脚本需要更新以包含git commit信息


### 非标客户、独立分支

ps： **部分早期**独立分支的打包脚本未更新，无法将git commit号打包到程序里
- 明纬
   - 正常版本 -- SMT
   - DIP 版本
- 芜湖华丰
- 云意电气
- 欣旺达-自动换线功能
- 威睿
- 深圳精科睿
- 蓝微
- 东莞台达  --- 很多非标功能
- 黄岛海信  --- 日志功能需要长期保存
- 佛山海尔  --- 简要日志里面，假设不发送MES，不需要打印发送成功
- 深圳创维  --- 无论是否勾选【使用密码】，保存参数时，都要输入密码确认
- 知识城    ---  （AIS203/AIS501版本）  新增的需求，改动到底层数据模块，影响到所有客户脚本，故将代码移动到了独立分支(zhishicheng/zhishicheng430) 
- 嘉兴安费诺V3  (branch:jiaxinganfeinuo_v3)   --- 日志需要按日期保存，保存30天
- 麦格米特2  (branch:maigemite2)   --- 维修站触发的条码校验，只要有一个成功，就可以发送mes
- 知识城430版本   --- 修改到公共代码
- 罗马尼亚Varroc --(branch:varroc),修改到异常弹窗的背景颜色
- 合肥长鑫 --(branch:hefeichangxin_SecsGem),半导体SECS/GEM协议
- 欣旺达  (鉴锐欣供应商) -- (branch:xinwangda_jrx) AIS431通用版本，因为有工单搜索功能，所以已经移动到非标分支。 深圳/惠州/浙江欣旺达 AIS431设备使用

## 🛠️ 安装和运行

### 系统要求

- **操作系统**: Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **Python版本**: Python 3.8+
- **内存**: 最低 4GB RAM
- **存储**: 最低 500MB 可用空间

### 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd mesconfig_python
   ```

2. **创建虚拟环境**
   ```bash
   # 使用 venv
   python3.8 -m venv venv
   source venv/bin/activate  # Linux/macOS
   # 或
   venv\Scripts\activate     # Windows
   
   # 使用 conda
   conda create -n mesconfig python=3.8
   conda activate mesconfig
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **运行程序**
   ```bash
   python mesconfig.py
   ```

### 常见问题

- **PyQt5安装问题**: 如遇到Qt相关错误，尝试安装不同版本的PyQt5
- **缺少依赖**: 运行时如提示缺少库，使用 `pip install <库名>` 安装
- **权限问题**: 确保有足够的文件读写权限

## 📚 API文档

### 核心引擎类

#### BaseEngine
基础引擎类，所有客户定制都需要继承此类。

```python
class BaseEngine(metaclass=abc.ABCMeta):
    # 版本信息
    version = {...}
    # 表单配置
    form = {...}
    # 按钮配置
    button = {...}
    # 下拉框配置
    combo = {...}
```

#### MesEngine
MES交互引擎，处理与MES系统的通信。

### HTTP API

#### 设备状态查询
```
GET /ping
响应: {"message": "Hello!"}
```

#### 设备状态上报
```
POST /<客户名称>/device_status
请求体: {"Resource": "设备编号"}
响应: {"Result": "OK", "State": "设备运行"}
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. **Fork** 本仓库
2. **创建** 特性分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Merge Request
6. **等待** 代码审查和合并

### 代码规范

- 遵循 PEP 8 Python 代码风格
- 添加适当的注释和文档字符串
- 确保所有测试通过
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与联系

- **问题反馈**: 请在 [Issues](../../issues) 页面提交
- **功能请求**: 请在 [Issues](../../issues) 页面提交功能请求
- **技术支持**: 联系开发团队

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

---

**注意**: 本项目持续更新中，如有任何问题或建议，欢迎提交Issue或Pull Request。
