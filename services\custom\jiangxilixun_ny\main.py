# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/12/27 上午9:50
# Author     ：sch
# version    ：python 3.8
# Description：江西立讯能源车间    江西吉安立讯能源车间
"""

import time
from datetime import datetime
import os
from typing import Any
from engine.FtpEngine import FTPClient

from common import xrequest, xcons, xenum
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "jiangxilixun_ny release v1.0.0.6",
        "device": "AIS203",
        "feature": ["条码校验", "上传数据", "获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2024-12-27 14:42  条码校验, 上传数据
date: 2025-01-11 10:59  新增从mes获取条码
date: 2025-01-13 15:38  兼容接口的返回
date: 2025-01-15 11:45  增加整版图上传ftp
date: 2025-01-16 12:41  修改上传ftp的文件名命名方式
date: 2025-03-04 11:08  jira:36419 其它参数增加【整板条码前缀】和【拼板条码前缀】配置，在发送整板和拼板条码之前添加配置的条码前缀
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口地址(条码检查)",
            "value": "http://127.0.0.1:8081/SMT/CheckPanel",
        },
        "api_url_save": {
            "ui_name": "接口地址(保存测试)",
            "value": "http://127.0.0.1:8081/SMT/AoiSnGo",
        },
        "api_url_panel_sn": {
            "ui_name": "接口地址(整版码获取条码)",
            "value": "http://127.0.0.1:8082/SMT/CheckPanelGetSnList",
        },
        "api_url_tooling_sn": {
            "ui_name": "接口地址(治具码获取条码)",
            "value": "http://127.0.0.1:8082/SMT/CheckToolingGetSnList",
        },
        "ftp_host": {
            "ui_name": "FTP服务器地址",
            "value": "127.0.0.1",
        },
        "ftp_port": {
            "ui_name": "FTP端口",
            "value": "21",
        },
        "ftp_user": {
            "ui_name": "FTP用户名",
            "value": "admin",
        },
        "ftp_password": {
            "ui_name": "FTP密码",
            "value": "123456",
        },
        "ftp_remote_path": {
            "ui_name": "FTP远程路径",
            "value": "",
        },
        "pcb_barcode_prefix": {
            "ui_name": "整板条码前缀",
            "value": "",
        },
        "board_barcode_prefix": {
            "ui_name": "拼板条码前缀",
            "value": "",
        },
    }

    form = {
        "equipment_id": {
            "ui_name": "设备号",
            "value": "",
        },
        "terminal_id": {
            "ui_name": "站点ID",
            "value": "",
        },
        "emp_no": {
            "ui_name": "用户工号",
            "value": "",
        }
    }

    combo = {
        "obtain_barcode": {
            "ui_name": "获取条码方式",
            "item": [
                "整版码",
                "治具码"
            ],
            "value": "整版码"
        }
    }

    # path = {
    #     "local_save_path": {
    #         "ui_name": "本地保存路径",
    #         "value": "",
    #     }
    # }

    def __init__(self):
        self.common_config["check_barcode_setting1"] = xenum.CheckSetting1.CheckFirst

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        """
        从mes获取条码
        """
        try:
            obtain_barcode = other_vo.get_value_by_cons_key("obtain_barcode")
            equipment_id = other_vo.get_value_by_cons_key("equipment_id")
            terminal_id = other_vo.get_value_by_cons_key("terminal_id")
            emp_no = other_vo.get_value_by_cons_key("emp_no")
            pcb_barcode_prefix = other_vo.get_value_by_cons_key("pcb_barcode_prefix")

            pcb_sn = other_vo.get_pcb_sn()
            if pcb_barcode_prefix:
                pcb_sn = pcb_barcode_prefix + pcb_sn

            # 根据界面获取条码方式确定使用哪个接口
            if obtain_barcode == "整版码":
                #  整版码接口
                api_url = other_vo.get_value_by_cons_key("api_url_panel_sn")
            else:
                # 治具码接口
                api_url = other_vo.get_value_by_cons_key("api_url_tooling_sn")

            # 检查接口地址是否配置
            if not api_url:
                return self.x_response("false", f"获取条码接口地址未配置，请检查配置")

            get_param = {
                "equipmentNo": equipment_id,
                "terminalId": terminal_id,
                "serialNumber": pcb_sn,
                "empNo": emp_no,
            }

            self.log.info(f"开始调用获取条码接口，接口地址：{api_url}，参数：{get_param}")
            ret = xrequest.RequestUtil.post_json(api_url, get_param)
            self.log.info(f"获取条码接口返回：{ret}")

            if str(ret.get("status")) != "0":
                return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('message')}")

            sn_orders = ret.get('SnOrders', [])
            if not sn_orders:
                sn_orders = ret.get('snOrders', [])

            if not sn_orders:
                return self.x_response("false", "未获取到拼板条码信息")

            ret_list = []
            for item in sn_orders:
                sn = item.get('SerialNumber')
                if sn:
                    ret_list.append(sn)
            return self.x_response("true", ",".join(ret_list))

        except Exception as e:
            self.log.error(f"获取条码异常：{str(e)}")
            return self.x_response("false", f"获取条码失败：{str(e)}")

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        equipment_id = other_vo.get_value_by_cons_key("equipment_id")
        terminal_id = other_vo.get_value_by_cons_key("terminal_id")
        emp_no = other_vo.get_value_by_cons_key("emp_no")
        pcb_barcode_prefix = other_vo.get_value_by_cons_key("pcb_barcode_prefix")
        board_barcode_prefix = other_vo.get_value_by_cons_key("board_barcode_prefix")

        ret_res = self.x_response()

        sn_list = other_vo.list_sn()
        for index, sn in enumerate(sn_list):
            if index == 0 and pcb_barcode_prefix:
                sn = pcb_barcode_prefix + sn
            elif index > 0 and board_barcode_prefix:
                sn = board_barcode_prefix + sn

            check_param = {
                "EquipmentNo": equipment_id,
                "TerminalId": terminal_id,
                "SerialNumber": sn,
                "EmpNo": emp_no,
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
            if str(ret.get("status")) != "0":
                ret_res = self.x_response("false", f"mes接口返回错误: {ret.get('message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_save = data_vo.get_value_by_cons_key("api_url_save")
        equipment_id = data_vo.get_value_by_cons_key("equipment_id")
        terminal_id = data_vo.get_value_by_cons_key("terminal_id")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        pcb_barcode_prefix = data_vo.get_value_by_cons_key("pcb_barcode_prefix")
        board_barcode_prefix = data_vo.get_value_by_cons_key("board_barcode_prefix")

        # FTP配置
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_remote_path = data_vo.get_value_by_cons_key("ftp_remote_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        if pcb_barcode_prefix:
            pcb_sn = pcb_barcode_prefix + pcb_sn
        pcb_images = pcb_entity.pcb_image
        self.log.info(f"整版图路径：{pcb_images}")

        image_list = pcb_images if isinstance(pcb_images, list) else [pcb_images]

        # 上传到FTP
        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        # 获取程序名
        project_name = pcb_entity.project_name

        # 获取当前日期
        today = datetime.now().strftime("%Y-%m-%d")
        self.log.info(f"当前日期为：{today}")

        # 在FTP上创建 程序名/日期文件夹
        remote_dir = f"{ftp_remote_path}/{project_name}/{today}"
        ftp_client.cd_or_mkdir(remote_dir)

        for pcb_image in image_list:
            if pcb_image and os.path.exists(pcb_image):
                # 使用原始文件名或者生成新的文件名
                # file_ext = os.path.splitext(pcb_image)[1]
                time_date = pcb_entity.get_start_time()
                new_filename = f"{pcb_sn}{time_date}"
                self.log.info(f"文件名为：{new_filename}")

                # 直接上传到FTP
                ftp_client.upload_file(pcb_image, new_filename)
                self.log.info(f"文件上传成功: {new_filename}")

        # 关闭FTP连接
        ftp_client.close()

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            if barcode and board_barcode_prefix:
                barcode = board_barcode_prefix + barcode

            comp_list = []
            only_one_ng_code = "N/A"

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator

                    for alg_entity in comp_entity.yield_alg_entity():
                        min_v = alg_entity.min_threshold
                        max_v = alg_entity.max_threshold

                        comp_list.append({
                            "itemName": comp_entity.robot_ng_str,
                            "itemType": alg_entity.test_name,
                            "itemValue": f"{min_v}~{max_v}",
                            "itemDescription": comp_tag,
                            "result": comp_entity.get_final_result("OK", "OK", "NG"),
                            "testTime": start_time,
                            "endTime": end_time,
                        })

                if comp_entity.is_repair_ng():
                    if only_one_ng_code == "N/A":
                        only_one_ng_code = comp_entity.repair_ng_code

            #
            board_list.append({
                "equipmentNo": equipment_id,
                "terminalId": terminal_id,
                "serialNumber": barcode,
                "empNo": emp_no,
                "defectCode": only_one_ng_code,
                "defectDesc": only_one_ng_code,
                "itemTests": comp_list,
            })

        # data_param = {
        #     "PanelNo": pcb_sn,
        #     "Datas": board_list
        # }

        ret = xrequest.RequestUtil.post_json(api_url_save, board_list)

        err_msg_list = []
        for item in ret:
            if str(item.get("status")) != "0":
                err_msg_list.append(f"{item.get('message')}")

        if err_msg_list:
            err_msgs = "\n".join(err_msg_list)
            return self.x_response("false", f"mes返回错误，{err_msgs}")

        return self.x_response()
