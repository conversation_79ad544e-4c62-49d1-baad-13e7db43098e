# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/4/17 下午5:18
# Author     ：sch
# version    ：python 3.8
# Description：宁波翰文
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import x_response, log, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import BaseEngine

global_data = {}


def post_device_status(api_url, status_code: str) -> dict:
    """
    发送设备状态
    :return:
    """
    upload_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT2)[:-3]
    device_url = f"{api_url}/SC_MES_SaveStatusData"
    device_param = {
        "json": json.dumps({
            "DataID": xutil.OtherUtil.get_uuid4_str(),
            "DeviceNo": "CDS02",
            "DeviceStatus": status_code,
            "AcquisitionTime": upload_time
        }, ensure_ascii=False)
    }

    ret_str = xrequest.RequestUtil.post_form(device_url, device_param, to_json=False)
    ret = xutil.XmlUtil.get_xml_root_by_str(ret_str).text

    ret = json.loads(ret)

    if ret.get("Result") != "1":
        return x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('ReturnMsg')}")

    return x_response()


class HanWenEngine(BaseEngine):
    version = {
        "title": "ningbohanwen release v1.1.2.6",
        "device": "630B",
        "feature": ["上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-17 17:17  init
date: 2023-04-18 10:30  上传数据、设备状态
date: 2023-04-18 15:54  解析返回的json参数
date: 2023-05-05 16:24  产量字段重新统计
date: 2023-05-08 11:04  OK和NG的板子分开统计
""",
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://192.168.1.199/SPIWebService/DAQ_SPI_Service.asmx"
        },
        "batch_no": {
            "ui_name": "产品批次",
            "value": "CDZ00001"
        },
    }

    button = {
        "clear_board_count": {
            "ui_name": "产量清零"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        batch_no = data_vo.get_value_by_cons_key("batch_no")

        data_url = f"{api_url}/SC_MES_SaveQuantityData"

        upload_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT2)[:-3]
        pcb_entity = data_vo.pcb_entity

        board_count_ok = global_data.get("board_count_ok", 0)
        board_count_ng = global_data.get("board_count_ng", 0)

        if pcb_entity.pcb_repair_result:
            board_count_ok += 1
        else:
            board_count_ng += 1

        global_data["board_count_ok"] = board_count_ok
        global_data["board_count_ng"] = board_count_ng

        self.log.info(f"{pcb_entity}")

        data_param = {
            "json": json.dumps({
                "DataID": xutil.OtherUtil.get_uuid4_str(),
                "DeviceNo": "CDS02",
                "BatchNo": batch_no,
                "Quantity": board_count_ok,
                "NGQuantity": board_count_ng,
                "QuantityUOM": "",
                # "QualityStatus": "PASS" if pcb_entity.pcb_repair_result else "NG",
                "QualityStatus": pcb_entity.get_repair_result("PASS", "NG"),
                "AcquisitionTime": upload_time,

            }, ensure_ascii=False)
        }

        ret_str = xrequest.RequestUtil.post_form(data_url, data_param, to_json=False)
        ret = xutil.XmlUtil.get_xml_root_by_str(ret_str).text

        ret = json.loads(ret)

        if ret.get("Result") != "1":
            return x_response("false", f"mes接口异常，上传测试数据失败，error：{ret.get('ReturnMsg')}")

        return x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        device_str = other_vo.get_device_status_str()

        if device_str in ["进板", "开始检测", "停止检查", "出板"]:
            status_code = "1"

        elif device_str in ["安全门", "调试", "板卡 NG", "紧急故障", "其他错误"]:
            status_code = "2"
        else:
            log.warning(f"未知的设备状态：{device_str}，不上传Mes！")
            return x_response()

        res = post_device_status(api_url, status_code)
        if res:
            return res

        return x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()

        if btn_key == "clear_board_count":
            global_data["board_count_ok"] = 0
            global_data["board_count_ng"] = 0

        return x_response()
