{
  "constants": {  // UI界面界面自定义参数
    "user": "admin",
    "password": "123456",
    "mesPath": "/home/<USER>/aoi/run/results/fakepcb.002/20220808/T_20220808160830607_1_NG"
  },
  "data": {
    "1": {
      "Barcode": "",  // 条码
      "BoardIndex": -1,
      "BoardNo": "1",  // 板子编号
      "BoardResult": "TRUE",  // 人工复判结果，也就是整块板子的最终结果  （TRUE，正常） （FALSE，不良）
      "BoardSide": "0",  // 版面 (0, TOP) (1, bottom)
      "Comps": [
        {
          "AlgorithmList": [
            {
              "MaxThreshold": "100.00",  // 最大阈值
              "MinThreshold": "50.00",  // 最小阈值
              "Result": "1",  // 算法检测结果    0：OK，其它：NG
              "TestName": "InspMissAi",  // 算法名称
              "TestVal": "-1.00"  // 算法结果阈值
            }
          ],
          "CompDesignator": "COMP1008",  //  器件位号
          "CompHeight": 0,  //  器件高度
          "CompId": "ed81da79-24e5-42e2-9fd0-bc670404e36b",  // 器件UUID
          "CompImagePath": "/home/<USER>/aoi/run/results/t.001/20220519/T_20220519141204290_1_NG/images/ng/IC/0/COMP1008_1008.png",
          "CompNgType": "0",  // 人工复判结果  （0，代表正常）（其他编号，表示不良）
          "CompNgTypeStr": "OK",  //  人工复判说明
          "CompPackage": "",  // 器件封装
          "CompPart": "",  // 器件料号
          "CompRobotNgType": "1",  // 机器判定结果  （0，代表正常）（其他编号，表示不良）
          "CompRobotNgTypeStr": "漏件",  // 机器判定结果说明
          "CompType": "IC",  // 器件类型
          "CompWidth": 0,  // 器件宽度
          "CompXPos": 0,
          "CompXoffset": "0",
          "CompYPos": 0,
          "CompYoffset": "0",
          "TemplateArea": 0,
          "compRect": ""
        }
      ],
      "DataTime": "2022-05-19T14:12:04",  // 板卡测试时间
      "ImagePath": "",  // 板卡图片地址
      "InspecTimeSrc": 3299,  // 检测持续时间，需要/1000，然后单位就是s
      "OrderID": "",
      "PcbBarcode": "",  // 治具编号,  大板的SN
      "ProjectName": "t.001",  // 项目工程名字
      "RepairUser": "admin",   // 复判用户名
      "RobotResult": "FALSE",  // AOI机器测试结果，   （TRUE，正常） （FALSE，不良）
      "TrackIndex": 0,  // (0, 1轨道) （1，2轨道）
      "UnionBarcode": "",
      "UnionID": "-1"  // UUID
    }
  }
}
