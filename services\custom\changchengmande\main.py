"""
#,File,,,,,,,:,main.py
#,Time,,,,,,,：2025/07/10,14:34
#,Author,,,,,："wxc"
#,version,,,,：python,3.8
#,Description：长城曼德
"""
from typing import Any

from common import xrequest, xcons, xutil
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo

fieldnames = """Version,CsvFileFormatVersion,OutputInfo,OutputInfoFaultComponentInfo,OutputInfoPinInfo,InspectionMachine,\
InspectionProcess,ProgramName,BothSideCode,CreateMachine,LibraryName,ReferencePosition,PcbSizeHeight,pcbSizeWidth,\
RailWidth,SavedDate,PcbNo,TestTime,CreateDate,PersonRevisor,PersonRevisorCode,ReviseTime,ReviseEndDate,TestResult,\
ReviseResult,OverlookFault,LotCount,Barcode,FaultRate,ComponentTotal,PinTotal,LandTotal,OutComponentTotal,\
VisualFaultFlag,RevisorMachineId,RevisorComputerName,Barcode,BothSideCode,PcbNo,ComponentBlockNo,ComponentBlockName,\
ComponentNo,PartsName,PartsTypeNo,PartsVarNo,PartsVarName,PartsArticleNo,PinNo,PinSpaceNo,FaultCode,RevisedFaultId,\
XShift,YShift,AngleMount,ComponentReviseEndDate,OutComponentFlag,FaultLandCount,Grid,ComponentPersonRevisor,\
ComponentReviseTime,ComponentReviseMachineId,ComponentReviserComputerName
{data}"""

data_template = """{Version},{CsvFileFormatVersion},{OutputInfo},{OutputInfoFaultComponentInfo},{OutputInfoPinInfo},\
{InspectionMachine},{InspectionProcess},{ProgramName},{BothSideCode},{CreateMachine},{LibraryName},\
{ReferencePosition},{PcbSizeHeight},{pcbSizeWidth},{RailWidth},{SavedDate},{PcbNo},{TestTime},{CreateDate},\
{PersonRevisor},{PersonRevisorCode},{ReviseTime},{ReviseEndDate},{TestResult},{ReviseResult},\
{OverlookFault},{LotCount},{Barcode},{FaultRate},{ComponentTotal},{PinTotal},{LandTotal},\
{OutComponentTotal},{VisualFaultFlag},{RevisorMachineId},{RevisorComputerName},{Barcode},{BothSideCode},\
{PcbNo},{ComponentBlockNo},{ComponentBlockName},{ComponentNo},{PartsName},{PartsTypeNo},{PartsVarNo},\
{PartsVarName},{PartsArticleNo},{PinNo},{PinSpaceNo},{FaultCode},{RevisedFaultId},{XShift},{YShift},\
{AngleMount},{ComponentReviseEndDate},{OutComponentFlag},{FaultLandCount},{Grid},{ComponentPersonRevisor},\
{ComponentReviseTime},{ComponentReviseMachineId},{ComponentReviserComputerName}"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["长城曼德", "changchengmande"],
        "version": "release v1.0.0.3",
        "device": "AIS401-C,AIS501,AIS501-B",
        "feature": ["条码校验", "保存csv到本地"],
        "author": "wxc",
        "release": """
date: 2025-07-10 14:35 ATAOI_2019-40903：条码校验,保存csv到本地，ftp上传
date: 2025-07-15 19:30 ATAOI_2019-40903：ComponentBlockNo、ComponentBlockName等字段修改，文件命名规则修改
date: 2025-07-16 16:38 序列号为条码，条码为空是文件命名规则改为拼板序号作区分
"""
    }
    other_form = {
        "ftp_server": {
            "ui_name": "FTP服务器",
            "value": "",
        },
        "ftp_port": {
            "ui_name": "FTP端口",
            "value": "",
        },
        "ftp_user": {
            "ui_name": "FTP用户名",
            "value": "",
        },
        "ftp_password": {
            "ui_name": "FTP密码",
            "value": "",
        },
        "ftp_path": {
            "ui_name": "FTP路径",
            "value": "",
        },
        "server_ip": {
            "ui_name": "服务器IP",
            "value": "**********",
        },
        "port": {
            "ui_name": "端口",
            "value": "4057",
        },
        "server_name": {
            "ui_name": "服务器名称",
            "value": "GWDJ-LEDJK",
        },
        "station": {
            "ui_name": "站别",
            "value": "0102-1410-36",
        },
    }
    form = {
        "device_name": {
            "ui_name": "Machine",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "line": {
            "ui_name": "线体",
            "value": "",
        },
    }
    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        server_ip = other_vo.get_value_by_cons_key("server_ip")
        port = other_vo.get_value_by_cons_key("port", to_int=True)
        server_name = other_vo.get_value_by_cons_key("server_name")
        station = other_vo.get_value_by_cons_key("station")

        msgStr = '1010;{ServerName};{StationID};SN={bcode}'
        sn_list = other_vo.list_sn()
        for sn in sn_list:
            msgStr = msgStr.format(ServerName=server_name, StationID=station, bcode=sn)
            recvdata = xrequest.SocketUtil.send_bytes_to_socket_server(server_ip, port, msgStr)
            recvlist = recvdata[:-1].split(';')
            if recvlist[0] != '1011' or recvlist[4] != 'OK':
                return self.x_response("false", f"mes接口异常")
        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        machine = data_vo.get_value_by_cons_key("device_name")
        operator = data_vo.get_value_by_cons_key("operator")
        line = data_vo.get_value_by_cons_key("line")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        ftp_server = data_vo.get_value_by_cons_key("ftp_server")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)

        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name
        side = "0" if pcb_entity.board_side == "T" else "1"
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        file_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        end_time = pcb_entity.get_end_time()
        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_FILE)
        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            board_no = board_entity.board_no
            self.log.info(board_entity)

            # 序列号为条码，条码为空是文件命名规则改为拼板序号作区分
            serial_number = barcode
            if not barcode:
                serial_number = board_no
            data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                data = {
                    "Version": "0",
                    "CsvFileFormatVersion": "3.10A",
                    "OutputInfo": "",
                    "OutputInfoFaultComponentInfo": "",
                    "OutputInfoPinInfo": "",
                    "InspectionMachine": machine,
                    "InspectionProcess": "",
                    "ProgramName": project_name,
                    "BothSideCode": side,
                    "CreateMachine": machine,
                    "LibraryName": "",
                    "ReferencePosition": "",
                    "PcbSizeHeight": "",
                    "pcbSizeWidth": "",
                    "RailWidth": "",
                    "SavedDate": test_time,
                    "PcbNo": "",
                    "TestTime": "0",
                    "CreateDate": test_time,
                    "PersonRevisor": operator,
                    "PersonRevisorCode": line,
                    "ReviseTime": "0",
                    "ReviseEndDate": end_time,
                    "TestResult": board_entity.get_robot_result("OK", "NG"),
                    "ReviseResult": board_entity.get_repair_result("OK", "NG"),
                    "OverlookFault": "",
                    "LotCount": "",
                    "Barcode": barcode,
                    "FaultRate": "",
                    "ComponentTotal": "",
                    "PinTotal": "",
                    "LandTotal": "",
                    "OutComponentTotal": "",
                    "VisualFaultFlag": "",
                    "RevisorMachineId": "",
                    "RevisorComputerName": "",
                    "ComponentBlockNo": "1",  # 拼板数量（默认1）
                    "ComponentBlockName": board_no,  # 拼板号
                    "ComponentNo": comp_entity.part,  # 物料料号
                    "PartsName": comp_entity.designator,  # 物料位置号
                    "PartsTypeNo": "",
                    "PartsVarNo": "",
                    "PartsVarName": "",
                    "PartsArticleNo": "",
                    "PinNo": "0",
                    "PinSpaceNo": "-1",
                    "FaultCode": comp_entity.robot_ng_code,
                    "RevisedFaultId": comp_entity.repair_ng_code,
                    "XShift": comp_entity.x_offset,
                    "YShift": comp_entity.y_offset,
                    "AngleMount": "",
                    "ComponentReviseEndDate": "",
                    "OutComponentFlag": "",
                    "FaultLandCount": "",
                    "Grid": "",
                    "ComponentPersonRevisor": "",
                    "ComponentReviseTime": "",
                    "ComponentReviseMachineId": "",
                    "ComponentReviserComputerName": "",
                }
                data_str += data_template.format(**data) + "\n"
            content = fieldnames.format(data=data_str)
            file_name = f"{save_path}/{machine}_{serial_number}_{project_name}_{side}_{file_time}_{review_time}.csv"
            xutil.FileUtil.write_content_to_file_pro(file_name, content)
            # 上传到FTP服务器
            ftp_client = FTPClient(ftp_server, ftp_user, ftp_password, ftp_port)
            ftp_client.login()
            ftp_client.cd_or_mkdir(ftp_path)
            ftp_client.upload_file(file_name)
        return self.x_response()
