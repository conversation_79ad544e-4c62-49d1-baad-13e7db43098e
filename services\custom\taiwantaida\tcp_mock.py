# -*- coding:UTF-8 -*-
# <AUTHOR> sunchangheng
# time : 2022/6/11 上午10:41
"""
粘包版本
"""
import json
import socket
import struct

sk = socket.socket()

sk.bind(("0.0.0.0", 9099))
sk.listen()

PACKETSTART = b'PACKET_START'
PACKETEND = b'PACKET_END'
PACKETTYPE = b'02'

# 包数据结构：  包头[12]+长度[8]+包类型[2]+数据[N]+包尾[10]

print('waiting....')
while True:
    conn, addr = sk.accept()
    print(f"recv from: {addr}")

    packet_start = conn.recv(12)
    if packet_start == b'':
        continue

    packet_len = conn.recv(8)
    print("pocket data len", packet_len)
    packet_type = conn.recv(2)
    data_len = struct.unpack("!q", packet_len)[0]
    print("data len", data_len)
    data = conn.recv(data_len)
    pack_end = conn.recv(10)

    print("--------------------------recv")
    # print("data", data, type(data))
    data_str = data.decode("utf8")
    print(data_str)
    data_json = json.loads(data_str)

    data_type = data_json.get("type")

    print(f"{data_type=}")
    if data_type == 12:
        string = '{"Result":"OK","Message":[{"MO_NUMBER":"19923413272","MODEL_NAME":"DAP-11036AC-1 C","LINE_NAME":"A05"},{"MO_NUMBER":"19923413282","MODEL_NAME":"DAP-11036AC-1 D","LINE_NAME":"A05"},{"MO_NUMBER":"19923413969","MODEL_NAME":"DAP-11036AC-1 B","LINE_NAME":"A05"},{"MO_NUMBER":"19923413989","MODEL_NAME":"DAP-11036AC-1 BA","LINE_NAME":"A05"},{"MO_NUMBER":"19923414896","MODEL_NAME":"DAP-11036AC-1 BA","LINE_NAME":"A05"},{"MO_NUMBER":"19923415538","MODEL_NAME":"DAP-11036AC-1 AB","LINE_NAME":"A05"},{"MO_NUMBER":"19923415691","MODEL_NAME":"DAP-11036AC-1 B","LINE_NAME":"A05"},{"MO_NUMBER":"19923419304","MODEL_NAME":"DAP-11036AC-1 B","LINE_NAME":"A05"},{"MO_NUMBER":"19923419314","MODEL_NAME":"DAP-11036AC-1 BA","LINE_NAME":"A05"},{"MO_NUMBER":"19923420478","MODEL_NAME":"DAP-11036AC-1 BA","LINE_NAME":"A05"},{"MO_NUMBER":"19923420488","MODEL_NAME":"DAP-11036AC-1 B","LINE_NAME":"A05"},{"MO_NUMBER":"19923421725","MODEL_NAME":"DAP-11036AC-1 C","LINE_NAME":"A05"},{"MO_NUMBER":"19923422701","MODEL_NAME":"DAP-11036AC-1 BA","LINE_NAME":"A05"},{"MO_NUMBER":"20023403543","MODEL_NAME":"DAP-11036AC-1 A","LINE_NAME":"A05"},{"MO_NUMBER":"20023404670","MODEL_NAME":"DAP-11036AC-1 A","LINE_NAME":"A05"},{"MO_NUMBER":"20023407030","MODEL_NAME":"DAP-11036AC-1 A","LINE_NAME":"A05"}]}'
    elif data_type == 14:
        string = '{"Result":"OK","Message":[{"LINE_NAME":"H02","SECTION_NAME":"M_APPLYGLU_PFM","GROUP_NAME":"M_APPLYGLU","STATION_NAME":"M_APPLYGLUE"},{"LINE_NAME":"H02","SECTION_NAME":"M_INPUT","GROUP_NAME":"M_INPUT","STATION_NAME":"M_INPUT"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_ICTTEST","STATION_NAME":"M_ICTTEST"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC04"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC09"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC11"},{"LINE_NAME":"H02","SECTION_NAME":"M_SOLDER_INSP","GROUP_NAME":"M_SOLDER_INSP","STATION_NAME":"M_SOLDER_INSP"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC05"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC07"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC15"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC16"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC19"},{"LINE_NAME":"H02","SECTION_NAME":"M_DIVIDING","GROUP_NAME":"M_DIVIDING","STATION_NAME":"M_DIVIDING"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_FCT","STATION_NAME":"M_FCT"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC01"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC17"},{"LINE_NAME":"H02","SECTION_NAME":"M_TORQUE","GROUP_NAME":"M_TORQUE_PFM1","STATION_NAME":"M_TORQUE_PFM1"},{"LINE_NAME":"H02","SECTION_NAME":"WHOLELINE","GROUP_NAME":"M_HANDINST_T","STATION_NAME":"M_HANDINST_T"},{"LINE_NAME":"H02","SECTION_NAME":"M_ASSY_SCREW","GROUP_NAME":"M_ASSY_SCREW","STATION_NAME":"M_ASSY_SCREW"},{"LINE_NAME":"H02","SECTION_NAME":"M_GF1500","GROUP_NAME":"M_GF1500","STATION_NAME":"M_GF1500"},{"LINE_NAME":"H02","SECTION_NAME":"M_INSP_A_COAT","GROUP_NAME":"M_INSP_A_COAT","STATION_NAME":"M_INSP_A_COAT"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC03"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC08"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC10"},{"LINE_NAME":"H02","SECTION_NAME":"M_TORQUE","GROUP_NAME":"M_TORQUE_PFM","STATION_NAME":"M_TORQUE_PFM"},{"LINE_NAME":"H02","SECTION_NAME":"M_INSPECT_B","GROUP_NAME":"M_INSPECT_B","STATION_NAME":"M_SOLDER_INSP_B"},{"LINE_NAME":"H02","SECTION_NAME":"M_INSPECT_T","GROUP_NAME":"M_INSPECT_T","STATION_NAME":"M_SOLDER_INSP_T"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC06"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC12"},{"LINE_NAME":"H02","SECTION_NAME":"M_TORQUE","GROUP_NAME":"M_TORQUE1","STATION_NAME":"M_TORQUE1"},{"LINE_NAME":"H02","SECTION_NAME":"M_AOIINSPECT","GROUP_NAME":"M_AOIINSPECT","STATION_NAME":"M_AOIINSPECT"},{"LINE_NAME":"H02","SECTION_NAME":"M_HANDINSERT","GROUP_NAME":"M_HANDINSERT","STATION_NAME":"M_HANDINSERT"},{"LINE_NAME":"H02","SECTION_NAME":"M_INSP_B_COAT","GROUP_NAME":"M_INSP_B_COAT","STATION_NAME":"M_INSP_B_COAT"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC18"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC20"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC21"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC22"},{"LINE_NAME":"H02","SECTION_NAME":"M_APPLYGLUE","GROUP_NAME":"M_APPLYGLUE","STATION_NAME":"M_APPLYGLUE"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_LOADPROG"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC02"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC13"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC14"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC23"},{"LINE_NAME":"H02","SECTION_NAME":"M_TEST_PWBA","GROUP_NAME":"M_LOADPROG","STATION_NAME":"M_PARAMETRIC24"},{"LINE_NAME":"H02","SECTION_NAME":"WHOLELINE","GROUP_NAME":"M_HANDINST_B","STATION_NAME":"M_HANDINST_B"}]}'
    elif data_type == 13:
        string = {
            "Result": "OK",
            "Message": [
                {
                    "SERIAL_NUMBER": "7499001404001",
                    "MO_NUMBER": "1991401988",
                    "MODEL_NAME": "5502749900",
                    "LINE_NAME": "S1"
                }
            ]
        }
    else:
        string = "OK"

    print(data_json.get("request_param"))

    print("-------------开始向客户端发消息")
    ret_data = {"result": True, "string": string}
    print(f"{ret_data=}")
    ret_data = json.dumps(ret_data, ensure_ascii=False).encode('utf8')
    print(",,,,,,,,,", ret_data)

    ret_len = struct.pack("!q", len(ret_data))

    ret_full_param = PACKETSTART + ret_len + PACKETTYPE + ret_data + PACKETEND

    conn.send(ret_full_param)
    print("发送完毕")

    conn.close()

# res = conn.recv(1024).decode('utf-8')
# print("recv:", res)

# sk.close()
# print("over")
