# # !/usr/bin/env python
# # -*-coding:utf-8 -*-
#
# """
# # File       : t3_img.py
# # Time       ：2025/3/24 下午4:10
# # Author     ：sch
# # version    ：python 3.8
# # Description：
# """
# import base64
# import hashlib
# import json
#
# import requests
#
# from common import xutil
# from common.xutil import log
#
#
# def generate_sign(app_id, app_key, json_body):
#     """生成签名"""
#
#     sign_str = f"{app_id}{json_body}{app_key}"
#
#     sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
#     return sign
#
#
# def xiaomi_request(app_id: str, app_key: str, url: str, json_body: dict, method: str, filepath=None) -> dict:
#     """
#     发送小米请求
#     """
#     json_body_str = json.dumps(json_body, indent=4, ensure_ascii=False)
#     log.info(f"请求URL：{url}   请求参数:\n{json_body_str}")
#
#     sign = generate_sign(app_id, app_key, json_body_str)
#     data = {
#         "header": {
#             "appid": app_id,
#             "sign": sign,
#             "method": method
#         },
#         "body": json_body_str
#     }
#     # 将数据转换为字符串并进行 Base64 编码
#     data_str = json.dumps(data)
#     data_base64 = base64.b64encode(data_str.encode('utf-8')).decode('utf-8')
#
#     # 构建表单数据
#     form_data = {
#         "data": data_base64,
#     }
#
#     if filepath:
#         files = {
#             "file": open(filepath, 'rb')
#         }
#     else:
#         files = None
#
#     # 发送 POST 请求
#     response = requests.post(url, data=form_data, files=files)
#
#     # 打印响应
#     log.info(f"接口返回参数：\n{response.text}")
#
#     return response.json()
#
#
# if __name__ == '__main__':
#     img_param = {
#         "bucket": "/home/<USER>/app",
#         "name": "test.jpg",
#         "uuid": xutil.OtherUtil.get_uuid4_str(),
#     }
#     print(json.dumps(img_param, indent=4, ensure_ascii=False))
#
#     api_url1 = "http://im.pre.mi.com/file/x5/file/upload"
#
#     xiaomi_request(
#         "Auto-Soft",
#         "5984710e-bb38-4806-b94d-7a9a727e3880",
#         api_url1,
#         img_param,
#         "Upload",
#         filepath="/home/<USER>/Pictures/COMP1435_1435.png"
#     )
