# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/6 上午11:35
# Author     ：sch
# version    ：python 3.8
# Description：海红
"""
import os
from typing import Any

from common import xutil, xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

board_template = """<dsWIPTD>
    <WIPTDHeader>
        <IntSerialNo>{sn}</IntSerialNo>
        <CommSN></CommSN>
        <Process>{process}</Process>
        <Result>{result}</Result>
        <TesterNo>{device_no}</TesterNo>
        <ProdLine>{line}</ProdLine>
        <ProgramName>{project_name}</ProgramName>
        <ProgramVersion></ProgramVersion>
        <IPSNo></IPSNo>
        <IPSVersion></IPSVersion>
        <OperatorName>{operator}</OperatorName>
        <Remark></Remark>
        <StartTime>{start_time}</StartTime>
        <EndTime>{end_time}</EndTime>
    </WIPTDHeader>
{ITEMLIST}
</dsWIPTD>
"""

comp_template = """    <WIPTDItem>
        <Item>{comp_desc}</Item>
        <TestStep></TestStep>
        <TestName>{test_name}</TestName>
        <OutputName></OutputName>
        <InputCondition></InputCondition>
        <OutputLoading></OutputLoading>
        <LowerLimit>{min_threshold}</LowerLimit>
        <Result>{result}</Result>
        <UpperLimit>{max_threshold}</UpperLimit>
        <Unit></Unit>
        <Status>{status}</Status>
        <IPSReference>{ips_reference}</IPSReference>
        <TestID></TestID>
        <StartTime>{start_time}</StartTime>
        <EndTime>{end_time}</EndTime>
    </WIPTDItem>
"""


# def x_request(api_url, param, service):
#     """
#     公共的请求方法
#     service: (SFC_GetLastTestResultNewPlatform,条码校验)  (SFC_WIPATEOutput,数据上传)
#     """
#     log.info(f"-->请求url： {api_url}  请求参数：{param}")
#     t1 = time.time()
#     imp = Import('http://www.w3.org/2001/XMLSchema', location='http://www.w3.org/2001/XMLSchema.xsd')
#     imp.filter.add("http://tempuri.org/")
#     doctor = ImportDoctor(imp)
#
#     client = Client(api_url, doctor=doctor)
#
#     if service == "SFC_GetLastTestResultNewPlatform":
#         ret = client.service.SFC_GetLastTestResultNewPlatform(param)
#     elif service == "SFC_WIPATEOutput":
#         ret = client.service.SFC_WIPATEOutput(param)
#     else:
#         raise Exception("不支持的service")
#
#     log.logger.info(f"x_request time cost: {int(time.time() - t1)} s")
#
#     log.logger.info(f">>>响应参数：{ret}")
#
#     return ret


def parse_check_sn_ret(ret):
    """
    解析条码过站的参数
    """
    root = xutil.XmlUtil.get_xml_root_by_str(ret)
    ret_data = root[0]

    status = ret_data.find("Status").text
    process = ret_data.find("Process").text
    model = ret_data.find("Model").text

    return status, process, model


class Engine(BaseEngine):
    version = {
        "title": "haihong release v1.0.0.6",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-06 11:37  init
date: 2023-11-08 11:00  增加校验Model
date: 2023-11-08 17:59  改变接口调用方式
date: 2023-11-10 14:29  修改上传数据
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "process_id": {
            "ui_name": "产品工序",
            "value": ""
        },
        "device_no": {
            "ui_name": "设备编号",
            "value": ""
        },
        "line_id": {
            "ui_name": "产线编号",
            "value": ""
        },
        "username": {
            "ui_name": "操作员",
            "value": ""
        },
    }

    path = {
        "txt_path": {
            "ui_name": "txt路径",
            "value": ""
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        process_id = other_vo.get_value_by_cons_key("process_id")
        txt_path = other_vo.get_value_by_cons_key("txt_path")
        # device_no = other_dao.get_value_by_cons_key("device_no")
        # line_id = other_dao.get_value_by_cons_key("line_id")
        # username = other_dao.get_value_by_cons_key("username")

        sn_list = other_vo.list_sn()

        if not txt_path:
            return self.x_response("false", f"请先选择txt路径！")

        txt_file = f"{txt_path}/model.txt"
        if not os.path.exists(txt_file):
            return self.x_response("false", f"{txt_file}文档不存在！")

        txt_content = xutil.FileUtil.read_file(txt_file)

        check_url = f"{api_url}//SFC_GetLastTestResultNewPlatform"

        for sn in sn_list:
            self.log.info(f"条码：{sn} 正在过站...")
            # ret = x_request(api_url, sn, "SFC_GetLastTestResultNewPlatform")

            ret = xrequest.RequestUtil.post_form(check_url, {"IntSerialNo": sn}, to_json=False)
            status, ret_process, model = parse_check_sn_ret(ret)

            if model not in txt_content:
                return self.x_response("false", f"返回的Model值:[{model}]在[{txt_file}]文档中不存在！")

            if status == "FAIL":
                return self.x_response("false", f"mes接口响应异常，条码校验失败！error: {ret}")

            if ret_process != process_id:
                return self.x_response("false", f"响应中的工序<{ret_process}>与当前设置的工序<{process_id}>不一致！")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        process_id = data_vo.get_value_by_cons_key("process_id")
        device_no = data_vo.get_value_by_cons_key("device_no")
        line_id = data_vo.get_value_by_cons_key("line_id")
        username = data_vo.get_value_by_cons_key("username")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_str = ""

            # if board_entity.repair_result:
            #     # 说明：PASS时 上传一条WIPTDItem项
            #     for comp_entity in board_entity.yield_comp_entity():
            #         alg_test_name = ""
            #         alg_max_val = ""
            #         alg_min_val = ""
            #         alg_result = ""
            #
            #         for alg_entity in comp_entity.yield_alg_entity():
            #             alg_test_name = alg_entity.test_name
            #             alg_max_val = alg_entity.max_threshold
            #             alg_min_val = alg_entity.min_threshold
            #             alg_result = alg_entity.result
            #             break
            #
            #         comp_data_str = comp_template.format(**{
            #             "comp_desc": comp_entity.designator,
            #             "test_name": alg_test_name,
            #             "min_threshold": alg_min_val,
            #             "max_threshold": alg_max_val,
            #             "result": alg_result,
            #             "status": "PASS" if comp_entity.repair_result else "FAIL",
            #             "ips_reference": alg_max_val,
            #             "start_time": start_time,
            #             "end_time": end_time,
            #         })
            # else:
            #     # 不良
            #     for comp_entity in board_entity.yield_comp_entity():
            #         if comp_entity.is_repair_ng():
            #             alg_test_name = ""
            #             alg_max_val = ""
            #             alg_min_val = ""
            #             alg_result = ""
            #
            #             for alg_entity in comp_entity.yield_alg_entity():
            #                 if alg_entity.result != "0":
            #                     pass
            #                     alg_test_name = alg_entity.test_name
            #                     alg_max_val = alg_entity.max_threshold
            #                     alg_min_val = alg_entity.min_threshold
            #                     alg_result = alg_entity.result
            #
            #             comp_data_str += comp_template.format(**{
            #                 "comp_desc": comp_entity.designator,
            #                 "test_name": alg_test_name,
            #                 "min_threshold": alg_min_val,
            #                 "max_threshold": alg_max_val,
            #                 "result": alg_result,
            #                 "status": "PASS" if comp_entity.repair_result else "FAIL",
            #                 "ips_reference": alg_max_val,
            #                 "start_time": start_time,
            #                 "end_time": end_time,
            #             })
            comp_data_str += comp_template.format(**{
                "comp_desc": board_no,
                "test_name": process_id,
                "min_threshold": "",
                "max_threshold": "",
                "result": board_entity.get_repair_result("0", "1"),
                "status": board_entity.get_repair_result("PASS", "FAIL"),
                "ips_reference": board_entity.get_repair_result("0", "1"),
                "start_time": start_time,
                "end_time": end_time,
            })

            board_param = board_template.format(**{
                "sn": barcode,
                "process": process_id,
                "result": board_entity.get_repair_result("PASS", "FAIL"),
                "device_no": device_no,
                "line": line_id,
                "project_name": pcb_entity.project_name,
                "operator": username,
                "start_time": start_time,
                "end_time": end_time,
                "ITEMLIST": comp_data_str.rstrip()
            })

            board_param = board_param.replace("<", "〈").replace(">", " 〉")

            # ret = x_request(api_url, board_param, "SFC_WIPATEOutput")

            data_url = f"{api_url}/SFC_WIPATEOutput"
            ret = xrequest.RequestUtil.post_form(data_url, {"TestData": board_param}, to_json=False)

            if ret != "PASS":
                return self.x_response("false", f"mes接口异常，测试数据上传失败，error: {ret}")

            return self.x_response()
