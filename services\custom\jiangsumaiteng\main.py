# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/23 下午2:12
# Author     ：sch
# version    ：python 3.8
# Description：江苏麦腾
"""
from typing import Any

from common import xrequest, xcons, xenum
from common.xutil import x_response
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

global_data = {}

txt_board_board_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
整板器件总数:{pcb_comp_number}
拼板序号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}
拼板器件数量:{board_comp_number}
拼板器件检测NG总数:{board_comp_robot_ng_number}
拼板器件复判NG总数:{board_comp_user_ng_number}
拼板器件误报总数:{board_comp_repass_number}

{CompData}
"""

txt_comp_board_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""


def x_refresh_token(api_url_login, user_id, password, dbaias):
    """
    刷新token
    :param api_url_login:
    :param user_id:
    :param password:
    :param dbaias:
    :return:
    """
    ret = xrequest.RequestUtil.post_json(api_url_login, {
        "userid": user_id,
        "password": password,
        "dbaias": dbaias,
        "errtype": "",
    })

    if str(ret.get("ErrCode")) != "0":
        return x_response("false", f"mes接口异常，获取Token失败，error：{ret.get('ErrMsg')}")

    token = ret.get('Data', {}).get('token')
    if not token:
        return x_response("false", f"mes接口异常，未获取到token！")

    global_data["token"] = token

    return None


class Engine(ErrorMapEngine):
    version = {
        "title": "jiangsumaiteng release v1.0.0.5",
        "device": "AIS303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-23 16:27  条码校验，上传数据
date: 2024-08-23 10:35  bugfix：修改请求参数
date: 2024-08-28 14:52  增加通过ftp上传图片、上传文件
date: 2024-09-06 17:13  需求变更
date: 2024-09-09 10:38  bugfix: 上传到ftp的文件，增加时间戳命名规则
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:5000/smtcore/api/WPReport/ReportAPI",
        },
        "api_url_login": {
            "ui_name": "接口URL(获取token)",
            "value": "http://127.0.0.1:5000/smtcore/api/Authentication/RequestToken",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:5000/smtcore/api/WPReport/ReportAPI",
        },
        "user_id": {
            "ui_name": "用户名",
            "value": "Admin",
        },
        "password": {
            "ui_name": "密码",
            "value": "[D1[1?[213rr]6rD+21rr221+?31D6~+",
        },
        "dbaias": {
            "ui_name": "dbaias",
            "value": "SMTDEV",
        },

        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
        "concat_prefix_path": {
            "ui_name": "拼接路径前缀(网络路径)",
            "ui_name_en": "ConcatPath",
            "value": "//127.0.0.1/AOI"
        },

    }

    form = {
        "order_id": {
            "ui_name": "工单",
            "value": "",
        },
        "process_id": {
            "ui_name": "工序",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备代码",
            "value": "",
        },
        "max_ng_count": {
            "ui_name": "NG图数量(超过不上传)",
            "value": "",
        },
    }

    combo = {
        "send_img_ui": {
            "ui_name": "整板图片是否上传",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "send_file_ui": {
            "ui_name": "测试结果文件是否上传",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "send_ng_comp_img": {
            "ui_name": "NG器件图片是否上传",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
    }

    button = {
        "get_token": {
            "ui_name": "获取Token",
            "value": "",
        },
    }

    def __init__(self):
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 6)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        order_id = other_vo.get_value_by_cons_key("order_id")
        process_id = other_vo.get_value_by_cons_key("process_id")
        device_code = other_vo.get_value_by_cons_key("device_code")

        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        dbaias = other_vo.get_value_by_cons_key("dbaias")

        token = global_data.get("token")
        if not token:
            x_res = x_refresh_token(api_url_login, user_id, password, dbaias)
            if x_res:
                return x_res

            token = global_data.get("token")
            if not token:
                return self.x_response("false", f"请先获取Token！")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", f"条码校验失败，未扫到条码！")

        err_msg = []
        for sn in sn_list:
            check_param = {
                "PlanNumber": order_id,
                "LabelCode": sn,
                "CheckOnly": "1",
                "WpNumber": process_id,
                "DevNumber": device_code,
                "programVer": other_vo.get_project_name(),
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param, headers=headers)
            if str(ret.get("ErrCode")) != "0":
                err_msg.append(ret.get('ErrMsg'))

        if err_msg:
            e1 = '\n'.join(err_msg)
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{e1}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        order_id = data_vo.get_value_by_cons_key("order_id")
        process_id = data_vo.get_value_by_cons_key("process_id")
        device_code = data_vo.get_value_by_cons_key("device_code")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        api_url_login = data_vo.get_value_by_cons_key("api_url_login")
        user_id = data_vo.get_value_by_cons_key("user_id")
        password = data_vo.get_value_by_cons_key("password")
        dbaias = data_vo.get_value_by_cons_key("dbaias")
        send_img_ui = data_vo.get_value_by_cons_key("send_img_ui")
        send_file_ui = data_vo.get_value_by_cons_key("send_file_ui")

        max_ng_count = data_vo.get_value_by_cons_key("max_ng_count")
        send_ng_comp_img = data_vo.get_value_by_cons_key("send_ng_comp_img")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        concat_prefix_path = data_vo.get_value_by_cons_key("concat_prefix_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        if not max_ng_count:
            max_ng_count = 999999
        else:
            try:
                max_ng_count = int(max_ng_count)
            except Exception as err:
                return self.x_response("false", f"不良图片数最大值必须为数字！err:{err}")

        token = global_data.get("token")
        if not token:
            x_res = x_refresh_token(api_url_login, user_id, password, dbaias)
            if x_res:
                return x_res

            token = global_data.get("token")
            if not token:
                return self.x_response("false", f"请先获取Token！")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        ret_res = self.x_response()

        # comp_number = 0
        # comp_user_ng_number = 0
        # comp_robot_ng_number = 0
        #
        # board_user_ng_count = 0
        # board_robot_ng_count = 0

        src_t_img = pcb_entity.get_pcb_t_image()

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = time_file[:8]

        is_upload_pcb_img = False
        project_name = pcb_entity.project_name
        ftp_client.cd_or_mkdir(f"{ftp_path}/{date_file}/{project_name}")

        pcb_sn = pcb_entity.pcb_barcode
        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode

            seq_ix = 0

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_ng_list = []

            file_list = []

            if send_img_ui == "Yes":
                # 上传到ftp服务器
                if not is_upload_pcb_img:
                    ftp_client.upload_file(src_t_img, f"{pcb_sn}_{time_file}.jpg")

                seq_ix += 1
                file_list.append({
                    "SEQ": str(seq_ix),
                    "FilePath": f"{concat_prefix_path}/{date_file}/{project_name}/{pcb_sn}_{time_file}.jpg",
                    "Remark": "整板图"
                })

            # comp_number += board_entity.comp_total_number
            # comp_user_ng_number += board_entity.comp_repair_ng_number
            # comp_robot_ng_number += board_entity.comp_robot_ng_number
            #
            # if board_entity.is_robot_ng():
            #     board_robot_ng_count += 1
            #
            # if board_entity.is_repair_ng():
            #     board_user_ng_count += 1

            comp_ix = 0

            comp_data_str = ""

            if board_entity.comp_robot_ng_number >= max_ng_count:
                self.log.warning(f"NG图片超出设定的最大数量，本次不上传NG图片到Mes")
                send_ng_comp_img = "No"

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                ftp_dst_img = ""
                if comp_entity.is_repair_ng():
                    comp_src_img = comp_entity.image_path

                    if send_ng_comp_img == "Yes":
                        if comp_src_img:
                            ftp_client.upload_file(comp_src_img, f"{barcode}_{comp_tag}_{time_file}.png")
                            # file_list.append({
                            #     "SEQ": str(seq_ix),
                            #     "FilePath": f"{concat_prefix_path}/{barcode}_{comp_tag}.png",
                            #     "Remark": "NG器件图"
                            # })

                            ftp_dst_img = f"{concat_prefix_path}/{date_file}/{project_name}/{barcode}_{comp_tag}_{time_file}.png"

                        else:
                            self.log.warning(f"{comp_tag} 未找到图片，无需上传")

                comp_ix += 1
                comp_ng_list.append({
                    "SEQ": str(comp_ix),
                    "BadNumber": comp_entity.robot_ng_code,
                    "BadPoint": comp_entity.designator,
                    "BadName": comp_entity.robot_ng_str,
                    "ImagePath": ftp_dst_img,
                    "PSBadNumber": comp_entity.repair_ng_code,
                    "BadQty": "1",
                    "Remark": ""
                })

                comp_data_str += txt_comp_board_template.format(**{
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data_content = txt_board_board_template.format(**{
                "device_name": device_code,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "CompData": comp_data_str
            })

            if send_file_ui == "Yes":
                ftp_client.upload_content(f"{barcode}_{time_file}.txt", board_data_content)

                seq_ix += 1

                file_list.append({
                    "SEQ": str(seq_ix),
                    "FilePath": f"{concat_prefix_path}/{date_file}/{project_name}/{barcode}_{time_file}.txt",
                    "Remark": "文件"
                })

            board_result = board_entity.get_repair_result("1", "0")

            if pcb_entity.is_repair_ng():
                self.log.warning(f"部分拼板NG，全部拼板都上传NG结果！")
                board_result = "0"

            data_param = {
                "PlanNumber": order_id,
                "LabelCode": barcode,
                "CheckOnly": "0",
                "WpNumber": process_id,
                "DevNumber": device_code,
                "programVer": pcb_entity.project_name,
                "Result": board_result,
                "Remark": "",
                "FileDetail": file_list,
                "InfoDetail": [],
                "BadDetail": comp_ng_list,
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param, headers=headers)
            if str(ret.get("ErrCode")) != "0":
                ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('ErrMsg')}")

        ftp_client.close()

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()

        if btn_key == "get_token":
            get_token_api_url = btn_vo.get_value_by_cons_key("api_url_login")
            user_id = btn_vo.get_value_by_cons_key("user_id")
            password = btn_vo.get_value_by_cons_key("password")
            dbaias = btn_vo.get_value_by_cons_key("dbaias")

            login_param = {
                "userid": user_id,
                "password": password,
                "dbaias": dbaias,
            }

            ret = xrequest.RequestUtil.post_json(get_token_api_url, login_param)
            if str(ret.get("ErrCode")) != "0":
                return self.x_response("false", f"mes接口异常，获取token失败")

            token = ret.get('Data', {}).get('token')
            if not token:
                return self.x_response("false", f"mes接口异常，未获取到token！")

            global_data["token"] = token

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        dbaias = other_vo.get_value_by_cons_key("dbaias")

        x_refresh_token(api_url_login, user_id, password, dbaias)
