# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/20 上午10:10
# Author     ：sch
# version    ：python 3.8
# Description：首航
"""
import json
import os
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import BaseEngine

global_data = {}


class Engine(BaseEngine):
    version = {
        "title": "shouhang release v1.0.0.4",
        "device": "203",
        "feature": ["条码校验", "上传数据", "上传设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-20 10:11  init
date: 2023-06-21 12:28  条码校验、上传数据、上传设备状态
date: 2023-08-18 15:47  更改接口URL配置
date: 2023-08-18 21:45  GET改为POST，Param参数改为Body参数
""", }

    form = {
        # "api_host": {
        #     "ui_name": "接口地址",
        #     "value": ""
        # },
        "username": {
            "ui_name": "账号",
            "value": ""
        },
        "password": {
            "ui_name": "密码",
            "value": ""
        },
        "order_no": {
            "ui_name": "工单号",
            "value": ""
        },
        "device_no": {
            "ui_name": "设备编号",
            "value": "DIP-AOI"
        },
        "work_step": {
            "ui_name": "作业规程",
            "value": ""
        },
        "machine_number": {
            "ui_name": "机台信息",
            "value": ""
        },
    }

    other_form = {
        # "get_token_url": {
        #     "ui_name": "获取token接口",
        #     "value": ""
        # },
        "check_url": {
            "ui_name": "规程校验接口",
            "value": ""
        },
        "upload_url": {
            "ui_name": "检测结果回传接口",
            "value": ""
        },
        "alarm_url": {
            "ui_name": "报警信息回传接口",
            "value": ""
        },
    }

    button = {
        # "get_token_btn": {
        #     "ui_name": "获取Token"
        # }
    }

    path = {
        "ng_path": {
            "ui_name": "NG器件图路径",
            "value": ""
        }
    }

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "get_token_btn":
            # api_host = btn_dao.get_value_by_cons_key("api_host")
            username = btn_vo.get_value_by_cons_key("username")
            password = btn_vo.get_value_by_cons_key("password")
            get_token_url = btn_vo.get_value_by_cons_key("get_token_url")

            # get_token_url = f"{api_host}/OrBitWCFServiceR16/OrBitWebAPI.ashx"
            token_param = {
                "UserName": username,
                "UserPassword": password,
            }

            self.log.info(f"开始获取token....")
            ret_token = xrequest.RequestUtil.get(get_token_url, token_param, to_json=False)
            if not ret_token:
                return self.x_response("false", f"mes接口异常，获取token失败，error：{ret_token}")

            global_data["token"] = ret_token
            self.log.info(f"获取token成功")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        list_sn = other_vo.list_sn()
        # api_host = other_dao.get_value_by_cons_key("api_host")
        order_no = other_vo.get_value_by_cons_key("order_no")
        device_no = other_vo.get_value_by_cons_key("device_no")
        work_step = other_vo.get_value_by_cons_key("work_step")
        check_url = other_vo.get_value_by_cons_key("check_url")

        if not list_sn:
            return self.x_response("false", f"设备未扫到条码！")

        check_barcode = list_sn[0]

        # token = global_data.get("token", "")
        # if not token:
        #     return self.x_response("false", f"未登录，请先登录！")

        data_param = {
            "MOName": order_no,
            "LotSN": check_barcode,
            "WorkFlowStep": work_step,
            "UserName": device_no
        }

        check_param = {
            "OutType": "JSON",
            "API": "CheckRouter",
            # "UserParameter": "",
            # "UserData": json.dumps(data_param, ensure_ascii=False),
            "UserData": data_param,
            "UserTicker": "",
        }
        ret_json = xrequest.RequestUtil.post_json(check_url, check_param)

        if not ret_json:
            return self.x_response("false", f"mes接口异常，条码校验失败！error_code:1")

        sql_data_set = ret_json.get("SQLDataSet", [])
        if not sql_data_set:
            return self.x_response("false", f"mes接口异常，条码校验失败！error_code:2")

        data1 = sql_data_set[0]

        if data1.get("Result") != "OK":
            return self.x_response("false", f"mes接口异常，条码校验失败！error：{ret_json.get('ErrorMSG')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # api_host = data_dao.get_value_by_cons_key("api_host")
        order_no = data_vo.get_value_by_cons_key("order_no")
        work_step = data_vo.get_value_by_cons_key("work_step")
        machine_number = data_vo.get_value_by_cons_key("machine_number")
        device_no = data_vo.get_value_by_cons_key("device_no")
        ng_path = data_vo.get_value_by_cons_key("ng_path")
        upload_url = data_vo.get_value_by_cons_key("upload_url")

        # upload_url = f"{api_host}/OrBitWCFServiceR16/OrBitWebAPI.ashx"

        # token = global_data.get("token", "")
        # if not token:
        #     return self.x_response("false", f"未登录，请先登录！")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # 缓存程序名
        global_data["project_name"] = pcb_entity.project_name

        time_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        time_date = time_now[:8]

        save_path = f"{ng_path}/{time_date}"

        if not os.path.exists(save_path):
            os.makedirs(save_path)

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    self.log.info(comp_entity)

                    comp_src_path = comp_entity.image_path
                    comp_tag = comp_entity.designator

                    if comp_src_path:
                        comp_dst_path = f"{save_path}/{barcode}_{comp_tag}_{time_now}.png"
                        xutil.FileUtil.copy_file(comp_src_path, comp_dst_path)
                    else:
                        comp_dst_path = ""

                    comp_data_list.append({
                        "Location": comp_tag,
                        "ErrorCode": comp_entity.repair_ng_code,
                        "ErrorMSG": comp_entity.repair_ng_str,
                        "FileInfo": comp_dst_path
                    })

            board_data_list.append({
                "LotSN": barcode,
                "Sequence": board_no,
                "Result": board_entity.get_repair_result("OK", "NG"),
                "LocationList": comp_data_list,
            })

        data_param = {
            "MOName": order_no,
            "BatchNO": pcb_entity.pcb_barcode,
            "WorkFlowStep": work_step,
            "Result": pcb_entity.get_repair_result("OK", "NG"),
            "MachineNumber": machine_number,
            "UserName": device_no,
            "CreateTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "ProgramName": pcb_entity.project_name,
            "DataList": board_data_list,
        }

        upload_param = {
            "OutType": "JSON",
            "API": "UploadTestResult",
            # "UserParameter": "",
            # "UserData": json.dumps(data_param, ensure_ascii=False),
            "UserData": data_param,
            "UserTicker": "",
        }

        ret_json = xrequest.RequestUtil.post_json(upload_url, upload_param)

        if not ret_json:
            return self.x_response("false", f"mes接口异常，上传数据失败！error_code:1")

        sql_data_set = ret_json.get("SQLDataSet", [])
        if not sql_data_set:
            return self.x_response("false", f"mes接口异常，上传数据失败！error_code:2")

        data1 = sql_data_set[0]

        if data1.get("Result") != "OK":
            return self.x_response("false", f"mes接口异常，上传数据失败！error：{ret_json.get('ErrorMSG')}")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        # api_host = other_dao.get_value_by_cons_key("api_host")
        machine_number = other_vo.get_value_by_cons_key("machine_number")
        alarm_url = other_vo.get_value_by_cons_key("alarm_url")

        status_str = other_vo.get_device_status_str()
        # alarm_url = f"{api_host}/OrBitWCFServiceR16/OrBitWebAPI.ashx"

        # token = global_data.get("token", "")
        # if not token:
        #     return self.x_response("false", f"未登录，请先登录！")

        if status_str == "开始检测":
            alarm_code = "01"
            alarm_msg = "开始"
        elif status_str == "停止检查":
            alarm_code = "04"
            alarm_msg = "停止"
        else:
            self.log.info(f"该状态暂不上传mes！")
            return self.x_response()

        data_param = {
            "MachineNumber": machine_number,
            "AlarmCode": alarm_code,
            "AlarmMSG": alarm_msg,
            "CreateTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "ProgramName": global_data.get("project_name", ""),
        }

        alarm_param = {
            "OutType": "JSON",
            "API": "UploadAlarmInfo",
            # "UserParameter": "",
            "UserData": data_param,
            # "UserData": json.dumps(data_param, ensure_ascii=False),
            "UserTicker": "",
        }

        ret_json = xrequest.RequestUtil.post_json(alarm_url, alarm_param)

        if not ret_json:
            return self.x_response("false", f"mes接口异常，上传设备状态失败！error_code:1")

        sql_data_set = ret_json.get("SQLDataSet", [])
        if not sql_data_set:
            return self.x_response("false", f"mes接口异常，上传设备状态失败！error_code:2")

        data1 = sql_data_set[0]

        if data1.get("Result") != "OK":
            return self.x_response("false", f"mes接口异常，上传设备状态失败！error：{ret_json.get('ErrorMSG')}")

        return self.x_response()
