import json
import os
import re
import subprocess
from flask import Flask, request, jsonify, Response
from log import output_log, get_cur_path

app = Flask(__name__)


@app.route('/', methods=['GET'])
def index():
    return "服务运行中……"


@app.route('/sn', methods=['GET', 'POST'])
def query_sn():
    output_log(f'===================  收到配置器查询SN请求  ===================')
    if request.method == 'GET':
        plate_sn = request.args.get('plate_sn')
        output_log(f'请求参数为:plate_sn={plate_sn}')
    else:
        request_data = request.get_json()
        output_log(f'请求参数为:{request_data}')
        plate_sn = request_data.get('plate_sn')

    if not plate_sn:
        response = {
            "code": -1,
            "message": "plate_sn为空，请传递正确的plate_sn!",
            "data": ""
        }
        output_log(f'返回响应：{response}')
        return jsonify(response)

    # 获取当前可执行文件的路径
    cur_path = get_cur_path()
    query_sn_exe_path = os.path.join(cur_path, "QuerySn", "QuerySn.exe")

    output_log("---------------- Start Call QuerySn.exe Query SN ----------------")
    # 调用 QuerySn.exe
    command = [query_sn_exe_path, f"-PlateSn={plate_sn}"]
    try:
        result = subprocess.run(
            command,
            capture_output=True,  # 捕获标准输出和标准错误
            text=True,  # 将输出解码为字符串
            check=False  # 如果返回码非零，抛出 CalledProcessError
        )

        # 提取标准输出，并写入日志
        output = result.stdout.strip()
        output_log(output, False)

        return_code = result.returncode
        board_sn4 = ''
        err_msg = result.stderr.strip()
        if return_code == 0:  # 正确获取到SN
            # 使用正则表达式匹配 {BoardSN4:...} 中的内容
            match = re.search(r'\{BoardSN4:(\S+)\}', output)
            if match:
                board_sn4 = match.group(1)  # 提取匹配到的 SN 值
            else:
                err_msg = "未找到{BoardSN4:...}标记，board_sn4未能正常解析"
                output_log(err_msg)
    except Exception as e:
        board_sn4 = ''
        err_msg = f"发送命令subprocess.run出错：{e}"
        output_log(err_msg)

    if board_sn4:
        response = {
            "code": 0,
            "message": "",
            "data": {"board_sn4": board_sn4}
        }
    else:
        # QuerySn.exe日志格式： 2025-04-11 11:16:18.835 ---Info--- Load RouteCommunication.dll error, GetLastError(): 126
        output_log(err_msg)
        # 提取实际错误信息返回给配置器
        start_index = err_msg.find("---Info---")
        if start_index != -1:
            start_index += len("---Info---")
            err_msg = err_msg[start_index:].strip()

        response = {
            "code": -1,
            "message": f"查询SN失败，失败信息：{err_msg}",
            "data": ""
        }
    output_log("------------------------- Exit QuerySn.exe -------------------------")
    if request.method == 'GET':
        return Response(
            json.dumps(response, ensure_ascii=False, indent=2),
            mimetype='application/json; charset=utf-8'
        )
    else:
        return jsonify(response)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9050)
