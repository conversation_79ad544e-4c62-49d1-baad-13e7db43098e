# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/5 上午9:10
# Author     ：sch
# version    ：python 3.8
# Description：无锡绿联
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "wuxilvlian release v1.0.0.1",
        "device": "203、303、401、630",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-05 19:50  init
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://*************:8312/api/Product/CheckSNStatus"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://*************:8312/api/Product/PassTerminalBySN"
        },
    }

    form = {
        "terminal_name": {
            "ui_name": "设备名称",
            "value": "AOI01",
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        terminal_name = other_vo.get_value_by_cons_key("terminal_name")

        sn_list = other_vo.list_sn()

        err_msg_list = []
        for ix, sn in enumerate(sn_list):
            check_param = {
                "SN": sn,
                "TerminalName": terminal_name
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

            if str(ret.get("Code")) != "200":
                err_msg_list.append(f"NO:{ix} SN:{sn} Error：{ret.get('Message')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)

            return self.x_response("false", f"MES响应异常，{err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        terminal_name = data_vo.get_value_by_cons_key("terminal_name")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            ng_point_list_repair = []
            ng_point_list_robot = []
            ok_point_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                if comp_entity.is_robot_ng():
                    ng_point_list_robot.append(f"{comp_tag}_{comp_entity.robot_ng_code}")

                if comp_entity.is_repair_ng():
                    ng_point_list_repair.append(f"{comp_tag}_{comp_entity.repair_ng_code}")
                else:
                    ok_point_list.append(comp_tag)

            data_param = {
                "SN": barcode,
                "TerminalName": terminal_name,
                "DefectData": ";".join(ng_point_list_robot),
                "TestResult": board_entity.get_robot_result("OK", "NG"),
                "SuccessData": ";".join(ok_point_list),
                "Manualresult": board_entity.get_repair_result("OK", "NG"),
                "ManualDate": ";".join(ng_point_list_repair)
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

            if str(ret.get("Code")) != "200":
                err_msg_list.append(f"NO:{board_no} SN:{barcode} Error：{ret.get('Message')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"MES响应异常，{err_str}")

        return self.x_response()
