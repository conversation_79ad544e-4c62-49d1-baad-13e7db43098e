# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/13 上午10:10
# Author     ：sch
# version    ：python 3.8
# Description：宝纳生
"""
import json
from typing import Any

from common import xutil, xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "baonasheng release v1.0.0.6",
        "device": "40x",
        "feature": ["上传数据", "从Mes获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-13 10:14  init
date: 2023-06-13 18:02  生成本地文档
date: 2023-06-19 17:56  直通的板卡保存一份数据到机器目录
date: 2023-09-09 12:19  不良类型区分细分项
date: 2023-11-09 15:10  文件命名规则加上时间
date: 2024-04-18 09:47  增加从MES获取条码的功能
date: 2024-05-21 14:24  需求变更
""", }

    path = {
        "data_path_robot": {
            "ui_name": "机器保存路径",
            "value": ""
        },
        "data_path_repair": {
            "ui_name": "复判保存路径",
            "value": ""
        },
    }

    other_form = {
        "username": {
            "ui_name": "用户名",
            "value": ""
        },
        "password": {
            "ui_name": "密码",
            "value": ""
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        # pcb_data = pcb_entity.standard_to_json()
        pcb_sn = pcb_entity.pcb_barcode

        # time_test = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        inspect_type = other_data.get("inspect_type")

        comp_pad_data, _ = pcb_entity.get_pad_test_data()
        # self.log.info(f"共有主pad点：{len(comp_pad_data)}")

        board_data = []
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0

        board_user_ng_count = 0
        board_robot_ng_count = 0

        datetime_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        if not pcb_sn:
            pcb_sn = datetime_now

        time_now = datetime_now[8:]

        self.log.info(self)
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # if not pcb_sn and board_entity.barcode:
            #     pcb_sn = board_entity.barcode

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode or barcode == pcb_sn:
                barcode = f"{pcb_sn}-{board_no.zfill(2)}"
                self.log.info(f"barcode had change: {barcode}")

            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number

            if board_entity.is_robot_ng():
                board_robot_ng_count += 1

            if board_entity.is_repair_ng():
                board_user_ng_count += 1

            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():
                robot_ng_code = comp_entity.robot_ng_code
                robot_ng_str = comp_entity.robot_ng_str
                repair_ng_code = comp_entity.repair_ng_code
                repair_ng_str = comp_entity.repair_ng_str

                if robot_ng_code == "34":
                    comp_id = comp_entity.comp_id
                    comp_uuid = comp_id.replace("{", "").replace("}", "")
                    pad_list = comp_pad_data.get(comp_uuid, [])
                    self.log.info(f"pad list: {pad_list}")

                    pad1 = pad_list[0]
                    pad_result = pad1.get("result")
                    custom_ng_str = xcons.AIS630_ERROR_MAP.get(pad_result, {}).get("standard", robot_ng_str)
                    robot_ng_code = pad_result
                    robot_ng_str = custom_ng_str

                    if comp_entity.is_repair_ng():
                        repair_ng_code = pad_result
                        repair_ng_str = custom_ng_str

                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": robot_ng_code,
                    "comp_robot_result": robot_ng_str,
                    "comp_user_code": repair_ng_code,
                    "comp_user_result": repair_ng_str,
                    "comp_image_path": comp_entity.image_path,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        pcb_data = {
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_user_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,
            "board_data": board_data
        }

        if pcb_entity.get_final_result() == "PASS":
            self.log.info("直通的板卡保存一份文件到机器目录...")
            data_path = data_vo.get_value_by_cons_key("data_path_robot")
            file_path = f"{data_path}/{pcb_sn}_{time_now}.json"
            xutil.FileUtil.dump_json_to_file(file_path, pcb_data)

        if inspect_type == "inspector":
            data_path = data_vo.get_value_by_cons_key("data_path_robot")
            file_path = f"{data_path}/{pcb_sn}_{time_now}.json"
            xutil.FileUtil.dump_json_to_file(file_path, pcb_data)
        else:

            data_path = data_vo.get_value_by_cons_key("data_path_repair")
            file_path = f"{data_path}/{pcb_sn}_{time_now}_R.json"
            xutil.FileUtil.dump_json_to_file(file_path, pcb_data)

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        username = other_vo.get_value_by_cons_key("username")
        password = other_vo.get_value_by_cons_key("password")
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")

        pcb_sn = other_vo.get_pcb_sn()

        userinfo_param = {
            "username": username,
            "Password": {
                "value": password,
                "isEncrypted": False
            },
            "utcoffset": "+08:00:00"
        }

        user_info_str = json.dumps(userinfo_param, indent=4, ensure_ascii=False)
        base64_bear = xutil.OtherUtil.str_to_base64(user_info_str)

        get_param = {
            "queryText": f"select ContainerLevelName, ContainerName, ES_PrimarySerialNumber, ES_SerialNumber2, ES_SerialNumber3, ES_PCBNumber, Qty from opxGetPanelSNInfo('{pcb_sn}') order by es_pcbnumber"
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {base64_bear}"
        }

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, get_param, headers=headers)

        if not ret:
            return self.x_response("false", f"mes接口异常，未获取到条码！")

        ret_list = []
        for item in ret:
            if item.get('ContainerLevelName') == 'PCB':
                ret_list.append(item.get('ContainerName', ''))

        return self.x_response('true', ','.join(ret_list))
