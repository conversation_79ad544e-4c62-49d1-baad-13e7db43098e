# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : cron_setting.py
# Time       ：2022/12/30 上午11:41
# Author     ：sch
# version    ：python 3.8
# Description：主界面--->定时清除
"""

from PyQt5.QtWidgets import *

from templates.cron_setting import Ui_CronSettingDialog


class CronSettingViews(QDialog, Ui_CronSettingDialog):
    def __init__(self, main_window):
        super(CronSettingViews, self).__init__()
        self.setupUi(self)

        self.main_window = main_window

    def save_app_setting_on_btn(self):
        """
        保存定时设置
        :return:
        """
        app_setting = self.main_window.config_data["app_setting"]

        app_setting["is_cron_clear"] = self.check_is_cron_clear.isChecked()
        app_setting["fixed_status_1"] = self.check_fixed_status_1.isChecked()
        app_setting["fixed_status_2"] = self.check_fixed_status_2.isChecked()

        time1 = self.edit_fixed_time_1.time()
        time2 = self.edit_fixed_time_2.time()
        app_setting["fixed_time_1"] = f"{time1.hour()}:{time1.minute()}"
        app_setting["fixed_time_2"] = f"{time2.hour()}:{time2.minute()}"

        app_setting["fixed_time_clear"] = self.radio_fixed_time_clear.isChecked()
        app_setting["interval_time_clear"] = self.radio_interval_time_clear.isChecked()
        app_setting["interval_time"] = self.spin_interval_time.value()

        self.main_window.save_config_data_to_file()

        self.main_window.log_info("保存定时设置成功！")
        self.main_window.log_info("定时设置将在重启后生效！")
