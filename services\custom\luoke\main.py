# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/2/22 上午9:52
# Author     ：sch
# version    ：python 3.8
# Description：洛克
"""

from typing import Any

from common import xsql
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

insert_board_template = """INSERT INTO boardData (board_sn, board_robot_result, board_user_result, board_final_result, pcb_comp_number, pcb_comp_robot_ng_number, pcb_comp_user_ng_number, pcb_comp_repass_number)  
OUTPUT INSERTED.id
VALUES {board_data};"""

insert_comp_template = """INSERT INTO compNgData (sid, board_sn, comp_designator, comp_part, comp_package, comp_type, comp_robot_result, comp_user_result, comp_final_result, comp_robot_code, comp_robot_name, comp_user_code, comp_user_name)  
VALUES {comp_data_str};"""


class Engine(ErrorMapEngine):
    version = {
        "title": "luoke release v1.0.0.3",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-02-22 11:11  写入数据到sqlserver
date: 2024-02-22 17:14  bugfix
""", }

    other_form = {
        "sql_server": {
            "ui_name": "数据库服务器IP地址",
            "value": "",
        },
        "sql_port": {
            "ui_name": "数据库端口",
            "value": "1433",
        },
        "sql_db": {
            "ui_name": "数据库名称",
            "value": "",
        },
        "sql_username": {
            "ui_name": "数据库账号",
            "value": "",
        },
        "sql_password": {
            "ui_name": "数据库密码",
            "value": "",
        },
        "sql_board_table": {
            "ui_name": "数据库拼板表",
            "value": "boardData",
        },
        "sql_comp_ng_table": {
            "ui_name": "数据库器件明细表",
            "value": "compNgData",
        },
    }

    button = {
        "test_connect": {
            "ui_name": "testConnectDb"
        }
    }

    password_style = ['sql_password']

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        sql_server = data_vo.get_value_by_cons_key('sql_server')
        sql_port = data_vo.get_value_by_cons_key('sql_port')
        sql_db = data_vo.get_value_by_cons_key('sql_db')
        sql_username = data_vo.get_value_by_cons_key('sql_username')
        sql_password = data_vo.get_value_by_cons_key('sql_password')
        sql_board_table = data_vo.get_value_by_cons_key('sql_board_table')
        sql_comp_ng_table = data_vo.get_value_by_cons_key('sql_comp_ng_table')

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        try:
            sql_port = int(sql_port)
        except Exception as err:
            return self.x_response("false", f"数据库端口[{sql_port}]必须为数字，error：{err}")

        if not sql_server:
            return self.x_response("false", f"数据库服务器IP地址不能为空！")
        if not sql_port:
            return self.x_response("false", f"数据库端口不能为空！")
        if not sql_db:
            return self.x_response("false", f"数据库名称不能为空！")
        if not sql_username:
            return self.x_response("false", f"数据库账号不能为空！")
        if not sql_password:
            return self.x_response("false", f"数据库密码不能为空！")
        if not sql_board_table:
            return self.x_response("false", f"数据库拼板表不能为空！")
        if not sql_comp_ng_table:
            return self.x_response("false", f"数据库器件明细表不能为空！")

        try:
            sql_conn = xsql.get_mssql_cursor(sql_server, sql_username, sql_password, sql_db, sql_port)
        except Exception as err:
            return self.x_response("false", f"连接数据库失败，error：{err}")

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            board_data = (
                barcode,
                board_entity.get_robot_result("PASS", "NG"),
                board_entity.get_robot_result("PASS", "NG"),
                board_entity.get_final_result("PASS", "REPASS", "NG"),
                board_entity.comp_total_number,
                board_entity.comp_robot_ng_number,
                board_entity.comp_repair_ng_number,
                board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number
            )

            insert_sql1 = insert_board_template.format(board_data=board_data)

            try:
                ret_id = xsql.insert_row_into_table_and_return_primary_id(sql_conn, insert_sql1)
                self.log.info(f"board primary id: {ret_id}")
            except Exception as err:
                ret_res = self.x_response("false", f"写入数据到board表失败，error：{err}")
                ret_id = 0

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_data_list.append(str((
                        ret_id,
                        barcode,
                        comp_entity.designator,
                        comp_entity.part,
                        comp_entity.package,
                        comp_entity.type,
                        comp_entity.get_final_result("PASS", "NG", "NG"),
                        comp_entity.get_final_result("PASS", "PASS", "NG"),
                        comp_entity.get_final_result("PASS", "REPASS", "NG"),
                        comp_entity.robot_ng_code,
                        comp_entity.robot_ng_str,
                        comp_entity.repair_ng_code,
                        comp_entity.repair_ng_str
                    )))

            if comp_data_list:
                self.log.info(f"insert comp data number: {len(comp_data_list)}")
                comp_data_str = ",\n".join(comp_data_list)
                insert_sql2 = insert_comp_template.format(comp_data_str=comp_data_str)
                xsql.insert_row_into_table(sql_conn, insert_sql2)

            self.log.info(f"拼板[{board_entity.board_no}]数据写入成功！")

        sql_conn.close()

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        sql_server = btn_vo.get_value_by_cons_key('sql_server')
        sql_port = btn_vo.get_value_by_cons_key('sql_port')
        sql_db = btn_vo.get_value_by_cons_key('sql_db')
        sql_username = btn_vo.get_value_by_cons_key('sql_username')
        sql_password = btn_vo.get_value_by_cons_key('sql_password')
        sql_board_table = btn_vo.get_value_by_cons_key('sql_board_table')
        sql_comp_ng_table = btn_vo.get_value_by_cons_key('sql_comp_ng_table')

        try:
            sql_port = int(sql_port)
        except Exception as err:
            return self.x_response("false", f"数据库端口[{sql_port}]必须为数字，error：{err}")

        if not sql_server:
            return self.x_response("false", f"数据库服务器IP地址不能为空！")
        if not sql_port:
            return self.x_response("false", f"数据库端口不能为空！")
        if not sql_db:
            return self.x_response("false", f"数据库名称不能为空！")
        if not sql_username:
            return self.x_response("false", f"数据库账号不能为空！")
        if not sql_password:
            return self.x_response("false", f"数据库密码不能为空！")
        if not sql_board_table:
            return self.x_response("false", f"数据库拼板表不能为空！")
        if not sql_comp_ng_table:
            return self.x_response("false", f"数据库器件明细表不能为空！")

        try:
            sql_conn = xsql.get_mssql_cursor(sql_server, sql_username, sql_password, sql_db, sql_port)
        except Exception as err:
            return self.x_response("false", f"连接数据库失败，error：{err}")

        sql_conn.close()

        return self.x_response()
