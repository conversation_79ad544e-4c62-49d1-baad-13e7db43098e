# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/22 下午2:47
# Author     ：sch
# version    ：python 3.8
# Description：恒润
"""

from typing import Any

from common import xrequest, xutil
from common.xutil import log
from vo.mes_vo import DataVo, ComboVo, ButtonVo, OtherVo
from engine.MesEngine import ErrorMapEngine


def sort_key(item):
    """
    定义一个排序函数，用于从字典的 key 中提取最后一个分隔符后的数字进行排序
    :param item:
    :return:
    """
    return item[0].rsplit('|', 1)[1]


def parse_menu_data(menu_data: dict):
    """
    解析 返回的工单列表
    :return:
    """
    name_id_map = {}
    tree_map = {}

    for _, pd_lines in menu_data.items():
        for pd_line in pd_lines:
            pd_line_id = pd_line.get("id")
            pd_line_name = pd_line.get("name")
            pd_list = pd_line.get("pdList")
            name_id_map[pd_line_name] = pd_line_id

            tree_map[pd_line_name] = {}

            for pd in pd_list:
                pd_id = pd.get("id")
                pd_name = pd.get("name")
                pd_work_list = pd.get("workOrderList")
                name_id_map[pd_name] = pd_id

                tree_map[pd_line_name][pd_name] = {}

                for work_order in pd_work_list:
                    work_name = work_order.get("name")
                    work_id = work_order.get("id")
                    name_id_map[work_name] = work_id

                    tree_map[pd_line_name][pd_name][work_name] = {}

    sorted_order_map = {}
    for k, v in tree_map.items():
        new_v = dict(sorted(v.items(), key=sort_key))
        sorted_order_map[k] = new_v

    log.info(f"工单已排序！")

    return name_id_map, sorted_order_map


global_data = {}


class Engine(ErrorMapEngine):
    version = {
        "title": "hengrun release v1.0.0.2",
        "device": "xx",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-23 14:47  条码校验，上传数据  (脚本改纯Python配置器 mesconfig_release-->mesconfig_python)
date: 2024-07-31 15:41  bugfix:条码校验时，打印了错误的报错信息
""", }

    other_form = {
        "device_ip": {
            "ui_name": "设备IP",
            "value": "",
        },
        "api_url_hr": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081",
        }
    }

    form = {
        "username": {
            "ui_name": "用户名",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
    }

    button = {
        "refresh_order_list": {
            "ui_name": "刷新工单列表",
        },
        "login_user": {
            "ui_name": "登录",
        },
    }

    combo = {
        "upload_ng_data": {
            "ui_name": "上传NG数据",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "pd_line_list": {
            "ui_name": "产品线",
            "item": [],
            "value": "",
        },
        "pd_list": {
            "ui_name": "产品",
            "item": [],
            "value": "",
        },
        "work_order_list": {
            "ui_name": "工单",
            "item": [],
            "value": "",
        },
        "process_list": {
            "ui_name": "工序",
            "item": [],
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        process_list_select = other_vo.get_value_by_cons_key("process_list")

        api_url_hr = other_vo.get_value_by_cons_key("api_url_hr")

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"未登录，请先登录")

        name_id_map = xutil.CacheUtil.get("name_id_map", {})
        process_id = name_id_map.get(process_list_select)

        # 1. 设置工序
        set_process_url = f"{api_url_hr}/mes/service/auto/configProcess"
        log.info(f"开始设置工序...")

        headers = {
            "iovtoken": token
        }
        params = {
            "processId": process_id,
        }

        ret = xrequest.RequestUtil.post_json(set_process_url, params, headers=headers)
        if ret.get("status") != "PASS":
            return self.x_response("false", f"接口响应异常，设置工序失败，error：{ret.get('message')}")

        process_key = ret.get("content", {}).get("processKey")
        global_data["process_key"] = process_key
        log.info(f"设置工序成功")

        # 2. 获取生产信息
        log.info(f"开始获取生产信息...")

        get_info_url = f"{api_url_hr}/mes/service/auto/queryProductionInfo"
        param2 = {
            "processKey": process_key,
        }
        xrequest.RequestUtil.post_json(get_info_url, param2, headers=headers)
        # 仅仅调用，不做其他特殊处理
        log.info(f"获取生产信息成功")

        # 3. 条码校验
        log.info(f"开始条码校验...")
        check_url = f"{api_url_hr}/mes/service/auto/validateNumber"

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            param3 = {
                "number": sn,
                "processKey": process_key
            }
            ret3 = xrequest.RequestUtil.post_json(check_url, param3, headers=headers)
            if ret3.get("status") != "PASS":
                ret_res = self.x_response("false", f"接口响应异常，条码校验失败，error: {ret3.get('message')}")

        log.info(f"工序过站结束")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_hr = data_vo.get_value_by_cons_key("api_url_hr")
        upload_ng_data = data_vo.get_value_by_cons_key("upload_ng_data")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        token = global_data.get("token")
        process_key = global_data.get("process_key")

        if not token:
            return self.x_response("false", f"未登录，请先登录")

        log.info(f"工序过站开始...")

        save_url = f"{api_url_hr}/mes/service/auto/completeTask"
        headers = {"iovtoken": token}

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            ng_pos = []
            ng_code = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    # 复判NG器件
                    ng_pos.append(comp_entity.designator)
                    ng_code.append(comp_entity.repair_ng_code)

            param = {
                "number": board_entity.barcode,
                "processKey": process_key,
                # "result": "1",  # 有可能参数名是这个
                "resultMainId": "1",
                "isSuccess": board_entity.get_repair_result("1", "0"),
                "errorCode": ",".join(ng_code),
                "errorInfo": ",".join(ng_pos),
                "bindMaterial": False,
            }

            if upload_ng_data == "Yes" or board_entity.repair_result:
                # 默认只传复判OK，机器测试OK，复判NG做成可配置是否上传
                ret = xrequest.RequestUtil.post_json(save_url, param, headers=headers)
                if ret.get("status") != "PASS":
                    ret_res = self.x_response("false", f"接口响应异常，工序过站失败，error：{ret.get('message')}")

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, main_window: Any):
        api_url_hr = btn_vo.get_value_by_cons_key("api_url_hr")
        device_ip = btn_vo.get_value_by_cons_key("device_ip")

        btn_key = btn_vo.get_btn_key()
        if btn_key == "refresh_order_list":
            get_order_url = f"{api_url_hr}/mes/service/auto/authToken/queryWorkOrderInfo"

            param = {
                # "deviceIp": device_ip
            }

            ret = xrequest.RequestUtil.post_json(get_order_url, param)

            if ret.get("status") != "PASS":
                return self.x_response("false", f"界面更新失败，请检查获取工单接口，error: {ret.get('message')}")

            ret_content = ret.get("content")
            name_id_map, pd_line_map = parse_menu_data(ret_content)

            cache_data = xutil.CacheUtil.get_cache_data()
            cache_data["pd_line_map"] = pd_line_map
            cache_data["name_id_map"] = name_id_map
            xutil.CacheUtil.save_cache_data(cache_data)

            item1 = getattr(main_window, "combo_pd_line_list")
            item2 = getattr(main_window, "combo_pd_list")
            item3 = getattr(main_window, "combo_work_order_list")
            item4 = getattr(main_window, "combo_process_list")

            item1.clear()
            item2.clear()
            item3.clear()
            item4.clear()

            pd_line_list = list(pd_line_map.keys())

            if pd_line_list:
                for i in pd_line_list:
                    item1.addItem(i)

                first_pd_line = pd_line_list[0]
            else:
                first_pd_line = ""

            item1.setCurrentText(first_pd_line)

            pd_list = list(pd_line_map.get(first_pd_line, {}).keys())
            if pd_list:
                for i in pd_list:
                    item2.addItem(i)

                first_pd = pd_list[0]
            else:
                first_pd = ""

            item2.setCurrentText(first_pd)

            work_order_list = list(pd_line_map.get(first_pd_line, {}).get(first_pd, {}).keys())
            if work_order_list:
                for i in work_order_list:
                    item3.addItem(i)

                first_work_order = work_order_list[0]
            else:
                first_work_order = ""

            item3.setCurrentText(first_work_order)

            # 去获取工序
            get_process_url = f"{api_url_hr}/mes/service/auto/authToken/queryDeviceProcessInfo"
            param = {
                "deviceIp": device_ip,
                "workOrderId": name_id_map.get(first_work_order)
            }
            ret = xrequest.RequestUtil.post_json(get_process_url, param)
            if ret.get("status") != "PASS":
                return self.x_response("false", f"接口响应异常，获取工序失败，error: {ret.get('message')}")

            ret_content = ret.get("content")

            cache_data = xutil.CacheUtil.get_cache_data()
            name_id_map = cache_data.get("name_id_map", {})

            process_list = []
            for process in ret_content.get("processList"):
                process_id = process.get("processId")
                process_name = process.get("processName")
                name_id_map[process_name] = process_id
                process_list.append(process_name)

            cache_data["name_id_map"] = name_id_map
            xutil.CacheUtil.save_cache_data(cache_data)

            if process_list:
                for i in process_list:
                    item4.addItem(i)

                the_first_process = process_list[0]
            else:
                the_first_process = ""

            item4.setCurrentText(the_first_process)

            main_window.config_data['combo']['pd_line_list']['item'] = pd_line_list
            main_window.config_data['combo']['pd_line_list']['value'] = first_pd_line

            main_window.config_data['combo']['pd_list']['item'] = pd_list
            main_window.config_data['combo']['pd_list']['value'] = first_pd

            main_window.config_data['combo']['work_order_list']['item'] = work_order_list
            main_window.config_data['combo']['work_order_list']['value'] = first_work_order

            main_window.config_data['combo']['process_list']['item'] = process_list
            main_window.config_data['combo']['process_list']['value'] = the_first_process

            main_window.save_config_data_to_file()

        elif btn_key == "login_user":
            username = btn_vo.get_value_by_cons_key("username")
            password = btn_vo.get_value_by_cons_key("password")

            work_order_select = btn_vo.get_value_by_cons_key("work_order_list")
            process_list_select = btn_vo.get_value_by_cons_key("process_list")

            name_id_map = xutil.CacheUtil.get("name_id_map", {})

            if not name_id_map:
                return self.x_response("false", f"请先刷新工单，再登录！")

            work_order_id = name_id_map.get(work_order_select)
            process_id = name_id_map.get(process_list_select)

            login_url = f"{api_url_hr}/mes/service/auto/authToken/userInfoAuth"

            param = {
                "username": username,
                "password": password,
                "workOrderId": work_order_id,
                "processId": process_id,
                "deviceIp": device_ip,
                "type": "device"
            }
            ret = xrequest.RequestUtil.post_json(login_url, param)

            err_msg = ret.get('message')

            if ret.get("status") != "PASS":
                return self.x_response("false", f"接口响应异常，登录失败，error：{err_msg}")

            token = ret.get("content").get("IOVTOKEN")
            if not token:
                return self.x_response("false", f"接口响应异常，登录失败，error：{ret}")

            if err_msg != "用户信息认证成功":
                return self.x_response("false", f"接口响应异常，登录失败，error：{ret}")

            global_data["token"] = token

        return self.x_response()

    def combo_index_changed(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()
        combo_value = combo_vo.get_combo_value()

        api_url_hr = combo_vo.get_value_by_cons_key("api_url_hr")
        pd_line_select = combo_vo.get_value_by_cons_key("pd_line_list")
        device_ip = combo_vo.get_value_by_cons_key("device_ip")
        pd_list_select = combo_vo.get_value_by_cons_key("pd_list")
        work_order_select = combo_vo.get_value_by_cons_key("work_order_list")

        log.info(f"{combo_key=}, {combo_value=}")
        # log.info(f"{pd_line_select=}")

        if combo_key == "pd_line_list":
            # 需要刷新：产品-工单-工序
            item2 = getattr(main_window, "combo_pd_list")
            item3 = getattr(main_window, "combo_work_order_list")
            item4 = getattr(main_window, "combo_process_list")

            item2.clear()
            item3.clear()
            item4.clear()

            pd_line_map = xutil.CacheUtil.get("pd_line_map", {})
            name_id_map = xutil.CacheUtil.get("name_id_map", {})

            pd_list = list(pd_line_map.get(pd_line_select, {}).keys())
            if pd_list:
                for i in pd_list:
                    item2.addItem(i)

                first_pd = pd_list[0]
            else:
                first_pd = ""

            item2.setCurrentText(first_pd)

            work_order_list = list(pd_line_map.get(pd_line_select, {}).get(first_pd, {}).keys())
            if work_order_list:
                for i in work_order_list:
                    item3.addItem(i)

                first_work_order = work_order_list[0]
            else:
                first_work_order = ""

            item3.setCurrentText(first_work_order)

            # 去获取工序
            get_process_url = f"{api_url_hr}/mes/service/auto/authToken/queryDeviceProcessInfo"
            param = {
                "deviceIp": device_ip,
                "workOrderId": name_id_map.get(first_work_order)
            }
            ret = xrequest.RequestUtil.post_json(get_process_url, param)
            if ret.get("status") != "PASS":
                return self.x_response("false", f"接口响应异常，获取工序失败，error: {ret.get('message')}")

            ret_content = ret.get("content")

            cache_data = xutil.CacheUtil.get_cache_data()
            name_id_map = cache_data.get("name_id_map", {})

            process_list = []
            for process in ret_content.get("processList"):
                process_id = process.get("processId")
                process_name = process.get("processName")
                name_id_map[process_name] = process_id
                process_list.append(process_name)

            cache_data["name_id_map"] = name_id_map
            xutil.CacheUtil.save_cache_data(cache_data)

            if process_list:
                for i in process_list:
                    item4.addItem(i)

                the_first_process = process_list[0]
            else:
                the_first_process = ""

            item4.setCurrentText(the_first_process)

            main_window.config_data['combo']['pd_list']['item'] = pd_list
            main_window.config_data['combo']['pd_list']['value'] = first_pd

            main_window.config_data['combo']['work_order_list']['item'] = work_order_list
            main_window.config_data['combo']['work_order_list']['value'] = first_work_order

            main_window.config_data['combo']['process_list']['item'] = process_list
            main_window.config_data['combo']['process_list']['value'] = the_first_process

            main_window.save_config_data_to_file()

        elif combo_key == "pd_list":
            item3 = getattr(main_window, "combo_work_order_list")
            item4 = getattr(main_window, "combo_process_list")

            item3.clear()
            item4.clear()

            pd_line_map = xutil.CacheUtil.get("pd_line_map", {})
            name_id_map = xutil.CacheUtil.get("name_id_map", {})

            work_order_list = list(pd_line_map.get(pd_line_select, {}).get(pd_list_select, {}).keys())
            if work_order_list:
                for i in work_order_list:
                    item3.addItem(i)

                first_work_order = work_order_list[0]
            else:
                first_work_order = ""

            item3.setCurrentText(first_work_order)

            # 去获取工序
            get_process_url = f"{api_url_hr}/mes/service/auto/authToken/queryDeviceProcessInfo"
            param = {
                "deviceIp": device_ip,
                "workOrderId": name_id_map.get(first_work_order)
            }
            ret = xrequest.RequestUtil.post_json(get_process_url, param)
            if ret.get("status") != "PASS":
                return self.x_response("false", f"接口响应异常，获取工序失败，error: {ret.get('message')}")

            ret_content = ret.get("content")

            cache_data = xutil.CacheUtil.get_cache_data()
            name_id_map = cache_data.get("name_id_map", {})

            process_list = []
            for process in ret_content.get("processList"):
                process_id = process.get("processId")
                process_name = process.get("processName")
                name_id_map[process_name] = process_id
                process_list.append(process_name)

            cache_data["name_id_map"] = name_id_map
            xutil.CacheUtil.save_cache_data(cache_data)

            if process_list:
                for i in process_list:
                    item4.addItem(i)

                the_first_process = process_list[0]
            else:
                the_first_process = ""

            item4.setCurrentText(the_first_process)

            main_window.config_data['combo']['work_order_list']['item'] = work_order_list
            main_window.config_data['combo']['work_order_list']['value'] = first_work_order

            main_window.config_data['combo']['process_list']['item'] = process_list
            main_window.config_data['combo']['process_list']['value'] = the_first_process

            main_window.save_config_data_to_file()

        elif combo_key == "work_order_list":
            item4 = getattr(main_window, "combo_process_list")
            item4.clear()

            name_id_map = xutil.CacheUtil.get("name_id_map", {})

            # 去获取工序
            get_process_url = f"{api_url_hr}/mes/service/auto/authToken/queryDeviceProcessInfo"
            param = {
                "deviceIp": device_ip,
                "workOrderId": name_id_map.get(work_order_select)
            }
            ret = xrequest.RequestUtil.post_json(get_process_url, param)
            if ret.get("status") != "PASS":
                return self.x_response("false", f"接口响应异常，获取工序失败，error: {ret.get('message')}")

            ret_content = ret.get("content")

            cache_data = xutil.CacheUtil.get_cache_data()
            name_id_map = cache_data.get("name_id_map", {})

            process_list = []
            for process in ret_content.get("processList"):
                process_id = process.get("processId")
                process_name = process.get("processName")
                name_id_map[process_name] = process_id
                process_list.append(process_name)

            cache_data["name_id_map"] = name_id_map
            xutil.CacheUtil.save_cache_data(cache_data)

            if process_list:
                for i in process_list:
                    item4.addItem(i)

                the_first_process = process_list[0]
            else:
                the_first_process = ""

            item4.setCurrentText(the_first_process)

            main_window.config_data['combo']['process_list']['item'] = process_list
            main_window.config_data['combo']['process_list']['value'] = the_first_process

            main_window.save_config_data_to_file()

        return self.x_response()
