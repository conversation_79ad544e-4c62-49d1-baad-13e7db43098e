# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/3 下午4:14
# Author     ：sch
# version    ：python 3.8
# Description：斯菲尔
"""
import json
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "sifeier release v1.0.0.3",
        "device": "303,401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-03 16:14  init
date: 2023-11-21 15:12  检测完发送数据到mes
date: 2023-11-23 14:28  post改成get方式
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "username": {
            "ui_name": "UserName",
            "value": "admin"
        },
        "api_url2": {
            "ui_name": "检测完发送接口URL",
            "value": "http://**********:8081/SFERE/SN_COLLECT"
        },
        "station_name": {
            "ui_name": "站点名",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        username = data_vo.get_value_by_cons_key("username")
        api_url2 = data_vo.get_value_by_cons_key("api_url2")
        station_name = data_vo.get_value_by_cons_key("station_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        inspect_type = other_data.get("inspect_type")
        self.log.info(f"{inspect_type=}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data_list = []
            ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_data_list.append(comp_entity.designator)
                    ng_list.append({
                        "DEFCODE": comp_entity.repair_ng_code,
                        "DEFNAME": comp_entity.repair_ng_str,
                    })

            board_param = {
                "username": username,
                "prosn": barcode,
                "jg": board_entity.get_repair_result("$OK$", "$NOK$"),
                "wz": ",".join(comp_data_list),
                "gz_list": ng_list
            }

            if inspect_type != "inspector":
                ret = xrequest.RequestUtil.get(api_url,
                                               params={"str": json.dumps(
                                                   board_param,
                                                   ensure_ascii=False)},
                                               to_json=False)

        # 检测完发送
        if inspect_type == "inspector":
            xrequest.RequestUtil.get(api_url2, params={
                "station": station_name,
                "username": username,
                "sn": pcb_sn,
            }, to_json=False)

        return self.x_response()
