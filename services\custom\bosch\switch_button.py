# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : switch_button.py
# Time       ：2025/06/06 16:38
# Author     ：chencb
# version    ：python 3.8
# Description：bosch MES服务开关按钮
"""
from PyQt5.QtWidgets import QPushButton, QStyle, QStyleOptionButton
from PyQt5.QtGui import QPainter, QColor, QPen, QFontMetrics
from PyQt5.QtCore import Qt, QSize

# 红绿圆形标记的左右padding
CIRCLE_PADDING = 5
# 圆的直径
CIRCLE_DIAMETER = 18
# 文本的左右padding
TEXT_PADDING = 5
# 按钮高度的上下padding
HEIGHT_PADDING = 6

# mes服务开关
MES_ON = "MES服务【开启中】"
MES_OFF = "MES服务【关闭中】"


class SwitchButton(QPushButton):
    def __init__(self, parent=None):
        super().__init__("", parent)
        # 设置为Check后，会有两种状态，通过这两种状态来标记开关：checked（选中，MES关）和 unchecked（未选中，MES开）
        self.setCheckable(True)
        self.clicked.connect(self.update)

    def paintEvent(self, event):
        # 按钮绘制系统默认的样式
        opt = QStyleOptionButton()
        self.initStyleOption(opt)
        painter = QPainter(self)
        self.style().drawControl(QStyle.CE_PushButton, opt, painter, self)

        if self.is_mes_off():
            color = QColor(Qt.red)
            text = MES_OFF
        else:
            color = QColor(Qt.green)
            text = MES_ON

        # 绘制开关标记
        painter.setPen(QPen(color, 2))
        painter.setBrush(color)
        painter.drawEllipse(CIRCLE_PADDING, (self.height() - CIRCLE_DIAMETER) / 2, CIRCLE_DIAMETER, CIRCLE_DIAMETER)

        # 绘制文本
        font_metrics = QFontMetrics(self.font())
        text_height = font_metrics.height()
        x = CIRCLE_PADDING * 2 + CIRCLE_DIAMETER + TEXT_PADDING
        y = (self.height() + text_height) / 2 - font_metrics.descent()

        painter.setPen(QColor(Qt.black))
        painter.drawText(x, y, text)

    def sizeHint(self):
        # 计算按钮的size
        text = MES_OFF if self.is_mes_off() else MES_ON
        font_metrics = QFontMetrics(self.font())
        text_width = font_metrics.width(text)
        base_width = CIRCLE_PADDING * 2 + CIRCLE_DIAMETER + TEXT_PADDING * 2 + text_width
        base_height = font_metrics.height() + HEIGHT_PADDING * 2
        return QSize(base_width, base_height)

    def is_mes_off(self):
        # 选中为MES关，未选中为MES开
        return True if self.isChecked() else False
