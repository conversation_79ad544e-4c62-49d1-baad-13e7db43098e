# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : package_mesconfig.py
# Time       ：2023/12/13 上午9:19
# Author     ：sch
# version    ：python 3.8
# Description：打包脚本
"""
import datetime
import json
import os
import shutil
import subprocess
import time
import traceback

from git import Repo
from webdav3.client import Client

from common import xcustomer
from common.xutil import Logger
from services.route import engine

p = os.getcwd()
l_path = f"{p}/package_log.txt"

package_log = Logger(l_path)


def get_ubuntu_major_version():
    major_version = ''
    try:
        with open('/etc/os-release', 'r') as f:
            lines = f.readlines()
            for line in lines:
                if line.startswith('VERSION='):
                    print(f"ubuntu版本信息：{line.strip()}")
                    # VERSION="18.04.6 LTS (Bionic Beaver)"
                    version_part = line.split('"')[1]
                    major_version = version_part.split('.')[0]
    except Exception as e:
        print(f"无法查找到ubuntu版本信息，error: {e}")
    return major_version


def upload_pkg_to_jianguoyun(pkg_path: str, customer_info_list_: list):
    print(f"开始上传包到坚果云……")

    # 先生成上传根目录
    # pkg_path：'/xxx/mesconfig_python/dist/mesconfig_changshajianke_v3.0.0.1_20250422104557_u18.zip'
    zip_name = os.path.basename(pkg_path)
    filename = os.path.splitext(zip_name)[0]
    parts = filename.rsplit('_', 1)
    ubuntu_ver = parts[1]
    if ubuntu_ver == 'u22':
        upload_dir = 'MES配置器存档(新)/mesconfig_python22.0.4'
    else:
        upload_dir = 'MES配置器存档(新)/mesconfig_python'

    options = {
        'webdav_hostname': "https://drive.cvte.com/dav/",
        'webdav_login': "<EMAIL>",
        'webdav_password': "a2xga4skezsr4qpp",
        'webdav_root': upload_dir,
        'disable_check': True  # 关闭初始化预检查，否则会上传不成功
    }

    try:
        client = Client(options)

        if customer_info_list_:
            customer_dir = customer_info_list_[0]
        else:
            # 匹配不到则上传至Others目录
            print(
                "warning: 获取不到客户中文名，请在Engine的version里增加customer信息或者在xcustomer.py文件增加客户映射，"
                "否则文件将会上传至`Others`文件夹！"
            )
            customer_dir = 'Others'

        client.mkdir(customer_dir)
        # 列表第一个是文件夹本身剔除
        files = client.list(customer_dir, get_info=True)[1:]
        for file_ in files:
            # 格式：mesconfig_changshajianke_v3.0.0.1_20250422104557
            sub_parts = parts[0].rsplit('_', 1)
            # mesconfig_changshajianke_v3.0.0.1_20250422
            filename_day = sub_parts[0] + '_' + sub_parts[1][:8]
            # 如果版本号一样且打包时间在1个小时内的，认为是打错的包，则只存储最新的包，避免随意执行打包脚本时所有的包都给上传了
            file_name = file_.get('name', '')
            if filename_day in file_name:
                upload_pkg_time = datetime.datetime.strptime(sub_parts[1], '%Y%m%d%H%M%S')
                file_name_parts = file_name.rsplit('_')
                yun_pkg_time = datetime.datetime.strptime(file_name_parts[-2], '%Y%m%d%H%M%S')
                time_diff = abs(upload_pkg_time - yun_pkg_time)
                # 检查时间差是否小于等于1小时
                if time_diff.total_seconds() <= 3600:
                    client.clean(f'{customer_dir}/{file_name}')
                    print(f'删除旧包：{file_name}')

        # 缓存的包控制在12个，避免到时客户多时，坚果云空间不够用
        MAX_PKG_COUNT = 15
        if len(files) >= MAX_PKG_COUNT:
            # 清理时间最早的一个包
            sorted_files = sorted(files, key=lambda x: x['modified'])
            delete_file = f'{customer_dir}/{sorted_files[0]}'
            client.clean(delete_file)
            print(f"此客户发布包数量已大于{MAX_PKG_COUNT}个，清理旧包：{delete_file}")

        remote_file_name = f'{customer_dir}/{zip_name}'
        client.upload(remote_path=remote_file_name, local_path=pkg_path)
        print(f"上传成功，已将新包 {pkg_path} 上传到 {upload_dir}/{customer_dir} 目录下")
        print(f"请到坚果云平台配置包分享权限以及拷贝链接给到客户！")

    except Exception as e:
        print(f"文件上传失败，WEBDAV服务访问失败: {e}")
        print(traceback.format_exc())


def get_folder_size(client, folder_path):
    total_size = 0
    # 第一个是文件夹本身剔除
    files = client.list(folder_path, get_info=True)[1:]
    for file in files:
        file_path = f"{folder_path}/{file['name']}"
        if file['isdir']:
            # 不是这个公共账号存储空间的，不统计
            if file['name'] == 'MES配置器存档':
                continue

            # 如果是文件夹，递归计算其大小
            total_size += get_folder_size(client, file_path)
        else:
            # 如果是文件，获取其大小并累加
            total_size += int(file['size'])
    return total_size


def calculate_storage():
    options = {
        'webdav_hostname': "https://drive.cvte.com/dav/",
        'webdav_login': "<EMAIL>",
        'webdav_password': "a2xga4skezsr4qpp",
        'webdav_root': "",
        'disable_check': True
    }
    try:
        client = Client(options)
        start_time = time.time()
        total_space = 110
        used_space = get_folder_size(client, '') / (1024 * 1024 * 1024)
        used_space = round(used_space, 3)  # 保留两位小数的数值
        available_space = total_space - used_space
        print(f'\n\033[95m-------------坚果云空间统计----------------\033[0m')
        print(f'\033[94m总空间：{total_space}G\033[0m，'
              f'\033[96m已用空间：{used_space}G\033[0m，'
              f'\033[92m剩余空间：{available_space}G\033[0m')
        if (available_space / total_space) < 0.05:
            print(f"\033[91m\033[1m可用空间已小于5%，告急，请上坚果云进行一次空间清理！\033[0m")
        end_time = time.time()
        execution_time = end_time - start_time
        execution_time = round(execution_time, 2)
        print(f"\033[32m统计存储空间总耗时: {execution_time}s\033[0m")
    except Exception as e:
        print(f"webdav服务访问失败: {e}")


def cmd_installer_pyqt_app():
    """
    使用pyinstaller打包pyqt程序
    :return:
    """
    # t1 = time.time()
    cmd = ['pyinstaller', '--onefile', '--hidden-import=somepackage', 'mesconfig.py', '-n', 'mesconfig']

    # 使用subprocess运行命令并实时打印输出
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True)

    # 读取并打印输出，直到进程结束
    for line in process.stdout:
        print(line, end='')  # end='' 用于避免额外的换行符

    # 等待进程结束并获取退出码
    process.wait()

    # 检查退出码
    if process.returncode != 0:
        print('\033[91m' + f'打包失败！Return code: {process.returncode}' + '\033[0m')

    # print('\033[32m' + f'打包成功！' + '\033[0m')
    # print('\033[32m' + f'打包用时：{int(time.time() - t1)}s' + '\033[0m')
    # print('\033[32m' + f'客户：{engine.version.get("title")}' + '\033[0m')


if __name__ == '__main__':
    t1 = time.time()
    pwd = os.getcwd()

    release = engine.get_release()  # 格式： "guangzhouyierda release v3.0.0.3"
    parts = release.split()
    customer_flag = parts[0]  # standard
    version = parts[2]  # v3.0.0.6

    customer_info_list = engine.version.get('customer', [])
    if not customer_info_list:
        customer_name = xcustomer.customer_map.get(customer_flag, '')

        if customer_name:
            customer_info_list = [customer_name, customer_flag]

    if os.path.exists(f"{pwd}/build"):
        shutil.rmtree(f"{pwd}/build")

    cmd_installer_pyqt_app()

    dist_path = f"{pwd}/dist"
    # print(dist_path)

    src_file = f"{dist_path}/mesconfig"
    dst_root_path = f"{dist_path}/mesconfig_python"
    dst_file = f"{dst_root_path}/mesconfig"

    if os.path.exists(dst_root_path):
        shutil.rmtree(dst_root_path)

    os.makedirs(dst_root_path)

    if not os.path.exists(src_file):
        raise Exception(f"{dst_file}不存在, 请先使用pyinstaller打包mesconfig文件！")

    shutil.move(src_file, dst_file)
    # print(f"文件已从 {src_file} 移动到 {dst_file}")

    p_path = f"{pwd}/static/package_static"

    if os.path.exists(p_path):
        print("正在打包静态文件文件...")
        for file in os.listdir(p_path):
            f_path = f"{p_path}/{file}"

            if os.path.isfile(f_path):
                shutil.copy(f_path, f"{dst_root_path}/{file}")

            if os.path.isdir(f_path):
                shutil.copytree(f_path, f"{dst_root_path}/{file}")

    # 写入哈希值
    repo = Repo("./")

    app_info = {
        "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "commit_id": str(repo.head.commit)[:10]
    }

    app_info_file = f"{dst_root_path}/static/app_info.json"

    with open(app_info_file, "w") as f:
        json.dump(app_info, f, ensure_ascii=False, indent=4)

    pkg_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    zip_src_dir = dst_root_path
    ubuntu_major_version = get_ubuntu_major_version()
    # 包命名：配置器_客户名_版本_打包日期_ubuntu主版本.zip
    zip_file = f'{dist_path}/mesconfig_{customer_flag}_{version}_{pkg_time}_u{ubuntu_major_version}'
    zip_path = f'{zip_file}.zip'
    shutil.make_archive(zip_file, "zip", zip_src_dir)
    print(f"打包完成，请拷贝：{zip_path}")

    # -------------- 某些版本需要特别打包
    if "nengxiao" in release:
        shutil.copy(f"{pwd}/NENGXIAO_MESDll.dll", f"{dst_root_path}/NENGXIAO_MESDll.dll")
    # --------------

    upload_pkg_to_jianguoyun(zip_path, customer_info_list)

    print('\033[32m' + f'打包成功！' + '\033[0m')
    print('\033[32m' + f'打包用时：{int(time.time() - t1)}s' + '\033[0m')
    print('\033[32m' + f'客户：{release}' + '\033[0m')

    package_log.info(f"打包成功，release: {release}")

    calculate_storage()
