# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/7 上午10:00
# Author     ：sch
# version    ：python 3.8
# Description：惠州骏亚SMT    https://jira.cvte.com/browse/ATAOI_2019-37745
"""

from typing import Any

from common import xrequest, xutil, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoujunya_smt release v1.0.0.3",
        "device": "AIS401、AIS430、AIS630",
        "feature": ["条码校验", "上传数据", "设备状态", "条码获取"],
        "author": "sunchangheng",
        "release": """
date: 2025-03-07 17:43  jira->37745: 条码校验，上传数据，上传设备状态
date: 2025-03-08 11:30  bugfix: 兼容接口返回，找不到操作员配置项，Machine->MachineID
date: 2025-04-22 17:20  jira->37745: 增加条码获取功能
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "条码校验接口",
            "value": "http://*************:9800/ims-cust/api/spi/BarCodeCheck",
        },
        "api_url_get_sn": {
            "ui_name": "条码获取接口",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "检测结果上传接口",
            "value": "http://127.0.0.1:8899/MesOutPut",
        },
        "api_url_device": {
            "ui_name": "设备状态接口",
            "value": "http://*************:9800/ims-cust/api/spi/UpdateDeviceStatus",
        },
    }

    form = {
        "line_name": {
            "ui_name": "线体名称",
            "value": "",
        },
        "work_station": {
            "ui_name": "工站(设备编码)",
            "value": "",
        },
        "machine_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "machine_ip": {
            "ui_name": "设备IP",
            "value": "",
        },
        "work_no": {
            "ui_name": "工单号",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "org_code": {
            "ui_name": "组织编码",
            "value": "",
        },
    }

    combo = {
        "board_side_ui": {
            "ui_name": "板面",
            "item": ["T", "B"],
            "value": "T",
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check", not_null=True)
        work_no = other_vo.get_value_by_cons_key("work_no")
        operator = other_vo.get_value_by_cons_key("operator")
        line_name = other_vo.get_value_by_cons_key("line_name")
        work_station = other_vo.get_value_by_cons_key("work_station")

        sn_list = other_vo.list_sn()
        project_name = other_vo.get_project_name()

        board_sn_list = []  # 拼板条码列表
        pcb_sn = ""

        if len(sn_list) == 1:
            pcb_sn = sn_list[0]
            board_sn_list = sn_list
        elif len(sn_list) >= 2:
            pcb_sn = sn_list[0]
            board_sn_list = sn_list[1:]

        check_param = {
            "BarCode": pcb_sn,
            "WorkNo": work_no,
            "Operator": operator,
            "JobName": project_name,
            "LineName": line_name,
            "WorkStation": work_station,
            "GroupBarCode": [],
            "ArrayBarCode": board_sn_list
        }

        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

        # 客户文档混乱，多写一个判断，以防接口返回首字母大写的字段
        if ret.get("result") == "NG" or ret.get("Result") == "NG":
            err_msg = ret.get('message')

            if not err_msg:
                err_msg = ret.get('Message')

            return self.x_response("false", f"mes接口异常，error：{err_msg}")

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn", not_null=True)
        org_code = other_vo.get_value_by_cons_key("org_code")

        pcb_sn = other_vo.list_sn()
        # 无整板条码则取拼板条码，否则获取其中一个拼板条码
        if pcb_sn:
            lb_code = pcb_sn
        else:
            lb_code = ''
            barcode_map = other_vo.get_barcode_map()
            for key, value in barcode_map.items():
                if key not in ['-1', '-2'] and value:
                    lb_code = value
                    break

        if not lb_code:
            return self.x_response("false", f"未获取到可用的条码")

        param = {
            "value": {
                "orgCode": org_code,
                "lbCode": lb_code
            }
        }
        try:
            ret = xrequest.RequestUtil.post_json(api_url_get_sn, param)
            if ret.get("code") == 0:
                rows = ret.get("data", {}).get('rows', [])
                if rows:
                    sn_list = []
                    for item in sorted(rows, key=lambda x: x["seq"]):
                        sn_list.append(item["lb_code"])
                    return self.x_response("true", ",".join(sn_list))
                else:
                    return self.x_response("false", f"mes接口返回拼板列表rows为空")

            else:
                err_msg = f"message:{ret.get('message')}, stackTrace:{ret.get('stackTrace')}"
                return self.x_response("false", f"mes接口返回错误，{err_msg}")
        except Exception as e:
            return self.x_response("false", f"本地网络异常，error：{e}")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data", not_null=True)
        line_name = data_vo.get_value_by_cons_key("line_name")
        work_station = data_vo.get_value_by_cons_key("work_station")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        operator = data_vo.get_value_by_cons_key("operator")
        work_no = data_vo.get_value_by_cons_key("work_no")
        board_side_ui = data_vo.get_value_by_cons_key("board_side_ui")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        comp_info_map, _ = pcb_entity.get_pad_test_data()

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_id = comp_entity.comp_id

                area_val = ""
                volume_val = ""
                height_val = ""
                x_offset_val = ""
                y_offset_val = ""
                for alg_entity in comp_entity.yield_alg_entity():
                    if alg_entity.test_name == "Area":
                        area_val = alg_entity.test_val
                    elif alg_entity.test_name == "Volume":
                        volume_val = alg_entity.test_val
                    elif alg_entity.test_name == "Height":
                        height_val = alg_entity.test_val
                    elif alg_entity.test_name == "XOffset":
                        x_offset_val = alg_entity.test_val
                    elif alg_entity.test_name == "YOffset":
                        y_offset_val = alg_entity.test_val

                comp_info = comp_info_map.get(comp_id, [])

                rh = ""
                if comp_info:
                    rh = comp_info[0].get("rh", "")

                comp_data_list.append({
                    "PadID": "1",
                    "ComponentID": comp_entity.designator,
                    "Type": comp_entity.type,
                    "PerArea": f"{height_val}%",
                    "Height": rh,
                    "PerVolume": "",
                    "XOffset": f"{x_offset_val}%",
                    "YOffset": f"{y_offset_val}%",
                    "PadSizeX": "",
                    "PadSizeY": "",
                    "Area": f"{area_val}%",
                    "PerHeight": "",
                    "Volume": f"{volume_val}%",
                    "Result": comp_entity.get_final_result("OK", "OK", "NG"),
                    "ErrCode": comp_entity.repair_ng_code,
                    "PinNum": "0",
                    "Barcode": barcode,
                    "Date": time_file[:8],
                    "Time": time_file[8:],
                    "ImagePath": comp_entity.image_path,
                    "ArrayID": board_no
                })

            board_data_list.append({
                "ArrayID": board_no,
                "ArrayStatus": board_entity.get_repair_result("OK", "NG"),
                "ArrayBarCode": barcode,
                "Pads": comp_data_list
            })

        data_param = {
            "MachineID": work_station,
            "MachineName": machine_name,
            "Operator": operator,
            "Side": board_side_ui,
            "JobName": pcb_entity.project_name,
            "LineName": line_name,
            "WorkStation": work_station,
            "WorkNo": work_no,
            "BoardBarCode": pcb_entity.pcb_barcode,
            "BoardStatus": pcb_entity.get_repair_result("OK", "NG"),
            "DateTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "PCBImagePath": pcb_entity.get_pcb_t_image(),
            "CarrierBoardCode": "",
            "Array": board_data_list
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

        # 客户文档混乱，多写一个判断，以防接口返回首字母大写的字段
        if ret.get("result") == "NG" or ret.get("Result") == "NG" or ret.get("Request") == "NG":
            err_msg = ret.get('message')

            if not err_msg:
                err_msg = ret.get('Message')

            return self.x_response("false", f"mes接口异常，error：{err_msg}")

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_device = other_vo.get_value_by_cons_key("api_url_device", not_null=True)
        work_station = other_vo.get_value_by_cons_key("work_station")
        machine_name = other_vo.get_value_by_cons_key("machine_name")
        machine_ip = other_vo.get_value_by_cons_key("machine_ip")

        status_code_v3 = other_vo.get_status_code_v3()
        status_desc_v3 = other_vo.get_status_desc_v3()

        error_list = []

        if status_code_v3 == "1002":
            dev_state = "run"
            dev_str = "运行"
        elif status_code_v3 == "1003":
            dev_state = "stop"
            dev_str = "停止"
        elif status_code_v3 == "3001":
            dev_state = "wait"
            dev_str = "等待"
        elif status_code_v3 in ["2001", "2002", "3002", "3003", "3004",
                                "3005", "3006", "3007", "4001", "4002", "4003",
                                "5001", "5002"]:
            dev_state = "error"
            dev_str = "异常"
            error_list.append({
                "ErrorCode": status_code_v3,
                "ErrorMessage": status_desc_v3,
                "SuggestedSolutions": "",
            })
        else:
            self.log.info(f"该设备状态不上传到Mes！")
            return self.x_response()

        device_param = {
            "Lane": "1",
            "MachineID": work_station,
            "MachineName": machine_name,
            "IP": machine_ip,
            "StatusCode": dev_state,
            "StatusMessage": dev_str,
            "ChangeTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "ErrorList": error_list,
        }

        xrequest.RequestUtil.post_json(api_url_device, device_param)

        return self.x_response()
