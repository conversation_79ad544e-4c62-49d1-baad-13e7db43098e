#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志分析工具主界面
"""

import sys
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QTabWidget, QTextEdit, 
                            QLineEdit, QPushButton, QLabel, QComboBox, QCheckBox,
                            QTableWidget, QTableWidgetItem, QSplitter, QGroupBox,
                            QDateTimeEdit, QSpinBox, QProgressBar, QMessageBox,
                            QFileDialog, QListWidget, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDateTime
from PyQt5.QtGui import QFont, QColor, QIcon

try:
    from .log_parser import LogParser, LogEntry
    from .log_searcher import LogSearcher, SearchCriteria
    from .error_analyzer import ErrorAnalyzer
except ImportError:
    from log_parser import LogParser, LogEntry
    from log_searcher import LogSearcher, SearchCriteria
    from error_analyzer import ErrorAnalyzer


class LogAnalysisThread(QThread):
    """日志分析线程"""
    progress_updated = pyqtSignal(int)
    analysis_completed = pyqtSignal(list, dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, log_files, search_criteria=None):
        super().__init__()
        self.log_files = log_files
        self.search_criteria = search_criteria
        
    def run(self):
        try:
            parser = LogParser()
            all_entries = []
            
            for i, file_path in enumerate(self.log_files):
                self.progress_updated.emit(int((i / len(self.log_files)) * 100))
                entries = parser.parse_log_file(file_path)
                all_entries.extend(entries)
            
            # 分析错误
            analyzer = ErrorAnalyzer()
            error_summary = analyzer.get_error_summary(all_entries)
            
            self.analysis_completed.emit(all_entries, error_summary)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class LogAnalyzerMainWindow(QMainWindow):
    """日志分析工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.entries = []
        self.searcher = LogSearcher()
        self.analyzer = ErrorAnalyzer()
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("日志快速定位分析工具 v1.0")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建工具栏
        toolbar_layout = self.create_toolbar()
        main_layout.addLayout(toolbar_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 1000])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
    def create_toolbar(self):
        """创建工具栏"""
        layout = QHBoxLayout()
        
        # 文件选择
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("选择日志文件或目录...")
        layout.addWidget(QLabel("日志路径:"))
        layout.addWidget(self.file_path_edit)
        
        self.browse_btn = QPushButton("浏览")
        layout.addWidget(self.browse_btn)
        
        self.load_btn = QPushButton("加载日志")
        self.load_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        layout.addWidget(self.load_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return layout
        
    def create_left_panel(self):
        """创建左侧面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 搜索区域
        search_group = QGroupBox("搜索条件")
        search_layout = QVBoxLayout(search_group)
        
        # 快速搜索
        quick_layout = QHBoxLayout()
        quick_layout.addWidget(QLabel("快速搜索:"))
        self.quick_search_edit = QLineEdit()
        self.quick_search_edit.setPlaceholderText("输入关键词...")
        quick_layout.addWidget(self.quick_search_edit)
        
        self.quick_search_btn = QPushButton("搜索")
        quick_layout.addWidget(self.quick_search_btn)
        search_layout.addLayout(quick_layout)
        
        # 高级搜索
        advanced_group = QGroupBox("高级搜索")
        advanced_layout = QGridLayout(advanced_group)
        
        # 日志级别
        advanced_layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        advanced_layout.addWidget(self.level_combo, 0, 1)
        
        # 时间范围
        advanced_layout.addWidget(QLabel("开始时间:"), 1, 0)
        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDateTime(QDateTime.currentDateTime().addDays(-1))
        advanced_layout.addWidget(self.start_time_edit, 1, 1)
        
        advanced_layout.addWidget(QLabel("结束时间:"), 2, 0)
        self.end_time_edit = QDateTimeEdit()
        self.end_time_edit.setDateTime(QDateTime.currentDateTime())
        advanced_layout.addWidget(self.end_time_edit, 2, 1)
        
        # 错误码
        advanced_layout.addWidget(QLabel("错误码:"), 3, 0)
        self.error_code_edit = QLineEdit()
        self.error_code_edit.setPlaceholderText("如: 255, 21, 错误码...")
        advanced_layout.addWidget(self.error_code_edit, 3, 1)
        
        # 排除关键词
        advanced_layout.addWidget(QLabel("排除关键词:"), 4, 0)
        self.exclude_edit = QLineEdit()
        self.exclude_edit.setPlaceholderText("用逗号分隔...")
        advanced_layout.addWidget(self.exclude_edit, 4, 1)
        
        # 区分大小写
        self.case_sensitive_cb = QCheckBox("区分大小写")
        advanced_layout.addWidget(self.case_sensitive_cb, 5, 0, 1, 2)
        
        # 搜索按钮
        self.advanced_search_btn = QPushButton("高级搜索")
        advanced_layout.addWidget(self.advanced_search_btn, 6, 0, 1, 2)
        
        search_layout.addWidget(advanced_group)
        layout.addWidget(search_group)
        
        # 快捷功能
        shortcuts_group = QGroupBox("快捷功能")
        shortcuts_layout = QVBoxLayout(shortcuts_group)
        
        self.recent_errors_btn = QPushButton("最近24小时错误")
        shortcuts_layout.addWidget(self.recent_errors_btn)
        
        self.critical_errors_btn = QPushButton("严重错误")
        shortcuts_layout.addWidget(self.critical_errors_btn)
        
        self.error_patterns_btn = QPushButton("错误模式分析")
        shortcuts_layout.addWidget(self.error_patterns_btn)
        
        layout.addWidget(shortcuts_group)
        
        # 统计信息
        self.stats_tree = QTreeWidget()
        self.stats_tree.setHeaderLabel("统计信息")
        layout.addWidget(self.stats_tree)
        
        return widget
        
    def create_right_panel(self):
        """创建右侧面板"""
        tab_widget = QTabWidget()
        
        # 搜索结果标签页
        results_tab = QWidget()
        results_layout = QVBoxLayout(results_tab)
        
        # 结果统计
        results_info_layout = QHBoxLayout()
        self.results_count_label = QLabel("搜索结果: 0 条")
        results_info_layout.addWidget(self.results_count_label)
        results_info_layout.addStretch()
        
        self.export_btn = QPushButton("导出结果")
        results_info_layout.addWidget(self.export_btn)
        results_layout.addLayout(results_info_layout)
        
        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels(["时间", "级别", "文件", "行号", "错误码", "消息"])
        self.results_table.setAlternatingRowColors(True)
        results_layout.addWidget(self.results_table)
        
        tab_widget.addTab(results_tab, "搜索结果")
        
        # 错误分析标签页
        error_tab = QWidget()
        error_layout = QVBoxLayout(error_tab)
        
        # 错误摘要
        self.error_summary_text = QTextEdit()
        self.error_summary_text.setMaximumHeight(200)
        error_layout.addWidget(QLabel("错误摘要:"))
        error_layout.addWidget(self.error_summary_text)
        
        # 错误详情表格
        self.error_table = QTableWidget()
        self.error_table.setColumnCount(5)
        self.error_table.setHorizontalHeaderLabels(["错误码", "描述", "分类", "次数", "严重程度"])
        error_layout.addWidget(QLabel("错误详情:"))
        error_layout.addWidget(self.error_table)
        
        tab_widget.addTab(error_tab, "错误分析")
        
        # 日志详情标签页
        detail_tab = QWidget()
        detail_layout = QVBoxLayout(detail_tab)
        
        self.log_detail_text = QTextEdit()
        self.log_detail_text.setFont(QFont("Consolas", 10))
        detail_layout.addWidget(self.log_detail_text)
        
        tab_widget.addTab(detail_tab, "日志详情")
        
        return tab_widget
        
    def setup_connections(self):
        """设置信号连接"""
        self.browse_btn.clicked.connect(self.browse_files)
        self.load_btn.clicked.connect(self.load_logs)
        self.quick_search_btn.clicked.connect(self.quick_search)
        self.advanced_search_btn.clicked.connect(self.advanced_search)
        self.recent_errors_btn.clicked.connect(self.show_recent_errors)
        self.critical_errors_btn.clicked.connect(self.show_critical_errors)
        self.error_patterns_btn.clicked.connect(self.show_error_patterns)
        self.export_btn.clicked.connect(self.export_results)
        self.results_table.itemSelectionChanged.connect(self.show_log_detail)
        
        # 回车键搜索
        self.quick_search_edit.returnPressed.connect(self.quick_search)
        
    def browse_files(self):
        """浏览文件"""
        file_dialog = QFileDialog()
        file_path = file_dialog.getExistingDirectory(self, "选择日志目录")
        
        if file_path:
            self.file_path_edit.setText(file_path)
            
    def load_logs(self):
        """加载日志"""
        path = self.file_path_edit.text().strip()
        if not path:
            QMessageBox.warning(self, "警告", "请选择日志文件或目录")
            return
            
        if os.path.isdir(path):
            parser = LogParser()
            log_files = parser.scan_log_directory(path)
        elif os.path.isfile(path):
            log_files = [path]
        else:
            QMessageBox.warning(self, "警告", "指定的路径不存在")
            return
            
        if not log_files:
            QMessageBox.warning(self, "警告", "未找到日志文件")
            return
            
        # 启动分析线程
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.load_btn.setEnabled(False)
        
        self.analysis_thread = LogAnalysisThread(log_files)
        self.analysis_thread.progress_updated.connect(self.progress_bar.setValue)
        self.analysis_thread.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_thread.error_occurred.connect(self.on_analysis_error)
        self.analysis_thread.start()
        
    def on_analysis_completed(self, entries, error_summary):
        """分析完成处理"""
        self.entries = entries
        self.searcher.load_entries(entries)
        
        self.progress_bar.setVisible(False)
        self.load_btn.setEnabled(True)
        
        # 更新统计信息
        self.update_statistics()
        
        # 显示所有日志
        self.display_results(entries[:1000])  # 限制显示数量
        
        self.statusBar().showMessage(f"加载完成，共 {len(entries)} 条日志")
        
    def on_analysis_error(self, error_msg):
        """分析错误处理"""
        self.progress_bar.setVisible(False)
        self.load_btn.setEnabled(True)
        QMessageBox.critical(self, "错误", f"分析日志时出错: {error_msg}")
        
    def quick_search(self):
        """快速搜索"""
        query = self.quick_search_edit.text().strip()
        if not query or not self.entries:
            return
            
        results = self.searcher.quick_search(query, limit=1000)
        self.display_results(results)
        
    def advanced_search(self):
        """高级搜索"""
        if not self.entries:
            return
            
        criteria = SearchCriteria()
        
        # 关键词
        query = self.quick_search_edit.text().strip()
        if query:
            criteria.keywords = [query]
            
        # 日志级别
        level = self.level_combo.currentText()
        if level != "全部":
            criteria.levels = [level]
            
        # 时间范围
        criteria.start_time = self.start_time_edit.dateTime().toPyDateTime()
        criteria.end_time = self.end_time_edit.dateTime().toPyDateTime()
        
        # 错误码
        error_code = self.error_code_edit.text().strip()
        if error_code:
            criteria.error_codes = [code.strip() for code in error_code.split(',')]
            
        # 排除关键词
        exclude = self.exclude_edit.text().strip()
        if exclude:
            criteria.exclude_keywords = [kw.strip() for kw in exclude.split(',')]
            
        criteria.case_sensitive = self.case_sensitive_cb.isChecked()
        
        results = self.searcher.search(criteria)
        self.display_results(results)
        
    def show_recent_errors(self):
        """显示最近错误"""
        if not self.entries:
            return
            
        results = self.searcher.get_recent_errors(hours=24, limit=100)
        self.display_results(results)
        
    def show_critical_errors(self):
        """显示严重错误"""
        if not self.entries:
            return
            
        criteria = SearchCriteria(levels=['CRITICAL', 'ERROR'])
        results = self.searcher.search(criteria)
        self.display_results(results)
        
    def show_error_patterns(self):
        """显示错误模式"""
        if not self.entries:
            return
            
        patterns = self.searcher.get_error_patterns()
        
        # 在错误分析标签页显示
        text = "错误模式分析:\n\n"
        for pattern, count in list(patterns.items())[:20]:
            text += f"{count:4d} 次: {pattern}\n"
            
        self.error_summary_text.setText(text)
        
    def display_results(self, results):
        """显示搜索结果"""
        self.results_table.setRowCount(len(results))
        self.results_count_label.setText(f"搜索结果: {len(results)} 条")
        
        for row, entry in enumerate(results):
            # 时间
            time_str = entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else ""
            self.results_table.setItem(row, 0, QTableWidgetItem(time_str))
            
            # 级别
            level_item = QTableWidgetItem(entry.level)
            if entry.level == 'ERROR':
                level_item.setBackground(QColor(255, 200, 200))
            elif entry.level == 'CRITICAL':
                level_item.setBackground(QColor(255, 150, 150))
            elif entry.level == 'WARNING':
                level_item.setBackground(QColor(255, 255, 200))
            self.results_table.setItem(row, 1, level_item)
            
            # 文件
            file_name = os.path.basename(entry.file_path)
            self.results_table.setItem(row, 2, QTableWidgetItem(file_name))
            
            # 行号
            self.results_table.setItem(row, 3, QTableWidgetItem(str(entry.line_number)))
            
            # 错误码
            self.results_table.setItem(row, 4, QTableWidgetItem(entry.error_code or ""))
            
            # 消息
            message = entry.message[:100] + "..." if len(entry.message) > 100 else entry.message
            self.results_table.setItem(row, 5, QTableWidgetItem(message))
        
        self.results_table.resizeColumnsToContents()
        
    def show_log_detail(self):
        """显示日志详情"""
        current_row = self.results_table.currentRow()
        if current_row >= 0 and current_row < len(self.entries):
            entry = self.entries[current_row]
            detail_text = f"文件: {entry.file_path}\n"
            detail_text += f"行号: {entry.line_number}\n"
            detail_text += f"时间: {entry.timestamp}\n"
            detail_text += f"级别: {entry.level}\n"
            detail_text += f"错误码: {entry.error_code or '无'}\n"
            detail_text += f"原始内容:\n{entry.raw_line}\n\n"
            detail_text += f"消息内容:\n{entry.message}"
            
            self.log_detail_text.setText(detail_text)
            
    def update_statistics(self):
        """更新统计信息"""
        if not self.entries:
            return
            
        stats = self.searcher.get_statistics()
        
        self.stats_tree.clear()
        
        # 总体统计
        total_item = QTreeWidgetItem(["总体统计"])
        total_item.addChild(QTreeWidgetItem([f"总条目数: {stats['total']}"]))
        self.stats_tree.addTopLevelItem(total_item)
        
        # 级别统计
        level_item = QTreeWidgetItem(["日志级别"])
        for level, count in stats['levels'].items():
            level_item.addChild(QTreeWidgetItem([f"{level}: {count}"]))
        self.stats_tree.addTopLevelItem(level_item)
        
        # 文件统计
        file_item = QTreeWidgetItem(["文件分布"])
        for file_name, count in list(stats['files'].items())[:10]:
            file_item.addChild(QTreeWidgetItem([f"{file_name}: {count}"]))
        self.stats_tree.addTopLevelItem(file_item)
        
        # 错误码统计
        if stats['error_codes']:
            error_item = QTreeWidgetItem(["错误码"])
            for code, count in list(stats['error_codes'].items())[:10]:
                error_item.addChild(QTreeWidgetItem([f"{code}: {count}"]))
            self.stats_tree.addTopLevelItem(error_item)
            
        self.stats_tree.expandAll()
        
    def export_results(self):
        """导出结果"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有可导出的结果")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出搜索结果", "search_results.csv", "CSV Files (*.csv)")
            
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # 写入表头
                    headers = ["时间", "级别", "文件", "行号", "错误码", "消息"]
                    f.write(','.join(headers) + '\n')
                    
                    # 写入数据
                    for row in range(self.results_table.rowCount()):
                        row_data = []
                        for col in range(self.results_table.columnCount()):
                            item = self.results_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        f.write(','.join(f'"{data}"' for data in row_data) + '\n')
                        
                QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("日志快速定位分析工具")
    
    window = LogAnalyzerMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
