# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/2 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：深圳欣旺达AVI    深圳欣旺达总部工厂五楼  （鉴锐欣供应商）机型：590
"""
import json
import os
import sqlite3
from datetime import datetime
from typing import Any

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import x_response, log
from engine.MesEngine import ErrorMapEngine
from services.custom.shenzhenxinwangda_jrx_5l_590.xwd_module import upload_image_to_s3_with_retries
from vo.mes_vo import DataVo, OtherVo, ButtonVo, ComboVo


def get_alg_data(review_path: str) -> dict:
    """
    获取算法数据
    """
    alg_db_path = f"{review_path}/AlgorithmInfo.db"

    if not os.path.exists(alg_db_path):
        log.warning(f"{alg_db_path}不存在！")
        return {}

    conn = sqlite3.connect(alg_db_path)
    data_map = {}

    cur = conn.cursor()

    try:
        cur.execute("SELECT * FROM compalgorithmdata;")

        # 获取所有行的结果列表
        rows = cur.fetchall()

        # 遍历结果并打印每一行
        for row in rows:
            data_map[str(row[1])] = {
                "alg_name": row[3],
                "alg_val": json.loads(row[4])
            }
    finally:
        cur.close()
        conn.close()

    log.info(f"算法数据： {len(data_map)}")

    return data_map


def refresh_token(
        api_url: str,
        user_id: str,
        password: str,
        device_id: str,
        timeout=5,
        log_save_path=None,
):
    """
    刷新token
    :param api_url:
    :param user_id:
    :param password:
    :param device_id:
    :param timeout:
    :param log_save_path:
    :return:
    """
    login_param = {
        "userId": user_id,
        "password": password,
        "deviceId": device_id
    }

    if log_save_path:
        log_str1 = f"请求URL：{api_url}  请求参数：\n{json.dumps(login_param, ensure_ascii=False)}"
        xutil.FileUtil.write_request_log(log_save_path, log_str1)

    ret = xrequest.RequestUtil.post_json(api_url, login_param, timeout=timeout)

    if log_save_path:
        log_str2 = f"接口响应：{json.dumps(ret, ensure_ascii=False)}"
        xutil.FileUtil.write_request_log(log_save_path, log_str2)

    if str(ret.get("code")) != "200":
        return x_response("false", f"MES接口响应异常，登录失败，error：{ret.get('msg')}")

    global_data["is_login"] = True


check_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTestCommon xmlns="WWW.SUNWODA.COM">
            <M_SN>{sn}</M_SN>
            <M_MACHINCENO>{machine_no}</M_MACHINCENO>
            <M_EMP>{emp}</M_EMP>
            <M_MO>{mo}</M_MO>
        </GroupTestCommon>
    </soap:Body>
</soap:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <WIPTEST xmlns="WWW.SUNWODA.COM">
            <M_SN>{M_SN}</M_SN>
            <M_RESULT>{M_RESULT}</M_RESULT>
            <M_USERNO>{M_USERNO}</M_USERNO>
            <M_MACHINENO>{M_MACHINENO}</M_MACHINENO>
            <M_ERROR>{M_ERROR}</M_ERROR>
            <M_ITEMVALUE>{M_ITEMVALUE}</M_ITEMVALUE>
        </WIPTEST>
    </soap:Body>
</soap:Envelope>"""

check_template_v2 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <BindCarrierAndPcbV2 xmlns="WWW.SUNWODA.COM">
      <CARRIER_ID>{CARRIER_ID}</CARRIER_ID>
      <PCB_SN>{PCB_SN}</PCB_SN>
      <M_MACHINCENO>{M_MACHINCENO}</M_MACHINCENO>
      <M_EMP>{M_EMP}</M_EMP>
      <M_MO>{M_MO}</M_MO>
    </BindCarrierAndPcbV2>
  </soap:Body>
</soap:Envelope>"""


bind_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <BindCarrierAndPcb xmlns="WWW.SUNWODA.COM">
      <CARRIER_ID>{CARRIER_ID}</CARRIER_ID>
      <PCB_SN>{PCB_SN}</PCB_SN>
    </BindCarrierAndPcb>
  </soap:Body>
</soap:Envelope>"""


def x_request_device_status(
        api_url_status: str,
        status_code_v3: str,
        req_cycle: int,
        device_id: str,
):
    data_list = []
    warn_list = []
    if status_code_v3 in ["1003", "1005", "3001"]:
        state = "0"  # 待机

        data_list.append({
            "extendCode": status_code_v3,
            "extendValue": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    elif status_code_v3 in ["1001", "1002", "1004"]:
        state = "1"  # 运行

        data_list.append({
            "extendCode": status_code_v3,
            "extendValue": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    elif status_code_v3 in ["2001", "2002", "3002", "3003", "3004", "3005", "3006", "3007",
                            "4001", "4002", "4003", "5001", "5002"]:
        state = "2"  # 报警

        warn_list.append({
            "warnCode": status_code_v3,
            "warnMsg": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    else:
        log.warning(f"未知的设备状态，本次不上传！")
        state = "99"

    send_count = global_data.get("send_status_count", 0)
    send_count += 1

    if state != "99":
        # 上传设备状态
        d1 = datetime.now()

        time_file = d1.strftime(xcons.FMT_TIME_FILE)
        f2 = d1.strftime(xcons.FMT_TIME_DEFAULT)
        device_param = {
            "taskId": f"DEV_{time_file}_{str(send_count).zfill(4)}",
            "reqTime": f2,
            "reqCycle ": req_cycle * 1000,
            "deviceCode": device_id,
            "statusCode": state,
            "dataList": data_list,
            "warnList": warn_list,
        }

        xrequest.RequestUtil.post_json(api_url_status, device_param)
        global_data["last_device_code_v3"] = status_code_v3


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenxinwangda_jrx_5l_590 release v1.0.0.19",
        "device": "AIS590",
        "feature": ["条码校验", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-02 18:32  条码校验，上传数据，登录，获取界面信息，设备状态
date: 2024-11-29 14:52  更换条码校验，上传数据的接口
date: 2024-11-30 11:34  修改条码校验格式
date: 2024-11-30 13:10  获取B面的条码过站
date: 2024-12-01 22:51  保存日志+上传图片到AWS存储桶
date: 2024-12-02 14:55  存储桶放在界面配置
date: 2024-12-15 22:25  新增传输算法数据
date: 2025-01-08 20:36  优化代码
date: 2025-02-20 14:28  bugfix: 第一次打开Mes配置器时，报警：PermissionError: [Errno 13] 权限不够
date: 2025-05-17 09:34  需求变更：1.算法数据不传了  2.增加一些 testItemList 项
date: 2025-06-04 09:26  ATAOI_2019-32897: 1. 条码前缀校验  2. 上传图片失败的话，重试三次
date: 2025-06-13 14:54  ATAOI_2019-32897: 增加小板SN与载具进行绑定功能
date: 2025-06-13 17:43  兼容接口返回
date: 2025-07-08 10:47  ATAOI_2019-40067: 增加坏板绑定治具的功能
""", }

    other_combo = {
        "device_cron": {
            "ui_name": "定时上传设备状态",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "device_sequence": {
            "ui_name": "定时上传频率(s)",
            "item": ["30", "60", "180", "360", "600", "1200", "2400", "3000"],
            "value": "180",
        },
    }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(登录)",
            "value": "http://127.0.0.1:8081/common/Login",
        },
        "api_url_factory": {
            "ui_name": "接口URL(获取工厂)",
            "value": "http://127.0.0.1:8081/common/FactoryInfoDownLoadV1",
        },
        "api_url_workstation": {
            "ui_name": "接口URL(获取车间)",
            "value": "http://127.0.0.1:8081/common/WorkStationDownLoadV1",
        },
        "api_url_line": {
            "ui_name": "接口URL(获取线体)",
            "value": "http://127.0.0.1:8081/common/LineDownLoadV1",
        },
        "api_url_station": {
            "ui_name": "接口URL(获取工站)",
            "value": "http://127.0.0.1:8081/common/StationDownloadV1",
        },
        "api_url_mo": {
            "ui_name": "接口URL(获取工单)",
            "value": "http://127.0.0.1:8081/common/MoInfoDownLoad",
        },
        "api_url_check": {
            "ui_name": "接口URL(工序校验)",
            "value": "http://127.0.0.1:8081/test/MESInterface.asmx",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据/过站)",
            "value": "http://127.0.0.1:8081/commonApi/TackProduct.ashx",
        },
        # "api_url_data2": {
        #     "ui_name": "接口URL(上传数据/过站-含图片链接)",
        #     "value": "",
        # },
        "api_url_status": {
            "ui_name": "接口URL(上传设备状态)",
            "value": "http://127.0.0.1:8081/common/deviceServices",
        },
        "aws_endpoint_url": {
            "ui_name": "AWS Service Url",
            "value": "zzzxhcp.hzhcp.sunwoda.com",
        },
        "aws_access_key_id": {
            "ui_name": "AWS Access Key ID",
            "value": "enp6eGhjcA==",
        },
        "aws_secret_access_key": {
            "ui_name": "AWS Secret Access Key",
            "value": "24785287ae393901fe8c08477dbf7450",
        },
        "region_name": {
            "ui_name": "AWS RegionName",
            "value": "",  # us-east-1
        },
        "bucket_name": {
            "ui_name": "AWS BucketName",
            "value": "ZZZX-BL-5D1L-BL-SMT-01-YS-DEK-01",  # us-east-1
        },
        "bind_sn_url": {
            "ui_name": "绑定条码接口URL",
            "value": "http://127.0.0.1:8081/bind_sn",
        },
    }

    form = {
        "syb": {
            "ui_name": "事业部",
            "value": "ZZZX",
        },
        "device_id": {
            "ui_name": "设备Id/设备编号",
            "value": "",
        },
        "user_id": {
            "ui_name": "工号",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "check_barcode_prefix": {
            "ui_name": "条码校验前缀",
            "value": "APH307AV",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登录",
        },
    }

    combo = {
        "is_bind_sn": {
            "ui_name": "小板SN与载具绑定",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "is_upload_img": {
            "ui_name": "是否上传图片",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "is_upload_ng": {
            "ui_name": "上传NG数据/过站",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "factory_list": {
            "ui_name": "工厂",
            "item": [],
            "value": "",
        },
        "workstation_list": {
            "ui_name": "车间",
            "item": [],
            "value": "",
        },
        "line_list": {
            "ui_name": "线体",
            "item": [],
            "value": "",
        },
        "station_list": {
            "ui_name": "工站",
            "item": [],
            "value": "",
        },
        "mo_list": {
            "ui_name": "工单",
            "item": [],
            "value": "",
        },
    }

    path = {
        "log_save_path": {
            "ui_name": "日志保存路径",
            "value": "",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        device_id = other_vo.get_value_by_cons_key("device_id")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        log_save_path = other_vo.get_value_by_cons_key("log_save_path")

        device_cron = other_vo.get_value_by_cons_key("device_cron")
        device_sequence = other_vo.get_value_by_cons_key("device_sequence", to_int=True)

        main_window.set_cron_setting(device_cron == "Yes", device_sequence)

        try:
            refresh_token(
                api_url_login,
                user_id,
                password,
                device_id,
                timeout=1,
                log_save_path=log_save_path
            )
            log.info(f"自动登录成功！")
        except Exception as err:
            err_msg = f"自动登录失败，error：{err}"
            self.log.warning(err_msg)
            xutil.FileUtil.write_request_log(log_save_path, err_msg)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        device_id = other_vo.get_value_by_cons_key("device_id")
        user_id = other_vo.get_value_by_cons_key("user_id")
        mo_list_select = other_vo.get_value_by_cons_key("mo_list")
        log_save_path = other_vo.get_value_by_cons_key("log_save_path", not_null=True)
        check_barcode_prefix = other_vo.get_value_by_cons_key("check_barcode_prefix")

        sn_list = other_vo.list_sn()

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        pcb_sn = sn_list[0]

        new_sn_list = sn_list[1:]

        format_sn_list = []
        ix = 0
        for item in new_sn_list:
            ix += 1
            format_sn_list.append(f"{ix}:{item}")

        if not pcb_sn.startswith(check_barcode_prefix):
            return self.x_response("false",
                                   f"PCB条码校验失败，error：PCB条码校验失败，扫到的条码：{pcb_sn} 需要匹配的前缀：{check_barcode_prefix}")

        check_param = check_template_v2.format(**{
            "CARRIER_ID": pcb_sn,
            "PCB_SN": "|".join(format_sn_list),
            "M_MACHINCENO": device_id,
            "M_EMP": user_id,
            "M_MO": mo_list_select,
        })

        try:
            log_str1 = f"接口URL：{api_url_check}  接口参数：\n{check_param}"
            xutil.FileUtil.write_request_log(log_save_path, log_str1)

            ret_str = xrequest.RequestUtil.post_soap(
                api_url_check,
                check_param,
                soap_action="WWW.SUNWODA.COM/BindCarrierAndPcbV2"
            )
            xutil.FileUtil.write_request_log(log_save_path, f"响应参数：{ret_str}")
            root_check = xutil.XmlUtil.get_xml_root_by_str(ret_str)

            ret_result = root_check[0][0][0].text
            if "TRUE" not in ret_result:
                return self.x_response("false", f"mes接口异常，error：{ret_result}")
        except Exception as err:
            err_msg = f"mes网络异常，error：{err}"

            xutil.FileUtil.write_request_log(log_save_path, err_msg)
            return self.x_response("false", err_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        user_id = data_vo.get_value_by_cons_key("user_id")
        device_id = data_vo.get_value_by_cons_key("device_id")

        # syb = data_vo.get_value_by_cons_key("syb")
        # factory_list = data_vo.get_value_by_cons_key("factory_list")
        # workstation_list = data_vo.get_value_by_cons_key("workstation_list")
        line_list_select = data_vo.get_value_by_cons_key("line_list")
        station_list = data_vo.get_value_by_cons_key("station_list")
        aws_access_key_id = data_vo.get_value_by_cons_key("aws_access_key_id")
        aws_secret_access_key = data_vo.get_value_by_cons_key("aws_secret_access_key")
        mo_list = data_vo.get_value_by_cons_key("mo_list")
        is_upload_img = data_vo.get_value_by_cons_key("is_upload_img")
        region_name = data_vo.get_value_by_cons_key("region_name")
        is_upload_ng = data_vo.get_value_by_cons_key("is_upload_ng")
        log_save_path = data_vo.get_value_by_cons_key("log_save_path", not_null=True)
        aws_endpoint_url = data_vo.get_value_by_cons_key("aws_endpoint_url")
        bucket_name_ui = data_vo.get_value_by_cons_key("bucket_name")
        is_bind_sn = data_vo.get_value_by_cons_key("is_bind_sn")
        bind_sn_url = data_vo.get_value_by_cons_key("bind_sn_url")

        cache_data = xutil.CacheUtil.get_cache_data()

        # factory_map = cache_data.get("factory_map", {})
        # workstation_map = cache_data.get("workstation_map", {})
        line_map = cache_data.get("line_map", {})
        station_map = cache_data.get("station_map", {})

        # factory_select = factory_map.get(factory_list, "")
        # workstation_select = workstation_map.get(workstation_list, "")
        line_select = line_map.get(line_list_select, "")
        station_select = station_map.get(station_list, "")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        pcb_sn = pcb_entity.pcb_barcode

        err_msg_list = []

        date_tmp = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date1 = date_tmp[:8]  # 年月日
        date2 = date_tmp[8:]  # 时分秒
        # bucket_name = f"{syb}-{factory_select}-{workstation_select}-{line_select}-{station_select}-{date1}"

        board_panel_url_list = []

        # t_review_path = pcb_entity.get_pcb_pcb_t_review_path()
        # b_review_path = pcb_entity.get_pcb_pcb_b_review_path()
        #
        # t_alg_data = get_alg_data(t_review_path)
        #
        # b_alg_data = {}
        # if b_review_path:
        #     b_alg_data = get_alg_data(b_review_path)


        if is_upload_img == "Yes":
            # 上传整板图
            upload_img_list = pcb_entity.list_all_pcb_image()
            self.log.info(f"开始上传整板图，上传数量：{len(upload_img_list)}")

            pcb_result = pcb_entity.get_repair_result("OK", "NG")

            only_one_ng_code = ""
            for board_entity in pcb_entity.yield_board_entity():
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        if not only_one_ng_code:
                            only_one_ng_code = f"_{comp_entity.repair_ng_code}"

            for pcb_src_filename in upload_img_list:
                if "/T_" in pcb_src_filename:
                    board_side = "TOP"
                else:
                    board_side = "BOT"

                dst_filename = f"{date1}/{pcb_sn}_{date2}_{board_side}_{pcb_result}{only_one_ng_code}.jpg"

                try:
                    ret_url = upload_image_to_s3_with_retries(
                        aws_endpoint_url,
                        aws_access_key_id,
                        aws_secret_access_key,
                        pcb_src_filename,
                        bucket_name_ui,
                        dst_filename,
                        region_name
                    )

                    if ret_url:
                        board_panel_url_list.append({
                            "url": ret_url
                        })

                except Exception as err:
                    err_msg_list.append(f"上传整板图失败，boardSide:{board_side}！err: {err}")

        bind_sn_map = {}

        for board_entity in pcb_entity.yield_board_entity():
            board_no = board_entity.board_no
            barcode = board_entity.barcode
            barcode_b = board_entity.b_barcode

            if not barcode and barcode_b:
                barcode = barcode_b

            # bind_sn_list.append(f"{board_no}:{barcode}")
            bind_sn_map[board_no] = barcode

        log.info(f"{bind_sn_map}")

        if is_bind_sn == "Yes":
            self.log.info(f"正在调用小板SN与载具进行绑定接口...")

            bad_data_info = pcb_entity.get_bad_data_info()

            bind_sn_list = []

            for no, is_bad in bad_data_info.items():
                if not is_bad:
                    barcode = bind_sn_map.get(no, "")
                    bind_sn_list.append(f"{no}:{barcode}")
                else:
                    bind_sn_list.append(f"{no}:ERROR")

                print("in here")

            bind_param = bind_template.format(**{
                "CARRIER_ID": pcb_sn,
                "PCB_SN": "|".join(bind_sn_list),
            })

            res_str = xrequest.RequestUtil.post_soap(
                bind_sn_url,
                bind_param,
                soap_action="WWW.SUNWODA.COM/BindCarrierAndPcb"
            )

            root = xutil.XmlUtil.get_xml_root_by_str(res_str)
            ret_str = root[0][0][0].text
            if ret_str != "TRUE":
                err_msg_list.append(f"小板SN与载具进行绑定失败，error：{ret_str}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.b_barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng() and is_upload_ng == "No":
                log.warning(f"NG数据不上传Mes！")
                continue

            time_now = datetime.now().strftime(xcons.FMT_TIME_DEFAULT)

            upload_data_item = [
                {
                    "testCode": "",  # 测试结果
                    "tetstItem": "载具条码",
                    "testValue": pcb_sn,
                    "badInfoList": [],
                },
                {
                    "testCode": "",
                    "tetstItem": "AVIResult",
                    "testValue": board_entity.get_repair_result("PASS", "FAIL"),
                    "badInfoList": [],
                },
                {
                    "testCode": "",
                    "tetstItem": "线体",
                    "testValue": line_select,
                    "badInfoList": [],
                },
                {
                    "testCode": "",
                    "tetstItem": "设备编号",
                    "testValue": device_id,
                    "badInfoList": [],
                },
                {
                    "testCode": "",
                    "tetstItem": "作业员",
                    "testValue": user_id,
                    "badInfoList": [],
                },
                {
                    "testCode": "",
                    "tetstItem": "过站时间",
                    "testValue": time_now,
                    "badInfoList": [],
                },
                {
                    "testCode": "",
                    "tetstItem": "通道号",
                    "testValue": board_no,
                    "badInfoList": [],
                },
            ]

            comp_ng_img_list = []  # 器件NG图片列表

            # def add_alg_data_to_list(alg_name_, alg_val_):
            #     upload_data_item.append({
            #         "testCode": comp_entity.designator,
            #         "tetstItem": alg_name_,
            #         "testValue": alg_val_,
            #         "badInfoList": [{
            #             "badcode": comp_entity.repair_ng_code,
            #             "badDes": f"{comp_entity.repair_ng_str}({alg_val_})",
            #             "badPostion": f"{comp_entity.designator}",
            #         }],
            #     })

            for comp_entity in board_entity.yield_comp_entity():
                #     comp_board_side = comp_entity.board_side
                #     comp_id_read = comp_entity.comp_id_real
                #
                #     if comp_board_side == "T":
                #         comp_alg_data = t_alg_data.get(comp_id_read, {}).get("alg_val", {})
                #     else:
                #         comp_alg_data = b_alg_data.get(comp_id_read, {}).get("alg_val", {})
                #
                #     for alg_name, alg_item in comp_alg_data.items():
                #
                #         if alg_name == "InspDimensionDetect":
                #             print("11111")
                #             # 尺寸检测算法
                #             alg0 = alg_item[0]
                #             insp_val = alg0.get("value", {})
                #
                #             add_alg_data_to_list(f"{alg_name}_Area", round(insp_val.get('area', 0), 2))
                #             add_alg_data_to_list(f"{alg_name}_Volume", round(insp_val.get('volume', 0), 2))
                #             add_alg_data_to_list(f"{alg_name}_Height", round(insp_val.get('height', 0), 2))
                #             add_alg_data_to_list(f"{alg_name}_Long", round(insp_val.get('long', 0), 2))
                #             add_alg_data_to_list(f"{alg_name}_Width", round(insp_val.get('width', 0), 2))
                #
                #         elif alg_name == "InspHeight":
                #             print("2222")
                #             # 高度
                #             insp0 = alg_item[0]
                #             add_alg_data_to_list(f"{alg_name}_Height", round(insp0.get('value', 0), 2))
                #
                #         elif alg_name == "InspHeightTilted":
                #             print("3333")
                #             # 高度倾斜
                #             for item in alg_item:
                #
                #                 if item.get("methodMode") == "Corner":
                #                     # 四角模式
                #                     corner_height = item.get("CornerAbsHeight", {}).get("resultValue", 0)
                #                     add_alg_data_to_list(f"{alg_name}_CornerAbsHeight", round(corner_height, 2))
                #
                #                 if item.get("methodMode") == "Cross":
                #                     # 十字模式
                #                     cross_x = item.get("CrossAbsHeightX", {}).get("resultValue", 0)
                #                     cross_y = item.get("CrossAbsHeightY", {}).get("resultValue", 0)
                #                     add_alg_data_to_list(f"{alg_name}_CrossAbsHeightX", round(cross_x, 2))
                #                     add_alg_data_to_list(f"{alg_name}_CrossAbsHeightY", round(cross_y, 2))
                #
                #         elif alg_name == "InspMeasureTwoCompPos":
                #             print("44444444")
                #             # 线特征
                #             for item in alg_item:
                #                 distance_val = item.get("distanceValue", 0)
                #                 add_alg_data_to_list(f"{alg_name}_distanceValue", round(distance_val, 2))
                #
                #         elif alg_name == "InspBilateralDistance":
                #             print("5555")
                #             # 双边距离
                #             item0 = alg_item[0]
                #
                #             distance_a = item0.get("distanceA", {})
                #             distance_b = item0.get("distanceB", {})
                #
                #             a_val = distance_a.get("value", 0)
                #             b_val = distance_b.get("value", 0)
                #
                #             add_alg_data_to_list(f"{alg_name}_distanceA", round(a_val, 2))
                #             add_alg_data_to_list(f"{alg_name}_distanceB", round(b_val, 2))

                if comp_entity.is_robot_ng():
                    comp_src_img = comp_entity.image_path
                    comp_tag = comp_entity.designator

                    if comp_src_img and is_upload_img == "Yes":
                        dst_filename = f"{date1}/{barcode}_{date2}_{board_no}_{comp_tag}_NG_{comp_entity.repair_ng_code}.png"

                        try:
                            ret_url = upload_image_to_s3_with_retries(
                                aws_endpoint_url,
                                aws_access_key_id,
                                aws_secret_access_key,
                                comp_src_img,
                                bucket_name_ui,
                                dst_filename,
                                region_name
                            )

                            if ret_url:
                                comp_ng_img_list.append({
                                    "url": ret_url
                                })

                        except Exception as err:
                            err_msg_list.append(f"上传器件图失败，位号：{comp_tag} err: {err}")

            data_param = {
                "deviceId": device_id,
                "stationId": station_select,
                "userId": user_id,
                "moNumber": mo_list,
                "pcbSN": barcode,
                "carrierSN": pcb_sn,
                "Result": board_entity.get_repair_result("PASS", "NG"),
                "bigPanelUrlList": board_panel_url_list,
                "dataList": [{
                    "smallPanelId": board_no,
                    "smallPanelTestResult": "",
                    "smallPanelUrlList": comp_ng_img_list,
                    "extend1": "",
                    "extend2": "",
                    "extend3": "",
                    "extend4": "",
                    "testItemList": upload_data_item,
                }],
            }
            log_str1 = f"接口URL：{api_url_data}  接口参数：\n{data_param}"
            xutil.FileUtil.write_request_log(log_save_path, log_str1)

            try:
                ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
                xutil.FileUtil.write_request_log(log_save_path, f"响应参数：{json.dumps(ret, ensure_ascii=False)}")

                if str(ret.get("code")) != "200":
                    err_msg_list.append(f"mes接口异常，上传过站数据失败，error：{ret.get('msg')}")
            except Exception as err:
                err_msg = f"mes网络异常，error：{err}"
                xutil.FileUtil.write_request_log(log_save_path, err_msg)
                return self.x_response("false", err_msg)

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"MES响应异常，error：{err_str}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
        device_id = btn_vo.get_value_by_cons_key("device_id")
        user_id = btn_vo.get_value_by_cons_key("user_id")
        password = btn_vo.get_value_by_cons_key("password")
        log_save_path = btn_vo.get_value_by_cons_key("log_save_path", not_null=True)

        if btn_vo.get_btn_key() == 'login_btn':
            if x_res := refresh_token(api_url_login, user_id, password, device_id, log_save_path=log_save_path):
                return x_res

        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()

        api_url_factory = combo_vo.get_value_by_cons_key("api_url_factory")
        api_url_workstation = combo_vo.get_value_by_cons_key("api_url_workstation")
        api_url_line = combo_vo.get_value_by_cons_key("api_url_line")
        api_url_station = combo_vo.get_value_by_cons_key("api_url_station")
        api_url_mo = combo_vo.get_value_by_cons_key("api_url_mo")
        device_id = combo_vo.get_value_by_cons_key("device_id")
        user_id = combo_vo.get_value_by_cons_key("user_id")

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        if combo_key == "factory_list":
            get_factory_param = {
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_factory, get_factory_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_factory_li = ret.get("factoryList", [])

            if not ret_factory_li:
                return self.x_response("false", f"未获取到工厂列表！")

            factory_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_factory_li}

            xutil.CacheUtil.set("factory_map", factory_map)

            return self.x_response("true", json.dumps({
                "new_items": list(factory_map.keys())
            }))

        elif combo_key == "workstation_list":
            factory_select = getattr(main_window, f"combo_factory_list").currentText()
            factory_sn = xutil.CacheUtil.get("factory_map", {}).get(factory_select)

            if not factory_sn:
                return self.x_response("false", "未选中工厂，请先选中工厂再获取车间！")

            get_workstation_param = {
                "factorySN": factory_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_workstation, get_workstation_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_area_list = ret.get("areaList", [])

            if not ret_area_list:
                return self.x_response("false", f"未获取到车间列表！")

            workstation_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_area_list}

            xutil.CacheUtil.set("workstation_map", workstation_map)

            return self.x_response("true", json.dumps({
                "new_items": list(workstation_map.keys())
            }))

        elif combo_key == "line_list":
            workstation_select = getattr(main_window, f"combo_workstation_list").currentText()
            workstation_sn = xutil.CacheUtil.get("workstation_map", {}).get(workstation_select)

            if not workstation_sn:
                return self.x_response("false", "未选中车间，请先选中车间再获取线体！")

            get_workstation_param = {
                "areaSN": workstation_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_line, get_workstation_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_line_list = ret.get("lineList", [])

            if not ret_line_list:
                return self.x_response("false", f"未获取到线体列表！")

            line_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_line_list}

            xutil.CacheUtil.set("line_map", line_map)

            return self.x_response("true", json.dumps({
                "new_items": list(line_map.keys())
            }))

        elif combo_key == "station_list":
            line_select = getattr(main_window, f"combo_line_list").currentText()
            line_sn = xutil.CacheUtil.get("line_map", {}).get(line_select)

            if not line_sn:
                return self.x_response("false", "未选中线体，请先选中线体再获取工站！")

            get_station_param = {
                "lineSN": line_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_station, get_station_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_station_list = ret.get("stationList", [])

            if not ret_station_list:
                return self.x_response("false", f"未获取到工站列表！")

            station_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_station_list}

            xutil.CacheUtil.set("station_map", station_map)

            return self.x_response("true", json.dumps({
                "new_items": list(station_map.keys())
            }))

        elif combo_key == "mo_list":
            station_select = getattr(main_window, f"combo_station_list").currentText()
            station_sn = xutil.CacheUtil.get("station_map", {}).get(station_select)

            if not station_sn:
                return self.x_response("false", "未选中工站，请先选中工站再获取工单！")

            get_mo_param = {
                "stationId": station_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_mo, get_mo_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_mo_list = ret.get("moList", [])

            if not ret_mo_list:
                return self.x_response("false", f"未获取到工单列表！")

            mo_map = {i.get("moNumber"): i for i in ret_mo_list}

            xutil.CacheUtil.set("mo_map", mo_map)

            return self.x_response("true", json.dumps({
                "new_items": list(mo_map.keys())
            }))

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_status = other_vo.get_value_by_cons_key("api_url_status")
        device_sequence = other_vo.get_value_by_cons_key("device_sequence", to_int=True)
        device_id = other_vo.get_value_by_cons_key("device_id")

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        status_code_v3 = other_vo.get_status_code_v3()
        x_request_device_status(api_url_status, status_code_v3, device_sequence, device_id)

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_status = other_vo.get_value_by_cons_key("api_url_status")
        device_sequence = other_vo.get_value_by_cons_key("device_sequence", to_int=True)
        device_id = other_vo.get_value_by_cons_key("device_id")

        status_code_v3 = global_data.get("last_device_code_v3")
        x_request_device_status(api_url_status, status_code_v3, device_sequence, device_id)


if __name__ == '__main__':
    ret_str1 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTestCommonResponse xmlns="WWW.SUNWODA.COM">
            <GroupTestCommonResult>string</GroupTestCommonResult>
        </GroupTestCommonResponse>
    </soap:Body>
</soap:Envelope>"""

    root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    print(root1[0][0][0].text)
