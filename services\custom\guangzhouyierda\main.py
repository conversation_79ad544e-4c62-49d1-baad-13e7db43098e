# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/1 下午15:25
# Author     ：chencb
# version    ：python 3.8
# Description：广州易而达 https://jira.cvte.com/browse/ATAOI_2019-38402
"""
import os
from typing import Any

from common import xcons
from common.xutil import log, x_response
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine
import csv


class Engine(ErrorMapEngine):
    version = {
        "title": "guangzhouyierda release v1.0.0.3",
        "device": "AIS40X",
        "feature": ["检测结果生成csv文件"],
        "author": "chenchongbing",
        "release": """
date: 2025-04-02 09:45  jira:38402 检测结果生成csv文件
date: 2025-04-02 16:30  jira:38402 检测PASS时，也需要生成csv文件
date: 2025-04-15 17:30  jira:38402 新增轨道2的文件保存路径，区分轨道发送
""",
    }

    path = {
        "csv_path_track_1": {
            "ui_name": "文件保存路径（轨道1）",
            "value": "",
        },
        "csv_path_track_2": {
            "ui_name": "文件保存路径（轨道2）",
            "value": "",
        },
    }

    form = {
        "g_machine_serial": {
            "ui_name": "机器序列号",
            "value": ""
        },
        "g_line_name": {
            "ui_name": "线名",
            "value": ""
        },
        "g_group_name": {
            "ui_name": "组名",
            "value": ""
        },
        "b_side": {
            "ui_name": "板面",
            "value": ""
        },
        "b_operator": {
            "ui_name": "作业员编号",
            "value": ""
        },
    }

    def _save_data_to_csv(self, csv_file, inspect_data):
        # 目录不存在时先创建目录
        os.makedirs(os.path.dirname(csv_file), exist_ok=True)

        h_group = ['g_machine_serial', 'g_line_name', 'g_group_name', 'g_board_name', 'g_auto_start_datetime']
        h_board = ['b_side', 'b_board_serial', 'b_operator', 'b_inspected_datetime', 'b_repaired_datetime',
                   'b_AOI_result', 'b_operator_Judgment', 'b_lotnumber', 'b_racknumber', 'b_rackcount',
                   'b_currentcount', 'b_ngcount']
        h_comp = ['c_reference_name', 'c_subboard_number', 'c_side', 'c_macro_name', 'c_libname', 'c_AOI_result',
                  'c_operator_Judgment', 'c_x', 'c_y']
        h_window = ['w_window_num', 'w_pin_num', 'w_subpin_num', 'w_AOI_result', 'w_operator_Judgment', 'w_ng_kind',
                    'w_ng_name', 'w_image', 'w_x1', 'w_y1', 'w_x2', 'w_y2', 'w_light', 'w_algnum', 'w_algname']
        headers = h_group + h_board + h_comp + h_window

        try:
            with open(csv_file, 'w', encoding='utf-8') as f:
                writer = csv.writer(f)
                # 写入表头
                writer.writerow(headers)

                row_group = []
                for g_key in h_group:
                    row_group.append(inspect_data.get(g_key, ''))

                board_data_list = inspect_data['board_data']
                for board_data in board_data_list:
                    row_board = []
                    for b_key in h_board:
                        row_board.append(board_data.get(b_key, ''))

                    comp_data_list = board_data['comp_data']
                    if comp_data_list:
                        for comp_data in comp_data_list:
                            row_comp = []
                            for c_key in h_comp:
                                row_comp.append(comp_data.get(c_key, ''))
                            for w_key in h_window:
                                row_comp.append(comp_data.get(w_key, ''))
                            # 写入数据行
                            row = row_group + row_board + row_comp
                            writer.writerow(row)
                    else:
                        # 没有器件时，值全部置为NA
                        length = len(h_comp) + len(h_window)
                        row_comp = ["NA"] * length
                        # 写入数据行
                        row = row_group + row_board + row_comp
                        writer.writerow(row)
        except Exception as e:
            return x_response('false', f'写入csv文件失败，error:{e}')

        self.log.info(f'写入数据到：{csv_file}')
        return x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        g_machine_serial = data_vo.get_value_by_cons_key('g_machine_serial')
        g_line_name = data_vo.get_value_by_cons_key('g_line_name')
        g_group_name = data_vo.get_value_by_cons_key('g_group_name')
        b_side = data_vo.get_value_by_cons_key('b_side')
        b_operator = data_vo.get_value_by_cons_key('b_operator')

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        inspect_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        repair_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_FILE)

        inspect_data = {
            'g_machine_serial': g_machine_serial,
            'g_line_name': g_line_name,
            'g_group_name': g_group_name,
            'g_board_name': pcb_entity.project_name,
            'g_auto_start_datetime': inspect_time,
            'board_data': []
        }

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_data = {
                'b_side': b_side,
                'b_board_serial': board_entity.barcode,
                'b_operator': b_operator,
                'b_inspected_datetime': inspect_time,
                'b_repaired_datetime': repair_time,
                'b_AOI_result': board_entity.get_robot_result('OK', 'NG'),
                'b_operator_Judgment': board_entity.get_repair_result('FC', 'NG'),
                'b_lotnumber': pcb_entity.order_id,
                'b_racknumber': '',  # 未知含义，填空
                'b_rackcount': '',  # 未知含义，填空
                'b_currentcount': '',  # 未知含义，填空
                'b_ngcount': board_entity.comp_robot_ng_number,
                'comp_data': [],
            }

            inspect_data['board_data'].append(board_data)

            # 板检测PASS，不用生成器件数据
            if not board_entity.is_robot_ng():
                continue

            for comp_entity in board_entity.yield_comp_entity():
                # 板检测PASS，不用生成数据
                if not comp_entity.is_robot_ng():
                    continue

                result_map = {True: 'OK', False: 'NG'}
                robot_result = result_map.get(comp_entity.robot_result)
                result_map = {True: 'FC', False: 'NG'}
                repair_result = result_map.get(comp_entity.repair_result)

                comp_data = {
                    'c_reference_name': comp_entity.designator,
                    'c_subboard_number': board_entity.board_no,
                    'c_side': b_side,
                    'c_macro_name': comp_entity.part,
                    'c_libname': comp_entity.package,
                    'c_AOI_result': robot_result,
                    'c_operator_Judgment': repair_result,
                    'c_x': comp_entity.x_pos,
                    'c_y': comp_entity.y_pos,
                }

                w_algname = ''
                ng_code = comp_entity.robot_ng_code
                for alg_entity in comp_entity.yield_alg_entity():
                    if alg_entity.result == ng_code:
                        w_algname = alg_entity.test_name
                        break

                w_x1 = float(comp_entity.x_pos) - float(comp_entity.width) / 2
                w_x2 = float(comp_entity.x_pos) + float(comp_entity.width) / 2
                w_y1 = float(comp_entity.y_pos) - float(comp_entity.height) / 2
                w_y2 = float(comp_entity.y_pos) + float(comp_entity.height) / 2

                window_data = {
                    'w_window_num': comp_entity.comp_id_real,
                    'w_pin_num': '',  # 不处理，传空
                    'w_subpin_num': '',  # 不处理，传空
                    'w_AOI_result': robot_result,
                    'w_operator_Judgment': repair_result,
                    'w_ng_kind': comp_entity.robot_ng_code,
                    'w_ng_name': comp_entity.robot_ng_str,
                    'w_image': comp_entity.image_path,
                    'w_x1': w_x1,
                    'w_y1': w_y1,
                    'w_x2': w_x2,
                    'w_y2': w_y2,
                    'w_light': '',  # 未知，传空
                    'w_algnum': len(comp_entity.alg_data),
                    'w_algname': w_algname,
                }

                # 统一记录在器件数据里
                comp_data.update(window_data)
                board_data['comp_data'].append(comp_data)

        pcb_sn = pcb_entity.pcb_barcode
        file_name = (pcb_sn if pcb_sn else inspect_time) + '.csv'

        if pcb_entity.track_index == 1:
            csv_path = data_vo.get_value_by_cons_key('csv_path_track_1', not_null=True)
        else:
            csv_path = data_vo.get_value_by_cons_key('csv_path_track_2', not_null=True)
        csv_file = os.path.join(csv_path, file_name)
        ret = self._save_data_to_csv(csv_file, inspect_data)
        return ret
