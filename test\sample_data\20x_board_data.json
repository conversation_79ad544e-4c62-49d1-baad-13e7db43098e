{"0": {"barcode": "", "board_no": "0", "robot_result": false, "user_result": false, "final_result": "NG", "comp_total_number": 16, "comp_robot_ng_number": 1, "comp_repair_ng_number": 1, "comp_data": {"101": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "101", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "102": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "102", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "103": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "103", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "104": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "104", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "105": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "105", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "106": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "106", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "107": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "107", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "108": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "108", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "113": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "113", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "114": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "114", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "115": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "115", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "116": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "116", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "117": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "117", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "118": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "118", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "119": {"part": "-1", "image_path": "", "robot_ng_str": "OK", "type": "其他器件", "designator": "119", "robot_ng_code": "0", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "120": {"part": "-1", "image_path": "/home/<USER>/aoi/aoi_ec_result/904_110/checked/20221126-043830_id2.1_NG/wrong/regular_0.position_1.id_120.index_0.status_1.barcode_.jpg", "robot_ng_str": "漏件", "type": "其他器件", "designator": "120", "robot_ng_code": "1", "package": "", "x_offset": null, "y_offset": null, "x_pos": null, "y_pos": null, "alg_data": [], "repair_ng_str": "漏件", "repair_ng_code": "1", "repair_auto_ng": null, "repair_auto_ng_code": null, "repair_checked_flag": null, "final_result": "NG"}}}}