"""
说明，执行订阅之前，先搭建和启动broker服务：
1. 安装 Mosquitto：
   sudo apt update
   sudo apt install mosquitto mosquitto-clients
2. 启动 Mosquitto 服务：
   sudo systemctl start mosquitto
3. 查看是否已正常启动
   sudo systemctl status mosquitto
"""

import paho.mqtt.client as mqtt

# MQTT Broker 配置信息，测试时需要填写为自己配置的信息
broker = "127.0.0.1"
port = 1883
client_id = "friendcom_subscriber001"  # 这个可以固定，只要不和发布者client_id冲突即可
username = ''
password = ''
topic = "/yxd/zk/device001/update"  # 订阅的主题，可以使用通配符（如 "+/topic" 或 "#"）

# 创建客户端实例
client = mqtt.Client(
    callback_api_version=mqtt.CallbackAPIVersion.VERSION2,
    client_id=client_id
)
client.enable_logger()

# 设置用户名和密码（如果有）
if username and password:
    client.username_pw_set(username, password)


# 连接回调函数
def on_connect(client, userdata, flags, rc, properties=None):
    if rc == 0:
        print("MQTT 连接成功")
        # 连接成功后订阅主题
        client.subscribe(topic)
        print(f"已订阅主题: '{topic}'")
    else:
        print(f"MQTT 连接失败，错误码: {rc}")


# 断开连接回调
def on_disconnect(client, userdata, rc):
    print("MQTT 已断开连接")


# 消息接收回调
def on_message(client, userdata, msg):
    # 打印接收到的消息
    print(f"收到来自主题 '{msg.topic}' 的消息: {msg.payload.decode()}")


# 绑定回调函数
client.on_connect = on_connect
client.on_disconnect = on_disconnect
client.on_message = on_message

try:
    # 连接到 MQTT Broker
    client.connect(broker, port=port, keepalive=60)
    # 开始网络循环（阻塞模式，会一直运行并处理网络流量）
    client.loop_forever()

except KeyboardInterrupt:
    print("用户中断，程序退出")
    client.disconnect()
except Exception as e:
    print(f"发生异常: {e}")
    client.disconnect()
