# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/3 上午9:30
# Author     ：sch
# version    ：python 3.8
# Description：深圳金百泽
"""
from datetime import datetime
from typing import Any

from common import xutil, xcons, xrequest
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenjinbaize release v1.0.0.3",
        "device": "203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-02 09:30  init
date: 2025-03-01 11:33  jira:30466,条码为空时，整板条码传时间戳，拼板条码传时间戳_拼板序号
date: 2025-03-06 15:14  jira:30466,单拼版时，拼版条码不用加拼版号
""", }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/API/V1.ashx",
        }
    }

    form = {
        "station": {
            "ui_name": "机台号",
            "value": "AOI001",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "PreAOI-LeiChen",
        },
    }

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        station = other_vo.get_value_by_cons_key("station")

        status_str = other_vo.get_device_status_str()

        if status_str == "开始检测":
            state = "Run"
        elif status_str == "停止检查":
            state = "Stop"
        else:
            self.log.warning(f"其他设备状态：{status_str}，暂不上传到MES！")
            return self.x_response()

        body_param = {
            "Station": station,
            "State": state,
            "ErrorCode": "",
            "ErrorDescription": "",
            "DateTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
        }

        query_param = {
            "action": "DIP_AOI_StationStateSave",
            "Token": "5ed1facb-b866-4cc6-a240-f5f25b397a6f",
        }
        ret = xrequest.RequestUtil.post_json(api_url, body_param, params=query_param)
        if str(ret.get("ReturnCode")) != "1":
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('ReturnMsg')}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        station = other_vo.get_value_by_cons_key("station")

        query_param = {
            "action": "API_IsExecute",
            "Token": "5ed1facb-b866-4cc6-a240-f5f25b397a6f",
        }

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            body_param = {
                "SN": sn,
                "Station": station
            }

            ret = xrequest.RequestUtil.post_json(api_url, body_param, params=query_param)
            if str(ret.get("ReturnCode")) != "1":
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('ReturnMsg')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        station = data_vo.get_value_by_cons_key("station")
        device_name = data_vo.get_value_by_cons_key("device_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        pcb_sn = pcb_entity.pcb_barcode if pcb_entity.pcb_barcode else datetime.now().strftime('%Y%m%d%H%M%S')

        pcb_final_result = pcb_entity.get_final_result()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_no = board_entity.board_no

            if str(board_no) == "0":
                barcode = board_entity.barcode if board_entity.barcode else datetime.now().strftime('%Y%m%d%H%M%S')
            else:
                barcode = board_entity.barcode if board_entity.barcode else f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{board_no}"


            f1 = board_entity.get_final_result()

            if f1 in ["PASS", "REPASS"]:
                pcb_final_result = f1

            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        pcb_data = {
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_final_result,
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        query_param = {
            "action": "DIP_AOI_Save",
            "Token": "5ed1facb-b866-4cc6-a240-f5f25b397a6f",
        }

        self.log.warning(f"整板的`pcb_final_result`参数有特殊要求！请参考需求文档！")
        ret = xrequest.RequestUtil.post_json(api_url, pcb_data, params=query_param)
        if str(ret.get("ReturnCode")) != "1":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('ReturnMsg')}")

        return self.x_response()
