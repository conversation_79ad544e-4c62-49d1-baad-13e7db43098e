# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : error_code_map_widget.py
# Time       ：2023/11/27 上午11:40
# Author     ：sch
# version    ：python 3.8
# Description：设置-自定义不良代码
"""
import os

from PyQt5 import QtCore, QtWidgets
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import *

from common import xutil, xconfig
from common.xutil import log
from engine.DataEngine import data_engine
from templates.error_code_map_widget import Ui_ErrorCodeWidget


class CheckBoxCellWidget(QWidget):
    def __init__(self, check_flag: bool = True):
        super().__init__()

        self.check_flag = check_flag

        self.checkbox = QCheckBox()
        self.checkbox.setChecked(check_flag)

        self.layout = QVBoxLayout()
        self.layout.addWidget(self.checkbox)
        self.layout.setAlignment(Qt.AlignCenter)  # 设置居中对齐
        self.layout.setContentsMargins(0, 0, 0, 0)  # 设置外边距为0，确保居中效果更好

        self.setLayout(self.layout)  # 设置外部布局，确保复选框居中显示

    def set_check_flag(self, check_flag: bool):
        self.checkbox.setChecked(check_flag)

    def get_current_flag(self):
        return self.checkbox.isChecked()


class ErrorCodeWidget(QWidget, Ui_ErrorCodeWidget):
    def __init__(self, main_window):
        super(ErrorCodeWidget, self).__init__()
        self.setupUi(self)

        self.combo_device_mode.activated.connect(self.device_mode_on_activated)

        self.btn_save.clicked.connect(self.btn_save_on_clicked)
        self.btn_import_config.clicked.connect(self.btn_import_config_on_clicked)
        self.btn_output_config.clicked.connect(self.btn_output_config_on_clicked)

        self.error_table_widget.verticalHeader().setHidden(True)
        self.center()

        self.main_window = main_window

    def center(self):
        """
        屏幕居中
        :return:
        """
        # 获取屏幕坐标系
        screen = QDesktopWidget().screenGeometry()

        # 获取窗口坐标系
        size = self.geometry()  # noqa

        new_left = (screen.width() - size.width()) / 2
        new_top = (screen.height() - size.height()) / 2

        self.move(int(new_left), int(new_top))  # noqa

    def add_row_item(
            self, device_mode: str,
            default_code: str,
            default_str: str,
            custom_code: str,
            custom_str: str,
            upload_mes: bool = True,
            check_flag2: bool = False,
            check_flag3: bool = False,
    ):
        _translate = QtCore.QCoreApplication.translate

        row_index = self.error_table_widget.rowCount()  # 获取当前行数并加1
        self.error_table_widget.insertRow(row_index)  # 在指定行索引处插入一行

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", device_mode))
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        self.error_table_widget.setItem(row_index, 0, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", default_code))
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        self.error_table_widget.setItem(row_index, 1, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", default_str))
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        self.error_table_widget.setItem(row_index, 2, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", custom_code))
        self.error_table_widget.setItem(row_index, 3, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", custom_str))
        self.error_table_widget.setItem(row_index, 4, item)

        checkbox = CheckBoxCellWidget(upload_mes)
        self.error_table_widget.setCellWidget(row_index, 5, checkbox)

        checkbox = CheckBoxCellWidget(check_flag2)
        self.error_table_widget.setCellWidget(row_index, 6, checkbox)

        checkbox = CheckBoxCellWidget(check_flag3)
        self.error_table_widget.setCellWidget(row_index, 7, checkbox)

        self.error_table_widget.resizeColumnToContents(0)  # 调整第1列的宽度
        self.error_table_widget.resizeColumnToContents(1)  # 调整第2列的宽度
        self.error_table_widget.resizeColumnToContents(2)  # 调整第3列的宽度
        self.error_table_widget.resizeColumnToContents(3)  # 调整第4列的宽度
        self.error_table_widget.resizeColumnToContents(4)  # 调整第5列的宽度
        self.error_table_widget.resizeColumnToContents(5)  # 调整第6列的宽度
        self.error_table_widget.resizeColumnToContents(6)  # 调整第7列的宽度
        self.error_table_widget.resizeColumnToContents(7)  # 调整第8列的宽度

    def show_widget(self):
        if not self.main_window.can_custom_error_code:
            # 不可以定制不良代码
            self.main_window.non_develop_message_on_click()
            return

        select_device = self.main_window.config_data.get("app_setting", {}).get("error_code_device", "")

        if not select_device or not data_engine.exist_error_code_map():
            # 不可以定制不良代码
            self.main_window.non_develop_message_on_click()
            return

        self.combo_device_mode.setCurrentText(select_device)
        self.refresh_table(select_device)

        self.show()

    def refresh_table(self, device_mode: str = ""):
        """
        切换机型
        :return:
        """
        if not device_mode:
            device_mode = self.combo_device_mode.currentText()

        error_map = data_engine.get_all_error_code_map()
        error_info = error_map.get(device_mode, {})
        if not error_info:
            log.warning(f"未获取到该机型[{device_mode}]的映射文件！")
            return

        self.error_table_widget.clear()
        self.error_table_widget.setRowCount(0)

        _translate = QtCore.QCoreApplication.translate

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "机型"))
        self.error_table_widget.setHorizontalHeaderItem(0, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "默认Code"))
        self.error_table_widget.setHorizontalHeaderItem(1, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "默认描述"))
        self.error_table_widget.setHorizontalHeaderItem(2, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "自定义Code"))
        self.error_table_widget.setHorizontalHeaderItem(3, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "自定义描述"))
        self.error_table_widget.setHorizontalHeaderItem(4, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "整体上传Mes"))
        self.error_table_widget.setHorizontalHeaderItem(5, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "单个上传Mes"))
        self.error_table_widget.setHorizontalHeaderItem(6, item)

        item = QtWidgets.QTableWidgetItem()
        item.setText(_translate("ErrorCodeWidget", "配置项3"))
        self.error_table_widget.setHorizontalHeaderItem(7, item)

        for default_code, data in error_info.items():
            self.add_row_item(device_mode,
                              default_code,
                              data.get('standard'),
                              data.get('custom_code'),
                              data.get('custom_str'),
                              data.get('upload_mes', True),
                              data.get('check_flag2', True),
                              data.get('check_flag3', False),
                              )

    def device_mode_on_activated(self):
        """
        切换机型
        :return:
        """
        device_mode = self.combo_device_mode.currentText()
        self.refresh_table(device_mode)

    def btn_save_on_clicked(self):
        """
        保存按钮
        :return:
        """
        device_mode = self.combo_device_mode.currentText()
        log.info(f"将保存机型[{device_mode}]的不良映射！")

        row_count = self.error_table_widget.rowCount()

        new_config = {}
        for i in range(0, row_count):
            col1 = self.error_table_widget.item(i, 1)
            col2 = self.error_table_widget.item(i, 2)
            col3 = self.error_table_widget.item(i, 3)
            col4 = self.error_table_widget.item(i, 4)
            upload_mes = self.error_table_widget.cellWidget(i, 5)
            check_flag2 = self.error_table_widget.cellWidget(i, 6)
            check_flag3 = self.error_table_widget.cellWidget(i, 7)

            new_config[col1.text()] = {
                "standard": col2.text(),
                "custom_code": col3.text(),
                "custom_str": col4.text(),
                "upload_mes": upload_mes.get_current_flag(),
                "check_flag2": check_flag2.get_current_flag(),
                "check_flag3": check_flag3.get_current_flag(),
            }

        data_engine.set_error_code_map(device_mode, new_config)

        # 选中的机型保存到app_setting里
        config_data = self.main_window.config_data
        app_setting = config_data.get('app_setting')
        app_setting['error_code_device'] = device_mode

        self.main_window.config_data['app_setting'] = app_setting
        self.main_window.save_config_data_to_file()

        QMessageBox.information(self, "提示", f"保存成功！")  # noqa

    def btn_import_config_on_clicked(self):
        """
        导入配置文件
        :return:
        """
        fname = QFileDialog.getOpenFileName(self, 'Open file', '.', 'Text files (*.json)')

        import_file_path = ""
        if fname:
            import_file_path = fname[0]

        if import_file_path and os.path.exists(import_file_path):
            new_settings_data = xutil.FileUtil.load_json_file(import_file_path)
            data_engine.set_all_error_code_map(new_settings_data)
            self.device_mode_on_activated()
        else:
            log.warning(f"导入配置文件失败，找不到该文件:{import_file_path}")

    def btn_output_config_on_clicked(self):
        """
        导出配置文件
        :return:
        """
        output_filepath = f"{xconfig.home_dir}/mes_error_code_map.json"
        output_data = data_engine.get_all_error_code_map()
        xutil.FileUtil.dump_json_to_file(output_filepath, output_data)
        QMessageBox.information(self, "提示", f"文件已导出至：{output_filepath}")  # noqa
