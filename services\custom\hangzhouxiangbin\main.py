# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/27 上午11:23
# Author     ：sch
# version    ：python 3.8
# Description：杭州湘滨
"""

from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "hangzhouxiangbin release v1.0.0.4",
        "device": "303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-27 11:24  条码校验，上传数据
date: 2024-08-28 15:00  兼容接口返回参数
date: 2025-05-13 10:00  ATAOI_2019-39455：增加新冗余线选项，与前一版本做区分
date: 2025-05-13 10:00  ATAOI_2019-39455：新冗余线接口的条码需要兼容处理 :条码中的*号需要替换成 -
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(流程检查)",
            "value": "http://**************:9210/webService/DeviceOutBoundFlowCheck",
        },
        "api_url_data": {
            "ui_name": "接口URL(过站上传)",
            "value": "http://**************:9210/webService/checkDeviceOutBound",
        },
        "new_api_url_data": {
            "ui_name": "新冗余线接口URL(过站上传)",
            "value": "http://**************:9210/webService/redundantAoiDeviceUnbind",
        },
    }

    form = {
        "workstation_sn": {
            "ui_name": "工位编码",
            "value": "SMT-AOI",
        },
        "operator": {
            "ui_name": "登录账号",
            "value": "admin",
        },
    }
    combo = {
        "is_new_redundancy": {
            "ui_name": "是否新冗余线",
            "item": ["是", "否"],
            "value": "否"
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        workstation_sn = other_vo.get_value_by_cons_key("workstation_sn")

        check_sn = []
        for sn in other_vo.list_sn():
            check_sn.append({
                "PRODUCE_SN": sn
            })

        check_param = {
            "DETAIL": check_sn,
            "WORK_STATION_SN": workstation_sn
        }

        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
        if ret.get("resultFlag") != "OK":
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('errorMsg')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        workstation_sn = data_vo.get_value_by_cons_key("workstation_sn")
        operator = data_vo.get_value_by_cons_key("operator")
        is_new_redundancy = data_vo.get_value_by_cons_key("is_new_redundancy")
        # 是否新冗余线
        if is_new_redundancy == "是":
            return self._send_data_to_mes_is_new_redundancy(data_vo, other_data, other_param)
        else:
            pcb_entity = data_vo.pcb_entity
            self.log.info(pcb_entity)

            board_data = []
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)

                board_data.append({
                    "PRODUCE_SN": board_entity.barcode,
                    "RESULT": board_entity.get_repair_result("OK", "NG")
                })

            data_param = {
                "WORK_STATION_SN": workstation_sn,
                "OPERATOR": operator,
                "TEST_DATE_TIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "DETAIL": board_data,
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if ret.get("resultFlag") != "OK":
                return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('errorMsg')}")

            return self.x_response()

    def _send_data_to_mes_is_new_redundancy(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        new_api_url_data = data_vo.get_value_by_cons_key("new_api_url_data")
        workstation_sn = data_vo.get_value_by_cons_key("workstation_sn")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_data = []
        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            self.log.info(board_entity)

            board_data.append({
                "KEYP_SN": barcode.replace("*", "-"),  # 客户条码中的*号需要替换成 -
                "RESULT": board_entity.get_repair_result("OK", "NG")
            })

        data_param = {
            "WORK_STATION_SN": workstation_sn,
            "PRODUCE_SN": pcb_sn.replace("*", "-"),  # 客户条码中的*号需要替换成 -
            "OPERATOR": operator,
            "TEST_DATE_TIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "DETAIL": board_data,
        }

        ret = xrequest.RequestUtil.post_json(new_api_url_data, data_param)
        if str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()
