# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/11/14 下午2:22
# Author     ：sch
# version    ：python 3.8
# Description：深圳行之成
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenxingzhicheng release v1.0.0.2",
        "device": "AIS203，AIS303，AIS40X，AIS43X",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-11-14 14:23  从mes获取条码, 条码校验, 上传数据
date: 2024-11-15 14:51  修改请求参数
""", }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "",
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
    }

    form = {
        "source1": {
            "ui_name": "岗位资源编号(1轨)",
            "value": "",
        },
        "source2": {
            "ui_name": "岗位资源编号(2轨)",
            "value": "",
        },
        "operator1": {
            "ui_name": "操作人员用户代码(1轨)",
            "value": "",
        },
        "operator2": {
            "ui_name": "操作人员用户代码(2轨)",
            "value": "",
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")

        get_sn_param = {
            "sn": other_vo.get_pcb_sn()
        }

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, get_sn_param)

        if str(ret.get("code")) != "0":
            return self.x_response("false", f"mes接口异常，error:{ret.get('message')}")

        ret_sn = []

        for item in ret.get("data"):
            ret_sn.append(item.get('SERIALNO'))

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")

        track_index = other_vo.get_track_index()

        if track_index == 1:
            source = other_vo.get_value_by_cons_key("source1")
        else:
            source = other_vo.get_value_by_cons_key("source2")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "type": "CheckRoutePassed",
                "detail": {
                    "iSN": sn,
                    "iResCode": source
                }
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
            if str(ret.get("code")) != "0":
                ret_res = self.x_response("false", f"mes接口异常，error:{ret.get('message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.track_index

        if track_index == 1:
            source = data_vo.get_value_by_cons_key("source1")
            operator = data_vo.get_value_by_cons_key("operator1")
        else:
            source = data_vo.get_value_by_cons_key("source2")
            operator = data_vo.get_value_by_cons_key("operator2")

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_ng_list.append(comp_entity.repair_ng_code)

            data_param = {
                "type": "SetPcbaData",
                "detail": {
                    "iSN": barcode,
                    "iResCode": source,
                    "iOperater": operator,
                    "iResult": board_entity.get_repair_result("OK", "NG"),
                    "iErrCode": ",".join(comp_ng_list),
                }
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if str(ret.get("code")) != "0":
                ret_res = self.x_response("false", f"mes接口异常，error:{ret.get('message')}")

        return ret_res
