# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/9 下午3:03
# Author     ：sch
# version    ：python 3.8
# Description：鸿帆
"""

from typing import Any

from common import xrequest, xcons
from common.xglobal import global_data
from common.xutil import x_response
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


def x_refresh_token(api_url_login, username, password):
    """
    刷新token
    :param api_url_login:
    :param username:
    :param password:
    :return:
    """
    query_param = {
        "userName": username,
        "password": password,
        "isPersisitent": "true"
    }

    ret = xrequest.RequestUtil.post_json(api_url_login, {}, params=query_param)
    if not ret.get('Success'):
        return x_response("false", f"mes接口异常，获取token失败，error：{ret.get('Message')}")

    token = ret.get("Result")
    if not token:
        return x_response("false", f"mes接口异常，获取到的token为空！")

    global_data["token"] = token
    return None


class Engine(ErrorMapEngine):
    version = {
        "title": "hongfan release v1.0.0.1",
        "device": "AIS303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-09 15:03  上传数据
""", }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(获取Token)",
            "value": "http://127.0.0.1:8081/api/UserApi/Login",
        },
        "api_url_data": {
            "ui_name": "接口URL(提交检测结果)",
            "value": "http://127.0.0.1:8081/api/M3Logic/Execute/SubmitInspectionResults",
        },
    }

    form = {
        "username": {
            "ui_name": "用户名",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "workstation": {
            "ui_name": "工位编号",
            "value": "",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 15 * 60)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_login = data_vo.get_value_by_cons_key("api_url_login")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        username = data_vo.get_value_by_cons_key("username")
        password = data_vo.get_value_by_cons_key("password")
        workstation = data_vo.get_value_by_cons_key("workstation")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        token = global_data.get("token")
        if not token:
            if res := x_refresh_token(api_url_login, username, password):
                return res

        token = global_data.get("token")
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        headers = {
            "Ticket": token
        }

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_result = board_entity.get_repair_result(1, 2)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = [
                workstation,
                start_time,
                board_result,
                barcode
            ]

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_list.append({
                    "Category": comp_entity.part,
                    "PointName": comp_entity.type,
                    "ParamaterName": comp_entity.designator,
                    "StandardValue": "",
                    "ActualValue": "",
                    "Message": comp_entity.repair_ng_str,
                    "Remark": "",
                    "Result": comp_entity.get_final_result(1, 1, 2)
                })

            ret = xrequest.RequestUtil.post_json(api_url_data, comp_data_list, headers=headers)

            if not ret.get("Success"):
                err_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('Message')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口响应异常，{err_str}")

        return self.x_response()
