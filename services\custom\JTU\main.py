# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/03/13 下午15:09
# Author     ：gyr
# version    ：python 3.8
# Description：日本大金
"""
from datetime import datetime
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

template_a = """Program name,{project_name}
Test time,{test_time}
Operator,{operator}
Barcode,{barcode}
Cavity number,{cavity_number}
Cavity judgement number,{cavity_judgment_number}
Total inspection point,{total_inspection_point}
Total judgement point,{total_judgement_point}

BoardNo,Barcode,BoardFinalResult,CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult{comp_data_str}
"""

template_b = """Date,time,program name,barcode,cavity,result"""

template_comp_row = """
{BoardNo},{Barcode},{BoardFinalResult},{CompDesignator},{CompPart},{CompPackage},{CompType},{CompRobotCode},{CompRobotResult},{CompUserCode},{CompUserResult}"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["日本大金", "JTU"],
        "version": "release v1.0.0.3",
        "device": "AIS401,AIS430,AIS501",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-03-12 15:10  jira:37837,上传数据
date: 2025-03-21 10:02  jria:38108,修改设备名称,删除csv中文名称
date: 2025-05-06 17:41  jria:38108,修改文件命名方式
""", }

    path = {
        "save_path_a": {
            "ui_name": "保存路径A",
            "ui_name_en": "SavePathA",
            "value": "",
        },
        "save_path_b": {
            "ui_name": "保存路径B",
            "ui_name_en": "SavePathB",
            "value": "",
        }
    }

    form = {
        "device_name": {
            "ui_name": "Device",
            "value": ""
        }
    }

    def __init__(self):
        super().__init__()

        # 初始化字典,用于存储不同的流水号
        self._serial_numbers = {}
        # 初始化日期,用于记录上一次流水号的日期
        self._last_date = None

    def _get_serial_number(self, project_name: str) -> str:
        """
        获取生产流水号
        """

        # 获取当前日期
        current_date = datetime.now().date()

        # 日期变化 则重置所有流水号
        if self._last_date != current_date:
            # 清空流水号字典
            self._serial_numbers = {}
            # 更新日期为当前日期
            self._last_date = current_date

        # 检查该项目是否已有流水号，如果是新项目则初始化为1
        if project_name not in self._serial_numbers:
            self._serial_numbers[project_name] = 1

        # 将当前流水号格式化为4位数的字符串（如 0001,0002,0003）
        serial_number = f"{self._serial_numbers[project_name]:04d}"
        # 流水号+1
        self._serial_numbers[project_name] += 1

        return serial_number

    # 以下内容基于taxan客户做修改
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_a = data_vo.get_value_by_cons_key("save_path_a")
        save_path_b = data_vo.get_value_by_cons_key("save_path_b")
        device_name = data_vo.get_value_by_cons_key("device_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        cavity_judgment_number = 0
        total_inspection_point = 0
        total_judgement_point = 0

        comp_data_str = ""

        t3 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT9)
        project_name = pcb_entity.project_name

        # 获取生产流水号
        serial_number = self._get_serial_number(project_name)

        b_content = template_b
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_no = board_entity.board_no
            barcode = board_entity.barcode

            if board_entity.is_robot_ng():
                cavity_judgment_number += 1

            total_inspection_point += board_entity.comp_total_number
            total_judgement_point += board_entity.comp_robot_ng_number

            result = board_entity.get_final_result()

            b_content += f"\n{t3[:10]},{t3[11:]},{project_name},{barcode},{board_no},{result}"

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += template_comp_row.format(**{
                    "BoardNo": board_no,
                    "Barcode": barcode,
                    "BoardFinalResult": board_entity.get_final_result(),
                    "CompDesignator": comp_entity.designator,
                    "CompPart": comp_entity.part,
                    "CompPackage": comp_entity.package,
                    "CompType": comp_entity.type,
                    "CompRobotCode": comp_entity.robot_ng_code,
                    "CompRobotResult": comp_entity.robot_ng_str,
                    "CompUserCode": comp_entity.repair_ng_code,
                    "CompUserResult": comp_entity.repair_ng_str
                })

        content_a = template_a.format(**{
            "project_name": project_name,
            "test_time": t3,
            "operator": pcb_entity.repair_user,
            "barcode": pcb_entity.pcb_barcode,
            "cavity_number": pcb_entity.board_count,
            "cavity_judgment_number": cavity_judgment_number,
            "total_inspection_point": total_inspection_point,
            "total_judgement_point": total_judgement_point,
            "comp_data_str": comp_data_str,
        })

        check_date = pcb_entity.get_start_time().strftime("%Y%m%d")
        check_time = pcb_entity.get_start_time().strftime("%H%M%S")
        barcode = pcb_entity.pcb_barcode
        result = pcb_entity.get_final_result("OK", "OK", "NG")

        file_name = f"{project_name}_{check_date}_{check_time}_{barcode}_{serial_number}_{device_name}_{result}"

        a_filepath = f"{save_path_a}/{file_name}.csv"
        content_a = content_a.replace("\n", "\r\n")
        xutil.FileUtil.write_content_to_file(a_filepath, content_a)

        b_filepath = f"{save_path_b}/{file_name}.csv"
        b_content = b_content.replace("\n", "\r\n")
        xutil.FileUtil.write_content_to_file(b_filepath, b_content)

        return self.x_response()
