# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/04/16 10:36
# Author     ：chencb
# version    ：python 3.8
# Description：苏州峻凌 https://jira.cvte.com/browse/ATAOI_2019-38784
"""
from typing import Any
from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "suzhoujunling release v1.0.0.4",
        "device": "",
        "feature": ["条码校验，发送数据"],
        "author": "chenchongbing",
        "release": """
date: 2025-04-16 15:00  jira->ATAOI_2019-38784: 条码校验，发送数据
date: 2025-04-16 21:15  jira->ATAOI_2019-38784: 增加前端配置项：载具条码、治具编号、设备程式名、设备程式名版本
date: 2025-04-17 16:30  拼板条码从条码校验返回的BARCODELIST中取值
date: 2025-07-21 15:43  增加生产模式选择：板边码生产模式和载具码生产模式
""", }

    combo = {
        "product_mode": {
            "ui_name": "生产模式",
            "item": ["板边码生产模式", "载具码生产模式"],
            "value": "板边码生产模式"
        },
    }

    form = {
        "part_no": {
            "ui_name": "机种料号",
            "value": "",
        },
        "station": {
            "ui_name": "站位",
            "value": "",
        },
        "jig_no": {
            "ui_name": "治具编号",
            "value": "",
        },
        "line_no": {
            "ui_name": "线体",
            "value": "",
        },
        "machine_no": {
            "ui_name": "设备编号",
            "value": "",
        },
        "sfc": {
            "ui_name": "SFC账号",
            "value": "",
        },
        "progrom_name": {
            "ui_name": "设备程式名",
            "value": "",
        },
        "progrom_ver": {
            "ui_name": "设备程式名版本",
            "value": "",
        },
        "check_url": {
            "ui_name": "条码校验URL",
            "value": "",
        },
        "data_url": {
            "ui_name": "发送数据URL",
            "value": "",
        },
    }

    def __init__(self):
        self.board_sn_dict = {}

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        check_url = other_vo.get_value_by_cons_key("check_url", not_null=True)
        part_no = other_vo.get_value_by_cons_key("part_no")
        station = other_vo.get_value_by_cons_key("station")
        line_no = other_vo.get_value_by_cons_key("line_no")
        machine_no = other_vo.get_value_by_cons_key("machine_no")
        product_mode = other_vo.get_value_by_cons_key("product_mode")
        jig_no = other_vo.get_value_by_cons_key("jig_no")
        progrom_name = other_vo.get_value_by_cons_key("progrom_name")
        progrom_ver = other_vo.get_value_by_cons_key("progrom_ver")

        barcode_map = other_vo.get_barcode_map()
        plate_code = barcode_map.get('-1', '')  # 板边条码
        loader_board = barcode_map.get('-2', '')  # 治具条码

        if product_mode == "板边码生产模式":
            loader_board = ""
        else:
            plate_code = ""

        # 如果两个条码都为空，则去取拼板条码
        if not plate_code and not loader_board:
            self.log.info("板边和治具条码都为空")
            sn_list = other_vo.list_sn()
            for sn in sn_list:
                plate_code = sn
                self.log.info("把拼板条码赋值给板边条码")
                break

        param = {
            "PARTNO": part_no,
            "PLATECODE": plate_code,
            "LOADERBOARD": loader_board,
            "STATION": station,
            "JIGNO": jig_no,
            "LINENO": line_no,
            "MACHINE": machine_no,
            "PROGROMNAME": progrom_name,
            "PROGROMVERSION": progrom_ver
        }
        try:
            ret = xrequest.RequestUtil.post_json(check_url, param)
            # {
            # "Result":"OK/NG",
            # "BARCODELIST":[{"NUM": "1",
            #                 "BARCODE": "TSMTTEST0001",
            #                 "RESULT":"OK/NG"}]
            # }
            # 解析拼板条码并缓存
            if ret.get("Result") == 'NG':
                return self.x_response("false", f"条码校验失败，error：{ret.get('Message')}")
            else:
                barcode_list = ret.get("BARCODELIST")
                for board in barcode_list:
                    # 如果取不到key值，赋值为-1,以便后面取拼板条码时置空
                    key = board.get('NUM', -1)
                    value = board.get('BARCODE', '')
                    self.board_sn_dict.update({str(key): value})

                return self.x_response()
        except Exception as e:
            return self.x_response("false", f"本地网络异常，error：{e}")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_url = data_vo.get_value_by_cons_key("data_url", not_null=True)
        part_no = data_vo.get_value_by_cons_key("part_no")
        station = data_vo.get_value_by_cons_key("station")
        line_no = data_vo.get_value_by_cons_key("line_no")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        sfc = data_vo.get_value_by_cons_key("sfc")
        product_mode = data_vo.get_value_by_cons_key("product_mode")
        jig_no = data_vo.get_value_by_cons_key("jig_no")
        progrom_name = data_vo.get_value_by_cons_key("progrom_name")
        progrom_ver = data_vo.get_value_by_cons_key("progrom_ver")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # 格式：不良位置1;不良代码1|不良位置2;不良代码2|
            ng_code = ''
            if board_entity.is_repair_ng():
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        ng_code += f'{comp_entity.designator};{comp_entity.robot_ng_code}|'

            board_no = board_entity.board_no
            if board_no == '0':  # 整板情况
                board_sn = board_entity.barcode
            else:  # 拼板情况
                board_sn = self.board_sn_dict.get(board_no, '')
            board_data = {
                "BARCODE": board_sn,
                "RESULT": board_entity.get_repair_result('OK', 'NG'),
                "NGCODE": ng_code,
                "TESTDETAIL": ""
            }
            board_data_list.append(board_data)

        # 格式：2019-09-19 13:22:00
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        if product_mode == "板边码生产模式":
            plate_code = pcb_entity.pcb_barcode
            loader_board = ""
        else:
            plate_code = ""
            loader_board = pcb_entity.fixture_barcode

        pcb_data = {
            "PARTNO": part_no,
            "PLATECODE": plate_code,
            "LOADERBOARD": loader_board,
            "STATION": station,
            "JIGNO": jig_no,
            "LINENO": line_no,
            "MACHINE": machine_no,
            "RESULT": pcb_entity.get_repair_result('OK', 'NG'),
            "OP": sfc,
            "STARTTIME": start_time,
            "ENDTIME": end_time,
            "UPLOADPATH": "",
            "PROGROMNAME": progrom_name,
            "PROGROMVERSION": progrom_ver,
            "BARCODELIST": board_data_list
        }

        err_msg = ''
        try:
            ret = xrequest.RequestUtil.post_json(data_url, pcb_data)
            # 返回结果：OK/NG
            if ret.get("Result") == 'NG':
                err_msg = f"发送数据结果返回失败，error：{ret.get('Message')}"
        except Exception as e:
            err_msg = f"本地网络异常，error：{e}"

        if err_msg:
            return self.x_response("false", err_msg)
        else:
            return self.x_response()
