# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/06/17 10:22
# Author     ：chencb
# version    ：python 3.8
# Description：越南立讯 https://jira.cvte.com/browse/ATAOI_2019-40244
"""
from typing import Any
from common import xrequest
from common.xcons import INSPECTOR
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "customer": ["越南立讯", "yuenanlixun"],
        "version": "release v1.0.0.2",
        "device": "AIS40X",
        "feature": ["入站", "出站"],
        "author": "chenchongbing",
        "release": """
date: 2025-06-17 11:30  jira:40244 入站、出站
date: 2025-06-19 17:43  返回结果从原来的code改为result里的sucessful
""", }

    form = {
        "barcode_type": {
            "ui_name": "条码类型",
            "value": ""
        },
        "process_id": {
            "ui_name": "流程ID",
            "value": ""
        },
        "eqp_code": {
            "ui_name": "设备编号",
            "value": ""
        },
        "user": {
            "ui_name": "用户",
            "value": ""
        },
        "station_name": {
            "ui_name": "站点名称",
            "value": ""
        },
        "api_url_move_in": {
            "ui_name": "接口地址（入站）",
            "value": ""
        },
        "api_url_move_std": {
            "ui_name": " 接口地址（出站）",
            "value": ""
        },
    }

    def __init__(self):
        self.carrier_sn = ''

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        sn_list = other_vo.list_sn()
        self.log.info('-------开始入站--------')
        if not sn_list:
            return self.x_response("false", f"条码列表为空，入站失败")

        barcode_map = other_vo.get_barcode_map()
        self.carrier_sn = barcode_map.get('-2', '')

        api_url_move_in = other_vo.get_value_by_cons_key("api_url_move_in", not_null=True)
        barcode_type = other_vo.get_value_by_cons_key("barcode_type")
        process_id = other_vo.get_value_by_cons_key("process_id")
        eqp_code = other_vo.get_value_by_cons_key("eqp_code")
        user = other_vo.get_value_by_cons_key("user")
        param = {
            "barCodes": sn_list,
            "barCodeType": barcode_type,
            "processId": process_id,
            "eqpCode": eqp_code,
            "ruleName": "TrackIn",
            "user": user
        }
        try:
            ret = xrequest.RequestUtil.post_json(api_url_move_in, param)
            result = ret.get("result", {})
            if not result.get('successful', False):
                return self.x_response("false", f"入站失败，入站接口返回错误信息：{result.get('message')}")
        except Exception as e:
            return self.x_response("false", f"入站失败，本地网络异常：{e}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()
        if inspect_type == INSPECTOR:
            self.log.info('复判后才发送出站，机器检测时直接返回不处理！')
            return self.x_response()

        self.log.info('-------开始出站--------')
        api_url_move_std = data_vo.get_value_by_cons_key("api_url_move_std", not_null=True)
        barcode_type = data_vo.get_value_by_cons_key("barcode_type")
        process_id = data_vo.get_value_by_cons_key("process_id")
        eqp_code = data_vo.get_value_by_cons_key("eqp_code")
        user = data_vo.get_value_by_cons_key("user")
        station_name = data_vo.get_value_by_cons_key("station_name")

        pcb_entity = data_vo.pcb_entity
        sn_list = []
        for board_entity in pcb_entity.yield_board_entity():
            sn_list.append(board_entity.barcode)

        param = {
            "barCodes": sn_list,
            "barCodeType": barcode_type,
            "processId": process_id,
            "eqpCode": eqp_code,
            "ruleName": "TrackOut",
            "user": user,
            "collectDataValues": {"carrier": self.carrier_sn},
            "toolingFixtureEncodes": [self.carrier_sn],
            "collectDataType": station_name
        }

        try:
            ret = xrequest.RequestUtil.post_json(api_url_move_std, param)
            result = ret.get("result", {})
            if not result.get('successful', False):
                return self.x_response("false", f"出站失败，出站接口返回错误信息：{result.get('message')}")
        except Exception as e:
            return self.x_response("false", f"出站失败，本地网络异常：{e}")

        return self.x_response()
