# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/1/25 下午3:45
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json

from common import xutil

if __name__ == '__main__':
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.mlsoap.org/soap/envelope/"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <soap:Body>
        <GetBoardsResponse xmlns="MesWebservice">
            <GetBoardsResult>
                {"PanelBarCode": "S003015241D000D", "Result": "OK", "Boards": [{"BoardBarCode": "S003015241D000D01"},
                {"BoardBarCode":"S0030152410000D02" }],"message":null}
            </GetBoardsResult>
        </GetBoardsResponse>
    </soap:Body>
</soap:Envelope>"""
    root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

    result = root[0][0][0]
    print(result.tag)
    print(result.text)

    result_json = json.loads(result.text)
    print(result_json)