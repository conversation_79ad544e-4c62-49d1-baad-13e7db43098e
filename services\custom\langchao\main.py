# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/12 上午11:27
# Author     ：sch
# version    ：python 3.8
# Description：浪潮
"""
import json
from typing import Any

from paho.mqtt import client as mqtt_client
from requests.auth import HTTPBasicAuth

from common import xcons, xrequest, xutil
from common.xutil import log
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


def connect_mqtt(client_id: str, broker_ip: str, broker_port: int):
    """
    连接MQTT
    :param client_id:
    :param broker_ip:
    :param broker_port:
    :return:
    """

    def on_connect(client, userdata, flags, rc):  # noqa
        if rc == 0:
            print("Connected to MQTT Broker!")
        else:
            print("Failed to connect, return code %d\n", rc)

    client = mqtt_client.Client(client_id)
    client.on_connect = on_connect
    client.connect(broker_ip, broker_port)
    return client


def send_data_to_mqtt(client_id: str, broker_ip: str, broker_port: int, topic: str, message: str):
    """
    发送数据到MQTT指定主题
    :param client_id:
    :param broker_ip:
    :param broker_port:
    :param topic:
    :param message:
    :return:
    """
    log.info(f"请求地址：{broker_ip} port:{broker_port} topic:{topic} message:{message}")
    client = connect_mqtt(client_id, broker_ip, broker_port)
    result = client.publish(topic, message)
    log.info(f"mqtt result: {result}")
    client.disconnect()


def send_data_to_mqtt_v2(client_id: str, broker_ip: str, broker_port: int, topic: str, message: str,
                         username='admin', password='public'):
    """
    发送数据到MQTT指定主题
    :param client_id:
    :param broker_ip:
    :param broker_port:
    :param topic:
    :param message:
    :param username:
    :param password:
    :return:
    """
    # log.info(f"请求地址：{broker_ip} port:{broker_port} topic:{topic} message:{message}")
    param = {"topic": topic, "payload": message, "qos": 1, "retain": False, "clientid": client_id,
             "properties": {"user_properties": {"id": 10010, "name": "emqx", "foo": "bar"},
                            "content_type": "text/plain"}}

    auth = HTTPBasicAuth(username, password)

    xrequest.RequestUtil.post_json(f"http://{broker_ip}:8081/api/v4/mqtt/publish", param, auth=auth)

    # client = connect_mqtt(client_id, broker_ip, broker_port)
    # result = client.publish(topic, message)
    # log.info(f"mqtt result: {result}")
    # client.disconnect()


class Engine(BaseEngine):
    version = {
        "title": "langchao release v1.0.0.6",
        "device": "203，303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-12 11:28  上传数据到MQTT平台
date: 2023-10-08 14:14  器件列表输出改为配置项
""", }

    form = {
        "broker_ip": {
            "ui_name": "Broker Ip",
            "value": "*********"
        },
        "broker_port": {
            "ui_name": "Broker Port",
            "value": "1883"
        },
        # "username": {
        #     "ui_name": "用户名",
        #     "value": ""
        # },
        # "password": {
        #     "ui_name": "密码",
        #     "value": ""
        # },
        "client_id": {
            "ui_name": "ClientId",
            "value": "AOI001"
        },
        "topic": {
            "ui_name": "Topic",
            "value": "/sys/Mr6D2DkCq1e/lchaoi1/thing/event/property/post"
        },
    }

    other_form = {
        "username_mqtt": {
            "ui_name": "账号",
            "value": "admin"
        },
        "password_mqtt": {
            "ui_name": "密码",
            "value": "public"
        },
    }

    combo = {
        "comp_list_type": {
            "ui_name": "器件列表输出",
            "item": ["全部", "检测NG", "复判NG"],
            "value": "复判NG"
        }
    }

    password_style = [
        "password_mqtt"
    ]

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        broker_ip = data_vo.get_value_by_cons_key("broker_ip")
        broker_port = data_vo.get_value_by_cons_key("broker_port")
        client_id = data_vo.get_value_by_cons_key("client_id")
        topic = data_vo.get_value_by_cons_key("topic")
        username_mqtt = data_vo.get_value_by_cons_key("username_mqtt")
        password_mqtt = data_vo.get_value_by_cons_key("password_mqtt")
        comp_list_type = data_vo.get_value_by_cons_key("comp_list_type")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        try:
            broker_port = int(broker_port)
        except Exception as err:
            return self.x_response("false", f"Broker Port必须为数字！error：{err}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                if (comp_list_type == "复判NG" and comp_entity.is_repair_ng()) or (
                        comp_list_type == "检测NG" and comp_entity.is_robot_ng()
                ) or comp_list_type == "全部":
                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                    })

            board_data = {
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_test_time": test_time,
                "pcb_project_name": pcb_entity.project_name,
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "board_comp_number": board_entity.comp_total_number,
                "board_co_ng_number": board_entity.comp_repair_ng_number,
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            }

            # send_data_to_mqtt(client_id, broker_ip, broker_port, topic, json.dumps(board_data,
            #                                                                        ensure_ascii=False))

            param1 = {
                "timeStamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                "deviceProperty": board_data
            }

            send_data_to_mqtt_v2(client_id, broker_ip, broker_port, topic,
                                 json.dumps(param1,
                                            ensure_ascii=False), username_mqtt, password_mqtt)

        return self.x_response()
