# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/config_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ConfigDialog(object):
    def setupUi(self, ConfigDialog):
        ConfigDialog.setObjectName("ConfigDialog")
        ConfigDialog.resize(387, 401)
        self.verticalLayout = QtWidgets.QVBoxLayout(ConfigDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame = QtWidgets.QFrame(ConfigDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setObjectName("formLayout")
        self.label = QtWidgets.QLabel(self.frame)
        self.label.setObjectName("label")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.check_barcode_setting1 = QtWidgets.QComboBox(self.frame)
        self.check_barcode_setting1.setObjectName("check_barcode_setting1")
        self.check_barcode_setting1.addItem("")
        self.check_barcode_setting1.addItem("")
        self.check_barcode_setting1.addItem("")
        self.check_barcode_setting1.addItem("")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.check_barcode_setting1)
        self.label_4 = QtWidgets.QLabel(self.frame)
        self.label_4.setObjectName("label_4")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_4)
        self.check_barcode_setting2 = QtWidgets.QComboBox(self.frame)
        self.check_barcode_setting2.setObjectName("check_barcode_setting2")
        self.check_barcode_setting2.addItem("")
        self.check_barcode_setting2.addItem("")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.check_barcode_setting2)
        self.label_2 = QtWidgets.QLabel(self.frame)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.sendmes_setting1 = QtWidgets.QComboBox(self.frame)
        self.sendmes_setting1.setObjectName("sendmes_setting1")
        self.sendmes_setting1.addItem("")
        self.sendmes_setting1.addItem("")
        self.sendmes_setting1.addItem("")
        self.sendmes_setting1.addItem("")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.sendmes_setting1)
        self.label_3 = QtWidgets.QLabel(self.frame)
        self.label_3.setObjectName("label_3")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.sendmes_setting2 = QtWidgets.QComboBox(self.frame)
        self.sendmes_setting2.setObjectName("sendmes_setting2")
        self.sendmes_setting2.addItem("")
        self.sendmes_setting2.addItem("")
        self.sendmes_setting2.addItem("")
        self.sendmes_setting2.addItem("")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.sendmes_setting2)
        self.sendmes_setting3 = QtWidgets.QComboBox(self.frame)
        self.sendmes_setting3.setObjectName("sendmes_setting3")
        self.sendmes_setting3.addItem("")
        self.sendmes_setting3.addItem("")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.sendmes_setting3)
        self.label_5 = QtWidgets.QLabel(self.frame)
        self.label_5.setObjectName("label_5")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.verticalLayout_2.addLayout(self.formLayout)
        self.verticalLayout.addWidget(self.frame)
        self.buttonBox = QtWidgets.QDialogButtonBox(ConfigDialog)
        self.buttonBox.setOrientation(QtCore.Qt.Horizontal)
        self.buttonBox.setStandardButtons(QtWidgets.QDialogButtonBox.Cancel|QtWidgets.QDialogButtonBox.Ok)
        self.buttonBox.setObjectName("buttonBox")
        self.verticalLayout.addWidget(self.buttonBox)

        self.retranslateUi(ConfigDialog)
        self.buttonBox.accepted.connect(ConfigDialog.accept) # type: ignore
        self.buttonBox.rejected.connect(ConfigDialog.reject) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(ConfigDialog)

    def retranslateUi(self, ConfigDialog):
        _translate = QtCore.QCoreApplication.translate
        ConfigDialog.setWindowTitle(_translate("ConfigDialog", "个性化配置项"))
        self.label.setText(_translate("ConfigDialog", "条码校验"))
        self.check_barcode_setting1.setItemText(0, _translate("ConfigDialog", "校验全部条码"))
        self.check_barcode_setting1.setItemText(1, _translate("ConfigDialog", "仅校验第一个条码"))
        self.check_barcode_setting1.setItemText(2, _translate("ConfigDialog", "不校验第一个条码"))
        self.check_barcode_setting1.setItemText(3, _translate("ConfigDialog", "仅校验板边条码"))
        self.label_4.setText(_translate("ConfigDialog", "条码校验"))
        self.check_barcode_setting2.setItemText(0, _translate("ConfigDialog", "校验失败发送数据到Mes"))
        self.check_barcode_setting2.setItemText(1, _translate("ConfigDialog", "校验失败不发送数据到Mes"))
        self.label_2.setText(_translate("ConfigDialog", "发送Mes"))
        self.sendmes_setting1.setItemText(0, _translate("ConfigDialog", "保存全部器件图"))
        self.sendmes_setting1.setItemText(1, _translate("ConfigDialog", "保存检测NG器件图"))
        self.sendmes_setting1.setItemText(2, _translate("ConfigDialog", "保存复判NG器件图"))
        self.sendmes_setting1.setItemText(3, _translate("ConfigDialog", "不保存器件图"))
        self.label_3.setText(_translate("ConfigDialog", "发送Mes"))
        self.sendmes_setting2.setItemText(0, _translate("ConfigDialog", "保存全部器件列表"))
        self.sendmes_setting2.setItemText(1, _translate("ConfigDialog", "仅保存检测NG器件列表"))
        self.sendmes_setting2.setItemText(2, _translate("ConfigDialog", "仅保存复判NG器件列表"))
        self.sendmes_setting2.setItemText(3, _translate("ConfigDialog", "不保存器件列表"))
        self.sendmes_setting3.setItemText(0, _translate("ConfigDialog", "不发送坏板"))
        self.sendmes_setting3.setItemText(1, _translate("ConfigDialog", "发送坏板"))
        self.label_5.setText(_translate("ConfigDialog", "发送Mes"))
