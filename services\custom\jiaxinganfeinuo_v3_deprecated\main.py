# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/10 下午3:23
# Author     ：sch
# version    ：python 3.8
# Description：嘉兴安费诺、嘉兴苹果安费诺、安费诺V3     重写脚本逻辑     jira： ATAOI_2019-38648    代码已移动到独立分支
"""
import json
import os
from datetime import datetime
from typing import Any

from common import xutil, xcons, xrequest
from engine.MesEngine import ErrorMapEngine
from services.custom.jiaxinganfeinuo_v3_deprecated import afn_module
from vo.mes_vo import DataVo, OtherVo, ButtonVo


class Engine(ErrorMapEngine):
    version = {
        "title": "jiaxinganfeinuo_v3 release v1.0.0.5",
        "device": "AIS401、AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-27 09:00  从嘉兴安费诺v2 copy，为和以前的做区分，本脚本为v3版本。对应jira号: ATAOI_2019-29226
date: 2024-03-27 10:21  生成txt文档到本地
date: 2024-03-29 15:04  1.板面识别程序名 `XXX-` 后面的字母T/B,如果不是则取界面上填写的板面  2.其他参数变更
date: 2024-04-01 14:23  message/test参数修改
date: 2024-04-01 17:52  器件明细项的`result`也要传PASS/REPASS/FAIL
date: 2024-04-03 09:21  DEFECT_CODE传不良描述
date: 2024-04-03 18:04  PANEL_SIDE,DEFECT_NAME,DEFECT_CODE参数修改
date: 2024-04-07 09:11  txt文件夹规则使用TOP/BOT做为板面枚举，CSV文件内容PANEL_SIDE使用TOP/BOTTOM做为板面枚举
date: 2024-04-28 15:15  增加从MES获取大板条码的功能
date: 2024-05-13 18:44  MES不良代码前面加0000
date: 2024-05-28 10:22  需求变更
date: 2024-06-06 18:01  mark识别错误，不生成数据
date: 2025-01-17 09:58  jira: 30629 ,panel_content的end_time参数修改为获取复判时间
date: 2025-01-23 09:58  jira: 30629 ,test_result参数修改为PASS,PASS,FAIL
date: 2025-02-05 11:19  jira: 30629 ,修改test_result参数和comp_json1参数
date: 2025-02-25 09:10  jira: 30629 ,修改日志生成规则,改为每天生成一个pymes文件，不管大小，保留30天
date: 2025-02-27 17:23  csv和txt文件名的时间改成复判时间命名，csv内容的DATE,START_TIME,END_TIME全部改成复判时间
date: 2025-03-03 10:17  jira:37612, 拼版生成txt文件后先加入缓存中 等待所有拼版都生成结束之后 一起将所有txt文件写入上传
date: 2025-03-13 11:52  jira:37881, 生成txt的路径文件夹，改为条码_时间戳

date: 2025-04-10 19:23  使用纯Python配置器重写，并增加缓存同步机制
date: 2025-04-11 17:21  jira->ATAOI_2019-38648: 增加一个路径判断挂载点是否存在
date: 2025-04-15 11:23  jira->ATAOI_2019-38648: 增加配置项，修改时间格式，增加图标
date: 2025-04-18 15:32  日志改成按天保存，保存30天
date: 2025-04-21 16:31  jira->ATAOI_2019-38648，修改板面的获取逻辑
""", }

    path = {
        "save_path_csv_local": {
            "ui_name": "保存路径(csv)",
            "value": "",
        },
        "save_path_txt_to_window": {
            "ui_name": "保存路径(txt)_共享映射路径",
            "value": "",
        },
        "save_path_txt_local": {
            "ui_name": "保存路径(txt)_缓存路径",
            "value": "",
        },
    }

    form = {
        "site": {
            "ui_name": "地点(txt)",
            "value": "",
        },
        "fixture_id": {
            "ui_name": "工序(txt)",
            "value": "",
        },
        "software_name": {
            "ui_name": "测试软件名称(txt)",
            "value": "",
        },
        "software_version": {
            "ui_name": "测试软件版本(txt)",
            "value": "",
        },
        "station_type": {
            "ui_name": "站点名称(txt)",
            "value": "",
        },
        "line_name": {
            "ui_name": "线体名称(txt)",
            "value": "",
        },
        "rule_filepath": {
            "ui_name": "csv规则文件",
            "value": "",
        },
        "window_ip": {
            "ui_name": "window中转IP",
            "value": "",
        },
        "line": {
            "ui_name": "产线号",
            "value": "",
        },
        "operator": {
            "ui_name": "操作者",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "device_sn": {
            "ui_name": "设备序列号",
            "value": "",
        },
    }

    combo = {
        "board_side_ui": {
            "ui_name": "面别",
            "item": ["T", "B"],
            "value": "T",
        },
        "save_type": {
            "ui_name": "保存类型",
            "item": [
                'csv',
                'txt',
                'csv+txt',
            ],
            "value": "csv",
        },
        "txt_filename_type": {
            "ui_name": "txt文件路径类型",
            "item": [
                '条码_时间戳',
                '条码',
            ],
            "value": "条码_时间戳",
        },
    }

    other_combo = {
        "sync_frequency": {
            "ui_name": "同步频率(s)-重启配置器生效",
            "item": list(map(str, range(1, 10))) + list(map(str, range(10, 300, 10))),
            "value": "60",
        }
    }

    other_form = {}

    button = {
        "upload_data_to_server": {
            "ui_name": "一键重传"
        }
    }

    other_path = {
        "mount_path": {
            "ui_name": "挂载路径(df -h)",
            "value": "",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        sync_frequency = other_vo.get_value_by_cons_key("sync_frequency", to_int=True)
        main_window.set_cron_setting(True, sync_frequency)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_csv_local = data_vo.get_value_by_cons_key("save_path_csv_local", not_null=True)
        save_path_txt_to_window = data_vo.get_value_by_cons_key("save_path_txt_to_window", not_null=True)
        save_path_txt_local = data_vo.get_value_by_cons_key("save_path_txt_local", not_null=True)
        site = data_vo.get_value_by_cons_key("site")
        fixture_id = data_vo.get_value_by_cons_key("fixture_id")
        software_name = data_vo.get_value_by_cons_key("software_name")
        software_version = data_vo.get_value_by_cons_key("software_version")
        station_type = data_vo.get_value_by_cons_key("station_type")
        line_name = data_vo.get_value_by_cons_key("line_name")
        rule_filepath = data_vo.get_value_by_cons_key("rule_filepath")
        line = data_vo.get_value_by_cons_key("line")
        operator = data_vo.get_value_by_cons_key("operator")
        device_name = data_vo.get_value_by_cons_key("device_name")
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        board_side_ui = data_vo.get_value_by_cons_key("board_side_ui")
        save_type = data_vo.get_value_by_cons_key("save_type")
        mount_path = data_vo.get_value_by_cons_key("mount_path", not_null=True)
        txt_filename_type = data_vo.get_value_by_cons_key("txt_filename_type")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name

        panel_count = xutil.CacheUtil.get(project_name, 0)
        panel_count += 1
        xutil.CacheUtil.set(project_name, panel_count)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)  # 检测开始时间
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)  # 检测结束时间

        time_now = datetime.now()  # 当前时间  （复判时间）
        review_time = time_now.strftime(xcons.FMT_TIME_DEFAULT)

        # 存储所有生成的txt文件
        save_txt_files = []

        date_fmt_date1 = "%Y%m%d_%H%M%S"
        date_fmt_date2 = "%Y%m%d%H%M%S"

        # 需要改成复判的时间 （ps：先改成发送mes的时间，其实约等于复判时间）
        datetime_1 = time_now.strftime(date_fmt_date1)
        datetime_2 = time_now.strftime(date_fmt_date2)

        if not os.path.exists(rule_filepath):
            return self.x_response("false", f"csv规则文件不存在！")

        rule_data = afn_module.get_rule_data(rule_filepath)
        print(f"{rule_data=}")

        if not software_version:
            software_version = "1.0.0.11"

        if not board_side_ui:
            board_side_ui = pcb_entity.board_side

        if board_side_ui == "B":
            board_side = "BOTTOM"
        else:
            board_side = "TOP"

        self.log.info(f"project name: {project_name}")
        # if "XXX" in project_name:
        #     project_list = project_name.split('XXX-')
        #     if len(project_list) >= 2:
        #         board_flag = project_list[1]
        #
        #         if len(board_flag) >= 1:
        #             flag1 = board_flag[0]
        #             if flag1 == "T":
        #                 board_side = "TOP"
        #                 self.log.info(f"XXX-后面是T, board side已改为：{board_side}")
        #
        #             elif flag1 == "B":
        #                 board_side = "BOTTOM"
        #                 self.log.info(f"XXX-后面是B, board side已改为：{board_side}")
        #         else:
        #             self.log.warning(f"board side未改变！")
        #     else:
        #         self.log.warning(f"board side未改变！")
        # else:
        #     self.log.warning(f"board side未改变！")

        if "-T-" in project_name:
            board_side = "TOP"
            self.log.info(f"检测到-T-, board side已改为：{board_side}")
        elif "-B-" in project_name:
            board_side = "BOTTOM"
            self.log.info(f"检测到-B-, board side已改为：{board_side}")
        else:
            self.log.warning(f"未检测到-T-或-B-, board side未改变！")

        board_data = ""
        comp_data = ""
        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            bad_mark = "YES" if board_entity.is_bad_board() else "NO"

            if not pcb_sn:
                pcb_sn = barcode

            board_ng_str = ""

            comp_data_json_list = []

            for comp_entity in board_entity.yield_comp_entity():
                robot_ng_code = comp_entity.robot_ng_code
                user_ng_code = comp_entity.repair_ng_code

                if robot_ng_code == "20":
                    self.log.warning(f"mark识别错误,本次不生成数据！")
                    return self.x_response()

                # 检测NG
                ng_code = afn_module.ais_40x_error_map.get(robot_ng_code, {}).get("custom_code", robot_ng_code)
                ng_str_en = afn_module.ais_40x_error_map.get(robot_ng_code, {}).get("custom_str")

                comp_tag = comp_entity.designator

                if robot_ng_code != "0":
                    if robot_ng_code == "25":
                        self.log.warning(f"坏板识别")
                        bad_mark = "YES"

                    if bad_mark == "YES":
                        self.log.warning(f"坏板数据不输出！")
                        continue

                    if not board_ng_str:
                        board_ng_str = ng_str_en

                    x_pos = comp_entity.geometry.cx
                    y_pos = comp_entity.geometry.cy

                    comp_row = afn_module.comp_template.format(**{
                        "board_no": board_no,
                        "comp_tag": comp_tag,
                        "pin_number": "",
                        "pos_x": x_pos,
                        "pos_y": y_pos,
                        "ng_str": "",
                        "ng_code": ng_str_en,
                        "result": "OK" if user_ng_code == "0" else "NG",
                    })
                    comp_data += f"\n{comp_row}"

                if robot_ng_code != '0':
                    if user_ng_code != '0':
                        comp_result = 'FAIL'
                    else:
                        comp_result = 'PASS'

                else:
                    comp_result = "PASS"

                ng_code = f"00000{ng_code}"

                for alg_entity in comp_entity.yield_alg_entity():
                    alg_max = alg_entity.max_threshold
                    alg_min = alg_entity.min_threshold
                    alg_test_name = alg_entity.test_name
                    alg_test_val = alg_entity.test_val

                    comp_json1 = {
                        "lower_limit": alg_min,
                        "parametric_key": comp_tag,
                        "priority": "0",
                        # "result": "PASS" if alg_result == '0' else "FAIL",
                        "result": comp_result,
                        "message": f"{ng_code}/{alg_test_name}",
                        "test": f"{comp_tag}#{ng_code}/{alg_test_name}",
                        "units": "999",
                        "upper_limit": alg_max,
                        "value": alg_test_val,
                    }

                    comp_json_str = json.dumps(comp_json1, indent=4, ensure_ascii=False)
                    comp_data_json_list.append(comp_json_str)

            if bad_mark == "YES":
                b_ng_flag = "BADMARK"
            else:
                b_ng_flag = board_ng_str

            board_data += afn_module.board_template.format(**{
                "board_no": board_no,
                "sn": barcode,
                "ng_str": b_ng_flag,
                "ng_code": b_ng_flag,
                "bad_mark": bad_mark,
                "result": board_entity.get_final_result("OK", "OK", "NG"),
            })

            txt_test_result = board_entity.get_final_result("PASS", "PASS", "FAIL")
            self.log.info(f"comp data len: {len(comp_data_json_list)}")
            comp_data_json_str = ",\r\n".join(comp_data_json_list)

            if board_entity.is_bad_board():
                txt_test_result = "BADMARK"
                comp_data_json_str = ""

            txt_content = afn_module.txt_template.format(**{
                "barcode": barcode,
                "panel_no": board_no,
                "fixture_id": fixture_id,
                "station_type": station_type,
                "test_result": txt_test_result,
                "test_start": start_time,
                "test_stop": end_time,
                "software_name": software_name,
                "software_version": software_version,
                "site": site,
                "line_id": line_name,
                "project_name": project_name,
                "operator_id": pcb_entity.repair_user,
                "comp_data_json": comp_data_json_str,
            })

            if save_type in ["txt", "csv+txt"]:
                if board_side == "BOTTOM":
                    board_side_dir = "BOT"
                else:
                    board_side_dir = board_side

                # 20240528 增加需求变更
                rule_info = rule_data.get(project_name, {})
                self.log.info(f"rule info: {rule_info}")

                if rule_info.get('flag', '1') in ["", "1"]:
                    save_p1 = rule_info.get('path', save_path_txt_local)

                    if save_p1 == save_path_txt_local:
                        # 需要先判断一下挂载目录是否挂载上了
                        if xutil.FileUtil.is_share_disk_mounted(mount_path):
                            save_p1 = save_path_txt_to_window
                        else:
                            self.log.warning(f"未挂载共享目录，txt文档将保存在本地缓存目录！")
                            save_p1 = save_path_txt_local
                    else:
                        self.log.warning(f"根据规则配置，txt文档将保存在本地缓存目录！")

                    if txt_filename_type == "条码_时间戳":
                        tmp_flag = f"{pcb_sn}_{datetime_2}"
                    else:
                        tmp_flag = f"{pcb_sn}"

                    txt_path = f"{save_p1}/{tmp_flag}/{board_side_dir}"

                    txt_filepath = f"{txt_path}/{pcb_sn}-{board_no}-{datetime_2}.txt"

                    save_txt_files.append({
                        'path': txt_path,
                        'filepath': txt_filepath,
                        'content': txt_content,
                    })
            else:
                self.log.warning(f"根据页面配置，无需保存txt文档！")

        panel_content = afn_module.panel_template.format(**{
            "line_name": line,
            "device_id": device_sn,
            "device_name": device_name,
            "operator": operator,
            "project_name": project_name,
            "board_side": board_side,
            "pcb_sn": pcb_sn,
            "track": pcb_entity.get_track_index(),

            "index": panel_count,
            "date": datetime_1[:8],
            "start_time": start_time[11:],
            "end_time": review_time[11:],
            "result": pcb_entity.get_final_result("", "OK", "NG"),
            "board_data": board_data.strip(),
            "comp_data": comp_data,
        })

        if save_type in ["csv", "csv+txt"]:
            file_path = f"{save_path_csv_local}/{datetime_1}_{project_name}_{pcb_sn}.csv"
            xutil.FileUtil.write_content_to_file_pro(file_path, panel_content)
        else:
            self.log.warning(f"根据页面配置，无需保存csv文档！")

        if save_txt_files:
            for txt_file in save_txt_files:
                xutil.FileUtil.ensure_dir_exist(txt_file['path'])
                xutil.FileUtil.write_content_to_file_pro(txt_file['filepath'], txt_file['content'])

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        barcode_map = other_vo.get_barcode_map()
        fixture_barcode = barcode_map.get("-2", '')

        ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, {
            "type": 1,
            "request_param": {
                "info": fixture_barcode
            }
        })

        ret_str = ret.get('string')

        if not ret.get('result'):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret_str}")

        barcode_map["-1"] = ret_str

        return self.x_response("true", json.dumps(barcode_map))

    def upload_data_to_server(
            self,
            save_path_txt_to_window,
            save_path_txt_local,
            mount_path
    ):
        """
        同步文件到挂载共享文件夹
        """
        if save_path_txt_to_window and save_path_txt_local:
            # 同步txt文档
            # 1. 先判断挂载是否存在
            if xutil.FileUtil.is_share_disk_mounted(mount_path):
                # 2. 挂载存在，则将本地的txt文件上传到挂载
                for file_p in os.listdir(save_path_txt_local):
                    filepath = f"{save_path_txt_local}/{file_p}"
                    dst_filepath = f"{save_path_txt_to_window}/{file_p}"
                    xutil.FileUtil.move_file(filepath, dst_filepath)
            else:
                self.log.warning(f"服务器挂载不存在，无法同步txt文档！")

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        save_path_txt_to_window = other_vo.get_value_by_cons_key("save_path_txt_to_window")
        save_path_txt_local = other_vo.get_value_by_cons_key("save_path_txt_local")
        mount_path = other_vo.get_value_by_cons_key("mount_path", not_null=True)

        self.upload_data_to_server(
            save_path_txt_to_window,
            save_path_txt_local,
            mount_path
        )

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        save_path_txt_to_window = btn_vo.get_value_by_cons_key("save_path_txt_to_window")
        save_path_txt_local = btn_vo.get_value_by_cons_key("save_path_txt_local")
        mount_path = btn_vo.get_value_by_cons_key("mount_path", not_null=True)

        btn_key = btn_vo.get_btn_key()

        if btn_key == "upload_data_to_server":
            self.upload_data_to_server(
                save_path_txt_to_window,
                save_path_txt_local,
                mount_path
            )

        return self.x_response()
