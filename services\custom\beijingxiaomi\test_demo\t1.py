# # !/usr/bin/env python
# # -*-coding:utf-8 -*-
#
# """
# # File       : test_demo.py
# # Time       ：2025/3/24 下午2:30
# # Author     ：sch
# # version    ：python 3.8
# # Description：
# """
# import hashlib
# import json
#
# import requests
#
#
# def calculate_sign(app_id: str, body: dict, app_key: str):
#     """
#     计算 X5 协议的签名
#     :param app_id: 应用ID
#     :param body: 请求体（字典格式）
#     :param app_key: 应用密钥
#     :return: 签名（大写MD5值）
#     """
#     # 将 body 转换为 JSON 字符串
#     body_str = json.dumps(body, separators=(',', ':'), ensure_ascii=False)
#
#     # 拼接 app_id、body 和 app_key
#     sign_str = f"{app_id}{body_str}{app_key}"
#     print(f"sign_str: \n{sign_str}")
#
#     # 计算 MD5 值并转换为大写
#     sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
#
#     print(f"sign: \n{sign}")
#     return sign
#
#
# if __name__ == '__main__':
#     app_id = "MIUAT"
#     body = {
#         "machineId": "A030007000102",
#         "stationId": "G05-AFCCBFP-11",
#         "clientMac": "00155DD01E93",
#         "clientTime": "2024-08-03 09:12:32.176",
#         "unitSn": "50303/N3X900237",
#         "userId": "AKPKTSQAS46NKE4NHY",
#         "factoryId": 1
#     }
#     app_key = "6B3D57E2-F062-471F-932B-76C8A79E0D66"
#     # calculate_sign(app_id, body, app_key)
#
#     api_url = f"http://im.pre.mi.com/mes/x5/pass/station/v2"
#
#     headers = {
#
#     }
#
#     ret = requests.post(api_url, data=body)
#     print(ret)
#     print(ret.text)
