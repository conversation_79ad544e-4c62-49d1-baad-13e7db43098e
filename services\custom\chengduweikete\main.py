# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/28 上午10:10
# Author     ：sch
# version    ：python 3.8
# Description：成都维克特
"""
import time
from typing import Any

from common import xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "chengduweikete release v1.0.0.5",
        "device": "AIS430",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-28 16:31  条码校验，上传数据
date: 2024-08-14 16:43  增加请求参数
date: 2024-08-14 17:37  修改请求参数
date: 2024-09-05 17:59  修改请求参数: splItemRes-->splItemRs
date: 2024-09-09 16:24  分拼板请求接口
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(SN检查)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(SN过站)",
            "value": "",
        },
        "request_type": {
            "ui_name": "requestType",
            "value": "HTTP",
        },
        "dev_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "wp_code": {
            "ui_name": "工序编码",
            "value": "",
        },
        "recipe_name": {
            "ui_name": "程序文件名称",
            "value": "",
        },
        "recipe_version": {
            "ui_name": "程序文件版本",
            "value": "",
        },
    }

    form = {
        "user_id": {
            "ui_name": "操作人",
            "value": "",
        },
    }

    combo = {
        "dev_type": {
            "ui_name": "设备分类",
            "item": ["SPI", "AOI", "XL", "XT"],
            "value": "AOI",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        request_type = other_vo.get_value_by_cons_key("request_type")
        user_id = other_vo.get_value_by_cons_key("user_id")
        dev_code = other_vo.get_value_by_cons_key("dev_code")
        dev_type = other_vo.get_value_by_cons_key("dev_type")
        wp_code = other_vo.get_value_by_cons_key("wp_code")
        recipe_name = other_vo.get_value_by_cons_key("recipe_name")
        recipe_version = other_vo.get_value_by_cons_key("recipe_version")

        cur_list = []
        err_msg_list = []
        for ix, sn in enumerate(other_vo.list_sn()):
            cur_list.append({
                "lbId": sn
            })

            request_id = xutil.OtherUtil.get_uuid4_str()
            check_param = {
                "requestId": request_id,
                "requestNo": request_id,
                "requestType": request_type,
                "sourceId": "AOI",
                "targetId": "IMS",
                "requestTime": int(time.time() * 1000),
                "userId": user_id,
                "data": {
                    "devCode": dev_code,
                    "devType": dev_type,
                    "wpCode": wp_code,
                    "recipeName": recipe_name,
                    "recipeVersion": recipe_version,
                    "curLbs": cur_list
                },
                "extend": {}
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
            if ret.get("resultCode") != "0000":
                err_msg_list.append(f"No:{ix} SN:{sn} Error:{ret.get('resultMsg')}")

        # return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('resultMsg')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        request_type = data_vo.get_value_by_cons_key("request_type")
        user_id = data_vo.get_value_by_cons_key("user_id")
        dev_code = data_vo.get_value_by_cons_key("dev_code")
        dev_type = data_vo.get_value_by_cons_key("dev_type")
        wp_code = data_vo.get_value_by_cons_key("wp_code")
        recipe_name = data_vo.get_value_by_cons_key("recipe_name")
        recipe_version = data_vo.get_value_by_cons_key("recipe_version")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        err_msg_list = []

        comp_ng_list = []
        comp_ix = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_ix += 1

                    comp_ng_list.append({
                        "ngItemCode": comp_entity.repair_ng_code,
                        "seq": str(comp_ix),
                        "splPointLoc": comp_entity.designator,
                        "splItemValue": comp_entity.image_path,
                        "splPrjCode": comp_entity.part,
                        "splItemRs": "0",  # 1为合格，0为不合格
                    })

            sn_list = [{
                "lbId": barcode,
                "wpRs": board_entity.get_repair_result(1, 0),
                "lbwpQcList": comp_ng_list
            }]

            request_id = xutil.OtherUtil.get_uuid4_str()
            data_param = {
                "requestId": request_id,
                "requestNo": request_id,
                "requestType": request_type,
                "sourceId": "AOI",
                "targetId": "IMS",
                "requestTime": int(time.time() * 1000),
                "userId": user_id,
                "data": {
                    "devCode": dev_code,
                    "devType": dev_type,
                    "wpCode": wp_code,
                    "recipeName": recipe_name,
                    "recipeVersion": recipe_version,
                    "curLbs": sn_list,
                },
                "extend": {}
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if ret.get("resultCode") != "0000":
                err_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('resultMsg')}")
                # return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('resultMsg')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_str}")

        return self.x_response()
