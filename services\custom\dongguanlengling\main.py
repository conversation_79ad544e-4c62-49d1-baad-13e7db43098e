# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/25 上午11:09
# Author     ：sch
# version    ：python 3.8
# Description：东莞崚凌
"""
from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanlengling release v1.0.0.6",
        "device": "AIS501,AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-25 15:12  上传数据到MES
date: 2024-04-26 14:24  机种预留在程序界面填写
date: 2024-04-26 17:05  RESULT==0的异常信息也需要弹窗报警
date: 2024-05-14 16:43  ID：就是如果有整版条码就传整版条码，没有的话就传空
date: 2024-05-16 16:22  所有异常弹窗消息都弹出来
date: 2024-05-20 15:45  需要把SerialNo也需要弹窗提示
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://***********:8016/api/aoi/aoiData/RunBill",
        },
        "part_no_ui": {
            "ui_name": "机种/程序料号",
            "value": "",
        },
        "station": {
            "ui_name": "站位编号",
            "value": "",
        },
        "line_no": {
            "ui_name": "线体",
            "value": "",
        },
        "machine_no": {
            "ui_name": "机台编号",
            "value": "",
        },
        "emp_no": {
            "ui_name": "SFC账号",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        station = data_vo.get_value_by_cons_key("station")
        line_no = data_vo.get_value_by_cons_key("line_no")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        api_url = data_vo.get_value_by_cons_key("api_url")
        part_no_ui = data_vo.get_value_by_cons_key("part_no_ui")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            # if not pcb_sn and barcode:
            #     pcb_sn = barcode

            comp_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_list.append({
                        "ERRORPOSITION": comp_entity.designator,
                        "ERRORCODE": comp_entity.robot_ng_str,
                        "JUDGE": comp_entity.repair_ng_str,
                        "ITEMCODE": comp_entity.part
                    })

            board_list.append({
                "BARCODE": barcode,
                "SERIALNO": board_no,
                "TESTDETAIL": "",
                "RESULT": board_entity.get_repair_result(1, 0),
                "NGCODE": comp_list
            })

        data_param = {
            "PARTNO": part_no_ui,
            "STATION": station,
            "LINENO": line_no,
            "MACHINE": machine_no,
            "EMPNO": emp_no,
            "ID": pcb_sn,
            "STARTTIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "ENDTIME": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
            "TESTDETAIL": "",
            "BARCODELIST": board_list
        }

        ret = xrequest.RequestUtil.post_json(api_url, data_param)

        # ret_res = self.x_response()

        error_list = []
        if str(ret.get('SUCCESS')) != '1':
            error_msg = ret.get('MESSAGE')

            error_list.append(ret.get('MESSAGE'))

            if not error_msg:
                for i in ret.get('RESULT', []):
                    msg = i.get('MESSAGE')
                    serial_no = i.get('SERIALNO')

                    if msg:
                        error_list.append(f"SerialNo:{serial_no}-->{msg}")

            err_infos = '\n'.join(error_list)
            if error_list:
                return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_infos}")

        else:
            for i in ret.get('RESULT', []):
                msg = i.get('MESSAGE')
                serial_no = i.get('SERIALNO')

                if str(i.get('RESULT')) == '0':
                    error_list.append(f"SerialNo:{serial_no}-->{msg}")

            err_infos = '\n'.join(error_list)

            if error_list:
                return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_infos}")

        return self.x_response()
