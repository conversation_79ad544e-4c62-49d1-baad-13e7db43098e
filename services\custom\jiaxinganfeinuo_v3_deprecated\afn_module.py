# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : afn_module.py
# Time       ：2025/4/10 下午3:30
# Author     ：sch
# version    ：python 3.8
# Description：
"""
panel_template = """[INFO]
LINE_NAME,MACHINE_SN,MACHINE_NAME,OPERATOR_ID
{line_name},{device_id},{device_name},{operator}
[INFO_END]

[PANEL_INSP_RESULT]
MODEL_NAME,MODEL_CODE,PANEL_SIDE,INDEX,BARCODE,DATE,START_TIME,END_TIME,DEFECT_NAME,DEFECT_CODE,RESULT,track
{project_name},,{board_side},{index},{pcb_sn},{date},{start_time},{end_time},,,{result},{track}
[PANEL_INSP_RESULT_END]

[BOARD_INSP_RESULT]
BOARD_NO,BARCODE,DEFECT_NAME,DEFECT_CODE,BADMARK,RESULT,Component_Name,Height(um)
{board_data}
[BOARD_INSP_RESULT_END]

[COMPONENT_INSP_RESULT]
BOARD_NO,LOCATION_NAME,PIN_NUMBER,POS_X,POS_Y,DEFECT_NAME,DEFECT_CODE,RESULT{comp_data}
[COMPONENT_INSP_RESULT_END]"""

board_template = """{board_no},{sn},{ng_str},{ng_code},{bad_mark},{result},,
"""

comp_template = "{board_no},{comp_tag},{pin_number},{pos_x},{pos_y},{ng_str},{ng_code},{result}"

txt_template = """BoardCode={barcode}
PanelNo={panel_no}
fixture_id={fixture_id}
station_type={station_type}
test_result={test_result}
test_start={test_start}
test_stop={test_stop}
software_name={software_name}
software_version={software_version}
site={site}
lineId={line_id}
projectName={project_name}
operatorId={operator_id}
results:
{comp_data_json}"""

ais_40x_error_map = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "缺件", "custom_code": "1", "custom_str": "Missing"},
    "2": {"standard": "文字错误", "custom_code": "2", "custom_str": "WrongText"},
    "3": {"standard": "极性", "custom_code": "3", "custom_str": "Polarity"},
    "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tombstone"},
    "5": {"standard": "位移", "custom_code": "5", "custom_str": "Misalign ment"},
    "6": {"standard": "反贴", "custom_code": "6", "custom_str": "Upsidedown"},
    "7": {"standard": "浮高", "custom_code": "7", "custom_str": "LiftedPackage"},
    "8": {"standard": "裂痕", "custom_code": "8", "custom_str": "Crack"},
    "9": {"standard": "焊点", "custom_code": "9", "custom_str": "Solder Joint"},
    "10": {"standard": "少锡", "custom_code": "10", "custom_str": "No Solder"},
    "11": {"standard": "多锡", "custom_code": "11", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "12", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "13", "custom_str": "PinHole"},
    "14": {"standard": "短路", "custom_code": "14", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "15", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "lead Lift"},
    "17": {"standard": "引脚偏移", "custom_code": "17", "custom_str": "Lead Misalign"},
    "18": {"standard": "异物", "custom_code": "18", "custom_str": "Foreign Material"},
    "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
    "23": {"standard": "浮起", "custom_code": "23", "custom_str": "Lift"},
    "24": {"standard": "污染物", "custom_code": "24", "custom_str": "Contamination"},
    "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}


def get_rule_data(csv_filepath: str) -> dict:
    """
    解析规则文件
    :param csv_filepath:
    :return:
    """
    with open(csv_filepath, "r") as f:
        rule_list = f.readlines()

    all_row = [i.strip() for i in rule_list]

    ret_dict = {}
    for row in all_row:
        row_list = row.split(",")

        if len(row_list) > 1:
            flag = row_list[1]
        else:
            flag = ""

        if len(row_list) > 2:
            path = row_list[2]
        else:
            path = ""

        ret_dict[row_list[0]] = {
            "flag": flag,
            "path": path
        }

    return ret_dict
