# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/6 上午9:28
# Author     ：sch
# version    ：python 3.8
# Description：惠州拓普
"""

from typing import Any
from urllib.parse import urlencode

from common import xrequest, xenum
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoutuobang release v1.0.0.3",
        "device": "203",
        "feature": ["从MES获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-06 16:19  条码校验，上传数据
date: 2024-08-19 11:15  新增从MES获取条码+修改部分参数
date: 2024-08-22 14:21  修改上传数据的业务逻辑
""",
    }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": ""
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": ""
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": ""
        },
    }

    form = {
        "site": {
            "ui_name": "工厂/站点",
            "value": "1001"
        },
        "user_id": {
            "ui_name": "登录MES用户账号",
            "value": "SITE_ADMIN"
        },
        "password": {
            "ui_name": "登录MES密码",
            "value": ""
        },
        "operation": {
            "ui_name": "操作",
            "value": "admin"
        },
        "resource": {
            "ui_name": "资源",
            "value": "D01FCT1"
        },
        "program_id": {
            "ui_name": "测试程序版本号",
            "value": "XLD 54123442"
        },
        "order_id_ui": {
            "ui_name": "工单号",
            "value": "XLD 54123442"
        },
        "test_threshold": {
            "ui_name": "test_threshold",
            "value": "2"
        },
        "pass_threshold": {
            "ui_name": "pass_threshold",
            "value": "2"
        },
        "main_nc_code": {
            "ui_name": "工序主要不良代码",
            "value": "NICT"
        },
        "tester_hw_rev": {
            "ui_name": "测试硬件版本",
            "value": ""
        },
        "field_description": {
            "ui_name": "工序描述",
            "value": ""
        },
        "field_name": {
            "ui_name": "工序名称",
            "value": ""
        },
        "field_value": {
            "ui_name": "工序值",
            "value": ""
        },

    }

    combo = {
        "pass_station": {
            "ui_name": "是否过站处理",
            "item": ["Y", "N"],
            "value": "Y"
        },
    }

    def __init__(self):
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        site = other_vo.get_value_by_cons_key("site")

        pcb_sn = other_vo.get_pcb_sn()

        get_sn_param = {
            "site": site,
            "sfc": pcb_sn
        }

        query = urlencode(get_sn_param)

        api_query = f"{api_url_get_sn}?{query}"
        ret = xrequest.RequestUtil.get(api_query, {})
        if ret.get("type") != "success":
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('message')}")

        ret_data = ret.get("data", [])

        ret_sn = []
        for i in ret_data:
            ret_sn.append(i.get("sfc"))

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        site = other_vo.get_value_by_cons_key("site")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        operation = other_vo.get_value_by_cons_key("operation")
        resource = other_vo.get_value_by_cons_key("resource")
        order_id_ui = other_vo.get_value_by_cons_key("order_id_ui")

        order_id = other_vo.get_order_id()
        if not order_id:
            order_id = order_id_ui

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", "未扫到条码！")

        check_param = {
            "site": site,
            "userid": user_id,
            "password": password,
            "operation": operation,
            "resrce": resource,
            "shop_order": order_id,
            "sfcs": sn_list
        }

        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
        if ret.get("type") != "success":
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        site = data_vo.get_value_by_cons_key("site")
        user_id = data_vo.get_value_by_cons_key("user_id")
        password = data_vo.get_value_by_cons_key("password")
        operation = data_vo.get_value_by_cons_key("operation")
        resource = data_vo.get_value_by_cons_key("resource")
        program_id = data_vo.get_value_by_cons_key("program_id")
        order_id_ui = data_vo.get_value_by_cons_key("order_id_ui")

        test_threshold = data_vo.get_value_by_cons_key("test_threshold")
        pass_threshold = data_vo.get_value_by_cons_key("pass_threshold")
        main_nc_code = data_vo.get_value_by_cons_key("main_nc_code")
        tester_hw_rev = data_vo.get_value_by_cons_key("tester_hw_rev")
        field_description = data_vo.get_value_by_cons_key("field_description")
        field_name = data_vo.get_value_by_cons_key("field_name")
        field_value = data_vo.get_value_by_cons_key("field_value")
        pass_station = data_vo.get_value_by_cons_key("pass_station")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data_list = []

        order_id = pcb_entity.order_id
        if not order_id:
            order_id = order_id_ui

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            # only_one_robot_str = "OK"
            # only_one_repair_str = "OK"

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():

                for alg_entity in comp_entity.yield_alg_entity():
                    comp_data_list.append({
                        "actual": alg_entity.test_val,
                        "description": comp_entity.designator,
                        "high_limit": alg_entity.max_threshold,
                        "low_limit": alg_entity.min_threshold,
                        "measure_name": f"{comp_entity.designator}_{alg_entity.test_name}",
                        "measure_status": comp_entity.get_final_result("PASS", "PASS", "FAIL"),
                        "unit_of_meas": "",
                        "parametricMeasureCustomList": [
                            {
                                "property_name": "MachineResult",
                                "property_value": comp_entity.robot_ng_str,
                            },
                            {
                                "property_name": "RepairResult",
                                "property_value": comp_entity.repair_ng_str,
                            },
                        ]
                    })

                # if comp_entity.is_robot_ng() and only_one_robot_str == "OK":
                #     only_one_robot_str = comp_entity.robot_ng_str
                #
                #     if comp_entity.is_repair_ng() and only_one_repair_str == "OK":
                #         only_one_repair_str = comp_entity.repair_ng_str

            item = {
                "site": site,
                "userid": user_id,
                "password": password,
                "operation": operation,
                "resrce": resource,
                "program_id": program_id,
                "program_rev": pcb_entity.project_name,
                "test_threshold": test_threshold,
                "pass_threshold": pass_threshold,
                "main_nc_code": main_nc_code,
                "passStation": pass_station,
                "shop_order": order_id,
                "sfc": barcode,
                "tester_hw_rev": tester_hw_rev,
                "test_status": board_entity.get_repair_result("PASS", "FAIL"),
                "parametricCustomList": [{
                    "field_description": field_description,
                    "field_name": field_name,
                    "field_value": field_value,
                }],
                "parametricMeasureInfoList": comp_data_list,

            }

            if pcb_entity.is_repair_ng():
                if board_entity.is_repair_ng():
                    board_data_list.append(item)
                else:
                    self.log.warning(f"该拼板的数据将不会上传！")
                    self.log.warning(f"测试结果里面有子板FAIL 则只上传子板FAIL的数据到MES。PASS子板数据不上传MES！")
            else:
                self.log.info(f"整板pass/repass，所有拼板的数据都将上传！")
                board_data_list.append(item)

        ret = xrequest.RequestUtil.post_json(api_url_data, board_data_list)

        ret_msg_list = []
        for i in ret:
            if i.get("type") != "success":
                ret_msg_list.append(i.get("message"))

        if ret_msg_list:
            err_msg = "\n".join(ret_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_msg}")

        return self.x_response()
