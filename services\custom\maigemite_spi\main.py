# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/11/7 上午10:17
# Author     ：sch
# version    ：python 3.8
# Description：麦格米特SPI   https://jira.cvte.com/browse/ATAOI_2019-38082
"""
import json
import time
from typing import Any

import httpx

from common import xrequest, xenum
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "maigemite_spi release v1.0.0.24",
        "device": "AIS63X",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-11-07 10:20  条码校验,上传数据
date: 2024-11-14 10:33  新增从mes获取条码功能
date: 2024-11-14 16:07  增加请求头，以及修改参数：ObjectValue-->ObjecValue
date: 2024-11-15 10:51  details参数 OK的板卡，默认传一条OK的记录
date: 2024-11-15 16:11  所有异常都需要弹窗提示
date: 2024-11-20 17:01  将`条码校验`的接口移到【发送mes】的流程里调用
date: 2024-11-26 10:19  details参数改成传xml格式
date: 2024-11-26 16:21  增加content-type
date: 2024-11-26 16:38  修改请求方式
date: 2024-12-09 16:23  增加【维修站触发从mes获取条码】
date: 2024-12-10 10:02  条码校验按整板条码校验
date: 2024-12-10 10:38  没有板边条码则使用第一个拼板去校验
date: 2024-12-11 11:26  整板部分NG，只上传NG的拼板，PASS的拼板不上传
date: 2024-12-13 10:14  兼容维修站的条码校验
date: 2024-12-13 14:55  xml格式添加字段task_order_code
date: 2025-01-07 09:47  增加配置项：上传数据超时时间
date: 2025-01-21 11:11  jira:34860,增加xml格式段落
date: 2025-02-07 15:29  增加分板发送逻辑
date: 2025-03-14 00:32  use board sn check
date: 2025-03-20 16:06  jira->ATAOI_2019-38082: SPI版本初始化
date: 2025-03-21 22:45  1
""", }

    form = {
        "task_order": {
            "ui_name": "生产任务",
            "value": "",
        },
        "sequence": {
            "ui_name": "工序",
            "value": "",
        },
        "work_line": {
            "ui_name": "线体",
            "value": "",
        },
        "work_station": {
            "ui_name": "工站",
            "value": "3",
        },
        "work_device": {
            "ui_name": "设备ID",
            "value": "5",
        },
        "department": {
            "ui_name": "部门",
            "value": "",
        },
        "worker_leader": {
            "ui_name": "领班",
            "value": "",
        },
        "worker": {
            "ui_name": "作业人员",
            "value": "",
        },
        "mes_user": {
            "ui_name": "mes用户",
            "value": "",
        },
    }

    combo = {
        "work_shift_x": {
            "ui_name": "班次",
            "item": ["A", "B"],
            "value": "A",
        },
    }

    other_form = {
        "authorization": {
            "ui_name": "Authorization",
            "value": "Bearer 1",
        },

        "api_url_check_other": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data_other": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
        "api_url_get_sn_other": {
            "ui_name": "接口URL(获取条码)",
            "value": "",
        },

    }

    other_combo = {
        "data_api_timeout": {
            "ui_name": "上传数据超时时间(秒)",
            "item": ["1", "3", "5", "10", "15", "20", "30", "60", "120"],
            "value": "5",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        work_shift_x_item = getattr(main_window, "combo_work_shift_x")
        work_shift_x_item.setEditable(True)

        self.common_config["check_barcode_setting1"] = xenum.CheckSetting1.CheckFirst

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn_other")
        authorization = other_vo.get_value_by_cons_key("authorization")

        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }

        pcb_sn = other_vo.get_pcb_sn()

        get_sn_param = {
            "Data": [
                {
                    "Key": "lot_number",
                    "Value": pcb_sn
                }
            ]
        }

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, get_sn_param, headers=headers)

        if str(ret.get("Code")) != "200":
            return self.x_response("false", f"mes接口异常，error：{ret.get('Message')}")

        ret_sn = ret.get('ObjectData')

        return self.x_response("true", ret_sn)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check_other")
        task_order = other_vo.get_value_by_cons_key("task_order")
        sequence = other_vo.get_value_by_cons_key("sequence")
        worker = other_vo.get_value_by_cons_key("worker")
        authorization = other_vo.get_value_by_cons_key("authorization")

        sn_list = other_vo.list_sn()

        headers = {
            "Authorization": authorization,
        }

        # barcode_map = other_vo.get_barcode_map()

        ret_res = self.x_response()
        ret_msg_list = []

        self.log.info(f"主软件触发的【条码校验】...")
        for sn in sn_list:
            check_param = {
                "Data": [{
                    "Key": "json",
                    "Value": "",
                    "ObjecValue": {
                        "TaskOrder": task_order,
                        "Sequence": sequence,
                        "Worker": worker,
                        "BarCode": sn,
                    },
                    "ParaType": "1",
                }]
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param, headers=headers)

            if str(ret.get("Code")) != "200":
                ret_msg_list.append(f"mes接口异常，error：{ret.get('Message')}")

        if ret_msg_list:
            return self.x_response("false", f"\n".join(ret_msg_list))

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data_other")
        task_order = data_vo.get_value_by_cons_key("task_order")
        sequence = data_vo.get_value_by_cons_key("sequence")
        worker = data_vo.get_value_by_cons_key("worker")
        work_line = data_vo.get_value_by_cons_key("work_line")
        work_station = data_vo.get_value_by_cons_key("work_station")
        work_shift_x = data_vo.get_value_by_cons_key("work_shift_x")
        work_device = data_vo.get_value_by_cons_key("work_device")
        department = data_vo.get_value_by_cons_key("department")
        worker_leader = data_vo.get_value_by_cons_key("worker_leader")
        mes_user = data_vo.get_value_by_cons_key("mes_user")
        authorization = data_vo.get_value_by_cons_key("authorization")
        data_api_timeout = data_vo.get_value_by_cons_key("data_api_timeout", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        headers = {
            "Authorization": authorization,
            "Content-Type": "application/json"
        }

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

        pcb_result = pcb_entity.get_repair_result("PASS", "FAIL")

        data_param = {
            "Data": [{
                "Key": "json",
                "Value": "",
                "ObjecValue": {
                    "TaskOrder": task_order,
                    "WorkLine": work_line,
                    "WorkStation": work_station,
                    "WorkShift": work_shift_x,
                    "WorkDevice": work_device,
                    "Department": department,
                    "Sequence": sequence,
                    "Worker": worker,
                    "WorkLeader": worker_leader,
                    "BarCode": pcb_sn,
                    "Result": pcb_result,
                    "details": "",
                    "Data": "",
                    "mes_user": mes_user
                },
                "ParaType": "1",
            }]
        }

        self.log.info(f"请求url：{api_url_data} 请求参数：\n{data_param}")
        t1 = time.time()
        ret = httpx.post(api_url_data, json=data_param, headers=headers, timeout=data_api_timeout).json()
        self.log.info(f"接口响应：{ret}")
        self.log.info(f"time cost: {time.time() - t1}")

        if str(ret.get("Code")) != "200":
            ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('Message')}")

        return ret_res
