#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2023/4/8
# Author: sunchangheng
import json
import socket
import traceback
from datetime import datetime

from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import *

from common import xutil, xcons
from services.route import engine
from templates.fake_send import Ui_Form

"""
# File       : install.py
# Time       ：2024/6/21 下午4:11
# Author     ：sch
# version    ：python 3.8
# Description： 工具-模拟主软件触发
"""


class FakeSendViews(QWidget, Ui_Form):
    upload_log = pyqtSignal(str)

    def __init__(self):
        super(FakeSendViews, self).__init__()
        self.setupUi(self)

        review_path = xutil.CacheUtil.get("setting_fake_send_path")
        self.line_review_path.setText(review_path)

        setting_fake_barcode_list = xutil.CacheUtil.get("setting_fake_barcode_list", "sn1,sn2,sn3")
        self.line_sn_list.setText(setting_fake_barcode_list)

        self.host = '127.0.0.1'
        self.port = engine.mes_config_port
        self.buffer_size = 4096

        self.text_log_print.setReadOnly(True)

        self.update_slot()

    def update_slot(self):
        """
        连接各种槽
        """
        self.btn_send_data.clicked.connect(self.send_data_on_click)
        self.btn_get_sn.clicked.connect(self.get_sn_on_click)
        self.btn_check_sn.clicked.connect(self.check_sn_on_click)
        self.btn_send_device_status.clicked.connect(self.send_device_status_on_click)
        self.btn_device_status_v3.clicked.connect(self.send_device_status_v3_on_click)
        self.btn_send_idle_status.clicked.connect(self.send_idle_status_on_click)
        self.btn_select_review_path.clicked.connect(self.select_review_path_on_click)

        self.btn_clear_log.clicked.connect(self.clear_log_info_on_click)

        self.upload_log.connect(self.update_log_on_emit)  # noqa

    def clear_log_info_on_click(self):
        """
        清除日志
        :return:
        """
        self.text_log_print.setPlainText("")

    def update_log_on_emit(self, msg: str = ""):
        self.text_log_print.appendHtml(msg)

    def log_info_to_window(self, status: bool = True, msg: str = "ok"):
        time_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if status:
            msg = f"<span style='color:green'>{time_now} {msg}</span>"
        else:
            msg = f"<span style='color:red'>{time_now} {msg}</span>"

        self.upload_log.emit(msg)  # noqa

    def send_json_data_to_mes_config(self, json_data: dict):
        """
        发送mes数据给mes配置器
        """
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(5)

        try:
            client.connect((self.host, self.port))
            send_data = json.dumps(json_data)
            client.send(bytes(send_data, encoding="utf-8"))
            receive_data = client.recv(2048)
            ret_data = json.loads(receive_data)
            print("---------------ret data------------")
            print(ret_data)
            print("---------------ret data------------")
        except Exception as e:
            self.log_info_to_window(False, f"其他异常:{e}")
            self.log_info_to_window(False, traceback.format_exc())

        finally:
            client.close()

    def _base_send_call_data(self, func_name, func_args, other_param=None):
        """

        :param func_name: 函数名称： （CheckBarcode，检查条码）（GetBadBoard，获取条码）（GetBarcodeList，获取条码列表）
        :param func_args:
        :return:
        """
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(5)

        try:
            base_param = {
                "funcName": func_name,
                "funcArgs": func_args,
            }
            if other_param and type(other_param) is dict:
                base_param.update(other_param)

            client.connect((self.host, self.port))
            send_data = json.dumps(base_param)

            self.log_info_to_window(True, f"请求接口: {self.host}:{self.port} 请求参数:{send_data}")
            client.send(bytes(send_data, encoding="utf-8"))
            receive_data = client.recv(self.buffer_size)
            # ret_data: {'key': '2022.05.23 12:15:44', 'result': False, 'string': '找不到此序号讯息H065W232392173402'}
            ret_data = json.loads(receive_data)
            self.log_info_to_window(True, f"接口响应:{ret_data}")

            if not ret_data.get("result"):
                self.log_info_to_window(False, ret_data.get('string'))
                return

        except Exception as e:
            self.log_info_to_window(False, f"其他异常:{e}")
            self.log_info_to_window(False, traceback.format_exc())

        finally:
            client.close()

    def send_data_on_click(self):
        """
        模拟主软件发送mes

        param  T_20220523164745910_1_NG数据绝对路径，
        如：/home/<USER>/aoi/run/results/t.001/20220523/T_20220523164745910_1_NG
        :return:
        """
        self.log_info_to_window(True, f"------模拟主软件触发[发送数据到mes]--------")

        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.settimeout(5)

        review_path = self.line_review_path.text()
        self.log_info_to_window(True, f"review path: {review_path}")

        if not review_path:
            self.log_info_to_window(False, "请先选择路径")
            return

        xutil.CacheUtil.set("setting_fake_send_path", review_path)

        is_repair_send = self.send_type_repair.isChecked()

        try:
            client.connect((self.host, self.port))
            send_data = json.dumps({
                "ReviewPath": review_path,
                "InspectMesType": "repair" if is_repair_send else "inspector"
            })
            self.log_info_to_window(True, f"请求接口: {self.host}:{self.port} 请求参数:{send_data}")
            client.send(bytes(send_data, encoding="utf-8"))
            receive_data = client.recv(self.buffer_size)
            ret_data = json.loads(receive_data)
            self.log_info_to_window(True, f"接口响应:{ret_data}")

            if not ret_data.get("MesStatus"):
                self.log_info_to_window(ret_data.get('Description'))
                return

        except Exception as e:
            self.log_info_to_window(False, f"其他异常:{e}")
            self.log_info_to_window(False, traceback.format_exc())
        finally:
            client.close()

    def get_sn_on_click(self):
        self.log_info_to_window(True, f"------模拟主软件触发[从mes获取条码]--------")
        pcb_sn = self.line_pcb_sn.text()

        if not pcb_sn:
            self.log_info_to_window(False, "请输入一个条码用于模拟测试")
            return

        self.send_json_data_to_mes_config({
            "funcArgs": [pcb_sn],
            "funcName": "GetBarcodeList",
            "trackIndex": 0,
            "orderID": "fake_order_id",
            "barcodeList": {"-2": "", "-1": "", "1": "", "2": "", "3": ""},
            "projectName": "333.001-T_A",
        })

    def check_sn_on_click(self):
        self.log_info_to_window(True, f"------模拟主软件触发[条码校验]--------")
        sn_str = self.line_sn_list.text()
        xutil.CacheUtil.set("setting_fake_barcode_list", sn_str)

        sn_list = sn_str.split(",")

        if not sn_list:
            self.log_info_to_window(False, "请输入一个条码用于模拟测试")
            return

        self.send_json_data_to_mes_config({
            "funcArgs": [sn_list],
            "funcName": "CheckBarcode",
            "projectName": "333.001-T_A",
            "orderID": "order1",
            "trackIndex": 0,
            "barcodeList": {"-2": "", "-1": "", "1": "", "2": "", "3": ""}
        })

    def send_device_status_on_click(self):
        self.log_info_to_window(True, f"------模拟主软件触发[发送设备状态]--------")
        device_status = self.combo_device_status.currentText()

        device_code = xcons.DEVICE_STATUS.get(device_status, '404')

        if device_code == '02':
            device_code_new = '01'
        elif device_code == '03':
            device_code_new = '02'
        else:
            device_code_new = device_code

        self.send_json_data_to_mes_config({
            "funcArgs": [
                device_status,
                "admin"
            ],
            "funcName": "SendDeviceStatus",
            "userName": "admin",
            "statusCode": device_code_new,
            "statusDesc": device_status,
            "projectName": "333.001-T_A",
            "orderID": "order1"
        })

    def send_device_status_v3_on_click(self):
        self.log_info_to_window(True, f"------模拟主软件触发[发送设备状态V3]--------")

        device_status_v3 = self.combo_device_status_v3.currentText()
        print(device_status_v3)

        ret1, ret2 = device_status_v3.split("-->")

        self.send_json_data_to_mes_config({
            "funcArgs": [
                "",
                "admin"
            ],
            "funcName": "SendDeviceStatus",
            "userName": "admin",
            "statusCode": "",
            "statusDesc": "",
            "statusCodeV3": ret1,
            "statusDescV3": ret2,
            "projectName": "333.001-T_A",
            "orderID": "order1",
        })

    def send_idle_status_on_click(self):
        self.log_info_to_window(True, f"------模拟主软件触发[空闲状态]--------")

        self._base_send_call_data("CycleCallMes", [])

    def select_review_path_on_click(self):
        review_path = QFileDialog.getExistingDirectory(self, "选择数据包文件夹")

        self.line_review_path.setText(review_path)
