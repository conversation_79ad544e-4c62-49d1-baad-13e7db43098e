# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/21 下午2:08
# Author     ：sch
# version    ：python 3.8
# Description：合肥聚川
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

txt_pcb_panel_template = """程序名：{pcb_project_name}
测试时间：{pcb_test_time}
操作员：{pcb_repair_user}
整板条码：{pcb_sn}
整板结果：{pcb_final_result}
拼板数量：{pcb_board_number}
拼板复判NG数量：{pcb_board_user_ng_number}
器件总数：{pcb_comp_number}
器件复判不良总数：{pcb_comp_user_ng_number}

{BoardData}
"""

txt_board_panel_template = """
================ No: {board_no} ================ 结果：{board_final_result}  --> 小板SN: {board_sn}{CompData}
"""

txt_comp_panel_template = """
---位号：{comp_designator}  料号：{comp_part}  封装：{comp_package} 　类型：{comp_type}  检测不良代码：{comp_robot_code}　 检测结果：{comp_robot_result}  复判不良代码：{comp_user_code}  复判结果：{comp_user_result}  器件图片：{comp_image}"""


class Engine(BaseEngine):
    version = {
        "title": "hefeijuchuan release v1.0.0.3",
        "device": "AIS430",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-21 14:09  生成本地标准txt格式
date: 2023-07-31 15:27  改成生成json格式
date: 2023-08-01 17:15  文件后缀改为.json
""", }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        track_index = pcb_entity.track_index

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_list.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data_list.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data_list,
            })

        pcb_data = {
            "pcb_sn": pcb_sn,
            "pcb_track_line": track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data_list
        }

        # pcb_content = txt_pcb_panel_template.format(**pcb_data)

        dst_path = f"{save_path}/{track_index}"
        xutil.FileUtil.ensure_dir_exist(dst_path)
        dst_filepath = f"{dst_path}/{pcb_sn}_{time_str}.json"
        # xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)
        xutil.FileUtil.dump_json_to_file(dst_filepath, pcb_data)

        return self.x_response()
