# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : changshuxiapu.py
# Time       ：2023/4/6 上午10:42
# Author     ：sch
# version    ：python 3.8
# Description：常熟夏普
"""
import socket
import time
import traceback
from typing import Any

from common.xutil import log, x_response
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

TIMEOUT = 6

sk = socket.socket()
sk.settimeout(TIMEOUT)
sk_is_connect = False


def x_socket_req_mes(host, port, content: bytes) -> str:
    """
    标准请求方式
    :param host:
    :param port:
    :param content: 发送的内容
    :return:
    """
    global sk, sk_is_connect
    log.info(f"请求地址：{host}:{port} 请求参数：{content}")
    try:
        if not sk_is_connect:
            sk.connect((host, port))
            sk_is_connect = True
            log.info("socket 连接成功...")

        sk.send(content)
        ret_data = sk.recv(1024)

    except Exception as e:
        log.warning(traceback.format_exc())
        log.warning(f"error: {e}, 连接已断开，1s后重新连接...")
        time.sleep(1)
        sk = socket.socket()
        sk.settimeout(TIMEOUT)

        sk.connect((host, port))
        log.info(f"重新连接成功，[{content}]数据已重新发送")
        sk.send(content)
        ret_data = sk.recv(1024)

    log.info(f"接口响应：{ret_data}")
    ret_data = ret_data.decode("utf8")
    return ret_data


class XiaPuEngine(BaseEngine):
    version = {
        "title": "changshuxiapu release v1.1.0.4",
        "device": "20x,30x,40x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-07 12:38  init
date: 2023-04-09 00:13  delete spi_ param
""",
    }

    combo = {
        "ng_comp": {
            "item": ["OK", "漏件", "错件", "反件", "立碑", "偏移", "翻转", "浮高", "损件", "露铜", "少锡",
                     "多锡", "未出脚", "孔洞", "连锡", "锡珠", "翘脚", "弯脚", "异物", "条码识别", "Marker搜索",
                     "多件", "溢胶", "虚焊", "脏污", "坏板", "定位", "数目错误", "少涂/多涂", "少涂",
                     "多涂", "气泡", "划痕", "距离", "锡膏检测", "共线性", "CPU插针检测"],
            "value": "OK",
            "ui_name": "统计不良"
        },
    }

    form = {
        "host": {
            "ui_name": "Mes Host",
            "value": "127.0.0.1"
        },
        "port": {
            "ui_name": "Mes Port",
            "value": "9093"
        },
        "line": {
            "ui_name": "线别",
            "value": "产线1"
        },

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict,
                         other_param: Any) -> dict:
        ng_comp_map = {'OK': '0', '漏件': '1', '错件': '2', '反件': '3', '立碑': '4', '偏移': '5', '翻转': '6',
                       '浮高': '7', '损件': '8', '露铜': '9', '少锡': '10', '多锡': '11', '未出脚': '12', '孔洞': '13',
                       '连锡': '14', '锡珠': '15', '翘脚': '16', '弯脚': '17', '异物': '18', '条码识别': '19',
                       'Marker搜索': '20', '多件': '21', '溢胶': '22', '虚焊': '23', '脏污': '24', '坏板': '25',
                       '定位': '26', '数目错误': '27', '少涂/多涂': '28', '少涂': '29', '多涂': '30', '气泡': '31',
                       '划痕': '32', '距离': '33', '锡膏检测': '34', '共线性': '35', 'CPU插针检测': '36'}

        mes_host = data_vo.get_value_by_cons_key("host")
        mes_port = data_vo.get_value_by_cons_key("port")
        print("mes port", mes_port)

        try:
            mes_port = int(mes_port)
        except Exception as e:
            log.warning(f"error: {e}")
            return x_response("false", "端口号必须为数字！")

        line = data_vo.get_value_by_cons_key("line")
        custom_ng_code = ng_comp_map.get(data_vo.get_value_by_cons_key("ng_comp"), "0")

        log.info(data_vo.pcb_entity)
        comp_ng_count = 0

        pcb_sn = data_vo.pcb_entity.pcb_barcode

        pcb_robot_result = "OK" if data_vo.pcb_entity.pcb_robot_result else "NG"

        for board_entity in data_vo.pcb_entity.yield_board_entity():
            log.info(board_entity)

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_ng_code == custom_ng_code:
                    comp_ng_count += 1

        s_data = f"{line},{pcb_sn},{pcb_robot_result},{comp_ng_count}".encode("utf8")
        ret_str = x_socket_req_mes(mes_host, mes_port, s_data)

        if not ret_str.startswith("OK"):
            return x_response("false", f"接口返回内容：[{ret_str}]，接口未返回OK，正在重新发送...")

        return x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        sn_list = other_vo.list_sn()

        mes_host = other_vo.get_value_by_cons_key("host")
        mes_port = other_vo.get_value_by_cons_key("port")
        line = other_vo.get_value_by_cons_key("line")

        try:
            mes_port = int(mes_port)
        except Exception as e:
            log.warning(f"error: {e}")
            return x_response("false", "端口号必须为数字！")

        pcb_sn = sn_list[0]

        s_data1 = f"{line},{pcb_sn}".encode("utf8")
        ret_str = x_socket_req_mes(mes_host, mes_port, s_data1)

        if not ret_str.startswith("CONTINUE"):
            return x_response("false", f"接口返回内容：[{ret_str}]，接口未返回CONTINUE，正在重新发送...")

        return x_response()
