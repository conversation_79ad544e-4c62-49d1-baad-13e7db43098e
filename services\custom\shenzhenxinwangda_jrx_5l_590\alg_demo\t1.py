# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t1.py
# Time       ：2024/12/14 上午10:28
# Author     ：sch
# version    ：python 3.8
# Description：获取算法数据
"""
import json
import sqlite3


def get_alg_data(alg_db_path: str = '/home/<USER>/Desktop/欣旺达现场调试/mes_data/T_20241216145650545_1') -> dict:
    """
    获取算法数据
    """
    conn = sqlite3.connect(f"{alg_db_path}/AlgorithmInfo.db")
    data_map = {}

    cur = conn.cursor()

    try:
        cur.execute("SELECT * FROM compalgorithmdata;")  # 替换为您的表名

        # 获取所有行的结果列表
        rows = cur.fetchall()

        # 遍历结果并打印每一行
        for row in rows:
            data_map[row[1]] = {
                "alg_name": row[3],
                "alg_val": json.loads(row[4])
            }
    finally:
        cur.close()
        conn.close()

    return data_map


if __name__ == '__main__':
    # 连接到SQLite数据库
    # 数据库文件是 'example.db'
    # 如果文件不存在，会自动在当前目录创建
    ret_data = get_alg_data()
    print(json.dumps(ret_data, indent=4))
