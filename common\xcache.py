# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xcache.py.py
# Time       ：2024/12/23 上午9:51
# Author     ：sch
# version    ：python 3.8
# Description：上传离线缓存数据相关
"""
import os
import time

from common import xrequest, xcons
from common.xutil import FileUtil, log, DateUtil


def cache_upload_failed_data(bak_path, pcb_param):
    """
    缓存上传失败的数据
    :param bak_path:
    :param pcb_param:
    :return:
    """
    time_now = int(time.time())
    time_fmt = DateUtil.time_to_fmt_time(time_now, xcons.FMT_TIME_DEFAULT6)
    bak_filepath = f"{bak_path}/offline_{time_fmt}_{time_now}.json"
    FileUtil.dump_json_to_file(bak_filepath, pcb_param)
    log.info(f"保存缓存数据成功！")


def re_upload_cache_data(
        bak_path,
        api_url,
        return_key="code",
        ret_ok_val="200",
        always_true: bool = False,
        expired_second=5 * 60 * 60 * 24  # 5天
):
    """
    重新上传缓存数据
    :param bak_path:
    :param api_url:
    :param return_key:
    :param ret_ok_val:
    :param always_true: 如果不需要判断接口返回状态码，则把该参数设置为true
    :param expired_second: 过期时间，单位：秒
    :return:
    """
    time_now = int(time.time())

    file_data = [f for f in os.listdir(bak_path) if "offline_" in f]
    total_count = len(file_data)

    log.info(f"将要上传的数据量：{total_count}")

    error_count = 0
    ok_count = 0

    old_data_path = f"{bak_path}/old_data"
    FileUtil.ensure_dir_exist(old_data_path)

    for file in file_data:

        try:
            item_list = file.split("_")
            cache_time = int(item_list[2].replace(".json", ""))

            bak_file = f"{bak_path}/{file}"

            if time_now > cache_time + expired_second:
                # 该数据已过期，最后一次上传，如果还上传不成功，将移动到old_data目录内
                expired_data = True
            else:
                # 数据未过期
                expired_data = False

            try:
                cache_param = FileUtil.load_json_file(bak_file)
                ret = xrequest.RequestUtil.post_json(api_url, cache_param)

                if str(ret.get(return_key)) == ret_ok_val or always_true:
                    log.info(f"数据上传成功，缓存数据将会删除！")
                    os.remove(bak_file)

                    ok_count += 1
                else:
                    error_count += 1

                    if expired_data:
                        log.warning(f"数据上传失败，数据已过期，数据将移动到old_data目录！")
                        dst_filepath = f"{old_data_path}/{file}"
                        FileUtil.move_file(bak_file, dst_filepath)

            except Exception as err:
                log.warning(f"数据重传失败，err:{err} --> {file}")
                error_count += 1

                if expired_data:
                    log.warning(f"数据上传失败，数据已过期，数据将移动到old_data目录！")
                    dst_filepath = f"{old_data_path}/{file}"
                    FileUtil.move_file(bak_file, dst_filepath)

        except Exception as err:
            log.warning(f"数据重传失败，err:{err} --> {file}")

    return total_count, ok_count, error_count
