# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/19 上午10:30
# Author     ：sch
# version    ：python 3.8
# Description：时代新安
"""
import base64
from collections import defaultdict
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


def get_authorization_header(username, password):
    auth_string = f"{username}:{password}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()

    authorization = 'Basic ' + encoded_auth
    return authorization


get_sn_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:cel="http://celltest.ws.atlmes.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <cel:getCellListByTrayId>
         <!--Optional:-->
         <CellListRequest>
            <site>{site}</site>
            <processLot>{barcode}</processLot>
            <mode>{mode}</mode>
         </CellListRequest>
      </cel:getCellListByTrayId>
   </soapenv:Body>
</soapenv:Envelope>"""

check_sn_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:mac="http://machine.ws.atlmes.com/">
   <soapenv:Header/>
   <soapenv:Body>
      <mac:processLotStartForSfc>
         <!--Optional:-->
         <StartProcessLotForSfcRequest>
            <site>{site}</site>
            <user>{user}</user>
            <operation>{operation}</operation>
            <!--Optional:-->
            <operationRevision>{operation_revision}</operationRevision>
            <resource>{resource}</resource>
            <!--1 or more repetitions:-->
{sn_list_str}
            <!--Optional:-->
            <columnOrRowFirst>{mode}</columnOrRowFirst>
         </StartProcessLotForSfcRequest>
      </mac:processLotStartForSfc>
   </soapenv:Body>
</soapenv:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?><SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <SOAP-ENV:Body>
    <yq1:dataCollectForSfcEx xmlns:yq1="http://machine.ws.atlmes.com/">
      <SfcDcExRequest>
        <site>{site}</site>
        <sfc>{barcode}</sfc>
        <dcGroup>*</dcGroup>
        <dcGroupRevision>#</dcGroupRevision>
        <operation>{operation}</operation>
        <operationRevision>{operation_version}</operationRevision>
        <resource>{resource}</resource>
        <user>{user}</user>
        <activityId>{activity_id}</activityId>{test_data_str}
        <modeProcessSfc>{mode_process}</modeProcessSfc>
        <ncCodeArray>
          <ncCode/>
          <hasNc>false</hasNc>
        </ncCodeArray>
      </SfcDcExRequest>
    </yq1:dataCollectForSfcEx>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>"""

comp_template = """
        <parametricDataArray>
          <name>{name}</name>
          <value>{value}</value>
          <dataType>{data_type}</dataType>
        </parametricDataArray>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "shidaixinan release v1.0.0.15",
        "device": "203，303",
        "feature": ["从MES获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-19 17:12  从mes获取条码，条码校验，上传数据到mes
date: 2023-12-21 10:35  增加window中转
date: 2023-12-23 11:06  增加配置项
date: 2023-12-25 17:46  传输全部器件数据
date: 2023-12-25 23:18  修改`parametricDataArray`参数传参逻辑
date: 2023-12-26 09:21  限制字符长度输出
date: 2024-08-17 10:41  203上传数据格式修改+保存整板图
date: 2024-12-24 14:24  203修改字段名称，加入不良位号
""", }

    combo = {
        "device_model": {
            "ui_name": "机型",
            "item": ["AIS203", "AIS303"],
            "value": "AIS303",
        },
    }

    other_form = {
        "window_ip": {
            "ui_name": "window中转IP",
            "value": "",
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "",
        },
        "api_url": {
            "ui_name": "接口URL",
            "value": "",
        },
        "api_username": {
            "ui_name": "服务器用户名",
            "value": "",
        },
        "api_password": {
            "ui_name": "服务器用户密码",
            "value": "",
        },
        "mode_get_sn": {
            "ui_name": "mode(获取条码)",
            "value": "COLUMN-FIRST",
        },
        "mode_check_sn": {
            "ui_name": "mode(条码校验)",
            "value": "ROW_FIRST",
        },
        "soap_action1": {
            "ui_name": "SOAPAction(获取条码)",
            "value": "CellListRequest",
        },
        "soap_action2": {
            "ui_name": "SOAPAction(条码校验)",
            "value": "StartProcessLotForSfcRequest",
        },
        "soap_action3": {
            "ui_name": "SOAPAction(上传数据)",
            "value": "SfcDcExRequest",
        },
        "content_type": {
            "ui_name": "ContentType",
            "value": "text/xml",
        },
    }

    other_combo = {
        "api_timeout": {
            "ui_name": "接口连接超时设置",
            "item": ["1", "2", "5", "10", "20", "60"],
            "value": "2",
        },
        "mode_process": {
            "ui_name": "过站模式",
            "item": ["MODE_NONE", "MODE_COMPLETE_SFC_POST_DC", "MODE_PASS_SFC_POST_DC", "MODE_START_SFC_PRE_DC"],
            "value": "MODE_NONE",
        },
    }

    form = {
        "site": {
            "ui_name": "站点",
            "value": "",
        },
        "username": {
            "ui_name": "操作员",
            "value": "",
        },
        "operation": {
            "ui_name": "工位",
            "value": "",
        },
        "operation_version": {
            "ui_name": "工位版本",
            "value": "",
        },
        "resource": {
            "ui_name": "设备资源号",
            "value": "",
        },
        "activity_id": {
            "ui_name": "activityId",
            "value": "",
        },
    }

    path = {
        "save_path": {
            "ui_name": "整板图路径",
            "value": "",
        }
    }

    password_style = ["api_password"]

    def __init__(self):

        for i in range(1, 37):
            i_key = str(i)
            self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"][i_key]["check_flag2"] = True

        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["9"]["custom_code"] = "LUTONG"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["9"]["custom_str"] = "露铜"

        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["10"]["custom_code"] = "SHAOXI"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["10"]["custom_str"] = "少锡"

        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["12"]["custom_code"] = "WEICHUJIAO"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["12"]["custom_str"] = "未出脚"

        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["13"]["custom_code"] = "KONGDONG"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["13"]["custom_str"] = "孔洞"

        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["14"]["custom_code"] = "QIAOLIAN"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["14"]["custom_str"] = "桥连"

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        api_username = other_vo.get_value_by_cons_key("api_username")
        api_password = other_vo.get_value_by_cons_key("api_password")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout")
        site = other_vo.get_value_by_cons_key("site")
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        mode_get_sn = other_vo.get_value_by_cons_key("mode_get_sn")
        soap_action1 = other_vo.get_value_by_cons_key("soap_action1")
        content_type = other_vo.get_value_by_cons_key("content_type")

        pcb_sn = other_vo.get_pcb_sn()

        headers = {
            # "User": api_username,
            # "Password": api_password,
            "Authorization": get_authorization_header(api_username, api_password),
            "SOAPAction": soap_action1,
            "Content-Type": content_type
        }

        req_param = get_sn_template.format(**{
            "site": site,
            "barcode": pcb_sn,
            "mode": mode_get_sn,
        })

        self.log.info(f"请求参数--------")
        self.log.info(f"\n{req_param}")
        self.log.info(f"请求参数--------")

        # ret_str = xrequest.RequestUtil.post_xml(api_url, req_param,
        #                                         headers=headers, timeout=int(api_timeout))

        packet_data = {
            "type": 18,
            "request_param": {
                "api_url": api_url_get_sn,
                "body_param": req_param,
                "headers": headers,
                "timeout": int(api_timeout)
            }
        }
        ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)

        if not ret.get('result'):
            return self.x_response("false", f"请求window中转程序失败！")

        ret_str = ret.get('string')

        root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        res_item = root[0][0][0]

        message = ""

        sfc_list = []
        code = "1"
        for i in res_item:
            i_str = i.text if i.text is not None else ""
            if i.tag == "sfcList":
                sfc_list.append(i_str)
            elif i.tag == "code":
                code = i_str
            elif i.tag == 'message':
                message = i_str

        if code != "0":
            return self.x_response("false", f"mes接口异常，获取条码失败! error:{message}")

        return self.x_response("true", ",".join(sfc_list))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        api_username = other_vo.get_value_by_cons_key("api_username")
        api_password = other_vo.get_value_by_cons_key("api_password")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout")
        site = other_vo.get_value_by_cons_key("site")
        username = other_vo.get_value_by_cons_key("username")
        operation = other_vo.get_value_by_cons_key("operation")
        operation_version = other_vo.get_value_by_cons_key("operation_version")
        resource = other_vo.get_value_by_cons_key("resource")
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        mode_check_sn = other_vo.get_value_by_cons_key("mode_check_sn")
        soap_action2 = other_vo.get_value_by_cons_key("soap_action2")
        content_type = other_vo.get_value_by_cons_key("content_type")

        sn_list = other_vo.list_sn()

        headers = {
            # "User": api_username,
            # "Password": api_password,
            "Authorization": get_authorization_header(api_username, api_password),
            "SOAPAction": soap_action2,
            "Content-Type": content_type
        }

        new_sn_list = []
        for sn in sn_list:
            new_sn_list.append(f"            <processLotArray>{sn}</processLotArray>")

        check_sn_param = check_sn_template.format(**{
            "site": site,
            "user": username,
            "operation": operation,
            "operation_revision": operation_version,
            "resource": resource,
            "sn_list_str": "\n".join(new_sn_list),
            "mode": mode_check_sn
        })

        self.log.info(f"请求参数--------")
        self.log.info(f"\n{check_sn_param}")
        self.log.info(f"请求参数--------")

        # ret_str = xrequest.RequestUtil.post_xml(api_url, check_sn_param,
        #                                         headers=headers, timeout=int(api_timeout))

        packet_data = {
            "type": 18,
            "request_param": {
                "api_url": api_url,
                "body_param": check_sn_param,
                "headers": headers,
                "timeout": int(api_timeout)
            }
        }
        ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)

        if not ret.get('result'):
            return self.x_response("false", f"请求window中转程序失败！")

        ret_str = ret.get('string')
        root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        res_item = root[0][0][0]

        code = res_item.find('code').text
        message = res_item.find('message').text

        if code != "0":
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{message}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        global ccdck2_data
        api_url = data_vo.get_value_by_cons_key("api_url")
        api_username = data_vo.get_value_by_cons_key("api_username")
        api_password = data_vo.get_value_by_cons_key("api_password")
        api_timeout = data_vo.get_value_by_cons_key("api_timeout")
        site = data_vo.get_value_by_cons_key("site")
        username = data_vo.get_value_by_cons_key("username")
        operation = data_vo.get_value_by_cons_key("operation")
        operation_version = data_vo.get_value_by_cons_key("operation_version")
        resource = data_vo.get_value_by_cons_key("resource")
        activity_id = data_vo.get_value_by_cons_key("activity_id")
        mode_process = data_vo.get_value_by_cons_key("mode_process")
        window_ip = data_vo.get_value_by_cons_key("window_ip")
        soap_action3 = data_vo.get_value_by_cons_key("soap_action3")
        content_type = data_vo.get_value_by_cons_key("content_type")
        save_path = data_vo.get_value_by_cons_key("save_path")

        device_model = data_vo.get_value_by_cons_key("device_model")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        headers = {
            # "User": api_username,
            # "Password": api_password,
            "Authorization": get_authorization_header(api_username, api_password),
            "SOAPAction": soap_action3,
            "Content-Type": content_type
        }

        all_error_code = ["SHAOXI", "WEICHUJIAO", "KONGDONG", "LUTONG", "QIAOLIAN"]

        pcb_sn = pcb_entity.pcb_barcode

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            ng_comp_data = defaultdict(list)
            cur_ng_data = []
            for comp_entity in board_entity.yield_comp_entity():
                robot_ng_code = comp_entity.robot_ng_code
                repair_ng_str = comp_entity.repair_ng_str
                comp_tag = comp_entity.designator

                if comp_entity.is_repair_ng():
                    cur_ng_data.append(comp_tag)

                if comp_entity.is_robot_ng():
                    if robot_ng_code not in all_error_code:
                        self.log.warning(f"不良代码[{robot_ng_code}]不在支持列表中，支持的不良代码：{all_error_code}")
                        continue

                    comp_result = pcb_entity.get_final_result('OK', f'{repair_ng_str}_复判OK', repair_ng_str)
                    ng_comp_data[robot_ng_code].append(f"{comp_tag}{comp_result}")


            ng_error_code = list(ng_comp_data.keys())
            pass_category = [i for i in all_error_code if i not in ng_error_code]

            self.log.info(f"直通的分类：{pass_category}")

            test_data_str = ""
            count = 0
            for category in pass_category:
                test_data_str += comp_template.format(**{
                    "name": category,
                    "value": 0,
                    "data_type": "NUMBER",
                })
                test_data_str += comp_template.format(**{
                    "name": f"{category}MS",
                    "value": "OK-设备",
                    "data_type": "TEXT",
                })
                count += 2

            for category, comp_list in ng_comp_data.items():
                all_ok = all(map(lambda comp_str: comp_str.endswith('复判OK'), comp_list))

                tmp_value_number = 1
                tmp_value_text = ",".join(comp_list)[:1000]

                if all_ok:
                    # 所有这个分类都复判OK了
                    tmp_value_number = 0
                    tmp_value_text = "OK-复判"

                test_data_str += comp_template.format(**{
                    "name": category,
                    "value": tmp_value_number,
                    "data_type": "NUMBER",
                })
                test_data_str += comp_template.format(**{
                    "name": f"{category}MS",
                    "value": tmp_value_text,
                    "data_type": "TEXT",
                })
                count += 2

            self.log.info(f"count: {count}")

            if device_model == "AIS203":
                board_result = board_entity.get_repair_result(0, 1)
                # board_result2 = board_entity.get_final_result("OK", "OK", "NG")

                ccdck2_data = "0" if board_result == 0 else ",".join(cur_ng_data)[:1000]

                # if board_result == 0:
                #     ccdck2_data = 0
                # else:
                #     ccdck2_data = ",".join(cur_ng_data)
                #     if len(ccdck2_data) > 1000:
                #         ccdck2_data = ccdck2_data[:1000]

                self.log.debug(f"cur_ng_data before usage: {cur_ng_data}")

                test_data_str = f"""
        <parametricDataArray>
          <name>CCDCK</name>
          <value>{board_result}</value>
          <dataType>NUMBER</dataType>
        </parametricDataArray>
        <parametricDataArray>
          <name>CCDCK2</name>
          <value>{ccdck2_data}</value>
          <dataType>TEXT</dataType>
        </parametricDataArray>"""

            board_param = data_template.format(**{
                "site": site,
                "barcode": barcode,
                "operation": operation,
                "operation_version": operation_version,
                "resource": resource,
                "user": username,
                "activity_id": activity_id,
                "test_data_str": test_data_str,
                "mode_process": mode_process,
            })

            # ret_str = xrequest.RequestUtil.post_xml(api_url, board_param,
            #                                         headers=headers, timeout=int(api_timeout))

            self.log.info(f"请求参数--------")
            self.log.info(f"\n{board_param}")
            self.log.info(f"请求参数--------")

            packet_data = {
                "type": 18,
                "request_param": {
                    "api_url": api_url,
                    "body_param": board_param,
                    "headers": headers,
                    "timeout": int(api_timeout)
                }
            }
            ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)
            if not ret.get('result'):
                return self.x_response("false", f"请求window中转程序失败！")

            ret_str = ret.get('string')

            root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
            res_item = root[0][0][0]

            code = res_item.find('code').text
            message = res_item.find('message').text

            if code != "0":
                error_msg = f"mes接口异常，上传数据失败，error：{message}"

        t_src_img = pcb_entity.get_unknown_t_pcb_image()
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        if t_src_img:
            t_dst_filepath = f"{save_path}/{pcb_sn}_{time_file}.jpg"
            xutil.FileUtil.copy_file(t_src_img, t_dst_filepath)

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
