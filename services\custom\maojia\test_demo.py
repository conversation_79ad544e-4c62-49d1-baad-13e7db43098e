# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/2/28 上午10:28
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from minio import Minio
from minio.error import S3Error


def upload_data_to_minio_server(
        minio_server: str,
        access_key: str,
        secret_key: str,
        src_filepath: str,
        dst_bucket_name: str,
        dst_filename: str,
):
    try:
        # 创建MinIO客户端对象
        client = Minio(
            minio_server,
            access_key=access_key,
            secret_key=secret_key,
            secure=False
        )

        print(f"{src_filepath=}")
        print(f"{dst_filename=}")
        ret = client.fput_object(dst_bucket_name, dst_filename, src_filepath, content_type='text/plain')
        print(f"上传成功: {ret=}")

    except S3Error as err:
        print("Error occurred: ", err)
    except Exception as err:
        print(f"上传失败，其他异常：error:{err}")


if __name__ == '__main__':
    # src_file = '/home/<USER>/Desktop/11.txt'
    #
    # upload_data_to_minio_server(
    #     "127.0.0.1:9000",
    #     "kh5LrFNdETJhdGFIiBbn",
    #     "zclayJCC3NXYruZr9P3W1KolFDGVXfkjvyidTVDr",
    #     src_file,
    #     "image",
    #     "2024/02/22/hello_minio.txt"
    # )
    with open(f"/home/<USER>/Desktop/download/hello_minio.txt", 'r') as f:
        print(f.read())

