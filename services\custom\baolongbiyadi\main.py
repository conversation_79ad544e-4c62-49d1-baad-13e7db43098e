# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/12/30 上午10:21
# Author     ：sch
# version    ：python 3.8
# Description：宝龙比亚迪
"""
import os
from typing import Any

from common import xrequest, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "baolongbiyadi release v1.0.0.4",
        "device": "AIS50X",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-12-30 10:21  条码校验，上传数据
date: 2025-01-22 16:48  jira:36472, 数据过站后,依次调用【数据上传】和【图片上传】的接口
date: 2025-02-14 14:26  jira:36472, 修改上传图片逻辑
date: 2025-03-28 15:51  bugfix:修复图片上传失败
""",
    }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:6003/WebService/AOITestService.asmx/LC_AOISaveTestData1"
        },
        "inspector_version": {
            "ui_name": "主软件版本",
            "value": ""
        }
    }

    form = {
        "test_station_name": {
            "ui_name": "工序(工站)",
            "value": "IQC-CAP"
        },
        "station_id": {
            "ui_name": "资源(机台号)",
            "value": "ITCZ_A04-2FT-02_5_LED-CAL"
        },
        "send_image_url": {
            "ui_name": "图片上传url",
            "value": "http://127.0.0.1:6003/bobcat"
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        station_id = other_vo.get_value_by_cons_key("station_id")
        test_station_name = other_vo.get_value_by_cons_key("test_station_name")

        for sn in other_vo.list_sn():
            check_param = {
                "p": "unit_process_check",
                "c": "QUERY_RECORD",
                "sn": sn,
                "test_station_name": test_station_name,
                "station_id": station_id,
            }

            ret = xrequest.RequestUtil.post_form(api_url, check_param, to_json=False)
            if "UNIT OUT OF PROCESS" in ret or (not ret.endswith("OK")):
                return self.x_response("false", f"mes接口异常，SN:{sn}，error：{ret}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        station_id = data_vo.get_value_by_cons_key("station_id")
        test_station_name = data_vo.get_value_by_cons_key("test_station_name")
        inspector_version = data_vo.get_value_by_cons_key("inspector_version")
        send_image_url = data_vo.get_value_by_cons_key("send_image_url")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        error_msg = ""

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn:
                pcb_sn = barcode

            comp_tag_list = []
            comp_ng_str_list = []

            # 收集OK和NG的数据
            ok_data_list = []
            ng_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                alg_val = ""

                # 获取到当前元器件的测试值
                for alg_obj in comp_entity.alg_data:
                    alg_val = alg_obj.get("test_val", "")
                    break

                if comp_entity.is_repair_ng():
                    comp_tag_list.append(comp_entity.designator)
                    comp_ng_str_list.append(comp_entity.repair_ng_str)
                    ng_data_list.append(f"NG:{comp_entity.repair_ng_str}/{alg_val}")
                else:
                    ok_data_list.append(f"OK:{comp_entity.designator}")

            board_result = board_entity.get_repair_result("PASS", "FAIL")
            save_param = {
                "test_station_name": test_station_name,
                "station_id": station_id,
                "c": "ADD_RECORD",
                "sn": barcode,
                "result": board_result,
                "start_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "stop_time": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
            }

            if board_result == "FAIL":
                save_param["list_of_failing_tests"] = ",".join(comp_tag_list)
                save_param["failure_message"] = ",".join(comp_ng_str_list)

            ret = xrequest.RequestUtil.post_form(api_url, save_param, to_json=False)
            ret_lower = ret.lower()
            if ret_lower not in ["0 sfc ok", "0 sfc_ok", "0 arc ok", "0 arc_ok"]:
                error_msg = f"mes接口异常，error：{ret_lower}"

            raw_data_param = {
                "test_station_name": test_station_name,
                "station_id": station_id,
                "c": "RAW_DATA_COLLECT",
                "sn": barcode,
                "result": board_result,
                "test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE),
                "sw_version": inspector_version,
                "project": pcb_entity.project_name,
                "raw_data": ";".join(ok_data_list),
                "raw_data_result": ";".join(ng_data_list)
            }

            ret = xrequest.RequestUtil.post_form(api_url, raw_data_param, to_json=False)
            ret_lower = ret.lower()
            if ret_lower not in ["0 sfc ok", "0 sfc_ok", "0 arc ok", "0 arc_ok"]:
                error_msg = f"mes接口异常，error：{ret_lower}"

        # 获取整版图
        image_list = pcb_entity.list_all_pcb_image_v2()
        self.log.info(f"获取到的图片列表数量: {len(image_list)}")
        # 检查列表是否为空或None,确保image_list确实是一个列表类型, 确保列表的第一个元素不为空
        if not image_list or not isinstance(image_list, list) or not image_list[0]:
            self.log.info("没有获取到整版图数据")
            return self.x_response()

        params = {
            "c": "ADD_PICTURE",
            "test_station_name": test_station_name,
            "station_id": station_id,
            "sn": pcb_sn
        }

        files = []
        # 处理多张图片
        for index, image_path in enumerate(image_list):
            if isinstance(image_path, str) and os.path.exists(image_path):
                #     if pcb_sn:
                #         file_name = f"{pcb_sn}_{index + 1}.jpg"
                #     else:
                #         timestamp = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
                #         file_name = f"{timestamp}_{index + 1}.jpg"
                files.append(("picture", open(image_path, "rb")))

        self.log.info(f"开始上传{len(files)}张图片到图片服务器，URL: {send_image_url}")

        try:
            ret = xrequest.RequestUtil.post_form(
                url=send_image_url,
                body_data={},
                params=params,
                files=files,  # noqa
                timeout=5,
                to_json=False
            )
            # ret = response.text

            if ret.lower() not in ["0 sfc ok", "0 sfc_ok", "0 arc ok", "0 arc_ok"]:
                error_msg = f"图片上传失败，返回值: {ret}"
                self.log.error(error_msg)
                return self.x_response("false", error_msg)

            self.log.info(f"所有{len(files)}张图片上传成功")

        except Exception as e:
            self.log.error(f"图片上传异常: {str(e)}")
            return self.x_response("false", f"图片上传异常: {str(e)}")

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
