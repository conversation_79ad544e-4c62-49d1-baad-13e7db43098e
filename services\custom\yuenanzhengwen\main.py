# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/20 下午2:46
# Author     ：sch
# version    ：python 3.8
# Description：越南正文
"""
import binascii
import json
import os
import shutil
import socket
import time
import traceback
from typing import Any, <PERSON><PERSON>

from common import xcons, xutil
from common.xutil import log, x_response
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


def timing_decorator(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        log.info(f"{func.__name__}执行时间: {execution_time} 秒")
        return result

    return wrapper


def save_socket_response(barcode, ret_body):
    """
    保存接口响应到日志文件
    """
    log_path = f"/home/<USER>/.aoi/log"
    all_file = os.listdir(log_path)

    date_file = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE0)
    time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

    file_path = f"{log_path}/pymes{date_file}.csv"

    if os.path.exists(file_path):
        log_template = xutil.FileUtil.read_file(file_path)
    else:
        log_template = f"时间,板卡条码,发送结果"

    new_row = f"\n{time_now},{barcode},{ret_body}"

    xutil.FileUtil.write_content_to_file(file_path, log_template + new_row)

    # 如果文件超过7个，则删除前面的
    log_list = [item for item in all_file if item.endswith(".csv")]
    log_list.sort(key=lambda x: x.replace('pymes', '').replace('.csv', ''))

    for file in log_list[:-7]:
        remove_file = f"{log_path}/{file}"
        os.remove(remove_file)
        log.info(f"日志已删除：{remove_file}")


def baseN(num, b):
    return ((num == 0) and "0") or \
           (baseN(num // b, b).lstrip("0") + "0123456789abcdefghijklmnopqrstuvwxyz"[num % b])


def checkSum(formatMsgList):
    """
    计算校验和
    """
    checksum = 0
    length = len(formatMsgList)
    for i in range(0, length):
        checksum += int(formatMsgList[i], 16)

    d = ~checksum & 0xFF
    decstr = int(bin(d)[2:], 2)
    base_ret = baseN(decstr, 16)
    base_ret = base_ret.zfill(2)
    hexsum = bytes.fromhex(base_ret)
    return hexsum


def concat_header_and_check_sum(header, payload):
    """
    拼接出socket请求参数
    ps: 此处可以用struct.pack优化 header
    """
    headerMsg = ''.encode('utf-8')
    formatMsgList = header
    datalenglist = []
    msglength = len(payload.encode('utf-8'))
    hexlen = hex(msglength)[2:]
    if len(hexlen) == 1:
        hexlen = '0' + hexlen

    # 补0操作
    if (len(hexlen) % 2) != 0:
        hexlen = '0' + hexlen
    strleng = len(hexlen) / 2
    for i in range(int(strleng)):
        datalenglist.append(hexlen[i * 2:(i + 1) * 2])
        # print(datalenglist)

    datalenglist.reverse()

    for data in datalenglist:
        formatMsgList.append(data)
        # print(formatMsgList)

    bw = 8 - len(hexlen)
    for j in range(int(bw / 2)):
        formatMsgList.append('00')
        # print(formatMsgList)

    for m in formatMsgList:
        headerMsg += bytes.fromhex(m)

    # translation msg
    for k in range(len(payload)):
        bytemsg = binascii.b2a_hex(payload[k].encode('utf-8'))
        formatMsgList.append(bytemsg.decode())
    formatMsg = headerMsg
    formatMsg += payload.encode('utf-8')
    formatMsg += checkSum(formatMsgList)
    # print(formatMsg)
    return formatMsg


@timing_decorator
def send_mes_to_socket_server(host,
                              port,
                              header,
                              payload: str,
                              barcode=None,
                              filter_list: list = None,
                              timeout=5
                              ) -> Tuple[Any, bool]:
    """
    发送数据到 socket 服务端
    """
    log.debug(f"send host: {host} port: {port} header: {header} payload: {payload}")

    send_data = concat_header_and_check_sum(header, payload)

    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    need_upload = False  # 是否需要重传
    try:
        client.settimeout(int(timeout))
        client.connect((host, int(port)))
        client.send(send_data)

        ret_data = client.recv(256)
        log.info(f"<<<socket response: {ret_data}")

        ret = ret_data[4]
        ret_body = ret_data[9:-1].decode('utf-8')
        ret_json = json.loads(ret_body)
        log.info(f"ret json: {ret_json}")

        # 20230324 新增需求：1. 保存日志  2. 过滤错误信息

        # ----------------- 20230324 <USER>
        <GROUP> barcode is not None:
            save_socket_response(barcode, f'"{ret_json}"')
        # ----------------- 20230324 <USER>

        <GROUP>.info(f"ret: {ret}")
        if ret == 0x00 and ret_json.get("Result") != "NG":
            # success
            log.info(f"request socket success!")
            # ret_body = ret_data[9:-1].decode('utf-8')
            # log.info(f"--->ret_body: {ret_body}")
        elif ret == 0x01:
            return x_response("false", "checksum error"), need_upload
        elif ret == 0x02:
            return x_response("false", "unknown content"), need_upload
        else:
            if ret_json.get("Result") == "NG":
                msg = ret_json.get("Message")

                if filter_list:
                    for filter_msg in filter_list:
                        if filter_msg in msg:
                            log.warning(f"error info had filtered,【{msg}】")
                            return None, need_upload

                return x_response("false", f"socket api error: {msg}"), need_upload

    except Exception as err:
        # 多是网络异常
        log.info(f"send socket data error： {err}")
        log.warning(f"send socket data error traceback: {traceback.format_exc()}")
        need_upload = True

        return x_response("false", f"send socket data network error: {err}"), need_upload
    finally:
        log.info(f"socket closed！")
        client.close()

    return None, need_upload


def upload_cache_data(save_path, socket_host, socket_port, api_timeout=10):
    """
    上传缓存数据
    @param save_path 缓存文件路径
    @param socket_host socket主机
    @param socket_port socket端口
    @param api_timeout 超时时间
    """
    res = None

    if not os.path.exists(save_path):
        log.warning(f"缓存目录不存在！！！")
        return res

    for dir_path, dir_names, filenames in os.walk(save_path):
        log.info(f"upload file by path: {dir_path}")

        if dir_path == f"{save_path}/Old":
            log.info(f"Old dir, continue...")
            continue

        log.info(f"将要上传的总数量为：{len(filenames)}。 为保证性能，每次最多上传10个离线文件...")

        ok_count = 0
        for filename in filenames[:10]:
            log.info("---------------")
            # if filename == "Old":
            #     log.info(f"Old文件夹跳过")
            #     continue
            file_path = f"{save_path}/{filename}"

            try:

                time_now = int(time.time())

                cache_time = filename.rsplit("_", 1)[1].split(".")[0]
                log.info(f"cache time: {cache_time}")
                time_delta = time_now - int(cache_time)
                log.info(f"time delta: {time_delta}")

                if time_delta < 24 * 60 * 60:
                    # 24小时内
                    log.info(f"--> upload cache data: {file_path}")
                    payload = xutil.FileUtil.read_file(file_path)
                    res, need_upload1 = send_mes_to_socket_server(socket_host,
                                                                  socket_port,
                                                                  ['00', '02', '01', '00', '00'],
                                                                  payload,
                                                                  timeout=api_timeout
                                                                  )
                    if not need_upload1:
                        os.remove(file_path)

                        ok_count += 1
                        log.info(f"cache file upload success, file is removed!")
                    else:
                        log.info(f"upload offline data fail!")
                else:
                    # 超过24小时, 移动到Old文件夹
                    old_cache_path = f"{save_path}/Old"

                    if not os.path.exists(old_cache_path):
                        os.makedirs(old_cache_path)

                    src_path = f"{save_path}/{filename}"
                    dst_path = f"{old_cache_path}/{filename}"

                    shutil.move(src_path, dst_path)
                    log.info(f"{src_path} move to {dst_path} ...")
            except Exception as err:
                log.info(f"离线数据上传失败，error：{err}  path:{file_path}")

        log.info(f"本次成功上传离线数据数量：{ok_count}")

    return res


class Engine(ErrorMapEngine):
    version = {
        "title": "yuenanzhengwen release v1.0.0.2",
        "device": "501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-20 17:34  条码校验，上传数据
date: 2024-03-21 09:33  增加超时配置项
""", }

    form = {
        "factory": {
            "ui_name": "Factory",
            "value": "",
        },
        "op_id": {
            "ui_name": "OP_ID",
            "value": "",
        },
        "line_number": {
            "ui_name": "LineNumber",
            "value": "",
        },
        "machine_id": {
            "ui_name": "MachineID",
            "value": "",
        },
        "small_station": {
            "ui_name": "SmallStation",
            "value": "",
        },
        "station": {
            "ui_name": "Station",
            "value": "",
        },
        "server_ip": {
            "ui_name": "ServerIP",
            "value": "",
        },
        "server_port": {
            "ui_name": "ServerPort",
            "value": "",
        },
        "filter_error_info": {
            "ui_name": "Filter Error Info",
            "value": "",
        },
    }

    path = {
        "save_path": {
            "ui_name": "SavePath",
            "value": "",
        },
    }

    button = {
        "upload_offline_data": {
            "ui_name": "Upload Data"
        }
    }

    other_combo = {
        "api_timeout": {
            "ui_name": "api timeout",
            "item": ["1", "2", "5", "10", "20", "30", "50", "100"],
            "value": "10",
        }
    }

    def __init__(self):
        self.app_setting["custom_interval_cron"] = True
        self.app_setting["custom_interval_time"] = 60 * 20

        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["1"]["custom_code"] = "P013"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["2"]["custom_code"] = "P003"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["3"]["custom_code"] = "P007"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["4"]["custom_code"] = "P010"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["5"]["custom_code"] = "P009"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["6"]["custom_code"] = "P016"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["7"]["custom_code"] = "P015"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["8"]["custom_code"] = "P004"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["11"]["custom_code"] = "P029"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["15"]["custom_code"] = "P006"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["17"]["custom_code"] = "P008"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["21"]["custom_code"] = "P012"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["22"]["custom_code"] = "P041"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["23"]["custom_code"] = "P031"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["25"]["custom_code"] = "P017"
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"]["32"]["custom_code"] = "P028"

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        factory = other_vo.get_value_by_cons_key('factory')
        op_id = other_vo.get_value_by_cons_key('op_id')
        line_number = other_vo.get_value_by_cons_key('line_number')
        machine_id = other_vo.get_value_by_cons_key('machine_id')
        small_station = other_vo.get_value_by_cons_key('small_station')
        station = other_vo.get_value_by_cons_key('station')
        server_ip = other_vo.get_value_by_cons_key('server_ip')
        server_port = other_vo.get_value_by_cons_key('server_port')
        api_timeout = int(other_vo.get_value_by_cons_key('api_timeout'))

        for sn in other_vo.list_sn():
            log.info(f"barcode: {sn} checking...")
            param = {
                "time": xutil.DateUtil.get_datetime_now("%Y%m%d%H%M%S"),
                "Barcode": sn,
                "FullImagePath": [],
                "InspecTime": "",
                "ProjectName": "",
                "RobotResult": "",
                "HumanResult": "",
                "JsonPath": "",
                "PicturePath": "",
                "Factory": factory,
                "OP_ID": op_id,
                "LineNumber": line_number,
                "MachineID": machine_id,
                "Station": station,
                "SmallStation": small_station,
            }

            payload = json.dumps(param, ensure_ascii=False)

            res, _ = send_mes_to_socket_server(server_ip,
                                               server_port,
                                               ['00', '01', '01', '00', '00'],
                                               payload,
                                               timeout=api_timeout)
            if res:
                return res

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key('save_path', not_null=True)
        factory = data_vo.get_value_by_cons_key('factory')
        op_id = data_vo.get_value_by_cons_key('op_id')
        line_number = data_vo.get_value_by_cons_key('line_number')
        machine_id = data_vo.get_value_by_cons_key('machine_id')
        small_station = data_vo.get_value_by_cons_key('small_station')
        station = data_vo.get_value_by_cons_key('station')
        server_ip = data_vo.get_value_by_cons_key('server_ip')
        server_port = data_vo.get_value_by_cons_key('server_port')
        filter_error_info = data_vo.get_value_by_cons_key('filter_error_info')
        api_timeout = int(data_vo.get_value_by_cons_key('api_timeout'))

        filter_list = filter_error_info.split(";")
        filter_list = [item for item in filter_list if item]

        log.info(f"filter info: {filter_list}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        mes_path = other_data.get("review_path")
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        error_res = None
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if pcb_entity.board_side == "T":
                full_image_path = [
                    f"{mes_path}/thumbnail/0/thumb.jpg",
                    "",
                ]
            else:
                full_image_path = [
                    "",
                    f"{mes_path}/thumbnail/1/thumb.jpg",
                ]

            file_name = f"{test_time}_{barcode}.txt"
            json_path = f"{save_path}/{file_name}"

            basic = {
                "time": test_time,
                "Barcode": barcode,
                "FullImagePath": full_image_path,
                "InspecTime": str(pcb_entity.origin_cycle_time),
                "ProjectName": pcb_entity.project_name,
                "RobotResult": board_entity.get_robot_result("OK", "NG"),
                "HumanResult": board_entity.get_repair_result("OK", "NG"),
                "JsonPath": json_path,
                "PicturePath": f"{mes_path}/images",
                "Factory": factory,
                "OP_ID": op_id,
                "LineNumber": line_number,
                "MachineID": machine_id,
                "SmallStation": small_station,
                "Station": station,
            }

            aoi_results = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    aoi_results.append({
                        "CompDesignator": comp_entity.designator,
                        "CompImagePath": comp_entity.image_path,
                        "CompNgTypeStr": comp_entity.repair_ng_str,
                        "CompNgType": comp_entity.repair_ng_code,
                        "CompRobotNgTypeStr": comp_entity.robot_ng_str,
                        "CompRobotNgType": comp_entity.robot_ng_code,
                        "CompPackage": comp_entity.package,
                        "HumanResult": comp_entity.get_final_result("OK", "OK", "NG"),
                    })

            payload = json.dumps({
                "basic": basic,
                "aoiResults": aoi_results
            }, ensure_ascii=False)

            res, need_upload = send_mes_to_socket_server(server_ip,
                                                         server_port,
                                                         ['00', '02', '01', '00', '00'],
                                                         payload,
                                                         barcode,
                                                         filter_list,
                                                         timeout=api_timeout
                                                         )
            if res:
                # 表示有异常需要处理
                error_res = res

            if need_upload:
                log.warning(f"cache param to file...")
                time_now = int(time.time())
                full_path = f"{save_path}/{test_time}_{barcode}_{time_now}.txt"
                xutil.FileUtil.write_content_to_file(full_path, payload)
                log.info(f"上传失败，文件已缓存在本地！")

        if error_res:
            return error_res

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        log.info(f"定时检测离线数据......")
        save_path = other_vo.get_value_by_cons_key('save_path', not_null=True)
        server_ip = other_vo.get_value_by_cons_key('server_ip')
        server_port = other_vo.get_value_by_cons_key('server_port')
        api_timeout = other_vo.get_value_by_cons_key('api_timeout')

        upload_cache_data(save_path, server_ip, server_port, api_timeout)

        return x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        save_path = btn_vo.get_value_by_cons_key('save_path', not_null=True)
        server_ip = btn_vo.get_value_by_cons_key('server_ip')
        server_port = btn_vo.get_value_by_cons_key('server_port')
        api_timeout = btn_vo.get_value_by_cons_key('api_timeout')

        if btn_vo.get_btn_key() == 'upload_offline_data':
            x_res = upload_cache_data(save_path, server_ip, server_port, api_timeout)
            if x_res:
                return x_res

        return self.x_response()
