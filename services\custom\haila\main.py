# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/22 下午4:05
# Author     ：sch
# version    ：python 3.8
# Description：海纳川海拉
"""
import socket
from datetime import datetime
from typing import Any

from common import xutil
from common.xutil import log, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


def xx_response(status: str = "true", msg: str = "ok"):
    if status == "true":
        result = True
    else:
        result = False

    time_now = datetime.now().strftime("%Y.%m.%d %H:%M:%S")
    return {'key': time_now, 'result': result, 'string': msg}


def haila_request(socket_ip: str, socket_port: int, param: str, timeout=5) -> Any:
    log.info(f"请求地址: {socket_ip}:{socket_port} 请求参数:{param}")

    sk = socket.socket()
    sk.settimeout(timeout)

    sk.connect((socket_ip, socket_port))
    param_bytes = param.encode('utf-8')

    # full_param = f"\x02{param_bytes}\r\n"
    full_param = b'\x02' + param_bytes + b'\r\n'
    sk.send(full_param)
    ret_str = sk.recv(1024)
    ret_str = ret_str.decode('utf-8')

    ret_str = ret_str.replace("\x02", "").replace("\r\n", "")
    log.info(f"接口响应: {ret_str}")
    sk.close()

    ret_list = ret_str.split(";")
    if len(ret_list) < 2:
        return xx_response("false", f"mes接口响应异常，error：{ret_list}")

    if ret_list[1] != "0":
        return xx_response("false", f"mes接口响应异常，error：{ret_list}")

    return None


AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "4524313", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "4524202", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "4524327", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "4524310", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "4524301", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "4524337", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "4524338", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "4524325", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "4524311", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "4524339", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "4524323", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "4524335", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "4524334", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "4524302", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "4524218", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "4524330", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "4524340", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "4524206", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "4524336", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "4524341", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "4524342", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "4524343", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "4524344", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "4524331", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "4524345", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "4524346", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "4524347", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "4524348", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "4524349", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "4524350", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}


class Engine(BaseEngine):
    version = {
        "title": "haila release v1.0.0.14",
        "device": "303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-22 16:06  init 
date: 2023-09-26 12:53  修改接口参数
date: 2023-10-19 15:56  改成按拼板上传，并且需要带上器件条码
date: 2023-10-23 14:19  修改不良代码
date: 2023-10-23 18:34  bugfix
date: 2023-10-24 10:15  优化解析器件条码
date: 2023-10-26 14:53  修复数据异常
""", }

    form = {
        "api_host1": {
            "ui_name": "条码校验接口",
            "value": "127.0.0.1:3333"
        },
        "api_host2": {
            "ui_name": "上传数据接口",
            "value": "127.0.0.1:3333"
        },
        "api_host3": {
            "ui_name": "上传设备状态接口",
            "value": "127.0.0.1:3890"
        }
    }

    other_form = {
        "project_name_bak": {
            "ui_name": "候补板式名",
            "value": ""
        }
    }

    other_combo = {
        "api_timeout": {
            "ui_name": "请求接口超时",
            "value": "5",
            "item": ["1", "2", "5", "10", "15", "30", "60"]
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host1 = other_vo.get_value_by_cons_key("api_host1")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout")
        project_name_bak = other_vo.get_value_by_cons_key("project_name_bak")

        api_timeout = int(api_timeout)

        try:
            api_host, api_port = api_host1.split(":")
            api_port = int(api_port)
        except Exception as err:
            return self.x_response("false", f"条码校验接口格式异常, 请参考格式[127.0.0.1:3333] error:{err}")

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            project_name = other_vo.get_project_name()

            if not project_name:
                project_name = project_name_bak

            param_1 = f"productStart;{project_name};{sn};;"

            ret = haila_request(api_host, api_port, param_1, timeout=api_timeout)
            if ret:
                return ret
        # if str(ret) != "0":
        #     return self.x_response("false", f"mes接口异常, 条码校验失败, error: {ret}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host2 = data_vo.get_value_by_cons_key("api_host2")
        api_timeout = data_vo.get_value_by_cons_key("api_timeout")

        api_timeout = int(api_timeout)

        try:
            api_host, api_port = api_host2.split(":")
            api_port = int(api_port)
        except Exception as err:
            return self.x_response("false", f"上传数据接口格式异常, 请参考格式[127.0.0.1:3333] error:{err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name
        cycle_time = pcb_entity.get_cycle_time()

        report_path = pcb_entity.get_pcb_t_report_xml()
        comp_barcode_map = xutil.XmlUtil.dict_comp_barcode(report_path)
        self.log.info(f"解析到的器件条码：{comp_barcode_map}")

        error_ret = self.x_response()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_barcode = comp_barcode_map.get(board_no, [])              # comp barcode: ["comp_sn1"]

            comp_sn_str = ""
            for item in comp_barcode:
                log.info(f"item: {item}")
                comp_sn_str = item.get("comp_barcode", "")

                self.log.info(f"一个拼板只取其中一个器件条码来传递！")
                break

            sn_list = [barcode, comp_sn_str]

            # ng_count += board_entity.comp_repair_ng_number

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    # comp_ng_str = comp_entity.repair_ng_str
                    repair_ng_code = comp_entity.repair_ng_code

                    custom_ng_str = AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("custom_str", "unknown")
                    custom_ng_code = AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("custom_code", repair_ng_code)

                    comp_data_list.append(f"{comp_tag};{custom_ng_code};{custom_ng_str}")

            # send data to mes

            if comp_data_list:
                ng_str = f";{';'.join(comp_data_list)}"
            else:
                ng_str = ""

            ng_count = len(comp_data_list)

            result = board_entity.get_repair_result("OK", "Fail")

            log.info(f"2222222222")
            param2 = f"uploadData;{project_name};{';'.join(sn_list)};{result};{cycle_time};{ng_count}{ng_str};"
            ret = haila_request(api_host, api_port, param2, timeout=api_timeout)
            if ret:
                error_ret = error_ret

        if error_ret:
            return error_ret

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host3 = other_vo.get_value_by_cons_key("api_host3")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout")

        api_timeout = int(api_timeout)

        try:
            api_host, api_port = api_host3.split(":")
            api_port = int(api_port)
        except Exception as err:
            return self.x_response("false", f"上传设备状态接口格式异常, 请参考格式[127.0.0.1:3890] error:{err}")

        device_status_str = other_vo.get_device_status_str()
        status_code = other_vo.get_status_code()

        if status_code == "02" or device_status_str == "开始检测":
            # 开始检测, 绿灯
            foo = "PR;green"

        elif status_code == "03" or device_status_str == "停止检查":
            # 停止检查, 黄灯
            foo = "SB;yellow"

        elif status_code in ["01", "04"] or device_status_str in ["进板", "出板"]:
            self.log.warning(f"无需上传的状态: {device_status_str}")
            return self.x_response()
        else:
            # 红灯
            foo = "UDT;red"

        param3 = f"productStart;{foo};;"
        res = haila_request(api_host, api_port, param3, timeout=api_timeout)
        if res:
            return res
        # if str(ret) != "0":
        #     return self.x_response("false", f"mes接口异常, 上传设备状态失败, error: {ret}")

        return self.x_response()
