# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/11 下午4:29
# Author     ：sch
# version    ：python 3.8
# Description：青岛恒新基
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "qingdaohengxinji release v1.0.0.2",
        "device": "AIS430",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-11 16:29  init
date: 2023-07-11 17:15  上传测试数据
date: 2023-07-12 12:06  不解析接口返回的数据
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "workstation": {
            "ui_name": "工作中心",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        api_url = data_vo.get_value_by_cons_key("api_url")
        workstation = data_vo.get_value_by_cons_key("workstation")

        pcb_standard_json = pcb_entity.to_standard_json()

        req_param = {
            "workstation": workstation
        }
        req_param.update(pcb_standard_json)

        ret = xrequest.RequestUtil.post_json(api_url, req_param, to_json=False)

        return self.x_response()
