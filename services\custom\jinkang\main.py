# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/31 下午3:06
# Author     ：sch
# version    ：python 3.8
# Description：金康
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "jinkang release v1.0.0.1",
        "device": "AIS501-0",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-31 15:07  init
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://*************:9210/tracking/saveProductOutBound"
        },
        "company": {
            "ui_name": "公司ID",
            "value": "909d1edafa9c40598e8474beccb8e471"
        },
        "company_name": {
            "ui_name": "公司名称",
            "value": "（测试）组织机构",
        },
        "user_id": {
            "ui_name": "用户ID",
            "value": "1",
        },
        "nickname": {
            "ui_name": "用户名称",
            "value": "超级管理员",
        },
        "workstation": {
            "ui_name": "工作中心",
            "value": "SMT1-2-2",
        },
        "mo_number": {
            "ui_name": "制令单号",
            "value": "0000000405-SMT-001",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        api_url = data_vo.get_value_by_cons_key("api_url")
        company = data_vo.get_value_by_cons_key("company")
        company_name = data_vo.get_value_by_cons_key("company_name")
        user_id = data_vo.get_value_by_cons_key("user_id")
        nickname = data_vo.get_value_by_cons_key("nickname")
        workstation = data_vo.get_value_by_cons_key("workstation")
        mo_number = data_vo.get_value_by_cons_key("mo_number")

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            ng_code_list = []
            ng_tag_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    ng_code_list.append(comp_entity.repair_ng_code)
                    ng_tag_list.append(comp_entity.designator)

            # 1.
            req_param = {
                "company": company,
                "companyName": company_name,
                "userId": user_id,
                "nickName": nickname,
                "produceSn": barcode,
                "workStationSn": workstation,
                "moNumber": mo_number,
                "errorCode": ",".join(ng_code_list),
                "errorPoint": ",".join(ng_tag_list)
            }

            ret = xrequest.RequestUtil.post_json(api_url, req_param)
            if str(ret.get('code')) != '200':
                error_msg = ret.get('msg')

        if error_msg:
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{error_msg}")

        return self.x_response()
