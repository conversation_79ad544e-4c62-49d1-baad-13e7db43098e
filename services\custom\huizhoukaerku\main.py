# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/1/3 下午4:05
# Author     ：sch
# version    ：python 3.8
# Description：惠州卡儿酷
"""

import hashlib
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import log
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


def create_signature(params, app_secret=""):
    # Step 1 & 2: Sort the parameters by key, ignoring 'signature' and 'image'
    sorted_params = sorted((k, v) for k, v in params.items() if k not in ['signature', 'image'])

    # Step 3: Concatenate keys and values into a single string
    param_string = ''.join(f"{k}{v}" for k, v in sorted_params)

    # Step 4: Prepend the app_secret to the string
    string_to_sign = f"{app_secret}{param_string}"

    log.info(f"origin str: {string_to_sign}")
    # Step 5: Compute the MD5 hash and convert it to hex
    md5_hash = hashlib.md5(string_to_sign.encode()).hexdigest()
    log.info(f"signature: {md5_hash}")

    return md5_hash


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoukaerku release v1.0.0.3",
        "device": "AIS501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-01-06 09:58  条码校验，上传数据
date: 2025-01-08 09:43  兼容接口返回
date: 2025-02-27 09:14  使用barcodeList的参数来校验条码  （ps：空条码也需要校验）
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(工序入站)",
            "value": "http://127.0.0.1:8081/api/v1/move_into_station",
        },
        "api_url_send": {
            "ui_name": "接口URL(工序出站)",
            "value": "http://127.0.0.1:8081/api/v1/move_out_station",
        },
        "app_key": {
            "ui_name": "AppKey",
            "value": "931284462aef37d5a2cc7e969",
        },
        "app_secret": {
            "ui_name": "AppSecret",
            "value": "d9c59c9478fa4b0e966171923180545d",
        },
    }

    form = {
        "process_code": {
            "ui_name": "工序编码",
            "value": "AIS501",
        },
        "resource_code": {
            "ui_name": "岗位资源编码",
            "value": "",
        },
        "login_user": {
            "ui_name": "登录账号",
            "value": "",
        },
        "order_id": {
            "ui_name": "生产工单号",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        app_key = other_vo.get_value_by_cons_key("app_key")
        process_code = other_vo.get_value_by_cons_key("process_code")
        resource_code = other_vo.get_value_by_cons_key("resource_code")
        login_user = other_vo.get_value_by_cons_key("login_user")
        order_id = other_vo.get_value_by_cons_key("order_id")
        device_code = other_vo.get_value_by_cons_key("device_code")
        app_secret = other_vo.get_value_by_cons_key("app_secret")

        barcode_map = other_vo.get_barcode_map()

        if "-2" in barcode_map:
            del barcode_map["-2"]

        if "-1" in barcode_map:
            del barcode_map["-1"]

        sn_list = list(barcode_map.values())

        ret_res = self.x_response()
        for sn in sn_list:
            check_param = {
                "app_key": app_key,
                "method": "wzdigit.in.move",
                "timestamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                "rcard_no": sn,
                "procedure_code": process_code,
                "location_code": resource_code,
                "product_state": "GOOD",
                "operator_code": login_user,
                "order_no": order_id,
                "device_no": device_code,
            }

            md5_sign = create_signature(check_param, app_secret)
            check_param["sign"] = md5_sign

            ret = xrequest.RequestUtil.post_form(api_url_check, check_param)
            if str(ret.get("code")) != "200":
                ret_res = self.x_response("false", f"mes接口异常，{ret.get('message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_send = data_vo.get_value_by_cons_key("api_url_send")
        app_key = data_vo.get_value_by_cons_key("app_key")
        process_code = data_vo.get_value_by_cons_key("process_code")
        resource_code = data_vo.get_value_by_cons_key("resource_code")
        login_user = data_vo.get_value_by_cons_key("login_user")
        order_id = data_vo.get_value_by_cons_key("order_id")
        device_code = data_vo.get_value_by_cons_key("device_code")
        app_secret = data_vo.get_value_by_cons_key("app_secret")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            # board_no = board_entity.board_no

            comp_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    repair_ng_str = comp_entity.repair_ng_str
                    comp_ng_list.append(f"{comp_tag}_{repair_ng_str}")

            data_param = {
                "app_key": app_key,
                "method": "wzdigit.out.move",
                "timestamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),

                "rcard_no": barcode,
                "procedure_code": process_code,
                "location_code": resource_code,
                "product_state": board_entity.get_repair_result("GOOD", "DEFECT"),
                "operator_code": login_user,
                "order_no": order_id,
                "device_no": device_code,
                "jig_no": pcb_entity.fixture_barcode if pcb_entity.fixture_barcode else "",
                "defect_item": ";".join(comp_ng_list),
            }

            md5_sign = create_signature(data_param, app_secret)
            data_param["sign"] = md5_sign

            ret = xrequest.RequestUtil.post_form(api_url_send, data_param)
            if str(ret.get("code")) != "200":
                ret_res = self.x_response("false", f"mes接口异常，{ret.get('message')}")

        return ret_res


if __name__ == '__main__':
    # 示例用法：
    params = {
        "param1": "value1",
        "param2": "value2",
        "param3": "value3"
    }
    secret_key = "your_secret_key_here"

    signature = create_signature(params, secret_key)
