# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2023/12/22 上午10:32
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import io

import requests

# filename = "/home/<USER>/test1/20231222102610__2.txt"


# with open(filename, 'rb') as f:
#
#     ret = requests.post("http://127.0.0.1:8081/test_file", files={"file": f})
#     print(ret.text)


# f = io.BytesIO("hello world".encode('utf8'))
# f = io.StringIO("hello world")
# ret = requests.post("http://127.0.0.1:8081/test_file", files={"file": f})
from common import xutil

if __name__ == '__main__':
    data_path = f"/home/<USER>/aoi/run/results/333.999/20240223/B_20240223150839843_1_NG"
    root = xutil.XmlUtil.get_xml_root_by_file(f"{data_path}/report.xml")
    r = root.find('uuid')

    uuid = root.find('uuid').text
    pcb = root.find('boards')[0].find('PCB').text
    bom = root.find('boards')[0].find('BOM').text

    print(f"{uuid=}")
    print(f"{pcb=}")
    print(f"{bom=}")
