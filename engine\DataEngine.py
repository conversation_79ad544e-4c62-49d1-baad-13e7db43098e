# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : DataEngine.py
# Time       ：2024/5/16 下午5:26
# Author     ：sch
# version    ：python 3.8
# Description：一个类似Redis的简易内存和磁盘混合存储系统


-----AI提示词------>
你需要使用Python来设置一个类似Redis的简易内存和磁盘混合存储系统

1. 需要有数据库的概念
2. 需要支持线程安全
3. 主要函数
    get(db:str, key: str) -> Any
    set_or_update(db:str, key: str, value: Any)
    delete(db:str, key: str)
4. 其他要求：
    4.1. 本地存储使用json模块来存储
-----AI提示词------>
"""
import json
import os
from threading import Lock
from typing import Any

from common import xconfig
from common.xutil import log


class HybridStorage:
    """
    一个类似Redis的简易内存和磁盘混合存储系统

    ps: 如果要新增一个db，需要提前代码写死，否则会缺少数据预热的功能
    """

    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self.memory_storage = {}
        self.lock = Lock()

        # 用于数据预热
        self.db_preload_flag = []

    def _preload_data(self, db: str):
        """
        数据预热，将磁盘上的数据加载到内存中

        我们可以在初始化数据库对象时加载所有数据到内存中。这样，当需要获取数据时，可以直接从内存中获取，而不必每次都读取磁盘文件。

        warning!!!! get,set_or_update,delete 时都要去做数据预热，否则有可能会出现一些奇怪的bug/problem
        :param db:
        :return:
        """
        if db not in self.db_preload_flag:
            file_path = self._get_file_path(db)

            if os.path.exists(file_path):
                log.info(f"----db:{db} 数据预热-----")

                with open(file_path, 'r') as file:
                    data = json.load(file)
                    self.memory_storage[db] = data

                    self.db_preload_flag.append(db)
            # else:
            #     self.memory_storage[db] = {}

    def _get_file_path(self, db: str) -> str:
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

        return os.path.join(self.data_dir, f"{db}.json")

    def _set_or_update_to_memory(self, db: str, key: str, value: Any):
        """
        存储到内存中
        :param db:
        :param key:
        :param value:
        :return:
        """
        if db not in self.memory_storage:
            self.memory_storage[db] = {}

        self.memory_storage[db][key] = value

    def _set_memory_data_to_local_storage(self, db: str):
        """
        持久化数据, 将硬盘中的数据存储到硬盘上
        :param db:
        :return:
        """
        file_path = self._get_file_path(db)
        with open(file_path, 'w') as file:
            json.dump(self.memory_storage[db], file, indent=4, ensure_ascii=False)

    def get(self, db: str, key: str, default_value: Any = None) -> Any:
        """
        获取数据
        """
        with self.lock:
            self._preload_data(db)
            return self.memory_storage.get(db, {}).get(key, default_value)

    def set_or_update(self, db: str, key: str, value: Any):
        """
        新设置/更新，先更新内存再更新到硬盘里
        """
        with self.lock:
            self._preload_data(db)
            self._set_or_update_to_memory(db, key, value)
            self._set_memory_data_to_local_storage(db)

    def delete(self, db: str, key: str):
        """
        删除数据
        """
        with self.lock:
            self._preload_data(db)

            if db in self.memory_storage and key in self.memory_storage[db]:
                del self.memory_storage[db][key]
                self._set_memory_data_to_local_storage(db)

    def exist_db(self, db: str) -> bool:
        """
        判断一个数据库是否存在
        :param db:
        :return:
        """
        with self.lock:
            self._preload_data(db)
            return db in self.memory_storage

    def get_db_all_data(self, db: str) -> dict:
        """
        返回整个数据库的数据
        :param db:
        :return:
        """
        with self.lock:
            self._preload_data(db)
            return self.memory_storage.get(db, {})

    def set_db_all_data(self, db: str, all_data: dict):
        """
        设置整个数据库的数据
        :param db:
        :param all_data: 全部数据
        :return:
        """
        with self.lock:
            self._preload_data(db)
            self.memory_storage[db] = all_data
            self._set_memory_data_to_local_storage(db)

    def get_mesconfig(self, key: str, default_value: Any = None) -> Any:
        return self.get('mesconfig', key, default_value)

    def set_mesconfig(self, key: str, value: Any):
        self.set_or_update('mesconfig', key, value)

    def get_error_code_map(self, key: str) -> dict:
        return self.get("mes_error_code_map", key, None)

    def set_error_code_map(self, key: str, value: Any):
        self.set_or_update("mes_error_code_map", key, value)

    def set_all_error_code_map(self, all_data: dict):
        """
        设置所有的数据到 mes_error_code_map.json
        :param all_data:
        :return:
        """
        self.set_db_all_data("mes_error_code_map", all_data)

    def get_all_error_code_map(self) -> dict:
        """
        获取所有的数据 mes_error_code_map.json
        :return:
        """
        return self.get_db_all_data("mes_error_code_map")

    def exist_error_code_map(self):
        return self.exist_db("mes_error_code_map")

    def get_cache_data(self, key: str, default_value: Any = None) -> Any:
        return self.get('mes_cache_data', key, default_value)

    def set_cache_data(self, key: str, value: Any):
        self.set_or_update('mes_cache_data', key, value)


data_engine = HybridStorage(xconfig.leichen_dir)

if __name__ == '__main__':
    pass
    # cache_engine.set_or_update("mes_error_code_map", "AIS203/AIS303/AIS40X/AIS50x", "hello")
    # log.info(cache_engine.get("mes_error_code_map", "AIS203/AIS303/AIS40X/AIS50x"))
    #
    # log.info(f"第二次读取")
    # log.info(cache_engine.get("mes_error_code_map", "AIS203/AIS303/AIS40X/AIS50x1"))

    # 2.
    # cache_engine.set_or_update("mes_error_code_map", "new test", "hello world1")
    # log.info(cache_engine.get("mes_error_code_map", "new test"))
    # cache_engine.set_or_update("mes_error_code_map", "new test", "hello world2")
    # log.info(cache_engine.get("mes_error_code_map", "new test"))

    # cache_engine.delete("mes_error_code_map", "new test")

    # 3.
    # print(data_engine.get_mesconfig('form'))

    # 4.
    d1 = "AIS203/AIS303/AIS40X/AIS50x"
    ret = data_engine.get_error_code_map(d1)
    ret["1"]["custom_code"] = "AOI01"
    data_engine.set_error_code_map(d1, ret)
    print(ret)
    ret = data_engine.get_error_code_map("AIS203/AIS303/AIS40X/AIS50x")
    print(ret)
    ret = data_engine.get_error_code_map("AIS203/AIS303/AIS40X/AIS50x")
    print(ret)

