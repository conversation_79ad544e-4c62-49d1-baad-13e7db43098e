# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/12/5 上午10:13
# Author     ：sch
# version    ：python 3.8
# Description：
"""

bad_param = {
    "barcodeList": {
        "-1": "D3VZ15KBT4021000PO2050",
        "-2": "",
        "0": "D3VZ15KBT4021000PO2050"
    },
    "funcArgs": [["D3VZ15KBT4021000PO2050"]],
    "funcName": "GetBadBoard",
    "orderID": "",
    "projectName": "/home/<USER>/aoi/program/projects/newui.001",
    "trackIndex": 0
}

barcode_list = bad_param.get('barcodeList')
del barcode_list['-1']
del barcode_list['-2']

ret_bad = list(barcode_list.keys())

print(','.join(ret_bad))
