# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/6 上午10:23
# Author     ：sch
# version    ：python 3.8
# Description：广西长城电源
"""
import os
import time
from typing import Any, <PERSON><PERSON>

from PIL import Image

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

global_data = {}

"""
流程：1269543
"""


def x_post_webservice(api_url, vip: str, p_cmd: str, p_data: str) -> str:
    """
    调用客户webservice接口
    :return:
    """
    ret_str = xrequest.RequestUtil.post_form(api_url, {
        "vip": vip,
        "p_cmd": p_cmd,
        "p_data": p_data
    }, to_json=False)
    ret = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    return ret.text


def get_pcb_src_image(data_path) -> <PERSON><PERSON>[str, bool]:
    """
    获取整板大图，优先获取高清原图
    return 大图的原地址，是否是高清图
    """
    thumbnail_path = f"{data_path}/thumbnail"

    ret_path = f"{data_path}/thumbnail/0/thumb.jpg"
    is_origin_img = False  # 是否是高清原图

    for i in os.listdir(thumbnail_path):
        if i.endswith(".jpg"):
            ret_path = f"{thumbnail_path}/{i}"
            is_origin_img = True
            break

    return ret_path, is_origin_img


class Engine(ErrorMapEngine):
    version = {
        "title": "changchengdianyuan release v1.0.0.22",
        "device": "AIS203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-08 16:18  
date: 2024-01-19 09:40  增加是否校验程序名的配置项
date: 2024-01-22 16:47  根据规则保存拼板图到指定文件夹
date: 2024-01-25 14:41  调取获取图片上传路径接口
date: 2024-03-12 18:47  使用接口5返回的地址来保存图片
date: 2024-03-15 18:27  上传全部检测项和不良代码
date: 2024-03-16 11:33  传全部器件过去
date: 2024-03-25 10:15  修改接口TestValue的格式
date: 2024-05-08 18:15  需求变更
date: 2024-05-09 16:54  需求变更 1
date: 2024-06-03 16:56  命令3，相同的不良，只需要传一个就可以
date: 2024-06-11 16:17  上传逻辑变更
date: 2024-06-13 15:03  接口3，NG的时候，只传一个不良代码
date: 2024-08-12 10:35  接口4，p_data参数单次上传的长度不允许超过4000，超过的话，分多次上传
date: 2024-11-04 17:05  接口3，传的NG代码改为传复判NG的第一个不良代码
""",
    }

    combo = {
        "use_mes": {
            "ui_name": "启用Mes总开关",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "check_project_name": {
            "ui_name": "校验程序名",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "image_rate": {
            "ui_name": "整板图缩放比",
            "item": ["1", "2", "3", "4", "8", "16"],
            "value": "1",
        },
    }

    form = {
        "webservice_url": {
            "ui_name": "webservice接口url",
            "value": "http://10.65.1.4/Services/MachineServices.asmx/MES_TransData",
        },
        "username": {
            "ui_name": "员工编号",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单",
            "value": "",
        },
        "station_name": {
            "ui_name": "站点名称",
            "value": "",
        },
    }

    button = {
        # "connect_window": {
        #     "ui_name": "TestConnect"
        # },
        "check_user": {
            "ui_name": "检查员工号",
        },
    }

    path = {
        "board_save_path": {
            "ui_name": "拼板图保存路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        username = data_vo.get_value_by_cons_key("username")
        board_save_path = data_vo.get_value_by_cons_key("board_save_path")
        order_id = data_vo.get_value_by_cons_key("order_id")
        use_mes = data_vo.get_value_by_cons_key("use_mes")
        webservice_url = data_vo.get_value_by_cons_key("webservice_url")
        station_name = data_vo.get_value_by_cons_key("station_name")
        image_rate = data_vo.get_value_by_cons_key("image_rate", to_int=True)

        if use_mes == "No":
            self.log.warning(f"未启用MES！")
            return self.x_response()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # test_result3 = []

        if not global_data.get('is_login'):
            return self.x_response("false", f"未登录，请先检查员工号！")

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        time_file1 = time_file[:6]

        pcb_final_result = pcb_entity.get_final_result()

        pcb_review_path = pcb_entity.get_pcb_pcb_t_review_path()
        pcb_src_image, is_origin_img = get_pcb_src_image(pcb_review_path)

        self.log.info(f"pcb src image: {pcb_src_image}")
        self.log.info(f"flag: {is_origin_img}")

        if is_origin_img:
            image_scale = 1 / image_rate
        else:
            image_scale = 0.0

        board_position = pcb_entity.get_board_box_position(image_scale=image_scale)

        time.sleep(0.3)

        error_msg = ''
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            # board_result = board_entity.get_repair_result("0", "1")
            board_result1 = board_entity.get_repair_result("OK", "NG")

            board_final_result = board_entity.get_final_result()

            if barcode:
                # param5 = {
                #     "type": 5,
                #     "request_param": f"{barcode};"
                # }
                # ret1 = xrequest.SocketUtil.x_socket_send_data(window_ip, param5)
                self.log.info(f"5. 正在获取图片保存地址保存拼板图...")

                ret_str1 = x_post_webservice(webservice_url, station_name, "5", f"{barcode};")
                if ret_str1.startswith("OK"):
                    result1, data1 = ret_str1.split(",")
                    print(f"result: {result1}")
                    print(f"path: {data1}")

                    data_list = data1.split('\\')

                    if len(data_list) >= 3:
                        print(data_list)
                        f1 = data_list[-3]
                        f2 = data_list[-2]
                        f3 = data_list[-1]
                        print(f"{f1=}")
                        print(f"{f2=}")
                        print(f"{f3=}")
                        board_dst_path = f"{board_save_path}/{f1}/{f2}/{f3}"
                    else:
                        self.log.warning(f"未从接口获取到图片地址！")
                        board_dst_path = f"{board_save_path}/{time_file1}/{order_id}/{barcode}"

                else:
                    self.log.warning(f"未获取到图片地址！")
                    board_dst_path = f"{board_save_path}/{time_file1}/{order_id}/{barcode}"

                xutil.FileUtil.ensure_dir_exist(board_dst_path)

                if pcb_src_image:
                    box_info = board_position.get(board_no)

                    image = Image.open(pcb_src_image)
                    cropped_image = image.crop(box_info)
                    board_dst_image = f"{board_dst_path}/{barcode}_{time_file}_{board_no}_{board_result1}.jpg"
                    cropped_image.save(board_dst_image)
                    self.log.info(f"NO:{board_no}, 拼板图保存成功，保存路径：{board_dst_image}")
            else:
                self.log.warning(f"没有条码，不调用接口5，不保存拼板图！")

            # comp_ng_data3 = []
            comp_ng_data4 = []

            ng_data3 = ""

            # ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                # if comp_entity.is_repair_ng():
                if comp_entity.is_repair_ng():
                    if not ng_data3:
                        ng_data3 = f"{comp_entity.repair_ng_code};"

                    # jira添加了备注 - 2024/05/31 18:53, 命令3，相同的不良，只需要传一个就可以
                    # if comp_entity.robot_ng_code not in comp_ng_data3:
                    #     comp_ng_data3.append(comp_entity.robot_ng_code)

                # ix += 1
                comp_tag = comp_entity.designator
                comp_ng_data4.append(f"{comp_tag}:{comp_tag}:{comp_entity.get_final_result('0', '0', '1')}")

                # for alg_entity in comp_entity.yield_alg_entity():
                #     ix += 1
                #     # if alg_entity.result != '0':
                #     alg_name = alg_entity.test_name
                #     alg_val = alg_entity.test_val
                #     comp_ng_data4.append(f"{alg_name}:{alg_val}:{alg_entity.result}")

            # param_item = [
            #     barcode,
            #     board_result1,
            # ]

            # comp_ng_str = ','.join(comp_ng_data3)

            # if comp_ng_str:
            #     param_item.append(comp_ng_str)

            # if board_final_result == "PASS":
            #     # 直通的板卡
            #     self.log.warning("pass板卡不需要调用接口4！")
            #
            # else:
            # NG板+误报的
            # param4 = {
            #     "type": 4,
            #     "request_param": f"{username};{barcode};{','.join(comp_ng_data4)};"
            # }
            # ret = xrequest.SocketUtil.x_socket_send_data(window_ip, param4)

            for chunk_data in xutil.OtherUtil.split_list_by_len(comp_ng_data4, 3500):
                self.log.info(f"4.上传测试数据....")
                param_str = f"{username};{barcode};{','.join(chunk_data)};"
                self.log.info(f"p_data 长度：{len(param_str)}")

                ret_str = x_post_webservice(
                    webservice_url,
                    station_name,
                    "4",
                    param_str
                )
                if not ret_str.startswith('OK'):
                    error_msg = f"mes接口异常，上传测试数据失败，error：{ret_str}"

            param_str3 = f"{username};{barcode};{board_result1};{ng_data3}"

            if pcb_final_result == "NG":
                # 部分拼板NG
                if board_final_result == "NG":
                    # test_result3.append(':'.join(param_item))

                    self.log.info(f"3.上传过站结果...")
                    # ret = xrequest.SocketUtil.x_socket_send_data(window_ip, param3)

                    ret_str = x_post_webservice(
                        webservice_url,
                        station_name,
                        "3",
                        param_str3
                    )
                    if not ret_str.startswith('OK'):
                        error_msg = f"mes接口异常，上传测试结果失败，error：{ret_str}"
                else:
                    self.log.warning(f"部分拼板NG，pass的板子不过站（接口3）！")

            else:
                # test_result3.append(':'.join(param_item))

                self.log.info(f"3.上传过站结果...")
                # ret = xrequest.SocketUtil.x_socket_send_data(window_ip, param3)
                ret_str = x_post_webservice(
                    webservice_url,
                    station_name,
                    "3",
                    param_str3
                )
                if not ret_str.startswith('OK'):
                    error_msg = f"mes接口异常，上传测试结果失败，error：{ret_str}"

        # param3_data = "&".join(test_result3)

        if is_origin_img:
            os.remove(pcb_src_image)
            self.log.warning(f"高清整板图已删除！")

        # param3 = {
        #     "type": 3,
        #     "request_param": f"{username};{param3_data};"
        # }

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        username = other_vo.get_value_by_cons_key("username")
        check_project_name = other_vo.get_value_by_cons_key("check_project_name")
        webservice_url = other_vo.get_value_by_cons_key("webservice_url")
        station_name = other_vo.get_value_by_cons_key("station_name")

        project_name = other_vo.get_project_name()

        use_mes = other_vo.get_value_by_cons_key("use_mes")

        if use_mes == "No":
            self.log.warning(f"未启用MES！")
            return self.x_response()

        if not global_data.get('is_login'):
            return self.x_response("false", f"未登录，请先检查员工号！")

        sn_list = other_vo.list_sn()

        error_msg = ""

        # 1. 条码校验
        self.log.info(f"2.检查SN...")
        # check_param = {
        #     "type": 2,
        #     "request_param": f"{username};{';'.join(sn_list)};"
        # }
        # ret = xrequest.SocketUtil.x_socket_send_data(window_ip, check_param)

        for sn in sn_list:
            ret_str = x_post_webservice(
                        webservice_url,
                        station_name,
                        "2",
                        f"{sn};"
                    )

            if not ret_str.startswith('OK'):
                error_msg = f"mes接口异常，条码校验失败，error：{ret_str}"

        if check_project_name == "Yes":
            # 2. 检查测试程序名称是否正确
            self.log.info(f"7.校验测试程式名称是否正确...")
            # check_param2 = {
            #     "type": 9,
            #     "request_param": f"{sn_list[0]};{project_name};"
            # }
            # ret = xrequest.SocketUtil.x_socket_send_data(window_ip, check_param2)
            ret_str = x_post_webservice(
                    webservice_url,
                    station_name,
                    "9",
                    f"{sn_list[0]};{project_name};"
                )

            if not ret_str.startswith('OK'):
                error_msg = f"mes接口异常，校验程序名称失败，error：{ret_str}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        username = btn_vo.get_value_by_cons_key("username")
        password = btn_vo.get_value_by_cons_key("password")

        use_mes = btn_vo.get_value_by_cons_key("use_mes")
        station_name = btn_vo.get_value_by_cons_key("station_name")
        webservice_url = btn_vo.get_value_by_cons_key("webservice_url")

        if use_mes == "No":
            self.log.warning(f"未启用MES！")
            return self.x_response()

        btn_key = btn_vo.get_btn_key()
        if btn_key == 'check_user':
            # packet_data = {
            #     "type": 1,
            #     "request_param": f"{username};{password};",
            # }

            # ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)
            ret_str = x_post_webservice(
                    webservice_url,
                    station_name,
                    "1",
                    f"{username};{password};",
                )
            if not ret_str.startswith('OK'):
                return self.x_response("false", f"检查员工号失败，error：{ret_str}")

            self.log.info(f"检查员工号成功！")
            global_data["is_login"] = True

        # elif btn_key == 'connect_window':
        #     ret = xrequest.SocketUtil.check_window_port(window_ip)
        #     if not ret:
        #         return self.x_response("false", f"请检查window中转程序是否打开！")
        #     else:
        #         return self.x_response("true", f"中转程序连接成功！")

        return self.x_response()
