# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : create_bucket.py
# Time       ：2024/12/2 下午4:24
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json

import boto3
from botocore.config import Config
from botocore.exceptions import ClientError


def create_public_bucket(bucket_name, region=None):
    """Create an S3 bucket in a specified region and allow public access.

    If a region is not specified, the bucket is created in the S3 default region (us-east-1).

    :param bucket_name: Bucket to create
    :param region: String region to create bucket in, e.g., 'us-west-2'
    :return: True if bucket created, else False
    """
    endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
    aws_access_key_id_ = "enp6eA=="
    aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"

    # Create bucket
    try:
        s3_client = boto3.client(
            's3',
            endpoint_url=endpoint_url_,  # 自定义终端节点
            config=Config(
                s3={'addressing_style': 'path'},  # 使用路径样式 URL
                signature_version='s3v4'  # 如果适用，可以指定签名版本
            ),
            region_name=region,  # 如果适用，可以指定区域名称
            aws_access_key_id=aws_access_key_id_,  # 替换为你的访问密钥
            aws_secret_access_key=aws_secret_access_key_  # 替换为你的秘密密钥
        )
        if region is None:
            s3_client.create_bucket(Bucket=bucket_name)
        else:
            s3_client = boto3.client('s3', region_name=region)
            location = {'LocationConstraint': region}
            s3_client.create_bucket(Bucket=bucket_name,
                                    CreateBucketConfiguration=location)

        print(f"Created bucket: {bucket_name}")
    except ClientError as e:
        print(e)
        return False

    # Disable block public access settings for this bucket
    try:
        s3_client.put_public_access_block(
            Bucket=bucket_name,
            PublicAccessBlockConfiguration={
                'BlockPublicAcls': False,
                'IgnorePublicAcls': False,
                'BlockPublicPolicy': False,
                'RestrictPublicBuckets': False
            }
        )
        print("Disabled block public access settings.")
    except ClientError as e:
        print(e)
        return False

    # Add bucket policy to allow public read access
    bucket_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "PublicReadGetObject",
                "Effect": "Allow",
                "Principal": "*",
                "Action": "s3:GetObject",
                "Resource": f"arn:aws:s3:::{bucket_name}/*"
            }
        ]
    }

    # Convert the policy from JSON dict to string
    bucket_policy = json.dumps(bucket_policy)

    try:
        s3_client.put_bucket_policy(Bucket=bucket_name, Policy=bucket_policy)
        print("Added bucket policy allowing public read access.")
    except ClientError as e:
        print(e)
        return False

    return True


# 使用函数创建公开访问的存储桶
bucket_name = 'my-public-bucket'  # 替换为您想要的存储桶名称
region = None  # 可选：替换为您想要的AWS区域
create_public_bucket(bucket_name, region)
