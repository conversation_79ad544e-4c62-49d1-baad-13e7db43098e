# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/2/24 下午4:24
# Author     ：sch
# version    ：python 3.8
# Description：合肥联宝   https://jira.cvte.com/browse/ATAOI_2019-37409   类似客户：芜湖联瑞
"""
import uuid
from datetime import datetime
from typing import Any

from common import xrequest, xcons
from common.xutil import log
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


def get_time_now():
    return datetime.now().strftime("%Y%m%d%H%M%S")


def x_req_old_1300(
        api_url_old,
        x_auth_meta,
        x_env,
        x_service,
        x_skip_verify,
        x_version,
        x_rule,
        barcode,
        board_no,
        machine_id,
        group_name,
        track_index,
        operator,
        project_name,
        cycle_time,
        start_time,
        board_result_str,
        comp_image_list_1300_old
) -> Any:
    log.info(f"2. 开始调用1300设备PRD信息交互接口上传数据...")

    if not comp_image_list_1300_old:
        comp_image_list_1300_old = [{
            "Board_ID": f"BOARD{board_no}",
            "Barcode": barcode,
            "Status": board_result_str
        }]

    data_param1300_old = {
        "header": {
            "info": {
                "messageClass": "1300",
                "transferTime": get_time_now()
            },
            "location": {
                "machineID": machine_id,
                "groupName": group_name,
                "laneNo": str(track_index)
            }
        },
        "body": {
            "PCBINFO": {
                "OPERATOR": operator,
                "PCB_ID": barcode,
                "PROGRAM_NAME": project_name,
                "PROGRAM_UPDATE_TIME": get_time_now(),
                "CYCLE_TIME": cycle_time,
                "BOARD_IN_TIME": start_time,
                "LANE_NO": str(track_index),
                "PRODUCTION": {
                    "IMAGE": comp_image_list_1300_old
                }
            }
        }
    }

    headers = {
        "X-App": "pec",
        "X-Auth-Meta": x_auth_meta,
        "X-Env": x_env,
        "X-Fn": "Machine-AOI1300Process",
        "X-Service": x_service,
        "X-Skip-Verify": x_skip_verify,
        "X-Version": x_version,
        "X-Rule": x_rule,
        "Content-Type": "application/json",
        "X-Date": get_time_now(),
        "X-Request-ID": str(uuid.uuid4()),
    }

    xrequest.RequestUtil.post_json(api_url_old, data_param1300_old, headers=headers)

    return ""


def x_req_old_1400(
        api_url_old,
        x_auth_meta,
        x_env,
        x_service,
        x_skip_verify,
        x_version,
        x_rule,
        barcode,
        board_no,
        machine_id,
        group_name,
        track_index,
        only_one_location,
        only_one_error_code,
        board_result,
) -> Any:
    log.info(f"3. 开始调用1400过站信息交互接口上传数据...")

    data_param1400_old = {
        "header": {
            "info": {
                "messageClass": "1400",
                "transferTime": get_time_now()
            },
            "location": {
                "machineID": machine_id,
                "groupName": group_name,
                "laneNo": str(track_index)
            }
        },
        "body": {
            "pcb": [
                {
                    "id": barcode,
                },
            ],
            "errorStatus": [
                {
                    "value": board_result
                },
            ],
            "image": [
                {
                    "locationName": only_one_location,
                    "errorCode": only_one_error_code,
                    "barcode": barcode,
                    "boardID": f"BOARD{board_no}"
                }
            ]
        }
    }

    headers = {
        "X-App": "pec",
        "X-Auth-Meta": x_auth_meta,
        "X-Env": x_env,
        "X-Fn": "Machine-AOI1400Process",
        "X-Service": x_service,
        "X-Skip-Verify": x_skip_verify,
        "X-Version": x_version,
        "X-Rule": x_rule,
        "Content-Type": "application/json",
        "X-Date": get_time_now(),
        "X-Request-ID": str(uuid.uuid4()),
    }
    ret = xrequest.RequestUtil.post_json(api_url_old, data_param1400_old, headers=headers)
    ret_body = ret.get("body", {})
    ret_result = ret_body.get("result", {})

    log.info(f"ret result: {ret_result}")

    if "errorMessage" in ret_result and ret_result.get("status") == "FAIL":
        return f"1400接口异常，error：{ret_result.get('errorMessage')}"

    if "errorCode" in ret_result and str(ret_result.get("errorCode")) != "0":
        return f"1400接口异常，error：{ret_result.get('errorText')}"

    if "ignorekey" in ret_body:
        for item in ret_body.get("ignorekey", []):
            ret_result = item.get("result", {})

            if ret_result.get("errorCode") != "0":
                return f"1400接口异常，条码:{barcode} errorText：{ret_result.get('errorText')}"

    return ""


def x_req_new_1300(
        api_url_new,
        x_auth_meta,
        x_env,
        x_service,
        x_skip_verify,
        x_version,
        x_rule,
        barcode,
        board_no,
        machine_id,
        group_name,
        track_index,
        operator,
        project_name,
        cycle_time,
        start_time,
        board_result_str,
        comp_image_list_1300_new,
        x_app,
        x_fn_1300,  # 431机型: Machine-AOI1300Process   520机型：FinAoi-FinalAOI1300
) -> Any:
    """
    1300 新接口上传参数
    """
    log.info(f"2. 开始调用1300设备PRD信息交互接口上传数据...")

    if not comp_image_list_1300_new:
        comp_image_list_1300_new = [{
            "BOARD_ID": f"BOARD{board_no}",
            "Barcode": barcode,
            "Status": board_result_str
        }]

    data_param1300 = {
        "header": {
            "info": {
                "messageClass": "1300",
                "transferTime": get_time_now()
            },
            "location": {
                "machineID": machine_id,
                "groupName": group_name,
                "laneNo": str(track_index)
            }
        },
        "body": {
            "PCBINFO": {
                "OPERATOR": operator,
                "PCB_ID": barcode,
                "PROGRAM_NAME": project_name,
                "PROGRAM_UPDATE_TIME": get_time_now(),
                "CYCLE_TIME": cycle_time,
                "BOARD_IN_TIME": start_time,
                "LANE_NO": str(track_index),
                "PRODUCTION": {
                    "IMAGE": comp_image_list_1300_new
                }
            }
        }
    }

    headers = {
        "X-App": x_app,
        "X-Auth-Meta": x_auth_meta,
        "X-Env": x_env,
        "X-Fn": x_fn_1300,
        "X-Service": x_service,
        "X-Skip-Verify": x_skip_verify,
        "X-Version": x_version,
        "X-Rule": x_rule,
        "Content-Type": "application/json",
        "X-Date": get_time_now(),
        "X-Request-ID": str(uuid.uuid4()),
    }

    # 客户备注接口不返回，故此处不做异常处理
    xrequest.RequestUtil.post_json(api_url_new, data_param1300, headers=headers, to_json=False)


def x_req_new_1400(
        api_url_new,
        x_auth_meta,
        x_env,
        x_service,
        x_skip_verify,
        x_version,
        x_rule,
        barcode,
        machine_id,
        group_name,
        track_index,
        operator,
        comp_data_list_1400,
        board_result,
        x_app,
        x_fn_1400,  # 431机型: Machine-AOI1400Process   520机型：FinAoi-FinalAOI1400
) -> Any:
    log.info(f"3. 开始调用1400过站信息交互接口上传数据...")

    data_param1400 = {
        "header": {
            "info": {
                "messageClass": "1400",
                "transferTime": get_time_now()
            },
            "location": {
                "machineID": machine_id,
                "groupName": group_name,
                "laneNo": str(track_index)
            }
        },
        "body": {
            "pcb": {
                "id": barcode,
            },
            "operator": {
                "id": operator,
            },
            "errorStatus": {
                "value": board_result,
            },
        }
    }

    if comp_data_list_1400:
        data_param1400["body"]["image"] = comp_data_list_1400

    headers = {
        "X-App": x_app,
        "X-Auth-Meta": x_auth_meta,
        "X-Env": x_env,
        "X-Fn": x_fn_1400,
        "X-Service": x_service,
        "X-Skip-Verify": x_skip_verify,
        "X-Version": x_version,
        "X-Rule": x_rule,
        "Content-Type": "application/json",
        "X-Date": get_time_now(),
        "X-Request-ID": str(uuid.uuid4()),
    }

    ret = xrequest.RequestUtil.post_json(api_url_new, data_param1400, headers=headers)

    ret_result = ret.get("body", {}).get("result", {})

    log.info(f"ret result: {ret_result}")

    # 此处有两种接口返回的数据结构不一样，需要分开处理
    """
    返回1：
    ## fail
    {
      "header": {
        "info": {
          "messageClass": "1400",
          "transferTime": "20250220102940"
        },
        "location": {
          "machineID": "1QVFIN01",
          "groupName": "V_FINAOI",
          "laneNo": "1"
        }
      },
      "body": {
        "result": {
          "errorMessage": " Get HOLD information failed. Msg:Object reference not set to an instance of an object.",
          "status": "FAIL"
        },
        "pcb": {
          "id": "B2391910001"
        }
      }
    }
    返回二：
    ## success
    {
      "header": {
        "info": {
          "messageClass": "1400",
          "transferTime": "20250220104505"
        },
        "location": {
          "machineID": "1QVFIN01",
          "groupName": "V_FINAOI",
          "laneNo": "1"
        }
      },
      "body": {
        "result": {
          "errorCode": "0",
          "errorText": "PASS",
          "actionCode": "0"
        },
        "pcb": {
          "id": "B2391910001"
        }
      }
    }
    ## fail
    {
      "header": {
        "info": {
          "messageClass": "1400",
          "transferTime": "20250220104828"
        },
        "location": {
          "machineID": "1QVFIN01",
          "groupName": "V_FINAOI",
          "laneNo": "1"
        }
      },
      "body": {
        "result": {
          "errorCode": "1",
          "errorText": " Get HOLD information failed. Msg:Object reference not set to an instance of an object.",
          "actionCode": "0"
        }
      }
    }
    """

    if "errorMessage" in ret_result and ret_result.get("status") == "FAIL":
        return f"1400接口异常，error：{ret_result.get('errorMessage')}"

    if "errorCode" in ret_result and str(ret_result.get("errorCode")) != "0":
        return f"1400接口异常，error：{ret_result.get('errorText')}"

    return ""


class Engine(ErrorMapEngine):
    version = {
        "title": "hefeilianbao release v1.0.0.16",
        "device": "AIS431、AIS520",
        "feature": ["上传数据", "条码校验", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2025-02-26 09:28  jira->37409: 条码校验，上传数据，设备状态
date: 2025-02-27 15:55  jira->37409: 修改参数
date: 2025-02-28 17:55  jira->37409: 修改参数
date: 2025-02-28 19:42  jira->37409: 将新的1300的Board_ID改为BOARD_ID
date: 2025-03-06 18:05  兼容旧1400的接口响应
date: 2025-04-17 15:00  兼容431机型的请求头参数
date: 2025-04-17 15:50  X-App全部改为pec
date: 2025-04-17 17:08  X-Fn改为Machine-AOI1100Process、Machine-AOI1300Process、Machine-AOI1400Process、Machine-AOI4100Process
date: 2025-04-18 16:24  1300,4100接口不返回任何东西，不需要处理返回值
date: 2025-04-19 11:18  ATAOI_2019-37409：需求变更
date: 2025-04-19 21:40  根据现场反馈，修改请求参数
date: 2025-04-21 14:13  如果没法全部识别到，传第一个条码就行了，其他的传N/A
date: 2025-04-27 16:05  ATAOI_2019-37409: 条码校验失败，不停机
""",
    }

    form = {
        "machine_id": {
            "ui_name": "设备ID",
            "value": "LeiChen",
        },
        "group_name": {
            "ui_name": "站点名称",
            "value": "AOI",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "user",
        },
    }

    combo = {
        "api_version": {
            "ui_name": "接口版本",
            "item": ["新", "旧"],
            "value": "新"
        },
        "headers_version": {
            "ui_name": "设备机型",
            "item": ["AIS520", "AIS431"],
            "value": "AIS520"
        },
        "ng_check_type": {
            "ui_name": "不良品是否过站",
            "item": ["不良品过站", "不良品不过站"],
            "value": "不良品不过站"
        },
    }

    other_form = {
        "api_url_new": {
            "ui_name": "接口URL(新)",
            "value": "http://lmcp-service.lcfuturecenter.com/pec/framework-webproxy/proxy/",
        },
        "api_url_old": {
            "ui_name": "接口URL(旧)",
            "value": "http://lmcp-service.lcfuturecenter.com/pec/framework-webproxy/proxy/",
        },
        "x_auth_meta": {
            "ui_name": "X-Auth-Meta)",
            "value": "",
        },
        "x_env": {
            "ui_name": "X-Env",
            "value": "prd",
        },
        "x_service": {
            "ui_name": "X-Service",
            "value": "datasync",
        },
        "x_skip_verify": {
            "ui_name": "X-Skip-Verify",
            "value": "",
        },
        "x_version": {
            "ui_name": "X-Version",
            "value": "v1",
        },
        "x_rule": {
            "ui_name": "X-Rule",
            "value": "",
        },
    }

    def _check_sn(self, cons_key_map: dict, sn_list: list, track_index: int):
        """
        校验条码列表中的每个条码是否通过 MES 1100 进板流程检测接口。

        :param cons_key_map: 包含配置参数的字典，如接口 URL、认证信息等。
        :param sn_list: 待校验的条码列表。
        :return: 包含校验结果的响应字典。
        """
        api_url_new = cons_key_map.get("api_url_new")
        x_auth_meta = cons_key_map.get("x_auth_meta")
        x_env = cons_key_map.get("x_env")
        x_service = cons_key_map.get("x_service")
        x_skip_verify = cons_key_map.get("x_skip_verify")
        x_version = cons_key_map.get("x_version")
        x_rule = cons_key_map.get("x_rule")
        machine_id = cons_key_map.get("machine_id")
        group_name = cons_key_map.get("group_name")
        headers_version = cons_key_map.get("headers_version")

        time_now = get_time_now()

        if headers_version == "AIS520":
            x_fn_1100 = "FinAoi-FinalAOI1100"
        else:
            x_fn_1100 = "Machine-AOI1100Process"

        x_app = "pec"

        headers = {
            "X-App": x_app,
            "X-Auth-Meta": x_auth_meta,
            "X-Env": x_env,
            "X-Fn": x_fn_1100,  # 520机型：FinAoi-FinalAOI1100   431机型：Machine-AOI1100Process
            "X-Service": x_service,
            "X-Skip-Verify": x_skip_verify,
            "X-Version": x_version,
            "X-Rule": x_rule,
            "Content-Type": "application/json",
            "X-Date": time_now,
            "X-Request-ID": str(uuid.uuid4()),
        }

        ret_res = self.x_response()

        for sn in sn_list:
            if sn:
                check_param = {
                    "header": {
                        "info": {
                            "messageClass": "1100",
                            "transferTime": time_now
                        },
                        "location": {
                            "machineID": machine_id,
                            "groupName": group_name,
                            "laneNo": str(track_index)
                        }
                    },
                    "body": {
                        "pcb": {
                            "id": sn
                        }
                    }
                }

                self.log.info(f"1. 开始调用1100进板流程检测接口...")
                ret_check = xrequest.RequestUtil.post_json(api_url_new, check_param, headers=headers)
                ret_result = ret_check.get('body', {}).get('result', {})
                if str(ret_result.get('errorCode')) != '0':
                    ret_res = self.x_response("false", f'mes接口异常，条码校验失败，error：{ret_result.get("errorText")}')

            else:
                self.log.warning(f"没有条码，无需和MES（1100接口）交互！")

        return ret_res

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_version = other_vo.get_value_by_cons_key("api_version")
        headers_version = other_vo.get_value_by_cons_key("headers_version")

        cons_key_map = {
            "api_url_new": other_vo.get_value_by_cons_key("api_url_new"),
            "x_auth_meta": other_vo.get_value_by_cons_key("x_auth_meta"),
            "x_env": other_vo.get_value_by_cons_key("x_env"),
            "x_service": other_vo.get_value_by_cons_key("x_service"),
            "x_skip_verify": other_vo.get_value_by_cons_key("x_skip_verify"),
            "x_version": other_vo.get_value_by_cons_key("x_version"),
            "x_rule": other_vo.get_value_by_cons_key("x_rule"),
            "machine_id": other_vo.get_value_by_cons_key("machine_id"),
            "group_name": other_vo.get_value_by_cons_key("group_name"),
            "headers_version": headers_version,
        }

        sn_list = other_vo.list_sn()
        track_index = other_vo.get_track_index()

        if api_version == "旧":
            self.log.warning(f"接口版本为旧，不需要调用1100进板流程检查接口！")
            return self.x_response()

        if headers_version == "AIS431":
            self.log.info(f"条码校验流程移动到发送mes里实现！")
            return self.x_response()

        return self._check_sn(cons_key_map, sn_list, track_index)

    def send_data_to_mes_431(self, data_vo: DataVo) -> dict:
        """
        431机型的发送mes
        """
        api_url_new = data_vo.get_value_by_cons_key("api_url_new")

        x_auth_meta = data_vo.get_value_by_cons_key("x_auth_meta")
        x_env = data_vo.get_value_by_cons_key("x_env")
        x_service = data_vo.get_value_by_cons_key("x_service")
        x_skip_verify = data_vo.get_value_by_cons_key("x_skip_verify")
        x_version = data_vo.get_value_by_cons_key("x_version")
        x_rule = data_vo.get_value_by_cons_key("x_rule")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        group_name = data_vo.get_value_by_cons_key("group_name")
        operator = data_vo.get_value_by_cons_key("operator")
        ng_check_type = data_vo.get_value_by_cons_key("ng_check_type")

        pcb_entity = data_vo.pcb_entity

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        cycle_time = str(int(pcb_entity.get_cycle_time()))
        project_name = pcb_entity.project_name

        track_index = pcb_entity.get_track_index()

        pcb_sn = pcb_entity.pcb_barcode

        board_data_1300 = []

        pcb_id_1400 = []
        error_status_1400 = []
        comp_data_1400 = []

        """
        如果没法全部识别到，传第一个条码就行了，其他的传N/A
        """
        is_all_scan = True  # 是否全部拼板都扫到了条码

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode

            if not barcode:
                is_all_scan = False

            if not pcb_sn and barcode:
                pcb_sn = barcode

        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode and pcb_sn and board_no == "1":
                barcode = pcb_sn

            if not barcode:
                barcode = "N/A"

            if not is_all_scan and board_no != "1":
                # 如果没法全部识别到，传第一个条码就行了，其他的传N/A
                barcode = "N/A"

            board_result = board_entity.get_repair_result("0", "3")

            if board_result == "3":
                if ng_check_type == "不良品过站":
                    board_result = "1"  # 不良品过站
                else:
                    board_result = "2"  # 不良品不过站

            pcb_id_1400.append({
                "id": barcode
            })
            error_status_1400.append({
                "value": board_result
            })

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    board_data_1300.append({
                        "Board_ID": f"BOARD{board_no}",
                        "Barcode": barcode,
                        "Status": "FAIL",
                        "LOCATION": {
                            "LOCATION_NAME": comp_entity.designator,
                            "ERROR_CODE": comp_entity.repair_ng_code
                        }
                    })

                    comp_data_1400.append({
                        "locationName": comp_entity.designator,
                        "errorCode": comp_entity.repair_ng_code,
                        "barcode": barcode,
                        "boardID": f"BOARD{board_no}"
                    })

            if not board_entity.is_repair_ng():
                # PASS板卡
                comp_data_1400.append({
                    "locationName": "0",
                    "errorCode": "0",
                    "barcode": barcode,
                    "boardID": f"BOARD{board_no}"
                })

                board_data_1300.append({
                    "Board_ID": f"BOARD{board_no}",
                    "Barcode": barcode,
                    "Status": "PASS",
                })

        # 1. 调用1300接口
        log.info(f"开始调用1300设备PRD信息交互接口上传数据...")
        param_1300 = {
            "header": {
                "info": {
                    "messageClass": "1300",
                    "transferTime": get_time_now()
                },
                "location": {
                    "machineID": machine_id,
                    "groupName": group_name,
                    "laneNo": str(track_index)
                }
            },
            "body": {
                "PCBINFO": {
                    "OPERATOR": operator,
                    "PCB_ID": pcb_sn,
                    "PROGRAM_NAME": project_name,
                    "PROGRAM_UPDATE_TIME": get_time_now(),
                    "CYCLE_TIME": cycle_time,
                    "BOARD_IN_TIME": start_time,
                    "LANE_NO": str(track_index),
                    "PRODUCTION": {
                        "IMAGE": board_data_1300
                    }
                }
            }
        }

        headers = {
            "X-App": "pec",
            "X-Auth-Meta": x_auth_meta,
            "X-Env": x_env,
            "X-Fn": "Machine-AOI1300Process",
            "X-Service": x_service,
            "X-Skip-Verify": x_skip_verify,
            "X-Version": x_version,
            "X-Rule": x_rule,
            "Content-Type": "application/json",
            "X-Date": get_time_now(),
            "X-Request-ID": str(uuid.uuid4()),
        }

        # 客户备注接口不返回，故此处不做异常处理
        xrequest.RequestUtil.post_json(api_url_new, param_1300, headers=headers, to_json=False)

        # 2. 调用1400接口
        log.info(f"开始调用1400过站信息交互接口上传数据...")
        param_1400 = {
            "header": {
                "info": {
                    "messageClass": "1400",
                    "transferTime": get_time_now()
                },
                "location": {
                    "machineID": machine_id,
                    "groupName": group_name,
                    "laneNo": str(track_index)
                }
            },
            "body": {
                "pcb": pcb_id_1400,
                "errorStatus": error_status_1400,
                "image": comp_data_1400
            }
        }

        headers_1400 = {
            "X-App": "pec",
            "X-Auth-Meta": x_auth_meta,
            "X-Env": x_env,
            "X-Fn": "Machine-AOI1400Process",
            "X-Service": x_service,
            "X-Skip-Verify": x_skip_verify,
            "X-Version": x_version,
            "X-Rule": x_rule,
            "Content-Type": "application/json",
            "X-Date": get_time_now(),
            "X-Request-ID": str(uuid.uuid4()),
        }

        ret = xrequest.RequestUtil.post_json(api_url_new, param_1400, headers=headers_1400)

        ret_result = ret.get("body", {}).get("result", {})

        log.info(f"ret result: {ret_result}")

        if type(ret_result) is list:
            err_msg_list = []
            for item in ret_result:
                print(f"item: {item}")
                if "errorMessage" in item and item.get("status") == "FAIL":
                    err_msg_list.append(f"1400接口异常，error：{item.get('errorMessage')}")

                if "errorCode" in item and str(item.get("errorCode")) != "0":
                    err_msg_list.append(f"1400接口异常，error：{item.get('errorText')}")

            if err_msg_list:
                return self.x_response("false", "\n".join(err_msg_list))

        if "errorMessage" in ret_result and ret_result.get("status") == "FAIL":
            return self.x_response("false", f"1400接口异常，error：{ret_result.get('errorMessage')}")

        if "errorCode" in ret_result and str(ret_result.get("errorCode")) != "0":
            return self.x_response("false", f"1400接口异常，error：{ret_result.get('errorText')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_new = data_vo.get_value_by_cons_key("api_url_new")

        x_auth_meta = data_vo.get_value_by_cons_key("x_auth_meta")
        x_env = data_vo.get_value_by_cons_key("x_env")
        x_service = data_vo.get_value_by_cons_key("x_service")
        x_skip_verify = data_vo.get_value_by_cons_key("x_skip_verify")
        x_version = data_vo.get_value_by_cons_key("x_version")
        x_rule = data_vo.get_value_by_cons_key("x_rule")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        group_name = data_vo.get_value_by_cons_key("group_name")
        operator = data_vo.get_value_by_cons_key("operator")
        api_version = data_vo.get_value_by_cons_key("api_version")
        ng_check_type = data_vo.get_value_by_cons_key("ng_check_type")
        api_url_old = data_vo.get_value_by_cons_key("api_url_old")
        headers_version = data_vo.get_value_by_cons_key("headers_version")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.get_track_index()

        if headers_version == "AIS431":
            # 由于改动过大，431的直接重写了，下面关于431的逻辑将不会生效，431机型将走函数`self.send_data_to_mes_431`逻辑

            # 1. 先条码校验
            cons_key_map = {
                "api_url_new": api_url_new,
                "x_auth_meta": x_auth_meta,
                "x_env": x_env,
                "x_service": x_service,
                "x_skip_verify": x_skip_verify,
                "x_version": x_version,
                "x_rule": x_rule,
                "machine_id": machine_id,
                "group_name": group_name,
                "headers_version": headers_version,
            }

            ret_check = self._check_sn(
                cons_key_map,
                pcb_entity.all_barcode,
                track_index
            )

            if ret_check != self.x_response():
                return ret_check

            # 2. 再发送数据到mes
            return self.send_data_to_mes_431(data_vo)

        if headers_version == "AIS520":
            x_fn_1300 = "FinAoi-FinalAOI1300"
            x_fn_1400 = "FinAoi-FinalAOI1400"
        else:
            x_fn_1300 = "Machine-AOI1300Process"
            x_fn_1400 = "Machine-AOI1400Process"

        x_app = "pec"

        self.log.info(f"api version: {api_version}")

        # pcb_sn = pcb_entity.pcb_barcode

        ret_res = self.x_response()
        ret_error_list = []

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        cycle_time = str(int(pcb_entity.get_cycle_time()))
        project_name = pcb_entity.project_name

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_no == "0":
                board_no = "1"

            comp_data_list_1400 = []
            comp_image_list_1300_new = []
            comp_image_list_1300_old = []

            only_one_location = "0"
            only_one_error_code = "0"

            board_result_str = board_entity.get_repair_result("PASS", "FAIL")

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_image_list_1300_new.append({
                        "BOARD_ID": f"BOARD{board_no}",
                        "Barcode": barcode,
                        "Status": board_entity.get_repair_result("PASS", "FAIL"),
                        "LOCATION": {
                            "LOCATION_NAME": comp_entity.designator,
                            "ERROR_CODE": comp_entity.repair_ng_code
                        }
                    })

                    comp_image_list_1300_old.append({
                        "Board_ID": f"BOARD{board_no}",
                        "Barcode": barcode,
                        "Status": board_entity.get_repair_result("PASS", "FAIL"),
                        "LOCATION": {
                            "LOCATION_NAME": comp_entity.designator,
                            "ERROR_CODE": comp_entity.repair_ng_code
                        }
                    })

                    comp_data_list_1400.append({
                        "locationName": comp_entity.designator,
                        "errorCode": comp_entity.repair_ng_code,
                        "barcode": barcode,
                        "boardID": f"BOARD{board_no}"
                    })

                    comp_tag = comp_entity.designator

                    only_one_location = comp_tag
                    only_one_error_code = comp_entity.repair_ng_code

            board_result = board_entity.get_repair_result("0", "3")

            if board_result == "3":
                if ng_check_type == "不良品过站":
                    board_result = "1"  # 不良品过站
                else:
                    board_result = "2"  # 不良品不过站

            if api_version == "新":
                x_req_new_1300(
                    api_url_new,
                    x_auth_meta,
                    x_env,
                    x_service,
                    x_skip_verify,
                    x_version,
                    x_rule,
                    barcode,
                    board_no,
                    machine_id,
                    group_name,
                    track_index,
                    operator,
                    project_name,
                    cycle_time,
                    start_time,
                    board_result_str,
                    comp_image_list_1300_new,
                    x_app,
                    x_fn_1300
                )

                ret_1400_new = x_req_new_1400(
                    api_url_new,
                    x_auth_meta,
                    x_env,
                    x_service,
                    x_skip_verify,
                    x_version,
                    x_rule,
                    barcode,
                    machine_id,
                    group_name,
                    track_index,
                    operator,
                    comp_data_list_1400,
                    board_result,
                    x_app,
                    x_fn_1400
                )

                if ret_1400_new:
                    ret_error_list.append(ret_1400_new)

            else:
                x_req_old_1300(
                    api_url_old,
                    x_auth_meta,
                    x_env,
                    x_service,
                    x_skip_verify,
                    x_version,
                    x_rule,
                    barcode,
                    board_no,
                    machine_id,
                    group_name,
                    track_index,
                    operator,
                    project_name,
                    cycle_time,
                    start_time,
                    board_result_str,
                    comp_image_list_1300_old
                )

                ret_1400_old = x_req_old_1400(
                    api_url_old,
                    x_auth_meta,
                    x_env,
                    x_service,
                    x_skip_verify,
                    x_version,
                    x_rule,
                    barcode,
                    board_no,
                    machine_id,
                    group_name,
                    track_index,
                    only_one_location,
                    only_one_error_code,
                    board_result,
                )
                if ret_1400_old:
                    ret_error_list.append(ret_1400_old)

        if ret_error_list:
            return self.x_response("false", "\n".join(ret_error_list))

        return ret_res

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_new = other_vo.get_value_by_cons_key("api_url_new")

        x_auth_meta = other_vo.get_value_by_cons_key("x_auth_meta")
        x_env = other_vo.get_value_by_cons_key("x_env")
        x_service = other_vo.get_value_by_cons_key("x_service")
        x_skip_verify = other_vo.get_value_by_cons_key("x_skip_verify")
        x_version = other_vo.get_value_by_cons_key("x_version")
        x_rule = other_vo.get_value_by_cons_key("x_rule")
        machine_id = other_vo.get_value_by_cons_key("machine_id")
        headers_version = other_vo.get_value_by_cons_key("headers_version")

        if headers_version == "AIS520":
            x_fn_4100 = "FinAoi-FinalAOI4100"
        else:
            x_fn_4100 = "Machine-AOI4100Process"

        x_app = "pec"

        api_version = other_vo.get_value_by_cons_key("api_version")

        if api_version == "旧":
            self.log.warning(f"接口版本为旧，不需要调用4100接口！")
            return self.x_response()

        headers = {
            "X-App": x_app,
            "X-Auth-Meta": x_auth_meta,
            "X-Env": x_env,
            "X-Fn": x_fn_4100,
            "X-Service": x_service,
            "X-Skip-Verify": x_skip_verify,
            "X-Version": x_version,
            "X-Rule": x_rule,
            "Content-Type": "application/json",
            "X-Date": get_time_now(),
            "X-Request-ID": str(uuid.uuid4()),
        }

        status_param = {
            "Machine_ID": machine_id,
            "work_MSG": {
                "Type_Code": other_vo.get_status_code_v3(),
                "Description": other_vo.get_status_desc_v3(),
                "Occur_Time": get_time_now()
            }
        }

        self.log.info(f"正在调用4100 机况信息接收接口上传数据...")
        xrequest.RequestUtil.post_json(api_url_new, status_param, headers=headers, to_json=False)

        return self.x_response()
