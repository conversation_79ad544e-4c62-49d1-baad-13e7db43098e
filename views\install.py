# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : install.py
# Time       ：2024/6/21 下午4:11
# Author     ：sch
# version    ：python 3.8
# Description： 未使用！！！！！！！！！！！
"""
import os
import shutil
import stat

from PyQt5.QtWidgets import QWidget

from common import xrequest, xutil, xconfig
from common.xutil import log
from static import xicon
from templates.install import Ui_InstallForm

install_path = "/home/<USER>/mesconfig_python"  # 安装路径


def create_desktop_on_menu():
    """
    创建桌面快捷方式
    :return:
    """
    desktop_file1 = f"{xconfig.home_dir}/桌面"
    desktop_file2 = f"{xconfig.home_dir}/Desktop"

    if os.path.exists(desktop_file2):
        desktop_dir = desktop_file2
    elif os.path.exists(desktop_file1):
        desktop_dir = desktop_file1
    else:
        log.warning("找不到桌面，创建桌面快捷方式失败")
        return

    desk_file = f"{desktop_dir}/mesconfig_python.desktop"

    if os.path.exists(desk_file):
        os.remove(desk_file)  # 删掉，重新创建

    static_dir = f"{install_path}/MES_STATIC"

    if not os.path.exists(static_dir):
        os.makedirs(static_dir)

    icon_file = f"{static_dir}/mes_icon.png"
    if not os.path.exists(icon_file):
        xutil.OtherUtil.base64_to_file(icon_file, xicon.mes_icon_base64)

    desktop_content = f"""[Desktop Entry]
Name=pymesApp
Icon={icon_file}
Exec={install_path}/mesconfig
Path={install_path}
Comment=CVTE AOI MES OL Application
Categories=Application
Terminal=false
Type=Application"""

    xutil.FileUtil.write_content_to_file(desk_file, desktop_content)
    os.chmod(desk_file, stat.S_IRWXU)
    log.info(f"创建桌面快捷方式成功！")


class InstallViews(QWidget, Ui_InstallForm):

    def __init__(self):
        super(InstallViews, self).__init__()
        self.setupUi(self)

        self.btn_next.clicked.connect(self.next_on_click)

    def next_on_click(self):
        if self.btn_next.text() == "关闭":
            self.close()
            log.info(f"安装界面已关闭！")
            return

        is_connect = xrequest.SocketUtil.check_window_port("127.0.0.1", 9090)

        if is_connect:
            self.ui_content.setText(f"请先退出正在运行的Mes配置器，再进行更新...")
        else:
            bak_path = "/home/<USER>/mesconfig_python_bak"  # 备份路径

            if os.path.exists(install_path):
                self.ui_content.setText(f"正在执行更新步骤...")
                if os.path.exists(bak_path):
                    shutil.rmtree(bak_path)

                shutil.move(install_path, bak_path)
                log.info("bak mesconfig_python done!")
            else:
                self.ui_content.setText(f"正在执行安装步骤...")

            pwd = os.getcwd()
            log.info(f"install pwd: {pwd}")

            if pwd == install_path:
                log.warning(f"无需执行安装移动步骤！")
            else:
                # 1. 将数据包移动到 install_path
                os.mkdir(install_path)
                pwd_list = os.listdir(pwd)

                for item in pwd_list:
                    src_file = f"{pwd}/{item}"

                    if os.path.isdir(src_file):
                        shutil.copytree(src_file, f"{install_path}/{item}")
                    else:
                        shutil.copy(src_file, f"{install_path}/{item}")

            # 2. 创建桌面快捷方式
            create_desktop_on_menu()

            # 3. 设置开机自启
            desktop_file1 = f"{xconfig.home_dir}/桌面"
            desktop_file2 = f"{xconfig.home_dir}/Desktop"

            if os.path.exists(desktop_file2):
                desktop_dir = desktop_file2
            elif os.path.exists(desktop_file1):
                desktop_dir = desktop_file1
            else:
                log.warning("找不到桌面，创建桌面快捷方式失败")
                return

            # 复制到 /home/<USER>/.config/autostart
            desk_file = f"{desktop_dir}/mesconfig_python.desktop"
            autostart_file = f"{xconfig.home_dir}/.config/autostart/mesconfig_python.desktop"
            xutil.FileUtil.copy_file(desk_file, autostart_file)
            log.info("设置开机自启动成功！")

            log.info(f"配置器安装成功！")
            self.ui_content.setText(f"配置器安装成功，请前往桌面打开！")

            self.btn_next.setText("关闭")
