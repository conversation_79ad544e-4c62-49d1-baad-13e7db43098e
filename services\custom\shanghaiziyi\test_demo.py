# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/9/3 下午5:01
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

if __name__ == '__main__':
        ret_str1 = """<Cluster>
<Name></Name>
<NumElts>4</NumElts>
<I32>
<Name>Status</Name>
<Val>200</Val>
</I32>
<Boolean>
<Name>Success</Name>
<Val>1</Val>
</Boolean>
<String>
<Name>Msg</Name>
<Val></Val>
</String>
<Cluster>
<Name>Response</Name>
<NumElts>2</NumElts>
<String>
<Name>ClampNO</Name>
<Val>pcb_barcode001</Val>
</String>
<Array>
<Name>UUTLabelNo</Name>
<Dimsize>2</Dimsize>
<String>
<Name>UUTLabelNo</Name>
<Val></Val>
</String>
<String>
<Name>UUTLabelNo</Name>
<Val></Val>
</String>
</Array>
</Cluster>
</Cluster>"""

        root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
        cluster = root1.find("Cluster").find("Array").findall("String")

        ret_sn_list = []
        for item in cluster:
            i = item.find("Val")
            print(bool(i))
            if i:
                ret_sn_list.append(i.text)

        print(ret_sn_list)

#     ret_str2 = """<Cluster>
# <Name></Name>
# <NumElts>4</NumElts>
# <I32>
# <Name>Status</Name>
# <Val>200</Val>
# </I32>
# <Boolean>
# <Name>Success</Name>
# <Val>1</Val>
# </Boolean>
# <String>
# <Name>Msg</Name>
# <Val>SN1:;SN2:</Val>
# </String>
# <Cluster>
# <Name>Response</Name>
# <NumElts>2</NumElts>
# <String>
# <Name>ClampNO</Name>
# <Val></Val>
# </String>
# <Array>
# <Name>UUTLabelNo</Name>
# <Dimsize>0</Dimsize>
# <String>
# <Name>UUTLabelNo</Name>
# <Val></Val>
# </String>
# </Array>
# </Cluster>
# </Cluster>"""
#
#     root2 = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
#     status = root2.find("I32").find("Val").text
#     msg = root2.find("String").find("Val").text
#
#     print(status, msg, type(status))
