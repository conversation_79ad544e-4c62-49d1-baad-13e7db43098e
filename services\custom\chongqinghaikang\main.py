"""
# File       : main.py
# Time       ：2025/07/14 16:29
# Author     ："wxc"
# version    ：python 3.8
# Description：重庆海康
"""

import os
from typing import Any

from common import xcons, xutil, xenum, xrequest
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import Error<PERSON>apEngine


def match_project_name(project_name: str) -> list:
    """
    模糊匹配程序名
    :param project_name: 板式
    :return:
    """
    project_path = f"/home/<USER>/aoi/program/projects"

    file_list_dir = os.listdir(project_path)

    filter_project_file = []

    for file in file_list_dir:
        filepath = f"{project_path}/{file}"

        if not os.path.isdir(filepath):
            continue

        if project_name not in file:
            continue

        filter_project_file.append(file)

    return filter_project_file


txt_template = """[Project]
Model Name:{Model Name}
InspectDate:{InspectDate}
Cycle Time:{Cycle Time}
Line Name:{Line Name}
Station Name:{Station Name}
Machine Name:{Machine Name}
Test Point Count:{Test Point Count}
Real Test Point Count:{Real Test Point Count}
NG Point Count:{NG Point Count}
FC Point Count:{FC Point Count}
Test Board Count:{Test Board Count}
OK Board Count:{OK Board Count}
NG Board Count:{NG Board Count}
FC Board Count:{FC Board Count}
Test Board Location:{Test Board Location}
Bad Board Count:{Bad Board Count}
Bad Board Location:{Bad Board Location}

[Panel_001]
PanelID=1
Barcode={Barcode}
Skipped={Skipped}
N Components={N Components}
N Inspected Components={N Inspected Components}
AOI Result={AOI Result}
OP Result={OP Result}
N AOI NG Parts={N AOI NG Parts}
N OP NG Parts={N OP NG Parts}{board_data_str}
"""

board_template = """
[Board_{board_no_flag}]
PanelID=1
BoardID={BoardID}
Barcode={Barcode}
Skipped={Skipped}
N Components={N Components}
N Inspected Components={N Inspected Components}
AOI Result={AOI Result}
OP Result={OP Result}
N AOI NG Parts={N AOI NG Parts}
N OP NG Parts={N OP NG Parts}
N OP FC Parts={N OP FC Parts}{comp_data_str}"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["重庆海康", "chongqinghaikang"],
        "version": "release v1.0.0.1",
        "device": "AIS40X",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-07-14 16:31 from wuhanhaorong---修改文件命名规则：线体+时间戳
"""
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        },
        "bak_path": {
            "ui_name": "备份路径",
            "value": "",
        },

    }

    combo = {

    }

    form = {
        "line_name": {
            "ui_name": "线体名称",
            "value": "",
        },
        "station_name": {
            "ui_name": "设备编号",
            "value": "",
        },
        "order_id": {
            "ui_name": "订单号",
            "value": "",
        },
        "matnr_id": {
            "ui_name": "组件物料号",
            "value": "",
        },
    }

    other_combo = {
        "is_save_bak1": {
            "ui_name": "备份数据",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "is_cron_connect": {
            "ui_name": "开启定时同步数据",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "cron_time": {
            "ui_name": "定时同步频率(分钟)",
            "item": ["1", "3", "5", "10", "30", "60"],
            "value": "10",
        },
    }

    other_form = {
        "api_url": {
            "ui_name": "从MPM系统获取程式接口URL",
            "value": "http://meshz-sit.hikvision.com.cn:8000/hiscada"
        },
        "authorization": {
            "ui_name": "Authorization头",
            "value": "Basic ODkwMC0wMTxxxxx",
        }
    }

    button = {
        "copy_to_save_path": {
            "ui_name": "同步数据"
        },
        "query_project_btn": {
            "ui_name": "从MPM系统自动调程式"
        },
    }

    def __init__(self):
        self.common_config["sendmes_setting3"] = xenum.SendMesSetting3.Send

    def init_main_window(self, main_window, other_vo: OtherVo):
        other_vo = OtherVo({}, main_window.config_data)

        is_cron_connect = other_vo.get_value_by_cons_key("is_cron_connect")
        cron_time = other_vo.get_value_by_cons_key("cron_time", to_int=True)

        if is_cron_connect == "Yes":
            interval_cron = True
        else:
            interval_cron = False

        main_window.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        main_window.config_data["app_setting"]["custom_interval_time"] = int(cron_time * 60)  # 分钟
        self.log.info("init main window done!")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        line_name = data_vo.get_value_by_cons_key("line_name")
        station_name = data_vo.get_value_by_cons_key("station_name")
        bak_path = data_vo.get_value_by_cons_key("bak_path")
        is_save_bak1 = data_vo.get_value_by_cons_key("is_save_bak1")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name

        if "TOP" in project_name:
            board_side1 = "TOP"
        elif "BOT" in project_name:
            board_side1 = "BOT"
        elif pcb_entity.board_side == "T":
            board_side1 = "TOP"
        elif pcb_entity.board_side == "B":
            board_side1 = "BOT"
        else:
            board_side1 = "TOP"

        board_data_str = ""

        board_no_list = []

        bad_board_list = []

        comp_ok_count = 0
        comp_ng_count = 0
        comp_fc_count = 0

        board_ok_count = 0
        board_ng_count = 0
        board_fc_count = 0
        board_bad_count = 0
        panel_skipped = "No"

        pcb_sn = pcb_entity.pcb_barcode

        pcb_sn = pcb_sn.replace("{", "").replace("}", "")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no
            barcode = board_entity.barcode

            barcode = barcode.replace("{", "").replace("}", "")

            board_no_1 = board_no.zfill(3)

            board_no_list.append(board_no)

            comp_ok_count = board_entity.comp_total_number - board_entity.comp_robot_ng_number
            comp_ng_count += board_entity.comp_repair_ng_number
            comp_fc_count += board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number

            final_result = board_entity.get_final_result()

            board_skipped = "No"
            if final_result == "PASS":
                board_ok_count += 1
            elif final_result == "REPASS":
                board_fc_count += 1
            elif final_result == "NG":
                board_ng_count += 1
            else:
                board_skipped = "Yes"
                panel_skipped = "Yes"
                bad_board_list.append(board_no)
                board_bad_count += 1

            comp_data_str = ""
            comp_ix = 0
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator
                    comp_part = comp_entity.part
                    repair_ng_str = comp_entity.repair_ng_str
                    repair_ng_code = comp_entity.repair_ng_code
                    comp_ix += 1
                    comp_data_str += f"\nNG Part {str(comp_ix).zfill(4)}={comp_tag}_{comp_part}_{repair_ng_str}_{repair_ng_code.zfill(3)}"

            board_data_str += board_template.format(**{
                "board_no_flag": board_no_1,
                "BoardID": board_no,
                "Barcode": barcode,
                "Skipped": board_skipped,
                "N Components": board_entity.comp_total_number,
                "N Inspected Components": board_entity.comp_total_number,
                "AOI Result": board_entity.get_robot_result("OK", "NG"),
                "OP Result": board_entity.get_repair_result("OK", "NG"),
                "N AOI NG Parts": board_entity.comp_robot_ng_number,
                "N OP NG Parts": board_entity.comp_repair_ng_number,
                "N OP FC Parts": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "comp_data_str": comp_data_str
            })

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        txt_content = txt_template.format(**{
            "Model Name": project_name,
            "InspectDate": time_file,
            "Cycle Time": pcb_entity.get_cycle_time(),
            "Line Name": line_name,
            "Station Name": station_name,
            "Machine Name": board_side1,
            "Test Point Count": pcb_entity.comp_count,
            "Real Test Point Count": pcb_entity.comp_count,
            "NG Point Count": comp_ng_count,
            "FC Point Count": comp_fc_count,
            "Test Board Count": pcb_entity.board_count,
            "OK Board Count": board_ok_count,
            "NG Board Count": board_ng_count,
            "FC Board Count": board_fc_count,
            "Test Board Location": ",".join(board_no_list),
            "Bad Board Count": board_bad_count,
            "Bad Board Location": ",".join(bad_board_list),
            "Barcode": pcb_sn,
            "Skipped": panel_skipped,
            "N Components": pcb_entity.comp_count,
            "N Inspected Components": comp_ok_count,
            "AOI Result": pcb_entity.get_robot_result(),
            "OP Result": pcb_entity.get_repair_result(),
            "N AOI NG Parts": board_ng_count + board_fc_count,
            "N OP NG Parts": board_ng_count,
            "board_data_str": board_data_str,
        })

        filepath = f"{save_path}/{line_name}_{time_file}.txt"

        txt_content = txt_content.replace("\n", "\r\n")

        try:
            xutil.FileUtil.write_content_to_file(filepath, txt_content)
        except Exception as err:
            self.log.warning(f"数据写入保存路径失败，另存至备份路径！err:{err}")

            if is_save_bak1:
                if not bak_path:
                    return self.x_response("false", f"请选择备份路径！")

                bak_filepath = f"{bak_path}/{line_name}_{time_file}.txt"
                xutil.FileUtil.write_content_to_file(bak_filepath, txt_content)

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        save_path = other_vo.get_value_by_cons_key("save_path")
        bak_path = other_vo.get_value_by_cons_key("bak_path")

        if save_path and bak_path:
            all_files = os.listdir(bak_path)
            file_number = len(all_files)

            if file_number == 0:
                self.log.warning(f"没有需要同步的备份数据")

            self.log.info(f"正在同步数据，需要同步的数据数量：{file_number}")
            for item in all_files:
                src_filepath = f"{bak_path}/{item}"
                dst_filepath = f"{save_path}/{item}"

                if os.path.isfile(src_filepath):
                    xutil.FileUtil.move_file(src_filepath, dst_filepath)
                else:
                    self.log.warning(f"{src_filepath}不是文件，暂不同步！")

        else:
            self.log.info(f"未选择备份路径和保存路径")

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        save_path = btn_vo.get_value_by_cons_key("save_path")
        bak_path = btn_vo.get_value_by_cons_key("bak_path")
        btn_key = btn_vo.get_btn_key()
        api_url_get_project = btn_vo.get_value_by_cons_key("api_url")
        order_id = btn_vo.get_value_by_cons_key("order_id")
        device_id = btn_vo.get_value_by_cons_key("device_id")
        authorization = btn_vo.get_value_by_cons_key("authorization")
        matnr_id = btn_vo.get_value_by_cons_key("matnr_id")
        if btn_key == "copy_to_save_path":
            if save_path and bak_path:
                all_files = os.listdir(bak_path)

                file_number = len(all_files)

                if file_number == 0:
                    return self.x_response("false", f"没有需要同步的备份数据！")

                self.log.info(f"正在同步数据，需要同步的数据数量：{file_number}")
                for item in all_files:
                    src_filepath = f"{bak_path}/{item}"
                    dst_filepath = f"{save_path}/{item}"

                    if os.path.isfile(src_filepath):
                        xutil.FileUtil.move_file(src_filepath, dst_filepath)
                    else:
                        self.log.warning(f"{src_filepath}不是文件，暂不同步！")

            else:
                return self.x_response("false", "未选择备份路径和保存路径")

        elif btn_key == "query_project_btn":
            headers = {
                "MachineId": device_id,
                "Authorization": authorization
            }

            pro_param = {
                "aufnr": order_id,
                "compMatnr": matnr_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_get_project, pro_param, headers)
            if ret.get("code") != "000000":
                return self.x_response("false", f"从mes获取程式名错误 {ret.get('message')}")
            flag_data = ret.get("data")
            product_program = flag_data.get("maktx")
            if not product_program:
                return self.x_response("false", "从mes获取到的程式名为空")
            check_project_list = match_project_name(product_program)
            if len(check_project_list) == 1:
                project_name = check_project_list[0]
                self.log.info(f"找到本地板式[{project_name}]，正在处理中...")

                ret = xrequest.SocketUtil.api_check_project(project_name, auto_start="1")
                if ret.get("ErrorCodeV2") != "0":
                    return self.x_response("false", f"自动执行程式失败，error：{ret.get('ErrorMessageV2')}")
            elif len(check_project_list) == 0:
                return self.x_response("false", f"程式库无该程式，无法进行处理！")
            else:
                return self.x_response("false", f"本地找到了多个程式，无法进行处理！ ---> {check_project_list}")

        return self.x_response()
