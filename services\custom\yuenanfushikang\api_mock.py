# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : api_mock.py
# Time       ：2024/10/18 下午4:27
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json

import uvicorn
from fastapi import FastAPI, Response

app = FastAPI()


@app.post("/api/center.asmx/getData")
def get_data():
    ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <s:Body>
        <getDataResponse xmlns="http://tempuri.org/">
            <getDataResult>{"Succeeded":true,"Errors":"","StatusCode":"200","Data":[{"panelSN":"PALM-W-00228-003","pcbSN":["X170BMA","X170BM8","X170BNC","X170BMZ","X170BN9","X170BND"]}]}</getDataResult>
        </getDataResponse>
    </s:Body>
</s:Envelope>"""
    return Response(media_type="application/xml", content=ret_str)


@app.post("/api/center.asmx/uploadData")
def get_data():
    ret2 = {"Succeeded": False,
            "Errors": "ORA-00936: missing expression;传入的参数:{\"timestamp\":\"1730087995\",\"random\":\"sdfsd\",\"data\":[{\"pcbSN\":\"X170BMA\",\"line\":\"PTH1\",\"aoiType\":\"AOI\",\"totalPoint\":\"100\",\"aoiPoint\":\"100\",\"opPoint\":\"10\",\"machine\":\"AOI\"}]}",
            "StatusCode": "400", "Data": None}

    ret_str2 = f"""<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <s:Body>
        <uploadDataResponse xmlns="http://tempuri.org/">
            <uploadDataResult>{json.dumps(ret2, ensure_ascii=False)}</uploadDataResult>
        </uploadDataResponse>
    </s:Body>
</s:Envelope>"""
    return Response(media_type="application/xml", content=ret_str2)


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8081)
