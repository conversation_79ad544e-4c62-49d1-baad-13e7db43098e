# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test.py
# Time       ：2023/4/7 上午9:04
# Author     ：sch
# version    ：python 3.8
# Description：康冠, 客户现场Ubuntu14.0.4系统，无法运行，改为c++版本
"""
from typing import Any

from common import xutil, xcons, xrequest
from common.xutil import x_response, log
from vo.mes_vo import DataVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine


class KangGuanEngine(BaseEngine):
    version = {
        "title": "kangguan release v1.1.0.4",
        "device": "40x",
        "author": "sunchangheng",
        "feature": [
            "发送数据到Mes",
        ],
        "release": """
date: 2023-04-09 09:58  init
date: 2023-04-10 10:18  上传数据到Mes
""",
    }

    combo = {
        "is_upload_image": {
            "item": [
                "上传", "不上传"
            ],
            "value": "上传",
            "ui_name": "FTP上传图片"
        },
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/mes/v1/upload_data"
        },
        "ftp_host": {
            "ui_name": "Ftp 服务器",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "Ftp 端口",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "Ftp 用户名",
            "value": "admin"
        },
        "ftp_password": {
            "ui_name": "Ftp 密码",
            "value": "fake_password"
        },
        "ftp_path": {
            "ui_name": "Ftp 路径",
            "value": "/AOI/NG_IMAGE"
        },
        "work_class": {
            "ui_name": "班次",
            "value": "早班"
        },
        "line": {
            "ui_name": "线别",
            "value": "Line1"
        },
        "operator": {
            "ui_name": "操作员",
            "value": "admin"
        },
        "device_sn": {
            "ui_name": "设备识别号",
            "value": "AOI0001"
        },

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity

        api_url = data_vo.get_value_by_cons_key("api_url")
        is_upload_image = data_vo.get_value_by_cons_key("is_upload_image")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        work_class = data_vo.get_value_by_cons_key("work_class")
        line = data_vo.get_value_by_cons_key("line")
        operator = data_vo.get_value_by_cons_key("operator")
        device_sn = data_vo.get_value_by_cons_key("device_sn")

        try:
            ftp_port = int(ftp_port)
        except Exception as e:
            return x_response("false", f"Ftp 端口必须为数字[1-65536]，error：{e}")

        if is_upload_image == "上传":
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()
            ftp_client.cd_or_mkdir(ftp_path)
        else:
            ftp_client = None

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE)

        log.info(pcb_entity)
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)
            barcode = board_entity.barcode

            comp_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if not comp_entity.repair_result:

                    # 上传FTP图片
                    if is_upload_image == "上传" and comp_entity.image_path:
                        comp_img_name = f"{time_now}_{barcode}_{comp_entity.designator}.png"
                        ftp_path_tmp = f"{ftp_path}/{comp_img_name}"
                        ftp_client.upload_file(comp_entity.image_path, f"{ftp_path}/{comp_img_name}")
                    else:
                        ftp_path_tmp = ""

                    comp_ng_list.append({
                        "badBarCode": barcode,
                        "ngComponentType": comp_entity.type,
                        "bitNum": comp_entity.designator,
                        "deviceCheckBadDesc": comp_entity.robot_ng_str,
                        "deviceCheckResult": "OK" if comp_entity.robot_result else "NG",
                        "deviceRejudgeBadDesc": comp_entity.repair_ng_str,
                        "deviceRejudgeResult": "OK" if comp_entity.repair_result else "NG",
                        "deviceMaterialCode": comp_entity.part,
                        "devicePackaging": comp_entity.package,
                        "filePath": ftp_path_tmp,
                    })

            comp_robot_ng_number = board_entity.comp_robot_ng_number
            comp_repair_ng_number = board_entity.comp_repair_ng_number

            board_param = {
                "pcbModelNum": pcb_entity.pcb,
                "bomNum": pcb_entity.bom,
                "deviceDiscernNum": device_sn,
                "barCode": barcode,
                "testTime": pcb_entity.get_cycle_time(),
                "testResult": "OK" if board_entity.repair_result else "NG",
                "pcbNgCount": comp_robot_ng_number,
                "errorJudgeNgCount": comp_robot_ng_number - comp_repair_ng_number,
                "confirmNgCount": board_entity.comp_repair_ng_number,
                "componentCount": board_entity.comp_total_number,
                "workClass": work_class,
                "lineName": line,
                "operator": operator,
                "checkDateTime": str(pcb_entity.get_start_time()),
                "badInfoArray": comp_ng_list,
            }

            ret = xrequest.RequestUtil.post_json(api_url, board_param)
            if not ret.get("success") or ret.get("success") == "false":
                return x_response("false", f"mes接口响应异常，上传数据失败，error：{ret.get('message')}")

        if is_upload_image == "上传":
            ftp_client.close()

        return x_response()
