# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/14 下午4:33
# Author     ：sch
# version    ：python 3.8
# Description：惠州海盈
"""
import os
import shutil
from typing import Any

from PIL import Image

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine

csv_board_template = """程序名,{pcb_project_name}
测试时间,{pcb_test_time}
操作员,{pcb_repair_user}
大板条码,{pcb_sn}
整板结果,{pcb_final_result}
拼板条码,{barcode}
拼板序号,{board_no}
拼板结果,{final_result}
拼板器件数量,{comp_total_number}
拼板器件复判NG数量,{comp_repair_ng_number}

CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,Comp<PERSON>ser<PERSON><PERSON>,CompUserResult,CompImage{CompData}
"""

csv_comp_template = """
{tag},{part},{package},{type},{robot_ng_code},{robot_ng_str},{repair_ng_code},{repair_ng_str},{image_path}"""

cache_path = f"{os.getcwd()}/cache_data"


class Engine(BaseEngine):
    version = {
        "title": "huizhouhaiying release v1.0.0.5",
        "device": "203P,501B",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-14 16:34  init
date: 2023-11-24 15:42  修改接口请求方式
date: 2023-12-21 18:34  修复B面拼板图不对bug
date: 2024-03-16 10:14  修改ftp保存路径
date: 2024-03-20 09:25  ftp保存路径改为： 根路径/程序pcb名/年月日/条码/file.jpg
""",
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "res_code": {
            "ui_name": "资源代码",
            "value": ""
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        # 初始化
        self.log.info(f"cache path: {cache_path}")
        xutil.FileUtil.ensure_dir_exist(cache_path)

        file_list = os.listdir(cache_path)
        self.log.info(f"缓存的压缩文件数量：{len(file_list)}")

        for filename in file_list:
            file_path = os.path.join(cache_path, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)  # 如果是文件，则删除它
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # 如果是文件夹，则递归删除整个文件夹

            self.log.info(f"文件：{file_path} 已删除")

        if len(file_list) != 0:
            self.log.info(f"缓存文件已删除完毕！")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        res_code = data_vo.get_value_by_cons_key("res_code")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.pcb
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        time_date = time_file[:8]

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP Port必须为数字，error：{err}")

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        pcb_sn = pcb_entity.pcb_barcode

        pcb_src_img_list = pcb_entity.pcb_image

        self.log.info(f"{pcb_src_img_list=}")
        t_board_box_info = pcb_entity.get_board_box_position()

        if len(pcb_src_img_list) >= 2:
            b_report_xml = pcb_entity.get_pcb_b_report_xml()
            if b_report_xml:
                b_board_box_info = pcb_entity.get_board_box_position(b_report_xml)
            else:
                self.log.warning(f"未找到B面的report.xml，B面拼板图不保存！")
                b_board_box_info = {}
        else:
            b_board_box_info = {}

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not barcode:
                barcode = time_file

            ftp_full_path = f"{ftp_path}/{project_name}/{time_date}/{barcode}"
            ftp_client.cd_or_mkdir(ftp_full_path)
            self.log.info(f"ftp路径已切换至：{ftp_full_path}")

            # 2. 保存拼板图
            if len(pcb_src_img_list) >= 2:
                # 上下面
                t_src_img = pcb_src_img_list[0]
                b_src_img = pcb_src_img_list[1]

                t_board_box = t_board_box_info.get(board_no, ())
                if t_board_box:
                    self.log.info(f"开始保存拼板[{board_no}]的T面图片...")
                    image = Image.open(t_src_img)
                    cropped_image = image.crop(t_board_box)

                    src_board_img = f"{cache_path}/T_{barcode}_{board_no}_{time_file}.jpg"
                    cropped_image.save(src_board_img)

                    # 上传到ftp
                    ftp_client.upload_file(src_board_img, f"T_{barcode}.jpg")

                    os.remove(src_board_img)

                b_board_box = b_board_box_info.get(board_no, ())
                if b_board_box:
                    self.log.info(f"开始保存拼板[{board_no}]的B面图片...")
                    image = Image.open(b_src_img)
                    cropped_image = image.crop(b_board_box)

                    src_board_img = f"{cache_path}/B_{barcode}_{board_no}_{time_file}.jpg"
                    cropped_image.save(src_board_img)

                    # 上传到ftp
                    ftp_client.upload_file(src_board_img, f"B_{barcode}.jpg")

                    os.remove(src_board_img)

            else:
                # 单面
                t_src_img = pcb_src_img_list[0]
                # t_dst_img = f"{pcb_sn}_T.jpg"
                # ftp_client.upload_file(t_src_img, t_dst_img)

                board_box = t_board_box_info.get(board_no, ())
                if board_box:
                    self.log.info(f"开始保存拼板[{board_no}]的图片...{t_src_img=}")
                    image = Image.open(t_src_img)
                    cropped_image = image.crop(board_box)

                    src_board_img = f"{cache_path}/{barcode}_{board_no}_{time_file}.jpg"
                    cropped_image.save(src_board_img)

                    # 上传到ftp
                    ftp_client.upload_file(src_board_img, f"{barcode}.jpg")

                    os.remove(src_board_img)

            comp_rows_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                comp_json = comp_entity.to_json_data()

                # 2. 拷贝器件图片到ftp
                comp_src_img = comp_entity.image_path
                if comp_src_img:
                    suffix = xutil.FileUtil.get_file_suffix(comp_src_img)
                    comp_dst_filename = f"{barcode}-{comp_tag}{suffix}"
                    ftp_client.upload_file(comp_src_img, comp_dst_filename)

                    comp_ftp_filename = f"{ftp_full_path}/{comp_dst_filename}"
                else:
                    comp_ftp_filename = ""

                comp_json["image_path"] = comp_ftp_filename
                comp_rows_str += csv_comp_template.format(**comp_json)

            board_json = board_entity.to_json_data()

            board_json["pcb_project_name"] = project_name
            board_json["pcb_test_time"] = pcb_entity.get_cycle_time()
            board_json["pcb_repair_user"] = pcb_entity.repair_user
            board_json["pcb_sn"] = pcb_entity.pcb_barcode
            board_json["pcb_final_result"] = pcb_entity.get_final_result()
            board_json["CompData"] = comp_rows_str

            board_content = csv_board_template.format(**board_json)
            csv_dst_name = f"{project_name}-{time_date}-{barcode}.csv"

            # 1. 上传csv到ftp
            ftp_client.upload_content(csv_dst_name, board_content)

        if not pcb_sn:
            pcb_sn = time_file

        # 3. 上传整板图
        pcb_ftp_path = f"{ftp_path}/{project_name}/{time_date}/{pcb_sn}"

        ftp_client.cd_or_mkdir(pcb_ftp_path)
        self.log.info(f"ftp路径已切换至：{pcb_ftp_path}")

        if len(pcb_src_img_list) >= 2:
            # 上下面
            t_src_img = pcb_src_img_list[0]
            b_src_img = pcb_src_img_list[1]

            t_dst_img = f"{pcb_sn}_T.jpg"
            b_dst_img = f"{pcb_sn}_B.jpg"

            ftp_client.upload_file(t_src_img, t_dst_img)
            ftp_client.upload_file(b_src_img, b_dst_img)
        else:
            # 单面
            t_src_img = pcb_src_img_list[0]
            t_dst_img = f"{pcb_sn}_T_PCB.jpg"
            ftp_client.upload_file(t_src_img, t_dst_img)

        self.log.info(f"整板图上传成功！")

        # 4. 上传图片路径
        req_param = {
            "iSN": pcb_sn,
            "iClass": "波峰焊前AOI",
            "iSubClass1": pcb_ftp_path,
            "iSubClass2": "",
            "iSubClass3": "",
            "iMaxValue": "",
            "iMinValue": "",
            "iActualValue": "",
            "iValue1": "",
            "iValue2": "",
            "iValue3": "",
            "iTestResult": "",
            "iResCode": res_code,
        }
        data_url = f"{api_url}/SetTestDetail"
        xrequest.RequestUtil.post_form(data_url, req_param, to_json=False)

        ftp_client.close()

        return self.x_response()
