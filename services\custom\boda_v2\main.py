# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/15 下午3:31
# Author     ：sch
# version    ：python 3.8
# Description：波达v2
"""
import os
from typing import Any

from common import xutil, xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "boda_v2 release v1.0.0.10",
        "device": "40x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-15 17:13  条码校验，上传数据
date: 2023-06-19 15:22  兼容现场返回的接口参数
date: 2023-06-26 15:12  保存图片到本地和FTP服务器
date: 2023-07-03 18:17  整板NG才需要保存整板图
date: 2023-09-05 16:48  条码校验时，也要传板式名
date: 2023-09-28 10:01  新增请求参数
""",
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        }
    }

    combo = {
        "is_save_to_ftp": {
            "ui_name": "图片保存至FTP服务器",
            "item": ["是", "否"],
            "value": "是"
        },
        "is_save_to_local": {
            "ui_name": "图片保存至本地",
            "item": ["是", "否"],
            "value": "是"
        }
    }

    path = {
        "pcb_save_path": {
            "ui_name": "整板图保存路径",
            "value": ""
        },
        "comp_save_path": {
            "ui_name": "NG器件图保存路径",
            "value": ""
        },
    }

    other_form = {
        "log_number": {
            "ui_name": "请求参数输出(字符)",
            "value": "50000"
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "*************"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "sch_password"
        },
        "ftp_path_pcb": {
            "ui_name": "FTP 整板图路径",
            "value": "/BOARD_NG"
        },
        "ftp_path_comp": {
            "ui_name": "FTP NG器件图路径",
            "value": "/LIST_NG"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        is_save_to_ftp = data_vo.get_value_by_cons_key("is_save_to_ftp")
        is_save_to_local = data_vo.get_value_by_cons_key("is_save_to_local")
        pcb_save_path = data_vo.get_value_by_cons_key("pcb_save_path")
        comp_save_path = data_vo.get_value_by_cons_key("comp_save_path")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")

        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path_pcb = data_vo.get_value_by_cons_key("ftp_path_pcb")
        ftp_path_comp = data_vo.get_value_by_cons_key("ftp_path_comp")

        if is_save_to_ftp == "是":

            try:
                ftp_port = int(ftp_port)
            except Exception as err:
                return self.x_response("false", f"FTP Port必须为数字！error： {err}")

            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()

        else:
            ftp_client = None

        pcb_entity = data_vo.pcb_entity
        time_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        time_date = time_now[:8]

        log_number = data_vo.get_value_by_cons_key("log_number")
        try:
            log_number = int(log_number)
        except Exception as err:
            return self.x_response("false", f"请求参数输出(字符)必须为数字！error:{err}")

        self.log.info(pcb_entity)

        board_data = []

        comp_number = 0
        comp_robot_ng_number = 0
        comp_user_ng_number = 0

        board_robot_ng_count = 0
        board_repair_ng_count = 0

        pcb_sn = pcb_entity.pcb_barcode

        is_cd_ftp_dir = False

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not board_entity.robot_result:
                board_robot_ng_count += 1

            if not board_entity.repair_result:
                board_repair_ng_count += 1

            comp_number += board_entity.comp_total_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():

                if not comp_entity.repair_result:
                    comp_tag = comp_entity.designator

                    if comp_entity.image_path:
                        # 保存器件图
                        # 1. 保存到ftp
                        if is_save_to_ftp == "是":
                            ftp_comp_full_path = f"{ftp_path_comp}/{time_date}"
                            if not is_cd_ftp_dir:
                                ftp_client.cd_or_mkdir(ftp_comp_full_path)

                            ftp_client.upload_file(comp_entity.image_path, f"{barcode}_{time_now}_{comp_tag}.png")

                            ftp_dst_file = f"{ftp_comp_full_path}/{barcode}_{time_now}_{comp_tag}.png"
                        else:
                            ftp_dst_file = ""

                        # 2. 保存到本地
                        comp_save_path1 = f"{comp_save_path}/{time_date}"
                        dst_file = f"{comp_save_path1}/{barcode}_{time_now}_{comp_tag}.png"

                        if is_save_to_local == "是":
                            if not os.path.exists(comp_save_path1):
                                os.makedirs(comp_save_path1)

                            xutil.FileUtil.copy_file(comp_entity.image_path, dst_file)

                    else:
                        ftp_dst_file = ""

                    comp_data.append({
                        "comp_designator": comp_tag,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_ng_GIF": ftp_dst_file,
                    })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_entity.board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        if not pcb_sn:
            pcb_sn = time_now

        pcb_t_image = pcb_entity.get_pcb_t_image()

        # 20230621改为ftp上传
        # 上传整板图
        # 1. 保存到ftp
        pcb_full_path = f"{ftp_path_pcb}/{time_date}"
        if is_save_to_ftp == "是" and pcb_t_image:
            if pcb_entity.final_result != "NG":
                self.log.info(f"整板结果：{pcb_entity.final_result}  无需上传整板图")
                pcb_dst_file = ""
            else:
                ftp_client.cd_or_mkdir(pcb_full_path)

                pcb_dst_file = f"{pcb_sn}_{time_now}.jpg"
                ftp_client.upload_file(pcb_t_image, pcb_dst_file)

                pcb_dst_file = f"{pcb_full_path}/{pcb_sn}_{time_now}.jpg"
        else:
            pcb_dst_file = ""

        # 2. 保存到本地
        if is_save_to_local == "是" and pcb_t_image:
            pcb_save_path = f"{pcb_save_path}/{time_date}"

            if not os.path.exists(pcb_save_path):
                os.makedirs(pcb_save_path)

            if pcb_entity.final_result != "NG":
                self.log.info(f"整板结果：{pcb_entity.final_result}  无需保存整板图")
                pcb_dst_file = ""
            else:
                xutil.FileUtil.copy_file(pcb_t_image, f"{pcb_save_path}/{pcb_sn}_{time_now}.jpg")

        misreport_count = comp_robot_ng_number - comp_user_ng_number

        pcb_data = {
            "check_type": "2",
            "pcb_comp_robot_ng_GIF": pcb_dst_file,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_repair_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,

            # 20240927新增 -----------
            "pcb_batch_no": pcb_entity.order_id,  # 批次信息
            "pcb_misreport_number": misreport_count,  # 误报元件数
            "pcb_misreport_rate": round((misreport_count / comp_number) / comp_number, 2),  # 元件误报率
            "pcb_final_result_user": pcb_entity.repair_user,  # 复判人
            # 20240927新增 -----------

            "board_data": board_data
        }

        api_url = data_vo.get_value_by_cons_key("api_url")
        ret = xrequest.RequestUtil.post_json(api_url, pcb_data, log_number=log_number)
        if not ret.get("result"):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        sn_list = other_vo.list_sn()

        log_number = other_vo.get_value_by_cons_key("log_number")
        try:
            log_number = int(log_number)
        except Exception as err:
            return self.x_response("false", f"请求参数输出(字符)必须为数字！error:{err}")

        board_data = []

        for ix, sn in enumerate(sn_list):
            ix += 1
            board_data.append(
                {
                    "board_no": str(ix),
                    "board_sn": sn,
                    "board_user_result": "",
                    "board_final_result": "",
                    "comp_data": []
                }
            )

        check_param = {
            "pcb_sn": "",
            "check_type": "1",
            "pcb_track_line": 1,
            "pcb_test_time": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": other_vo.get_project_name(),
            "pcb_user_result": "",
            "pcb_robot_result": "",
            "pcb_final_result": "N",
            "pcb_repair_user": "",
            "pcb_board_number": 0,
            "pcb_board_user_ng_number": 0,
            "pcb_board_robot_ng_number": 0,
            "pcb_comp_number": 0,
            "pcb_comp_user_ng_number": 0,
            "pcb_comp_robot_ng_number": 0,
            "pcb_comp_robot_ng_GIF": "",
            "board_data": board_data
        }

        ret = xrequest.RequestUtil.post_json(api_url, check_param, log_number=log_number)
        if not ret.get("result"):
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('msg')}")

        return self.x_response()
