# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/27 上午9:36
# Author     ：sch
# version    ：python 3.8
# Description：重庆盟讯
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "chongqingmengxun release v1.0.0.1",
        "device": "501,401B",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-27 10:03  上传数据
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://************:9213/integrate/smtdata/saveAOIPCBResult"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")

        pcb_entity = data_vo.pcb_entity

        pcb_data = pcb_entity.standard_to_json(has_board_robot_result=False, has_comp_path=False)

        ret = xrequest.RequestUtil.post_json(api_url, pcb_data)

        return self.x_response()
