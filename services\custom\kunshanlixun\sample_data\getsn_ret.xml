<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <getsnResponse xmlns="MesWebService">
      <getsnResult>{
	"PanelBarCode": "AP303STF1119D00003",
	"Result": "OK",
	"Boards": [{
		"Blocknumber": "001",
		"BoardBarCode": "AP303STF1119D00003001",
		"SKIP": "N"
	}, {
		"Blocknumber": "002",
		"BoardBarCode": "AP303STF1119D00003002",
		"SKIP": "N"
	}],
	"MESSAGE": ""
}</getsnResult>
    </getsnResponse>
  </soap:Body>
</soap:Envelope>