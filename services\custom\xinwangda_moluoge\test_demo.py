# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2023/12/21 下午3:23
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

if __name__ == '__main__':
    # 解析report.xml
    report_file = f"/home/<USER>/aoi/run/results/333.001/20231204/T_20231204095657483_1_NG/report.xml"
    root = xutil.XmlUtil.get_xml_root_by_file(report_file)
    boards = root.find("boards").findall("board")
    ret_data = {}
    for board in boards:
        board_id = board.attrib.get("id")
        board_geometry = board.find('geometry')

        board_width = board_geometry.find('width').text
        board_height = board_geometry.find('height').text
        board_angle = board_geometry.find('angle').text

        ret_data[board_id] = {
            'width': board_width,
            'height': board_height,
            'angle': board_angle
        }

