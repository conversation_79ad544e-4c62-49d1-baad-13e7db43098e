# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/13 下午3:03
# Author     ：sch
# version    ：python 3.8
# Description：慧眼视讯
"""
import json
import os
import shutil
import traceback
from typing import Any

from common import xutil, xrequest, xcons, xenum
from common.xutil import log
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

global_data = {}

cache_path = f"{os.getcwd()}/cache_data"


class Engine(ErrorMapEngine):
    version = {
        "title": "huiyanshixun release v1.0.0.5",
        "device": "401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-13 16:55  获取token，上传附件，上传数据
date: 2023-12-15 16:43  获取token增加type参数
date: 2023-12-19 11:18  修改上传文件接口
date: 2023-12-20 09:36  bugfix
date: 2024-03-06 10:37  增加配置项
""", }

    form = {
        "tenant_id": {
            "ui_name": "租户ID",
            "value": "",
        },
        "username": {
            "ui_name": "账号",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "order_no": {
            "ui_name": "工单号(1轨)",
            "value": "",
        },
        "station_code": {
            "ui_name": "工位编码(1轨)",
            "value": "",
        },
        "route_code": {
            "ui_name": "工序编码(1轨)",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员(1轨)",
            "value": "",
        },
        "machine_code": {
            "ui_name": "设备编码(1轨)",
            "value": "",
        },
        "order_no2": {
            "ui_name": "工单号(2轨)",
            "value": "",
        },
        "station_code2": {
            "ui_name": "工位编码(2轨)",
            "value": "",
        },
        "route_code2": {
            "ui_name": "工序编码(2轨)",
            "value": "",
        },
        "operator2": {
            "ui_name": "操作员(2轨)",
            "value": "",
        },
        "machine_code2": {
            "ui_name": "设备编码(2轨)",
            "value": "",
        },
    }

    combo = {
        "is_input_sn": {
            "ui_name": "是否投入产品",
            "item": ["true", "false"],
            "value": "false"
        },
        "upload_mes1": {
            "ui_name": "上传数据到Mes(1轨)",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
        "upload_mes2": {
            "ui_name": "上传数据到Mes(2轨)",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
    }

    other_form = {
        "api_url1": {
            "ui_name": "获取Token接口URL",
            "value": "",
        },
        "api_url2": {
            "ui_name": "附件上传接口URL",
            "value": "",
        },
        "api_url3": {
            "ui_name": "成品测试接口URL",
            "value": "",
        },
        "authorization": {
            "ui_name": "Authorization值",
            "value": "Basic c3dvcmQ6c3dvcmRfc2VjcmV0"
        },
        "content_type": {
            "ui_name": "Content-Type",
            "value": "application/x-www-form-urlencoded"
        },
        "tenant_id_header": {
            "ui_name": "请求头Tenant-Id",
            "value": ""
        },
    }

    button = {
        "get_token": {
            "ui_name": "获取Token",
        }
    }

    def __init__(self):
        self.common_config["sendmes_setting1"] = xenum.SendMesSetting1.SaveInspectNG

        xutil.FileUtil.ensure_dir_exist(cache_path)

        file_list = os.listdir(cache_path)

        log.info(f"缓存的压缩文件数量：{len(file_list)}")

        for filename in file_list:
            file_path = os.path.join(cache_path, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)  # 如果是文件，则删除它
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # 如果是文件夹，则递归删除整个文件夹

            log.logger.info(f"文件：{file_path} 已删除")

        if len(file_list) != 0:
            log.logger.info(f"缓存文件已删除完毕！")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # tenant_id = data_dao.get_value_by_cons_key("tenant_id")
        # username = data_dao.get_value_by_cons_key("username")
        # password = data_dao.get_value_by_cons_key("password")
        is_input_sn = data_vo.get_value_by_cons_key("is_input_sn")
        api_url2 = data_vo.get_value_by_cons_key("api_url2")
        api_url3 = data_vo.get_value_by_cons_key("api_url3")
        authorization = data_vo.get_value_by_cons_key("authorization")

        upload_mes1 = data_vo.get_value_by_cons_key("upload_mes1")
        upload_mes2 = data_vo.get_value_by_cons_key("upload_mes2")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.track_index
        log.info(f"轨道：{track_index}")

        if track_index == 1 and upload_mes1 == 'No':
            log.warning(f"上传Mes(1轨)：No, 本次不上传数据到Mes！")
            return self.x_response()

        if track_index == 2 and upload_mes2 == 'No':
            log.warning(f"上传Mes(2轨)：No, 本次不上传数据到Mes！")
            return self.x_response()

        if track_index == 2:
            order_no = data_vo.get_value_by_cons_key("order_no2")
            station_code = data_vo.get_value_by_cons_key("station_code2")
            route_code = data_vo.get_value_by_cons_key("route_code2")
            operator = data_vo.get_value_by_cons_key("operator2")
            machine_code = data_vo.get_value_by_cons_key("machine_code2")
        else:
            order_no = data_vo.get_value_by_cons_key("order_no")
            station_code = data_vo.get_value_by_cons_key("station_code")
            route_code = data_vo.get_value_by_cons_key("route_code")
            operator = data_vo.get_value_by_cons_key("operator")
            machine_code = data_vo.get_value_by_cons_key("machine_code")

        access_token = global_data.get('access_token')
        if not access_token:
            return self.x_response("false", f"未获取到Token！")

        headers = {
            "Authorization": authorization,
            "Blade-Auth": f"bearer {access_token}"
        }

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data = []
            error_code = []

            board_data_path = f"{cache_path}/{barcode}_{time_file}"
            xutil.FileUtil.ensure_dir_exist(board_data_path)

            ng_count = 0
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                robot_ng_code = comp_entity.robot_ng_code

                comp_data.append({
                    "器件位号": comp_tag,
                    "器件料号": comp_entity.part,
                    "器件封装": comp_entity.package,
                    "器件类型": comp_entity.type,
                    "器件机器检测不良代码": robot_ng_code,
                    "器件机器检测不良指述": comp_entity.robot_ng_str,
                    "器件人工复判不良代码": comp_entity.repair_ng_code,
                    "器件人工复判不良描述": comp_entity.repair_ng_str,
                    "机器检测结果": comp_entity.get_final_result("PASS", "FAIL", "FAIL"),
                    "人工复判结果": comp_entity.get_final_result("PASS", "PASS", "FAIL")
                })

                if comp_entity.is_repair_ng():
                    error_code.append(comp_entity.repair_ng_code)

                comp_src_img = comp_entity.image_path

                if comp_src_img:
                    ng_count += 1
                    xutil.FileUtil.copy_file(
                        comp_src_img,
                        f"{board_data_path}/{barcode}_{comp_tag}_{robot_ng_code}",
                        is_auto_add_suffix=True
                    )

            ret_link = ""
            ret_origin_name = ""

            if ng_count > 0:
                # 上传NG器件图
                zip_file = board_data_path

                shutil.make_archive(zip_file, 'zip', zip_file)
                log.logger.info(f"文件夹：{zip_file} 已压缩至 {f'{zip_file}.zip'}")

                log.logger.info(f"NG器件图：{ng_count}张")

                zip_src_file = f"{zip_file}.zip"
                log.logger.info(f"开始上传文件：{zip_src_file}")

                with open(zip_src_file, "rb") as f:
                    try:
                        ret = xrequest.RequestUtil.post_form(api_url2,
                                                             {},
                                                             headers=headers,
                                                             files={"file": f})
                        if str(ret.get("code")) != "200":
                            log.warning(f"mes接口异常，上传压缩包失败，error：{ret.get('msg')}")

                        log.info(f"上传文件成功！！！")
                        ret_link = ret.get("data", {}).get("link", '')
                        ret_origin_name = ret.get('data', {}).get('originalName', '')

                    except Exception as err:
                        self.log.warning(f"上传压缩包失败，error：{err}")
                        self.log.warning(traceback.format_exc())

                # 删除缓存文件
                os.remove(zip_src_file)

            if os.path.exists(board_data_path):
                shutil.rmtree(board_data_path)

            attachments = [{
                "link": ret_link,
                "originName": ret_origin_name
            }]

            failure_code = ",".join(error_code)

            board_param = {
                "workOrderNo": order_no,
                "stationCode": station_code,
                "routeCode": route_code,
                "isInputSn": is_input_sn,
                "sequenceNumber": barcode,
                "eventResult": board_entity.get_repair_result("PASS", "FAIL"),
                "failureCode": failure_code,
                "transBy": operator,
                "machineTestData": {
                    "machineCode": machine_code,
                    "attachments": json.dumps(attachments, ensure_ascii=False),
                    "titles": {
                        "器件位号": "1",
                        "器件料号": "2",
                        "器件封装": "3",
                        "器件类型": "4",
                        "器件机器检测不良代码": "5",
                        "器件机器检测不良描述": "6",
                        "器件人工复判不良代码": "7",
                        "器件人工复判不良描述": "8",
                        "机器检测结果": "9",
                        "人工复判结果": "10"
                    },
                    "results": comp_data
                }
            }

            ret = xrequest.RequestUtil.post_json(api_url3, board_param, headers=headers)
            if str(ret.get("code")) != "200":
                error_msg = f"mes接口异常，上传数据失败，error：{ret.get('msg')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url1 = btn_vo.get_value_by_cons_key("api_url1")
        tenant_id = btn_vo.get_value_by_cons_key("tenant_id")
        username = btn_vo.get_value_by_cons_key("username")
        password = btn_vo.get_value_by_cons_key("password")
        authorization = btn_vo.get_value_by_cons_key("authorization")
        content_type = btn_vo.get_value_by_cons_key("content_type")
        tenant_id_header = btn_vo.get_value_by_cons_key("tenant_id_header")

        if btn_vo.get_btn_key() == "get_token":
            req_param = {
                "grant_type": "password",
                "username": username,
                "password": xutil.OtherUtil.get_md5_sign(password),
                "scope": "all",
                "tenantId": tenant_id,
                "type": "account"
            }

            req_headers = {
                "Authorization": authorization,
                "Tenant-Id": tenant_id_header,
                "Content-Type": content_type
            }

            ret = xrequest.RequestUtil.post_form(api_url1, req_param, headers=req_headers)
            access_token = ret.get('access_token')
            if not access_token:
                return self.x_response("false", f"mes接口异常，未获取到Token！")

            global_data["access_token"] = access_token

        return self.x_response()
