# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/12 上午10:18
# Author     ：sch
# version    ：python 3.8
# Description：盟讯
"""
from typing import Any

from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "mengxun release v1.0.0.1",
        "device": "20x,30x,40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-12 11:13  init
""", }

    # ---------------以下配置，维修站已硬编码，不可以改变配置项的key值-------------
    other_form = {
        "ai_api_url": {
            "ui_name": "AI_接口URL",
            "value": "http://127.0.0.1:8081"
        },
        "ai_line": {
            "ui_name": "AI_线别",
            "value": "SMTLine"
        },
        "ai_station": {
            "ui_name": "AI_站别",
            "value": "SMTStation"
        },
    }

    other_combo = {
        "ai_get_ai_result_timeout": {
            "ui_name": "AI_获取AI结果超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "30", "60", "100", "300"],
            "value": "30"
        },
        "ai_is_get_ai_result": {
            "ui_name": "AI_从AI获取复判结果",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
    }
    # ---------------以下配置，维修站已硬编码，不可以改变配置项的key值-------------

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        return self.x_response()
