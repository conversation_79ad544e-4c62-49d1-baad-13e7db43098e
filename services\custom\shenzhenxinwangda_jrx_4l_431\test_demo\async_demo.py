import os
import time
import threading
from queue import Queue
import logging
import json
from PIL import Image, ImageDraw, ImageFont
import boto3
from botocore.client import Config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


class ThreadPool:
    def __init__(self, max_workers=5):
        self.max_workers = max_workers
        self.task_queue = Queue()
        self.workers = []
        self.results = {}
        self.lock = threading.Lock()
        self.completed_event = threading.Event()
        self.active_tasks = 0
        self._init_workers()

    def _init_workers(self):
        """初始化工作线程池"""
        for _ in range(self.max_workers):
            worker = threading.Thread(target=self._worker, daemon=True)
            worker.start()
            self.workers.append(worker)

    def _worker(self):
        """工作线程的主循环"""
        while True:
            try:
                task_id, func, args, kwargs = self.task_queue.get()
                with self.lock:
                    self.active_tasks += 1
                try:
                    for attempt in range(3):  # 最多重试3次
                        try:
                            result = func(*args, **kwargs)
                            with self.lock:
                                self.results[task_id] = {
                                    'status': 'success',
                                    'result': result
                                }
                            break
                        except Exception as e:
                            if attempt == 2:  # 最后一次尝试失败
                                raise
                            time.sleep(2 ** attempt)  # 指数退避
                except Exception as e:
                    with self.lock:
                        self.results[task_id] = {
                            'status': 'error',
                            'error': str(e)
                        }
                finally:
                    self.task_queue.task_done()
                    with self.lock:
                        self.active_tasks -= 1
                        if self.active_tasks == 0 and self.task_queue.empty():
                            self.completed_event.set()
            except Exception as e:
                log.error(f"Worker thread error: {e}")
                continue

    def add_task(self, task_id, func, *args, **kwargs):
        """添加任务到队列"""
        self.task_queue.put((task_id, func, args, kwargs))

    def wait_completion(self, timeout=20):
        """等待所有任务完成，支持超时"""
        if self.completed_event.wait(timeout=timeout):
            return self.results
        else:
            log.warning(f"任务执行超过{timeout}秒超时限制")
            return {task_id: {'status': 'error', 'error': f'Upload timeout ({timeout}s)'}
                    for task_id in self.results.keys()}

    def clear_results(self):
        """清除结果缓存"""
        with self.lock:
            self.results.clear()


def upload_image_to_s3(endpoint_url, aws_access_key_id, aws_secret_access_key,
                       file_path, bucket_name, object_key, region_name):
    """上传图片到S3/Minio服务器"""
    try:
        s3_client = boto3.client(
            's3',
            endpoint_url=f'http://{endpoint_url}',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name,
            config=Config(signature_version='s3v4')
        )

        # 上传文件
        s3_client.upload_file(file_path, bucket_name, object_key)

        # 生成URL
        url = f"http://{endpoint_url}/{bucket_name}/{object_key}"
        log.info(f"文件上传成功: {url}")
        return url

    except Exception as e:
        log.error(f"上传失败: {str(e)}")
        raise


def create_test_board_image(board_no, has_ng=False):
    """创建测试用的拼版图"""
    width = 800
    height = 600
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((width // 2, height // 2), f'Board {board_no}', fill='black')

    filename = f'test_board_{board_no}_rect.jpg'
    img.save(filename)
    return filename


def test_board_upload():
    """测试拼版图上传"""
    # 初始化线程池
    thread_pool = ThreadPool(max_workers=5)

    # Minio配置
    aws_endpoint_url = "127.0.0.1:9000"
    aws_access_key_id = "minioadmin"
    aws_secret_access_key = "minioadmin"
    bucket_name = "test-bucket"
    region_name = "us-east-1"

    # 模拟数据
    pcb_sn = "TEST_PCB_001"
    date1 = time.strftime("%Y%m%d")
    date2 = time.strftime("%H%M%S")

    # 创建S3客户端确保bucket存在
    s3_client = boto3.client(
        's3',
        endpoint_url=f'http://{aws_endpoint_url}',
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=region_name,
        config=Config(signature_version='s3v4')
    )

    try:
        s3_client.create_bucket(Bucket=bucket_name)
    except:
        pass

    board_data_list = []
    upload_tasks = []
    board_image_info = {}

    # 模拟4个拼版的数据
    for board_no in range(1, 5):
        # 创建测试图片
        board_image_path = create_test_board_image(board_no, has_ng=(board_no % 2 == 0))
        board_result = "NG" if board_no % 2 == 0 else "OK"

        try:
            # 创建带白板的拼版图
            img = Image.open(board_image_path)
            width, height = img.size
            new_width = width + 1500
            new_img = Image.new('RGB', (new_width, height), 'white')
            new_img.paste(img, (0, 0))

            # 添加信息到白板
            draw = ImageDraw.Draw(new_img)
            font = ImageFont.load_default()

            # 添加标题
            title = f"Board {board_no} Components:"
            draw.text((width + 10, 50), title, fill='black', font=font)

            # 保存新图片
            suffix = f"_board{board_no}_{'ng' if board_result == 'NG' else 'ok'}_info.jpg"
            processed_filename = os.path.splitext(board_image_path)[0] + suffix
            new_img.save(processed_filename, quality=95)
            log.info(f"已生成带信息的拼版图: {processed_filename}")

            # 构建目标文件名
            dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{board_no}_{board_result}_{date1}{date2}.jpg"

            # 准备上传任务
            upload_task = {
                "endpoint_url": aws_endpoint_url,
                "aws_access_key_id": aws_access_key_id,
                "aws_secret_access_key": aws_secret_access_key,
                "file_path": processed_filename,
                "bucket_name": bucket_name,
                "object_key": dst_filename,
                "region_name": region_name,
            }

            # 记录文件信息
            board_image_info[dst_filename] = {
                'board_no': board_no,
                'processed_filename': processed_filename,
                'board_image_path': board_image_path
            }

            # 添加到线程池
            task_id = f"board_{board_no}"
            thread_pool.add_task(task_id, upload_image_to_s3, **upload_task)
            upload_tasks.append({
                "task_id": task_id,
                "task": upload_task
            })

            # 准备board_data
            board_data = {
                "smallPanelId": str(board_no),
                "smallPanelSN": f"PIN_BOARD_{board_no}",
                "smallPanelTestResult": board_result,
                "smallPanelUrlList": []
            }
            board_data_list.append(board_data)

        except Exception as err:
            log.error(f"处理拼版{board_no}图片失败: {str(err)}")
            continue

    # 等待所有上传任务完成
    start_time = time.time()
    try:
        log.info("等待所有上传任务完成...")
        results = thread_pool.wait_completion(timeout=20)

        # 处理上传结果
        for task in upload_tasks:
            try:
                info = board_image_info[task['task']['object_key']]
                board_no = info['board_no']
                result = results.get(task['task_id'])

                if result and result['status'] == 'success':
                    panel_url = result['result']
                    if panel_url:
                        log.info(f"拼版{board_no}图片上传成功，URL为: {panel_url}")

                        # 更新board_data中的URL
                        for board_data in board_data_list:
                            if board_data["smallPanelId"] == str(board_no):
                                board_data["smallPanelUrlList"].append({"url": panel_url})
                                break

                        # 清理文件
                        try:
                            os.remove(info['processed_filename'])
                            os.remove(info['board_image_path'])
                            log.info(f"已删除相关图片文件")
                        except Exception as del_err:
                            log.warning(f"删除文件失败: {del_err}")
                    else:
                        log.error(f"拼版{board_no}图片上传失败: 未获取到URL")
                else:
                    err_msg = result.get('error', '未知错误') if result else '上传超时'
                    log.error(f"拼版{board_no}图片上传失败: {err_msg}")

            except Exception as err:
                log.error(f"处理上传结果出错: {err}")

    finally:
        thread_pool.clear_results()

    end_time = time.time()
    total_time = end_time - start_time

    # 打印最终结果
    log.info("\n=== 上传测试结果 ===")
    log.info(f"总耗时: {total_time:.2f} 秒")
    log.info(f"是否异步上传: {'是' if total_time < len(upload_tasks) * 2 else '否'}")
    log.info("\nboard_data_list 最终内容:")
    log.info(json.dumps(board_data_list, indent=2, ensure_ascii=False))


if __name__ == '__main__':
    test_board_upload()