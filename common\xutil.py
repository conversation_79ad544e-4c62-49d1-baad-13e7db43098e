# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : utils.py
# Time       ：2022/12/29 上午11:15
# Author     ：sch
# version    ：python 3.8
# Description：
"""

import base64
import errno
import hashlib
import inspect
import json
import logging
import os
import subprocess
import xml.dom.minidom
import re
import shutil
import smtplib
import threading
import time
import traceback
import uuid
from collections import OrderedDict
from datetime import datetime, timedelta
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from logging import handlers
from typing import List, Tuple, Any
from xml.etree import ElementTree
from xml.etree import cElementTree

from common import xconfig, xcons


def time_cost(func):
    """
    计算函数消耗的装饰器
    :param func:
    :return:
    """

    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        log.info("{}执行时间: {:.4f} 秒".format(func.__name__, execution_time))
        return result

    return wrapper


class Logger(object):
    level_relations = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'critical': logging.CRITICAL
    }  # 日志级别关系映射

    def __init__(
            self,
            filename,
            level='debug',
            log_type="file_rotate",
            back_count=50,
            fmt='%(asctime)s - [line:%(lineno)d] - %(levelname)s: %(message)s',
            when="MIDNIGHT",
    ):
        """
        实例化 TimedRotatingFileHandler
        interval是时间间隔，backupCount是备份文件的个数，如果超过这个个数，就会自动删除，when是间隔的时间单位，单位有以下几种：
        S 秒
        M 分
        H 小时、
        D 天、
        W 每星期（interval==0时代表星期一）
        MIDNIGHT 每天凌晨

        @param: log_type  (file_rotate 按文件大小回滚文件) (date_rotate 按日期大小回滚文件)
        """
        # filename = str("{a}/.aoi/log/{b}").format(a=os.path.expanduser('~'), b=filename)
        # filename = xconfig.pymes_log_file
        # log_path = xconfig.log_dir

        # log_filename = f"{log_path}/{filename}"
        # filename = xconfig.pymes_log_file
        # print(filename)

        self.logger = logging.getLogger(filename)
        format_str = logging.Formatter(fmt)  # 设置日志格式
        self.logger.setLevel(self.level_relations.get(level))  # 设置日志级别
        std_handler = logging.StreamHandler()  # 往屏幕上输出
        std_handler.setFormatter(format_str)  # 设置屏幕上显示的格式

        if log_type == "date_rotate":
            # 按日期回滚
            file_handler = handlers.TimedRotatingFileHandler(filename=filename,
                                                             when=when,  # 每天凌晨
                                                             backupCount=back_count,
                                                             encoding='utf-8')
        else:
            # 按文件大小回滚  （default）
            file_handler = handlers.RotatingFileHandler(filename=filename,
                                                        maxBytes=1024 * 1024 * 5,  # 5M大小
                                                        backupCount=back_count,
                                                        encoding='utf-8')

        file_handler.setFormatter(format_str)  # 设置文件里写入的格式
        self.logger.addHandler(std_handler)  # 把对象加到logger里
        self.logger.addHandler(file_handler)

    def debug(self, msg):
        self.logger.debug(msg)
        logging.shutdown()

    def info(self, msg):
        self.logger.info(msg)
        logging.shutdown()

    def warning(self, msg):
        self.logger.warning(msg)
        logging.shutdown()

    def error(self, msg):
        self.logger.error(msg)
        logging.shutdown()


if not os.path.exists(xconfig.log_dir):
    os.makedirs(xconfig.log_dir)
    print(f"创建日志目录：{xconfig.log_dir}")

log = Logger(xconfig.pymes_log_file, level='debug')


def log_call(func):
    """
    # log_call 是一个装饰器函数，用于记录被装饰函数的调用信息
    :param func:
    :return:
    """

    def wrapper(*args, **kwargs):
        print("traceback.format_stack()-----------------start")
        for line in traceback.format_stack():
            print(line.strip())
        frame = inspect.stack()[1]  # 获取调用此wrapper的堆栈帧
        module = inspect.getmodule(frame[0])
        print(f"Function {func.__name__} called from module {module.__name__} at {frame.filename}:{frame.lineno}")
        print("traceback.format_stack()-----------------end")
        return func(*args, **kwargs)

    return wrapper


class FileUtil(object):
    """
    文件操作相关
    """
    template_dir = "template"

    @staticmethod
    def get_tmp_filename(file_path: str) -> str:
        """
        获取临时文件名
        """
        uid = OtherUtil.get_uuid4_str()[:5]
        time1 = DateUtil.get_datetime_now()
        return f"{file_path}.{time1}.{uid}.tmp"  # 创建唯一的临时文件名

    @classmethod
    def write_content_to_file(cls, file_path: str, content: str, encoding="utf-8", window_line=False):
        """
        将文件内容写入文件，文件不存在会创建
        如果文件已经存在则会【覆盖旧文件】
        :param file_path:
        :param content:
        :param encoding:
        :param window_line: window换行符
        :return:
        """
        with open(file_path, "w", encoding=encoding) as f:
            if window_line:
                content = content.replace("\n", "\r\n")

            f.write(content)

        log.info(f"文件保存成功！")
        cls.log_file_size(file_path)

    @classmethod
    def write_content_to_file_pro(
            cls, filepath: str,
            content: str,
            encoding="utf-8",
            window_line: bool = False,
            timeout: int = 3
    ) -> bool:
        """
        写入数据到网络磁盘推荐使用这个，有超时机制，并且会自动创建文件夹
        :param filepath:
        :param content:
        :param encoding:
        :param window_line:
        :param timeout:
        :return:
        """

        def write_to_file():
            base_path = os.path.dirname(filepath)
            cls.ensure_dir_exist(base_path)
            cls.write_content_to_file(filepath, content, encoding, window_line)

        write_thread = threading.Thread(target=write_to_file)
        write_thread.start()

        # 等待线程在指定时间内完成
        write_thread.join(timeout)

        if write_thread.is_alive():
            log.warning(f"写入文件失败，写入超时！")
            is_write_successful = False
        else:
            log.info(f"写入成功")
            is_write_successful = True

        return is_write_successful

    @staticmethod
    def append_content_to_file(file_path: str, content: str):
        """
        追加文本内容到指定文件，文件不存在会创建
        如果文件已经存在则会在【末尾追加写入】
        :param file_path: 文件路径
        :param content: 追加的文本内容
        :return:
        """
        with open(file_path, "a") as f:
            f.write(content)

        log.info(f"内容已追加至：{file_path}")

    @staticmethod
    def read_file(file_path) -> str:
        """
        读取一个文件
        :param file_path:
        :return:
        """
        with open(file_path, "r") as f:
            return f.read()

    @staticmethod
    def dump_json_to_file(file_path, json_data, fmt=True):
        """
        写入json数据到文件
        :param file_path:
        :param json_data:
        :param fmt: 是否进行格式化
        :return:
        """
        with open(file_path, "w") as f:

            if fmt:
                json.dump(json_data, f, ensure_ascii=False, indent=4)
            else:
                json.dump(json_data, f, ensure_ascii=False, separators=(",", ":"))

        log.info(f">>文件已保存成功！")
        FileUtil.log_file_size(file_path)

    @classmethod
    def dump_json_to_file_atomic(cls, file_path, json_data, fmt=True):
        """
        写入json数据到文件
        :param cls:
        :param file_path:
        :param json_data:
        :param fmt: 是否进行格式化
        :return:
        """
        temp_filepath = cls.get_tmp_filename(file_path)

        with open(temp_filepath, "w") as f:

            if fmt:
                json.dump(json_data, f, ensure_ascii=False, indent=4)
            else:
                json.dump(json_data, f, ensure_ascii=False, separators=(",", ":"))

        os.replace(temp_filepath, file_path)
        log.info(f">>atomic save ok!")
        FileUtil.log_file_size(file_path)

    @classmethod
    def dump_json_to_file_pro(cls, filepath: str, json_data, timeout: int = 5) -> bool:
        """
        写入到挂载目录时，推荐使用此方法
        :param filepath:
        :param json_data:
        :param timeout:
        :return:
        """

        def write_to_file():
            base_path = os.path.dirname(filepath)
            cls.ensure_dir_exist(base_path)
            cls.dump_json_to_file(filepath, json_data)

        write_thread = threading.Thread(target=write_to_file)
        write_thread.start()

        # 等待线程在指定时间内完成
        write_thread.join(timeout)

        if write_thread.is_alive():
            log.warning(f"写入文件失败，写入超时！")
            is_write_successful = False
        else:
            log.info(f"写入成功")
            is_write_successful = True

        return is_write_successful

    @staticmethod
    # @log_call
    def load_json_file(file_path: str) -> dict:
        """
        加载json文件
        """
        # print("load json file", file_path)
        # print('--------------')
        # print('Function called. Stack trace:')
        # for line in traceback.format_stack():
        #     print(line.strip())
        # print('--------------')
        with open(file_path, "r") as f:
            data = json.load(f)
        return data

    @staticmethod
    # @log_call
    def load_json_file2(file_path: str) -> list:
        """
        加载json文件,返回list
        """
        with open(file_path, "r") as f:
            data = json.load(f)
        return data

    @classmethod
    def load_config_file(cls) -> dict:
        """
        加载配置文件
        :return:
        """
        file = xconfig.mesconfig_file
        if not os.path.exists(file):
            return {}
        else:
            return cls.load_json_file(file)

    @classmethod
    def save_config_file(cls, config_data: dict):
        """
        保存配置文件
        :return:
        """
        file = xconfig.mesconfig_file
        cls.dump_json_to_file_atomic(file, config_data)

    @classmethod
    def copy_file(cls, src_file: str, dst_file: str, is_auto_add_suffix=False):
        """
        拷贝文件
        :param src_file: 源文件路径
        :param dst_file: 目标文件路径
        :param is_auto_add_suffix: 是否自动添加后缀
        :return: 自动添加的后缀
        """
        try:
            if is_auto_add_suffix:
                suffix = cls.get_file_suffix(src_file)
                dst_file = f"{dst_file}{suffix}"
            else:
                suffix = ""

            shutil.copy(src_file, dst_file)
            log.info(f"文件已从[{src_file}]拷贝到[{dst_file}]")

            return suffix

        except Exception as e:
            log.warning(f"error9001 {str(e)}")
            log.warning(traceback.format_exc())
            log.warning(f"文件拷贝出错, src: {src_file}  dst: {dst_file}")

    @classmethod
    def move_file(cls, src_file: str, dst_file: str, is_auto_add_suffix=False):
        """
        拷贝文件
        :param src_file: 源文件路径
        :param dst_file: 目标文件路径
        :param is_auto_add_suffix: 是否自动添加后缀
        :return: 自动添加的后缀
        """
        try:
            if is_auto_add_suffix:
                suffix = cls.get_file_suffix(src_file)
                dst_file = f"{dst_file}{suffix}"
            else:
                suffix = ""

            shutil.move(src_file, dst_file)
            log.info(f"文件已从[{src_file}]移动到[{dst_file}]")

            return suffix

        except Exception as e:
            log.warning(f"error9001 {str(e)}")
            log.warning(traceback.format_exc())
            log.warning(f"文件移动出错, src: {src_file}  dst: {dst_file}")

    @classmethod
    def load_template(cls, template_name):
        file_path = f"{cls.template_dir}/{template_name}"
        return cls.read_file(file_path)

    @staticmethod
    def ensure_dir_exist(root_path):
        """
        确保路径存在
        """
        if not os.path.exists(root_path):
            log.info(f"开始创建目录: {root_path}")
            os.makedirs(root_path)

    @staticmethod
    def get_file_suffix(filename: str) -> str:
        """
        获取文件名后缀
        :param filename:
        :return: .png
        """
        _, suffix = os.path.splitext(filename)
        return suffix

    @staticmethod
    def write_request_log(log_save_path: str, write_content):
        """
        保存接口请求日志
        :return:
        """
        if log_save_path:
            date_file = DateUtil.get_datetime_now(xcons.FMT_TIME_FILE0)
            time_file = DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
            filepath = f"{log_save_path}/{date_file}.log"

            with open(filepath, "a") as f:
                f.write(f"{time_file} -   {write_content}\n")
        else:
            log.warning(f"保存接口请求日志失败，还未选择日志保存路径！")

    @staticmethod
    def is_valid_json_file(file_path):
        """
        检查文件内容是否是有效的 JSON 格式。

        Args:
            file_path: 要检查的文件路径。

        Returns:
            True 如果文件内容是有效的 JSON，False 否则。
        """
        try:
            with open(file_path, 'r') as f:
                json.load(f)  # 尝试加载 JSON 数据
            return True  # 如果没有抛出异常，说明是有效的 JSON
        except FileNotFoundError:
            log.warning(f"文件未找到: {file_path}")  # 或者 raise  FileNotFoundError
            return False
        except json.JSONDecodeError as e:
            log.warning(f"JSON 解析错误: {e}")
            return False
        except Exception as e:
            log.warning(f"发生未知错误: {e}")
            return False

    @staticmethod
    def calculate_md5(file_path, block_size=65536):
        """
        计算指定文件的 MD5 哈希值。
        :param file_path: 文件的路径
        :param block_size: 每次读取的字节数，默认为 64KB
        :return: 文件的 MD5 哈希值（32 位十六进制字符串）
        """
        md5 = hashlib.md5()
        try:
            with open(file_path, 'rb') as f:
                while True:
                    data = f.read(block_size)
                    if not data:
                        break
                    md5.update(data)
            return md5.hexdigest()
        except FileNotFoundError:
            print(f"文件未找到: {file_path}")
            return None
        except Exception as e:
            print(f"计算 MD5 时出错: {e}")
            return None

    @staticmethod
    def log_file_size(filepath: str):
        """
        获取文件大小
        :param filepath:
        :return:
        """
        try:
            if not os.path.exists(filepath):
                log.warning(f"文件不存在: {filepath}")
                return 0

            file_size = os.path.getsize(filepath)

            if file_size >= 1073741824:  # 1 GB
                log.info(f"File size of {filepath}: {file_size / 1073741824:.2f} GB")
            elif file_size >= 1048576:  # 1 MB
                log.info(f"File size of {filepath}: {file_size / 1048576:.2f} MB")
            elif file_size >= 1024:  # 1 KB
                log.info(f"File size of {filepath}: {file_size / 1024:.2f} KB")
            else:
                log.info(f"File size of {filepath}: {file_size} bytes")

        except Exception as e:
            log.warning(f"获取文件大小出错: {str(e)}")
            return 0

    @staticmethod
    def get_df_output(timeout=3):
        """
        执行 `df -h` 命令并返回其输出。

        Returns:
            str: `df -h` 命令的输出。
        """
        try:
            result = subprocess.run(['df', '-h'], capture_output=True, text=True, check=True, timeout=timeout)
            return result.stdout
        except subprocess.TimeoutExpired as e:
            log.warning(f"Command timed out after {e.timeout} seconds")
            return None
        except subprocess.CalledProcessError as e:
            log.warning(f"Error executing df -h: {e}")
            return None

    @classmethod
    @time_cost
    def ismount_pro(cls, path: str, timeout=3):
        """
        检查路径是否挂载，支持超时（跨平台）
        :param path: 要检查的路径
        :param timeout: 超时时间（秒）
        :return: True（已挂载） / False（未挂载或超时）
        """
        result = None

        def check_mount():
            nonlocal result
            result = os.path.ismount(path)

        check_t = threading.Thread(target=check_mount)
        check_t.daemon = True
        check_t.start()
        check_t.join(timeout=timeout)  # 等待线程完成，最多 timeout 秒

        if check_t.is_alive():
            return False  # 超时
        return result  # 返回 ismount 结果

    @classmethod
    def is_share_disk_mounted(cls, filepath: str, timeout: int = 2):
        """
        检查指定文件所在的共享磁盘是否已挂载。
        Args:
            filepath (str): 要检查的文件路径。
            timeout (int): 超时时间，单位为秒。
        Returns:
            bool: 如果共享磁盘已挂载，则返回 True；否则返回 False。
        """
        df_output = cls.get_df_output(timeout)

        if not df_output:
            return False

        return filepath in df_output

    @staticmethod
    def file_is_exists(filepath: str):
        """
        判断文件是否存在
        """
        return os.path.exists(filepath)

    @staticmethod
    def remove_file(filepath: str):
        """
        安全的删除文件

        safe_remove_file
        """
        if not filepath:
            log.warning(f"filepath is None")
            return

        if os.path.exists(filepath):
            os.remove(filepath)
            log.info(f"文件删除成功：{filepath}")
        else:
            log.warning(f"文件不存在：{filepath}")

    @classmethod
    def write_content_to_file_atomic(
            cls,
            filepath: str,
            new_content: str,
            encoding: str = 'utf-8',
            window_line=False
    ) -> bool:
        """
        原子性地更新文件内容    ps: 一般更新文件内容的时候使用！！！
        该函数首先将新内容写入一个临时文件，然后使用 os.replace()
        原子性地将临时文件替换为目标文件。

        解决问题：更新文件时，如果更新失败，可能会留下一个空文件。

        Args:
            filepath: 要更新的文件路径。
            new_content: 要写入的新内容。
            encoding: 文件编码 (默认为 'utf-8')。
            window_line: 是否改成window换行符
        Returns:
            True 如果更新成功，False 如果更新失败。
        """
        if window_line:
            new_content = new_content.replace("\n", "\r\n")

        temp_filepath = cls.get_tmp_filename(filepath)

        try:
            # 1. 写入临时文件
            with open(temp_filepath, 'w', encoding=encoding) as f:
                f.write(new_content)
            # 2. 原子性地替换文件
            os.replace(temp_filepath, filepath)
            log.info(f"atomic save successful!")
            cls.log_file_size(filepath)

            return True
        except OSError as e:
            # 处理权限错误
            if e.errno == errno.EACCES or e.errno == errno.EPERM:
                log.error(f"权限错误：没有权限写入文件 {filepath}")
            elif e.errno == errno.ENOSPC:
                log.error(f"磁盘空间不足，无法写入文件 {filepath}")
            elif e.errno == errno.EROFS:
                log.error(f"文件系统为只读，无法写入文件 {filepath}")
            else:
                log.error(f"OSError 错误: {e}")
            # 清理临时文件 (如果存在)
            if os.path.exists(temp_filepath):
                try:
                    os.remove(temp_filepath)
                except OSError as e2:
                    log.error(f"无法删除临时文件 {temp_filepath}: {e2}")
            return False
        except Exception as e:
            log.error(f"更新文件时发生意外错误: {e}")
            log.error(traceback.format_exc())
            # 清理临时文件 (如果存在)
            if os.path.exists(temp_filepath):
                try:
                    os.remove(temp_filepath)
                except OSError as e2:
                    log.error(f"无法删除临时文件 {temp_filepath}: {e2}")
            return False


class MathUtil(object):

    @staticmethod
    def cal_rate(prop: int, total: int):
        """
        计算占比
        :param prop: 分子
        :param total: 分母
        :return: 返回格式    如： 50%
        """
        if not total:
            return "0%"

        return f'{"%.0f" % (prop / total * 100)}%'


class DateUtil(object):
    """
    时间相关操作
    """

    @staticmethod
    def convert_date(date_time: str, fmt: str = "%Y%m%d%H%M%S"):
        """
        将时间转换为指定格式的字符串
        :param date_time: 字符串时间
        :param fmt:
        :return:
        """
        time1 = datetime.strptime(date_time, '%Y-%m-%dT%H:%M:%S')
        return time1.strftime(fmt)

    @staticmethod
    def cal_end_date(date_time: str, time_ct: int, fmt: str):
        """
        计算结束时间
        :param date_time: 开始时间
        :param time_ct: 时间间隔， 单位 1000/s
        :param fmt: 格式
        :return:
        """
        start_time = datetime.strptime(date_time, "%Y-%m-%dT%H:%M:%S")
        return (start_time + timedelta(seconds=time_ct / 1000)).strftime(fmt)

    @staticmethod
    def get_datetime_now(fmt="%Y%m%d%H%M%S") -> str:
        """
        获取当前时间，一般用于文件名
        :param fmt: 格式
        :return:
        """
        return datetime.now().strftime(fmt)

    @staticmethod
    def time_to_fmt_time(_timestamp: int, fmt="%Y-%m-%d %H:%M:%S") -> str:
        """

        :param _timestamp:
        :param fmt:
        :return:
        """
        dt_object = datetime.fromtimestamp(_timestamp)
        # 格式化datetime对象为特定的格式
        formatted_time = dt_object.strftime(fmt)
        return formatted_time


class XmlUtil(object):
    """
    xml文档操作相关
    """

    @classmethod
    def get_xml_root_by_file(cls, file_path: str) -> ElementTree:
        """
        获取 xml 根元素
        :param file_path: xml文件路径
        :return:
        """
        try:
            tree = cElementTree.parse(file_path)
            root = tree.getroot()
            return root
        except Exception as err:
            log.warning(f"xml解析出错，重新解析一次！err:{err} 异常文件：{file_path}")
            time.sleep(0.5)
            file_content = FileUtil.read_file(file_path)

            try:
                return cls.get_xml_root_by_str(file_content)

            except Exception as err1:
                log.warning(f"xml数据解析异常，文件：{file_path}出错！")
                # log.warning(f"文件内容为：----------\n{file_content}\n----------")
                raise err1

    @staticmethod
    def get_xml_root_by_str(xml_content: str) -> ElementTree:
        """
        获取 xml 根元素
        :param xml_content: xml文件内容
        :return:
        """
        try:
            root = cElementTree.fromstring(xml_content)
        except Exception as err:
            log.warning(f"xml数据解析异常，文件内容为：----------\n{xml_content}\n----------")
            raise err
        return root

    @staticmethod
    def format_xml(xml_string: str) -> str:
        """
        对xml数据进行下格式化，确保xml数据格式的正确性和美观性
        """
        # 如果'<?xml..'之前有空白字符需先去除，否则执行parseString会报错
        cleaned_xml_string = xml_string.lstrip()

        # 格式化XML，使用toprettyxml方法，设置缩进为4个空格, 指定编码为UTF-8
        dom = xml.dom.minidom.parseString(cleaned_xml_string)
        pretty_xml_bytes = dom.toprettyxml(indent="    ", encoding="UTF-8")
        pretty_xml_str = pretty_xml_bytes.decode('UTF-8')

        # 去除格式化后可能产生的多余空白行
        lines = [line for line in pretty_xml_str.split('\n') if line.strip()]
        formatted_xml = '\n'.join(lines)
        return formatted_xml

    @staticmethod
    def get_board_position(file_path: str) -> Tuple[dict, dict]:
        """
        获取小板在大板中的位置
        :param file_path: report.xml 路径
        :return:
        """
        root = XmlUtil.get_xml_root_by_file(file_path)

        image_scale = float(root.find("imageScale").text)
        pixel_width = float(root.find("orignal-pixel-size").find("width").text)
        pixel_height = float(root.find("orignal-pixel-size").find("height").text)

        # pcb 的一些位置信息
        pcb_data = {
            "image_scale": image_scale,
            "pixel_width": pixel_width,
            "pixel_height": pixel_height
        }

        boards = root.find("boards").findall("board")

        # 每一块小板的位置信息  {board_id: <位置信息>}
        position_map = {}

        for board in boards:
            board_id = board.attrib.get("id")

            geometry_ele = board.find("geometry")
            position = {
                "cx": float(geometry_ele.find("cx").text),
                "cy": float(geometry_ele.find("cy").text),
                "width": float(geometry_ele.find("width").text),
                "height": float(geometry_ele.find("height").text),
            }
            position_map[board_id] = position

        return position_map, pcb_data

    @classmethod
    def dict_x_y_pos(cls, report_file_path: str) -> dict:
        """
        解析 report.xml 里的 x y 坐标
        :param report_file_path:
        :return:
        """
        root = cls.get_xml_root_by_file(report_file_path)
        boards = root.find("boards").findall("board")
        ret_data = {}
        for board in boards:
            comps = board.find("components").findall("component")
            for comp in comps:
                # uuid =
                geometry = comp.find("geometry")
                cx = geometry.find("cx").text
                cy = geometry.find("cy").text
                angle = geometry.find("angle").text
                width = geometry.find("width").text
                height = geometry.find("height").text
                ret_data[comp.find("uuid").text] = {
                    "cx": cx,
                    "cy": cy,
                    "angle": angle,
                    "width": width,
                    "height": height,
                }
        return ret_data

    @classmethod
    def get_report_data(cls, report_filepath) -> dict:
        """
        获取report.xml的信息
        :param report_filepath:
        :return:
        """
        root = cls.get_xml_root_by_file(report_filepath)

        boards = root.find("boards")

        order = root.find("order")

        if order is not None:
            order_id = order.text
        else:
            order_id = ""

        # image_scale = float(root.find("imageScale").text)
        pixel_width = float(root.find("orignal-pixel-size").find("width").text)
        pixel_height = float(root.find("orignal-pixel-size").find("height").text)

        comp_data_map = {}
        bad_data_map = {}
        for board in boards:
            board_no = board.attrib.get("id")

            bad_board = board.find("BadBoard").text

            bad_data_map[board_no] = {
                "bad_board": bad_board,  # ("1", 坏板)  ("0", 好板)
            }

            comps = board.find("components").findall("component")
            for comp in comps:
                # uuid =
                geometry = comp.find("geometry")
                comp_id = comp.find("id").text
                cx = geometry.find("cx").text
                cy = geometry.find("cy").text
                angle = geometry.find("angle").text
                width = geometry.find("width").text
                height = geometry.find("height").text
                comp_data_map[comp.find("uuid").text] = {
                    # "cx": cx,
                    # "cy": cy,
                    # "width": width,
                    # "height": height,
                    "cx": int(float(cx) / pixel_width),
                    "cy": int(float(cy) / pixel_height),
                    "width": int(float(width) / pixel_width),
                    "height": int(float(height) / pixel_height),

                    "angle": int(float(angle)),
                    "comp_id": comp_id
                }

        return {
            "bad_data_map": bad_data_map,
            "comp_data_map": comp_data_map,
            "order_id": order_id,
        }

    @classmethod
    def dict_comp_barcode(cls, report_xml_path: str) -> dict:
        """
        解析 report.xml 里的器件条码

        return param: -------------------
        {
            "1": [
                {
                    "comp_tag": "COMP1002",
                    "comp_barcode": "D3VZ15KBT4021000PO2050"
                }
            ],
            "2": []
        }
        return param: -------------------

        :param report_xml_path:
        :return:
        """
        root = cls.get_xml_root_by_file(report_xml_path)

        boards = root.find("boards").findall("board")
        ret_data = {}
        for board in boards:
            board_id = board.attrib.get("id")

            comps = board.find("components").findall("component")

            barcode_list = []
            for comp in comps:
                comp_pos = comp.find("designator").text
                # barcode_ele = comp.find("barcode")
                #
                # if barcode_ele is not None:
                #     barcode_list.append({
                #             "comp_tag": comp_pos,
                #             "comp_barcode": barcode_ele.text
                #         })

                children = comp.find("children")
                for child in children:
                    barcode_ele = child.find("barcode")
                    defect = child.find("defect").text

                    if barcode_ele is not None:
                        log.info(f"comp barcode: {barcode_ele.text}")
                        barcode_list.append({
                            "comp_tag": comp_pos,
                            "comp_barcode": barcode_ele.text
                        })
                    else:
                        if defect == "19":
                            # 兼容条码未识别到的场景
                            barcode_list.append({
                                "comp_tag": comp_pos,
                                "comp_barcode": ""
                            })

            ret_data[board_id] = barcode_list

        return ret_data

    @staticmethod
    def get_pad_test_data(report_xml: str) -> dict:
        """
        解析report.xml，并获取到component的数据
        :return:
        """
        root = XmlUtil.get_xml_root_by_file(report_xml)

        boards = root.find("boards")

        data_ret = {}
        for board in boards:
            comps = board.find("components")
            for comp in comps:
                designator = comp.find("designator").text
                all_child = comp.find("children")

                comp_pad = []

                if len(all_child) == 0:
                    # 没有pad
                    pad_ele = comp.find("SolderpasteParam")

                    if pad_ele:
                        height = pad_ele.find("height").text
                        offset = pad_ele.find("offset").text
                        area = pad_ele.find("area").text
                        valumn = pad_ele.find("valumn").text
                        real_height = pad_ele.find("realHeight").text
                        result = pad_ele.find("result").text

                        offset = offset.split("   ")

                        comp_pad.append({
                            "designator": designator,
                            "pad_no": f"{designator}-1",
                            "a": area,
                            "v": valumn,
                            "h": height,
                            "rh": real_height,
                            "sx": offset[0][2:],
                            "sy": offset[1][2:],
                            "result": result,
                        })
                    else:
                        log.warning(f"{designator}, Don't had Solder paste Param!")

                else:
                    # 有多个pad
                    for ix, child in enumerate(all_child):
                        pad_ele = child.find("SolderpasteParam")  # 锡膏检测参数
                        height = pad_ele.find("height").text
                        offset = pad_ele.find("offset").text
                        area = pad_ele.find("area").text
                        valumn = pad_ele.find("valumn").text
                        real_height = pad_ele.find("realHeight").text
                        result = pad_ele.find("result").text

                        offset = offset.split("   ")
                        comp_pad.append({
                            "designator": designator,
                            "pad_no": f"{designator}-{ix + 1}",
                            "a": area,
                            "v": valumn,
                            "h": height,
                            "rh": real_height,
                            "sx": offset[0][2:],
                            "sy": offset[1][2:],
                            "result": result
                        })

                data_ret[comp.find("uuid").text] = comp_pad

        log.info(f"共有主pad点：{len(data_ret)}")

        return data_ret

    @classmethod
    def dict_board_geometry(cls, report_file: str) -> dict:
        """
        获取拼板的 geometry
        :return:
        """
        root = cls.get_xml_root_by_file(report_file)
        boards = root.find("boards").findall("board")
        ret_data = {}
        for board in boards:
            board_id = board.attrib.get("id")
            board_geometry = board.find('geometry')

            board_width = board_geometry.find('width').text
            board_height = board_geometry.find('height').text
            board_angle = board_geometry.find('angle').text

            ret_data[board_id] = {
                'width': board_width,
                'height': board_height,
                'angle': board_angle
            }

        return ret_data


class OtherUtil(object):

    @staticmethod
    def get_md5_sign(origin_str: str):
        """
        hash 字符串
        :param origin_str:
        :return:
        """
        return hashlib.md5(origin_str.encode(encoding="utf8")).hexdigest()

    @staticmethod
    def list_chunks(_list: list, n) -> List[list]:
        """
        将一个列表切分成长度为N的若干份
        """
        for i in range(0, len(_list), n):
            yield _list[i:i + n]

    @staticmethod
    def split_list_n_list(origin_list, n):
        """
        讲一个列表切分成n份
        :param origin_list:
        :param n:
        :return:
        """
        if len(origin_list) % n == 0:
            cnt = len(origin_list) // n
        else:
            cnt = len(origin_list) // n + 1

        for page in range(0, n):
            yield origin_list[page * cnt:(page + 1) * cnt]

    @staticmethod
    def split_list_by_len(origin_list: list, threshold: int):
        """
        将列表分成N份，每份的字符串总长度不超过设定的threshold
        如果单个字符串长度大于threshold，则该字符串单独为一份

        示例：
        入参：origin_list = ["sfdf", "re", "eret", "eret232"]  threshold = 6
        返回参数：[["sfdf", "re"],  ["eret"], ["eret232"]]
        """
        current_list = []
        current_len = 0

        for string in origin_list:
            string_len = len(string)
            # 如果字符串长度大于阈值，则它单独作为一份
            if string_len > threshold:
                if current_list:
                    yield current_list
                    current_list = []
                    current_len = 0
                yield [string]
            # 如果加入当前列表后总长度不超过阈值，则加入当前列表
            elif current_len + string_len <= threshold:
                current_list.append(string)
                current_len += string_len
            # 否则，保存当前列表，并开始一个新列表
            else:
                yield current_list
                current_list = [string]
                current_len = string_len

        # 将最后的列表yield
        if current_list:
            yield current_list

    @staticmethod
    def match_host(api_url):
        """
        匹配 接口地址 上的主机名
        :return:
        """
        pat = r"http://(.*?)/"  # noqa

        host = re.match(pat, api_url)
        if host:
            host = host.group(1)
        else:
            host = ""

        return host

    @staticmethod
    def get_origin_uuid4_str() -> str:
        """
        获取一个UUID
        :return:
        """
        return str(uuid.uuid4())

    @staticmethod
    def get_uuid4_str() -> str:
        """
        获取一个UUID
        :return:
        """
        return str(uuid.uuid4()).replace('-', '').upper()

    @staticmethod
    def update_config_by_key(key, new_value):
        """
        更新配置文件
        """
        with open(xconfig.mes_ini_file, "r+", encoding="gbk") as f:
            rows = f.readlines()

            row_ix = -1
            for ix, row in enumerate(rows):
                if row.startswith(f"{key}="):
                    row_ix = ix

            if row_ix >= 0:
                new_row = f"{key}={new_value}\n"
                rows[row_ix] = new_row

            f.seek(0)
            f.writelines(rows)

    @staticmethod
    def read_config_by_key(key):
        with open(xconfig.mes_ini_file, "r", encoding="gbk") as f:
            rows = f.readlines()

            value = ""
            for row in rows:
                if row.startswith(f"{key}="):
                    row = row.replace("\n", "")
                    value = row.split("=")[1]

            return value

    @staticmethod
    def file_to_base64_content(image_path) -> str:
        """
        图片转换成base64格式
        """
        with open(image_path, "rb") as f:
            image_content = f.read()
            base64_data = base64.b64encode(image_content)

        return base64_data.decode("utf-8")

    @staticmethod
    def base64_to_file(dst_file, base64_content: str):
        """
        base64转成图片
        :return:
        """
        with open(dst_file, "wb") as f:
            f.write(base64.b64decode(base64_content))

    @staticmethod
    def str_to_base64(original_string: str) -> str:
        """
        base64编码
        :param original_string:
        :return:
        """
        return base64.b64encode(original_string.encode('utf-8')).decode('utf-8')

    @staticmethod
    def complete_array(arr: list) -> list:
        """
        元素补全
        [0, 0, 33, 0] --> return [31,32,33,34]
        """
        nonzero_index = arr.index(next(filter(lambda x: x != 0, arr)))

        for i in range(len(arr)):
            if i < nonzero_index:
                arr[i] = arr[nonzero_index] - (nonzero_index - i)
            else:
                arr[i] = arr[nonzero_index] + (i - nonzero_index)

        return arr

    @staticmethod
    def obj_to_json(obj: Any, indent=None):
        """"""
        return json.dumps(obj, ensure_ascii=False, separators=(",", ':'), indent=indent)

    @staticmethod
    def geometry_to_box(
            pixel_width, pixel_height, image_scale,
            board_cx, board_cy, board_width, board_height
    ):
        if image_scale >= 1:
            image_scale = 1 / image_scale

        x = int(board_cx / pixel_width * image_scale)
        y = int(board_cy / pixel_height * image_scale)
        w = int(board_width / pixel_width * image_scale)
        h = int(board_height / pixel_height * image_scale)

        print(x, y, w, h)
        left_x = int(x - w / 2)
        left_y = int(y - h / 2)

        left = left_x
        upper = left_y
        right = w + left_x
        lower = left_y + h

        # 定义切割区域（左上角坐标和右下角坐标）
        box = (left, upper, right, lower)
        return box

    @staticmethod
    def convert_int_to_utf8_bytes(num: int) -> bytes:
        """
        将输入的整数转换为其对应的 UTF-8 编码的字节串。
        参数:
        num (int): 一个整数，表示要转换的字符的 ASCII 码。
        返回:
        bytes: 包含输入整数对应的 UTF-8 编码的字节串。
        示例:
        >>> OtherUtil.convert_int_to_utf8_bytes(65)
        b'A'
        >>> OtherUtil.convert_int_to_utf8_bytes(228)
        b'\xc3\xa4'
        >>> OtherUtil.convert_int_to_utf8_bytes(10)
        b'\r'
        >>> OtherUtil.convert_int_to_utf8_bytes(13)
        b'\n'
        """
        return bytes(chr(num), 'utf-8')

    @staticmethod
    def get_mac_address() -> str:
        mac_addr = ''
        try:
            # 执行 route -n 命令 获取当前正在使用网卡
            result = subprocess.run(['route', '-n'], stdout=subprocess.PIPE, text=True)
            lines = result.stdout.splitlines()
            network_card = ''
            for line in lines:
                if line.startswith('0.0.0.0'):
                    parts = line.split()
                    network_card = parts[-1]  # 默认路由的网卡名在最后一列
                    break

            if not network_card:
                log.info(f'当前可能没有联网，找不到正在使用的网卡，MAC地址返回空')
                return mac_addr

            # 使用 ip link 获取当前使用网卡的MAC地址
            result = subprocess.run(['ip', 'link', 'show', network_card],
                                    stdout=subprocess.PIPE, text=True)
            output = result.stdout
            mac_match = re.search(r'link/ether ([0-9a-fA-F:]+)', output)
            mac_addr = mac_match.group(1) if mac_match else ''
        except Exception as e:
            log.info(f'MAC地址获取失败，error:{e}')

        return mac_addr


class CacheUtil(object):
    """
    缓存控制
    """
    cache_data_path = xconfig.mes_cache_data

    @classmethod
    def get_cache_data(cls) -> dict:
        """
        读取缓存数据
        :return:
        """
        try:
            with open(cls.cache_data_path, "r") as f:
                data = json.load(f)

        except Exception as e:
            print(e)
            log.info("第一次获取缓存数据")
            data = {}

        return data

    @classmethod
    def save_cache_data(cls, content: dict, save_key: str = ""):
        """
        保存缓存数据
        :param content 缓存的数据
        :param save_key
        :return:
        """
        FileUtil.dump_json_to_file_atomic(
            cls.cache_data_path, content
        )
        log.info(f"keyName:{save_key}")

    @classmethod
    def set(cls, key, value):
        """
        缓存一个key
        :param key:
        :param value:
        :return:
        """
        cache_data = cls.get_cache_data()
        cache_data[key] = value
        cls.save_cache_data(cache_data, key)

    @classmethod
    def append_or_update_new_data(cls, new_data: dict):
        """
        更新最新的数据
        :param new_data:
        :return:
        """
        cache_data = cls.get_cache_data()
        cache_data.update(new_data)
        cls.save_cache_data(cache_data, "update data")

    @classmethod
    def get(cls, key, default=None):
        """
        获取一个缓存key, 如果获取不到，返回None
        :param key:
        :param default:
        :return:
        """
        cache_data = cls.get_cache_data()
        return cache_data.get(key, default)

    @classmethod
    def cal_oee(cls) -> str:
        """
        从缓存文件中计算OEE
        :return:
        """
        cache_data = cls.get_cache_data()
        # --------计算oee
        x_time_device_run_count = int(time.time()) - cache_data.get('X_time_device_run', 0)
        x_time_except_count = cache_data.get('X_time_except_count', 0)
        # x_time_stop_count = cache_data.get('X_time_stop_count', 0)

        oee_time = x_time_device_run_count - x_time_except_count
        log.info(f"oee time:{oee_time}  total time:{x_time_device_run_count}")
        oee = oee_time / x_time_device_run_count
        oee_str = f"{round(oee, 4) * 100}%"

        log.info(f"oee: {oee_str}")
        # ----------
        return oee_str


class ImageUtil(object):

    @staticmethod
    def crop_image_from_pcb(source_image_file_path,
                            output_image_file_path,
                            image_scale,
                            pixel_width,
                            pixel_height,
                            cx,
                            cy,
                            width,
                            height,
                            ):
        """
        从源图片里根据输出图片信息裁剪图片
        :param source_image_file_path: 源图片地址
        :param output_image_file_path: 输出图片地址
        :param image_scale: 源图片信息
        :param pixel_width: 源图片信息
        :param pixel_height: 源图片信息
        :param cx: 小板位置信息
        :param cy: 小板位置信息
        :param width: 小板位置信息
        :param height: 小板位置信息
        :return:
        """
        # 如果需要调用此函数，则必须导入相关包，不调用时，则不导入
        img = Image.open(source_image_file_path)  # noqa ps: from PIL import Image
        box = OtherUtil.geometry_to_box(
            pixel_width, pixel_height, image_scale,
            cx, cy, width, height
        )
        img.crop(box).save(output_image_file_path)

    @staticmethod
    def file_to_base64_content(image_path: str) -> str:
        """
        图片转换成base64格式
        """
        with open(image_path, "rb") as f:
            image_content = f.read()
            base64_data = base64.b64encode(image_content)

        return base64_data.decode("utf-8")


def x_response(status: str = "true", msg: str = "发送成功"):
    """
    返回一个标准的响应
    :param status: 是否成功响应  (true, 成功) (false, 失败)
    :param msg: 返回给界面的信息
    :return:
    """
    if status == "false":
        log.error(msg)

    if status == "true":
        result = True
    else:
        result = False

    time_now = DateUtil.get_datetime_now("%Y.%m.%d %H:%M:%S")
    return {'key': time_now, 'result': result, 'string': msg}


def send_logfile_by_email(custom_name):
    """
    发送日志文件给开发
    """
    # 设置主题和正文
    subject = f'【MesLog日志接收】来自Mes配置器：{custom_name}'
    body = 'Please find the attached file.'

    # 设置文件路径和名称
    file_path = '/home/<USER>/.aoi/log/pymes.log'

    if not os.path.exists(file_path):
        return False

    file_name = 'pymes.log'

    # 构造邮件对象
    msg = MIMEMultipart()
    msg['From'] = xconfig.email_sender
    msg['To'] = ",".join(xconfig.email_recipient)
    msg['Subject'] = subject

    # 添加正文
    msg.attach(MIMEText(body, 'plain'))

    # 添加附件
    with open(file_path, 'rb') as f:
        part = MIMEApplication(f.read(), Name=os.path.basename(file_path))
        part['Content-Disposition'] = f'attachment; filename="{file_name}"'
        msg.attach(part)

    # 连接 Gmail SMTP 服务器并发送邮件
    with smtplib.SMTP_SSL('smtp.qq.com', 465) as smtp:
        smtp.login(xconfig.email_sender, xconfig.email_auth_key)
        smtp.sendmail(xconfig.email_sender, xconfig.email_recipient, msg.as_string())

    log.info('Email sent successfully.')
    return True


def parse_template_image(report_xml: str) -> Tuple[str, dict]:
    """
    解析整板的模板图和器件的模板图
    """
    root = XmlUtil.get_xml_root_by_file(report_xml)
    image_path = root.find("image-path").text
    path_1 = "../../../../.."
    path_2 = "/home/<USER>/aoi"
    pcb_template_img = image_path.replace(path_1, path_2)

    comp_template_img_map = {}
    boards = root.find("boards")
    for board in boards:
        # print(board.attrib)
        # board_id = board.attrib.get("id")

        comps = board.find("components")
        for comp in comps:
            comp_tag = comp.find("designator").text
            standard_image = comp.find("standard-image").text
            standard_image = standard_image.replace(path_1, path_2) if standard_image is not None else standard_image

            comp_template_img_map[f"{comp_tag}"] = standard_image

    return pcb_template_img, comp_template_img_map


class LimitedDict:
    """
    有限空间的Dict
    """

    def __init__(self, capacity):
        self.capacity = capacity
        self.dict = {}
        self.keys_queue = []

    def add_item(self, key, value):
        if key in self.dict:
            # 如果键已存在，更新值并将键移到队列末尾
            self.keys_queue.remove(key)
        elif len(self.dict) >= self.capacity:
            # 如果达到容量限制，删除最早添加的键值对
            oldest_key = self.keys_queue.pop(0)
            del self.dict[oldest_key]

        # 添加新键值对
        self.dict[key] = value
        self.keys_queue.append(key)

    def get_value(self, key, default_value=None):
        return self.dict.get(key, default_value)

    def is_exist_item(self, item: str):
        """
        检查map中是否存在指定元素。
        :param item:要检查的元素。
        :return:bool: 如果元素存在，则返回 True，否则返回 False。
        """
        return item in self.dict

    def remove_item(self, item):
        """
        remove item
        :param item:
        :return:
        """
        if self.is_exist_item(item):
            key_ix = self.keys_queue.index(item)

            del self.keys_queue[key_ix]
            del self.dict[item]


class LimitedCapacityDict:
    def __init__(self, capacity: int, file_path: str = ""):
        self.capacity = capacity

        if not file_path:
            file_path = xconfig.mes_limit_cache_data

        self.file_path = file_path
        self.cache = None
        self.load_data()

    def load_data(self):
        try:
            with open(self.file_path, 'r') as f:
                data = json.load(f)
                self.cache = OrderedDict(data)
        except FileNotFoundError:
            self.cache = OrderedDict()

    def save_data(self):
        FileUtil.dump_json_to_file_atomic(
            self.file_path,
            self.cache
        )

    def __getitem__(self, key):
        value = self.cache[key]
        self.cache.move_to_end(key)  # 将访问的元素放到最后
        return value

    def __setitem__(self, key, value):
        if key in self.cache:
            self.cache.pop(key)
        elif len(self.cache) >= self.capacity:
            self.cache.popitem(last=False)  # 移除最早添加的元素
        self.cache[key] = value
        self.save_data()

    def get(self, key, default=None):
        if key in self.cache:
            return self[key]  # 使用__getitem__方法，确保访问的元素放到最后
        return default

    def set(self, key, value):
        self[key] = value  # 使用__setitem__方法，确保新元素放到最后

    def __delitem__(self, key):
        del self.cache[key]
        self.save_data()

    def __repr__(self):
        return str(self.cache)


class CircularList:
    """
    循环列表（或称为环形缓冲区），这种数据结构非常适合于存储固定数量的最新数据。这里实现添加/删除和检查元素的功能。
    """

    def __init__(self, capacity: int, buffer=None):
        """
        创建一个 CircularList 对象。

        Args:
        - capacity: int，列表的最大容量。
        """
        self.capacity = capacity
        self.current_index = 0

        if buffer:
            self.buffer = buffer
        else:
            self.buffer = []

    def add_item(self, item: Any):
        """
        向列表中添加一个元素。如果列表已满，则替换最早添加的元素。

        Args:
        - item: 添加的元素。
        """
        if len(self.buffer) < self.capacity:
            self.buffer.append(item)
        else:
            # 替换最早添加的元素
            self.buffer[self.current_index] = item
        # 更新当前索引位置
        self.current_index = (self.current_index + 1) % self.capacity

    def is_exist_item(self, item: Any):
        """
        检查列表中是否存在指定元素。
        :param item:要检查的元素。
        :return:bool: 如果元素存在，则返回 True，否则返回 False。
        """
        return item in self.buffer

    def remove_item(self, item: Any):
        """
        从缓冲区中移除所有与给定项匹配的元素。

        参数:
        - item: Any - 需要移除的元素，类型不限。

        返回值:
        - 无
        """
        # 移除缓冲区中所有与item相等的元素
        self.buffer = [x for x in self.buffer if x != item]

        # 根据缓冲区当前长度，重新计算当前索引位置
        self.current_index = len(self.buffer) % self.capacity

    def remove_one_item(self, item: Any):
        """
        从缓冲区中移除其中一个item。

        参数:
        - item: Any - 需要移除的元素，类型不限。

        返回值:
        - 无
        """
        if item in self.buffer:
            self.buffer.remove(item)
            buffer_size = len(self.buffer)
            if buffer_size > 0:
                if self.current_index == buffer_size:
                    self.current_index = buffer_size - 1
            else:
                self.current_index = 0

    def __repr__(self):
        """
        返回 CircularList 的字符串表示形式。
        :return:
        """
        return str(self.buffer)


def cal_device_status_time(
        device_status_list_old: list,
        range_start: int = None,
        range_end: int = None,
        default_ret: int = 600
):
    """
    统计某个时间段的设备状态时间

    device_status_list 示例
    [
        {
            "state_last": "",
            "time_last": "",
            "state_current": "START",
            "time_current": 1714111538
        },
        {
            "state_last": "START",
            "time_last": 1714111538,
            "state_current": "ERROR",
            "time_current": 1714111541
        },
        {
            "state_last": "ERROR",
            "time_last": 1714111541,
            "state_current": "START",
            "time_current": 1714111547
        },
        {
            "state_last": "START",
            "time_last": 1714111547,
            "state_current": "STOP",
            "time_current": 1714111552
        },
        {
            "state_last": "STOP",
            "time_last": 1714111552,
            "state_current": "START",
            "time_current": 1714111556
        },
        {
            "state_last": "START",
            "time_last": 1714111556,
            "state_current": "STOP",
            "time_current": 1714111559
        }
    ]

    状态流转

    上一次  -->  当前

    ''  --> start/error    # stop前面肯定会有start
    start  -->  stop/error
    error  -->  start     # error后面不可以有stop
    stop  -->  start      # stop不可以有error


    -----------

    ## 第一个状态, 一定要将 time_last 改为边缘时间
    '' --> start  # 等待

    start --> stop # 工作
    stop --> start # 等待
    start --> error # 工作
    error --> start # 故障

    ## 最后一个状态, 一定要加多一个边缘时间，从time_current --> 边缘
    start -> ''  # 工作
    stop -> ''  # 等待
    error -> ''  # 故障

    @param device_status_list_old 设备状态列表
    @param range_start 开始的边缘时间
    @param range_end 结束的边缘时间
    @param default_ret 默认返回时间
    """
    device_status_list = [item for item in device_status_list_old if
                          range_start <= item.get('time_current') <= range_end]

    if not range_start:
        range_start = device_status_list[0].get('time_current')

    if not range_end:
        range_end = device_status_list[-1].get('time_current')

    log.info(f"range start: {range_start}  rang end: {range_end}")

    t1 = datetime.fromtimestamp(range_start).strftime('%Y-%m-%d %H:%M:%S')
    t2 = datetime.fromtimestamp(range_end).strftime('%Y-%m-%d %H:%M:%S')

    log.info(f">>>======={t1=} {t2=} time delta: {range_end - range_start}s status count: {len(device_status_list)}")

    if not device_status_list:
        if device_status_list_old:
            old_item_n = device_status_list_old[-1]
            log.info(f"last status: {old_item_n.get('state_current')}")

            if old_item_n.get('state_current') == "ERROR":
                # 最后一次状态是error
                time_run = 0
                time_wait = 0
                time_error = default_ret
                log.info(
                    f"time count: {time_run=} {time_wait=} {time_error=} total:{time_run + time_wait + time_error}")
                return time_run, time_wait, time_error

            elif old_item_n.get('state_current') == "START":
                # 最后一次状态是start
                time_run = default_ret
                time_wait = 0
                time_error = 0

                log.info(
                    f"time count: {time_run=} {time_wait=} {time_error=} total:{time_run + time_wait + time_error}")
                return time_run, time_wait, time_error
            else:
                time_run = 0
                time_wait = default_ret
                time_error = 0

                log.info(
                    f"time count: {time_run=} {time_wait=} {time_error=} total:{time_run + time_wait + time_error}")
                return time_run, time_wait, time_error

        else:
            log.info(f"stop: {default_ret}, start: 0 error: 0")
            return 0, default_ret, 0

    time_run = 0
    time_wait = 0
    time_error = 0

    log.info("------------>>>>>>>>")
    for item in device_status_list[1:]:
        state_current = item.get('state_current')
        state_last = item.get('state_last')

        time_delta = item.get('time_current') - item.get('time_last')

        log.info(f"[{state_last}]  -->  [{state_current}]  ===> [{time_delta}]")

        if state_current in ["STOP", "ERROR"]:
            time_run += time_delta

        elif state_current == "START":
            if state_last == "ERROR":
                time_error += time_delta

            elif state_last == "STOP":
                time_wait += time_delta

    # 特殊处理，第一个数据包 --------------start
    item0 = device_status_list[0]
    state_last0 = item0.get('state_last')
    state_current0 = item0.get('state_current')
    time_last0 = item0.get('time_last')
    if not state_last0:
        state_last0 = "STOP"
        time_last0 = range_start

    if time_last0 > range_start:
        time_last0 = range_start

    log.info(f"first [边缘/{state_last0}]  -->  [{state_current0}]  ===> [{item0.get('time_current') - time_last0}]")

    if state_current0 in ["STOP", "ERROR"]:
        time_run += item0.get('time_current') - time_last0

    elif state_current0 == "START":
        if state_last0 == "ERROR":
            time_error += item0.get('time_current') - time_last0

        elif state_last0 == "STOP":
            time_wait += item0.get('time_current') - time_last0
    # 特殊处理，第一个数据包 --------------end

    # 特殊处理，最后一个数据包  -------------start
    item_n = device_status_list[-1]
    state_current_n = item_n.get('state_current')
    log.info(f"end [{state_current_n}]  -->  [边缘]  ===> [{item0.get('time_current') - time_last0}]")

    if state_current_n == 'START':
        time_run += range_end - item_n.get('time_current')
    elif state_current_n == 'STOP':
        time_wait += range_end - item_n.get('time_current')
    elif state_current_n == 'ERROR':
        time_error += range_end - item_n.get('time_current')
    # 特殊处理，最后一个数据包  -------------end
    log.info("------------>>>>>>>>")

    log.info(f"time count: {time_run=} {time_wait=} {time_error=} total:{time_run + time_wait + time_error}")

    return time_run, time_wait, time_error


def filter_v3_status_code(inner_func):
    """
    装饰器：过滤v3版本状态码
    """

    def wrapper(*args, **kwargs):
        other_dao = args[1]

        v1_status_str = other_dao.get_device_status_str()
        origin_json_data = other_dao.json_data
        status_code_v3 = origin_json_data.get("statusCodeV3")

        if v1_status_str not in xcons.DEVICE_STATUS and "safedoor" not in v1_status_str \
                and "安全门" not in v1_status_str:
            log.warning(f"过滤V3设备状态码！ {status_code_v3}")

            return x_response()

        elif status_code_v3 in ["1005", "2001",
                                "3001", "3002", "3003", "3004", "3005", "3006", "3007",
                                "5001", "5002", "0001"]:
            log.warning(f"过滤V3设备状态码！ {status_code_v3}")

            return x_response()

        result = inner_func(*args, **kwargs)
        return result

    return wrapper


def complete_array(arr: list, split_index: int = 4) -> list:
    """
    补全条码
    :param arr:
    :param split_index:
    :return:
    """
    nonempty_element = next(filter(None, arr))
    nonempty_suffix = nonempty_element[-split_index:]

    for i in range(len(arr)):
        if arr[i] == "":
            new_suffix = str(int(nonempty_suffix) + (i - arr.index(nonempty_element)))

            new_element = nonempty_element[:-split_index] + str(new_suffix).zfill(split_index)
            arr[i] = new_element

    return arr


if __name__ == '__main__':
    # ret = XmlUtil.dict_comp_barcode("/home/<USER>/Documents/MESDATA/器件条码/B_20230824113632871/report.xml")
    # print(ret)
    # print(FileUtil.get_file_suffix("/home/<USER>/pymes.log"))  # output: `.log`
    # pass
    # root = XmlUtil.get_xml_root_by_file(
    #     f"/home/<USER>/Desktop/日志文件/皓榕/MES/mes/1/man.xml")
    # print(root.attrib)
    # report_uuid = root.find('uuid').text
    # print(report_uuid)

    #     ret1 = """<?xml version="1.0" encoding="utf-8"?>
    # <QAoiMesManData BoardResult="0" ReviewTime="2024-05-16 08:36:37" Barcode="FE3346304" BoardID="2e82c3dd-c8ca-43d7-8626-9d301c39d281" RepairUser="admin">
    #     <CompResults/>
    # </QAoiMesManData>"""
    #     man_root = XmlUtil.get_xml_root_by_str(ret1)
    #
    #     print(man_root.attrib)
    #
    #     repair_comp_results = man_root.find("CompResults")
    #     print(repair_comp_results, len(repair_comp_results))
    #     for repair_comp_result in repair_comp_results:
    #         print(repair_comp_result)

    # ret = XmlUtil.get_pad_test_data("/home/<USER>/Desktop/日志文件/silian/T_20240828082248920_id6.1/report.xml")
    # print(ret)
    # ret = OtherUtil.file_to_base64_content(
    #     f"/home/<USER>/aoi/run/results/333.999bak/20241011/T_20241011153824380_1_NG/images/ng/Chip_R/0/COMP1379_1379.png")
    # print(ret)
    # ret = OtherUtil.file_to_base64_content(
    #     "/home/<USER>/Downloads/R-C.jpg",
    # )
    # print(ret)

    FileUtil.write_content_to_file_atomic(
        "./test1.txt",
        "sdfsdf"
    )
