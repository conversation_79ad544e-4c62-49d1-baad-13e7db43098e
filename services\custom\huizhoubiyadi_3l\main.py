# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/7 下午2:33
# Author     ：sch
# version    ：python 3.8
# Description：惠州比亚迪  3楼
"""
import json
import os
from typing import Any

from PyQt5.QtCore import QTime
from PyQt5.QtWidgets import QTimeEdit, QLabel

from common import xrequest, xutil, xcons
from common.xutil import LimitedCapacityDict
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from services.custom.huizhoubiyadi_3l.byd_socket_server import SockerServerThread
from vo.mes_vo import DataVo, OtherVo

circle_list = xutil.CircularList(200)


def xx_request(api_url: str, req_param: dict) -> dict:
    ret_str1 = xrequest.RequestUtil.post_form(api_url, req_param, to_json=False)
    ret_data1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1).text
    return json.loads(ret_data1)


csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoubiyadi_3l release v1.0.0.15",
        "device": "AIS40X",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-08 15:08  条码校验，上传数据
date: 2024-04-10 09:20  部分参数修改
date: 2024-04-10 11:03  bugfix：csv文件
date: 2024-04-10 11:56  上传csv成功后也需要调用AddTestLogInfo接口
date: 2024-04-10 16:01  兼容AddTestLogInfo接口的返回值
date: 2024-12-20 17:02  新增需求
date: 2024-12-21 09:34  fix: 上传整板图
date: 2025-05-08 18:04  ATAOI_2019-29588: 新增上传数据和设备状态接口
date: 2025-05-19 10:26  ATAOI_2019-29588: 新增主动切换板式功能
date: 2025-05-19 10:26  ATAOI_2019-29588: 新增 条码枪扫码切换板式 功能
date: 2025-06-16 15:34  tcp服务端改为长连接
date: 2025-06-17 11:43  优化报错信息显示
date: 2025-06-17 15:35  根据现场返回，修改响应数据
date: 2025-06-17 17:20  增加配置项：是否上传设备参数
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "条码校验接口URL",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/CheckSNCanBeTest",
        },
        # "api_url_data1": {
        #     "ui_name": "物料绑定接口URL",
        #     "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/bindNoneBomData",
        # },
        "api_url_data2": {
            "ui_name": "测试数据上传接口URL",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/uploadTestData",
        },
        "api_url_data3": {
            "ui_name": "上传记录接口URL",
            "value": "http://**********:20020/MES2/Service.action",
        },
        "api_url_data4": {
            "ui_name": "getNoneNVSnInfo接口",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/getNoneNVSnInfo",
        },
        "ftp_host1": {
            "ui_name": "FTP Host1",
            "value": "127.0.0.1"
        },
        "ftp_port1": {
            "ui_name": "FTP Port1",
            "value": "21"
        },
        "ftp_user1": {
            "ui_name": "FTP 账号1",
            "value": "sch"
        },
        "ftp_password1": {
            "ui_name": "FTP 密码1",
            "value": "123456"
        },
        "ftp_path1": {
            "ui_name": "FTP 路径1",
            "value": "/logfile/MACHINE"
        },
        # "ftp_host2": {
        #     "ui_name": "FTP Host2",
        #     "value": "127.0.0.1"
        # },
        # "ftp_port2": {
        #     "ui_name": "FTP Port2",
        #     "value": "21"
        # },
        # "ftp_user2": {
        #     "ui_name": "FTP 账号2",
        #     "value": "sch"
        # },
        # "ftp_password2": {
        #     "ui_name": "FTP 密码2",
        #     "value": "123456"
        # },
        # "ftp_path2": {
        #     "ui_name": "FTP 路径2",
        #     "value": "/logfile/MACHINE"
        # },
        "v2_api_url_data": {
            "ui_name": "V2_设备参数接口URL",
            "value": "http://127.0.0.1:10086/Machine/IDCMachineParam",
        },
        "v2_equipment_id": {
            "ui_name": "V2_machineNo",
            "value": "XT005644",
        },
        "v2_line": {
            "ui_name": "V2_line",
            "value": "A301_H6",
        },
        "v2_station": {
            "ui_name": "V2_工站",
            "value": "IOT",
        },
        "v2_addr_ip": {
            "ui_name": "V2_设备IP",
            "value": "",
        },
        "v2_addr_mac": {
            "ui_name": "V2_设备MAC",
            "value": "",
        },
        # "v2_check_project_url": {
        #     "ui_name": "V2_根据SN获取料号",
        #     "value": "http://***********:28820/blaketestws/BlakeTestService.asmx/getOneBySn",
        # },
        "v2_check_project_url2": {
            "ui_name": "V2_根据SN获取程序名",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/getmesSwVersionPar",
        },
        "v2_check_project_server_port": {
            "ui_name": "V2_切换板式服务端口",
            "value": "10004",
            "is_port": True
        },
    }

    form = {
        "line_id": {
            "ui_name": "线别",
            "value": "",
        },
        "station_id": {
            "ui_name": "工站",
            "value": "",
        },
        "fixture_no": {
            "ui_name": "fixtureNo",
            "value": "",
        },
        "operator": {
            "ui_name": "operator",
            "value": "",
        },
        "machine_no": {
            "ui_name": "machineNo",
            "value": "",
        },
    }

    path = {
        "save_path_img": {
            "ui_name": "整板图保存路径",
            "value": "",
        }
    }

    other_combo = {
        "light_source": {
            "ui_name": "大图光源",
            "item": ["0", "1", "2"],
            "value": "1"
        },
        "ftp_encoding": {
            "ui_name": "FTP 编码",
            "item": ["gbk", "utf-8"],
            "value": "gbk"
        },
        "is_upload_device_status": {
            "ui_name": "是否上传设备参数",
            "item": ["Yes", "No"],
            "value": "No"
        },
    }

    password_style = [
        "ftp_password2", "ftp_password1"
    ]

    def __init__(self):
        self.start_time_edit = None
        self.end_time_edit = None
        self.label_start_time = None
        self.label_end_time = None

        self.sk_thread = None  # 切换板式服务线程

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.start_time_edit = QTimeEdit(main_window)
        self.end_time_edit = QTimeEdit(main_window)

        start_time = xutil.CacheUtil.get("v2_start_time", "08:00")
        end_time = xutil.CacheUtil.get("v2_end_time", "20:00")

        start_h = start_time.split(":")[0]
        start_m = start_time.split(":")[1]
        self.start_time_edit.setTime(QTime(int(start_h), int(start_m)))

        end_h = end_time.split(":")[0]
        end_m = end_time.split(":")[1]
        self.end_time_edit.setTime(QTime(int(end_h), int(end_m)))

        self.start_time_edit.setDisplayFormat("HH:mm")
        self.end_time_edit.setDisplayFormat("HH:mm")

        self.label_start_time = QLabel(main_window)
        self.label_start_time.setText("V2_开始时间")
        self.label_end_time = QLabel(main_window)
        self.label_end_time.setText("V2_结束时间")

        main_window.setting_param_layout.addRow(self.label_start_time, self.start_time_edit)
        main_window.setting_param_layout.addRow(self.label_end_time, self.end_time_edit)

        v2_check_project_server_port = other_vo.get_value_by_cons_key("v2_check_project_server_port", to_int=True)
        v2_equipment_id = other_vo.get_value_by_cons_key("v2_equipment_id")
        v2_line = other_vo.get_value_by_cons_key("v2_line")
        v2_station = other_vo.get_value_by_cons_key("v2_station")
        # v2_check_project_url = other_vo.get_value_by_cons_key("v2_check_project_url")
        v2_check_project_url2 = other_vo.get_value_by_cons_key("v2_check_project_url2")

        self.sk_thread = SockerServerThread(
            main_window,
            v2_check_project_server_port,
            v2_equipment_id,
            v2_line,
            v2_station,
            **{
                # "v2_check_project_url": v2_check_project_url,
                "v2_check_project_url2": v2_check_project_url2,
                "v2_station": v2_station,
            }
        )
        self.sk_thread.start()
        self.sk_thread.daemon = True  # 设置为守护线程

    def save_btn_on_window(self, main_window):
        start_time = self.start_time_edit.text()
        end_time = self.end_time_edit.text()

        xutil.CacheUtil.set("v2_start_time", start_time)
        xutil.CacheUtil.set("v2_end_time", end_time)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station_id = other_vo.get_value_by_cons_key("station_id")
        line_id = other_vo.get_value_by_cons_key("line_id")
        fixture_no = other_vo.get_value_by_cons_key("fixture_no")
        operator = other_vo.get_value_by_cons_key("operator")

        project_name = other_vo.get_project_name()

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "sn": sn,
                "processName": station_id,
                "processData": json.dumps({
                    "diag": project_name,
                    "line": line_id,
                    "fixtureNo": fixture_no,
                    "operator": operator,
                }, ensure_ascii=False)
            }

            ret = xx_request(api_url_check, check_param)
            if ret.get('Result') != 'PASS':
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('Message')}")

        return ret_res

    def _v2_send_data_to_mes(self, data_vo: DataVo) -> dict:
        """
        20250427需求变更
        """
        is_upload_device_status = data_vo.get_value_by_cons_key("is_upload_device_status")

        if is_upload_device_status == "No":
            self.log.warning(f"未配置上传设备参数！")
            return self.x_response()

        review_path = data_vo.get_review_path()

        pcb_entity = data_vo.pcb_entity
        limit_dict = LimitedCapacityDict(200)  # 只保存最近200天的数据

        start_time_obj = pcb_entity.get_start_time()
        start_date = start_time_obj.strftime(xcons.FMT_DATE)

        start_h = start_time_obj.hour

        v2_start_time = int(xutil.CacheUtil.get("v2_start_time", "08:00").split(":")[0])
        v2_end_time = int(xutil.CacheUtil.get("v2_end_time", "20:00").split(":")[0])

        time_in_range = v2_start_time <= start_h < v2_end_time

        self.log.info(f"s:{v2_start_time}  e:{v2_end_time}  c:{start_h} ===> {time_in_range}")

        if time_in_range and not circle_list.is_exist_item(review_path):
            circle_list.add_item(review_path)

            pass_count = limit_dict.get(start_date, {}).get("total_pass_count", 0)
            ok_count = limit_dict.get(start_date, {}).get("total_ok_count", 0)
            ng_count = limit_dict.get(start_date, {}).get("total_ng_count", 0)

            if pcb_entity.get_final_result() == "PASS":
                pass_count += 1

            if pcb_entity.pcb_repair_result:
                ok_count += 1
            else:
                ng_count += 1

            limit_dict.set(start_date, {
                "total_pass_count": pass_count,
                "total_ok_count": ok_count,
                "total_ng_count": ng_count
            })

            self.log.info(f"data had cached!")

        else:
            self.log.info(f"data not cached!")

        v2_api_url_data = data_vo.get_value_by_cons_key("v2_api_url_data")
        v2_equipment_id = data_vo.get_value_by_cons_key("v2_equipment_id")
        v2_line = data_vo.get_value_by_cons_key("v2_line")
        v2_station = data_vo.get_value_by_cons_key("v2_station")
        v2_addr_ip = data_vo.get_value_by_cons_key("v2_addr_ip")
        v2_addr_mac = data_vo.get_value_by_cons_key("v2_addr_mac")

        total_pass_count = limit_dict.get(start_date, {}).get("total_pass_count", 0)
        total_ok_count = limit_dict.get(start_date, {}).get("total_ok_count", 0)
        total_ng_count = limit_dict.get(start_date, {}).get("total_ng_count", 0)

        total_count = total_ok_count + total_ng_count

        if total_count:
            pass_rate = round(total_pass_count / total_count * 100, 2)  # 直通率
            yield_rate = round(total_ok_count / total_count * 100, 2)  # 良率
        else:
            pass_rate = 0.0
            yield_rate = 0.0

        param_collection = [
            {"NAME": "passRate", "VALUE": pass_rate},
            {"NAME": "yield", "VALUE": yield_rate},
        ]

        count_ix = 0
        ng_filter_set = set()

        for board_entity in pcb_entity.yield_board_entity():
            for comp_entity in board_entity.yield_comp_entity():

                comp_ng_str = comp_entity.repair_ng_str

                if comp_entity.is_repair_ng():
                    count_ix += 1

                    if comp_ng_str not in ng_filter_set:
                        comp_ng_data2 = {
                            "NAME": f"defectiveInformation{count_ix}",
                            "VALUE": comp_ng_str
                        }

                        param_collection.append(comp_ng_data2)
                        ng_filter_set.add(comp_ng_str)

        data_param = {
            "EquipmentID": v2_equipment_id,
            "Line": v2_line,
            "Station": v2_station,
            "Date": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "AddrIP": v2_addr_ip,
            "AddrMAC": v2_addr_mac,
            "State": "运行",
            "ErrorCode": "",
            "Description": "",
            "SN": pcb_entity.get_unique_sn(),
            "ParamCollection": param_collection,
            "Production": total_count,
            "CT": pcb_entity.get_cycle_time(),
            "DT": "",
            "OEE": "",
        }

        try:
            xrequest.RequestUtil.post_json(v2_api_url_data, data_param)
        except Exception as e:
            self.log.error(f"v2接口异常：{e}")
            self.log.error(f"v2接口异常：{data_param}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        self._v2_send_data_to_mes(data_vo)

        # api_url_data1 = data_vo.get_value_by_cons_key("api_url_data1")
        api_url_data2 = data_vo.get_value_by_cons_key("api_url_data2")
        api_url_data3 = data_vo.get_value_by_cons_key("api_url_data3")
        ftp_host1 = data_vo.get_value_by_cons_key("ftp_host1")
        ftp_port1 = data_vo.get_value_by_cons_key("ftp_port1", to_int=True)
        ftp_user1 = data_vo.get_value_by_cons_key("ftp_user1")
        ftp_password1 = data_vo.get_value_by_cons_key("ftp_password1")
        ftp_path1 = data_vo.get_value_by_cons_key("ftp_path1")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img", not_null=True)

        # ftp_host2 = data_vo.get_value_by_cons_key("ftp_host2")
        # ftp_port2 = data_vo.get_value_by_cons_key("ftp_port2", to_int=True)
        # ftp_user2 = data_vo.get_value_by_cons_key("ftp_user2")
        # ftp_password2 = data_vo.get_value_by_cons_key("ftp_password2")
        # ftp_path2 = data_vo.get_value_by_cons_key("ftp_path2")

        station_id = data_vo.get_value_by_cons_key("station_id")
        line_id = data_vo.get_value_by_cons_key("line_id")
        fixture_no = data_vo.get_value_by_cons_key("fixture_no")
        operator = data_vo.get_value_by_cons_key("operator")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        api_url_data4 = data_vo.get_value_by_cons_key("api_url_data4")
        light_source = data_vo.get_value_by_cons_key("light_source")
        ftp_encoding = data_vo.get_value_by_cons_key("ftp_encoding")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        start_1 = pcb_entity.get_start_time()

        start_time = start_1.strftime(xcons.FMT_TIME_DEFAULT)
        time_file = start_1.strftime(xcons.FMT_TIME_DEFAULT7)
        time_file1 = start_1.strftime(xcons.FMT_TIME_FILE)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        comp_ng_data = {}
        ng_code = "PASS"
        comp_ng_data2 = []

        ret_res = self.x_response()

        pcb_result = pcb_entity.get_repair_result("PASS", "FAIL")

        self.log.info(f"{ftp_host1}, {ftp_user1}, {ftp_password1}, {ftp_port1}")
        ftp_client1 = FTPClient(
            ftp_host1,
            ftp_user1,
            ftp_password1,
            ftp_port1,
            encoding=ftp_encoding
        )
        ftp_client1.login()

        # ftp_client2 = FTPClient(ftp_host2, ftp_user2, ftp_password2, ftp_port2)
        # ftp_client2.login()

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        only_one_comp = ""
        comp_data_str = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                if not only_one_comp:
                    only_one_comp = comp_tag

                if comp_entity.is_repair_ng():
                    comp_ng_data[comp_tag] = comp_entity.repair_ng_str

                    if ng_code == "PASS":
                        ng_code = comp_entity.repair_ng_code

                    if not comp_ng_data2:
                        comp_ng_data2.append({
                            "name": comp_tag,
                            "value": comp_entity.repair_ng_code
                        })

                comp_data_str += csv_comp_panel_template.format(**{
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            # if not cd_ftp_path:
            #     ftp_client1.cd_or_mkdir(f"{ftp_path1}/BYDMESLOG/{time_file}")
            #     ftp_client2.cd_or_mkdir(f"{ftp_path2}/BYDMESLOG/{time_file}")
            #     cd_ftp_path = True
            #
            # board_result = board_entity.get_repair_result("PASS", "FAIL")
            # filename = f"BYD_PN_{barcode}_{board_result}_{station_id}_{time_file1}.jpg"
            #
            # ftp_client1.upload_content(filename, board_data)
            # ftp_client2.upload_content(filename, board_data)

        # self.log.info(f"1.调用物料不良绑定接口...")
        #
        # if not comp_ng_data:
        #     comp_ng_data[only_one_comp] = "OK"
        #
        # data_param1 = {
        #     "sn": pcb_sn,
        #     "processData": json.dumps(comp_ng_data, ensure_ascii=False)
        # }
        #
        # ret1 = xx_request(api_url_data1, data_param1)
        # if ret1.get('Result') != 'PASS':
        #     ret_res = self.x_response("false", f"mes接口异常，物料绑定失败，error：{ret1.get('Message')}")

        self.log.info(f"2.测试数据上传及过站...")
        data_param2 = {
            "sn": pcb_sn,
            "processName": station_id,
            "processData": json.dumps({
                "projectName": pcb_entity.project_name,
                "diag": pcb_entity.project_name,
                "line": line_id,
                "fixtureNo": fixture_no,
                "machineNo": machine_no,
                "operator": operator,
                "logfilePath": "123",
                "testResult": pcb_entity.get_repair_result("PASS", "FAIL"),
                "startTime": start_time,
                "stopTime": end_time,
                "ncCode": ng_code,
                "testDataList": comp_ng_data2
            }, ensure_ascii=False)
        }
        ret1 = xx_request(api_url_data2, data_param2)
        if ret1.get('Result') != 'PASS':
            ret_res = self.x_response("false", f"mes接口异常，测试数据上传及过站失败，error：{ret1.get('Message')}")

        self.log.info(f"3.开始上传csv文件...")
        pcb_data = {
            "device_name": machine_no,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "CompData": comp_data_str
        }

        pcb_content = csv_pcb_panel_template.format(**pcb_data)
        ftp_client1.cd_or_mkdir(f"{ftp_path1}/BYDMESLOG/{time_file}")

        filename = f"BYD_PN_{pcb_sn}_{pcb_result}_{station_id}_{time_file1}.csv"
        ftp_client1.upload_content(filename, pcb_content)

        try:
            self.log.info(f"getNoneNVSnInfo 查询非NV SN信息...")
            ret = xx_request(api_url_data4, {
                "sn": pcb_sn
            })

            byd_pb = ret.get("DATA", {}).get("BYDPN", "")
        except Exception as err:
            self.log.warning(f"查询非NV SN信息失败， err: {err}")
            byd_pb = ""

        product_name = byd_pb

        self.log.info(f"5. 接口发送上传记录...")
        data_param3 = {
            "LOGIN_ID": -1,
            "CLIENT_ID": 1,
            "DATA": {
                "SFC": pcb_sn,
                "LOG_PATH": f"/MACHINE/BYDMESLOG/{time_file}/{filename}",
                "RESULT": pcb_entity.get_repair_result(1, 0),
                "STATION": station_id,
                "TIME": start_time,
                "LINE": line_id,
                "EQ_NO": "LOG",
                "FIXTURE_NO": fixture_no,
                "PRODUCT_NAME": product_name,
            }
        }

        ret5 = xrequest.RequestUtil.get(api_url_data3, {
            'method': 'AddTestLogInfo',
            'param': json.dumps(
                data_param3, ensure_ascii=False, separators=(",", ":")
            )}, to_json=True)
        if ret5.get('RESULT') != 'PASS':
            ret_res = self.x_response("false", f"mes接口异常，AddTestLogInfo接口调用失败，error：{ret5.get('MESSAGE')}")

        self.log.info(f"4.开始上传整板图...")
        # all_pcb_image = pcb_entity.list_all_pcb_image()

        # self.log.info(f"pcb image len: {len(all_pcb_image)}")
        # if len(all_pcb_image) >= 1:

        for r_path in pcb_entity.review_path:
            if "/T" in r_path:
                board_side = "A"
            else:
                board_side = "B"

            pcb_src_image = f"{r_path}/thumbnail/{light_source}/thumb.jpg"

            if os.path.exists(pcb_src_image):
                # src_img1 = all_pcb_image[0]
                dst_filename = f"BYD_{product_name}_{pcb_sn}_{pcb_result}_{station_id}_{time_file1}_{board_side}.jpg"

                # 改成存储到本地
                img_dst_path = f"{save_path_img}/BYDIMGLOG/{time_file}"
                xutil.FileUtil.ensure_dir_exist(img_dst_path)

                dst_filepath = f"{img_dst_path}/{dst_filename}"
                xutil.FileUtil.copy_file(pcb_src_image, dst_filepath)

                self.log.info(f"5. 接口发送上传记录...")
                data_param3 = {
                    "LOGIN_ID": -1,
                    "CLIENT_ID": 1,
                    "DATA": {
                        "SFC": pcb_sn,
                        "LOG_PATH": f"/MACHINE/BYDIMGLOG/{time_file}/{dst_filename}",
                        "RESULT": pcb_entity.get_repair_result(1, 0),
                        "STATION": station_id,
                        "TIME": start_time,
                        "LINE": line_id,
                        "EQ_NO": "IMG",
                        "FIXTURE_NO": fixture_no,
                        "PRODUCT_NAME": product_name,
                    }
                }

                ret5 = xrequest.RequestUtil.get(api_url_data3, {
                    'method': 'AddTestLogInfo',
                    'param': json.dumps(
                        data_param3, ensure_ascii=False, separators=(",", ":")
                    )}, to_json=True)

                if ret5.get('RESULT') != 'PASS':
                    ret_res = self.x_response(
                        "false",
                        f"mes接口异常，AddTestLogInfo接口调用失败，error：{ret5.get('MESSAGE')}"
                    )

            else:
                self.log.warning(f"找不到需要上传的整板图！！！")

        ftp_client1.close()
        # ftp_client2.close()

        return ret_res

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        v2_api_url_data = other_vo.get_value_by_cons_key("v2_api_url_data")
        v2_equipment_id = other_vo.get_value_by_cons_key("v2_equipment_id")
        v2_line = other_vo.get_value_by_cons_key("v2_line")
        v2_station = other_vo.get_value_by_cons_key("v2_station")
        v2_addr_ip = other_vo.get_value_by_cons_key("v2_addr_ip")
        v2_addr_mac = other_vo.get_value_by_cons_key("v2_addr_mac")

        status_code_v3 = other_vo.get_status_code_v3()
        status_desc_v3 = other_vo.get_status_desc_v3()

        if status_code_v3 in [
            "1001",
            "1002",
            "1004",
        ]:
            # 运行
            state = "运行"
        elif status_code_v3 in [
            "1003",
            "1005",
            "3001",
        ]:
            # 待机、空闲
            state = "待机"

        elif status_code_v3 in [
            "2001",
            "2002",
            "3002",
            "3003",
            "3005",
            "3006",
            "3007",
            "5001",
            "5002",
        ]:
            state = "报警"

        else:
            self.log.warning(f"其他设备状态暂不处理！！！")
            return self.x_response()

        status_param = {
            "EquipmentID": v2_equipment_id,
            "Line": v2_line,
            "Station": v2_station,
            "Date": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "AddrIP": v2_addr_ip,
            "AddrMAC": v2_addr_mac,
            "State": state,
            "ErrorCode": status_code_v3,
            "Description": status_desc_v3,
            "SN": "",
            "ParamCollection": "",
            "Production": "",
            "CT": "",
            "DT": "",
            "OEE": "",
        }

        xrequest.RequestUtil.post_json(v2_api_url_data, status_param)

        return self.x_response()

    def get_project_name_by_sn(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        v2_check_project_url2 = other_vo.get_value_by_cons_key("v2_check_project_url2")
        v2_station = other_vo.get_value_by_cons_key("v2_station")

        barcode = ""
        for sn in other_vo.list_sn():
            barcode = sn
            break

        self.log.info(f"使用条码：{barcode}去获取程序名...")

        ret_str = xrequest.RequestUtil.get(
            v2_check_project_url2,
            {
                "SN": barcode,
                "processName": v2_station
            }, to_json=False
        )

        ret_json = json.loads(xutil.XmlUtil.get_xml_root_by_str(ret_str).text)
        if ret_json.get("Result") != "PASS":
            return self.x_response("false", f"mes接口异常，获取程序名失败，error：{ret_json.get('Message')}")

        ret_list = ret_json.get("DATA", [])

        program_name = ""

        for ret_obj in ret_list:
            if "softWareVersion" in ret_obj:
                program_name = ret_obj.get("softWareVersion", "").replace(".rar", "")
                break

        self.log.info(f"从mes获取到的程序名是：{program_name}")

        # # 3. 使用程序名取调用主软件的 主动换程序接口
        # ret3 = xrequest.SocketUtil.api_check_project(program_name)
        #
        # if ret3.get("ErrorCodeV2") != "0":
        #     return self.x_response("false", f"主软件接口异常，切换板式失败，error：{ret3.get('ErrorMessageV2')}")

        return self.x_response("true", program_name)


if __name__ == '__main__':
    # ret_str1 = """<string xmlns="http://tempuri.org/">{"Result":"FAIL","Code":0,"Message":"NGNG,CheckSNCanBeTest接口异常:线体锁不允许测试!","DATA":null}</string>"""
    # ret_1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1).text
    # ret = json.loads(ret_1)
    # print(ret)
    pass
