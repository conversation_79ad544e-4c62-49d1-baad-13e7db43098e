# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/4/24 下午2:45
# Author     ：sch
# version    ：python 3.8
# Description：百度技术/西安631所
"""
import os
import time
from typing import Any

from common import xrequest, xutil
from common.xsql import MySQLService
from common.xutil import x_response, log
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "baidu/xian631suo release v1.1.0.11",
        "device": "40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-24 17:22  上传数据 
date: 2023-05-15 14:55  修改图片保存路径 
date: 2023-07-04 11:35  增加上传整板、器件标准图
date: 2023-09-20 10:14  增加x,y坐标
date: 2024-04-15 09:52  从数据库获取条码（获取维修站补录的条码）
date: 2024-04-15 17:58  从数据库获取whole条码（获取维修站补录的条码）
date: 2024-04-15 20:07  add time sleep
date: 2024-05-14 14:49  增加参数：pcb_retrial_time，复判时间
date: 2024-05-16 11:47  增加备份一份数据到其他路径
""",
    }

    path = {
        "data_path": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    button = {
        "upload_offline_data": {
            "ui_name": "上传离线数据"
        }
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081"
        },
        "line": {
            "ui_name": "线别",
            "value": "LINE_TEST"
        },
        "group": {
            "ui_name": "组别",
            "value": ""
        },
        "device_name": {
            "ui_name": "机器名",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": "admin"
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "A000001"
        },

    }

    combo = {
        "is_save_image": {
            "ui_name": "是否保存图片",
            "item": ["是", "否"],
            "value": "是"
        },
        "send_mes_time_sleep": {
            "ui_name": "发送延时(s)",
            "item": ["0.1", "0.5", "1", "3", "5", "10", "30"],
            "value": "1"
        }
    }

    other_form = {}

    other_combo = {
        "is_save_bak_data": {
            "ui_name": "保存数据到备份路径",
            "item": ["Yes", "No"],
            "value": "No"
        },
    }

    other_path = {
        "bak_path": {
            "ui_name": "备份路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        line = data_vo.get_value_by_cons_key("line")
        group = data_vo.get_value_by_cons_key("group")
        device_name = data_vo.get_value_by_cons_key("device_name")
        operator = data_vo.get_value_by_cons_key("operator")
        order_id = data_vo.get_value_by_cons_key("order_id")
        data_path = data_vo.get_value_by_cons_key("data_path", not_null=True)
        bak_path = data_vo.get_value_by_cons_key("bak_path")
        is_save_image = data_vo.get_value_by_cons_key("is_save_image")
        send_mes_time_sleep = data_vo.get_value_by_cons_key("send_mes_time_sleep")
        is_save_bak_data = data_vo.get_value_by_cons_key("is_save_bak_data")

        self.log.info(f"time sleep start...")
        time.sleep(float(send_mes_time_sleep))
        self.log.info(f"time sleep end...")

        if is_save_bak_data == "Yes":
            if not bak_path:
                return x_response("false", "请先选择备份路径！")

        pcb_entity = data_vo.pcb_entity

        board_robot_ng_count = 0
        board_user_ng_count = 0

        pcb_comp_robot_ng_count = 0
        pcb_comp_user_ng_count = 0

        log.info(pcb_entity)

        board_data = []

        pcb_sn = pcb_entity.pcb_barcode
        time_now = xutil.DateUtil.get_datetime_now()
        date_now = time_now[:8]

        # 确保文件夹存在
        image_path = f"{data_path}/{date_now}/IMAGE"
        json_path = f"{data_path}/{date_now}/FILE"

        xutil.FileUtil.ensure_dir_exist(image_path)
        xutil.FileUtil.ensure_dir_exist(json_path)

        # 保存模板图
        t_report_xml = pcb_entity.get_pcb_t_report_xml()
        project_name = pcb_entity.project_name
        pcb_template, comp_template_map = xutil.parse_template_image(t_report_xml)

        self.log.info(f"1. 开始保存整板标准图中...")
        template_path = f"{data_path}/{date_now}/{project_name}"
        xutil.FileUtil.ensure_dir_exist(template_path)

        pcb_template_dst = f"{template_path}/template.jpg"
        if not os.path.exists(pcb_template_dst):
            xutil.FileUtil.copy_file(pcb_template, pcb_template_dst)

        if is_save_bak_data == "Yes":
            if not bak_path:
                return x_response("false", "请先选择备份路径！")

            image_path_bak = f"{bak_path}/{date_now}/IMAGE"
            json_path_bak = f"{bak_path}/{date_now}/FILE"
            template_path_bak = f"{bak_path}/{date_now}/{project_name}"

            xutil.FileUtil.ensure_dir_exist(image_path_bak)
            xutil.FileUtil.ensure_dir_exist(json_path_bak)
            xutil.FileUtil.ensure_dir_exist(template_path_bak)

            # 备份一份数据
            pcb_template_dst_bak = f"{template_path_bak}/template.jpg"
            if not os.path.exists(pcb_template_dst_bak):
                xutil.FileUtil.copy_file(pcb_template, pcb_template_dst_bak)
        else:
            image_path_bak = ""
            json_path_bak = ""
            template_path_bak = ""

        self.log.info(f"2. 开始保存器件标准图...")
        for comp_tag, comp_template_src in comp_template_map.items():

            if comp_template_src:
                comp_template_dst = f"{template_path}/template_{comp_tag}.jpg"
                if not os.path.exists(comp_template_dst):
                    xutil.FileUtil.copy_file(comp_template_src, comp_template_dst)

                if template_path_bak:
                    # 备份一份数据
                    comp_template_dst_bak = f"{template_path_bak}/template_{comp_tag}.jpg"
                    if not os.path.exists(comp_template_dst_bak):
                        xutil.FileUtil.copy_file(comp_template_src, comp_template_dst_bak)

        sql_service = MySQLService("127.0.0.1", "AIS400")

        ng_image_list = []
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = sql_service.get_barcode_by_board_uuid(board_entity.board_uuid)
                log.info(f"从数据库获取到条码：{barcode}")

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not barcode:
                barcode = time_now

            if not board_entity.robot_result:
                # 检测NG
                board_robot_ng_count += 1

                if board_entity.repair_result:
                    # 复判NG
                    board_user_ng_count += 1

            pcb_comp_robot_ng_count += board_entity.comp_robot_ng_number
            pcb_comp_user_ng_count += board_entity.comp_repair_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                comp_image = comp_entity.image_path
                comp_tag = comp_entity.designator

                if not comp_entity.robot_result and comp_image:
                    # 复制图片到指定目录

                    dst_image = f"{image_path}/comp_{barcode}_{board_no}_{time_now}_{comp_tag}.png"

                    dst_image_bak = f"{image_path_bak}/comp_{barcode}_{board_no}_{time_now}_{comp_tag}.png"

                    ng_image_list.append({
                        # "barcode": barcode,
                        # "no": board_no,
                        # "tag": comp_tag,
                        "dst_image": dst_image,
                        "src_image": comp_image,
                        "dst_image_bak": dst_image_bak if image_path_bak else ""
                    })
                else:
                    dst_image = ""

                comp_standard_img = comp_template_map.get(comp_tag, "")
                if comp_standard_img:
                    comp_template_dst = f"{template_path}/template_{comp_tag}.jpg"
                else:
                    comp_template_dst = ""

                comp_data.append({
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": dst_image,
                    "comp_template_image": comp_template_dst,
                    "comp_XPos": comp_entity.x_pos,
                    "comp_YPos": comp_entity.y_pos,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_entity.board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        sql_service.close()

        if not pcb_sn:
            pcb_sn = time_now

        pcb_image_path = pcb_entity.get_pcb_t_image()

        pcb_dst_image = f"{image_path}/pcb_{pcb_sn}_{time_now}.jpg"

        pcb_data = {
            "pcb_sn": pcb_sn,
            # "pcb_image_path": pcb_image_path,
            "pcb_image_path": pcb_dst_image,
            "pcb_template_image_path": f"{data_path}/{date_now}/{project_name}/template.jpg",
            "pcb_data_path": pcb_entity.get_pcb_pcb_t_review_path(),
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_retrial_time": str(pcb_entity.get_review_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": "PASS" if pcb_entity.pcb_repair_result else "FAIL",
            "pcb_robot_result": "PASS" if pcb_entity.pcb_robot_result else "FAIL",
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_user_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_count,
            "cons_line": line,
            "cons_group": group,
            "cons_device_name": device_name,
            "cons_operator": operator,
            "cons_order_id": order_id,
            "board_data": board_data
        }

        # 1. 保存文档
        log.info(f"3. 保存json文档到指定目录中...")
        xutil.FileUtil.dump_json_to_file(f"{json_path}/{pcb_sn}_{time_now}.json", pcb_data)

        if json_path_bak:
            # 备份一份数据
            xutil.FileUtil.dump_json_to_file(f"{json_path_bak}/{pcb_sn}_{time_now}.json", pcb_data)

        if is_save_image == "是":
            # 2. 保存大图
            log.info(f"4. 保存大图到指定目录中...")
            xutil.FileUtil.copy_file(pcb_image_path, pcb_dst_image)

            if image_path_bak:
                # 备份一份数据
                pcb_dst_image_bak = f"{image_path_bak}/pcb_{pcb_sn}_{time_now}.jpg"
                xutil.FileUtil.copy_file(pcb_image_path, pcb_dst_image_bak)

            # 3. 保存NG器件图
            for item in ng_image_list:
                # sn = item.get("barcode")
                # no = item.get("no")
                # tag = item.get("tag")
                dst_image = item.get("dst_image")
                src_image = item.get("src_image")
                dst_image_bak = item.get("dst_image_bak")

                log.info(f"5. 保存NG器件图到指定目录中...")
                xutil.FileUtil.copy_file(src_image, dst_image)

                if dst_image_bak:
                    # 备份一份数据
                    xutil.FileUtil.copy_file(src_image, dst_image_bak)

        try:
            if api_url:
                ret = xrequest.RequestUtil.post_json(api_url, pcb_data)
                if ret.get("code") != 200:
                    return x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")
            else:
                log.warning(f"接口URL未填写，暂不调用接口上传！")

        except Exception as err:
            log.error(f"上传失败的数据缓存在本地; {err}")

            cache_data = "./cache_data"
            if not os.path.exists(cache_data):
                os.makedirs(cache_data)

            xutil.FileUtil.dump_json_to_file(f"{cache_data}/{pcb_sn}_{time_now}_offline.json", pcb_data)

        return x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url = btn_vo.get_value_by_cons_key("api_url")
        btn_key = btn_vo.get_btn_key()

        if btn_key == "upload_offline_data":
            cache_path = "./cache_data"
            if not os.path.exists(cache_path):
                os.makedirs(cache_path)

            cache_data_list = os.listdir(cache_path)

            success_count = 0
            err_count = 0
            for file in cache_data_list:

                if file.endswith(".json"):
                    try:
                        cache_file = f"./cache_data/{file}"
                        log.info(f"正在上传离线数据：{cache_file}")
                        pcb_data = xutil.FileUtil.load_json_file(cache_file)
                        xrequest.RequestUtil.post_json(api_url, pcb_data)
                        os.remove(cache_file)
                        success_count += 1

                    except Exception as err:
                        log.warning(f"上传离线数据失败：{err}")
                        err_count += 1

            other_param.log_info(f"上传成功：{success_count} 上传失败：{err_count}")

        return x_response()
