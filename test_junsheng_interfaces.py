#!/usr/bin/env python
# -*-coding:utf-8 -*-

"""
测试君胜MES接口参数格式
"""

import json

def test_interface_formats():
    """测试各个接口的参数格式"""
    
    print("=== 君胜MES接口参数格式测试 ===\n")
    print("包含5个接口（其中customMaterialFixDefects为扩展接口）：\n")

    # 1. 零件进站接口 - customMaterialTrackIn
    print("1. 零件进站接口 (customMaterialTrackIn):")
    track_in_param = {
        "prebSnr": "008xxxxxxxxx1",
        "mesServer": "Stg/Prd",
        "mesStation": "SMT_Coating_xxx"
    }
    print(json.dumps(track_in_param, indent=2, ensure_ascii=False))
    print()

    # 2. 程式检查接口 - customVerifyMaterialRecipe
    print("2. 程式检查接口 (customVerifyMaterialRecipe):")
    verify_recipe_param = {
        "prebSnr": "008xxxxxxxxx1",
        "mesServer": "Stg/Prd",
        "mesStation": "SMT_Coating_xxx",
        "program": "13251-xxx-xxxx.mpp"
    }
    print(json.dumps(verify_recipe_param, indent=2, ensure_ascii=False))
    print()

    # 3. 上传测试结果接口 - customUploadTestResult
    print("3. 上传测试结果接口 (customUploadTestResult):")
    upload_result_param = {
        "prebSnr": "Stencil#xxxxxxxxx1",
        "mesServer": "Stg/Prd",
        "mesStation": "SMT_Coating_xxx",
        "operator": "Operator例\"123\"",
        "programName": "programName 例:\"13251-xxxx_test\"",
        "testStartTime": "testStartTime例:\"2024-11-11 08:30:00\"",
        "testEndTime": "testEndTime例:\"2024-11-11 08:30:00\"",
        "parameter1": "例40.00",
        "parameter2": "例40.00",
        "parameter3": "例40.00",
        "parameterX": "例40.00",
        "result": "例Pass/Fail",
        "reportPath": "url路径，例:\"M:\\abc\\ff.txt\"",
        "picturePath": "url路径，例:\"M:\\abc\\ff.jpg\"",
        "reportPath1": "url路径，例:\"M:\\abc\\ff.txt\"",
        "picturePath1": "url路径，例:\"M:\\abc\\ff.jpg\""
    }
    print(json.dumps(upload_result_param, indent=2, ensure_ascii=False))
    print()

    # 4. 上传测试不良接口 - customMaterialEndWithDefects
    print("4. 上传测试不良接口 (customMaterialEndWithDefects):")
    end_with_defects_param = {
        "mesServer": "Prd",
        "UID": "30880000995038",
        "ResourceName": "AOI_Line4-1",
        "OperationResult": "Fail",
        "IsToPerformMoveNext": True,
        "DefectParameters": [
            {
                "Reason": "matching value",
                "ReferenceDesignator": "T101-2",
                "Remark": "7,9-11"
            },
            {
                "Reason": "matching value",
                "ReferenceDesignator": "R102",
                "Remark": "12,9-11"
            }
        ],
        "Reason": "matching value",
        "ReferenceDesignator": "T101-2",
        "Remark": "7,9-11",
        "reportPath": "url路径，例：M:\\abc\\ff.txt",
        "picturePath": "url路径，例：M:\\abc\\ff.jpg"
    }
    print(json.dumps(end_with_defects_param, indent=2, ensure_ascii=False))
    print()

    # 5. 上传复判不良接口 - customMaterialFixDefects (扩展接口)
    print("5. 上传复判不良接口 (customMaterialFixDefects) - 扩展接口:")
    fix_defects_param = {
        "mesServer": "Prd",
        "UID": "30880000995038",
        "ResourceName": "AOI_Line4-1",
        "OperationResult": "Fail",
        "IsToPerformMoveNext": True,
        "DefectParameters": [
            {
                "Reason": "matching value",
                "ReferenceDesignator": "T101-2",
                "Remark": "7,9-11"
            }
        ],
        "Reason": "matching value",
        "ReferenceDesignator": "T101-2",
        "Remark": "7,9-11",
        "reportPath": "",
        "picturePath": ""
    }
    print(json.dumps(fix_defects_param, indent=2, ensure_ascii=False))
    print()
    
    print("=== 预期响应格式 ===")
    expected_response = {
        "result": True,
        "errorInfo": "",
        "msg": "success",
        "value": "0"
    }
    print(json.dumps(expected_response, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_interface_formats()
