# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2024/9/25 下午2:29
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import os

from common.xutil import log


def match_project_name(part_name: str, board_side: str) -> list:
    """
    模糊匹配程序名
    :param part_name: 料号
    :param board_side: 板面
    :return:
    """
    project_path = f"/home/<USER>/aoi/program/projects"

    file_list_dir = os.listdir(project_path)

    filter_project_file = []

    for file in file_list_dir:
        filepath = f"{project_path}/{file}"

        if not os.path.isdir(filepath):
            continue

        if part_name not in file:
            continue

        if board_side not in file:
            continue

        filter_project_file.append(file)

    return filter_project_file


if __name__ == '__main__':
    ret = match_project_name("6670170000", "BOT")
    print(ret)
