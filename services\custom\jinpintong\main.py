# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/23 下午2:57
# Author     ：sch
# version    ：python 3.8
# Description：深圳金品通 - 天地通
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

txt_template = """PCB编号={test_time}
产品名称={project_name}
面={board_side}
测试结果={final_result}
机器编号={device_code}
条码={barcode}
PCB Ng 总数={comp_robot_ng_number}
误判Ng数量={comp_repair_repass_number}
确认NG数量={comp_repair_real_ng_number}
操作员={operator}
班次={work_shift}
线别={line}
元件数量={comp_total_number}
屏蔽=0
子板编号={board_id}
维修=False
工单={order_id}
大板条码={pcb_sn}
关联条码=
关联条码类型=
PcName={pc_name}
AoiTestTime={cycle_time}
AoiResult={test_result}
NumberTime={test_time_1}
TrackId={trace_index}
AppendMOnce=
BlockQty=0
PCBTestDate={test_date}
PCBTestTime={test_time2}
"""


class Engine(BaseEngine):
    version = {
        "title": "jinpintong release v1.0.0.2",
        "device": "203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-23 16:25  保存本地txt文档
date: 2023-12-02 09:15  文档后缀改为`RP1`
""", }

    path = {
        "save_path": {
            "ui_name": "txt保存路径",
            "value": "",
        },
        "bak_path": {
            "ui_name": "txt备份路径",
            "value": "",
        },
    }

    form = {
        "device_sn": {
            "ui_name": "机器编号",
            "value": "",
        },
        "line": {
            "ui_name": "线别",
            "value": "",
        },
        "pc_name": {
            "ui_name": "PcName",
            "value": "ALD7976-PC",
        },
        "order_id": {
            "ui_name": "工单",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
    }

    combo = {
        "board_side": {
            "ui_name": "板面",
            "item": ["A", "B"],
            "value": "A"
        },
        "work_shift": {
            "ui_name": "班次",
            "item": ["白班", "夜班"],
            "value": "白班"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        bak_path = data_vo.get_value_by_cons_key("bak_path")

        device_sn = data_vo.get_value_by_cons_key("device_sn")
        order_id = data_vo.get_value_by_cons_key("order_id")
        operator = data_vo.get_value_by_cons_key("operator")
        line = data_vo.get_value_by_cons_key("line")
        pc_name = data_vo.get_value_by_cons_key("pc_name")

        board_side = data_vo.get_value_by_cons_key("board_side")
        work_shift = data_vo.get_value_by_cons_key("work_shift")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        d1 = pcb_entity.get_start_time()

        time_file = d1.strftime(xcons.FMT_TIME_FILE)
        test_time = d1.strftime("%Y-%m-%d %H:%M:%S.000")

        pcb_sn = pcb_entity.pcb_barcode
        project_name = pcb_entity.project_name

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            # for comp_entity in board_entity.yield_comp_entity():
            #     print(comp_entity)

            if pcb_entity.track_index == 1:
                trace_index = "A"
            else:
                trace_index = "B"

            txt_content = txt_template.format(**{
                "test_time": test_time,
                "project_name": project_name,
                "board_side": board_side,
                "final_result": board_entity.get_repair_result("Pass", "NG"),
                "device_code": device_sn,
                "barcode": barcode,
                "comp_total_number": board_entity.comp_total_number,
                "comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "comp_repair_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "comp_repair_real_ng_number": board_entity.comp_repair_ng_number,
                "operator": operator,
                "work_shift": work_shift,
                "line": line,
                "board_id": board_no,
                "order_id": order_id,
                "pcb_sn": pcb_sn,
                "pc_name": pc_name,
                "cycle_time": pcb_entity.get_cycle_time(),
                "test_result": board_entity.get_robot_result("Pass", "NG"),
                "test_time_1": time_file,
                "trace_index": trace_index,
                "test_date": d1.strftime(xcons.FMT_DATE),
                "test_time2": d1.strftime(xcons.FMT_TIME),
            })

            dst_filename1 = f"{save_path}/{project_name}_{time_file}.RP1"
            dst_filename2 = f"{bak_path}/{project_name}_{time_file}.RP1"
            xutil.FileUtil.write_content_to_file(dst_filename1, txt_content)
            xutil.FileUtil.write_content_to_file(dst_filename2, txt_content)

        return self.x_response()
