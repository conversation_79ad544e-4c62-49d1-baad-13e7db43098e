# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/23 下午5:57
# Author     ：sch
# version    ：python 3.8
# Description：安捷利
"""

from typing import Any

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import filter_v3_status_code, x_response
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

status_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                  xmlns:ser="http://service.webservice.services.intmes.com/">
    <soapenv:Header/>
    <soapenv:Body>
        <ser:MACRUNSTATUS>
            <ser:MACRUNSTATUSIN>
                <CLOSETIME>{CLOSETIME}</CLOSETIME>
                <FAULTCODE>{FAULTCODE}</FAULTCODE>
                <FAULTDETAIL>{FAULTDETAIL}</FAULTDETAIL>
                <MACHINEID>{MACHINEID}</MACHINEID>
                <MACHINENAME>{MACHINENAME}</MACHINENAME>
                <OPENTIME>{OPENTIME}</OPENTIME>
                <RUNSTATUS>{RUNSTATUS}</RUNSTATUS>
                <UPLOADTIME>{UPLOADTIME}</UPLOADTIME>
            </ser:MACRUNSTATUSIN>
        </ser:MACRUNSTATUS>
    </soapenv:Body>
</soapenv:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <AOITESTinsert xmlns="http://MesWebService.org/">
            <AOITEST>
                <LINENAME>{LINENAME}</LINENAME>
                <MACHINESN>{MACHINESN}</MACHINESN>
                <PROGRAMNAME>{PROGRAMNAME}</PROGRAMNAME>
                <BARCODE>{BARCODE}</BARCODE>
                <PANELBARCODE>{PANELBARCODE}</PANELBARCODE>
                <PCBSTATE>{PCBSTATE}</PCBSTATE>
                <REVIEWPCBSTATE>{REVIEWPCBSTATE}</REVIEWPCBSTATE>
                <COMPONENTTOTAL>{COMPONENTTOTAL}</COMPONENTTOTAL>
                <PINTOTAL>{PINTOTAL}</PINTOTAL>
                <INSPECTSTARTTIME>{INSPECTSTARTTIME}</INSPECTSTARTTIME>
                <INSPECTENDTIME>{INSPECTENDTIME}</INSPECTENDTIME>
                <CYCLETIME>{CYCLETIME}</CYCLETIME>
                <REVIEWENDTIME>{REVIEWENDTIME}</REVIEWENDTIME>
                <PERSONREVIEW>{PERSONREVIEW}</PERSONREVIEW>
                <ARRAYTOTEL>{ARRAYTOTEL}</ARRAYTOTEL>
                <F_POINTS>{F_POINTS}</F_POINTS>
            </AOITEST>
            <ListAOITEST_DETAIL>{comp_data_str}
            </ListAOITEST_DETAIL>
        </AOITESTinsert>
    </soap:Body>
</soap:Envelope>"""

row_template = """
                <AOITEST_DETAIL>
                    <PINNUMBER>{PINNUMBER}</PINNUMBER>
                    <ARRAYNUMBER>{ARRAYNUMBER}</ARRAYNUMBER>
                    <PARTSNAME>{PARTSNAME}</PARTSNAME>
                    <PARTNUMBER>{PARTNUMBER}</PARTNUMBER>
                    <INSPECTRESULT>{INSPECTRESULT}</INSPECTRESULT>
                    <REVIEWRESULT>{REVIEWRESULT}</REVIEWRESULT>
                    <DEFECTTYPE>{DEFECTTYPE}</DEFECTTYPE>
                    <DEFECTCODE>{DEFECTCODE}</DEFECTCODE>
                    <IMAGEADDRESS>{IMAGEADDRESS}</IMAGEADDRESS>
                </AOITEST_DETAIL>"""

data_template_v2 = """<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:mes="http://MesWebService.org/">
    <soap:Header/>
    <soap:Body>
        <mes:AOITESTinsert>
            <mes:AOITEST>
                <mes:LINENAME>{LINENAME}</mes:LINENAME>
                <mes:MACHINESN>{MACHINESN}</mes:MACHINESN>
                <mes:PROGRAMNAME>{PROGRAMNAME}</mes:PROGRAMNAME>
                <mes:BARCODE>{BARCODE}</mes:BARCODE>
                <mes:PANELBARCODE>{PANELBARCODE}</mes:PANELBARCODE>
                <mes:PCBSTATE>{PCBSTATE}</mes:PCBSTATE>
                <mes:REVIEWPCBSTATE>{REVIEWPCBSTATE}</mes:REVIEWPCBSTATE>
                <mes:COMPONENTTOTAL>{COMPONENTTOTAL}</mes:COMPONENTTOTAL>
                <mes:PINTOTAL>{PINTOTAL}</mes:PINTOTAL>
                <mes:INSPECTSTARTTIME>{INSPECTSTARTTIME}</mes:INSPECTSTARTTIME>
                <mes:INSPECTENDTIME>{INSPECTENDTIME}</mes:INSPECTENDTIME>
                <mes:CYCLETIME>{CYCLETIME}</mes:CYCLETIME>
                <mes:REVIEWENDTIME>{REVIEWENDTIME}</mes:REVIEWENDTIME>
                <mes:PERSONREVIEW>{PERSONREVIEW}</mes:PERSONREVIEW>
                <mes:ARRAYTOTEL>{ARRAYTOTEL}</mes:ARRAYTOTEL>
                <mes:F_POINTS>{F_POINTS}</mes:F_POINTS>
            </mes:AOITEST>
            <mes:ListAOITEST_DETAIL>
                <!--Zero or more repetitions:-->{comp_data_str}
            </mes:ListAOITEST_DETAIL>
        </mes:AOITESTinsert>
    </soap:Body>
</soap:Envelope>"""

row_template2 = """
                <mes:AOITEST_DETAIL>
                    <mes:PINNUMBER>{PINNUMBER}</mes:PINNUMBER>
                    <mes:ARRAYNUMBER>{ARRAYNUMBER}</mes:ARRAYNUMBER>
                    <mes:PARTSNAME>{PARTSNAME}</mes:PARTSNAME>
                    <mes:PARTNUMBER>{PARTNUMBER}</mes:PARTNUMBER>
                    <mes:INSPECTRESULT>{INSPECTRESULT}</mes:INSPECTRESULT>
                    <mes:REVIEWRESULT>{REVIEWRESULT}</mes:REVIEWRESULT>
                    <mes:DEFECTTYPE>{DEFECTTYPE}</mes:DEFECTTYPE>
                    <mes:DEFECTCODE>{DEFECTCODE}</mes:DEFECTCODE>
                    <mes:IMAGEADDRESS>{IMAGEADDRESS}</mes:IMAGEADDRESS>
                </mes:AOITEST_DETAIL>"""

data_template_v3 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <BFH_AOI xmlns="http://MesWebService.org/">
            <AOITEST>
                <LINENAME>{LINENAME}</LINENAME>
                <MACHINESN>{MACHINESN}</MACHINESN>
                <PROGRAMNAME>{PROGRAMNAME}</PROGRAMNAME>
                <BARCODE>{BARCODE}</BARCODE>
                <PANELBARCODE>{PANELBARCODE}</PANELBARCODE>
                <PCBSTATE>{PCBSTATE}</PCBSTATE>
                <REVIEWPCBSTATE>{REVIEWPCBSTATE}</REVIEWPCBSTATE>
                <COMPONENTTOTAL>{COMPONENTTOTAL}</COMPONENTTOTAL>
                <PINTOTAL>{PINTOTAL}</PINTOTAL>
                <INSPECTSTARTTIME>{INSPECTSTARTTIME}</INSPECTSTARTTIME>
                <INSPECTENDTIME>{INSPECTENDTIME}</INSPECTENDTIME>
                <CYCLETIME>{CYCLETIME}</CYCLETIME>
                <REVIEWENDTIME>{REVIEWENDTIME}</REVIEWENDTIME>
                <PERSONREVIEW>{PERSONREVIEW}</PERSONREVIEW>
                <ARRAYTOTEL>{ARRAYTOTEL}</ARRAYTOTEL>
                <F_POINTS>{F_POINTS}</F_POINTS>
            </AOITEST>
            <ListAOITEST_DETAIL>{comp_data_str}
            </ListAOITEST_DETAIL>
        </BFH_AOI>
    </soap:Body>
</soap:Envelope>"""

comp_template_v3 = """
                <AOITEST_DETAIL>
                    <PINNUMBER>{PINNUMBER}</PINNUMBER>
                    <ARRAYNUMBER>{ARRAYNUMBER}</ARRAYNUMBER>
                    <PARTSNAME>{PARTSNAME}</PARTSNAME>
                    <PARTNUMBER>{PARTNUMBER}</PARTNUMBER>
                    <INSPECTRESULT>{INSPECTRESULT}</INSPECTRESULT>
                    <REVIEWRESULT>{REVIEWRESULT}</REVIEWRESULT>
                    <DEFECTTYPE>{DEFECTTYPE}</DEFECTTYPE>
                    <DEFECTCODE>{DEFECTCODE}</DEFECTCODE>
                    <IMAGEADDRESS>{IMAGEADDRESS}</IMAGEADDRESS>
                </AOITEST_DETAIL>"""


def update_device_status(api_url, fmt_data: dict) -> dict:
    """
    上传设备状态
    :param api_url:
    :param fmt_data:
    :return:
    """
    fault_code = fmt_data.get("FAULTCODE")

    if not fault_code:
        fmt_data["FAULTCODE"] = global_data.get("last_fault_code", "03")
        fmt_data["FAULTDETAIL"] = global_data.get("last_fault_str", "停止检查")
        fmt_data["RUNSTATUS"] = global_data.get("last_run_status", 0)

    status_content = status_template.format(**fmt_data)
    ret_str = xrequest.RequestUtil.post_xml(api_url, status_content)

    root_status = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    ret_status = root_status[0][0][0].find("STATUS").text
    ret_code = root_status[0][0][0].find("ERRCODE").text

    if ret_status != "Y":
        return x_response("false", f"mes接口异常，ERRCODE：{ret_code}")

    global_data["last_fault_code"] = fmt_data["FAULTCODE"]
    global_data["last_fault_str"] = fmt_data["FAULTDETAIL"]
    global_data["last_run_status"] = fmt_data["RUNSTATUS"]

    return {}


class Engine(ErrorMapEngine):
    version = {
        "title": "anjieli release v1.0.0.6",
        "device": "501",
        "feature": ["设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-24 17:10  设备状态，上传数据
date: 2024-11-04 14:48  修改参数
date: 2024-11-21 14:48  修改AOITESTinsert接口参数
date: 2025-01-17 16:48  jira:34364 修改上传数据接口: AOITESTinsert --> BFH_AOI
date: 2025-01-20 11:57  jira:34364 修改BARCODE与PANELBARCODE参数位置
date: 2025-01-20 19:06  jira:34364 返回None代表成功 返回其他值代表失败
""",
    }

    other_form = {
        "api_url_device_status": {
            "ui_name": "状态信息传输接口",
            "value": "http://192.168.103.59:11211/ws/webservice-MacRunStatus"
        },
        "api_url_device_data": {
            "ui_name": "产品信息传输接口",
            "value": "http://173.254.16.252:8081/MesWebService.asmx"
        },
    }

    other_combo = {
        "is_cron_upload": {
            "ui_name": "是否开启定时上传",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "upload_freq": {
            "ui_name": "定时上传频率(分钟)",
            "item": list(map(str, range(1, 100, 2))),
            "value": "3"
        }
    }

    form = {
        "line_name": {
            "ui_name": "线别",
            "value": "",
        },
        "machine_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "machine_id": {
            "ui_name": "设备编号",
            "value": "",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        is_cron_upload = other_vo.get_value_by_cons_key("is_cron_upload")
        upload_freq = other_vo.get_value_by_cons_key("upload_freq", to_int=True)

        main_window.set_cron_setting(is_cron_upload == "Yes", upload_freq * 60)

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_device_status = other_vo.get_value_by_cons_key("api_url_device_status")
        machine_name = other_vo.get_value_by_cons_key("machine_name")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        device_status_str = other_vo.get_device_status_str()
        old_device_status_code = other_vo.get_old_device_status_code()

        if old_device_status_code in ["02", "03", "20", "21", "22"]:
            state = 0
        elif old_device_status_code in ["11"]:
            state = 1
        elif old_device_status_code in ["10", "12", "13", "23", "99"]:
            state = 2
        else:
            self.log.warning(f"other status code: {old_device_status_code}")
            return self.x_response()

        if x_res := update_device_status(api_url_device_status, {
            "CLOSETIME": "",
            "FAULTCODE": old_device_status_code,
            "FAULTDETAIL": device_status_str,
            "MACHINEID": machine_id,
            "MACHINENAME": machine_name,
            "OPENTIME": "",
            "RUNSTATUS": state,
            "UPLOADTIME": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE),
        }):
            return x_res

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_device_data = data_vo.get_value_by_cons_key("api_url_device_data")
        line_name = data_vo.get_value_by_cons_key("line_name")
        machine_id = data_vo.get_value_by_cons_key("machine_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()
        has_error = False
        error_msg = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += comp_template_v3.format(**{
                    "PINNUMBER": comp_entity.designator,
                    "ARRAYNUMBER": board_no,
                    "PARTSNAME": comp_entity.designator,
                    "PARTNUMBER": comp_entity.part,
                    "INSPECTRESULT": comp_entity.get_final_result("OK", "NG", "NG"),
                    "REVIEWRESULT": comp_entity.get_final_result("OK", "OK", "NG"),
                    "DEFECTTYPE": comp_entity.robot_ng_str,
                    "DEFECTCODE": comp_entity.robot_ng_code,
                    "IMAGEADDRESS": comp_entity.image_path,
                })

            data_content = data_template_v3.format(**{
                "LINENAME": line_name,
                "MACHINESN": machine_id,
                "PROGRAMNAME": pcb_entity.project_name,
                "BARCODE": pcb_entity.pcb_barcode,
                "PANELBARCODE": barcode,
                "PCBSTATE": pcb_entity.get_final_result("OK", "OK", "NG"),
                "REVIEWPCBSTATE": pcb_entity.get_final_result("OK", "OK", "NG"),
                "COMPONENTTOTAL": board_entity.comp_total_number,
                "PINTOTAL": board_entity.comp_total_number,
                "INSPECTSTARTTIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1),
                "INSPECTENDTIME": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT1),
                "CYCLETIME": pcb_entity.get_cycle_time(),
                "REVIEWENDTIME": pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT1),
                "PERSONREVIEW": pcb_entity.repair_user,
                "ARRAYTOTEL": pcb_entity.board_count,
                "F_POINTS": f"{round(board_entity.comp_robot_ng_number / board_entity.comp_total_number * 100, 2)}%",
                "comp_data_str": comp_data_str,
            })

            ret2 = xrequest.RequestUtil.post_soap(
                api_url_device_data,
                data_content,
                soap_action="http://MesWebService.org/BFH_AOI"
            )
            if ret2 is None:
                self.log.info(f"拼版 {barcode} 返回成功 返回None")
            else:
                self.log.warning(f"拼版 {barcode} 返回不为None")

        return self.x_response()
        # try:
        #     root2 = xutil.XmlUtil.get_xml_root_by_str(ret2)
        #     root4 = xutil.XmlUtil.get_xml_root_by_str(root2[0][0][0].text)
        #
        #     ret_status = root4.find("SATUS").text
        #     ret_msg = root4.find("MSG").text
        #
        #     if ret_status != "OK":
        #         return self.x_response("false", f"mes接口异常，error：{ret_msg}")
        #
        # except Exception as xml_err:
        #     self.log.info(f"XML解析异常，视为成功")
        #     return self.x_response()
        #
        # return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_device_status = other_vo.get_value_by_cons_key("api_url_device_status")
        machine_name = other_vo.get_value_by_cons_key("machine_name")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        update_device_status(api_url_device_status, {
            "CLOSETIME": "",
            "FAULTCODE": "",
            "FAULTDETAIL": "",
            "MACHINEID": machine_id,
            "MACHINENAME": machine_name,
            "OPENTIME": "",
            "RUNSTATUS": "",
            "UPLOADTIME": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE),
        })


if __name__ == '__main__':
    #     ret1 = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    #    <soap:Body>
    #       <ns2:MACRUNSTATUSResponse xmlns:ns2="http://service.webservice.services.intmes.com/">
    #          <return>
    #             <ERRCODE>NG</ERRCODE>
    #             <MACHINEID>?</MACHINEID>
    #             <STATUS>Y</STATUS>
    #             <UPLOADTIME>?</UPLOADTIME>
    #          </return>
    #       </ns2:MACRUNSTATUSResponse>
    #    </soap:Body>
    # </soap:Envelope>"""
    #
    #     root1 = xutil.XmlUtil.get_xml_root_by_str(ret1)
    #     ret_status = root1[0][0][0].find("STATUS").text
    #     ret_code = root1[0][0][0].find("ERRCODE").text
    #     print(ret_status)
    #     print(ret_code)
    #     ret2 = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    #    <soap:Body>
    #       <AOITESTinsertResponse xmlns="http://MesWebService.org/">
    #          <AOITESTinsertResult><![CDATA[<return><MACHINESN>string</MACHINESN><PROGRAMNAME>string</PROGRAMNAME><BARCODE>string</BARCODE><PANELBARCODE>1</PANELBARCODE><TIMESTMP>20210812155159</TIMESTMP><SATUS>OK</SATUS><MSG>插入成功</MSG></return>]]></AOITESTinsertResult>
    #       </AOITESTinsertResponse>
    #    </soap:Body>
    # </soap:Envelope>"""
    #
    #     root2 = xutil.XmlUtil.get_xml_root_by_str(ret2)
    #     root4 = xutil.XmlUtil.get_xml_root_by_str(root2[0][0][0].text)
    #
    #     ret_status = root4.find("SATUS").text
    #     ret_msg = root4.find("MSG").text
    #
    #     print(ret_status)
    #     print(ret_msg)
    pass
