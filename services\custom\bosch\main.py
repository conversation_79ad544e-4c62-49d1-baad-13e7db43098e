# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/02/10 14:16
# Author     ：chencb
# version    ：python 3.8
# Description：苏州博世 https://jira.cvte.com/browse/ATAOI_2019-37766
"""
import socket
import struct
import subprocess
import threading
from typing import Any
from PyQt5.QtWidgets import Q<PERSON>rame, QBoxLayout
from common import xenum
from common.xcons import DEVICE_STATUS_EN_MAP, INSPECTOR, REPAIR
from common.xrequest import post_msg_to_repair
from common.xutil import OtherUtil
from entity.MesEntity import PcbEntity, BoardEntity
from services.custom.bosch.event_xml_manager import *
from services.custom.bosch.switch_button import SwitchButton
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

# 以下code参考DEVICE_STATUS_V3
# key为errorNo，value为errorType(1:Error, 2:Warning)
PLC_ERROR_DICT = {
    '2002': '1', '2003': '1', '3003': '1', '3004': '1', '3005': '1', '3006': '1',
    '3007': '1', '3008': '1', '4001': '1', '4002': '1', '5001': '1', '5002': '1'
}
ABORTED_LIST = ['2001']
PART_MISSING_CODE = '3001'
JAM_CODE = '3002'
CHANGE_OVER_CODE = '1005'
CHANGE_OVER_FAIL = '3008'
START_INSPECTING = '1002'

# 定义发送partReceived的来源
FROM_AOI = 'aoi'  # 相机扫码发送
FROM_CHECK_SN = 'check_sn'  # 条码校验发送
FROM_VI = 'vi'  # 维修站发送

UNKNOWN_CODE = '-999'


class Engine(ErrorMapEngine):
    version = {
        "customer": ["苏州博世", "bosch"],
        "version": "release v1.0.0.12",
        "device": "AIS330",
        "feature": ["相机扫码校验", "板式切换", "发送数据到MES", "获取坏板信息", "发送各种状态事件"],
        "author": "chenchongbing",
        "release": """
date: 2025-05-08 16:30  jira:37766 条码校验、板式切换、获取坏板信息、发送数据到MES、发送各种状态事件
date: 2025-05-13 16:00  增加维修站扫码发送partRecived流程
date: 2025-05-15 15:40  维修站和AOI设备的MES数据做分离
date: 2025-05-16 17:20  修改维修站换型提示易懂和美观些，以及正常过站不用弹出提示
date: 2025-05-20 11:50  优化整体数据分离和事件分离框架，补齐维修站的一些事件流，更改相关提示信息让操作人员易懂
date: 2025-05-28 15:50  减少日志打印、维修站ping通才发送定时事件、增加光栅报警、修复板式切换会使用上次板式名以及复判完成MES未能正常返回过站信息
date: 2025-05-29 17:05  未进行过扫码发送MES数据不能记录为发送过数据；非引导板式切换时（比如自检）不触发partProcessingStarted事件
date: 2025-05-30 11:30  发送数据时增加不是同一块板以及mes返回出错或者partForStation=false时的提示，并不再发送数据
date: 2025-05-30 17:00  换线状态和发送开始检测事件优化；上一块板partForStation=false，需要清空typeNo确保下一块板可以正常获取板式
date: 2025-06-07 11:15  增加MES服务开关功能和红绿标记图标显示，通过自定义按钮实现
date: 2025-06-09 15:45  MES服务关闭只针对离线测试场景，如果扫码请求MES时返回MES服务已关闭提醒
date: 2025-06-13 15:00  拼板数量不一致或者板式名不一致时，不能发送数据给MES
""", }

    combo = {
        "tracing_type": {
            "ui_name": "tracing type",
            "ui_name_en": "tracing type",
            "item": [
                "Group",
                "One Part",
            ],
            "value": "Group"
        },
    }

    form = {
        "lineNo": {
            "ui_name": "Line No",
            "ui_name_en": "Line No",
            "value": ""
        },
        "statNo": {
            "ui_name": "Station No",
            "ui_name_en": "Station No",
            "value": ""
        },
        "statIdx": {
            "ui_name": "Station index",
            "ui_name_en": "Station index",
            "value": ""
        },
        "fuNo": {
            "ui_name": "Function No",
            "ui_name_en": "Function No",
            "value": ""
        },
        "workPos": {
            "ui_name": "Work Pos",
            "ui_name_en": "Work Pos",
            "value": ""
        },
        "workPos_vi": {
            "ui_name": "Work Pos VI",
            "ui_name_en": "Work Pos VI",
            "value": ""
        },
        "toolPos": {
            "ui_name": "Tool Pos",
            "ui_name_en": "Tool Pos",
            "value": ""
        },
        "processNo": {
            "ui_name": "Process No",
            "ui_name_en": "Process No",
            "value": ""
        },
        "processName": {
            "ui_name": "Process Name",
            "ui_name_en": "Process Name",
            "value": ""
        },
        "processName_vi": {
            "ui_name": "Process Name VI",
            "ui_name_en": "Process Name VI",
            "value": ""
        },
        "machineID": {
            "ui_name": "machine ID",
            "ui_name_en": "machine ID",
            "value": ""
        },
        "systemName": {
            "ui_name": "system Name",
            "ui_name_en": "system Name",
            "value": ""
        },
        "userID": {
            "ui_name": "user ID",
            "ui_name_en": "user ID",
            "value": ""
        },
        "mes_ip": {
            "ui_name": "MES IP",
            "ui_name_en": "MES IP",
            "value": ""
        },
        "mes_port": {
            "ui_name": "MES Port",
            "ui_name_en": "MES Port",
            "value": ""
        },
    }

    other_form = {
        "machineDescription": {
            "ui_name": "Equipment Description",
            "ui_name_en": "Equipment Description",
            "value": ""
        },
        "machineSupplier": {
            "ui_name": "Supplier",
            "ui_name_en": "Supplier",
            "value": ""
        },
        "machineType": {
            "ui_name": "Machine Type",
            "ui_name_en": "Machine Type",
            "value": ""
        },
        "machineProductionYear": {
            "ui_name": "Year of Production",
            "ui_name_en": "Year of Production",
            "value": ""
        },
        "serialNumber": {
            "ui_name": "Equipment Serial Number",
            "ui_name_en": "Equipment Serial Number",
            "value": ""
        },
        "machineSystemSoftware": {
            "ui_name": "System Software",
            "ui_name_en": "System Software",
            "value": ""
        },
        "softwareServicePack": {
            "ui_name": "System Software Service-Pack",
            "ui_name_en": "System Software Service-Pack",
            "value": ""
        },
        "plant": {
            "ui_name": "Bosch Production Plant",
            "ui_name_en": "Bosch Production Plant",
            "value": ""
        },
        "machineDokuNo": {
            "ui_name": "Plant Documentation Number",
            "ui_name_en": "Plant Documentation Number",
            "value": ""
        },
        "machineSoftwareVersion": {
            "ui_name": "Equipment Software Version",
            "ui_name_en": "Equipment Software Version",
            "value": ""
        },
        "interfaceSoftwareVersionNo": {
            "ui_name": "MES Interface Software Version Number",
            "ui_name_en": "MES Interface Software Version Number",
            "value": ""
        },
        "xmlVersion": {
            "ui_name": "OpCon XML Version",
            "ui_name_en": "OpCon XML Version",
            "value": "2.3"
        },
        "machineTime": {
            "ui_name": "machine time W3C format",
            "ui_name_en": "machine time W3C format",
            "value": ''
        },
        "machineNumberOfProductionArea": {
            "ui_name": "Equipment internal Process Area",
            "ui_name_en": "Equipment internal Process Area",
            "value": ""
        },
    }

    button = {
        "switch_mes": {
            "customized": True,
            "ui_name": "开关MES服务"
        }
    }

    def __init__(self):
        # 用于读取配置项
        self.other_vo = None
        # 记录上次设备状态，状态切换时做对应事件处理，格式[status_code, status_desc]
        self.error_record = None
        # 当前是否是切换板式中
        self.change_over_flag = False
        # 板式切换完成后需再发送一次partReceived事件
        self.send_part_received_again = False
        # --------- 由于国内使用暂时默认为中文 ---------
        # bosch版本默认需为英文版
        # self.set_lang_to_en()
        # bosch默认需发送坏板数据
        # self.common_config["sendmes_setting3"] = 'Send Defective Boards'
        self.common_config["sendmes_setting3"] = xenum.SendMesSetting3.Send
        # ------------------------------------------
        # AOI和维修站数据独立，维修站等收到维修站条码时再创建
        self.event_xml_manager_aoi = EventXmlManager(INSPECTOR)
        self.event_xml_manager_vi = EventXmlManager(REPAIR)
        # 5分钟定时任务日志
        self.print_5min_timer_log = True
        self.switch_button = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        # 缓存到对象中供后续获取参数使用
        self.other_vo = other_vo

        main_window.config_data['other_form']['machineTime']['value'] = DateUtil.get_datetime_now(
            xcons.FMT_TIME_DEFAULT8)

        # 用于dataUploadRequired事件(The present set-up status),5分钟循环一次
        main_window.config_data["app_setting"]["custom_interval_cron"] = True
        main_window.config_data["app_setting"]["custom_interval_time"] = int(5 * 60)  # 5分钟

        # 用于dataUploadRequired事件(current requipment identification data)，一天发送一次，凌晨1点发送
        main_window.config_data["app_setting"]["custom_interval_cron3"] = True
        main_window.config_data["app_setting"]["custom_fixed_time"] = {'minute': '0', 'hour': '1'}  # 每天凌晨1点发送

    def get_event_xml_manager(self, inspect_type):
        event_xml_manager = self.event_xml_manager_aoi if inspect_type == INSPECTOR else self.event_xml_manager_vi
        return event_xml_manager

    def switch_program(self, inspect_type, change_over):
        """
        切换板式事件执行流程：plcChangeOverStarted获取板式 -> 返回主软件 -> 发送plcChangeOver切换中
        于其关联的还有一个事件dataDownloadRequired：这个事件主要用于主软件重启时发送使用，当前我们无法知晓主软件的运行状态
        dataDownloadRequired这个事件当前我们先不做场景发送
        """
        # change_over为true才需要走change over流程
        if change_over:
            # 切换板式前先发送"plcChangeOverStarted"
            resp = self._send_event(MesEvent.PLC_CHANGE_OVER_STARTED, inspect_type)
            code = resp.get('code')
            if code != '0':
                # 无法切换板式，返回错误给主软件
                aoi_resp = self.x_response("false", resp.get('msg'))
                return aoi_resp
            msg = '取mes返回的程序名'
        else:
            msg = '取缓存里的程序名'

        event_xml_manager = self.get_event_xml_manager(inspect_type)
        program_name = event_xml_manager.get_public_param('Prog1.Name')
        if not program_name:
            aoi_resp = self.x_response("false", "need changeover, but Prog1.Name is None")
            return aoi_resp

        self.log.info(f"{msg} ：{program_name}")

        self.change_over_flag = True

        # 返回参数给AOI执行切换板式,主软件的板式命名都是带后缀.001，需要添加上
        program_name += '.001'
        ret_param = {
            "projectName": program_name,
            "topSideVersion": '',
            "bottomSideVersion": '',
        }
        self.log.info(f"返回主软件进行板式切换 ：{ret_param}")
        ret_param_str = OtherUtil.obj_to_json(ret_param)
        aoi_resp = self.x_response("true", ret_param_str)

        # 异步发送切换板式事件
        if change_over:
            self._send_event_async(MesEvent.PLC_CHANGE_OVER, inspect_type)

        return aoi_resp

    def _reset_status_before_inspector(self, inspect_type):
        """
        每次新板检测前需要重置下一些状态信息和mes参数，避免上个板数据串到新板
        """
        event_xml_manager = self.get_event_xml_manager(inspect_type)
        event_xml_manager.clear_public_params()
        self.change_over_flag = False
        self.error_record = None
        self.send_part_received_again = False

    def _ping_repair_station(self):
        # self.log.info(f'维修站发送dataUploadRequired事件前检查下维修站是否能连通')
        repair_ip = self.other_vo.config_data_json.get('app_setting', {}).get("repair_ip", "")
        if not repair_ip:
            self.log.info(f'维修站ip地址未配置，不再发送dataUploadRequired')
            return False

        # self.log.info(f'ping 维修站ip：{repair_ip}')
        try:
            # 使用 subprocess 调用 ping 命令
            # -c 4 表示发送4个ICMP包，如果你的环境不同，可以调整这个参数
            # devnull 用于忽略ping命令输出到stdout和stderr的信息
            res = subprocess.run(['ping', '-c', '1', repair_ip], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL,
                                 timeout=5)
            # 根据 ping 命令的结果进行判断
            if res.returncode == 0:
                return True
            else:
                self.log.info(f'维修站ping失败，不再发送dataUploadRequired')
                return False
        except Exception as e:
            self.log.info(f'维修站ping失败，不再发送dataUploadRequired：{e}')
            return False

    def _post_msg_to_repair(self, msg: str):
        self.log.info(f'推送弹窗提示信息给维修站')
        repair_ip = self.other_vo.config_data_json.get('app_setting', {}).get("repair_ip", "")
        post_msg_to_repair(repair_ip, False, msg)
        return self.x_response()

    def _check_project_by_barcode(self, pcb_barcode: str, from_type):
        self.log.info(f'发送partRecived事件的类型：{from_type}')

        # 由于设备状态的进板和出板时机不可用，把jam和partMissing事件恢复放在这里
        if from_type == FROM_AOI and self.error_record:
            error_no = self.error_record[0]
            # 如果上次是partMissing，重新进板表示缺料已解除，则发送恢复事件
            if error_no == PART_MISSING_CODE:
                event = MesEvent.PLC_PARTS_MISSING
                self._send_event(event, INSPECTOR)
                self.error_record = None
            # 如果上次是jam，重新进板表示上个板已出板，则发送恢复事件
            elif error_no == JAM_CODE:
                event = MesEvent.PLC_JAM
                self._send_event(event, INSPECTOR)
                self.error_record = None

        inspect_type = REPAIR if from_type == FROM_VI else INSPECTOR
        event_xml_manager = self.get_event_xml_manager(inspect_type)

        # 重新检测新板时，需清空上次板的一些数据
        if from_type in [FROM_AOI, FROM_VI]:
            self._reset_status_before_inspector(inspect_type)
            event_xml_manager.set_pcb_sn(pcb_barcode)

        event_data = {
            'identifier': pcb_barcode,
        }

        resp = self._send_event(MesEvent.PART_RECEIVED, inspect_type, event_data)
        if resp.get('code') == '0':
            # 获取workPart中4个参数值，并执行对应的业务逻辑
            work_part = event_xml_manager.get_public_param('workPart')
            part_for_station = work_part.get('partForStation')
            change_over = work_part.get('changeOver')
            if part_for_station == 'true':
                if change_over == 'true':  # partForStation="true" changeOver="true",切换板式
                    if from_type == FROM_CHECK_SN:
                        # 如果是板式切换完成后从条码校验流程中再次发送partReceived还要进行板式切换，则提示错误
                        err_msg = "this is partReceived event after change over, Can't do the changeover again"
                        aoi_resp = self.x_response("false", err_msg)
                    else:
                        aoi_resp = self.switch_program(inspect_type, True)
                        if from_type == FROM_VI:
                            # 维修站没有切换板式动作，获取板式名后就标记切换完成
                            self.change_over_flag = False
                            # 如果是发送维修站之前进行一次MES信息核实，则只显示板式切换信息，不进行实际板式切换
                            if aoi_resp.get('result'):
                                program_name = event_xml_manager.get_public_param('Prog1.Name')
                                # 构建workPart标签
                                attr_str = ' '.join([f'{key}="{value}"' for key, value in work_part.items()])
                                xml_tag = f'<workPart {attr_str} />'
                                err_msg = f"\n此板是新板式：【 {program_name} 】，请确认！！！\n\npartRecived mes return changeOver=true :{xml_tag}"
                                # 由于维修站不需要进行板式切换，提示为新板式后，就可以进入检测流程
                                self._send_event_async(MesEvent.PART_PROCESSING_STARTED, inspect_type, event_data)
                            else:
                                err_msg = f"\nMES获取板式出错，请核实原因！！\n\nchange over fail: {aoi_resp.get('string')}"
                            aoi_resp = self.x_response("false", err_msg)
                        elif from_type == FROM_AOI:
                            # 只要changeOver为true，都需再次发送partReceived
                            # 由于板式切换完，会正常走条码校验流程，统一在条码校验流程中再发送一次partReceived流程
                            self.send_part_received_again = True
                else:  # partForStation="true" changeOver="false" 校验通过，允许进站
                    if from_type == FROM_AOI:
                        # 正常继续检测，拿缓存的Prog1.Name进行板式切换
                        aoi_resp = self.switch_program(inspect_type, False)
                    else:
                        # 板正常过站，不做任何提示
                        aoi_resp = self.x_response()

                    # 条码校验是第二次partReceived返回的，已经在测试，直接标记开始测试
                    # 维修站因为不需要设备检测，也直接标记开始检测
                    # 检测软件直接切换程序不再有换线事件，直接在这里发送开始检测事件
                    event_data = {
                        'identifier': pcb_barcode,
                    }
                    self._send_event_async(MesEvent.PART_PROCESSING_STARTED, inspect_type, event_data)

                return aoi_resp
            else:
                err_msg = f"\n此板已过站，请确认是否还要重新测试！！！\n\nmes return 【partForStation=false】 : {resp.get('msg')}"
                return self.x_response("false", err_msg)
        else:
            err_msg = f"\nMES返回报错，请核实出错原因！\n\nmes return returnCode！=0: {resp.get('msg')}"
            return self.x_response("false", err_msg)

    def check_project_by_barcode(self, other_vo: OtherVo, param: dict):
        """
        AOI相机扫码或者维修站条码枪扫码时，需要发送partRecived事件流程
        ----------------
        aoi param参数如下：
        {
            "funcName":"CheckProjectByBarcode",
            "barcode": [[条码]],  # 返回的是一个列表
            "trackIndex":0,
        }
        返回值中string的定义：
        result为true时且需要板式切换时string是一个字典的序列化，正常检测时string为空，false时是异常信息
        {
            "projectName": "切换板式名",
            "topSideVersion": '',
            "bottomSideVersion": '',
        }
        ------------------
        维修站参数如下：
        {
            "funcName": "CheckProjectByBarcode",
            "barcode":[[条码]],
           "InspectMesType": "repair"
        }
        返回true给维修站，提示信息由配置器主动发送给维修站弹窗
        """
        inspect_type = param.get("InspectMesType", INSPECTOR)
        if self.switch_button.is_mes_off():
            if inspect_type == INSPECTOR:
                err_msg = 'MES服务关闭中，若要进行正常的跑板测试，请先开启MES服务！！！'
            else:
                err_msg = 'MES服务关闭中，若要进行正常的数据复判，请先开启MES服务！！！'
            return self.x_response('false', err_msg)

        barcode_list = param.get('barcode')
        pcb_barcode = barcode_list[0][0]

        from_type = FROM_VI if inspect_type == REPAIR else FROM_AOI
        ret = self._check_project_by_barcode(pcb_barcode, from_type)

        # 把MES返回的信息同步到维修站进行显示
        if from_type == FROM_VI and not ret.get('result'):
            ret = self._post_msg_to_repair(ret.get('string'))

        return ret

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        if self.switch_button.is_mes_off():
            return self.x_response()

        resp = self.x_response()
        if self.send_part_received_again:
            self.send_part_received_again = False
            # 只要是partReceived，都统一使用相机扫码的条码, 条码校验只出现在AOI设备上
            pcb_sn = self.event_xml_manager_aoi.get_pcb_sn()
            resp = self._check_project_by_barcode(pcb_sn, FROM_CHECK_SN)

        return resp

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        """
        分类不同的状态，并发送对应的状态事件到MES，详细状态码信息可查看：DEVICE_STATUS_V3
        """
        if self.switch_button.is_mes_off():
            return self.x_response()

        status_code = other_vo.get_status_code_v3()
        # 增加下面逻辑是因为主软件中文信息显示在statusDesc
        # 示例：'statusCode': '', 'statusCodeV3': '3008', 'statusDesc': '引导换线失败', 'statusDescV3': 'BootLoaderFailed',
        status_desc = other_vo.get_status_desc()
        if status_desc:
            # 映射成英文
            status_desc = DEVICE_STATUS_EN_MAP.get(status_desc, status_desc)
        else:
            status_desc = other_vo.get_status_desc_v3()

        # 上次有状态记录时，先处理下状态切换时要发送的事件
        if self.error_record:
            error_no = self.error_record[0]
            error_text = self.error_record[1]
            # 重新开始检测且上次事件是PLC_ERROR，则重新发送下事件,并标记acknowledged
            if status_code == START_INSPECTING and error_no in PLC_ERROR_DICT:
                # 事件发生后，重新开始检测，代表error已解除，代表已知晓error
                event = MesEvent.PLC_ERROR
                error_type = PLC_ERROR_DICT[error_no]
                event_data = {
                    'errorNo': error_no,
                    'errorText': error_text,
                    'errorType': error_type,
                    'errorState': '1',  # Error is acknowledged
                }
                # 发送设备状态都是AOI，直接传AOI的manger
                self._send_event(event, INSPECTOR, event_data)
                self.error_record = None
                return self.x_response()

        is_jam = 'false'
        is_part_missing = 'false'
        event = None
        event_data = {}
        need_record = False
        # 发送设备状态只有AOI设备有
        pcb_sn = self.event_xml_manager_aoi.get_pcb_sn()
        if status_code in PLC_ERROR_DICT:
            event = MesEvent.PLC_ERROR
            error_type = PLC_ERROR_DICT[status_code]
            event_data = {
                'errorNo': status_code,
                'errorText': status_desc,
                'errorType': error_type,
                'errorState': '0',  # 0 - Error is occurred
            }
            need_record = True
        elif status_code in ABORTED_LIST:
            event = MesEvent.PART_PROCESSING_ABORTED
            event_data = {
                'identifier': pcb_sn,
            }
        elif status_code == PART_MISSING_CODE:
            event = MesEvent.PLC_PARTS_MISSING_STARTED
            is_part_missing = 'true'
            need_record = True
        elif status_code == JAM_CODE:
            event = MesEvent.PLC_JAM_STARTED
            is_jam = 'true'
            need_record = True
        elif status_code == CHANGE_OVER_CODE:
            if self.change_over_flag:
                self.change_over_flag = False
                # changeover为false，不需要二次发送partreceived，板式切换完直接发送开始检测事件
                if not self.send_part_received_again and pcb_sn:
                    event_data = {
                        'identifier': pcb_sn,
                    }
                    self._send_event_async(MesEvent.PART_PROCESSING_STARTED, INSPECTOR, event_data)
        elif status_code == CHANGE_OVER_FAIL:
            self.change_over_flag = False

        if event:
            resp = self._send_event(event, INSPECTOR, event_data)
            if is_jam == 'true' or is_part_missing == 'true':
                # jam和part missing时需额外再多发这个事件
                change_flag = 'true' if self.change_over_flag else 'false'
                event_data = {
                    'changeFlag': change_flag,
                    'jam': is_jam,
                    'partMissing': is_part_missing,
                    'errorNo': status_code,
                    'errorText': status_desc,
                }
                resp = self._send_event(MesEvent.DATA_UPLOAD_REQUIRED_10, INSPECTOR, event_data)

            code = resp.get('code')
            if code == '0':
                response = self.x_response()
            else:
                response = self.x_response('false', resp.get('msg'))
        else:
            response = self.x_response()

        if need_record:
            self.error_record = [status_code, status_desc]
        else:
            self.error_record = None

        return response

    def get_bad_board_info(self, other_vo: OtherVo, other_param: Any):
        """
        partReceived返回的workItems中如果有partForStation=false，需要返回给AOI告诉它不测
        这个接口需要主软件中勾选【坏拼板检查通过MES】
        """
        if self.switch_button.is_mes_off():
            return self.x_response()

        # 获取坏板只有AOI接口调用
        work_items = self.event_xml_manager_aoi.get_public_param('workItems')
        bad_list = []
        for item in work_items:
            if item.get('partForStation') == 'false':
                bad_list.append(item.get('pos'))

        msg = ','.join(bad_list)
        return self.x_response("true", msg)

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        """
        定时任务1：
        用于dataUploadRequired事件(The present set-up status)
        5分钟循环发送一次或者jam、partMissing事件触发时发送
        """
        if self.switch_button.is_mes_off():
            return

        # 这个事件关联的值主要是以下值，进行对应赋值
        change_flag = 'true' if self.change_over_flag else 'false'
        is_jam = 'false'
        is_part_missing = 'false'
        error_no = '0'  # 无错误时为0
        error_text = 'acknowledge'  # 无错误时为0
        if self.error_record:
            status_code = self.error_record[0]
            status_desc = self.error_record[1]
            if status_code in [JAM_CODE, PART_MISSING_CODE]:
                is_jam = 'true' if status_code == JAM_CODE else 'false'
                is_part_missing = 'true' if status_code == PART_MISSING_CODE else 'false'
                error_no = status_code
                error_text = status_desc

        event_data = {
            'changeFlag': change_flag,
            'jam': is_jam,
            'partMissing': is_part_missing,
            'errorNo': error_no,
            'errorText': error_text,
        }

        # 定时任务，维修站和AOI设备都要发送
        self._send_event(MesEvent.DATA_UPLOAD_REQUIRED_10, INSPECTOR, event_data)
        # 维修站电脑能Ping通才发送
        if self._ping_repair_station():
            self._send_event(MesEvent.DATA_UPLOAD_REQUIRED_10, REPAIR, event_data)
        self.print_5min_timer_log = False

    def custom_cron_function3(self, other_vo: OtherVo, main_window, other_param: Any):
        """
        用于dataUploadRequired事件(current requipment identification data)
        一天发送一次，凌晨1点发送
        """
        if self.switch_button.is_mes_off():
            return

        event_data = {
            'machineDescription': other_vo.get_value_by_cons_key("machineDescription"),
            'machineSupplier': other_vo.get_value_by_cons_key("machineSupplier"),
            'machineType': other_vo.get_value_by_cons_key("machineType"),
            'machineProductionYear': other_vo.get_value_by_cons_key("machineProductionYear"),
            'serialNumber': other_vo.get_value_by_cons_key("serialNumber"),
            'machineSystemSoftware': other_vo.get_value_by_cons_key("machineSystemSoftware"),
            'softwareServicePack': other_vo.get_value_by_cons_key("softwareServicePack"),
            'plant': other_vo.get_value_by_cons_key("plant"),
            'machineDokuNo': other_vo.get_value_by_cons_key("machineDokuNo"),
            'machineSoftwareVersion': other_vo.get_value_by_cons_key("machineSoftwareVersion"),
            'interfaceSoftwareVersionNo': other_vo.get_value_by_cons_key("interfaceSoftwareVersionNo"),
            'xmlVersion': other_vo.get_value_by_cons_key("xmlVersion"),
            'machineTime': DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT8),
            'machineNumberOfProductionArea': other_vo.get_value_by_cons_key("machineNumberOfProductionArea"),
        }

        # 定时任务，维修站和AOI设备都要发送
        self._send_event(MesEvent.DATA_UPLOAD_REQUIRED_105, INSPECTOR, event_data)
        # 维修站电脑能Ping通才发送
        if self._ping_repair_station():
            self._send_event(MesEvent.DATA_UPLOAD_REQUIRED_105, REPAIR, event_data)

    def _send_data_to_socket(self, ip: str, port: int, data: str, wait_response=True, print_log=True):
        """
        发送数据到socket服务端
        wait_response：是否等待服务器响应数据，bool类型，默认为True
        """

        def _print_log(msg):
            if print_log:
                log.info(msg)

        _print_log(f"tcp请求地址: {ip}:{port}")
        formatted_xml = XmlUtil.format_xml(data)
        _print_log(f"请求数据:{formatted_xml}")

        sk = socket.socket()
        sk.settimeout(5)
        sk.connect((ip, port))

        body = data.encode('utf-8')
        # 计算包体长度
        length = len(body)
        # log.info(f"要发送xml长度(字节数):{length}")
        # 包头协议：将一个xml长度以大端序（>：Big-Endian）的4字节无符号整数（I）格式打包成二进制数据
        header = struct.pack('>I', length + 4)
        # log.info(f"发送的包头为:{header}")
        # log.info(f"发送的包体为：{body}")
        packet = header + body
        sk.sendall(packet)
        _print_log(f"数据已发送给MES")

        xml_data = ''
        if wait_response:
            # Step 1: 接收前 4 字节，解析出数据长度
            _print_log(f'等待接收MES返回数据……')
            header = sk.recv(4)  # 接收前 4 字节
            if header:
                _print_log(f'接收到包头：{header}')
                xml_length = struct.unpack('>I', header)[0] - 4  # 解析长度并减去头部 4 字节
                _print_log(f'接收的xml长度(字节数)：{xml_length}')
                xml_data_byte = sk.recv(xml_length)
                # log.info(f'接收到xml数据（字节）:{xml_data_byte}')
                xml_data = xml_data_byte.decode('utf-8')
                formatted_xml = XmlUtil.format_xml(xml_data)
                _print_log(f'接收到xml数据:{formatted_xml}')
        else:
            _print_log(f"服务器本身定义不返回响应数据，直接关闭socket连接")

        sk.close()
        # log.info(f'socket连接已关闭')
        return xml_data

    def _send_event_async(self, event: MesEvent, inspect_type: str, event_data: dict = None):
        """
        异步执行发送事件
        """
        timer = threading.Timer(0.2, self._send_event, args=[event, inspect_type, event_data])
        timer.start()

    def _send_event(self, event: MesEvent, inspect_type: str, event_data: dict = None):
        """
        发送对应事件给MES服务器，会根据对应event类型生成对应事件的xml数据以及决定是否等待响应
        如果这个事件有独有的参数时，需要通过event_data传递过来，如果是公共参数就不需要，底层会统一赋值
        return: event_response
        """
        mes_ip = self.other_vo.get_value_by_cons_key("mes_ip")
        mes_port = self.other_vo.get_value_by_cons_key("mes_port")

        if not mes_ip or not mes_port:
            raise Exception("Please fill mes ip or mes port")

        event_xml_manager = self.get_event_xml_manager(inspect_type)

        # 发送请求前先更新公共参数
        event_xml_manager.update_public_params(self.other_vo)
        xml_data = event_xml_manager.get_event_xml(event, event_data)
        if not xml_data:
            err_msg = f"事件类型{event}暂未支持"
            return event_response(code=UNKNOWN_CODE, msg=err_msg)

        try:
            # 获取此事件是否需要等待响应
            wait_response = event_xml_manager.is_wait_response(event)
            send_data_params = {
                'ip': mes_ip,
                'port': int(mes_port),
                'data': xml_data,
                'wait_response': wait_response
            }
            # 5分钟定时器日志只打印一次，减少日志量
            print_log = True
            if event == MesEvent.DATA_UPLOAD_REQUIRED_10:
                send_data_params['print_log'] = self.print_5min_timer_log
                print_log = self.print_5min_timer_log
            if print_log:
                log.info("------------------------bosch event start-----------------------")
                log.info(f"send event: {event.value[0]}，from：{inspect_type}")
            ret_xml = self._send_data_to_socket(**send_data_params)
            if ret_xml:
                resp = event_xml_manager.parse_response_xml(event, ret_xml)
            else:
                # 不需要等待响应场景
                resp = event_response()
            if print_log:
                log.info("------------------------bosch event end-----------------------")
        except Exception as e:
            self.log.info(f'本地网络异常，事件：{event.value[0]} 发送失败：{e}')
            resp = event_response(code=UNKNOWN_CODE, msg=f"sent event：{event.value[0]} fail，error:{e}")
        return resp

    def _parse_pin_no(self, report_xml, board_no, parent_id, ng_alg_name, ng_alg_code):
        pin_no = ''
        # 解析pinNo，当前引脚都是采用InspSolderAi（焊锡智能算法），直接hardcode在代码中判断
        if ng_alg_name != 'InspSolderAi':
            return pin_no

        root = XmlUtil.get_xml_root_by_file(report_xml)
        for board in root.iterfind('.//board'):
            board_id = board.get('id')
            if board_id == board_no:
                for component in board.iterfind('components/component'):
                    comp_id = component.find('id').text
                    if comp_id == parent_id:
                        for child in component.iterfind('children/child'):
                            defect = child.find('defect').text
                            pin_type = child.find('type').text
                            child_id = child.find('id').text
                            # 查找和父器件一样不良代码的引脚器件id(引脚类型为102)
                            if defect == ng_alg_code and pin_type == '102':
                                pin_no = child_id
                                break
                        break
                break
        return pin_no

    def _generate_comp_list(self, board_entity: BoardEntity, scrap_ack, inspect_type):
        system_name = self.other_vo.get_value_by_cons_key("systemName")
        user_id = self.other_vo.get_value_by_cons_key("userID")
        report_xml = board_entity.pcb_entity.get_pcb_t_report_xml()

        board_no = board_entity.board_no
        comp_list = []
        for comp_entity in board_entity.yield_comp_entity():
            # 只生成机器检测NG的数据
            if inspect_type == INSPECTOR:
                is_ng = comp_entity.is_robot_ng()
            else:
                is_ng = comp_entity.is_repair_ng()
            if not is_ng:
                continue

            all_alg_ng_results = []
            robot_ng_code = comp_entity.robot_ng_code
            test_name, test_val = '', ''
            for alg_entity in comp_entity.yield_alg_entity():
                # 只记录不良代码结果的算法数据
                if alg_entity.result == robot_ng_code:
                    test_name = alg_entity.test_name
                    test_val = alg_entity.test_val

                # checkAllFeature需要记录所有NG算法
                if alg_entity.result != '0':  # 当前bosch的设备为AOI类型
                    all_alg_ng_results.append(alg_entity.result)

            pin_no = self._parse_pin_no(report_xml, board_no, comp_entity.comp_id_real, test_name, robot_ng_code)

            # 0 机器检测NG;1 复判PASS;2 复判后NG;3 机器检测PASS
            if inspect_type == INSPECTOR:
                ack = '3' if comp_entity.robot_result else '0'
            else:
                ack = '1' if comp_entity.repair_result else '2'
            comp = {
                'posNo': comp_entity.designator,
                'pos': board_no,
                'package': comp_entity.package,
                'typeNo': comp_entity.part,
                'windowNo': comp_entity.comp_id_real,
                'pinNo': pin_no,
                'checkType': test_name,
                'checkValue': test_val,
                'checkFeature': robot_ng_code,
                'checkAllFeature': list(set(all_alg_ng_results)),  # 去重
                'errorCode': robot_ng_code,
                'ack': ack,
                'scrapAck': scrap_ack,  # not tested scrap flag is active
                'systemName': system_name,  # 界面配置
                'userID': user_id,  # 界面配置
            }

            # 以下是不良图片的相关数据，如果bosch后续不需要到时再去除，现在先保留
            comp.update({
                'errorImage': comp_entity.image_path,
                'errorText': comp_entity.robot_ng_str,
                'imageNo': comp_entity.comp_id_real,  # 参考图片保存的文件名
                'errorXPosImage': comp_entity.x_offset,
                'errorYPosImage': comp_entity.y_offset,
                'errorXSize': comp_entity.width,
                'errorYSize': comp_entity.height,
                'uXPosImage': comp_entity.x_pos,
                'uYPosImage': comp_entity.y_pos,
            })

            comp_list.append(comp)

        return comp_list

    def _send_board_data(self, pcb_entity: PcbEntity):
        """
        单板数据生成和发送，每个板单独发送一次
        """
        inspect_type = pcb_entity.get_insect_mes_type()
        event_xml_manager = self.get_event_xml_manager(inspect_type)

        err_msg_list = []
        work_items = event_xml_manager.get_public_param('workItems')
        work_part = event_xml_manager.get_public_param('workPart')
        for index, board_entity in enumerate(pcb_entity.yield_board_entity()):
            self.log.info(board_entity)
            # 1：机器pass，2：机器NG，3：坏板，-1：不测试
            if inspect_type == INSPECTOR:
                board_result = board_entity.get_final_result(m1="1", m2="2", m3="2", m4="12")
            else:
                board_result = board_entity.get_final_result(m1="1", m2="1", m3="2", m4="12")

            board_identifier = board_entity.barcode
            if pcb_entity.board_count == 1:
                if work_part:
                    part_for_station = work_part.get('partForStation')
                    board_identifier = work_part.get('identifier')
                    # 拼板partForStation为false，Workpart not processing -> not tested
                    if part_for_station == 'false':
                        board_result = '-1'
            else:
                if work_items:
                    # 拼板条码没有传给过主软件，缓存在work_items里
                    # <item pos="1" id="68077000068810012501070285021028" partForStation="true" />
                    board_identifier = work_items[index].get('id')
                    part_for_station = work_items[index].get('partForStation')
                    # 拼板partForStation为false，Workpart notprocessing -> not tested
                    if part_for_station == 'false':
                        board_result = '-1'

            # not tested scrap flag is active
            scrap_ack = '1' if board_result == '-1' else '0'
            comp_list = self._generate_comp_list(board_entity, scrap_ack, inspect_type)

            array_aoi = event_xml_manager.get_array_xml('AOI', comp_list)
            arrays = {
                'array_results': '',
                'array_AOI': array_aoi,
                'array_matData': '',
                'array_comp': '',
                'array_statisticData': '',
            }
            arrays_xml = event_xml_manager.get_arrays_xml('part', arrays)

            # 需要底层拿，如果entity里拿会包含.001
            program_name = event_xml_manager.get_public_param('Prog1.Name')
            event_data = {
                'eventSwitch': '-1',
                'identifier': board_identifier,
                'result': board_result,
                'Prog1_Name': program_name,
                'WorkPart_FailCount': board_entity.comp_robot_ng_number,
                'arrays': arrays_xml,
            }
            response = self._send_event(MesEvent.PART_PROCESSED, inspect_type, event_data)
            code = response.get('code')
            if code != '0':
                err_msg_list.append(response.get('msg'))

        # 把所有错误信息组装起来发送给AOI
        response = self.x_response()
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"one part send data to mes fail，error：{err_str}")
        return response

    def _send_panel_data(self, pcb_entity: PcbEntity):
        """
        整板数据生成和发送
        """
        inspect_type = pcb_entity.get_insect_mes_type()
        event_xml_manager = self.get_event_xml_manager(inspect_type)

        pcb_comp_ng_number = 0
        board_list = []
        all_comp_list = []
        work_items = event_xml_manager.get_public_param('workItems')
        boards_result = []
        static_results = []
        for index, board_entity in enumerate(pcb_entity.yield_board_entity()):
            self.log.info(board_entity)
            if inspect_type == INSPECTOR:
                pcb_comp_ng_number += board_entity.comp_robot_ng_number
            else:
                pcb_comp_ng_number += board_entity.comp_repair_ng_number

            # 1：机器pass，2：机器NG，12：坏板，-1：不测试
            if inspect_type == INSPECTOR:
                board_result = board_entity.get_final_result(m1="1", m2="2", m3="2", m4="12")
            else:
                board_result = board_entity.get_final_result(m1="1", m2="1", m3="2", m4="12")

            if not work_items:
                board_identifier = board_entity.barcode
                board_no = board_entity.board_no
            else:
                # 拼板条码没有传给过主软件，缓存在work_items里
                # <item pos="1" id="68077000068810012501070285021028" partForStation="true" />
                board_identifier = work_items[index].get('id')
                board_no = work_items[index].get('pos')
                part_for_station = work_items[index].get('partForStation')
                # 拼板partForStation为false，Workpart notprocessing -> not tested
                if part_for_station == 'false':
                    board_result = '-1'

            board_list.append({
                'pos': board_no,
                'result': board_result,
                'identifier': board_identifier
            })
            boards_result.append(board_result)

            # not tested scrap flag is active
            scrap_ack = '1' if board_result == '-1' else '0'
            comp_list = self._generate_comp_list(board_entity, scrap_ack, inspect_type)
            all_comp_list.extend(comp_list)

            if inspect_type == INSPECTOR:
                ng_count = board_entity.comp_robot_ng_number
            else:
                ng_count = board_entity.comp_repair_ng_number
            pass_count = board_entity.comp_total_number - ng_count
            static_results.append({
                'pos': board_no,
                'countComponent': board_entity.comp_total_number,
                'countTermination': 0,
                'errorCountComponent': ng_count,
                'passCountComponent': pass_count,
                'errorCountTermination': 0,
                'passCountTermination': 0,
            })

        # 当前只发results和AOI场景
        array_results = event_xml_manager.get_array_xml('results', board_list)
        array_aoi = event_xml_manager.get_array_xml('AOI', all_comp_list)
        array_mat_data = event_xml_manager.get_array_xml('matData', [])
        array_comp = event_xml_manager.get_array_xml('comp', [])
        array_statistic_data = event_xml_manager.get_array_xml('statisticData', static_results)
        arrays = {
            'array_results': array_results,
            'array_AOI': array_aoi,
            'array_matData': array_mat_data,
            'array_comp': array_comp,
            'array_statisticData': array_statistic_data,
        }
        arrays_xml = event_xml_manager.get_arrays_xml('group', arrays)

        # 需要底层拿，如果entity里拿会包含.001
        program_name = event_xml_manager.get_public_param('Prog1.Name')

        unique_boards_result = set(boards_result)
        if inspect_type == INSPECTOR:
            # 只要有一个拼板是NG，整板结果为NG
            if '2' in boards_result:
                pcb_result = '2'
            # partForStation=False之外的所有拼板结果都为pass，整板就为pass
            elif unique_boards_result in [{'1', '-1'}, {'1'}]:
                pcb_result = '1'
            else:
                pcb_result = '12'
        else:
            # 只要有一个拼板是PASS，整板结果为PASS
            if '1' in boards_result:
                pcb_result = '1'
            # 全部是坏板，整板结果为坏板
            elif unique_boards_result in [{'-1', '12'}, {'-1'}, {'12'}]:
                pcb_result = '12'
            # 其它全为NG，或者既有NG又有坏板，则判为NG
            else:
                pcb_result = '2'

        pcb_sn = event_xml_manager.get_pcb_sn()
        event_data = {
            'eventSwitch': '-1',
            'identifier': pcb_sn,
            'result': pcb_result,
            'Prog1_Name': program_name,
            'WorkPart_FailCount': pcb_comp_ng_number,
            'arrays': arrays_xml,
        }

        response = self._send_event(MesEvent.PART_PROCESSED, inspect_type, event_data)
        code = response.get('code')
        if code == '0':
            response = self.x_response()
        else:
            response = self.x_response('false', response.get('msg'))
        return response

    def _get_repeat_send_count(self, review_path, inspect_type):
        from services.socket_service import circular_list_robot, circular_list_repair
        if inspect_type == INSPECTOR:
            send_count = circular_list_robot.buffer.count(review_path)
        else:
            send_count = circular_list_repair.buffer.count(review_path)
        return send_count

    def _remove_review_path(self, review_path, inspect_type):
        from services.socket_service import circular_list_robot, circular_list_repair
        if inspect_type == INSPECTOR:
            circular_list_robot.remove_one_item(review_path)
        else:
            circular_list_repair.remove_one_item(review_path)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        if self.switch_button.is_mes_off():
            return self.x_response()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        inspect_type = other_data.get('inspect_type')
        review_path = other_data.get('review_path')

        event_xml_manager = self.get_event_xml_manager(inspect_type)

        pcb_sn = event_xml_manager.get_pcb_sn()
        is_allowed_send, ret_msg = event_xml_manager.is_allowed_send_data_to_mes(pcb_entity)
        err_msg = ''
        if not pcb_sn:
            data_type = '检测' if inspect_type == INSPECTOR else '复判'
            err_msg = f'板【{pcb_entity.pcb_barcode}】未进行过扫码，请进行扫码后再发送{data_type}数据！'
        elif pcb_entity.pcb_barcode and pcb_sn != pcb_entity.pcb_barcode:
            err_msg = f'发送数据的板【{pcb_entity.pcb_barcode}】非扫码的板【{pcb_sn}】，请重新扫码发送！'
        elif not is_allowed_send:
            err_msg = ret_msg

        if err_msg:
            # 直接返回不能记录为发送过数据
            self._remove_review_path(review_path, inspect_type)
            if inspect_type == INSPECTOR:
                return self.x_response('false', err_msg)
            else:
                ret = self._post_msg_to_repair(err_msg)
                return ret

        repeat_cnt = self._get_repeat_send_count(review_path, inspect_type)
        if repeat_cnt > 1:
            if inspect_type == INSPECTOR:
                err_msg = f"该检测结果数据第[{repeat_cnt}]次重复发送MES，请评估是否合理，如果不合理，请勾选【设置-其他设置-去除重复数据！】"
                other_param.log_info(err_msg, status=False, pop_prompt=True)
            else:
                err_msg = f"该复判结果数据第[{repeat_cnt}]次重复发送MES，请评估是否合理，如果不合理，请在[MES配置器]中勾选【设置-其他设置-去除重复数据！】"
                self._post_msg_to_repair(err_msg)

        tracing_type = self.other_vo.get_value_by_cons_key("tracing_type")
        if tracing_type == "Group":
            response = self._send_panel_data(pcb_entity)
        elif tracing_type == "One Part":
            response = self._send_board_data(pcb_entity)
        else:
            response = self.x_response("false", f"unsupported tracing type！")

        # 如果是维修站发送数据，则出错信息直接返回维修站显示
        if inspect_type == REPAIR and not response.get('result'):
            err_msg = f"\n发送MES返回失败，请核实！\n\n{response.get('string')}"
            response = self._post_msg_to_repair(err_msg)

        # 增加一层保护，万一没有收到检测软件换线的事件，发送数据完成时清空下
        self.change_over_flag = False

        return response

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        msg = '关闭MES服务' if self.switch_button.is_mes_off() else '开启MES服务'
        self.log.info(msg)
        return self.x_response()

    def create_custom_button(self, parent: QFrame):
        self.switch_button = SwitchButton(parent)
        return self.switch_button
