# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/5 上午9:13
# Author     ：sch
# version    ：python 3.8
# Description：宁波华翔
"""
import json
from typing import Any

from common import xutil, xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

check_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <CheckHJAOIInfo xmlns="http://www.cnlean.net/">
            <FReqCode>{FReqCode}</FReqCode>
            <FSN>{FSN}</FSN>
            <FDeviceNo>{FDeviceNo}</FDeviceNo>
        </CheckHJAOIInfo>
    </soap:Body>
</soap:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GetHJAOIInfo xmlns="http://www.cnlean.net/">
            <FReqCode>{FReqCode}</FReqCode>
            <FSN>{FSN}</FSN>
            <FDeviceNo>{FDeviceNo}</FDeviceNo>
            <FResult>{FResult}</FResult>
            <FHBDate>{FHBDate}</FHBDate>
            <FContent>{FContent}</FContent>
        </GetHJAOIInfo>
    </soap:Body>
</soap:Envelope>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "ningbohuaxiang release v1.0.0.5",
        "device": "AIS501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-05 09:14  init
date: 2024-06-05 10:10  条码校验，上传数据
date: 2024-06-05 15:48  参数格式改为xml
date: 2024-06-12 17:14  FContent传器件数据
date: 2024-07-01 14:45  数据格式修改
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
    }

    form = {
        "device_no": {
            "ui_name": "设备编码",
            "value": "HJAOI01"
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        device_no = other_vo.get_value_by_cons_key("device_no")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_xml = check_template.format(**{
                "FReqCode": xutil.OtherUtil.get_uuid4_str(),
                "FSN": sn,
                "FDeviceNo": device_no
            })

            ret_xml1 = xrequest.RequestUtil.post_soap(api_url_check,
                                                      check_xml,
                                                      soap_action="http://www.cnlean.net/CheckHJAOIInfo")

            root1 = xutil.XmlUtil.get_xml_root_by_str(ret_xml1)
            ret_str = root1[0][0][0].text
            ret = json.loads(ret_str)
            if ret.get("result") != "Succeed":
                msg = ret.get("data")
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{msg}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        device_no = data_vo.get_value_by_cons_key("device_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_list.append({
                    "result": comp_entity.get_final_result("PASS", "FAIL"),
                    "Qty": comp_entity.designator,
                    "Tep": comp_entity.repair_ng_str
                })

            content = json.dumps(comp_data_list, separators=(",", ":"))

            # if content:
            #     content = content[1:-1]
            # else:
            #     content = {}

            data_param = {
                "FReqCode": xutil.OtherUtil.get_uuid4_str(),
                "FSN": barcode,
                "FDeviceNo": device_no,
                "FResult": board_entity.get_repair_result("OK", "NG"),
                "FHBDate": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT2),
                "FContent": content,
            }

            data_str = data_template.format(**data_param)

            ret_xml2 = xrequest.RequestUtil.post_soap(api_url_data,
                                                      data_str,
                                                      soap_action="http://www.cnlean.net/GetHJAOIInfo"
                                                      )

            root2 = xutil.XmlUtil.get_xml_root_by_str(ret_xml2)
            ret_str = root2[0][0][0].text
            ret = json.loads(ret_str)

            if ret.get("result") != "Succeed":
                msg = ret.get("data")
                ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{msg}")

        return ret_res


# if __name__ == '__main__':
#     ret_xml = """<?xml version="1.0" encoding="utf-8"?>
# <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
#                xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
#     <soap:Body>
#         <CheckHJAOIInfoResponse xmlns="http://www.cnlean.net/">
#             <CheckHJAOIInfoResult>{"result": "Succeed", "FReqCode": "202405161701333", "FDeviceNo": "HJAOI01",
#             "data": "{\"Status\":\"OK\",\"CheckResult\":\"校验通过\"}"}</CheckHJAOIInfoResult>
#         </CheckHJAOIInfoResponse>
#     </soap:Body>
# </soap:Envelope>"""
#
#     root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
#     print(root[0][0][0].text)
