# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/04/22 下午11:55
# Author     ：gyr
# version    ：python 3.8
# Description：深圳助儿达    https://jira.cvte.com/browse/ATAOI_2019-38917
"""
from typing import Any

from common import xcons, xutil
from engine.MesEngine import BaseEngine
from vo.mes_vo import DataVo

pcb_template = '''Model name,Line number
{model_name},{line_number}
Board Status,{pcb_result},Array Barcode,{pcb_sn}{board_data_str}
PadID,ComponentID,Area(%),Volume(%),Height(%),Volume,Type,Height,XOffset,YOffset,PadSize(X),PadSize(Y),Area,Result,UserResult,Errcode,Errdetail,location,PinNum,Barcode,Date,Time,ArrayID
{comp_data_str}
'''

board_template = """
ArrayID,{no},Array Status,{final_result},Array Barcode,{sn}"""

comp_template_row = '''{comp_id_real},{component_id},{area},{volume},{height},{comp_volume},{type},{comp_height},{comp_x_offset},{comp_y_offset},{comp_height2},{comp_width},{comp_area},{comp_robot_result},{comp_user_result},{robot_ng_code},{robot_ng_str},{comp_designator},{comp_part},{pcb_sn},{comp_date},{comp_time},{no}
'''


class Engine(BaseEngine):
    version = {
        "title": "shenzhenzhuerda release v1.0.0.1",
        "device": "AIS303",
        "feature": ["生成csv"],
        "author": "wxc",
        "release": """
date: 2025-04-22 11:55  生成本地csv文档
""",
    }

    form = {
        "line_number": {
            "ui_name": "线别",
            "value": ""
        },
    }
    path = {
        "save_path": {
            "ui_name": "csv保存路径",
            "value": ""
        }
    }

    @staticmethod
    def safe_value(value, default=0):
        """
        安全获取值：
        - 如果 value 为 None 或无效值（如 0），返回 default。
        - 否则返回 value。
        """
        return default if not value else value

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        line_number = data_vo.get_value_by_cons_key("line_number")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        # 文件命名方式:整板条码，没有整板条码取拼板 1 条码，如果还是没有，就用时间戳代替（年月日时分秒）
        unique_sn = ""

        if pcb_entity.all_barcode:
            unique_sn = pcb_entity.all_barcode[0]

        if not unique_sn:
            unique_sn = test_time

        final_path = f"{save_path}/{unique_sn}.csv"

        board_data_str = ""
        comp_data_str = ""

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            ix_count = 0
            for comp_entity in board_entity.yield_comp_entity():
                ix_count += 1

                comp_data_str += comp_template_row.format(**{
                    "comp_id_real": comp_entity.comp_id_real,  # 器件ID
                    "component_id": 0,
                    "area": 0,
                    "volume": 0,
                    "height": 0,
                    "comp_volume": 0,
                    "type": "",  # 传
                    "comp_height": 0,  # 高度，没有则传 0
                    "comp_x_offset": self.safe_value(comp_entity.x_pos),  # X 坐标，没有则传 0
                    "comp_y_offset": self.safe_value(comp_entity.y_pos),  # Y 坐标，没有则传 0
                    "comp_height2": self.safe_value(comp_entity.height),
                    "comp_width": self.safe_value(comp_entity.width),  # 器件 XY 长宽，没有则传 0
                    "comp_area": 0,
                    # 面积，没有则传 0
                    "comp_robot_result": comp_entity.get_final_result("pass", "NG", "NG"),  # 机器检测结果（pass / NG）
                    "comp_user_result": comp_entity.get_final_result("pass", "pass", "NG"),  # 人工复判结果（pass / NG）
                    "robot_ng_code": comp_entity.robot_ng_code,  # 器件不良代码
                    "robot_ng_str": comp_entity.robot_ng_str,  # 器件不良描述
                    "comp_designator": comp_entity.designator,  # 位号
                    "comp_part": ix_count,  # 引脚ID
                    "pcb_sn": pcb_sn,  # 整板条码
                    "comp_date": str(test_time)[:8],  # 日期（年月日）
                    "comp_time": str(test_time)[8:],  # 时间（时分秒）
                    "no": board_no  # 拼板号
                })

            board_data_str += board_template.format(**{
                "no": board_no,  # 拼板序号
                "final_result": board_entity.get_repair_result("pass", "NG"),
                "sn": barcode  # 拼板条码
            })

        pcb_content = pcb_template.format(**{
            "model_name": pcb_entity.project_name,  # 程序名
            "line_number": line_number,
            "pcb_result": pcb_entity.get_repair_result("pass", "NG"),  # 整板结果（pass/NG）
            "pcb_sn": pcb_sn,  # 整板条码
            "board_data_str": board_data_str,
            "comp_data_str": comp_data_str
        })
        xutil.FileUtil.write_content_to_file_pro(final_path, pcb_content)

        return self.x_response()
