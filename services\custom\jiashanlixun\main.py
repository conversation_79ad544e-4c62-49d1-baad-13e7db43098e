# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/20 下午2:55
# Author     ：sch
# version    ：python 3.8
# Description：嘉善立讯
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "jiashanlixun release v1.0.0.4",
        "device": "430",
        "feature": ["从mes获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-20 17:35  从mes获取条码
date: 2023-12-02 10:03 改用中转方式传输
date: 2023-12-19 17:58  几拼版就获取几次条码
date: 2023-12-20 16:04  bugfix
""", }

    form = {
        "window_ip": {
          "ui_name": "window中转IP",
          "value": "",
        },
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://***********/Automation/sfc_automation.aspx"
        },
        "c_param": {
            "ui_name": "c",
            "value": "query"
        },
        "p_param": {
            "ui_name": "p",
            "value": "GetSNByCarrierSN"
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        c_param = other_vo.get_value_by_cons_key("c_param")
        p_param = other_vo.get_value_by_cons_key("p_param")
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        pcb_sn = other_vo.get_pcb_sn()

        barcode_map = other_vo.get_origin_param("barcodeList", {"-2": "", "-1": "", "1": "", "2": ""})

        board_number = len(barcode_map) - 2
        self.log.info(f"需要获取{board_number}次条码！")

        ret_sn_list = []
        for i in range(board_number):
            param = {
                "c": c_param,
                "p": p_param,
                "CarrierSN": pcb_sn,
                "api_url": api_url
            }

            # ret = xrequest.RequestUtil.get(api_url, param, to_json=False)
            packet_data = {
                "type": 3,
                "request_param": param
            }
            res = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)
            ret = res.get('string')

            if not ret.startswith('0'):
                return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret}")

            ret_list = ret.split("=")
            if len(ret_list) < 2:
                return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret}")

            ret_sn = ret_list[1]

            self.log.info(f"sn:{ret_sn}")

            ret_sn_list.append(ret_sn)

        return self.x_response("true", ",".join(ret_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        return self.x_response()


if __name__ == '__main__':
    a = "0 sfc ok".startswith("0")
    print(a)
