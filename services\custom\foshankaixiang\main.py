# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/28 下午4:57
# Author     ：sch
# version    ：python 3.8
# Description：佛山凯祥
"""

from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

board_template = """PCB编号,产品名称,面,测试结果,机器编号,条码,PCB Ng 总数,误判Ng数量,确认NG数量,操作员,班次,线别,元件数量,屏蔽,子板编号,维修,工单,大板条码,关联条码,关联条码类型,直通率
{test_time},{product_name},{board_side},{test_result},{machine_code},{barcode},{robot_ng_number},{repass_number},{repair_ng_number},{operator},{work_shift},{line_id},{total_number},,{board_no},{repair_result},{order_id},{pcb_sn},,,

PCB编号,元件编号,元件位置,角度,X坐标,Y坐标,NG名称,标准名称,料号,元件名称,初判NG,直通率{comp_data_str}"""

comp_template = """
{test_time},{comp_id},{comp_tag},{angle},{x_pos},{y_pos},{repair_ng_str},,{comp_part},{comp_type},{robot_ng_str},"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["佛山凯祥", "foshankaixiang"],
        "version": "release v1.0.0.3",
        "device": "303B、400-D、630B、501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-29 14:42  生成csv文件
date: 2024-12-24 09:42  新增从mes获取条码
date: 2025-05-06 15:12  jira:ATAOI_2019-39298,修改测试结果为复判结果
""", }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "获取拼板条码_接口URL",
            "value": "http://121.36.200.99/mes/api/get_block_sns/",
        },
        "username": {
            "ui_name": "获取拼板条码_用户名",
            "value": "",
        },
        "password": {
            "ui_name": "获取拼板条码_密码",
            "value": "",
        },
    }

    form = {
        "machine_code": {
            "ui_name": "机器编号",
            "value": "",
        },
        "line_id_1": {
            "ui_name": "线别(1轨)",
            "value": "",
        },
        "line_id_2": {
            "ui_name": "线别(2轨)",
            "value": "",
        },
        "order_id_1": {
            "ui_name": "工单(1轨)",
            "value": "",
        },
        "order_id_2": {
            "ui_name": "工单(2轨)",
            "value": "",
        },
        "board_side_1": {
            "ui_name": "面(1轨)",
            "value": "",
        },
        "board_side_2": {
            "ui_name": "面(2轨)",
            "value": "",
        },
        "product_name": {
            "ui_name": "产品名称(1轨)",
            "value": "",
        },
        "product_name2": {
            "ui_name": "产品名称(2轨)",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "work_shift": {
            "ui_name": "班次",
            "value": "",
        },
    }

    path = {
        "save_path": {
            "ui_name": "保存路径(1轨)",
            "value": "",
        },
        "save_path2": {
            "ui_name": "保存路径(2轨)",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        machine_code = data_vo.get_value_by_cons_key("machine_code")
        line_id_1 = data_vo.get_value_by_cons_key("line_id_1")
        line_id_2 = data_vo.get_value_by_cons_key("line_id_2")
        order_id_1 = data_vo.get_value_by_cons_key("order_id_1")
        order_id_2 = data_vo.get_value_by_cons_key("order_id_2")
        board_side_1 = data_vo.get_value_by_cons_key("board_side_1")
        board_side_2 = data_vo.get_value_by_cons_key("board_side_2")
        product_name1 = data_vo.get_value_by_cons_key("product_name")
        product_name2 = data_vo.get_value_by_cons_key("product_name2")
        operator = data_vo.get_value_by_cons_key("operator")
        work_shift = data_vo.get_value_by_cons_key("work_shift")
        save_path1 = data_vo.get_value_by_cons_key("save_path", not_null=True)
        save_path2 = data_vo.get_value_by_cons_key("save_path2", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1)

        if pcb_entity.track_index == 1:
            board_side = board_side_1
            line_id = line_id_1
            order_id = order_id_1
            product_name = product_name1
            save_path = save_path1
        else:
            board_side = board_side_2
            line_id = line_id_2
            order_id = order_id_2
            product_name = product_name2
            save_path = save_path2

        pcb_sn = pcb_entity.pcb_barcode

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_str = ""

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += comp_template.format(**{
                    "test_time": test_time,
                    "comp_id": comp_entity.comp_id,
                    "comp_tag": comp_entity.designator,
                    "angle": comp_entity.geometry.angle,
                    "x_pos": comp_entity.geometry.cx,
                    "y_pos": comp_entity.geometry.cy,
                    "repair_ng_str": comp_entity.repair_ng_str,
                    "comp_part": comp_entity.part,
                    "comp_type": comp_entity.type,
                    "robot_ng_str": comp_entity.robot_ng_str,
                })

            board_content = board_template.format(**{
                "test_time": test_time,
                "product_name": product_name,
                "board_side": board_side,
                "test_result": board_entity.get_repair_result("PASS", "NG"),
                "machine_code": machine_code,
                "barcode": barcode,
                "robot_ng_number": board_entity.comp_robot_ng_number,
                "repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "repair_ng_number": board_entity.comp_repair_ng_number,
                "operator": operator,
                "work_shift": work_shift,
                "line_id": line_id,
                "total_number": board_entity.comp_total_number,
                "board_no": board_no,
                "repair_result": board_entity.get_repair_result("TRUE", "FALSE"),
                "order_id": order_id,
                "pcb_sn": pcb_sn,
                "comp_data_str": comp_data_str,
            })

            filepath = f"{save_path}/{barcode}_{board_no}_{time_file}.csv"
            xutil.FileUtil.write_content_to_file(filepath, board_content)

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        username = other_vo.get_value_by_cons_key("username")
        password = other_vo.get_value_by_cons_key("password")

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "username": username,
            "pw": password,
            "scan_sn": pcb_sn,
            "method": "normal"
        }

        ret = xrequest.RequestUtil.post_form(api_url_get_sn, param)
        if str(ret.get("errno")) != "0":
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('errmsg')}")

        block_sns = ret.get('data', {}).get('block_sns', [])

        return self.x_response("true", ",".join(block_sns))
