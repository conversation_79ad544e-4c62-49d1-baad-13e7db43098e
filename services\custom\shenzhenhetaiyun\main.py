# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/27 上午9:21
# Author     ：sch
# version    ：python 3.8
# Description：深圳和泰云
"""
import json
from typing import Any

import requests

from common.xutil import log
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


def post_json(url,
              body_data,
              headers: dict = None,
              params: dict = None,
              to_json=True,
              log_number: int = 300000,
              timeout=5,
              auth=None,
              ) -> Any:
    """
    post 接口，并默认返回json数据
    请求头：content-type: application/json
    :param url: 请求API
    :param headers: 请求头
    :param params: 请求参数
    :param body_data: 请求体参数
    :param to_json: 是否需要将返回参数转成 `python dict` 类型
    :param log_number: 打印多少参数
    :param auth: auth
    :param timeout: timeout
    :return:
    """
    body_data_str = json.dumps(body_data, ensure_ascii=False)

    log.info(f"-->请求URL：{url}  【Body Json】参数：\n{body_data_str[:log_number]}")
    if headers:
        log.info(f"-->请求头：\n{headers}")

    if params:
        log.info(f"-->【Param】参数：\n{params}")

    if auth:
        res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, auth=auth)
    else:
        res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout)

    log.info(f"post_json 响应参数 code: {res} msg: {res.text}")

    if not res:
        # log.warning(f"post_json code: {res}  error: {res.text}")
        raise Exception("接口调用出错2003，请检查接口服务是否正常！")

    if to_json:
        return res.json()
    else:
        return res.text


class Engine(BaseEngine):
    version = {
        "title": "hetaiyun release v1.0.0.1",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-27 09:40 上传数据到mes
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/test_json/hetaiyun"
        },
        "device_sn": {
            "ui_name": "设备编码",
            "value": "CQ-001"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        device_sn = data_vo.get_value_by_cons_key("device_sn")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        standard_json = pcb_entity.to_standard_json()

        param = [device_sn, standard_json]

        ret = post_json(api_url, param)
        if not ret.get('Data'):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('Message')}")

        return self.x_response()
