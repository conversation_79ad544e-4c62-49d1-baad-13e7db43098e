# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/21 16:00
# Author     ：sch
# version    ：python 3.8
# Description：衢州威睿    https://jira.cvte.com/browse/ATAOI_2019-38115
"""

from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "quzhouweirui release v1.0.0.1",
        "device": "AIS303B-L",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-03-21 16:01  jira->ATAOI_2019-38115: 上传数据到mes
""", }

    form = {
        "tcp_host": {
            "ui_name": "Tcp主机地址",
            "value": "127.0.0.1",
        },
        "tcp_port": {
            "ui_name": "Tcp端口",
            "value": "5000",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        tcp_host = data_vo.get_value_by_cons_key("tcp_host")
        tcp_port = data_vo.get_value_by_cons_key("tcp_port", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            board_result = board_entity.get_repair_result("PASS", "FAIL")

            if not barcode:
                barcode = f"{start_time}_{board_no}"

            param = f"Barcode:{barcode}|Result:{board_result}"
            xrequest.SocketUtil.send_data_to_socket_server(tcp_host, tcp_port, param)

        return self.x_response()

