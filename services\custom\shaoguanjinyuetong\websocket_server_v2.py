# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : websocket_server_v2.py
# Time       ：2025/5/13 上午10:02
# Author     ：sch
# version    ：python 3.8
# Description：
"""

import asyncio
import json

import websockets
from PyQt5.QtCore import QThread, pyqtSignal, QObject


class WebsocketServer(QObject):
    """WebSocket 服务器类，运行在独立的线程中"""
    # 用于向 PyQt 界面发送消息
    message_received = pyqtSignal(str)  # type: pyqtSignal
    # 用于向 PyQt 界面发送服务器状态
    server_status = pyqtSignal(bool)  # type: pyqtSignal
    # 用于发送连接数量
    connection_count_changed = pyqtSignal(int)  # type: pyqtSignal
    # 用于通知服务器线程关闭
    shutdown_signal = pyqtSignal()  # type: pyqtSignal

    def __init__(self, host, port):
        super().__init__()
        self.host = host
        self.port = port
        self.server = None
        self.clients = set()  # 保存所有连接的客户端
        self.running = False
        self.loop = None  # 保存 asyncio 事件循环

    async def handler(self, websocket):
        """处理单个客户端连接"""
        self.clients.add(websocket)
        self.connection_count_changed.emit(len(self.clients))  # 更新连接数量
        try:
            async for message in websocket:
                self.message_received.emit(f"Received: {message}")
                # 在这里处理接收到的消息
        except websockets.exceptions.ConnectionClosedError:
            print("Connection closed unexpectedly.")
        finally:
            self.clients.remove(websocket)
            self.connection_count_changed.emit(len(self.clients))  # 更新连接数量

    async def start_server(self):
        """启动 WebSocket 服务器"""
        try:
            self.running = True
            async with websockets.serve(self.handler, self.host, self.port) as server:
                self.server = server
                self.server_status.emit(True)  # 发送服务器启动状态
                print(f"WebSocket server started at ws://{self.host}:{self.port}")
                # await asyncio.Future()  # 保持服务器运行直到手动停止
                # 使用 asyncio.Event 来代替 asyncio.Future，以便可以在其他线程中设置它
                self.shutdown_event = asyncio.Event()
                await self.shutdown_event.wait()  # 等待关闭信号

        except Exception as e:
            print(f"Server error: {e}")
            self.server_status.emit(False)
            self.running = False
            if self.running:  # 如果不是手动停止，则尝试重启
                print("Attempting to restart server...")
                await asyncio.sleep(5)  # 稍等片刻再重启
                asyncio.create_task(self.start_server())  # 再次启动服务器

    def stop_server(self):
        """停止 WebSocket 服务器"""
        self.running = False
        # self.shutdown_signal.emit() # 通知server_thread关闭
        # 使用 asyncio.run_coroutine_threadsafe 在服务器线程的事件循环中运行 _stop_server
        if self.server:
            asyncio.run_coroutine_threadsafe(self._stop_server(), self.loop)  # 在服务器的loop里执行
        self.server_status.emit(False)
        print("WebSocket server stopped.")

    async def _stop_server(self):
        """异步停止服务器的协程"""
        for client in self.clients:
            await client.close()  # 关闭所有客户端连接
        if self.server:
            self.server.close()
            await self.server.wait_closed()
        # 设置 shutdown_event 以停止服务器
        if hasattr(self, "shutdown_event"):
            self.shutdown_event.set()

    def send_message(self, data):
        """向所有客户端发送消息"""
        asyncio.run(self._send_message(data))

    async def _send_message(self, data):
        """异步发送消息的协程"""
        if self.clients:
            message = json.dumps({"data": data})  # 构造带 "data" 参数的消息
            for client in self.clients:
                try:
                    await client.send(message)
                    self.message_received.emit(f"Sent: {message}")
                except websockets.exceptions.ConnectionClosedError:
                    print(f"Failed to send to a client: Connection closed.")
                    self.clients.remove(client)  # 移除已断开的客户端
                    self.connection_count_changed.emit(len(self.clients))  # 更新连接数量
                except Exception as e:
                    print(f"Failed to send to a client: {e}")


class ServerThread(QThread):
    """用于运行 asyncio 事件循环的线程"""

    def __init__(self, websocket_server):
        super().__init__()
        self.websocket_server = websocket_server
        self.loop = None

    def run(self):
        """在独立的线程中运行 asyncio 事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.websocket_server.loop = self.loop  # 保存loop
        self.loop.run_until_complete(self.websocket_server.start_server())
        self.loop.close()
