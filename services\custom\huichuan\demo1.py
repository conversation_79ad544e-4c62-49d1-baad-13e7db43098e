# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2025/1/8 上午10:28
# Author     ：sch
# version    ：python 3.8
# Description：
"""


def format_board_data(board_no, status, comp_data_list, max_line_length=100):
    """
    格式化板数据字符串，确保每行不超过指定的最大长度，并且每行都以固定的板信息开头和分号结尾。

    参数:
    - board_no (str): 板编号/板条码
    - status (str): 检测结果状态（例如 "PASS", "Repass", "Repair"）。
    - comp_data_list (list of str): 组件数据列表，每个元素代表一个组件的信息。
    - max_line_length (int, optional): 每行的最大字符数，默认为100。

    返回:
    - str: 格式化后的板数据字符串，其中每一行都包含板信息并以分号结尾。
    """
    # 创建模板字符串
    fixed_part = f"Board NO:{board_no};Result:{status};"
    fixed_part_length = len(fixed_part)

    # 可用空间为最大行长度减去固定部分的长度，预留一个字符给分号
    available_space = max_line_length - fixed_part_length - 1

    # 将组件数据转换为分号分隔的字符串，并考虑换行符
    comp_data_str_parts = []
    current_part = ""

    for item in comp_data_list:
        # 如果添加新item后超过可用空间，则开始新的一段
        if current_part and len(current_part) + len(item) + 2 > available_space:  # 加2是因为分号和可能的空格
            comp_data_str_parts.append(current_part + ";")
            current_part = item
        else:
            if current_part:
                current_part += ";" + item
            else:
                current_part = item

    # 不要忘记添加最后一段，并确保其以分号结尾
    if current_part:
        comp_data_str_parts.append(current_part + ";")

    # 使用换行符连接各段，并且每行前加上固定的板信息和分号结尾
    formatted_lines = [f"{fixed_part}{part}" for part in comp_data_str_parts]

    # 格式化最终的板信息字符串
    board_data_str = "\n".join(formatted_lines)

    # 如果生成的字符串超过了单行的最大长度，则需要进一步处理
    lines = board_data_str.split('\n')
    final_lines = []
    for line in lines:
        while len(line) > max_line_length:
            # 找到最接近max_line_length位置的分号
            split_pos = line.rfind(';', 0, max_line_length)
            if split_pos == -1 or split_pos < fixed_part_length:
                # 如果没有找到分号或分号位于固定部分内，则强制切割
                split_pos = max_line_length - 1
            final_lines.append(line[:split_pos] + ";")
            line = fixed_part + line[split_pos + 1:]
        if line:
            # 确保最后一行也以分号结尾
            if not line.endswith(';'):
                line += ';'
            final_lines.append(line)

    return '\n'.join(final_lines)


def format_board_data2(board_no, status, comp_data_list, max_line_length=99):
    """
    格式化板数据字符串，确保每行不超过指定的最大长度，并且每行都以固定的板信息开头和分号结尾。

    参数:
    - board_no (str): 板编号。
    - status (str): 检测结果状态（例如 "PASS", "Repass", "Repair"）。
    - comp_data_list (list of str): 组件数据列表，每个元素代表一个组件的信息。
    - max_line_length (int, optional): 每行的最大字符数，默认为100。

    返回:
    - str: 格式化后的板数据字符串，其中每一行都包含板信息并以分号结尾。
    """

    # 创建固定部分的模板字符串，它将出现在每一行的开始
    fixed_part = f"Board NO:{board_no};Result:{status};"
    fixed_part_length = len(fixed_part)

    # 可用空间为最大行长度减去固定部分的长度，再减去一个字符给分号
    available_space = max_line_length - fixed_part_length - 1

    # 存储分割后的组件数据部分
    lines = []
    current_line = ""

    # 遍历组件数据列表，确保每行不超过可用空间
    for item in comp_data_list:
        # 如果加上当前item后超过可用空间，则开始新行（已考虑分号和空格）
        if len(current_line) + len(item) + 2 > available_space:
            lines.append(current_line + ";")
            current_line = item
        else:
            if current_line:
                current_line += ";" + item
            else:
                current_line = item

    # 确保最后一行以分号结尾
    if current_line:
        lines.append(current_line + ";")

    # 在每行前添加固定部分，并检查是否超过最大长度
    formatted_lines = []
    for line in lines:
        full_line = fixed_part + line

        # 如果超过最大长度，则找到最近的一个分号位置进行分割
        while len(full_line) > max_line_length:
            split_pos = full_line.rfind(';', 0, max_line_length)
            if split_pos == -1 or split_pos < fixed_part_length:
                # 如果没有找到合适位置，则强制在最大长度处分割
                split_pos = max_line_length - 1

            # 添加分割后的前半部分到结果列表，并处理后半部分

            new_line = full_line[:split_pos + 1]

            if not new_line.endswith(';'):
                new_line += ';'

            formatted_lines.append(new_line)  # 包含分号
            full_line = fixed_part + full_line[split_pos + 1:]

        # 确保最后一行也以分号结尾
        if not full_line.endswith(';'):
            full_line += ';'

        # 添加最后一行（已经确保不会超出长度）
        formatted_lines.append(full_line)

    # 返回格式化后的多行字符串
    return '\n'.join(formatted_lines)


# 示例调用
board_flag = "B123456"
status = "PASS"  # 假设这是从board_entity.get_final_result(...)得到的结果
comp_data_list = ["Component4", "Component2", "A very long component name that will force a line break1 break2 break3 break4 break5", "Component4",
                  "Component2", "Component2", "Component2", "Component2", "Component2", "Component2", "Component2",
                  "Component2", "Component2", "Component2", "Component2", "Component2", "Component2", "Component2",
                  "Component2", "Component2", "Component2", ]

board_data_str = format_board_data2(board_flag, status, comp_data_list)

print(board_data_str)
