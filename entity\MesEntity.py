#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2023/4/9
# Author: sun<PERSON><PERSON>g
import os
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Tuple

from common import xcons
from common.xsql import SqlLiteHelper
from common.xutil import XmlUtil, log, OtherUtil


class AlgEntity(object):
    def __init__(self, json_data: dict):
        self.json_data = json_data
        self.test_name = json_data.get("test_name", "")  # 算法名称
        self.test_val = json_data.get("test_val", "")  # 算法测试值
        self.result = json_data.get("result", "")  # 算法检测结果   (AOI, 0：OK，其它：NG)  (SPI, 1：OK，其它：NG)
        # self.mix_threshold = json_data.get("mix_threshold", "")  # 算法最小阈值
        self.max_threshold = json_data.get("max_threshold", "")  # 算法最大阈值
        self.min_threshold = json_data.get("min_threshold", "")  # 算法最小阈值


class GeometryVo(object):
    def __init__(self, json_data: dict):
        """
        Geometry 坐标系
        :param json_data:
        """
        self.cx: int = json_data.get("cx", 0)  # 在原图上的x坐标, 单位：像素
        self.cy: int = json_data.get("cy", 0)  # 在原图上的y坐标, 单位：像素
        self.angle: int = json_data.get("angle", 0)
        self.width: int = json_data.get("width", 0)
        self.height: int = json_data.get("height", 0)

    def __str__(self):
        return f"坐标系：cx:{self.cx} cy:{self.cy} angle:{self.angle} width:{self.width} height:{self.height}"


class CompEntity(object):
    """
    器件实体
    """

    def __init__(self, comp_id, comp_item: dict):
        self.comp_json = comp_item

        self.comp_id_real = comp_item.get("comp_id")  # 器件id,    ps:431机型，此字段为空 ""

        self.comp_id = comp_id  # 器件uuid
        self.board_side = comp_item.get("board_side")  # T/B
        self.designator = comp_item.get("designator")  # 器件位号
        self.part = comp_item.get("part")  # 器件料号
        self.type = comp_item.get("type")  # 器件类型
        self.package = comp_item.get("package")  # 器件封装
        self.image_path = comp_item.get("image_path")  # 器件图片保存路径
        self.x_offset = comp_item.get("x_offset")  # 器件x偏移
        self.y_offset = comp_item.get("y_offset")  # 器件y偏移
        self.x_pos = comp_item.get("x_pos")  # 器件x坐标
        self.y_pos = comp_item.get("y_pos")  # 器件y坐标
        self.width = comp_item.get("width")  # 器件框的宽度
        self.height = comp_item.get("height")  # 器件框的高度
        self.alg_data = comp_item.get("alg_data")
        self.repair_auto_ng = comp_item.get("repair_auto_ng")
        self.repair_auto_ng_code = comp_item.get("repair_auto_ng_code")
        self.repair_checked_flag = comp_item.get("repair_checked_flag")

        self.robot_ng_code = comp_item.get("robot_ng_code")  # 器件机器不良代码
        self.robot_ng_str = comp_item.get("robot_ng_str")  # 器件机器不良描述
        self.repair_ng_code = comp_item.get("repair_ng_code")  # 器件复判不良代码
        self.repair_ng_str = comp_item.get("repair_ng_str")  # 器件复判不良描述
        self.final_result = comp_item.get("final_result")  # 器件最终结果 (PASS, 直通) (REPASS, 误报) (NG, 真实NG)

        self.ui_upload_mes = comp_item.get('upload_mes', True)
        self.ui_check_flag2 = comp_item.get('check_flag2', False)
        self.ui_check_flag3 = comp_item.get('check_flag3', False)

        geometry_obj = comp_item.get("geometry_obj", {})
        self.geometry: GeometryVo = GeometryVo(geometry_obj)

    def get_origin_robot_ng_code(self):
        """
        获取原始的机器检测不良代码
        :return:
        """
        return self.comp_json.get("origin_robot_ng_code")

    def get_origin_repair_ng_code(self):
        """
        获取原始的复判不良代码
        :return:
        """
        return self.comp_json.get("origin_repair_ng_code")

    def to_json_data(self, m1="PASS", m2="REPASS", m3="NG"):
        """
        器件常用格式
        转换成常用的json格式
        {
          "tag": "COMP1034",
          "type": "SolderJoint",
          "part": "",
          "package": "",
          "image_path": "/home/<USER>/aoi/run/results/333.001/20230421/T_20230421111834308_1_NG/images/ng/SolderJoint/0/COMP1034_1034.png",
          "robot_ng_str": "孔洞",
          "robot_ng_code": "13",
          "repair_ng_str": "Pin Hole",
          "repair_ng_code": "13",
          "robot_result": "NG",
          "repair_result": "NG",
          "final_result": "NG"
        }
        """
        return {
            "tag": self.designator,  # 位号
            "type": self.type,
            "part": self.part,
            "package": self.package,
            "image_path": self.image_path,
            "robot_ng_str": self.robot_ng_str,
            "robot_ng_code": self.robot_ng_code,
            "repair_ng_str": self.repair_ng_str,
            "repair_ng_code": self.repair_ng_code,
            "robot_result": m3 if self.is_robot_ng() else m1,
            "repair_result": m3 if self.is_repair_ng() else m1,
            "final_result": self.get_final_result(m1, m2, m3)
        }

    def to_standard_board_data_v1(self):
        """
        器件常用格式
        转换成常用的标准版格式  (拼板)
        """
        return {
            "comp_designator": self.designator,  # 位号
            "comp_type": self.type,
            "comp_part": self.part,
            "comp_package": self.package,
            "comp_robot_result": self.robot_ng_str,
            "comp_robot_code": self.robot_ng_code,
            "comp_user_result": self.repair_ng_str,
            "comp_user_code": self.repair_ng_code,
            "comp_image": self.image_path,
        }

    @property
    def robot_result(self):
        """
        器件检测结果是否ok
        :return: (True, OK)  (False, NG)
        """
        return self.robot_ng_code == "0"

    def is_robot_ng(self):
        """
        器件检测ng
        :return: true,检测ng   false,检测ok
        """
        return not self.robot_result

    @property
    def repair_result(self):
        """
        器件复判结果是否ok
        :return: (True, OK)  (False, NG)
        """
        return self.repair_ng_code == "0"

    def is_repair_ng(self):
        """
        复判ng
        :return:
        """
        return not self.repair_result

    def get_final_result(self, m1="PASS", m2="REPASS", m3="NG"):
        """
        获取器件的最终结果
        :return:
        :param m1: PASS-机器通过
        :param m2: REPASS-人工复判通过
        :param m3: NG-机器人工复判均不通过
        :return:
        """
        final_result_map = {
            "PASS": m1,
            "REPASS": m2,
            "NG": m3
        }
        return final_result_map.get(self.final_result)

    def __str__(self):
        return f"--> 器件信息 位号:{self.designator} " \
               f"板面：{self.board_side} " \
               f"检测结果:{self.robot_ng_str} " \
               f"复判结果:{self.repair_ng_str} " \
               f"最终结果:{self.final_result} " \
               f"检测不良代码:{self.robot_ng_code} " \
               f"复判不良代码:{self.repair_ng_code}"

    def yield_alg_entity(self) -> List[AlgEntity]:
        for alg in self.alg_data:
            yield AlgEntity(alg)


class BoardEntity(object):
    """
    拼板实体
    """

    def __init__(self, board_item: dict, pcb_entity):
        self.barcode: str = board_item.get("barcode")  # 拼板条码
        self.board_uuid = board_item.get("board_uuid")  # 拼板的uuid
        self.board_no: str = board_item.get("board_no")  # 拼板号

        self.b_barcode = board_item.get("b_barcode")  # b面条码，假设是合并发送时，该字段才会有值

        self.robot_result: bool = board_item.get("robot_result")  # 机器结果  (True, pass)  (False, ng)
        self.repair_result: bool = board_item.get("user_result")  # 复判结果  (True, pass)  (False, ng)

        # 最终结果  (PASS, 直通) (REPASS, 误报) (NG, 真实NG) (BadBoard, 坏板)
        self.final_result: str = board_item.get("final_result")

        self.comp_total_number: int = board_item.get("comp_total_number", 0)  # 拼板器件总数
        self.comp_robot_ng_number: int = board_item.get("comp_robot_ng_number", 0)  # 拼板机器NG器件数
        self.comp_repair_ng_number: int = board_item.get("comp_repair_ng_number", 0)  # 拼板复判NG器件数
        self.board_image_path: str = board_item.get("board_image_path")  # 431 拼版图

        self.comp_data: dict = board_item.get("comp_data")

        self.pcb_entity: PcbEntity = pcb_entity

    def get_comp_pass_number(self):
        """
        获取器件直通数
        """
        return self.comp_total_number - self.comp_robot_ng_number

    def get_comp_repass_number(self):
        """
        获取器件误报数

        注意：如果数据未经过复判，也就是没有man.xml，则该数据默认为0，被认为是不准确的
        :return:
        """
        return self.comp_robot_ng_number - self.comp_repair_ng_number

    def to_json_data(self, m1="PASS", m2="REPASS", m3="NG"):
        """
        拼板常用格式
        转换成常用的json格式
        {
          "barcode": "fake0001",
          "board_no": "1",
          "robot_result": "NG",
          "repair_result": "NG",
          "final_result": "NG",
          "comp_total_number": 147,
          "comp_robot_ng_number": 83,
          "comp_repair_ng_number": 4,
          "comp_repass_ng_number": 79
        }
        """
        return {
            "barcode": self.barcode,  # 条码
            "board_no": self.board_no,  # 拼板号
            "robot_result": self.get_robot_result(m1, m3),  # （PASS，检测OK）（NG，检测NG）
            "repair_result": self.get_repair_result(m1, m3),  # （PASS，复判OK）（NG，复判NG）
            "final_result": self.get_final_result(m1, m2, m3),  # （PASS，直通）（REPASS，误报）（NG，NG）
            "comp_total_number": self.comp_total_number,  # 器件总数
            "comp_robot_ng_number": self.comp_robot_ng_number,  # 器件检测NG数
            "comp_repair_ng_number": self.comp_repair_ng_number,  # 器件复判NG数
            "comp_repass_ng_number": self.comp_robot_ng_number - self.comp_repair_ng_number  # 器件误报数
        }

    def to_standard_board_data_v1(self):
        """
        转换成标准版的格式，（拼板）
        :return:
        """
        return {
            "pcb_sn": "",
            "pcb_track_line": "",
            "pcb_test_time": self.pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": self.pcb_entity.project_name,
            "pcb_final_result": self.pcb_entity.get_final_result(),
            "pcb_repair_user": self.pcb_entity.repair_user,
            "board_sn": self.barcode,
            "board_no": self.board_no,
            "board_comp_number": self.comp_total_number,
            "board_comp_user_ng_number": self.comp_repair_ng_number,
            "board_final_result": self.get_final_result(),
        }

    def __str__(self):
        return f"------> 拼板信息 NO:{self.board_no} SN:{self.barcode} " \
               f"器件数:{self.comp_total_number} 检测NG数:{self.comp_robot_ng_number} 复判NG数:{self.comp_repair_ng_number} 最终结果:{self.final_result}"

    def yield_comp_entity(self) -> List[CompEntity]:
        """
        返回器件实体
        """
        for comp_id, comp_item in self.comp_data.items():
            yield CompEntity(comp_id, comp_item)

    def get_final_result(self, m1="PASS", m2="REPASS", m3="NG", m4="BadBoard"):
        """
        获取拼板的最终结果
        :return:
        :param m1: PASS-机器通过
        :param m2: REPASS-人工复判通过
        :param m3: NG-机器人工复判均不通过
        :param m4: BadBoard-坏板
        :return:
        """
        final_result_map = {
            "PASS": m1,
            "REPASS": m2,
            "NG": m3,
            "BadBoard": m4,
        }
        return final_result_map.get(self.final_result, self.final_result)

    def get_robot_result(self, m1="OK", m2="NG"):
        """
        获取拼板的测试结果
        :param m1: OK-检测OK
        :param m2: NG-检测NG
        :return:
        """
        robot_result_map = {
            True: m1,
            False: m2
        }
        return robot_result_map.get(self.robot_result)

    def get_repair_result(self, m1="OK", m2="NG"):
        """
        获取拼板的复判结果
        :param m1: OK-复判OK
        :param m2: NG-复判NG
        :return:
        """
        robot_result_map = {
            True: m1,
            False: m2
        }
        return robot_result_map.get(self.repair_result)

    def is_robot_ng(self):
        """
        检测ng
        :return:
        """
        return not self.robot_result

    def is_repair_ng(self):
        """
        复判ng
        :return:
        """
        return not self.repair_result

    def get_repass_number(self):
        """
        器件误报总数
        """
        return self.comp_robot_ng_number - self.comp_repair_ng_number

    def is_bad_board(self) -> bool:
        """
        是否是坏板
        :return: bool: True: 坏板  False: 不是坏板
        """
        return self.get_final_result() == "BadBoard"


class PcbEntity(object):
    """
    整板实体
    """

    def __init__(self, pcb_info: dict, board_data: dict):
        self.pcb_info_json = pcb_info
        self.board_data_json = board_data

        self.order_id = pcb_info.get("order_id")  # 批次号，从主软件传过来的
        self.pcb = pcb_info.get("pcb")  # pcb
        self.project_name: str = pcb_info.get("project_name")  # 程序名
        self.comp_count: int = int(pcb_info.get("comp_count", 0))  # 整板器件数
        self.pcb_barcode = pcb_info.get("pcb_barcode")  # 板边条码/整板条码
        self.pcb_image: list = pcb_info.get("pcb_image")  # 整板图像
        self.bom = pcb_info.get("bom")  # bom
        self.track_index: int = pcb_info.get("track_index")  # （1，1轨）（2，2轨）
        self.board_count: int = int(pcb_info.get("board_count", 0))
        self.board_side = pcb_info.get("board_side")  # (T, 顶面) (B, 底面) (T+B, 合并发送)
        self.review_time = pcb_info.get("review_time")  # 复判时间
        self.repair_user = pcb_info.get("repair_user")  # 维修站复判用户名
        self.inspect_time = pcb_info.get("inspect_time")  # 检测时间，一般通过:get_start_time() 这个方法去获取，并进行对应格式化
        self.pcb_robot_result: bool = pcb_info.get("pcb_robot_result")  # 整板机器结果  True/False
        self.pcb_repair_result: bool = pcb_info.get("pcb_user_result")  # 整板复判结果  True/False
        self.review_path: list = pcb_info.get("review_path")  # 数据包路径
        self.final_result: str = pcb_info.get("final_result")  # 整板最终结果  (PASS, 直通) (REPASS, 误报) (NG, 真实NG)
        self.all_barcode: list = pcb_info.get("all_barcode", [])
        self.origin_cycle_time: str = pcb_info.get("cycle_time", "0")  # 整板的CT时间
        self.fixture_barcode: str = pcb_info.get("fixture_barcode")  # 治具条码

        self.carry_in_time: str = pcb_info.get("carry_in_time", "")  # 进板时间点
        self.carry_out_time: str = pcb_info.get("carry_out_time", "")  # 出板时间点
        self.fov_count: str = pcb_info.get("fov_count", "")  # fov数量
        self.image_path: str = pcb_info.get("image_path", "")  # 431 整板图
        self.image_path_401 = pcb_info.get("image_path_401", "")  # 401整板图

        try:
            cycle_time = int(pcb_info.get("cycle_time", 0))
        except Exception as e:
            print(f"检测时间转换出错: {e}")
            cycle_time = 0

        self.cycle_time = cycle_time

    def get_bad_data_info(self):
        """
        获取整板的坏件信息

        1-4拼板，假如拼板2是坏板，则返回格式如下：
        {
            "1": False,
            "2": True,
            "3": False,
            "4": False
        }
        :return:
        """
        bad_data_map = self.pcb_info_json.get("bad_data_map", {})

        return {
            no: item.get("bad_board") == "1" for no, item in bad_data_map.items()
        }

    def get_unique_sn(self) -> str:
        """
        获取整板上的一个条码
        优先获取整板条码，如果没有，则取第一个拼板的条码
        """
        ret_sn = self.pcb_barcode

        if not ret_sn:
            for board_entity in self.yield_board_entity():
                barcode = board_entity.barcode
                if barcode:
                    ret_sn = board_entity.barcode
                    break

        return ret_sn

    def get_image_path_431(self) -> str:
        """
        获取单面431整板图, 优先取文件名里包含 `thumb_Bgr` 字符的图片
        """
        if os.path.isdir(self.image_path):
            thumbnail_dir = f"{self.image_path}thumbnail"
            for file in os.listdir(thumbnail_dir):
                if "thumb_Bgr" in file:
                    filename = f"{thumbnail_dir}/{file}"
                    return filename

            for file in os.listdir(thumbnail_dir):
                if file.endswith(".jpg"):
                    filename = f"{thumbnail_dir}/{file}"
                    return filename

            return ""

        elif self.image_path.endswith(".jpg"):
            return self.image_path
        else:
            log.warning(f"找不到431整板图源路径！")
            return ""

    def get_image_rect_431(self):
        # 获取带标注的431整板图(xxx_rect.jpg)
        if not self.image_path:
            # log.warning(f"image_path为空,无法获取431整版图")
            return ""

        if os.path.isdir(self.image_path):
            # 若self.image_path是一个目录
            thumbnail_dir = f"{self.image_path}/thumbnail"
            if os.path.exists(thumbnail_dir):
                for file in os.listdir(thumbnail_dir):
                    if file.endswith("_rect.jpg"):
                        filename = f"{thumbnail_dir}/{file}"
                        # log.info(f"找到带标注的431整板图:{filename}")
                        return filename
                    elif file.endswith(".jpg"):
                        filename = f"{thumbnail_dir}/{file}"
                        log.info(f"未找到标注整板图,使用原图:{filename}")
                        return filename

                log.warning(f"在目录{thumbnail_dir}找不到带标注的431整版图！")
                return ""
        elif self.image_path.endswith(".jpg"):
            # 若self.image_path直接是.jpg结尾的图片
            dir_path = os.path.dirname(
                self.image_path)  # 获取文件路径中的目录部分 例如：从 /home/<USER>/ais/run/results/PCB/B5/CK505CP10203452.jpg 提取出 /home/<USER>/ais/run/results/PCB/B5
            file_name = os.path.basename(
                self.image_path)  # 获取文件路径中的文件名部分 例如：从 /home/<USER>/ais/run/results/PCB/B5/CK505CP10203452.jpg 提取出 CK505CP10203452.jpg
            base_name = os.path.splitext(file_name)[
                0]  # 将文件名拆分为基本名称和扩展名，并取基本名称部分 例如: 从 CK505CP10203452.jpg 提取出 CK505CP10203452

            rect_file = f"{dir_path}/{base_name}_rect.jpg"  # 拼接成带_rect.jpg的图片路径
            if os.path.exists(rect_file):
                log.info(f"找到带标注的431整板图:{rect_file}")
                return rect_file
            else:
                log.info(f"未找到标注图,使用原图:{self.image_path}")
                return self.image_path
        else:
            log.warning(f"找不到带标注的431整版图路径！ image_path={self.image_path}")
            return ""

    def get_image_rect_401(self):
        # 获取带标注的401整板图(xxx_rect.jpg)
        if not self.image_path_401:
            log.warning(f"image_path_401为空,无法获取401整版图")
            return ""

        if os.path.isdir(self.image_path_401):
            # 若self.image_path_401是一个目录
            thumbnail_dir = f"{self.image_path_401}/thumbnail"
            if os.path.exists(thumbnail_dir):
                for file in os.listdir(thumbnail_dir):
                    if file.endswith("_rect.jpg"):
                        filename = f"{thumbnail_dir}/{file}"
                        # log.info(f"找到带标注的401整板图:{filename}")
                        return filename
                    elif file.endswith(".jpg"):
                        filename = f"{thumbnail_dir}/{file}"
                        log.info(f"未找到标注图,使用原图:{filename}")
                        return filename

                log.warning(f"在目录{thumbnail_dir}找不到带标注的401整版图！")
                return ""
        elif self.image_path_401.endswith(".jpg"):
            # 若self.image_path_401直接是.jpg结尾的图片
            dir_path = os.path.dirname(self.image_path_401)
            file_name = os.path.basename(self.image_path_401)
            base_name = os.path.splitext(file_name)[0]

            rect_file = f"{dir_path}/{base_name}_rect.jpg"
            if os.path.exists(rect_file):
                log.info(f"找到带标注的401整板图:{rect_file}")
                return rect_file
            else:
                log.info(f"未找到标注图,使用原图:{self.image_path_401}")
                return self.image_path_401
        else:
            log.warning(f"找不到带标注的401整版图路径！ image_path_401={self.image_path_401}")
            return ""

    def list_board_ids(self):
        """
        获取所有拼板号
        :return:
        """
        return self.pcb_info_json.get('board_ids', [])

    def to_json_data(self, m1="PASS", m2="REPASS", m3="NG", fmt="%Y-%m-%d %H:%M:%S"):
        """
        整板常用格式
        转换成常用的json格式
        {
          "project_name": "333.001",
          "barcode": "D3VZ15KBT4021000PO2050",
          "order_id": "",
          "pcb": "333",
          "bom": "001",
          "comp_count": 147,
          "track_index": 1,
          "board_count": 2,
          "board_side": "T",
          "review_time": "2023-04-21 11:19:07",
          "repair_user": "",
          "start_time": "2023-04-21 11:18:34",
          "end_time": "2023-04-21 11:18:39",
          "cycle_time": 5.51,
          "robot_result": "NG",
          "repair_result": "NG",
          "final_result": "NG",
          "pcb_image1": "/home/<USER>/Desktop/ReviewData/40x/ng/T_20230421111834308_1_NG/thumbnail/0/thumb.jpg",
          "review_path1": "/home/<USER>/Desktop/ReviewData/40x/ng/T_20230421111834308_1_NG"
        }
        """
        return {
            "project_name": self.project_name,
            "barcode": self.pcb_barcode,
            "order_id": self.order_id,
            "pcb": self.pcb,
            "bom": self.bom,
            "comp_count": self.comp_count,
            "track_index": self.track_index,
            "board_count": self.board_count,
            "board_side": self.board_side,
            "review_time": self.get_review_time().strftime(fmt),
            "repair_user": self.repair_user,
            "start_time": self.get_start_time().strftime(fmt),
            "end_time": self.get_end_time().strftime(fmt),
            "cycle_time": self.get_cycle_time(),
            "robot_result": self.get_robot_result(m1, m3),
            "repair_result": self.get_repair_result(m1, m3),
            "final_result": self.get_final_result(m1, m2, m3),
            "pcb_image1": self.pcb_image[0],
            "review_path1": self.review_path[0],
        }

    def to_standard_json(self) -> dict:
        """
        转换成整板的标准json格式
        :return:
        """
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        board_data = []
        for board_entity in self.yield_board_entity():
            log.info(board_entity)

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            pcb_comp_number += board_entity.comp_total_number
            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str
                })

            board_data.append({
                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        return {
            "pcb_sn": self.pcb_barcode,
            "pcb_track_line": self.track_index,
            "pcb_test_time": self.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": self.project_name,
            "pcb_user_result": self.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": self.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": self.get_final_result(),
            "pcb_repair_user": self.repair_user,
            "pcb_board_number": self.board_count,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_comp_number": pcb_comp_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "board_data": board_data
        }

    def get_pixel_size431(self):
        """
        获取431的像素转换比和图片压缩比
        """
        return self.pcb_info_json.get("pixel_size_431", None)

    def __str__(self):
        """
        返回拼板信息
        """
        return f"----------> 整板信息 SN: {self.pcb_barcode} 板面:{self.board_side} 轨道:{self.track_index}轨 " \
               f"拼板数:{self.board_count} 器件总数:{self.comp_count} 最终结果:{self.final_result}"

    def get_unknown_t_pcb_image(self):
        """
        获取未知机型大图路径
        未知机型。201、40x
        """
        mes_path = self.get_pcb_pcb_t_review_path()

        pcb_image = f"{mes_path}/thumbnail/0/thumb.jpg"

        if not os.path.exists(pcb_image):
            pcb_image = ""
            all_files = os.listdir(mes_path)
            for file in all_files:
                if file.endswith(".jpg"):
                    pcb_image = f"{mes_path}/{file}"
                    break

        return pcb_image

    def get_unknown_b_pcb_image(self):
        """
        获取未知机型大图路径
        未知机型。201、40x
        """
        try:
            mes_path = self.get_pcb_pcb_b_review_path()

            pcb_image = f"{mes_path}/thumbnail/0/thumb.jpg"

            if not os.path.exists(pcb_image):
                pcb_image = ""
                all_files = os.listdir(mes_path)
                for file in all_files:
                    if file.endswith(".jpg"):
                        pcb_image = f"{mes_path}/{file}"
                        break

            return pcb_image
        except Exception as err:
            log.warning(f"未获取到B面大板图片，err：{err}")
            return ""

    @classmethod
    def _list_all_pcb_image(cls, mes_path) -> list:
        """
        获取所有大图

        :param mes_path:
        :return:
        """
        ret_list = []
        pcb_image = ""
        all_files = os.listdir(mes_path)
        for file in all_files:
            if file.endswith(".jpg"):
                pcb_image = f"{mes_path}/{file}"
                ret_list.append(pcb_image)

        if not pcb_image:
            thumbnail_dir = f"{mes_path}/thumbnail"

            if os.path.exists(thumbnail_dir):
                for file in os.listdir(thumbnail_dir):
                    filename = f"{thumbnail_dir}/{file}"

                    if file.endswith(".jpg"):
                        ret_list.append(filename)

                    if os.path.isdir(filename):
                        f1 = f"{filename}/thumb.jpg"
                        if os.path.exists(f1):
                            ret_list.append(f1)
        return ret_list

    @classmethod
    def _list_all_pcb_image_v2(cls, mes_path) -> list:
        """
        获取所有大图
        20240814，会优先获取主软件保存的高清大板图
        :param mes_path:
        :return:
        """
        ret_list = []
        pcb_image = ""
        all_files = os.listdir(mes_path)
        for file in all_files:
            if file.endswith(".jpg"):
                pcb_image = f"{mes_path}/{file}"
                ret_list.append(pcb_image)

        if not pcb_image:
            thumbnail_dir = f"{mes_path}/thumbnail"

            if os.path.exists(thumbnail_dir):
                for file in os.listdir(thumbnail_dir):
                    filename = f"{thumbnail_dir}/{file}"

                    if file.endswith(".jpg"):
                        ret_list.append(filename)
                        break

                if not ret_list:
                    for file in os.listdir(thumbnail_dir):
                        filename = f"{thumbnail_dir}/{file}"
                        if os.path.isdir(filename):
                            f1 = f"{filename}/thumb.jpg"
                            if os.path.exists(f1):
                                ret_list.append(f1)

        return ret_list

    def list_all_pcb_image(self) -> list:
        """
        获取所有大图, 会有高清原图、缩略整板图
        :return:
        """
        if len(self.review_path) >= 2:
            log.info(f"合并发送，存在多张大图！")
            mes_path1 = self.review_path[0]
            mes_path2 = self.review_path[1]

            ret1 = self._list_all_pcb_image(mes_path1)
            ret2 = self._list_all_pcb_image(mes_path2)

            return ret1 + ret2
        else:
            return self._list_all_pcb_image(self.review_path[0])

    def list_all_pcb_image_v2(self) -> list:
        """
        获取所有大图, 会优先获取高清大板图，如果没有，则获取缩略大板图
        :return:
        """
        if len(self.review_path) >= 2:
            log.info(f"合并发送，存在多张大图！")
            mes_path1 = self.review_path[0]
            mes_path2 = self.review_path[1]

            ret1 = self._list_all_pcb_image_v2(mes_path1)
            ret2 = self._list_all_pcb_image_v2(mes_path2)

            return ret1 + ret2
        else:
            return self._list_all_pcb_image_v2(self.review_path[0])

    @classmethod
    def _get_origin_pcb_img(cls, mes_path) -> list:
        """
        获取高清原图
        :param mes_path:
        :return:
        """
        ret_list = []

        thumbnail_dir = f"{mes_path}/thumbnail"

        if os.path.exists(thumbnail_dir):
            for file in os.listdir(thumbnail_dir):
                filename = f"{thumbnail_dir}/{file}"
                if file.endswith(".jpg"):
                    ret_list.append(filename)

        return ret_list

    def list_all_pcb_image_origin_img(self) -> list:
        """
        获取高清原图地址，如果是501 t/b面将会有两张
        :return:
        """
        if len(self.review_path) >= 2:
            log.info(f"合并发送，存在多张大图！")
            mes_path1 = self.review_path[0]
            mes_path2 = self.review_path[1]

            ret1 = self._get_origin_pcb_img(mes_path1)
            ret2 = self._get_origin_pcb_img(mes_path2)

            return ret1 + ret2
        else:
            return self._get_origin_pcb_img(self.review_path[0])

    def get_pcb_t_image(self):
        """
        获取整板T面图片
        """
        return self.pcb_image[0]

    def get_pcb_b_image(self):
        """
        获取整板B面图片
        """
        ret_str = ""

        if len(self.pcb_image) > 1:
            ret_str = self.pcb_image[1]

        return ret_str

    def get_pcb_t_report_xml(self) -> str:
        return f"{self.get_pcb_pcb_t_review_path()}/report.xml"

    def get_pcb_b_report_xml(self) -> str:
        b_review_path = self.get_pcb_pcb_b_review_path()
        if b_review_path:
            return f"{b_review_path}/report.xml"
        else:
            return ""

    def get_pcb_pcb_t_review_path(self):
        """
        获取整板B面review path
        """
        return self.review_path[0]

    def get_pcb_pcb_b_review_path(self):
        """
        获取整板B面review path
        """
        return self.review_path[1] if len(self.review_path) >= 2 else ""

    def get_final_result(self, m1="PASS", m2="REPASS", m3="NG"):
        """
        获取整板的最终结果
        :return:
        :param m1: PASS-机器通过
        :param m2: REPASS-人工复判通过
        :param m3: NG-机器人工复判均不通过
        :return:
        """
        final_result_map = {
            "PASS": m1,
            "REPASS": m2,
            "NG": m3
        }
        return final_result_map.get(self.final_result)

    def get_robot_result(self, m1="OK", m2="NG"):
        """
        获取整板的测试结果
        :param m1: OK-检测OK
        :param m2: NG-检测NG
        :return:
        """
        robot_result_map = {
            True: m1,
            False: m2
        }
        return robot_result_map.get(self.pcb_robot_result)

    def is_robot_ng(self):
        """
        检测ng
        :return:
        """
        return not self.pcb_robot_result

    def is_repair_ng(self):
        """
        复判ng
        :return:
        """
        return not self.pcb_repair_result

    def get_repair_result(self, m1="OK", m2="NG"):
        """
        获取拼板的复判结果
        :param m1: OK-复判OK
        :param m2: NG-复判NG
        :return:
        """
        robot_result_map = {
            True: m1,
            False: m2
        }
        return robot_result_map.get(self.pcb_repair_result)

    def get_cycle_time(self) -> float:
        """
        获取检测时间
        return 单位s
        """
        return round(self.cycle_time / 1000, 2)

    def get_start_time(self) -> datetime:
        """
        获取检测开始时间  20240719调整
        """
        end_time = self.get_end_time()

        try:
            cycle_second = int(self.cycle_time / 1000)
        except Exception as err:
            log.warning(f"未获取到cycle_time, error: {err}")
            cycle_second = 0

        if not cycle_second:
            return end_time

        return end_time - timedelta(seconds=cycle_second)

    def get_end_time(self) -> datetime:
        """
        获取检测结束时间  20240719调整
        """
        if not self.inspect_time:
            return datetime.now()

        return datetime.strptime(self.inspect_time, "%Y-%m-%d %H:%M:%S")

    def get_review_time(self):
        """
        获取复判时间
        """
        return datetime.strptime(self.review_time, "%Y-%m-%d %H:%M:%S")

    def get_track_index(self) -> int:
        """
        获取轨道号
        (1, 1轨道)  (2, 2轨道)
        """
        return self.track_index

    def get_board_side(self):
        """
        获取板面
        (T, 顶面)  (B, 底面) (T+B, 合并发送)
        """
        return self.board_side

    def yield_board_entity(self) -> List[BoardEntity]:
        """
        返回拼板实体
        """
        for _, board_item in self.board_data_json.items():
            yield BoardEntity(board_item, self)

    @staticmethod
    def dict_board_info(report_xml):
        """
        获取小板信息
        坏板信息，器件数量
        """
        root = XmlUtil.get_xml_root_by_file(report_xml)

        boards = root.find("boards")

        ret_dict = {}
        for board in boards:
            board_no = board.attrib.get("id")

            bad_board = board.find("BadBoard").text
            barcode = board.find("barcode").text
            comp_size = board.find("componentsize").text

            ret_dict[board_no] = {
                "bad_board": bad_board,  # ("1", 坏板)  ("0", 好板)
                "comp_size": comp_size,
                "barcode": barcode
            }

        return ret_dict

    def dict_t_board_info(self):
        """
        获取T面坏板信息
        :return:
        """
        t_report_xml = self.get_pcb_t_report_xml()
        return self.dict_board_info(t_report_xml)

    def dict_b_board_info(self):
        """
        获取B面坏板信息
        :return:
        """
        return self.dict_board_info(self.get_pcb_b_report_xml())

    def standard_to_json(self):
        """
        标准版格式: json
        :return:
        """
        board_data = []
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0

        board_user_ng_count = 0
        board_robot_ng_count = 0

        log.info(self)
        for board_entity in self.yield_board_entity():
            log.info(board_entity)
            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number

            if board_entity.is_robot_ng():
                board_robot_ng_count += 1

            if board_entity.is_repair_ng():
                board_user_ng_count += 1

            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data.append({
                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        return {
            "pcb_sn": self.pcb_barcode,
            "pcb_track_line": self.track_index,
            "pcb_test_time": str(self.get_start_time()),
            "pcb_project_name": self.project_name,
            "pcb_user_result": self.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": self.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": self.get_final_result(),
            "pcb_repair_user": self.repair_user,
            "pcb_board_number": self.board_count,
            "pcb_board_user_ng_number": board_user_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,
            "board_data": board_data
        }

    def get_board_box_position(self, report_xml: str = "", image_scale: float = 0.0) -> Dict[str, tuple]:
        """
        获取拼板图在整板图中的坐标位置
        元组中元素的顺序是需要裁剪得到的图片在原图中的左上、右下坐标，即(left, upper, right, lower)


        拿到图片之后可以使用pillow切割图片，如下
        ```
        from PIL import Image

        # 打开图像
        image = Image.open('image.jpg')

        # 定义切割区域（左上角坐标和右下角坐标）
        box = (100, 100, 300, 300)

        # 切割图像
        cropped_image = image.crop(box)

        # 保存切割后的图像
        cropped_image.save('cropped_image.jpg')
        ```
        """
        # if report_xml:
        #     report_xml = report_xml
        # else:
        #     report_xml =
        if not report_xml:
            report_xml = self.get_pcb_t_report_xml()

        root = XmlUtil.get_xml_root_by_file(report_xml)

        if not image_scale:
            image_scale = float(root.find("imageScale").text)

        log.info(f"image_scale: {image_scale}")

        pixel_width = float(root.find("orignal-pixel-size").find("width").text)
        pixel_height = float(root.find("orignal-pixel-size").find("height").text)

        boards = root.find("boards").findall("board")

        # 每一块小板的位置信息  {board_id: <位置信息>}
        position_map = {}

        for board in boards:
            board_id = board.attrib.get("id")
            geometry_ele = board.find("geometry")

            board_cx = float(geometry_ele.find("cx").text)
            board_cy = float(geometry_ele.find("cy").text)
            board_width = float(geometry_ele.find("width").text)
            board_height = float(geometry_ele.find("height").text)

            box = OtherUtil.geometry_to_box(
                pixel_width, pixel_height, image_scale,
                board_cx, board_cy, board_width, board_height
            )

            position_map[board_id] = box

        return position_map

    def get_pad_test_data(self) -> Tuple[dict, str]:
        """
        解析report.xml，并获取到component的数据
        :return:
        """
        # file_path = self.get_report_xml_path()
        file_path = self.get_pcb_t_report_xml()
        root = XmlUtil.get_xml_root_by_file(file_path)

        boards = root.find("boards")

        fixture_item = root.find("fixtrue-barcode")
        if fixture_item is not None:
            fixture_barcode = fixture_item.text
        else:
            fixture_barcode = ""

        data_ret = {}
        for board in boards:
            comps = board.find("components")
            for comp in comps:
                designator = comp.find("designator").text
                all_child = comp.find("children")

                comp_pad = []

                if len(all_child) == 0:
                    # 没有pad
                    pad_ele = comp.find("SolderpasteParam")

                    if pad_ele is None:
                        continue

                    height = pad_ele.find("height").text
                    offset = pad_ele.find("offset").text
                    area = pad_ele.find("area").text
                    valumn = pad_ele.find("valumn").text
                    real_height = pad_ele.find("realHeight").text
                    result = pad_ele.find("result").text

                    offset = offset.split("   ")

                    comp_pad.append({
                        "designator": designator,
                        "pad_no": f"{designator}-1",
                        "a": area,
                        "v": valumn,
                        "h": height,
                        "rh": real_height,
                        "sx": offset[0][2:],
                        "sy": offset[1][2:],
                        "result": result,
                    })

                else:
                    # 有多个pad
                    for ix, child in enumerate(all_child):
                        pad_ele = child.find("SolderpasteParam")  # 锡膏检测参数
                        if pad_ele is None:
                            continue

                        height = pad_ele.find("height").text
                        offset = pad_ele.find("offset").text
                        area = pad_ele.find("area").text
                        valumn = pad_ele.find("valumn").text
                        real_height = pad_ele.find("realHeight").text
                        result = pad_ele.find("result").text

                        offset = offset.split("   ")
                        comp_pad.append({
                            "designator": designator,
                            "pad_no": f"{designator}-{ix + 1}",
                            "a": area,
                            "v": valumn,
                            "h": height,
                            "rh": real_height,
                            "sx": offset[0][2:],
                            "sy": offset[1][2:],
                            "result": result
                        })

                data_ret[comp.find("uuid").text] = comp_pad

        log.info(f"共有主pad点：{len(data_ret)}")

        return data_ret, fixture_barcode

    def get_report_uuid(self):
        """
        获取report uuid
        :return:
        """
        report_xml = self.get_pcb_t_report_xml()
        log.info(f"get report uuid by report.xml...")
        root = XmlUtil.get_xml_root_by_file(report_xml)
        report_uuid = root.find('uuid').text
        log.info(f"report uuid: {report_uuid}")
        return report_uuid

    def get_insect_mes_type(self):
        """
        获取主软件传过来的原始 InspectMesType repair, inspector
        :return:
        """
        return self.pcb_info_json.get('inspect_type', 'repair')

    def get_all_comp_barcode(self):
        """
        获取T/B面所有的器件条码

        return param: -------------------
        {
            "1": [
                {
                    "comp_tag": "COMP1002",
                    "comp_barcode": "D3VZ15KBT4021000PO2050"
                }
            ],
            "2": []
        }
        return param: -------------------
        """
        t_report_xml = self.get_pcb_t_report_xml()
        t_comp_barcode_map = XmlUtil.dict_comp_barcode(t_report_xml)

        b_report_xml = self.get_pcb_b_report_xml()
        if b_report_xml:
            # 如果存在B面，则把B面的器件条码也附加到T面一起返回
            b_comp_barcode_map = XmlUtil.dict_comp_barcode(b_report_xml)

            for k, v in t_comp_barcode_map.items():
                b_data = b_comp_barcode_map.get(k, [])
                if b_data:
                    v.extend(b_data)

        return t_comp_barcode_map

    def get_height_measure_and_color_width_data_431(self) -> dict:
        """
        获取431机型`3D高度及颜色宽度算法`的数据
        """
        review_path = self.get_pcb_pcb_t_review_path()
        with SqlLiteHelper(review_path) as sql_helper:
            data_map = sql_helper.get_height_measure_and_color_width_data()
            log.info(f"alg len: {len(data_map)}")
            return data_map


if __name__ == '__main__':
    pass
