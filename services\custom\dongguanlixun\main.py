# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/11 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：东莞立讯
"""
import json
import os
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanlixun release v1.0.0.17",
        "device": "203P、303-L",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-11 09:23  init
date: 2023-07-11 14:44  上传数据到mes
date: 2023-08-14 15:59  改成按拼板上传数据
date: 2023-12-13 17:53  增加不良跳过的功能
date: 2024-03-11 11:12  增加AI接口调用
date: 2024-03-13 15:53  Line传轨道, Result传P/F 
date: 2024-03-15 11:19  兼容接口返回
date: 2024-03-15 14:30  增加配置项控制是否上传数据到AI服务器
date: 2024-03-15 21:36  bugfix
date: 2024-04-22 14:13  新增条码校验+上传数据功能
date: 2024-05-06 17:55  增加删除fov图功能
date: 2024-05-20 16:17  增加配置仅上传检测NG的fov图
date: 2024-08-22 15:19  增加【上传FOV信息超时时间】配置项
date: 2024-09-05 15:28  删除启动时去删除fov图的业务逻辑
date: 2024-10-29 16:11  出现错漏反立碑时，不上传内容到mes
date: 2025-01-17 09:19  jira: 35470 上传FOV，不删除fov图
date: 2025-01-21 18:08  jira->29777: 增加整板过站方式
""", }

    form = {
        "api_url_mes_check": {
            "ui_name": "条码校验(Mes服务器)",
            "value": "http://*************/DIP_AOI/AOI/api/Mes/AOISPICheckRoute_DIP"
        },
        "api_url_mes_data": {
            "ui_name": "上传数据(Mes服务器)",
            "value": "http://*************/DIP_AOI/AOI/api/Mes/AOISPISngo_DIP"
        },
        "part_no_ui": {
            "ui_name": "机种编码(Mes服务器)",
            "value": ""
        },
        "terminal_id": {
            "ui_name": "站点ID(Mes服务器)",
            "value": ""
        },
        "user_id": {
            "ui_name": "用户ID(Mes服务器)",
            "value": ""
        },
        "machine_id": {
            "ui_name": "线体(Mes服务器)",
            "value": ""
        },
        "line_no_ui": {
            "ui_name": "产线名(Mes服务器)",
            "value": ""
        },
        "machine_type": {
            "ui_name": "设备名称(Mes服务器)",
            "value": ""
        },
        "api_url_ai": {
            "ui_name": "接口URL(AI服务器)",
            "value": "http://127.0.0.1:1803"
        },
        "station": {
            "ui_name": "站点名称(AI服务器)",
            "value": "PreAOI",
        }
    }

    combo = {
        "get_mes_result": {
            "ui_name": "从AI获取复判结果",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "upload_data_to_ai": {
            "ui_name": "上传数据到AI服务器",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "api_timeout": {
            "ui_name": "获取结果超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "30", "60", "100", "300"],
            "value": "1",
        },
        "api_timeout_req": {
            "ui_name": "上传FOV信息超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "15", "20", "25", "30", "35", "60", "100", "300"],
            "value": "5",
        },
        "comp_ng_list1": {
            "ui_name": "上传检测结果范围(fov图)",
            "item": ["全部", "仅检测NG"],
            "value": "全部",
        },
        "send_type_checkout": {
            "ui_name": "上传过站数据",
            "item": ["整板发送", "拼板发送"],
            "value": "拼板发送",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # api_url = data_dao.get_value_by_cons_key("api_url")
        terminal_id = data_vo.get_value_by_cons_key("terminal_id")
        user_id = data_vo.get_value_by_cons_key("user_id")
        machine_id = data_vo.get_value_by_cons_key("machine_id")

        api_url_ai = data_vo.get_value_by_cons_key("api_url_ai")
        station = data_vo.get_value_by_cons_key("station")
        # comp_ng_list = data_dao.get_value_by_cons_key("comp_ng_list")
        upload_data_to_ai = data_vo.get_value_by_cons_key("upload_data_to_ai")
        api_url_mes_data = data_vo.get_value_by_cons_key("api_url_mes_data")
        part_no_ui = data_vo.get_value_by_cons_key("part_no_ui")
        line_no_ui = data_vo.get_value_by_cons_key("line_no_ui")
        machine_type = data_vo.get_value_by_cons_key("machine_type")
        comp_ng_list1 = data_vo.get_value_by_cons_key("comp_ng_list1")
        api_timeout_req = data_vo.get_value_by_cons_key("api_timeout_req", to_int=True)
        send_type_checkout = data_vo.get_value_by_cons_key("send_type_checkout")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        inspect_type = other_data.get('inspect_type')  # (inspector,主软件检测完发送)  (repair,维修站复判后发送)
        # track_index = pcb_entity.track_index
        pcb_sn = pcb_entity.pcb_barcode

        upstream_json_data = other_data.get("upstream_json_data")
        aoi_type = upstream_json_data.get("ReviewFlag", "0")  # 0是人工, 1是AI复判
        self.log.info(f"维修站传过来的复判标识为：{aoi_type}   (ps:没传则默认取0)")

        repair_comp_list_ai = []
        ret_res = self.x_response()

        comp_data_info = {}

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        board_list_pcb = []

        self.log.info(f"发送模式：{send_type_checkout}")

        is_upload_mes_pcb = True

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data = []
            board_list = []

            # 当机器检测漏件、错件、反件、立碑时，无论人工复判是NG还是PASS，都不请求接口发送板卡数据给mes系统。
            is_upload_mes = True

            for comp_entity in board_entity.yield_comp_entity():
                origin_robot_ng_code = comp_entity.get_origin_robot_ng_code()
                if origin_robot_ng_code in [
                    "1",
                    "2",
                    "3",
                    "4",
                ]:
                    is_upload_mes = False
                    is_upload_mes_pcb = False
                    self.log.warning(f"出现错漏反立碑  ---> {comp_entity.designator} {comp_entity.robot_ng_str}")

                # ---------- ai复判需求
                comp_data_info[comp_entity.comp_id] = {
                    "board_no": board_entity.board_no,
                    "is_robot_ng": comp_entity.is_robot_ng()
                }
                # ---------- ai复判需求

                if comp_entity.is_robot_ng():
                    comp_data.append({
                        "name": comp_entity.designator,
                        "code": comp_entity.robot_ng_code,
                        "confirm": comp_entity.robot_ng_str,
                        "r1": "",
                        "r2": "",
                        "r3": "",
                        "Name": "",
                        "Package": "",
                        "Confirm": ""
                    })

                    repair_comp_list_ai.append({
                        "board": board_no,
                        "part_name": comp_entity.designator,
                        "part_code": comp_entity.part,
                        "result": comp_entity.get_final_result("P", "P", "F"),
                    })

            board_item = {
                "program": pcb_entity.project_name,
                "barcode": barcode,
                "lineNo": line_no_ui,
                "ship": "",
                "machineTpye": machine_type,
                "lotNo": "",
                "testDate": start_time,
                "testTime": end_time,
                "status": board_entity.get_final_result("0", "1", ""),
                "surface": "",
                "componentNum": str(board_entity.comp_total_number),
                "NGcomponentNum": board_entity.comp_repair_ng_number,
                "NGcomponentList": comp_data
            }

            board_list.append(board_item)
            board_list_pcb.append(board_item)

            board_param = {
                "Terminal_id": terminal_id,
                "UserId": user_id,
                "MachineId": machine_id,
                "PanelSn": barcode,
                "AoiType": aoi_type,
                "partNo": part_no_ui,
                "testResult": board_entity.get_repair_result("OK", "NG"),
                "TestItem": json.dumps({"Boards": board_list}, ensure_ascii=False)
            }

            if inspect_type != 'inspector' and send_type_checkout == "拼板发送":
                if is_upload_mes:
                    self.log.info(f"按拼板上传数据到Mes服务器...")
                    ret = xrequest.RequestUtil.post_form(api_url_mes_data, board_param)
                    msg = ret.get("msg", "")

                    if not msg:
                        msg = ret.get('Msg', '')

                    if "ok" not in msg.lower():
                        ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{msg}")
                else:
                    self.log.warning(f"出现错漏反立碑时，不上传内容到mes！")

        if inspect_type != 'inspector' and send_type_checkout == "整板发送":
            if is_upload_mes_pcb:
                self.log.info(f"按整板上传数据到Mes服务器...")

                pcb_param = {
                    "Terminal_id": terminal_id,
                    "UserId": user_id,
                    "MachineId": machine_id,
                    "PanelSn": pcb_sn,
                    "AoiType": aoi_type,
                    "partNo": part_no_ui,
                    "testResult": pcb_entity.get_repair_result("OK", "NG"),
                    "TestItem": json.dumps({"Boards": board_list_pcb}, ensure_ascii=False)
                }

                ret = xrequest.RequestUtil.post_form(api_url_mes_data, pcb_param)
                msg = ret.get("msg", "")

                if not msg:
                    msg = ret.get('Msg', '')

                if "ok" not in msg.lower():
                    ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{msg}")
            else:
                self.log.warning(f"出现错漏反立碑时，不上传内容到mes！")

        if upload_data_to_ai == "Yes":
            if inspect_type == 'inspector':
                self.log.info(f"传输FOV信息给AI...")
                # self.log.info(f"comp info: {comp_data_info}")
                comp_image = pcb_entity.get_pcb_pcb_t_review_path()
                self.log.info(f"{comp_image=}")

                fov_path = comp_image.replace('results', 'fovs')

                fov_path = fov_path.replace("_NG", "")
                self.log.info(f"{fov_path=}")

                fov_json = f"{fov_path}/fovs.json"

                if not os.path.exists(fov_json):
                    return self.x_response("false", f"请打开主软件的保存fov图设置！")

                fov_data = xutil.FileUtil.load_json_file(fov_json)
                # self.log.info(f"fov json: {fov_data}")

                fov_list = fov_data.get('fov', [])
                self.log.info(f"将上传fov数据，fov数量：{len(fov_list)}")

                count = 0
                for item in fov_list:
                    fov_comp_list = item.get('comp', [])
                    fov_index = item.get('index')
                    filename = str(fov_index).rjust(4, '0')
                    fov_src_img = f"{fov_path}/0/{filename}.jpg"

                    if not os.path.exists(fov_src_img):
                        return self.x_response("未找到需要上传的fov图片，请主软件勾选上【保存FOV图像】设置！")

                    fov_data_1 = []
                    fov_result = True
                    for fov_comp in fov_comp_list:
                        comp_uuid = fov_comp.get('comp_uuid').replace('{', '').replace('}', '')
                        board_no_ = comp_data_info.get(comp_uuid).get('board_no', '1')

                        is_robot_ng = comp_data_info.get(comp_uuid).get('is_robot_ng')
                        if is_robot_ng:
                            fov_result = False

                        fov_data_1.append({
                            "board": board_no_,
                            "part_name": fov_comp.get('designator', ''),
                            "part_code": fov_comp.get('part', ''),
                            "a": fov_comp.get('angle', ''),
                            "x": fov_comp.get('x', ''),
                            "y": fov_comp.get('y', ''),
                            "w": fov_comp.get('width', ''),
                            "h": fov_comp.get('height', ''),
                        })

                    self.log.info(f"fov result: {fov_result}")

                    if (comp_ng_list1 == "仅检测NG" and not fov_result) or (comp_ng_list1 == "全部"):
                        self.log.info(f"正在上传fov图....")

                        aoi_param = {
                            "line": machine_id,
                            "station": station,
                            "name": pcb_entity.project_name,
                            "sn": pcb_sn,
                            "image_info": {
                                "image_step": str(fov_index),
                                "image_type": 1,
                                # "image_body": "",
                            },
                            "part_list": fov_data_1
                        }
                        aoi_url = f"{api_url_ai}/predict_data/"
                        self.log.info(f"请求url：{aoi_url} 请求参数：{json.dumps(aoi_param, indent=4)}")
                        self.log.info(f"图片已转成base64编码附加到[image_info:image_body]参数里，因参数过大，并没有打印！图片地址：{fov_src_img}")

                        aoi_param['image_info']['image_body'] = xutil.ImageUtil.file_to_base64_content(fov_src_img)
                        ret = xrequest.RequestUtil.post_json(aoi_url, aoi_param, log_number=0, timeout=api_timeout_req)
                        if str(ret.get('error_code')) != '0':
                            ret_res = self.x_response("false", f"mes接口异常，上传fov数据失败，error：{ret.get('error_describe')}")

                        count += 1

                    # if os.path.exists(fov_src_img):
                    #     os.remove(fov_src_img)
                    #     self.log.info(f"fov图已删除！")

                self.log.info(f"成功上传fov图数量：{count}")

            else:
                repair_param = {
                    "line": machine_id,
                    "station": station,
                    "name": pcb_entity.project_name,
                    "sn": pcb_sn,
                    "op_result": repair_comp_list_ai
                }

                repair_url = f"{api_url_ai}/op_result/"
                ret = xrequest.RequestUtil.post_json(repair_url, repair_param)
                if str(ret.get('error_code')) != '0':
                    ret_res = self.x_response("false", f"mes接口异常，上传复判数据失败，error：{ret.get('error_describe')}")

        else:
            self.log.info(f"无需上传数据到AI服务器！")

        return ret_res

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_mes_check = other_vo.get_value_by_cons_key("api_url_mes_check")
        terminal_id = other_vo.get_value_by_cons_key("terminal_id")
        user_id = other_vo.get_value_by_cons_key("user_id")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        ret_res = self.x_response()

        for sn in other_vo.list_sn():
            check_param = {
                "Terminal_id": terminal_id,
                "UserId": user_id,
                "MachineId": machine_id,
                "PanelSn": sn
            }
            ret = xrequest.RequestUtil.post_json(api_url_mes_check, check_param)
            msg = ret.get("msg", "")

            if not msg:
                msg = ret.get('Msg', '')

            if "ok" not in msg.lower():
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{msg}")

        return ret_res
