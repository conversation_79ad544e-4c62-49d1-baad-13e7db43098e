# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : draw_image.py
# Time       ：2025/3/10 下午3:10
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import sys

from PyQt5.QtCore import QRect
from PyQt5.QtGui import QPixmap, QPainter, QPen, QColor, QImage
from PyQt5.QtWidgets import QA<PERSON>lication, QWidget, QLabel, QVBoxLayout


def draw_rectangle_on_image(image_path, rect):
    """
    Loads an image, draws a rectangle on it, and returns the modified QPixmap.

    Args:
        image_path (str): Path to the image file.
        rect (QRect): The rectangle to draw.

    Returns:
        QPixmap: The modified QPixmap with the rectangle drawn on it, or None if the image loading fails.
    """
    pixmap = QPixmap(image_path)
    if pixmap.isNull():
        print(f"Error: Could not load image from {image_path}")
        return None

    q_image = QImage(image_path)
    print(q_image)

    painter = QPainter(pixmap)
    pen = QPen()
    pen.setColor(QColor(255, 0, 0))  # Red
    pen.setWidth(3)
    painter.setPen(pen)
    painter.drawRect(rect)
    painter.end()  # Important:  Call end() to release the painter

    return pixmap


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # Replace with the actual path to your image
    # <---- IMPORTANT:  Change this to a valid image path
    image_path = "/home/<USER>/Downloads/ng/T_20230914104225486_1_NG/thumbnail/0/thumb.jpg"
    rectangle = QRect(932.965, 336.145, 21.4939, 10.9894)

    modified_pixmap = draw_rectangle_on_image(image_path, rectangle)

    if modified_pixmap:
        window = QWidget()
        layout = QVBoxLayout()
        image_label = QLabel()
        image_label.setPixmap(modified_pixmap)
        layout.addWidget(image_label)
        window.setLayout(layout)
        window.setWindowTitle("Image with Rectangle")
        window.show()
        sys.exit(app.exec_())
    else:
        print("Exiting due to image loading error.")
        sys.exit(1)  # Exit with an error code
