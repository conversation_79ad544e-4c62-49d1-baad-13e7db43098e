# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/20 上午9:31
# Author     ：sch
# version    ：python 3.8
# Description：方太
"""

from typing import Any

from common import xutil, xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "fangtai release v1.0.0.1",
        "device": "AIS203，AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-20 14:37  上传数据到MES
""", }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "",
        },
    }

    form = {
        "workstation_code": {
            "ui_name": "工作中心编码",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        workstation_code = data_vo.get_value_by_cons_key("workstation_code")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        ret_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_tag_list = []
            comp_ng_code_list = []
            comp_ng_str_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_tag_list.append(comp_entity.designator)
                    comp_ng_code_list.append(comp_entity.repair_ng_code)
                    comp_ng_str_list.append(comp_entity.repair_ng_str)

            data_param = {
                "ID": xutil.OtherUtil.get_uuid4_str(),
                "SN_NO": barcode,
                "WORK_CENTER_NO": workstation_code,
                "TEST_RESULTS": board_entity.get_repair_result("OK", "NG"),
                "COMPONENT_NAME": ",".join(comp_tag_list),
                "BAD_CODING": ",".join(comp_ng_code_list),
                "BAD_NAME": ",".join(comp_ng_str_list),
                "INSPECTION_TIME": test_time,
            }

            ret = xrequest.RequestUtil.post_json(api_url, data_param)
            if ret.get('success') not in ["true", True, "OK", "ok"]:
                ret_msg_list.append(f"No:{board_no} SN:{barcode} Err:{ret.get('error_msg')}")

        if ret_msg_list:
            msg_str = "\n".join(ret_msg_list)
            return self.x_response('false', f"mes接口异常，上传数据失败，{msg_str}")

        return self.x_response()
