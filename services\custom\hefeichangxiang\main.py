# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/4 上午9:39
# Author     ：sch
# version    ：python 3.8
# Description： 合肥畅想
"""
import os
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import x_response
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

global_data = {}


def x_refresh_token(api_url_login, user_id, password, dbaias):
    """
    刷新token
    :param api_url_login:
    :param user_id:
    :param password:
    :param dbaias:
    :return:
    """
    ret = xrequest.RequestUtil.post_json(api_url_login, {
        "userid": user_id,
        "password": password,
        "dbaias": dbaias,
        "errtype": "",
    })

    if str(ret.get("ErrCode")) != "0":
        return x_response("false", f"mes接口异常，获取Token失败，error：{ret.get('ErrMsg')}")

    token = ret.get('Data', {}).get('token')
    if not token:
        return x_response("false", f"mes接口异常，未获取到token！")

    global_data["token"] = token

    return None


class Engine(ErrorMapEngine):
    version = {
        "title": "hefeichangxiang release v1.0.0.15",
        "device": "401,430,630",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-04 09:40  init
date: 2024-06-07 15:04  条码校验前先获取Token
date: 2024-06-17 09:36  自动获取token
date: 2024-07-05 11:23  需求变更：增加上传数据到MES，以及上传NG器件图到ftp服务器
date: 2024-07-09 16:21  Result改成传1/0
date: 2024-07-10 14:47  InfoDetail ""改成[]
date: 2024-07-11 11:26  未扫到条码，停机报警
date: 2024-07-11 18:21  optimized code
date: 2024-08-23 15:54  修改上传逻辑
date: 2024-09-05 17:01  未识别到条码，所有拼板都不过站
date: 2025-01-02 11:39  增加BadPoint不良位号字段
date: 2025-06-17 15:53  ATAOI_2019-30472: 1. 改为上传检测NG的器件  2. 增加整板图上传
date: 2025-06-20 15:29  ATAOI_2019-30472: PASS的不发送整板图到ftp
""", }

    # other_form = {
    #     "ftp_host": {
    #         "ui_name": "FTP Host",
    #         "value": "127.0.0.1"
    #     },
    #     "ftp_port": {
    #         "ui_name": "FTP Port",
    #         "value": "21"
    #     },
    #     "ftp_user": {
    #         "ui_name": "FTP 账号",
    #         "ui_name_en": "FTP User",
    #         "value": "sch"
    #     },
    #     "ftp_password": {
    #         "ui_name": "FTP 密码",
    #         "ui_name_en": "FTP Password",
    #         "value": "123456"
    #     },
    #     "ftp_path": {
    #         "ui_name": "FTP 路径",
    #         "ui_name_en": "FTP Path",
    #         "value": "/AOI"
    #     },
    # }

    other_form = {}

    form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_login": {
            "ui_name": "接口URL(获取token)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:5000/smtcore/api/WPReport/ReportAPI",
        },
        "order_id": {
            "ui_name": "工单",
            "value": "",
        },
        "process_id": {
            "ui_name": "工序",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备代码",
            "value": "",
        },
        "user_id": {
            "ui_name": "用户名",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "dbaias": {
            "ui_name": "dbaias",
            "value": "Trans",
        },
        "ftp_host1": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port1": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user1": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password1": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path1": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    path = {
        "ng_img_path": {
            "ui_name": "NG图保存路径",
            "value": "",
        },
        "json_file_path": {
            "ui_name": "json文档保存路径",
            "value": "",
        },
    }

    password_style = [
        "ftp_password1"
    ]

    # button = {
    #     "get_token": {
    #         "ui_name": "获取Token"
    #     }
    # }

    def __init__(self):
        self.app_setting["use_password"] = True

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 6)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        order_id = other_vo.get_value_by_cons_key("order_id")
        process_id = other_vo.get_value_by_cons_key("process_id")
        device_code = other_vo.get_value_by_cons_key("device_code")

        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        dbaias = other_vo.get_value_by_cons_key("dbaias")

        token = global_data.get("token")
        if not token:
            x_res = x_refresh_token(api_url_login, user_id, password, dbaias)
            if x_res:
                return x_res

            token = global_data.get("token")
            if not token:
                return self.x_response("false", f"请先获取Token！")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", f"条码校验失败，未扫到条码！")

        err_msg = []
        for sn in sn_list:
            check_param = {
                "PlanNumber": order_id,
                "LabelCode": sn,
                "CheckOnly": "1",
                "WpNumber": process_id,
                "DevNumber": device_code,
                "programVer": other_vo.get_project_name(),
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param, headers=headers)
            if str(ret.get("ErrCode")) != "0":
                err_msg.append(ret.get('ErrMsg'))

        if err_msg:
            e1 = '\n'.join(err_msg)
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{e1}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        order_id = data_vo.get_value_by_cons_key("order_id")
        process_id = data_vo.get_value_by_cons_key("process_id")
        device_code = data_vo.get_value_by_cons_key("device_code")
        ng_img_path = data_vo.get_value_by_cons_key("ng_img_path")
        json_file_path = data_vo.get_value_by_cons_key("json_file_path")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        api_url_login = data_vo.get_value_by_cons_key("api_url_login")
        user_id = data_vo.get_value_by_cons_key("user_id")
        password = data_vo.get_value_by_cons_key("password")
        dbaias = data_vo.get_value_by_cons_key("dbaias")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host1")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port1", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user1")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password1")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path1")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # standard_json = {
        #     "device_name": device_code
        # }

        token = global_data.get("token")
        if not token:
            x_res = x_refresh_token(api_url_login, user_id, password, dbaias)
            if x_res:
                return x_res

            token = global_data.get("token")
            if not token:
                return self.x_response("false", f"请先获取Token！")

        headers = {
            "Authorization": f"Bearer {token}"
        }
        ret_res = self.x_response()

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        board_data = []
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0

        board_user_ng_count = 0
        board_robot_ng_count = 0

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        pcb_result = pcb_entity.get_repair_result()

        if pcb_result == "OK":
            self.log.info(f"整板结果PASS/REPASS，所有拼板都要发送mes")

        not_check_out = False  # 不过站
        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode

            if not barcode:
                not_check_out = True
                self.log.info(f"No：{board_entity.board_no}拼板未识别到条码！")

            if not pcb_sn and barcode:
                pcb_sn = barcode

        ftp_client.cd_or_mkdir(f"{ftp_path}/{time_file}_{pcb_sn}")

        # 上传整板图
        pcb_t_src_img = pcb_entity.get_pcb_t_image()

        pcb_result = pcb_entity.get_final_result()

        if pcb_result != "PASS":
            if os.path.exists(pcb_t_src_img):
                ftp_client.upload_file(pcb_t_src_img, f"{time_file}_{pcb_sn}.jpg")
                self.log.info(f"整板图上传成功！")
            else:
                self.log.warning(f"找不到需要上传的整板图！{pcb_t_src_img}")
        else:
            self.log.info(f"整板结果PASS，不上传整板图！")

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_list = []

            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number

            if board_entity.is_robot_ng():
                board_robot_ng_count += 1

            if board_entity.is_repair_ng():
                board_user_ng_count += 1

            comp_data = []

            comp_ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_src_img = comp_entity.image_path
                    comp_designator = comp_entity.designator
                    r1 = comp_entity.robot_ng_str
                    r2 = comp_entity.repair_ng_str

                    if comp_src_img:
                        img_full_path = f"{ng_img_path}/{time_file}_{pcb_sn}"
                        xutil.FileUtil.ensure_dir_exist(img_full_path)

                        comp_dst_img = f"{img_full_path}/{barcode}_{comp_designator}_{r1}_{r2}.png"
                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

                        ftp_client.upload_file(comp_src_img, f"{barcode}_{comp_designator}_{r1}_{r2}.png")

                    else:
                        comp_dst_img = ""

                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_dst_img,
                    })

                if comp_entity.is_robot_ng():
                    comp_ix += 1
                    comp_ng_list.append({
                        "SEQ": str(comp_ix),
                        "BadNumber": comp_entity.robot_ng_code,
                        "BadPoint": comp_entity.designator,
                        "BadName": comp_entity.robot_ng_str,
                        "PSBadNumber": comp_entity.repair_ng_code,
                        "BadQty": "1",
                        "Remark": comp_entity.repair_ng_str,
                    })

            data_param = {
                "PlanNumber": order_id,
                "LabelCode": barcode,
                "CheckOnly": "0",
                "WpNumber": process_id,
                "DevNumber": device_code,
                "programVer": pcb_entity.project_name,
                "Result": board_entity.get_repair_result("1", "0"),
                "BadDetail": comp_ng_list,
                "InfoDetail": [],
            }

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

            # if not barcode:
            #     self.log.warning(f"未识别到条码，该拼板数据不上传到MES！")
            #     continue
            if not_check_out:
                self.log.warning(f"未识别到条码，所有拼板都不过站！")
                break

            if pcb_result == "OK":
                # PASS/REPASS
                ret = xrequest.RequestUtil.post_json(api_url_data, data_param, headers=headers)
                if str(ret.get("ErrCode")) != "0":
                    ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('ErrMsg')}")
            else:
                # NG
                # self.log.warning(f"拼板部分复判NG，部分复判OK的，只发送复判NG的板的数据，OK的板不发!")
                if board_entity.is_repair_ng():
                    ret = xrequest.RequestUtil.post_json(api_url_data, data_param, headers=headers)
                    if str(ret.get("ErrCode")) != "0":
                        ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('ErrMsg')}")
                else:
                    self.log.warning(f"拼板部分复判NG，OK的拼板不发送MES！")

        standard_json = {
            "device_name": device_code,
            "pcb_sn": pcb_entity.pcb_barcode,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_user_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,
            "board_data": board_data
        }

        ftp_client.close()

        pcb_result = pcb_entity.get_repair_result("pass", "fail")
        json_path = f"{json_file_path}/{pcb_result}"
        xutil.FileUtil.ensure_dir_exist(json_path)

        xutil.FileUtil.dump_json_to_file(f"{json_path}/{pcb_sn}_{time_file}.json", standard_json)

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "get_token":
            api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
            user_id = btn_vo.get_value_by_cons_key("user_id")
            password = btn_vo.get_value_by_cons_key("password")
            dbaias = btn_vo.get_value_by_cons_key("dbaias")

            x_res = x_refresh_token(api_url_login, user_id, password, dbaias)
            if x_res:
                return x_res

            # ret = xrequest.RequestUtil.post_json(api_url_login, {
            #     "userid": user_id,
            #     "password": password,
            #     "dbaias": dbaias,
            #     "errtype": "",
            # })
            #
            # if str(ret.get("ErrCode")) != "0":
            #     return self.x_response("false", f"mes接口异常，获取Token失败，error：{ret.get('ErrMsg')}")
            #
            # token = ret.get('Data', {}).get('token')
            # if not token:
            #     return self.x_response("false", f"mes接口异常，未获取到token！")
            #
            # global_data["token"] = token

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        dbaias = other_vo.get_value_by_cons_key("dbaias")

        x_refresh_token(api_url_login, user_id, password, dbaias)
