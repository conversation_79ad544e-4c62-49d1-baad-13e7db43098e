# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/01/20 下午14:28
# Author     ：gyr
# version    ：python 3.8
# Description：深圳核达中远通
"""

from datetime import datetime
import os
from typing import Any
from engine.FtpEngine import FTPClient

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

txt_template = """{program_name}
{pcb_sn}
{smt_line}
{board_no}
{user_name}
{work_order}
{test_time}
{repair_time}
{final_result}
{board_side}
{total_comp}
{ng_comp}
{ng_details}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenhedazhongyuantong release v1.0.0.3",
        "device": "AIS203",
        "feature": ["上传数据", "条码校验"],
        "author": "gaoyurui",
        "release": """
date: 2025-02-05 17:26 jira:36581,按照标准版生成csv文件，条码校验，上传数据
date: 2025-02-12 10:50 jira:36581,修改上传格式,修复原有bug
date: 2025-02-20 14:02 jira:36581,生成csv文件改为txt文件,修改上传文件的参数
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "user_name": {
            "ui_name": "员工号",
            "value": ""
        },
        "station": {
            "ui_name": "工序名称",
            "value": ""
        },
        "res": {
            "ui_name": "资源名称",
            "value": ""
        },
        "machine": {
            "ui_name": "设备编号",
            "value": ""
        },
        "fix_ture": {
            "ui_name": "工治具编码",
            "value": ""
        },
        # "device_name": {
        #     "ui_name": "设备名称",
        #     "value": ""
        # },
        "mo_name": {
            "ui_name": "工单号",
            "value": ""
        },
        "smt_line": {
            "ui_name": "SMT线别名称",
            "value": ""
        },
        "project_name": {
            "ui_name": "机种名称",
            "value": ""
        }
    }

    other_form = {
        "ftp_host": {
            "ui_name": "FTP服务器地址",
            "value": "127.0.0.1",
        },
        "ftp_port": {
            "ui_name": "FTP端口",
            "value": "21",
        },
        "ftp_user": {
            "ui_name": "FTP用户名",
            "value": "admin",
        },
        "ftp_password": {
            "ui_name": "FTP密码",
            "value": "123456",
        }
    }

    path = {
        "save_txt": {
            "ui_name": "TXT保存路径",
            "value": ""
        }
    }


    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        self.log.info(f"----------------条码校验----------------")

        api_url = other_vo.get_value_by_cons_key("api_url", not_null=True)
        user_name = other_vo.get_value_by_cons_key("user_name", not_null=True)
        station = other_vo.get_value_by_cons_key("station", not_null=True)
        res = other_vo.get_value_by_cons_key("res", not_null=True)
        machine = other_vo.get_value_by_cons_key("machine", not_null=True)
        fix_ture = other_vo.get_value_by_cons_key("fix_ture")

        # 获取所有拼板条码
        sn_list = other_vo.list_sn()

        # ret_res = self.x_response()
        for sn in sn_list:
            if not sn:
                continue
            check_param = f"01;{user_name};{sn};{station};{res};{machine};{fix_ture};"
            ret = xrequest.RequestUtil.post_form(api_url, {'commandString': check_param}, to_json=False)
            result = xutil.XmlUtil.get_xml_root_by_str(ret).text
            res_data = result.split(";")
            if res_data[0] != "OK":
                # return self.x_response("false", f"mes接口返回错误: {ret.get('返回值')}")
                return self.x_response("false", f"条码校验失败: {res_data[1]}")

        return self.x_response()

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:

        api_url = data_dao.get_value_by_cons_key("api_url")
        ftp_host = data_dao.get_value_by_cons_key("ftp_host")
        ftp_port = data_dao.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_dao.get_value_by_cons_key("ftp_user")
        ftp_password = data_dao.get_value_by_cons_key("ftp_password")
        user_name = data_dao.get_value_by_cons_key("user_name")
        station = data_dao.get_value_by_cons_key("station")
        res = data_dao.get_value_by_cons_key("res")
        machine = data_dao.get_value_by_cons_key("machine")
        fix_ture = data_dao.get_value_by_cons_key("fix_ture")
        # save_csv = data_dao.get_value_by_cons_key("save_csv")
        # device_name = data_dao.get_value_by_cons_key("device_name")
        mo_name = data_dao.get_value_by_cons_key("mo_name")
        smt_line = data_dao.get_value_by_cons_key("smt_line")
        save_txt = data_dao.get_value_by_cons_key("save_txt")
        project_name = data_dao.get_value_by_cons_key("project_name")

        pcb_entity = data_dao.pcb_entity
        # program_name = pcb_entity.project_name
        pcb_sn = pcb_entity.pcb_barcode
        self.log.info(pcb_entity)
        current_date = datetime.now()
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        date_folder = current_date.strftime("%Y%m%d")
        pcb_board_robot_ng_number = 0
        pcb_board_user_ng_number = 0
        pcb_comp_robot_ng_number = 0
        pcb_comp_user_ng_number = 0

        # 4. 建立FTP连接
        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()
        ftp_path = f"/AOI/{date_folder}"

        # 5. 上传整版图片
        pcb_images = pcb_entity.list_all_pcb_image_v2()
        image_list = pcb_images if isinstance(pcb_images, list) else [pcb_images]

        ftp_client.cd_or_mkdir(ftp_path)
        pcb_image_path = ""

        for pcb_image in image_list:
            if pcb_image and os.path.exists(pcb_image):
                time_str = pcb_entity.get_start_time().strftime("%Y%m%d%H%M%S")
                board_image_name = f"{pcb_sn}_{time_str}_pcb.jpg"

                self.log.info(f"准备上传文件: {pcb_image}")
                self.log.info(f"目标FTP路径: {ftp_path}/{board_image_name}")

                pcb_image_path = f"{ftp_path}/{board_image_name}"
                ftp_client.upload_file(pcb_image, pcb_image_path)
                self.log.info(f"文件上传成功: {board_image_name}")

        if not pcb_image_path:
            pcb_image_path = ""
            self.log.info(f"没有获取到PCB整版图")


        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode or pcb_entity.get_start_time().strftime("%Y%m%d%H%M%S")
            board_no = board_entity.board_no

            board_final_result = board_entity.get_final_result("Pass", "Repass", "Fail", "Skip")
            self.log.info(board_entity)

            # 收集不良器件信息
            ng_details_list = []
            comp_data5 = []
            board_result = board_entity.get_repair_result()

            # 更新计数器
            if not board_entity.robot_result:
                pcb_board_robot_ng_number += 1
            if not board_entity.repair_result:
                pcb_board_user_ng_number += 1

            # 处理csv数据
            for comp_entity in board_entity.yield_comp_entity():
                # 更新计数器
                if not comp_entity.robot_result:
                    pcb_comp_robot_ng_number += 1
                    ng_code = "OK" if comp_entity.repair_result else comp_entity.repair_ng_code
                    ng_count = "0" if comp_entity.repair_result else "1"
                    ng_details_list.append(f"{comp_entity.designator};{comp_entity.part};{ng_code};{ng_count}")
                if not comp_entity.repair_result:
                    pcb_comp_user_ng_number += 1
                    # 上传不良元件图片
                    comp_image = comp_entity.image_path
                    comp_image_name = f"{comp_entity.designator}_{timestamp}_comp.png"
                    try:
                        ftp_client.upload_file(comp_image, comp_image_name)
                        comp_path = f"{ftp_path}/{comp_image_name}"

                        comp_data5.append(
                            f"{comp_entity.designator}:{comp_entity.robot_ng_code}:"f"{comp_entity.part}:{comp_path}")
                        self.log.info(f"不良元器件图片上传成功: {comp_image_name}")
                    except Exception as e:
                        self.log.error(f"不良元器件图片上传失败: {str(e)}")


            save_param5 = f"05;{user_name};{barcode};{station};{res};{machine};{fix_ture};{board_result};{pcb_image_path};" \
                        f"{','.join(comp_data5)};"

            ret = xrequest.RequestUtil.post_form(api_url, {'commandString': save_param5}, to_json=False)
            result = xutil.XmlUtil.get_xml_root_by_str(ret).text
            res_data = result.split(";")
            if res_data[0] != "OK":
                return self.x_response("false", f"接口异常，上传测试数据失败，error：{res_data[1]}")


            txt_data = {
                "program_name": project_name,
                "pcb_sn": barcode,
                "smt_line": smt_line,
                "board_no": board_no,
                "user_name": user_name,
                "work_order": mo_name if mo_name else "0",
                "test_time": pcb_entity.get_start_time().strftime("%Y/%m/%d %H:%M:%S"),
                "repair_time": pcb_entity.get_end_time().strftime("%Y/%m/%d %H:%M:%S"),
                "final_result": board_final_result,
                "board_side": "T" if pcb_entity.board_side.upper() == "TOP" else "B",
                "total_comp": str(sum(1 for _ in board_entity.yield_comp_entity())),
                "ng_comp": str(sum(1 for comp in board_entity.yield_comp_entity() if not comp.repair_result)),
                "ng_details": "\n".join(ng_details_list) if ng_details_list else " "
            }

            timestamp = pcb_entity.get_start_time().strftime("%Y%m%d%H%M%S")
            txt_filename = f"{smt_line}_{timestamp}_{board_no}.txt"
            txt_filepath = f"{save_txt}/{txt_filename}"

            try:
                xutil.FileUtil.write_content_to_file(
                    file_path=txt_filepath,
                    content=txt_template.format(**txt_data),
                    window_line=True
                )
                self.log.info(f"已生成TXT文件: {txt_filepath}")
            except Exception as e:
                self.log.error(f"TXT文件写入失败: {str(e)}")
                return self.x_response("false", f"TXT文件写入失败: {str(e)}")

        # 关闭FTP连接
        ftp_client.close()

        return self.x_response()
