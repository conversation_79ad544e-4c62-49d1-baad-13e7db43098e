# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/23 上午10:14
# Author     ：sch
# version    ：python 3.8
# Description：郑州华思光电
"""

from typing import Any

from common import xcons
from vo.mes_vo import DataVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
测试时间,{pcb_test_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "huasiguangdian release v1.0.0.1",
        "device": "630B、401B",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-23 10:35  上传整板csv格式到ftp服务器
""", }

    combo = {
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "仅复判NG"
        },
        "newline_type": {
            "ui_name": "换行符格式",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        },
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        device_name = data_vo.get_value_by_cons_key("device_name")
        newline_type = data_vo.get_value_by_cons_key("newline_type")
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP Port必须为数字，error：{err}")

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_data_output == "全部" or \
                        (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                        (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })
        pcb_data = {
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        comp_data_str = ""
        for _board_data in pcb_data.get("board_data", []):
            for _comp_data in _board_data.get("comp_data", {}):
                _comp_data.update(_board_data)
                comp_data_str += csv_comp_panel_template.format(**_comp_data)

        pcb_data["CompData"] = comp_data_str
        pcb_content = csv_pcb_panel_template.format(**pcb_data)

        if newline_type == 'window':
            pcb_content = pcb_content.replace('\n', '\r\n')

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        ftp_full_path = f"{ftp_path}/{time_str[:8]}"
        ftp_client.cd_or_mkdir(ftp_full_path)

        dst_filename = f"{device_name}_{pcb_sn}_{time_str}.csv"
        ftp_client.upload_content(dst_filename, pcb_content)

        return self.x_response()
