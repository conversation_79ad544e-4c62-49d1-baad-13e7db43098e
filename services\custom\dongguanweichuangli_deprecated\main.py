# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/20 上午10:14
# Author     ：sch
# version    ：python 3.8
# Description：东莞伟创力 Deprecated
"""
from typing import Any

from common import xrequest, xutil, xcons, xenum
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

# check_sn_template = """<soapenv:Envelope
#         xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
#         xmlns:tem="http://tempuri.org/">
#     <soapenv:Header/>
#     <soapenv:Body>
#         <tem:GetUnitInfo>
#             <!--Optional:-->
#             <tem:serialNumber>{serial}</tem:serialNumber>
#             <!--Optional:-->
#             <tem:stationName>{station}</tem:stationName>
#             <!--Optional:-->
#             <tem:userId>{user_id}</tem:userId>
#         </tem:GetUnitInfo>
#     </soapenv:Body>
# </soapenv:Envelope>"""

# pcb_template1 = """<?xml version="1.0" ?>
# <BATCH TIMESTAMP="{test_time}" SYNTAX_REV=""
#        COMPATIBLE_REV="">
#     <FACTORY NAME="{factory}" LINE="{line}" TESTER="{station}" FIXTURE="" SHIFT="" USER="{operator}"/>
#     <PRODUCT NAME="{project_name}" REVISION="{bom}" FAMILY="" CUSTOMER=""/>
#     <REFS SEQ_REF="" FTS_REF="" LIM_REF="" CFG_REF="" CAL_REF="" INSTR_REF=""/>
#     <PANEL ID="{pcb_sn}" COMMENT="" RUNMODE="Debug" TIMESTAMP="{test_time}" TESTTIME="{cycle_time}" WAITTIME="0" STATUS="{pcb_result}">{board_data_str}
#     </PANEL>
# </BATCH>"""

# pcb_template = """<soapenv:Envelope
#         xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
#         xmlns:tem="http://tempuri.org/">
#     <soapenv:Header/>
#     <soapenv:Body>
#         <tem:SaveResult>
#             <!--Optional:-->
#             <tem:ffTesterXmlRequest></tem:ffTesterXmlRequest>
#             <!--Optional:-->
#             <tem:stationName>{station}</tem:stationName>
#             <!--Optional:-->
#             <tem:userId>{user_id}</tem:userId>
#             <!--Optional:-->
#             <tem:extraData></tem:extraData>
#         </tem:SaveResult>
#     </soapenv:Body>
# </soapenv:Envelope>
# """

# get_sn_template = """<?xml version="1.0" encoding="utf-8"?>
# <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
#   <soap:Body>
#     <GetUnitsByPanel xmlns="http://tempuri.org/">
#       <FFID>{ffid}</FFID>
#       <PanelSN>{panel_sn}</PanelSN>
#       <SerialnumberType>0</SerialnumberType>
#     </GetUnitsByPanel>
#   </soap:Body>
# </soap:Envelope>"""

check_sn_template_v2 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <soap:Body>
    <GetUnitInfo xmlns="http://tempuri.org/">
      <serialNumber>{serial_number}</serialNumber>
      <stationName>{station_name}</stationName>
      <userId>{user_id}</userId>
      <extraData />
    </GetUnitInfo>
  </soap:Body>
</soap:Envelope>"""


pcb_template_v3 = """<?xml version="1.0" ?>
<BATCH TIMESTAMP="{test_time}" SYNTAX_REV="1.1"
       COMPATIBLE_REV="1.1">
    <FACTORY NAME="{factory}" LINE="{line}" TESTER="{tester}" FIXTURE="" SHIFT="" USER="{operator}"/>
    <PRODUCT NAME="{project_name}" REVISION="{bom}" FAMILY="" CUSTOMER=""/>
    <REFS SEQ_REF="" FTS_REF="" LIM_REF="" CFG_REF="" CAL_REF="" INSTR_REF=""/>
    <PANEL ID="{pcb_sn}" COMMENT="" RUNMODE="Debug" TIMESTAMP="{test_time}" TESTTIME="{cycle_time}" WAITTIME="0" STATUS="{pcb_result}">{board_data_str}
    </PANEL>
</BATCH>"""

pcb_template_v2 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <soap:Body>
    <SaveResult xmlns="http://tempuri.org/">
      <ffTesterXmlRequest>{test_xml}</ffTesterXmlRequest>
      <stationName>{station}</stationName>
      <userId>{user_id}</userId>
      <extraData />
    </SaveResult>
  </soap:Body>
</soap:Envelope>"""

board_template = """
        <DUT ID="{barcode}" COMMENT="" PANEL="{board_no}" SOCKET="{comp_number}" TIMESTAMP="{test_time}" TESTTIME="{cycle_time}" STATUS="{board_result}">{comp_data_str}
        </DUT>"""

comp_template = """
            <GROUP NAME="{comp_tag}" STEPGROUP="" GROUPINDEX="{comp_ix}" LOOPINDEX="{comp_ix}" TYPE="" RESOURCE="" MODULETIME="0" TOTALTIME="0" TIMESTAMP="{test_time}" STATUS="{comp_result}">{alg_data_str}
            </GROUP>"""

alg_template = """
                <TEST NAME="{alg_name}" DESCRIPTION="{comp_ng_str}" UNIT="" VALUE="{test_value}" HILIM="{max_val}" LOLIM="{min_val}" STATUS="{alg_result}" RULE="" TARGET="" DATATYPE="Number"/>"""


get_sn_template_v3 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <soap:Body>
    <ExecuteGenericFunction xmlns="http://tempuri.org/">
      <functionName>GetUnitsByPanel</functionName>
      <spInputParameter>&lt;flx&gt;&lt;SN&gt;{pcb_sn}&lt;/SN&gt;&lt;/flx&gt;</spInputParameter>
      <stationName>{station_name}</stationName>
      <userId>user_id</userId>
      <output />
      <errorDescription />
    </ExecuteGenericFunction>
  </soap:Body>
</soap:Envelope>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanweichuangli deprecated v1.0.0.16",
        "device": "430",
        "feature": ["获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-20 16:33  从mes获取条码，条码校验，上传数据
date: 2023-12-28 14:29  修改成window中转方案
date: 2024-01-02 14:03  修改上传数据方式
date: 2024-01-02 14:03  已废弃，已改使用Python脚本方式实现
""", }

    form = {
        "window_ip": {
            "ui_name": "window中转IP",
            "value": "",
        },
        "station": {
            "ui_name": "站位名称",
            "value": "",
        },
        "factory": {
            "ui_name": "工厂",
            "value": "",
        },
        "line": {
            "ui_name": "线别",
            "value": "",
        },
        "device_id": {
            "ui_name": "设备ID",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
    }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口地址(获取条码)",
            "value": "",
        },
        "api_url_check": {
            "ui_name": "接口地址(条码校验)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口地址(上传数据)",
            "value": "",
        },
        "content_type": {
            "ui_name": "ContentType",
            "value": "text/xml; charset=utf-8",
        },
    }

    button = {
        "connect_window": {
            "ui_name": "TestConnect"
        }
    }

    def __init__(self):
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        content_type = other_vo.get_value_by_cons_key("content_type")
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        station = other_vo.get_value_by_cons_key("station")
        operator = other_vo.get_value_by_cons_key("operator")

        pcb_sn = other_vo.get_pcb_sn()

        get_sn_content = get_sn_template_v3.format(**{
            "station_name": station,
            "user_id": operator,
            "pcb_sn": pcb_sn,
        })

        self.log.info(f"请求参数--------")
        self.log.info(f"\n{get_sn_content}")
        self.log.info(f"请求参数--------")

        headers = {
            "Content-Type": content_type,
            "Url": "http://donnt118:9900/",
            "SOAPAction": "http://tempuri.org/IFFTesterService/ExecuteGenericFunction"
        }

        packet_data = {
            "type": 18,
            "request_param": {
                "api_url": api_url_get_sn,
                "body_param": get_sn_content,
                "headers": headers,
                "timeout": 5
            }
        }

        get_sn_res = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)
        get_sn_ret = get_sn_res.get('string')

        root = xutil.XmlUtil.get_xml_root_by_str(get_sn_ret)
        result_item = root[0][0]
        output_item = result_item.find('{http://tempuri.org/}output')

        ret_str = output_item.text
        self.log.info(f'返回的条码列表：{ret_str}')

        flx_root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

        ret_sn = []
        for item in flx_root:
            ret_sn.append(item.attrib.get('Value', ''))

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station = other_vo.get_value_by_cons_key("station")
        operator = other_vo.get_value_by_cons_key("operator")
        content_type = other_vo.get_value_by_cons_key("content_type")
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        headers = {
            "Content-Type": content_type,
            "Url": "http://donnt118:9900/",
            "SOAPAction": "http://tempuri.org/IFFTesterService/GetUnitInfo"
        }

        error_msg = ""
        for sn in other_vo.list_sn():
            check_param = check_sn_template_v2.format(**{
                "serial_number": sn,
                "station_name": station,
                "user_id": operator,
            })

            self.log.info(f"请求参数--------")
            self.log.info(f"\n{check_param}")
            self.log.info(f"请求参数--------")

            # check_ret = xrequest.RequestUtil.post_xml(api_url_check, check_param)
            packet_data = {
                "type": 18,
                "request_param": {
                    "api_url": api_url_check,
                    "body_param": check_param,
                    "headers": headers,
                    "timeout": 5
                }
            }
            check_res = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)
            check_ret = check_res.get('string')

            root = xutil.XmlUtil.get_xml_root_by_str(check_ret)
            result_item = root[0][0][0]
            result_id = result_item.find("{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Id").text
            result_value = result_item.find(
                "{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Value").text
            if result_id != "0":
                error_msg = f"mes接口异常，条码校验失败，error：{result_value}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        station = data_vo.get_value_by_cons_key("station")
        factory = data_vo.get_value_by_cons_key("factory")
        device_id = data_vo.get_value_by_cons_key("device_id")
        operator = data_vo.get_value_by_cons_key("operator")
        line = data_vo.get_value_by_cons_key("line")
        content_type = data_vo.get_value_by_cons_key("content_type")
        window_ip = data_vo.get_value_by_cons_key("window_ip")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        headers = {
            "Content-Type": content_type,
            "Url": "http://donnt118:9900/",
            "SOAPAction": "http://tempuri.org/IFFTesterService/SaveResult"
        }

        pcb_sn = pcb_entity.pcb_barcode
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        cycle_time = pcb_entity.get_cycle_time()

        board_data_str = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn:
                pcb_sn = barcode

            comp_data_str = ""
            comp_ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                robot_ng_str = comp_entity.robot_ng_str
                comp_ix += 1

                alg_data_str = ""
                for alg_entity in comp_entity.yield_alg_entity():
                    alg_data_str += alg_template.format(**{
                        "alg_name": alg_entity.test_name,
                        "comp_ng_str": robot_ng_str,
                        "test_value": alg_entity.test_val,
                        "max_val": alg_entity.max_threshold,
                        "min_val": alg_entity.min_threshold,
                        "alg_result": "Passed" if alg_entity.result == "0" else "Failed"
                    })

                comp_data_str += comp_template.format(**{
                    "comp_tag": comp_entity.designator,
                    "comp_ix": comp_ix,
                    "test_time": start_time,
                    "comp_result": comp_entity.get_final_result("Passed", "Passed", "Failed"),
                    "alg_data_str": ""
                })

            board_data_str += board_template.format(**{
                "barcode": barcode,
                "board_no": board_no,
                "comp_number": board_entity.comp_total_number,
                "test_time": start_time,
                "cycle_time": cycle_time,
                "board_result": board_entity.get_repair_result("Passed", "Failed"),
                "comp_data_str": comp_data_str,
            })

        test_xml = pcb_template_v3.format(**{
            "station": station,
            "user_id": operator,
            "test_time": start_time,
            "factory": factory,
            "line": line,
            "tester": device_id,
            "operator": operator,
            "project_name": pcb_entity.pcb,
            "bom": pcb_entity.bom,
            "pcb_sn": pcb_sn,
            "cycle_time": cycle_time,
            "pcb_result": pcb_entity.get_repair_result("Passed", "Failed"),
            "board_data_str": board_data_str
        })

        test_xml = test_xml.replace('<', '&lt;').replace('>', '&gt;')

        pcb_param = pcb_template_v2.format(**{
            "test_xml": test_xml,
            "station": station,
            "user_id": operator,
            "test_time": start_time,
            "factory": factory,
            "line": line,
            "tester": device_id,
            "operator": operator,
            "project_name": pcb_entity.pcb,
            "bom": pcb_entity.bom,
            "pcb_sn": pcb_sn,
            "cycle_time": cycle_time,
            "pcb_result": pcb_entity.get_repair_result("Passed", "Failed"),
            "board_data_str": board_data_str
        })

        self.log.info(f"请求参数--------")
        self.log.info(f"\n{pcb_param}")
        self.log.info(f"请求参数--------")

        packet_data = {
            "type": 18,
            "request_param": {
                "api_url": api_url_data,
                "body_param": pcb_param,
                "headers": headers,
                "timeout": 5
            }
        }
        data_res = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)
        data_ret = data_res.get('string')

        root = xutil.XmlUtil.get_xml_root_by_str(data_ret)
        result_item = root[0][0][0]
        result_id = result_item.find("{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Id").text
        result_value = result_item.find(
            "{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Value").text
        if result_id != "0":
            error_msg = f"mes接口异常，上传数据失败，error：{result_value}"
            return self.x_response("false", error_msg)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        btn_key = btn_vo.get_btn_key()

        if btn_key == 'connect_window':
            ret = xrequest.SocketUtil.check_window_port(window_ip)
            if not ret:
                return self.x_response("false", f"请检查window中转程序是否打开！")
            else:
                return self.x_response("true", f"中转程序连接成功！")

        return self.x_response()
