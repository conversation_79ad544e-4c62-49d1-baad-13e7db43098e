# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : init_env.py
# Time       ：2025/5/6 上午10:05
# Author     ：sch
# version    ：python 3.8
# Description：初始化环境
"""
import os
import shutil

from common import xconfig


def remove_file(filepath: str):
    """
    删除文件
    """
    if not filepath:
        print(f"filepath is None")
        return

    if os.path.exists(filepath):
        os.remove(filepath)
        print(f"文件删除成功：{filepath}")
    else:
        print(f"文件不存在：{filepath}")


if __name__ == '__main__':
    if os.path.exists(xconfig.log_dir):
        shutil.rmtree(xconfig.log_dir)
        print(f"目录删除成功：{xconfig.log_dir}")

    remove_file(xconfig.mesconfig_file)
    remove_file(xconfig.mes_cache_data)
    remove_file(xconfig.mes_limit_cache_data)
    remove_file(xconfig.mes_error_code_map)
