# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/01/08 上午10:05
# Author     ：gyr
# version    ：python 3.8
# Description：天津森普捷
"""
import os
from typing import Any

from common import xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

txt_template = """MainSN:{pcb_sn}
PanelSN:{panel_sn}
PanelID:{board_no}
ModelName:{program_name}
Side:{board_side}
MachineName:{equipment_name}
CustomerName:{customer_name}
Operator:{operator_name}
Programer:{engineer_name}
InspectionDate:{inspection_date}
BeginTime:{start_time}
EndTime:{end_time}
CycleTimeSec:{cycle_time}
InspectionBatch:{batch}
ReportResult:{inspect_result}
ConfirmedResult:{repair_result}
TotalComponent:{total_component}
ReportFailComponent:{report_fail_component}
ConfirmedFailComponent:{confirmed_fail_component}
{empty_line_20}
{empty_line_21}
{empty_line_22}
{empty_line_23}
{empty_line_24}
{empty_line_25}
{empty_line_26}
{empty_line_27}
{empty_line_28}
{empty_line_29}
{empty_line_30}
{empty_line_31}
ComponentName,MainSN,PanelSN,PanelID,LibraryModel,PN,MaterialCode,Package,Angle,ReportResult,ReportResultCode,ConfirmedResult,ConfirmedResultCode
{component_data}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "tianjinsenpujie release v1.0.0.2",
        "device": "AIS401-D,AIS430-D",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-01-08 jira:36619,生成本地txt文件
date: 2025-01-24 jira:36619,生成文件按照轨道区分
""", }

    path = {
        "save_path": {
            "ui_name": "数据保存路径(1轨)",
            "value": ""
        },
        "save_path2": {
            "ui_name": "数据保存路径(2轨)",
            "value": ""
        }
    }

    form = {
        "equipment_name": {
            "ui_name": "设备名称",
            "value": ""
        },
        "customer_name": {
            "ui_name": "客户名称",
            "value": ""
        },
        "operator_name": {
            "ui_name": "操作员名称",
            "value": ""
        },
        "engineer_name": {
            "ui_name": "工程师名称",
            "value": ""
        },
        "batch": {
            "ui_name": "批次",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        equipment_name = data_dao.get_value_by_cons_key("equipment_name")
        operator_name = data_dao.get_value_by_cons_key("operator_name")
        customer_name = data_dao.get_value_by_cons_key("customer_name")
        engineer_name = data_dao.get_value_by_cons_key("engineer_name")
        batch = data_dao.get_value_by_cons_key("batch")
        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.get_track_index()

        if track_index == 1:
            save_path = data_dao.get_value_by_cons_key("save_path", not_null=True)
        else:
            save_path = data_dao.get_value_by_cons_key("save_path2", not_null=True)

        pcb_sn = pcb_entity.pcb_barcode

        start_time = pcb_entity.get_start_time()
        time_str = start_time.strftime("%Y%m%d%H%M%S")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            total_component = board_entity.comp_total_number
            report_fail_component = board_entity.comp_robot_ng_number
            confirmed_fail_component = board_entity.comp_repair_ng_number

            component_data = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    component_data.append(
                        f"{comp_entity.designator},"  # 不良位号
                        f"{pcb_sn},"  # 整版条码
                        f"{barcode},"  # 拼版条码
                        f"{board_no},"  # 拼版序号
                        f"0,"  # 库
                        f"0,"  # PN
                        f"{comp_entity.part},"  # 物料编码
                        f"0,"  # 角度
                        f"{comp_entity.robot_ng_str},"  # 不良类型
                        f"{comp_entity.robot_ng_code or '0'},"  # 不良类型代码
                        f"{comp_entity.repair_result},"  # 复判结果
                        f"{comp_entity.repair_ng_code or '0'}"  # 复判结果代码
                    )

            content = txt_template.format(**{
                "pcb_sn": pcb_sn,
                "panel_sn": barcode,
                "board_no": board_no,
                "program_name": pcb_entity.project_name,
                "board_side": pcb_entity.board_side,
                "equipment_name": equipment_name,
                "customer_name": customer_name,
                "operator_name": operator_name,
                "engineer_name": engineer_name,
                "inspection_date": start_time.strftime("%Y/%m/%d"),
                "start_time": start_time.strftime("%H:%M:%S"),
                "end_time": pcb_entity.get_end_time().strftime("%H:%M:%S"),
                "cycle_time": pcb_entity.get_cycle_time(),
                "batch": batch,
                "inspect_result": board_entity.get_robot_result("P", "F"),
                "repair_result": board_entity.get_repair_result("P", "F"),
                "total_component": total_component,
                "report_fail_component": report_fail_component,
                "confirmed_fail_component": confirmed_fail_component,
                "empty_line_20": "",
                "empty_line_21": "",
                "empty_line_22": "",
                "empty_line_23": "",
                "empty_line_24": "",
                "empty_line_25": "",
                "empty_line_26": "",
                "empty_line_27": "",
                "empty_line_28": "",
                "empty_line_29": "",
                "empty_line_30": "",
                "empty_line_31": "",
                "component_data": "\n".join(component_data)
            })

            filename = f"{pcb_sn}_{time_str}_{board_no}_{time_str}.txt"
            file_path = os.path.join(save_path, filename)

            try:
                xutil.FileUtil.write_content_to_file(file_path, content)
            except Exception as e:
                self.log.error(f"本地文件保存失败: {str(e)}")

        return self.x_response()
