# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/16 上午09:16
# Author     ：gyr
# version    ：python 3.8
# Description：印度捷普
"""
import locale
import os
from datetime import datetime
from typing import Any

from common import xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo


def format_datetime(dt):
    """格式化日期时间，确保显示AM/PM"""
    hour = dt.hour
    am_pm = "AM" if hour < 12 else "PM"
    if hour > 12:
        hour -= 12
    elif hour == 0:
        hour = 12
    return dt.strftime(f"%Y-%m-%d {hour:02d}:%M:%S {am_pm}")


def format_timestamp(dt):
    """格式化时间戳，用于文件名"""
    hour = dt.hour
    am_pm = "AM" if hour < 12 else "PM"
    if hour > 12:
        hour -= 12
    elif hour == 0:
        hour = 12
    return dt.strftime(f"%-m.%d.%Y {hour:02d}.%M.%S {am_pm}")


class Engine(ErrorMapEngine):
    version = {
        "title": "jiepu release v1.0.0.2",
        "device": "AIS50X",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-04-16 09:23  jira:ATAOI_2019-38729,生成txt文件
date: 2025-04-16 17:09  jira:ATAOI_2019-38729,复判PASS参数修改
""", }
    form = {
        "Customer": {
            "ui_name": "Customer",
            "value": ""
        },
        "CustomerDivision": {
            "ui_name": "CustomerDivision",
            "value": ""
        },
        "Station": {
            "ui_name": "Station",
            "value": ""
        },
        "process": {
            "ui_name": "process",
            "value": ""
        },
        "Operator": {
            "ui_name": "Operator",
            "value": ""
        }
    }

    path = {
        "DataPath": {
            "ui_name": "TXT file save path",
            "value": ""
        }
    }

    def __init__(self):
        super().__init__()
        # 设置为英文环境
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        customer = data_dao.get_value_by_cons_key("Customer")
        customer_division = data_dao.get_value_by_cons_key("CustomerDivision")
        station = data_dao.get_value_by_cons_key("Station")
        process = data_dao.get_value_by_cons_key("process")
        operator = data_dao.get_value_by_cons_key("Operator")
        data_path = data_dao.get_value_by_cons_key("DataPath")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)
        program_name = pcb_entity.project_name

        if pcb_entity.is_repair_ng():
            result_data = "TF"
        else:
            result_data = "TP"

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            now = datetime.now()
            start_time = format_datetime(now)
            lines = [
                {"type": "S", "value": barcode},
                {"type": "C", "value": customer},
                {"type": "I", "value": customer_division},
                {"type": "N", "value": station},
                {"type": "P", "value": process},
                {"type": "n", "value": program_name},
                {"type": "O", "value": operator},
                {"type": "[", "value": start_time},
            ]
            if board_entity.is_repair_ng():
                # 收集不良器件信息
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        lines.extend([
                            {
                                "type": "F",
                                "designator": comp_entity.designator,
                                "code": comp_entity.repair_ng_str,
                                "value": f"{comp_entity.designator} {comp_entity.repair_ng_str}"
                            },
                            {
                                "type": ">",
                                "value": comp_entity.repair_ng_str
                            },
                            {
                                "type": "c",
                                "value": comp_entity.designator
                            }
                        ])

            end_time = format_datetime(datetime.now())
            lines.append({"type": "[", "value": end_time})
            lines.append({"type": "", "value": result_data})

            timestamp = format_timestamp(datetime.now())
            filename = f"{barcode} {timestamp}.txt" if barcode else f"{timestamp}.txt"
            file_path = os.path.join(data_path, filename)

            content = "\n".join(f"{item['type']}{item['value']}" for item in lines)
            xutil.FileUtil.write_content_to_file(file_path, content)

        return self.x_response()
