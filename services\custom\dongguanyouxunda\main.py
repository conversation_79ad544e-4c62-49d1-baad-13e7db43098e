# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/06/13 17:22
# Author     ：chencb
# version    ：python 3.8
# Description：东莞友讯达 https://jira.cvte.com/browse/ATAOI_2019-40219
"""
import json
import os
from datetime import datetime
from typing import Any
from common import xcons, xrequest
from common.xconfig import home_dir
from common.xutil import log, x_response, XmlUtil, DateUtil, OtherUtil, FileUtil, LimitedCapacityDict
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

# 直通率数据本地缓存
MES_CACHE_DIR = f"{home_dir}/.aoi/mes_cache"
BOARD_TEST_RECORD_FILE = f'{MES_CACHE_DIR}/board_test_record.json'


class Engine(ErrorMapEngine):
    version = {
        "customer": ["东莞友讯达", "dongguanyouxunda"],
        "version": "release v1.0.0.5",
        "device": "AIS50X, AIS63X",
        "feature": ["发送数据", "发送设备状态"],
        "author": "chenchongbing",
        "release": """
date: 2025-06-17 09:30  jira:40219 发送xml结果数据
date: 2025-06-18 19:24  1.TestTime字段去除中间T；LotSN字段改为拼板条码
date: 2025-06-19 17:30  请求方式从post_xml改为post_form
date: 2025-07-14 11:20  发送设备状态到MQTT；数据发送增加PassRate字段记录直通率
date: 2025-07-18 11:35  设备状态改为旧设备状态实现逻辑
""",
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "substrate_name": {
            "ui_name": "料号",
            "value": "",
        },
        "batch_number": {
            "ui_name": "批次号",
            "value": "",
        },
        "line_type": {
            "ui_name": "线体",
            "value": "",
        },
        "mes_api_url": {
            "ui_name": "接口地址",
            "value": "",
        },
        "version": {
            "ui_name": "【总控】版本",
            "value": "",
        },
        "message_type": {
            "ui_name": "【总控】消息类型",
            "value": "",
        },
        "device_code": {
            "ui_name": "【总控】设备编码",
            "value": "",
        },
        "mq_broker": {
            "ui_name": "【MQ】服务器地址",
            "value": "",
        },
        "mq_port": {
            "ui_name": "【MQ】端口号",
            "value": "",
        },
        "mq_account": {
            "ui_name": "【MQ】登录账号",
            "value": "",
        },
        "mq_pwd": {
            "ui_name": "【MQ】登录密码",
            "value": "",
        },
    }

    password_style = ["mq_pwd"]

    pcb_data_template = f'''
    <AOI_Data>
        <PassRate>{{pass_rate}}</PassRate>
        {{board_data}}
    </AOI_Data>'''

    board_data_template = f'''
    <AOI_DataItem>
        {{fail_image}}
        <TestTime>{{test_time}}</TestTime>
        <DeviceName>{{device_name}}</DeviceName>
        <LotSN>{{board_sn}}</LotSN>
        <PanelNumber>{{board_no}}</PanelNumber>
        <DefectiveComponents>0</DefectiveComponents>
        <FalseAalarmComponent>0</FalseAalarmComponent>
        <WarnComponent>1</WarnComponent>
        <SubstrateName>{{substrate_name}}</SubstrateName>
        <TotalComponents>{{comp_count}}</TotalComponents>
        <PanelDrawing>{{pcb_image_path}}</PanelDrawing>
        <operator>{{operator}}</operator>
        <Side>{{side}}</Side>
        <BatchNumber>{{batch_number}}</BatchNumber>
        <Linetype>{{line_type}}</Linetype>
        <Result>{{repair_result}}</Result>
    </AOI_DataItem>'''

    fail_image_template = f'''
    <Fail_Image>
        <fileUrl>{{ng_pic_path}}</fileUrl>
        <fileName>{{ng_pic_name}}</fileName>
        <TagNumber>{{ng_desc}}</TagNumber>
        <XY_Axle>{{xy_axle}}</XY_Axle>
    </Fail_Image>'''

    def __init__(self):
        self.last_device_status = {}
        self.board_test_records = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        FileUtil.ensure_dir_exist(MES_CACHE_DIR)
        self.board_test_records = LimitedCapacityDict(200, file_path=BOARD_TEST_RECORD_FILE)

    def _get_pass_rate(self, project_name, pass_board_cnt, total_board_cnt):
        # 结构：{'板式名'：[pass板卡数量，total板卡数量]}
        board_test_record = self.board_test_records.get(project_name, [])
        if board_test_record:
            last_pass_board_cnt = board_test_record[0]
            last_total_board_cnt = board_test_record[1]
            pass_board_cnt = last_pass_board_cnt + pass_board_cnt
            total_board_cnt = last_total_board_cnt + total_board_cnt

        pass_rate = f'{round(pass_board_cnt / total_board_cnt * 100, 2)}%'

        # 每次更新新的直通率后，进行下数据缓存
        self.board_test_records.set(project_name, [pass_board_cnt, total_board_cnt])
        return pass_rate

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key('device_name')
        operator = data_vo.get_value_by_cons_key('operator')
        substrate_name = data_vo.get_value_by_cons_key('substrate_name')
        batch_number = data_vo.get_value_by_cons_key('batch_number')
        line_type = data_vo.get_value_by_cons_key('line_type')
        mes_api_url = data_vo.get_value_by_cons_key('mes_api_url', not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        inspect_type = pcb_entity.get_insect_mes_type()
        if inspect_type == xcons.INSPECTOR:
            return x_response('false', '只发送复判后数据，本次为机器检测发送，直接返回不处理!')

        board_data_list = []
        pass_board_cnt = 0
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)
            if board_entity.robot_result:
                pass_board_cnt += 1

            ng_comp_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.repair_result:
                    continue

                ng_pic_path = comp_entity.image_path
                ng_pic_name = os.path.basename(ng_pic_path)
                # xy坐标：X:Y
                xy_axle = f'{comp_entity.x_pos}:{comp_entity.y_pos}'
                # 格式：位号-不良描述-ng
                ng_desc = f'{comp_entity.designator}-{comp_entity.robot_ng_str}-ng'
                fail_image = {
                    'ng_pic_path': ng_pic_path,
                    'ng_pic_name': ng_pic_name,
                    'ng_desc': ng_desc,
                    'xy_axle': xy_axle
                }
                ng_comp = self.fail_image_template.format(**fail_image)
                ng_comp_list.append(ng_comp)

            test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
            board_data = {
                'fail_image': '\n'.join(ng_comp_list),
                'test_time': test_time,
                'device_name': device_name,
                'board_sn': board_entity.barcode,
                "board_no": board_entity.board_no,
                "substrate_name": substrate_name,
                "comp_count": board_entity.comp_total_number,
                "pcb_image_path": ','.join(pcb_entity.pcb_image),
                "operator": operator,
                "side": pcb_entity.board_side,
                "batch_number": batch_number,
                "line_type": line_type,
                "repair_result": board_entity.get_repair_result('REPASS', 'NG'),
            }
            board_data = self.board_data_template.format(**board_data)
            board_data_list.append(board_data)

        pass_rate = self._get_pass_rate(pcb_entity.project_name, pass_board_cnt, pcb_entity.board_count)
        pcb_data = {
            'pass_rate': pass_rate,
            'board_data': '\n'.join(board_data_list)
        }
        pcb_data_xml = self.pcb_data_template.format(**pcb_data)
        pcb_data_xml = XmlUtil.format_xml(pcb_data_xml)
        # 去除头，否则对方服务会报错
        pcb_data_xml = pcb_data_xml.replace('<?xml version="1.0" encoding="UTF-8"?>', '', 1).lstrip()
        self.log.info(f'发送的xml数据为：{pcb_data_xml}')
        try:
            form_data = {'xml': pcb_data_xml}
            ret = xrequest.RequestUtil.post_form(mes_api_url, form_data, to_json=False, raw_response=True)
            if ret.status_code == 200:
                return self.x_response()
            else:
                err_msg = f'服务返回状态码：{ret.status_code},返回文本信息:{ret.text}'
                return self.x_response('false', err_msg)
        except Exception as e:
            return self.x_response('false', f'本地网络异常，发送数据失败，error:{e}')

    def _send_status_to_mqtt(self, other_vo: OtherVo, status_code, status_desc):
        version = other_vo.get_value_by_cons_key('version')
        message_type = other_vo.get_value_by_cons_key('message_type')
        device_name = other_vo.get_value_by_cons_key('device_name')
        device_code = other_vo.get_value_by_cons_key('device_code', not_null=True)
        mq_broker = other_vo.get_value_by_cons_key('mq_broker', not_null=True)
        mq_port = other_vo.get_value_by_cons_key('mq_port', not_null=True, to_int=True)
        mq_account = other_vo.get_value_by_cons_key('mq_account')
        mq_pwd = other_vo.get_value_by_cons_key('mq_pwd')

        if not device_code:
            return self.x_response('false', 'topic和client_id需要填写设备编码，设备编码不能为空！！')

        # 格式：***************
        mac = OtherUtil.get_mac_address()
        mac = mac.replace(':', '').upper()

        last_state = self.last_device_status.get('state', '')
        last_time = self.last_device_status.get('time', '')
        # 格式：供应商拼音首字母缩写(镭晨装备)-故障代码-故障描述
        current_state = f"lczb-{status_code}-{status_desc}"
        cur_time = DateUtil.get_datetime_now()

        # 0表示设备正常，1代表设备等待，2代表设备故障
        if status_desc in ['待料(空闲)']:
            status = '1'
        elif status_desc in ['进板', "开始检测", "停止检查", "出板"]:
            status = '0'
        else:
            status = '2'

        duration = 0
        if last_time:
            dt1 = datetime.strptime(last_time, xcons.FMT_TIME_FILE)
            dt2 = datetime.strptime(cur_time, xcons.FMT_TIME_FILE)
            duration = int((dt2 - dt1).total_seconds())

        payload = {
            "version": version,
            "type": message_type,
            "sim": "",
            "equipmentname": device_name,
            "equipmentno": device_code,
            "mac": mac,
            "laststate": last_state,
            "currentstate": current_state,
            "duration": duration,
            "time": cur_time,
            "status": status
        }

        client_id = f'friendcom_{device_code}'
        topic = f'/yxd/zk/{device_code}/update'
        payload_str = OtherUtil.obj_to_json(payload)
        result, msg = xrequest.publish_mqtt_message(mq_broker, mq_port, client_id, topic, payload_str, mq_account,
                                                    mq_pwd)
        if result:
            self.last_device_status = {
                "state": current_state,
                "time": cur_time
            }
            return self.x_response()
        else:
            return self.x_response('false', msg)

    def send_idle_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        status_code = 3001
        status_desc = '待料(空闲)'
        ret = self._send_status_to_mqtt(other_vo, status_code, status_desc)
        return ret

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        # 东莞友讯达是旧的设备状态接口
        status_desc = other_vo.get_status_desc()
        status_code = other_vo.get_status_code()

        ret = self._send_status_to_mqtt(other_vo, status_code, status_desc)
        return ret
