# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/2/5 上午11:23
# Author     ：sch
# version    ：python 3.8
# Description：高悦智能
"""
import json
import os
from typing import Any

from PIL import Image

from common import xcons, xutil, xrequest
from common.xutil import CircularList
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

pcb_template = """程序名：{project_name}
测试时间：{start_time}
操作员：{repair_user}
整板条码：{barcode}
整板结果：{final_result}
拼板数量：{board_count}
拼板复判NG数量：{repair_ng_count}
器件总数：{comp_count}
器件复判不良总数：{pcb_comp_ng_count}
整板图片的存储地址：{pcb_image_path}
拼板图片的存储地址：{board_image_path}
{BoardData}
"""

pcb_template1 = """程序名：{project_name}
测试时间：{start_time}
操作员：{repair_user}
整板条码：{barcode}
拼板人工复判结果：{board_repair_result}
机器检测结果：{board_robot_result}
拼板数量：{board_count}
拼板复判NG数量：{repair_ng_count}
器件总数：{comp_count}
器件复判不良总数：{pcb_comp_ng_count}
整板图片的存储地址：{pcb_image_path}
拼板图片的存储地址：{board_image_path}
{BoardData}
"""

board_template = """
================ No: {board_no} ================ 结果：{final_result}  --> 小板SN: {barcode}{CompData}
"""

comp_template = """
---位号：{tag}  料号：{part}  封装：{package} 　类型：{type}  检测不良代码：{robot_ng_code}　 检测结果：{robot_ng_str}  复判不良代码：{robot_ng_code}  复判结果：{repair_ng_str}  图片路径：{comp_image}"""


circle_list2 = CircularList(200)


class Engine(ErrorMapEngine):
    version = {
        "title": "gaoyuezhineng release v1.0.0.9",
        "device": "303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-02-05 11:23  init
date: 2024-02-05 14:29  新增分拼板保存方式
date: 2024-03-01 15:29  增加接口上传
date: 2024-03-14 14:44  增加上传TCP的配置项
date: 2024-03-14 16:15  修改txt文档格式，以及按分板上传tcp
date: 2024-05-23 10:23  可以根据维修站 man.xml 的RepairResult标识将pass结果改成ng结果
date: 2024-06-25 17:34  tcp发送增加重发机制,重发三次
date: 2024-06-26 17:55  bugfix: 发送超时的异常没有发送给维修站
date: 2024-08-23 11:36  优化上传图片逻辑：如果图片已经保存过，不需要重复保存！  ps: 因为有分板发送逻辑
""", }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    other_combo = {
        # "is_upload_tcp": {
        #     "ui_name": "上传到TCP",
        #     "item": ["Yes", "No"],
        #     "value": "Yes",
        # },
        "upload_tcp_type": {
            "ui_name": "上传到TCP",
            "item": ["仅检测完上传", "仅复判后上传", "检测完+复判后上传", "都不上传"],
            "value": "复判后上传",
        },
        "line_save_type": {
            "ui_name": "线体类型",
            "item": ["DP-EPS-FA线", "SD电子线束PCBA检测"],
            "value": "DP-EPS-FA线",
        },
    }

    other_form = {
        "tcp_host": {
            "ui_name": "Tcp Host",
            "value": "",
        },
        "tcp_port": {
            "ui_name": "Tcp Port",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        save_type = data_vo.get_value_by_cons_key("line_save_type")

        upload_tcp_type = data_vo.get_value_by_cons_key("upload_tcp_type")
        tcp_host = data_vo.get_value_by_cons_key("tcp_host")
        tcp_port = data_vo.get_value_by_cons_key("tcp_port")

        pcb_entity = data_vo.pcb_entity
        time_date = pcb_entity.get_start_time().strftime("%Y/%m/%d")
        time_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        hour_now = time_now[8:]

        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_index_list = other_data.get('board_index_list', [])
        self.log.info(f"分板发送 --> board index: {board_index_list}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not pcb_sn:
                pcb_sn = barcode

        if not pcb_sn:
            pcb_sn = time_now

        inspect_type = other_data.get("inspect_type")

        # 保存整板图片
        pcb_t_image = pcb_entity.get_pcb_t_image()

        # 3. 保存拼板图
        box_pos_map = pcb_entity.get_board_box_position()
        pcb_image_obj = Image.open(pcb_t_image)

        board_data = ""
        repair_ng_count = 0
        pcb_comp_ng_count = 0
        board_image_list = []

        review_path = data_vo.get_review_path()

        if circle_list2.is_exist_item(review_path):
            # 这个数据包已经发送过了
            is_upload_img = True
        else:
            # 该数据包第一次发送
            circle_list2.add_item(review_path)
            is_upload_img = False

        for board_entity in pcb_entity.yield_board_entity():
            if board_entity.is_repair_ng():
                repair_ng_count += 1

            pcb_comp_ng_count += board_entity.comp_repair_ng_number

        self.log.info(f"save type: {save_type}")

        review_path = pcb_entity.get_pcb_pcb_t_review_path()

        pcb_result1 = "OK"
        pcb_result2 = pcb_entity.get_final_result()

        for board_entity in pcb_entity.yield_board_entity():
            board_no = board_entity.board_no
            barcode = board_entity.barcode

            if board_entity.is_repair_ng():
                pcb_result1 = "NG"

            if save_type == "DP-EPS-FA线":
                board_dst_path = f"{save_path}/{time_date}/{pcb_sn}/拼板图"
                comp_dst_ok_path = f"{save_path}/{time_date}/{pcb_sn}/器件图/OK图"
                comp_dst_ng_path = f"{save_path}/{time_date}/{pcb_sn}/器件图/NG图"
            else:
                board_dst_path = f"{save_path}/{time_date}/{barcode}_{time_now}_{board_no}/拼板图"
                comp_dst_ok_path = f"{save_path}/{time_date}/{barcode}_{time_now}_{board_no}/器件图/OK图"
                comp_dst_ng_path = f"{save_path}/{time_date}/{barcode}_{time_now}_{board_no}/器件图/NG图"

            xutil.FileUtil.ensure_dir_exist(board_dst_path)
            xutil.FileUtil.ensure_dir_exist(comp_dst_ok_path)
            xutil.FileUtil.ensure_dir_exist(comp_dst_ng_path)

            # 20240523 需要去解析一下man.xml, 看一下是否将PASS结果改成复判NG
            # jira: ATAOI_2019-29207
            man_path = f"{review_path}/mes/{board_no}/man.xml"
            if os.path.exists(man_path):
                root = xutil.XmlUtil.get_xml_root_by_file(man_path)
                repair_result = root.attrib.get("RepairResult", "0")  # 1,复判成NG
                self.log.info(f"repair result: {repair_result}")

            else:
                self.log.warning(f"拼板{board_no}的man.xml不存在，无法查看维修站是否复判成NG！")
                repair_result = "0"

            # 3. 保存拼板图
            box = box_pos_map.get(board_no)
            board_dst_img = f"{board_dst_path}/{pcb_sn}_{hour_now}_{board_no}.jpg"
            if save_type != "DP-EPS-FA线":
                board_dst_img = f"{board_dst_path}/{barcode}_{hour_now}_{board_no}.jpg"

            if not is_upload_img:
                cropped_image = pcb_image_obj.crop(box)
                cropped_image.save(board_dst_img)
                self.log.info(f"拼板图保存成功：{board_dst_img}")
            else:
                print("本次不保存拼板图！")

            board_image_list.append(board_dst_img)

            comp_data = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                # 4. 保存器件图
                comp_src_image = comp_entity.image_path
                if comp_src_image:

                    if comp_entity.is_robot_ng():
                        comp_save_path = comp_dst_ng_path
                        comp_result = "NG"
                    else:
                        comp_save_path = comp_dst_ok_path
                        comp_result = "OK"

                    comp_dst_img = f"{comp_save_path}/{pcb_sn}_{hour_now}_{comp_tag}_{comp_result}.png"
                    if save_type != "DP-EPS-FA线":
                        comp_dst_img = f"{comp_save_path}/{barcode}_{hour_now}_{comp_tag}_{comp_result}.png"

                    if not is_upload_img:
                        xutil.FileUtil.copy_file(comp_src_image, comp_dst_img)
                    else:
                        print("本次不保存器件图片")

                else:
                    comp_dst_img = ""

                comp_json_data = comp_entity.to_json_data()

                if repair_result == "1":
                    comp_json_data["repair_ng_code"] = "127"
                    comp_json_data["repair_ng_str"] = "可维修(Repairable)"

                comp_json_data["comp_image"] = comp_dst_img
                comp_data += comp_template.format(**comp_json_data)

            board_json_data = board_entity.to_json_data()

            result1 = board_entity.get_repair_result()

            board_json_data["barcode"] = pcb_sn
            board_json_data["CompData"] = comp_data
            if repair_result == "1":
                result1 = "NG"
                pcb_result1 = "NG"
                pcb_result2 = "NG"
                board_json_data["repair_result"] = result1

                board_json_data["repair_result"] = "NG"
                board_json_data["final_result"] = "NG"

            board_item = board_template.format(**board_json_data)

            board_data += board_item

            # 2024新增
            if save_type == "SD电子线束PCBA检测":
                # SD电子线束PCBA检测, 发送...
                # 保存整板图
                pcb_dst_path = f"{save_path}/{time_date}/{barcode}_{time_now}_{board_no}/整板图"
                xutil.FileUtil.ensure_dir_exist(pcb_dst_path)
                pcb_dst_img = f"{pcb_dst_path}/{time_now}.jpg"

                if not is_upload_img:
                    xutil.FileUtil.copy_file(pcb_t_image, pcb_dst_img)
                else:
                    print("本次不上传整板图片")

                # 保存txt
                dst_file_path = f"{save_path}/{time_date}/{barcode}_{time_now}_{board_no}"

                xutil.FileUtil.ensure_dir_exist(dst_file_path)
                dst_file = f"{dst_file_path}/{barcode}_{hour_now}.txt"

                pcb_json_data = pcb_entity.to_json_data()

                pcb_json_data["repair_ng_count"] = repair_ng_count
                pcb_json_data["pcb_comp_ng_count"] = pcb_comp_ng_count
                pcb_json_data["pcb_image_path"] = pcb_dst_img
                pcb_json_data["board_image_path"] = board_dst_img
                pcb_json_data["BoardData"] = board_item  # 按拼板保存

                pcb_json_data["board_robot_result"] = board_entity.get_robot_result()
                pcb_json_data["board_repair_result"] = result1

                if inspect_type == "inspector":
                    pcb_json_data["board_repair_result"] = "none"

                if not board_index_list or board_no in board_index_list:
                    pcb_data2 = pcb_template1.format(**pcb_json_data)

                    xutil.FileUtil.write_content_to_file(dst_file, pcb_data2)
                    self.log.info(f"文档保存成功！")

                    # if is_upload_tcp == "Yes":
                    if (inspect_type == "inspector" and upload_tcp_type in ["仅检测完上传", "检测完+复判后上传"]) \
                            or (inspect_type == 'repair' and upload_tcp_type in ['仅复判后上传', '检测完+复判后上传']):
                        try:
                            tcp_port = int(tcp_port)
                        except Exception as err:
                            return self.x_response("false", f"Tcp Port [{tcp_port}] 必须为数字，error: {err}")

                        send_param = {
                            "code": barcode,
                            "result_aoi": board_entity.get_robot_result("OK", "NG"),
                            "result_artificial": result1,
                            "file_path": f"{save_path}/{time_date}/{barcode}"
                        }
                        xrequest.SocketUtil.send_data_with_retries(tcp_host, tcp_port, json.dumps(
                            send_param,
                            separators=(",", ":")
                        ))
                    else:
                        self.log.warning(f"发送类型：{inspect_type}")
                        self.log.warning(f"配置项：上传到TCP-->{upload_tcp_type}")
                        self.log.warning(f'无需上传数据到tcp服务器！')
                else:
                    self.log.warning(f"该拼板本次不上传！")

        if save_type == "DP-EPS-FA线":
            pcb_dst_path = f"{save_path}/{time_date}/{pcb_sn}/整板图"

            xutil.FileUtil.ensure_dir_exist(pcb_dst_path)

            pcb_dst_img = f"{pcb_dst_path}/{pcb_sn}_{hour_now}.jpg"
            if not is_upload_img:
                xutil.FileUtil.copy_file(pcb_t_image, pcb_dst_img)
            else:
                print("本次不上传图片")

            # 保存文档
            pcb_json_data = pcb_entity.to_json_data()

            pcb_json_data["repair_result"] = pcb_result1
            pcb_json_data["final_result"] = pcb_result2

            pcb_json_data["repair_ng_count"] = repair_ng_count
            pcb_json_data["pcb_comp_ng_count"] = pcb_comp_ng_count
            pcb_json_data["pcb_image_path"] = pcb_dst_img
            pcb_json_data["board_image_path"] = ",".join(board_image_list)
            pcb_json_data["BoardData"] = board_data  # 按整板保存

            pcb_data = pcb_template.format(**pcb_json_data)

            dst_file_path = f"{save_path}/{time_date}/{pcb_sn}"
            xutil.FileUtil.ensure_dir_exist(dst_file_path)
            dst_file = f"{dst_file_path}/{pcb_sn}.txt"
            xutil.FileUtil.write_content_to_file(dst_file, pcb_data)

            # if (inspect_type == "inspector" and upload_tcp_type in ["仅检测完上传", "检测完+复判后上传"]) \
            #         or (inspect_type == 'repair' and upload_tcp_type in ['仅复判后上传', '检测完+复判后上传']):
            #     try:
            #         tcp_port = int(tcp_port)
            #     except Exception as err:
            #         return self.x_response("false", f"Tcp Port [{tcp_port}] 必须为数字，error: {err}")
            #
            #     send_param = {
            #         "code": pcb_sn,
            #         "result_aoi": pcb_entity.get_robot_result("OK", "NG"),
            #         "result_artificial": pcb_entity.get_repair_result("OK", "NG"),
            #         "file_path": f"{save_path}/{time_date}/{pcb_sn}"
            #     }
            #     xrequest.SocketUtil.send_data_to_socket_server(tcp_host, tcp_port, json.dumps(
            #         send_param,
            #         separators=(",", ":")
            #     ))
            # else:
            #     self.log.warning(f"发送类型：{inspect_type}")
            #     self.log.warning(f"配置项：上传到TCP-->{upload_tcp_type}")
            #     self.log.warning(f'无需上传数据到tcp服务器！')

        return self.x_response()
