import requests


def send_post_request():
    # 定义目标 URL
    url = "http://127.0.0.1:9050/QuerySn"
    # url = "http://127.0.0.1:5000/QuerySn"

    # 定义要发送的 JSON 数据
    payload = {
        "plate_sn": "111",
    }

    try:
        # 发送 POST 请求，指定 json 参数
        response = requests.post(url, json=payload)

        # 检查响应状态码
        if response.status_code == 200:
            print("请求成功！")
            # 打印返回的 JSON 数据
            data = response.json()
            print("响应数据:", data)
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print("响应内容:", response.text)

    except requests.exceptions.RequestException as e:
        # 捕获请求异常
        print(f"请求出错: {e}")


if __name__ == '__main__':
    send_post_request()