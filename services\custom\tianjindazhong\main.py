"""
# File       : main.py
# Time       ：2025/06/24 15:57
# Author     ："wxc"
# version    ：python 3.8
# Description：天津大众
"""
import re
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["天津大众", "tianjindazhong"],
        "version": "release v1.0.0.1",
        "device": "AIS40X",
        "feature": ["保存整板图"],
        "author": "wxc",
        "release": """
date: 2025-06-24 15:58 ATAOI_2019-39589: 保存整板图
"""
    }
    path = {
        "save_img_path": {
            "ui_name": "整板图保存根目录",
            "value": "",
        },
    }
    form = {
        "camer_no": {
            "ui_name": "相机编码",
            "value": "",
        },
        "step_number": {
            "ui_name": "步骤号",
            "value": "",
        }

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_img_path = data_vo.get_value_by_cons_key("save_img_path", not_null=True)
        camera_no = data_vo.get_value_by_cons_key("camer_no")
        step_number = data_vo.get_value_by_cons_key("step_number")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        invalid_chars = r'[\\/\$\*\?\&\^\|\[\]\{\}<>`~"' + '\x00' + '\t\n\r\f\v' + ']'
        if re.search(invalid_chars, pcb_sn):
            self.log.warning(f"条码存在非法字符: {pcb_sn}, 将进行替换处理")
        # 替换非法字符为 -
        pcb_sn = re.sub(invalid_chars, '-', pcb_sn)

        time_path = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        test_time = pcb_entity.get_start_time().strftime("%Y%m%d_%H%M%S")
        image_path = pcb_entity.get_pcb_t_image()
        repair_result = pcb_entity.get_repair_result()
        # 目标路径
        dst_image_path = f"{save_img_path}/{repair_result}/{time_path}"
        xutil.FileUtil.ensure_dir_exist(dst_image_path)
        image_filename = f"{camera_no}_{pcb_sn}_{test_time}_{step_number}_{repair_result}.jpg"
        # copy整板图到目标路径
        xutil.FileUtil.copy_file(image_path, f"{dst_image_path}/{image_filename}")

        return self.x_response()

