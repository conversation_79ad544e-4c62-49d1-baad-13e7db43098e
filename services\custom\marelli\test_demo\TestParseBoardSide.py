# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : TestParseBoardSide.py
# Time       ：2025/4/26 上午11:24
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import unittest
from services.custom.marelli.mar_module import parse_board_side


class TestParseBoardSide(unittest.TestCase):

    def test_board_side_top(self):
        board_side = parse_board_side("/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172422008_1_NG")
        self.assertEqual(board_side, "0")

    def test_board_side_bottom(self):
        board_side = parse_board_side("/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172401154_1_NG")
        self.assertEqual(board_side, "1")

    def test_board_side_unknown(self):
        board_side = parse_board_side("/home/<USER>/aoi/run/results/333.999bak/20250423")
        self.assertEqual(board_side, "0")

    def test_board_side_null(self):
        board_side = parse_board_side("")
        self.assertEqual(board_side, "0")
