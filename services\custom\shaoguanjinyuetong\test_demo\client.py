# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : client.py
# Time       ：2025/5/6 下午3:24
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import asyncio
import websockets
import json
import time


async def receive_messages(websocket):
    async for message in websocket:

        data = json.loads(message)

        msg_type = data.get("type")
        if msg_type == "statusResponse":
            print("[客户端] 收到设备状态响应：", )
        elif msg_type == "statusUpdate":
            print("[客户端] 收到设备主动推送状态：")
        elif msg_type == "pong":
            print("[客户端] 收到设备心跳响应")
        elif msg_type == "closeConnection":
            print("[客户端] 收到关闭连接的指令，关闭连接")
            await websocket.close()  # 关闭连接
            break
        else:
            print("[客户端] 收到未知消息：")

        print("message : ", message)


async def send_heartbeat_and_requests(websocket):
    while True:
        # 每 10 秒请求状态
        await websocket.send(json.dumps({"type": "getStatus"}))
        print("[客户端] 请求设备状态")
        await asyncio.sleep(30)

        # # 每 3 秒发送心跳
        # for _ in range(3):
        #     await asyncio.sleep(3)
        #     await websocket.send(json.dumps({"type": "ping"}))
        #     print("[客户端] 发送心跳")


async def run_client():
    uri = "ws://localhost:9393"  # 或设备的实际 IP 地址
    try:
        async with websockets.connect(uri) as websocket:
            print("[客户端] 已连接设备")
            await asyncio.gather(
                receive_messages(websocket),
                send_heartbeat_and_requests(websocket)
            )
    except Exception as e:
        print(f"[客户端] 连接失败：{e}")


asyncio.run(run_client())
