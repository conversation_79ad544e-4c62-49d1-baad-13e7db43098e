# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/23 上午10:32
# Author     ：sch
# version    ：python 3.8
# Description：德赛西威
"""
import time
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

sv_template = """{1_1},{1_2},{1_3},{1_4},{1_5},{1_6},{1_7},{1_8},{1_9},{1_10},{1_11},{1_12}"""

txt_template = """UUT_Order|UUT_SOURCE|DeviceA2C|Equipment Function|Serial Number|Station Name||||Product Time|TestStand Name|Login Name|Execution Time|TestScoket|BatchSerialNumber|Error Code|Error Message||||||||||||||TestStatus|{board_data}
Step_Order|Step_Source|ProductionTime|ErrorCode|ErrorMessage|TotalTime|StepName|TestStatus|ReportText|Num_Loops|Num_Passed|Num_Failed|StepGroup|StepType|Step_PassFail|Multiple_SubName|Units|Comp|Limit.Step_Data|Limit.Low|Limit.High|Result_String|Limits_String|{comp_data_str}"""
txt_template1 = """
{ev01}|{ev02}|{ev03}|{ev04}|{ev05}|{ev06}|{ev07}|{ev08}|{ev09}|{ev10}|{ev11}|{ev12}|{ev13}|{ev14}|{ev15}|{ev16}|{ev17}|{ev18}|{ev19}|{ev20}|{ev21}|{ev22}|{ev23}|{ev24}|{ev25}|{ev26}|{ev27}|{ev28}|{ev29}|{ev30}|{ev31}"""
txt_template2 = """
{me01}|{me02}|{me03}|{me04}|{me05}|{me06}|{me07}|{me08}|{me09}|{me10}|{me11}|{me12}|{me13}|{me14}|{me15}|{me16}|{me17}|{me18}|{me19}|{me20}|{me21}|{me22}|{me23}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "desaixiwei release v1.0.0.17",
        "device": "501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-23 10:33  init
date: 2024-03-24 10:02  条码校验，上传数据
date: 2024-03-24 21:10  MoveIn增加HideWindow参数
date: 2024-03-26 14:28  MoveStd接口改到复判后发送
date: 2024-03-29 13:02  bugfix
date: 2024-03-29 16:24  增加[发送数据模式]配置项
date: 2024-03-30 10:53  txt格式修改
date: 2024-04-03 09:27  ev15输出板面信息
date: 2024-04-11 12:13  me16输出器件板面信息
date: 2024-04-12 14:13  器件板面信息改输出到me18  
date: 2024-07-18 14:33  优化xml解析模块，如果第一次解析失败则0.5s后尝试重新解析
""", }

    other_form = {
        "window_ip": {
            "ui_name": "window中转IP",
            "value": "",
        },
        "station": {
            "ui_name": "MES工位名",
            "value": "MED_ICT_0001",
        },
        "station_spec": {
            "ui_name": "MES工位类型",
            "value": "",
        },
        "sv_db_username": {
            "ui_name": "数据库用户名(SV_interlocking)",
            "value": "TE_IP",
        },
        "sv_db_password": {
            "ui_name": "数据库密码(SV_interlocking)",
            "value": "PEDTEST@sql2005",
        },
        "sv_db_name": {
            "ui_name": "数据库名称(SV_interlocking)",
            "value": "TKP_EPS_ECU",
        },
        "sv_server_name": {
            "ui_name": "服务器名称(SV_interlocking)",
            "value": "HZHE015A",
        },
        "sv_station_id": {
            "ui_name": "工位名称(SV_interlocking)",
            "value": "EPS_FT",
        },
        "sv_line_group": {
            "ui_name": "线体编号(SV_interlocking)",
            "value": "1",
        },
        "sv_user": {
            "ui_name": "系统用户名(SV_interlocking)",
            "value": "1",
        },

    }

    form = {
        "employee": {
            "ui_name": "操作员工号",
            "value": "",
        },
        # "product_id": {
        #     "ui_name": "产品ID(DeviceA2C)",
        #     "value": "",
        # },
    }

    other_combo = {
        "check_mode": {
            "ui_name": "条码校验模式",
            "item": ["MoveIn", "Sv_Interlocking_Main", "MoveIn+Sv_Interlocking_Main", "-"],
            "value": "MoveIn",
        },
        "send_mes_mode": {
            "ui_name": "发送数据模式",
            "item": ["txt", "MoveStd", "txt+MoveStd", "-"],
            "value": "MoveStd",
        },
        "hide_window": {
            "ui_name": "HideWindow(MoveIn)",
            "item": ["True", "False"],
            "value": "False",
        },
        "sv_debug": {
            "ui_name": "调试状态(SV_interlocking)",
            "item": ["True", "False"],
            "value": "False",
        },
        "sv_show_window": {
            "ui_name": "ShowWindow(SV_interlocking)",
            "item": ["True", "False"],
            "value": "True",
        },
        "sv_pass_for_no_db": {
            "ui_name": "PassForNoDB(SV_interlocking)",
            "item": ["True", "False"],
            "value": "False",
        },
        "sv_function": {
            "ui_name": "Function(SV_interlocking)",
            "item": ["-1", "0", "1", "2", "3", "4", "5", "6", "7", ""],
            "value": "5",
        },

        "time_sleep_2": {
            "ui_name": "timeSleep",
            "item": ["0", "1", "2", "3", "4", "5", "6", "7"],
            "value": "0",
        },
    }

    path = {
        "save_path_txt": {
            "ui_name": "txt保存路径",
            "value": "",
        }
    }

    button = {
        "test_connect": {
            "ui_name": "testConnect"
        }
    }

    password_style = ["sv_db_password"]

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_mode = other_vo.get_value_by_cons_key("check_mode")
        window_ip = other_vo.get_value_by_cons_key("window_ip", not_null=True)
        station = other_vo.get_value_by_cons_key("station")
        station_spec = other_vo.get_value_by_cons_key("station_spec")
        employee = other_vo.get_value_by_cons_key("employee")

        sv_db_username = other_vo.get_value_by_cons_key("sv_db_username")
        sv_db_password = other_vo.get_value_by_cons_key("sv_db_password")
        sv_db_name = other_vo.get_value_by_cons_key("sv_db_name")
        sv_server_name = other_vo.get_value_by_cons_key("sv_server_name")
        sv_station_id = other_vo.get_value_by_cons_key("sv_station_id")
        sv_line_group = other_vo.get_value_by_cons_key("sv_line_group")
        sv_user = other_vo.get_value_by_cons_key("sv_user")
        sv_debug = other_vo.get_value_by_cons_key("sv_debug")
        sv_show_window = other_vo.get_value_by_cons_key("sv_show_window")
        sv_pass_for_no_db = other_vo.get_value_by_cons_key("sv_pass_for_no_db")
        sv_function = other_vo.get_value_by_cons_key("sv_function")
        hide_window = other_vo.get_value_by_cons_key("hide_window")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            if check_mode in ["MoveIn", "MoveIn+Sv_Interlocking_Main"]:
                param_str = f"{sn},{station},{station_spec},{employee},{hide_window}"

                packet_data_move_in = {
                    "type": 3,
                    "request_param": {
                        "info": param_str,
                    }
                }

                self.log.info(f"开始调用MoveIn接口....")
                ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data_move_in, timeout=10)
                ret_str = ret.get('string')
                if not str(ret_str).startswith('0'):
                    ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret_str}")

            if check_mode in ["Sv_Interlocking_Main", "MoveIn+Sv_Interlocking_Main"]:
                self.log.info(f"开始调用Sv_Interlocking_Main接口....")

                input_str = sv_template.format(**{
                    "1_1": sv_db_password,
                    "1_2": sv_db_username,
                    "1_3": sv_db_name,
                    "1_4": sv_server_name,
                    "1_5": sn,
                    "1_6": sv_station_id,
                    "1_7": sv_line_group,
                    "1_8": sv_user,
                    "1_9": sv_debug,
                    "1_10": sv_show_window,
                    "1_11": sv_pass_for_no_db,
                    "1_12": sv_function,
                })

                packet_data_sv = {
                    "type": 5,
                    "request_param": {
                        "info": input_str,
                    }
                }

                self.log.info(f"开始调用SV_interlocking接口....")
                ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data_sv)
                ret_str = ret.get('string')
                if not str(ret_str).startswith('0'):
                    ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret_str}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        window_ip = data_vo.get_value_by_cons_key("window_ip", not_null=True)
        station = data_vo.get_value_by_cons_key("station")
        station_spec = data_vo.get_value_by_cons_key("station_spec")
        employee = data_vo.get_value_by_cons_key("employee")
        hide_window = data_vo.get_value_by_cons_key("hide_window")
        time_sleep_2 = data_vo.get_value_by_cons_key("time_sleep_2")
        send_mes_mode = data_vo.get_value_by_cons_key("send_mes_mode")
        # product_id = data_dao.get_value_by_cons_key("product_id")
        save_path_txt = data_vo.get_value_by_cons_key("save_path_txt", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        tt1 = pcb_entity.get_start_time()
        start_time = tt1.strftime(xcons.FMT_TIME_DEFAULT6)
        time_file = tt1.strftime(xcons.FMT_TIME_FILE)

        inspect_type = other_data.get('inspect_type')
        project_name = pcb_entity.project_name

        cycle_time = pcb_entity.get_cycle_time()

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            product_id = barcode[:12]
            board_no = board_entity.board_no

            board_result = board_entity.get_repair_result("1", "0")

            std_str = f"{barcode},{station},{start_time},{board_result},{employee},{hide_window}"

            packet_data_move_std = {
                "type": 4,
                "request_param": {
                    "info": std_str,
                }
            }

            # ps：客户要求，因为我们设备测试的top面是客户板卡的b面
            if pcb_entity.board_side == "T":
                board_side = "Bottom"
            elif pcb_entity.board_side == "B":
                board_side = "Top"
            else:
                board_side = "Top+Bottom"

            ix = 0
            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                # if comp_entity.is_repair_ng():

                # ps：客户要求，因为我们设备测试的top面是客户板卡的b面
                comp_board_side = "Top" if comp_entity.board_side == "B" else "Bottom"

                for alg_entity in comp_entity.yield_alg_entity():
                    # if alg_entity.test_val != "0":
                    ix += 1
                    comp_data_str += txt_template2.format(**{
                        "me01": ix,
                        "me02": "ME",
                        "me03": time_file,
                        "me04": comp_entity.repair_ng_code,
                        "me05": comp_entity.repair_ng_str,
                        "me06": round(cycle_time),
                        "me07": alg_entity.test_name,
                        "me08": comp_entity.get_final_result("Passed", "Passed", "Failed"),
                        "me09": comp_entity.designator,
                        "me10": 1,
                        "me11": 1,
                        "me12": 0,
                        "me13": "Main",
                        "me14": "NumericLimitTest",
                        "me15": "",
                        "me16": "",
                        # "me16": comp_board_side,
                        "me17": "",
                        "me18": comp_board_side,
                        "me19": alg_entity.test_val,
                        "me20": alg_entity.min_threshold,
                        "me21": alg_entity.max_threshold,
                        "me22": "",
                        "me23": ""
                    })
                    comp_data_str = f"{comp_data_str}|"

            if inspect_type == 'inspector':
                pass
            else:
                self.log.info(f"开始保存txt文档...")

                board_r = board_entity.get_repair_result("Passed", "Failed")
                board_data = txt_template1.format(**{
                    "ev01": board_no,
                    "ev02": "EV",
                    "ev03": product_id,
                    "ev04": station_spec,
                    "ev05": barcode,
                    "ev06": station,
                    "ev07": "",
                    "ev08": "",
                    "ev09": "",
                    "ev10": time_file,
                    "ev11": project_name,
                    "ev12": employee,
                    "ev13": round(cycle_time),
                    "ev14": "0",
                    "ev15": board_side,  # 20240403 输出板面信息
                    "ev16": "",
                    "ev17": "",
                    "ev18": "",
                    "ev19": "",
                    "ev20": "",
                    "ev21": "",
                    "ev22": "",
                    "ev23": "",
                    "ev24": "",
                    "ev25": "",
                    "ev26": "",
                    "ev27": "",
                    "ev28": "",
                    "ev29": "",
                    "ev30": "",
                    "ev31": board_r
                })

                board_data = f"{board_data}|"

                if send_mes_mode in ["txt", "txt+MoveStd"]:
                    txt_content = txt_template.format(board_data=board_data, comp_data_str=comp_data_str)
                    filename = f"{save_path_txt}/{product_id}_{barcode}_{time_file}_{board_r}.txt"
                    xutil.FileUtil.write_content_to_file(filename, txt_content)

                if send_mes_mode in ["MoveStd", "txt+MoveStd"]:
                    self.log.info(f"等待中....")
                    time.sleep(int(time_sleep_2))
                    self.log.info(f"开始调用MoveStd接口....")
                    ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data_move_std, timeout=10)
                    ret_str = ret.get('string')
                    if not str(ret_str).startswith('0'):
                        ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret_str}")

            return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        if btn_vo.get_btn_key() == 'test_connect':
            is_connect = xrequest.SocketUtil.check_window_port(window_ip)
            if not is_connect:
                return self.x_response("false", f"连接window中转程序失败，请检查网络或者window中转程序是否打开！")

        return self.x_response()
