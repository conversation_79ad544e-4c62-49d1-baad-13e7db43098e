# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/15 下午15:04
# Author     ：gyr
# version    ：python 3.8
# Description：CETC43所
"""

from typing import Any

from common import xrequest
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


def x_request_device_status(
        tag,
        programname,
        ngrate,
        total,
        vehiclecode,
        ngsite,
        status_code_v3="1001"
):
    if not status_code_v3:
        status_code_v3 = "1001"

    # 根据状态码映射到MES状态
    if status_code_v3 in ["1001", "1002", "1003", "1004", "1005", "3002", "0001"]:
        mes_status = "1"  # 运行
    elif status_code_v3 in ["2001", "2002", "5001", "5002"]:
        mes_status = "4"  # 停机
    elif status_code_v3 in ["3001"]:
        mes_status = "2"  # 待机
    elif status_code_v3 in ["3003", "3004", "3005", "3006", "3007", "4001", "4002", "4003"]:
        mes_status = "3"  # 报警
    else:
        mes_status = "0"  # 未知状态

    alarm_list = []
    # 如果是报警状态,添加对应的报警码
    if mes_status == "3":
        alarm_list.append(status_code_v3)

    data = {
        "tag": tag,
        "status": mes_status,
        "alarmStart": alarm_list,
        "params": {
            "programName": programname,
            "ngRate": ngrate,
            "total": total,
            "vehicleCode": vehiclecode,
            "ngSite": ngsite
        }
    }

    return data


class Engine(ErrorMapEngine):
    version = {
        "title": "CETC_43 release v1.0.0.2",
        "device": "AIS43X,AIS63X",
        "feature": ["设备状态,上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-04-15 17:37  jira:36312,数据采集
date: 2025-04-18 16:30  bugfix:修改alarmStart
""", }
    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": " "
        },
        "tag": {
            "ui_name": "设备标签",
            "value": ""
        }
    }

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        发送设备状态
        """

        tag = other_vo.get_value_by_cons_key("tag")
        api_url = other_vo.get_value_by_cons_key("api_url")
        status_code_v3 = other_vo.get_status_code_v3()

        data = x_request_device_status(
            tag,
            "",
            "",
            "",
            "",
            "",
            status_code_v3
        )

        ret = xrequest.RequestUtil.post_json(api_url, data)
        if ret.get("code") != 200:
            code = ret.get('Code')
            message = ret.get('message')
            self.log.error(f"获取接口返回失败，code={code}, message={message}")
            return self.x_response("false", f"获取接口返回失败，code={code},message={message}") \

        return self.x_response()

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        发送mes
        """

        tag = data_dao.get_value_by_cons_key("tag")
        api_url = data_dao.get_value_by_cons_key("api_url")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)
        # 程序名
        program_name = pcb_entity.project_name
        vehicle_code = pcb_entity.fixture_barcode

        # 收集所有板卡的不良位置并计算不良率
        ng_sites = []
        ng_count = 0
        total_count = 0

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            total_count += 1

            if board_entity.is_repair_ng():
                ng_count += 1
                ng_sites.append(board_entity.board_no)

        ng_rate = ng_count / total_count if total_count > 0 else 0.0

        data = x_request_device_status(
            tag,
            program_name,
            ng_rate,
            total_count,
            vehicle_code,
            ng_sites
        )

        ret = xrequest.RequestUtil.post_json(api_url, data)
        if ret.get("code") != 200:
            code = ret.get('Code')
            message = ret.get('message')
            self.log.error(f"获取接口返回失败，code={code}, message={message}")
            return self.x_response("false", f"获取接口返回失败，code={code},message={message}")

        self.log.info("数据上传成功")

        return self.x_response()
