# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/other_param_setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ParamDialog(object):
    def setupUi(self, ParamDialog):
        ParamDialog.setObjectName("ParamDialog")
        ParamDialog.resize(449, 333)
        self.verticalLayout = QtWidgets.QVBoxLayout(ParamDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame = QtWidgets.QFrame(ParamDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setContentsMargins(0, 1, 1, 1)
        self.verticalLayout_2.setSpacing(1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.scrollArea = QtWidgets.QScrollArea(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scrollArea.sizePolicy().hasHeightForWidth())
        self.scrollArea.setSizePolicy(sizePolicy)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 428, 280))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.param_layout = QtWidgets.QFormLayout(self.scrollAreaWidgetContents)
        self.param_layout.setObjectName("param_layout")
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout_2.addWidget(self.scrollArea)
        self.verticalLayout.addWidget(self.frame)
        self.buttonBox = QtWidgets.QDialogButtonBox(ParamDialog)
        self.buttonBox.setOrientation(QtCore.Qt.Horizontal)
        self.buttonBox.setStandardButtons(QtWidgets.QDialogButtonBox.Cancel|QtWidgets.QDialogButtonBox.Ok)
        self.buttonBox.setObjectName("buttonBox")
        self.verticalLayout.addWidget(self.buttonBox)

        self.retranslateUi(ParamDialog)
        self.buttonBox.accepted.connect(ParamDialog.accept) # type: ignore
        self.buttonBox.rejected.connect(ParamDialog.reject) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(ParamDialog)

    def retranslateUi(self, ParamDialog):
        _translate = QtCore.QCoreApplication.translate
        ParamDialog.setWindowTitle(_translate("ParamDialog", "其他参数设置"))
