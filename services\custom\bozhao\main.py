# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/23 上午10:36
# Author     ：sch
# version    ：python 3.8
# Description：柏兆   吉安柏兆
"""

from typing import Any

from common import xcons, xutil
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

csv_template = """{project_name}
{barcode}
SPI
{test_time}
{result}
{username}
{line}
"""

txt_template = """{project_name}
{barcode}
AOI
0
TRI
0
{start_time}
{end_time}
{final_result}
{board_side}
{comp_total_number}
{comp_ng_number}{comp_data_str}
##
"""

txt_comp_template = """
{comp_tag};{comp_part};{robot_result};{repair_result};0"""


class Engine(ErrorMapEngine):
    version = {
        "title": "bozhao release v1.0.0.10",
        "device": "630,401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-23 14:55  上传数据到ftp服务器
date: 2023-12-12 15:09  修改文件名命名方式
date: 2023-12-18 15:39  增加两套ftp配置
date: 2024-02-27 14:52  增加生成txt文档，对应机型401
date: 2024-03-06 12:07  REPASS改成RPASS
date: 2024-03-08 18:44  REPASS改成RPASS  --> 改到txt
date: 2024-04-07 09:29  需求变更
date: 2024-04-17 17:32  使用gbk编码
date: 2025-02-07 17:08  jira->26706: 增加配置项：是否保存整板图到本地
date: 2025-05-16 15:11  打开不良代码映射功能
""",
    }

    form = {
        "username": {
            "ui_name": "人员工号",
            "value": ""
        },
        "line": {
            "ui_name": "线别",
            "value": ""
        },
    }

    combo = {
        "board_side_2": {
            "ui_name": "板面",
            "item": ["T", "B", "TB"],
            "value": "T"
        },
        "file_type": {
            "ui_name": "文档类型",
            "item": ["csv", "txt"],
            "value": "csv"
        },
        "is_save_local_img": {
            "ui_name": "是否保存整板图到本地",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
    }

    path = {
        "csv_path": {
            "ui_name": "文档本地保存路径",
            "value": ""
        },
        "image_path": {
            "ui_name": "整板图本地保存路径",
            "value": ""
        },
        "image_path_comp": {
            "ui_name": "NG器件图本地保存路径",
            "value": ""
        },
    }

    other_form = {
        "ftp_host_csv": {
            "ui_name": "FTP Host(file)",
            "value": "127.0.0.1"
        },
        "ftp_port_csv": {
            "ui_name": "FTP Port(file)",
            "value": "21"
        },
        "ftp_user_csv": {
            "ui_name": "FTP 账号(file)",
            "value": "sch"
        },
        "ftp_password_csv": {
            "ui_name": "FTP 密码(file)",
            "value": "123456"
        },
        "ftp_path_csv_csv": {
            "ui_name": "FTP 文档路径(file)",
            "value": "/MES/FILE"
        },

        "ftp_host_img": {
            "ui_name": "FTP Host(图片)",
            "value": "127.0.0.1"
        },
        "ftp_port_img": {
            "ui_name": "FTP Port(图片)",
            "value": "21"
        },
        "ftp_user_img": {
            "ui_name": "FTP 账号(图片)",
            "value": "sch"
        },
        "ftp_password_img": {
            "ui_name": "FTP 密码(图片)",
            "value": "123456"
        },
        "ftp_path_image_img": {
            "ui_name": "FTP 整图路径(图片)",
            "value": "/MES/IMG"
        },
    }

    password_style = [
        "ftp_password_csv",
        "ftp_password_img",
    ]

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        csv_path = data_vo.get_value_by_cons_key("csv_path")
        image_path = data_vo.get_value_by_cons_key("image_path")

        ftp_host_csv = data_vo.get_value_by_cons_key("ftp_host_csv")
        ftp_port_csv = data_vo.get_value_by_cons_key("ftp_port_csv")
        ftp_user_csv = data_vo.get_value_by_cons_key("ftp_user_csv")
        ftp_password_csv = data_vo.get_value_by_cons_key("ftp_password_csv")
        ftp_path_csv_csv = data_vo.get_value_by_cons_key("ftp_path_csv_csv")

        ftp_host_img = data_vo.get_value_by_cons_key("ftp_host_img")
        ftp_port_img = data_vo.get_value_by_cons_key("ftp_port_img")
        ftp_user_img = data_vo.get_value_by_cons_key("ftp_user_img")
        ftp_password_img = data_vo.get_value_by_cons_key("ftp_password_img")
        ftp_path_image_img = data_vo.get_value_by_cons_key("ftp_path_image_img")
        board_side_2 = data_vo.get_value_by_cons_key("board_side_2")
        image_path_comp = data_vo.get_value_by_cons_key("image_path_comp")

        file_type = data_vo.get_value_by_cons_key("file_type")

        username = data_vo.get_value_by_cons_key("username")
        line = data_vo.get_value_by_cons_key("line")

        is_save_local_img = data_vo.get_value_by_cons_key("is_save_local_img")

        print("locals", locals())

        try:
            ftp_port_csv = int(ftp_port_csv)
            ftp_port_img = int(ftp_port_img)
        except Exception as err:
            return self.x_response("false", f"FTP端口号必须为数字！error：{err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        d1 = pcb_entity.get_start_time()
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT1)
        time_file = d1.strftime(xcons.FMT_TIME_FILE)
        start_time = d1.strftime(xcons.FMT_TIME_DEFAULT1)

        date_file = time_file[:8]

        txt_comp_data = ""
        comp_repass_number = 0
        comp_total_number = 0
        comp_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_no = board_entity.board_no

            comp_repass_number += board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number
            comp_total_number += board_entity.comp_total_number
            comp_ng_number += board_entity.comp_repair_ng_number

            barcode_1 = board_entity.barcode

            if not barcode_1 and pcb_sn:
                barcode_1 = pcb_sn

            if not pcb_sn and board_entity.barcode:
                pcb_sn = board_entity.barcode

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                repair_ng_str = comp_entity.repair_ng_str
                if comp_entity.is_robot_ng():
                    txt_comp_data += txt_comp_template.format(**{
                        "comp_tag": comp_tag,
                        "board_no": board_no,
                        "comp_part": comp_entity.part,
                        "robot_result": comp_entity.robot_ng_str,
                        "repair_result": repair_ng_str,
                    })

                if comp_entity.is_repair_ng():
                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        dst_path = f"{image_path_comp}/AOI_{barcode_1}_1_{time_file}_{comp_tag}_1_{repair_ng_str}"
                        xutil.FileUtil.copy_file(comp_src_img, dst_path, is_auto_add_suffix=True)

        t_src_image = pcb_entity.get_pcb_t_image()

        pcb_sn_file = pcb_sn if pcb_sn else time_file

        if is_save_local_img == "Yes":
            # 1. 拷贝整板图
            if t_src_image:
                if pcb_sn:
                    t_dst_path = f"{image_path}/{date_file}"
                    t_dst_image = f"{t_dst_path}/{pcb_sn_file}.jpg"
                    xutil.FileUtil.ensure_dir_exist(t_dst_path)
                    xutil.FileUtil.copy_file(t_src_image, t_dst_image)

                else:
                    self.log.warning(f"没条码则按测试时间命名！")
                    t_dst_image = ""

                    t_dst_path = f"{image_path}/{date_file}"
                    t_dst_image = f"{t_dst_path}/{pcb_sn_file}.jpg"
                    xutil.FileUtil.ensure_dir_exist(t_dst_path)
                    xutil.FileUtil.copy_file(t_src_image, t_dst_image)
            else:
                self.log.warning(f"找不到需要上传的整板图！")
                t_dst_image = ""
        else:
            self.log.info(f"无需保存整板图到本地！")

        csv_content = csv_template.format(**{
            "project_name": pcb_entity.project_name,
            "barcode": pcb_sn,
            "test_time": start_time,
            "result": pcb_entity.get_final_result(),
            "username": username,
            "line": line
        })

        txt_content = txt_template.format(**{
            "project_name": pcb_entity.project_name,
            "barcode": pcb_sn,
            "start_time": start_time,
            "end_time": end_time,
            "final_result": pcb_entity.get_final_result("PASS", "RPASS", "FAIL"),
            "board_side": board_side_2,
            "comp_total_number": comp_total_number,
            "comp_repass_number": comp_repass_number,
            "comp_ng_number": comp_ng_number,
            "comp_data_str": txt_comp_data,
        })

        # 2. 生成csv
        if file_type == "csv":
            csv_dst_path = f"{csv_path}"
            csv_dst_file = f"{csv_dst_path}/{time_file[8:]}.csv"

            xutil.FileUtil.ensure_dir_exist(csv_dst_path)
            xutil.FileUtil.write_content_to_file(csv_dst_file, csv_content)
        else:
            dst_file = f"{csv_path}/AOI_{time_file}_{line}_{username}.txt"
            xutil.FileUtil.write_content_to_file(dst_file, txt_content, encoding='gbk')

        # 3. 上传图片到ftp
        if t_src_image:
            ftp_client_img = FTPClient(ftp_host_img, ftp_user_img, ftp_password_img, ftp_port_img)
            ftp_client_img.login()
            dst_ftp_path = f"{ftp_path_image_img}/{date_file}/{line}"
            ftp_client_img.cd_or_mkdir(dst_ftp_path)
            ftp_client_img.upload_file(t_src_image, f"{pcb_sn_file}.jpg")
            ftp_client_img.close()

        ftp_client_csv = FTPClient(ftp_host_csv, ftp_user_csv, ftp_password_csv, ftp_port_csv)
        ftp_client_csv.login()
        ftp_client_csv.cd_or_mkdir(ftp_path_csv_csv)

        if file_type == "csv":
            # 4. 上传csv到ftp
            ftp_client_csv.upload_content(f"{time_file}-{line}.csv", csv_content)
        else:
            ftp_client_csv.upload_content(f"AOI_{time_file}_{line}_{username}.txt", txt_content)

        ftp_client_csv.close()

        return self.x_response()
