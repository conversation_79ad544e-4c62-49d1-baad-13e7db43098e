# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : ah_module.py
# Time       ：2025/4/12 上午9:48
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from typing import Tuple

from common import xutil

barcode_relation_one_to_many_template = """<s:Envelope
    xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <BarcodeRelationOneToMany
            xmlns="http://tempuri.org/">
            <configdtstr>{configdtstr}</configdtstr>
            <serialNumber>{serialNumber}</serialNumber>
            <relationBarcodeNos>{board_sn_str}</relationBarcodeNos>
        </BarcodeRelationOneToMany>
    </s:Body>
</s:Envelope>"""

sn_row_template = """
            <relationBarcodeNo{ix}>{sn}</relationBarcodeNo{ix}>"""

check_and_write_template = """<s:Envelope
    xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CheckAndwriteResult xmlns="http://tempuri.org/">
            <configdtstr>{configdtstr}</configdtstr>
            <serialNumber>{serialNumber}</serialNumber>
            <Result>{Result}</Result>
            <allResultValues>{allResultValues}</allResultValues>
        </CheckAndwriteResult>
    </s:Body>
</s:Envelope>"""


# CheckRouteForSerialNumber 验证条码
check_route_template = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CheckRouteForSerialNumber xmlns="http://tempuri.org/">
            <configdtstr>{configdtstr}</configdtstr>
            <serialNumber>{serialNumber}</serialNumber>
        </CheckRouteForSerialNumber>
    </s:Body>
</s:Envelope>"""


# GetMultiboardList 一拖多查询
get_sn_template = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetMultiboardList xmlns="http://tempuri.org/">
            <configdtstr>{configdtstr}</configdtstr>
            <serialNumber>{serialNumber}</serialNumber>
            <checkWorkStation>{checkWorkStation}</checkWorkStation>
        </GetMultiboardList>
    </s:Body>
</s:Envelope>"""


# CheckAndwriteResultByAOI  AOI不良图片上传
check_and_write_result_template = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <h:Result xmlns:h="http://tempuri.org/">{Result}</h:Result>
        <h:allResultValues xmlns:h="http://tempuri.org/">{allResultValues}</h:allResultValues>
        <h:badness xmlns:h="http://tempuri.org/">{badness}</h:badness>
        <h:badnessSite xmlns:h="http://tempuri.org/">{badnessSite}</h:badnessSite>
        <h:configdtstr xmlns:h="http://tempuri.org/">{configdtstr}</h:configdtstr>
        <h:serialNumber xmlns:h="http://tempuri.org/">{serialNumber}</h:serialNumber>
    </s:Header>
    <s:Body>
        <CompositeType xmlns="http://tempuri.org/">
            <fileContent>{fileContent}</fileContent>
        </CompositeType>
    </s:Body>
</s:Envelope>
"""


def parse_ret(ret_str) -> Tuple[str, str]:
    root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    result_item = root[0][0][0]
    r1, r2 = result_item[0].text, result_item[1].text

    return r1, r2
