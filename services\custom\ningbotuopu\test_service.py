#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的MES测试服务端 - 按需求文档检查格式并打印结果
"""

import json
from flask import Flask, request, jsonify
from datetime import datetime

app = Flask(__name__)


def check_interlock_format(data):
    """检查InterLockCheck接口格式"""
    issues = []

    # 检查必填字段
    required_fields = ['SessionID', 'SerialNumber', 'WorkNodeID', 'ProgramName', 'SerialNumberType']
    for field in required_fields:
        if field not in data:
            issues.append(f"❌ 缺少必填字段: {field}")

    # 检查具体格式
    if 'SessionID' in data:
        if not data['SessionID']:
            issues.append(f"❌ SessionID格式错误，当前: {data['SessionID']}")

    if 'SerialNumber' in data:
        sn = data['SerialNumber']
        if isinstance(sn, str) and sn.startswith("['") and sn.endswith("']"):
            issues.append(f"❌ SerialNumber格式错误，不应该是列表字符串: {sn}")
        elif not sn:
            issues.append(f"❌ SerialNumber不能为空")

    if 'SerialNumberType' in data:
        sn_type = data['SerialNumberType']
        if sn_type not in ['0', '1', '2', '']:
            issues.append(f"❌ SerialNumberType应该是0(正面)、1(反面)、2(不分面)或空字符串，当前: {sn_type}")

    return issues


def check_upload_result_format(data):
    """检查UploadResult接口格式"""
    issues = []

    # 检查必填字段
    required_fields = ['SessionID', 'SerialNumber', 'WorkNodeID', 'StartTime', 'EndDate',
                       'ProgramName', 'TestResult', 'ReviseResult', 'TestDataDetails']
    for field in required_fields:
        if field not in data:
            issues.append(f"❌ 缺少必填字段: {field}")

    # 检查TestDataDetails
    if 'TestDataDetails' in data:
        if not isinstance(data['TestDataDetails'], list):
            issues.append(f"❌ TestDataDetails应该是数组")
        else:
            for i, detail in enumerate(data['TestDataDetails']):
                if not isinstance(detail, dict):
                    issues.append(f"❌ TestDataDetails[{i}]应该是对象")

    return issues


def check_machine_condition_format(data):
    """检查UploadMachineCondition接口格式"""
    issues = []

    # 检查必填字段
    required_fields = ['WorkNodeID', 'condition_code', 'condition_name', 'text', 'date_from']
    for field in required_fields:
        if field not in data:
            issues.append(f"❌ 缺少必填字段: {field}")

    return issues


def print_check_result(api_name, data, issues):
    """打印检查结果"""
    print(f"\n{'=' * 60}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"接口: {api_name}")
    print(f"{'=' * 60}")
    print("接收到的数据:")
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print(f"{'=' * 60}")

    if issues:
        print("❌ 格式检查发现问题:")
        for issue in issues:
            print(f"  {issue}")
    else:
        print("✅ 格式检查通过！")

    print(f"{'=' * 60}")


@app.route('/api/InterLockCheck', methods=['POST'])
def interlock_check():
    """流程检查接口"""
    data = request.get_json()
    issues = check_interlock_format(data)
    print_check_result("InterLockCheck", data, issues)

    return jsonify({
        "SessionID": data.get('SessionID', ''),
        "SerialNumber": data.get('SerialNumber', ''),
        "WorkNodeID": data.get('WorkNodeID', ''),
        "Result": True,
        "ErrorCode": "0",
        "Message": "route check success",
        "SNDetails": f"SN:{data.get('SerialNumber', '')} OK",
        "ProgramName": data.get('ProgramName', ''),
        "ProgramFile": ""
    })


@app.route('/api/UploadResult', methods=['POST'])
def upload_result():
    """上传结果接口"""
    data = request.get_json()
    issues = check_upload_result_format(data)
    print_check_result("UploadResult", data, issues)

    return jsonify({
        "SessionID": data.get('SessionID', ''),
        "SerialNumber": data.get('SerialNumber', ''),
        "WorkNodeID": data.get('WorkNodeID', ''),
        "Result": True,
        "ErrorCode": "0",
        "Message": "Upload Success"
    })


@app.route('/api/UploadMachineCondition', methods=['POST'])
def upload_machine_condition():
    """上传设备状态接口"""
    data = request.get_json()
    issues = check_machine_condition_format(data)
    print_check_result("UploadMachineCondition", data, issues)

    return jsonify({
        "valueReturn": "0",
        "errorMessage": "Upload Success"
    })


if __name__ == '__main__':
    print("启动MES测试服务器...")
    print("地址: http://127.0.0.1:50001")
    print("功能: 接收数据 -> 检查格式 -> 打印结果")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)

    app.run(host='127.0.0.1', port=50001, debug=False)