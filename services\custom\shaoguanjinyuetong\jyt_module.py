import os
import traceback

from common.xutil import log, XmlUtil, FileUtil


def get_project_create_user(project_name: str) -> str:
    """
    获取对应程序的制作员
    """
    log.info(f"正在获取: {project_name} 程序的制作员")
    author_base_path = os.path.expanduser(f"~/aoi/program/projects/{project_name}")
    author_xml_path = ""
    for root, dirs, files in os.walk(author_base_path):
        if "meta_editor.xml" in files:
            author_xml_path = os.path.join(root, "meta_editor.xml")
            log.info(f"找到 meta_editor.xml 文件：{author_xml_path}")
            break

    # 如果未找到文件，记录警告并退出
    if not author_xml_path:
        log.warning("未找到 meta_editor.xml 文件")
        pcb_program_developer = "unknown"
    else:
        author_xml_content = XmlUtil.get_xml_root_by_file(author_xml_path)
        pcb_program_developer = author_xml_content.find(".//data[variable='Author']/value").text  # 程序制作员
        if not pcb_program_developer:
            pcb_program_developer = "unknown"
            log.warning(f"读取程序制作员为空，赋默认值 unknown")

    log.info(f"获取到程序制作员 {pcb_program_developer}")
    return pcb_program_developer


def get_track_width_by_file(project_name: str) -> str:
    """
    获取轨道宽度 单位mm
    """
    base_path = os.path.expanduser(f"/home/<USER>/aoi/program/projects/{project_name}")

    track_width = "0.01"

    top_edit_xml = f"{base_path}/Top/meta_editor.xml"
    if os.path.exists(top_edit_xml):
        top_edit_xml_content = XmlUtil.get_xml_root_by_file(top_edit_xml)
        track_width_xml = top_edit_xml_content.find(".//data[variable='ConveryWidth']/value").text
        if track_width_xml:
            track_width = track_width_xml
    else:
        bottom_edit_xml = f"{base_path}/Bottom/meta_editor.xml"
        if os.path.exists(bottom_edit_xml):
            bottom_edit_xml_content = XmlUtil.get_xml_root_by_file(bottom_edit_xml)
            track_width_xml = bottom_edit_xml_content.find(".//data[variable='ConveryWidth']/value").text
            if track_width_xml:
                track_width = track_width_xml

    return track_width


def get_insp_data_by_track_index(track_index) -> dict:
    """
    获取轨道统计数据
    """
    home_dir = os.path.expanduser("~")
    mes_static_path = f"{home_dir}/aoi/MesStatistic"

    filepath = f"{mes_static_path}/lane{track_index}.json"

    if os.path.exists(filepath):
        try:
            ret_data = FileUtil.load_json_file(filepath)
            log.info(f"获取到 lane{track_index}.json 数据：{ret_data}")
        except Exception as err:
            log.warning(f"读取 lane{track_index}.json 文件失败：{err}")
            log.warning(traceback.format_exc())
            return {}

        return ret_data
    else:
        log.warning(f"未找到 /home/<USER>/aoi/lane{track_index}.json 文件")
        return {}


def track_is_exist(track_index: int) -> bool:
    """
    判断某个轨道是否存在统计数据

    track_index: （1，一轨） （2，二轨）

    return bool True: 存在 False: 不存在
    """
    home_dir = os.path.expanduser("~")
    mes_static_path = f"{home_dir}/aoi/MesStatistic"

    filepath = f"{mes_static_path}/lane{track_index}.json"

    is_exist = os.path.exists(filepath)

    if not is_exist:
        log.warning(f"未找到 /home/<USER>/aoi/lane{track_index}.json 文件")

    return is_exist


if __name__ == '__main__':
    # u = get_project_create_user("YGW2Sides.001")
    ret_width = get_track_width_by_file("jsftest.1017002")
    print("ret_width", ret_width)
