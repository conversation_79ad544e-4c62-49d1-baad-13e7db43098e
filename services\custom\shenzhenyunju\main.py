# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/2/28 上午9:25
# Author     ：sch
# version    ：python 3.8
# Description：深圳云聚  --- （安徽瑞德、乐图、中山宝利金类似）
"""
from typing import Any

from common import xrequest, xcons
from common.xutil import log, LimitedDict
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

global_data = {}
limit_global_data = LimitedDict(200)


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenyunju release v1.0.0.2",
        "device": "203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-02-28 09:26  登录，条码校验，上传数据
date: 2024-03-29 11:37  如果界面未填写工单，上传数据的工单从条码校验返回参数获取  
""", }

    form = {
        "order_id1": {
            "ui_name": "工单号",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
    }

    other_form = {
        "api_host": {
            "ui_name": "接口地址",
            "value": "http://127.0.0.1:8081"
        },
        "equipment_code": {
            "ui_name": "设备编码",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        equipment_code = data_vo.get_value_by_cons_key("equipment_code")
        order_id = data_vo.get_value_by_cons_key("order_id1")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        log.info(pcb_entity)

        start_datetime = pcb_entity.get_start_time()
        start_time = start_datetime.strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        token = global_data.get("token", "")

        if not token:
            return self.x_response("false", "未登录，请先登录！")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        board_data = []
        for board_entity in pcb_entity.yield_board_entity():
            log.logger.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not order_id:
                order_id = limit_global_data.get_value(barcode)
                log.info(f"界面未配置工单，工单将从缓存中获取，获取到的工单为：{order_id}")

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "test_type": comp_entity.type,
                    "test_item": comp_entity.designator,
                    "test_result": "ok" if comp_entity.robot_result else "ng",
                    "test_result_mes": comp_entity.get_final_result("ok", "pass", "ng"),
                    "defect_code": comp_entity.repair_ng_code,
                    "defect_name": comp_entity.repair_ng_str
                })

            board_data.append({
                "no": board_no,
                "sn_pcb_side": "",
                "sn": barcode,
                "sn_result": board_entity.get_final_result("ok", "pass", "ng"),
                "sn_remark": "",
                "sn_items": comp_data,
            })

        req_param = {
            "type": "TestReport",
            "equipment_code": equipment_code,
            "start_time": start_time,
            "end_time": end_time,
            "wo_no": order_id,
            "uni_code": f"{equipment_code}{start_datetime.strftime(xcons.FMT_TIME_FILE)}",
            "track": str(pcb_entity.track_index),
            "pcb_side": "",
            "operator": operator,
            "result": pcb_entity.get_final_result("ok", "pass", "ng"),
            "sn_list": board_data,
        }

        data_url = f"{api_host}/api/AssemblyOperation/TestReport"
        ret = xrequest.RequestUtil.post_json(data_url, req_param, headers=headers)
        if not ret.get("res"):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('reason')}")

        return self.x_response()

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        username, password = other_dao.get_login_info()

        api_host = other_dao.get_value_by_cons_key("api_host")
        login_url = f"{api_host}/api/Auth/GetToken"

        get_token_param = {
            "UserName": username,
            "Password": password
        }

        ret = xrequest.RequestUtil.post_json(login_url, get_token_param)
        error_info = ret.get("ErrorInfo")

        status = error_info.get("Status")
        log.info(f"status： {status}")

        if not status:
            # 登陆成功
            token = ret.get("Result").get("Token")
            global_data["token"] = token
            log.info(f"登录成功！")

        else:
            msg = error_info.get("Message")
            return self.x_response("false", f"mes接口异常，登录失败，error：{msg}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        equipment_code = other_vo.get_value_by_cons_key("equipment_code")
        order_id = other_vo.get_value_by_cons_key("order_id1")

        token = global_data.get("token")
        if not token:
            return self.x_response("false", "未登录，请先登录！")

        check_list = []

        sn_list = other_vo.list_sn()
        for ix, sn in enumerate(sn_list):
            check_list.append({
                "no": str(ix + 1),
                "equipment_code": equipment_code,
                "track": "",
                "wo_no": order_id,
                "sn_pcb_side": "",
                "sn": sn,
            })

        check_url = f"{api_host}/api/AssemblyOperation/CheckSn"
        check_param = {
            "type": "CheckSn",
            "checkSnlist": check_list
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        ret = xrequest.RequestUtil.post_json(check_url, check_param, headers=headers)

        status = ret.get("res")
        msg = ret.get("reason")

        if not status:
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{msg}")

        log.info(f"开始缓存wo_no号码，用于后续的数据上传...")

        ret_sn_list = ret.get('checkSnList', [])

        for item in ret_sn_list:
            ret_sn = item.get('sn', 'unknown')
            ret_wo_no = item.get('wo_no', 'unknown')
            limit_global_data.add_item(ret_sn, ret_wo_no)
            log.info(f"条码:{ret_sn} 工单:{ret_wo_no} 已缓存！")

        return self.x_response()
