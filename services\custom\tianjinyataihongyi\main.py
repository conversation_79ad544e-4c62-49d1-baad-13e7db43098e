"""
# File       : main.py
# Time       ：2025/05/30 11:09
# Author     ："wxc"
# version    ：python 3.8
# Description：天津亚太弘毅
"""
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

txt_board_template = """MainSN:{pcb_sn}
PanelSN:{board_barcode}
PanelID:{board_no}
ModelName:{model_name}
Side:{side}
MachineName:{machine_name}
CustomerName:{customer_name}
Operator:{operator}
Programer:{programer}
InspectionDate:{inspection_date}
BeginTime:{begin_time}
EndTime:{end_time}
CycleTimeSec:{cycle_time_sec}
InspectionBatch:{inspection_batch}
ReportResult:{report_result}
ConfirmedResult:{confirmed_result}
TotalComponent:{total_component}
ReportFailComponent:{report_fail_component}
ComfirmedFailComponent:{comfirmed_fail_component}


ComponentName,SN,SubBoardID,LibraryModel,P<PERSON>,Package,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Report<PERSON><PERSON>ult<PERSON><PERSON>,Con<PERSON><PERSON><PERSON><PERSON>,ConfirmedR<PERSON>ultCode
{comp_data}
"""
comp_data_template = """{comp_designation},{borad_barcode},{board_no},{comp_model},{comp_part},{comp_package},{comp_angle},{robot_result},{robot_ng_code},{repair_result},{repair_ng_code}
"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["天津亚太弘毅", "tianjinyataihongyi"],
        "version": "release v1.0.0.2",
        "device": "303 40X",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-30 11:11  ATAOI_2019-39926: 保存txt文件，保存ng器件图片
date: 2025-06-24 15:11  去掉文件列数pannel sn  
"""
    }
    path = {
        "txt_save_path": {
            "ui_name": "文本保存路径",
            "value": "",
        },
        "image_save_path": {
            "ui_name": "器件图片保存路径",
            "value": "",
        },
    }
    form = {
        "model_name": {
            "ui_name": "机种名称",
            "value": "",
        },
        "machine_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "customer_name": {
            "ui_name": "客户名称",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "programer": {
            "ui_name": "程序员",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        txt_save_path = data_vo.get_value_by_cons_key("txt_save_path", not_null=True)
        image_save_path = data_vo.get_value_by_cons_key("image_save_path", not_null=True)
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        customer_name = data_vo.get_value_by_cons_key("customer_name")
        operator = data_vo.get_value_by_cons_key("operator")
        programer = data_vo.get_value_by_cons_key("programer")
        model_name = data_vo.get_value_by_cons_key("model_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        side = pcb_entity.board_side
        inspection_date = pcb_entity.get_start_time().strftime(xcons.FMT_DATE)
        bengin_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME)
        time_file_date = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        time_file_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT13)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_barcode = board_entity.barcode
            board_no = board_entity.board_no
            # 如果拼板条码不存在，则使用主板条码输出
            if not board_barcode:
                board_barcode = pcb_sn

            comp_data_str = ""
            robot_ng_count = 0
            repair_ng_count = 0
            for comp in board_entity.yield_comp_entity():
                if not comp.robot_result:
                    robot_ng_count += 1
                if not comp.robot_result:
                    repair_ng_count += 1
                image_path = comp.image_path
                if comp.get_final_result() == "NG":
                    # 保存不良器件图（copy）
                    image_name = f"{comp.designator}_{board_no}_{board_barcode}.jpeg"
                    cp_image_path = f"{image_save_path}/{image_name}"
                    if xutil.FileUtil.file_is_exists(image_path):
                        xutil.FileUtil.copy_file(image_path, cp_image_path)
                    else:
                        self.log.warning(
                            f"拼板：{board_no},器件{image_path}不存在，无法保存到[{image_save_path}]")
                comp_data_str += comp_data_template.format(**{
                    "comp_designation": comp.designator,
                    "borad_barcode": board_barcode,
                    "board_no": board_no,
                    "comp_model": "",
                    "comp_part": comp.part,
                    "comp_package": comp.package,
                    "comp_angle": comp.geometry.angle,
                    "robot_result": comp.robot_ng_str,
                    "robot_ng_code": comp.robot_ng_code,
                    "repair_result": "OK" if comp.repair_result else "NG",
                    "repair_ng_code": comp.repair_ng_code
                })
            board_data = {
                "pcb_sn": pcb_sn,
                "board_barcode": board_barcode,
                "board_no": board_no,
                "model_name": model_name,
                "side": side,
                "machine_name": machine_name,
                "customer_name": customer_name,
                "operator": operator,
                "programer": programer,
                "inspection_date": inspection_date,
                "begin_time": bengin_time,
                "end_time": end_time,
                "cycle_time_sec": pcb_entity.get_cycle_time(),
                "inspection_batch": pcb_entity.order_id,
                "report_result": board_entity.get_robot_result("P", "F"),
                "confirmed_result": board_entity.get_repair_result("P", "F"),
                "total_component": board_entity.comp_total_number,
                "report_fail_component": robot_ng_count,
                "comfirmed_fail_component": repair_ng_count,
                "comp_data": comp_data_str
            }
            # 写入文件
            txt_file_name = f"{board_barcode}_{time_file_date}_{time_file_time}_{board_no}.txt"
            xutil.FileUtil.write_content_to_file_pro(f"{txt_save_path}/{txt_file_name}",
                                                     txt_board_template.format(**board_data))
        return self.x_response()
