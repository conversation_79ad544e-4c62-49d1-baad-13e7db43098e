# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/5/31 上午9:57
# Author     ：sch
# version    ：python 3.8
# Description：惠州金百泽
"""
import json
from datetime import datetime
from typing import Any

from common import xcons, xrequest, xutil
from common.xutil import log, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

global_data = {}

global_data_board_info = {}


def get_filename_by_api(api_url) -> str:
    """
    优先从本地获取文件名，获取不到，则从MES获取
    :return:
    """

    cache_filename_list = global_data.get("cache_filename_list", [])

    if cache_filename_list:

        item0 = cache_filename_list.pop(0)
        log.info(f"还剩余缓存文件名：{len(cache_filename_list)}")

        return item0
    else:
        log.warning(f"获取不到文件名，从MES获取文件名！")
        ret = xrequest.RequestUtil.post_json(api_url, {"Code": 321})

        if str(ret.get('status')) != '200':
            log.warning(f"获取文件名失败")
            return ""

        ret_filename = ret.get('message')
        file_list = ret_filename.split(",")

        item0 = file_list.pop(0)

        cache_filename_list.extend(file_list)

        log.info(f"还剩余缓存文件名：{len(cache_filename_list)}")
        global_data["cache_filename_list"] = cache_filename_list
        return item0


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoujinbaize release v1.0.0.6",
        "device": "501",
        "feature": ["设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-05-31 17:14  设备状态，上传数据
date: 2024-06-05 14:08  LotSN取主软件输入的批次号
date: 2024-06-20 15:34  上传图片到ftp时，尝试传三次，三次都失败就缓存到本地备份路径
date: 2024-12-24 17:37  新增需求  ---- (需配合主软件更新)
date: 2025-01-13 10:54  修改carry_out_time获取方法
date: 2025-02-28 10:46  jira:30430,无条码时pcb_sn传时间戳，board_sn传时间戳_拼板序号
""", }

    other_form = {
        "api_url_jbz_data": {
            "ui_name": "接口URL(测试数据上传)",
            "value": "",
        },
        "api_url_jbz_filename": {
            "ui_name": "接口URL(请求FTP文件名)",
            "value": "",
        },
        "api_url_jbz_oee": {
            "ui_name": "接口URL(OEE相关数据上传)",
            "value": "",
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/MES/SMT"
        },
    }

    form = {
        "mo_name": {
            "ui_name": "流程卡号",
            "value": "",
        },
        "sys_user": {
            "ui_name": "用户工号",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "group_id": {
            "ui_name": "线别",
            "value": "",
        },
    }

    path = {
        "bak_path": {
            "ui_name": "备份路径",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_jbz_data = data_vo.get_value_by_cons_key("api_url_jbz_data")
        api_url_jbz_filename = data_vo.get_value_by_cons_key("api_url_jbz_filename")
        mo_name = data_vo.get_value_by_cons_key("mo_name")
        sys_user = data_vo.get_value_by_cons_key("sys_user")
        device_code = data_vo.get_value_by_cons_key("device_code")
        device_name = data_vo.get_value_by_cons_key("device_name")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        bak_path = data_vo.get_value_by_cons_key("bak_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        pcb_sn = pcb_entity.pcb_barcode if pcb_entity.pcb_barcode else datetime.now().strftime('%Y%m%d%H%M%S')

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()
        file_y_month = pcb_entity.get_start_time().strftime(xcons.FMT_DATE0)
        ftp_client.cd_or_mkdir(f"{ftp_path}/{file_y_month}")

        bak_full_path = f"{bak_path}/{file_y_month}"

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no
            barcode = board_entity.barcode if board_entity.barcode else f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{board_no}"

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                comp_src_img = comp_entity.image_path

                if comp_src_img:
                    dst_filename = get_filename_by_api(api_url_jbz_filename)

                    try:
                        ftp_client.upload_file(comp_src_img, f"{dst_filename}.png")
                    except Exception as err:
                        log.warning(f"上传失败，第一次重传，err:{err}")

                        try:
                            ftp_client.upload_file(comp_src_img, f"{dst_filename}.png")
                        except Exception as err:
                            log.warning(f"上传失败，第二次重传，err:{err}")

                            try:
                                ftp_client.upload_file(comp_src_img, f"{dst_filename}.png")
                            except Exception as err:
                                log.warning(f"上传失败三次，数据缓存到本地，err:{err}")
                                xutil.FileUtil.ensure_dir_exist(bak_full_path)
                                xutil.FileUtil.copy_file(comp_src_img, f"{bak_full_path}/{dst_filename}.png")

                    full_dst_filepath = f"ftp://{ftp_host}{ftp_path}/{file_y_month}/{dst_filename}.png"
                else:
                    full_dst_filepath = ""

                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": full_dst_filepath
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        carry_in_time = pcb_entity.carry_in_time[:-4]
        carry_out_time = pcb_entity.carry_out_time[:-4]

        if not carry_out_time:
            carry_out_time_obj = pcb_entity.get_end_time()
            carry_out_time = carry_out_time_obj.strftime(xcons.FMT_TIME_DEFAULT)

            self.log.warning(f"未检测到出板时间，使用检测结束时间当成出板时间！")
        else:
            carry_out_time_obj = datetime.strptime(carry_out_time, xcons.FMT_TIME_DEFAULT)

        carry_in_time_obj = datetime.strptime(carry_in_time, xcons.FMT_TIME_DEFAULT)

        cycle_delta_obj = carry_out_time_obj - carry_in_time_obj
        cycle_time = cycle_delta_obj.seconds
        self.log.info(f"周期时间：{cycle_time}")
        board_count = pcb_entity.board_count

        if not cycle_time:
            cycle_time = pcb_entity.cycle_time
            self.log.warning(f"周期时间为CT时间！")

        pcb_data = {
            "Code": 320,
            "LotSN": pcb_entity.order_id,
            "MOName": mo_name,
            "SysUser": sys_user,
            "Device_code": device_code,
            "device_name": device_name,

            "fov_num": pcb_entity.fov_count,
            "pcb_total_testtime": str(cycle_time),
            "boardDetTime": str(round(cycle_time / board_count, 2)),
            "hourly_capacity": str(int(3600 / (cycle_time / board_count))),
            "pcb_num": board_count,
            "pcb_in_time": carry_in_time,
            "pcb_out_time": carry_out_time,

            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        ftp_client.close()

        ret = xrequest.RequestUtil.post_json(api_url_jbz_data, pcb_data)
        if str(ret.get('status')) != '200':
            ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

        return ret_res

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_jbz_oee = other_vo.get_value_by_cons_key("api_url_jbz_oee")
        device_code = other_vo.get_value_by_cons_key("device_code")
        device_name = other_vo.get_value_by_cons_key("device_name")
        group_id = other_vo.get_value_by_cons_key("group_id")

        old_status_code = other_vo.get_old_device_status_code()
        device_status_str = other_vo.get_device_status_str()

        if old_status_code in ["01", "02", "04"]:
            state = 0
            error_msg = ""
        elif old_status_code == "03":
            state = 1
            error_msg = ""
        elif old_status_code in ["10", "11", "20", "21", "12", "13", "22", "23", "99"]:
            state = 2
            error_msg = device_status_str

        else:
            log.warning(f"未知的状态码，不上传！")
            return self.x_response()

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT11)
        oee_param = {
            "Code": 322,
            "DeviceCode": device_code,
            "DeviceName": device_name,
            "DevGroup": group_id,
            "Time": time_now,
            "LabelItem": [
                {
                    "LabelCode": " BoardPassRate",
                    "LabelName": "板卡直通率",
                    "Value": global_data_board_info.get("board_pass_rate", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " BoardNGRate",
                    "LabelName": "板卡不良率",
                    "Value": global_data_board_info.get("board_ng_rate", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " BoardFCRate",
                    "LabelName": "板卡误报率",
                    "Value": global_data_board_info.get("board_fc_rate", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " BoardTotalCount",
                    "LabelName": "板卡检测总数量",
                    "Value": global_data_board_info.get("board_total_number", "0"),
                    "ValueType": "int",
                    "IsSuccess": True,
                    "Time": time_now
                },

                {
                    "LabelCode": " BoardPassCount",
                    "LabelName": "板卡检测良品数量",
                    "Value": global_data_board_info.get("board_ok_number", "0"),
                    "ValueType": "int",
                    "IsSuccess": True,
                    "Time": time_now
                },

                {
                    "LabelCode": " BoardNGCount",
                    "LabelName": "板卡检测不良数量",
                    "Value": global_data_board_info.get("board_ng_number", "0"),
                    "ValueType": "int",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " CompPassRate",
                    "LabelName": "元件直通率",
                    "Value": global_data_board_info.get("comp_pass_rate", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " CompNGRate",
                    "LabelName": "元件不良率",
                    "Value": global_data_board_info.get("comp_ng_rate", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " CompFCRate",
                    "LabelName": "元件误判率",
                    "Value": global_data_board_info.get("comp_fc_rate", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " CompTotalCount",
                    "LabelName": "元件检测总数量",
                    "Value": global_data_board_info.get("comp_total_number", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " CompPassCount",
                    "LabelName": "元件检测良品数量",
                    "Value": global_data_board_info.get("comp_ok_number", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " CompNGCount",
                    "LabelName": "元件检测不良数量",
                    "Value": global_data_board_info.get("comp_ng_number", "0.0"),
                    "ValueType": "Float",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " DeviceStatus",
                    "LabelName": "设备状态",
                    "Value": str(state),
                    "ValueType": "int",
                    "IsSuccess": True,
                    "Time": time_now
                },
                {
                    "LabelCode": " ErrorInfo",
                    "LabelName": "报警信息",
                    "Value": error_msg,
                    "ValueType": "str",
                    "IsSuccess": True,
                    "Time": time_now
                },
            ]
        }

        ret = xrequest.RequestUtil.post_json(api_url_jbz_oee, oee_param)

        if str(ret.get('status')) != '200':
            return self.x_response("false", f"mes接口异常，上传oee数据失败，error：{ret.get('message')}")

        return self.x_response()

    def send_board_data_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        board_data = other_vo.json_data.get("boardData", [])

        board_total_number = 0
        board_ok_number = 0
        board_ng_number = 0
        board_falsecall_number = 0

        comp_total_number = 0
        comp_ok_number = 0
        comp_ng_number = 0
        comp_falsecall_number = 0

        for item in board_data:
            board_total_number += int(item.get("boardTotalCount", 0))
            board_ok_number += int(item.get("boardOKCount", 0))
            board_ng_number += int(item.get("boardNGCount", 0))
            board_falsecall_number += int(item.get("boardFalseCallCount", 0))

            comp_total_number += int(item.get("compTotalCount", 0))
            comp_ok_number += int(item.get("compOKCount", 0))
            comp_ng_number += int(item.get("compNGCount", 0))
            comp_falsecall_number += int(item.get("compFalseCallCount", 0))

        if board_total_number:
            board_pass_rate = f"{round(board_ok_number / board_total_number * 100, 2)}"
            board_ng_rate = f"{round(board_ng_number / board_total_number * 100, 2)}"
            board_fc_rate = f"{round(board_falsecall_number / board_total_number * 100, 2)}"
        else:
            board_pass_rate = "0.0"
            board_ng_rate = "0.0"
            board_fc_rate = "0.0"

        if comp_total_number:
            comp_pass_rate = f"{round(comp_ok_number / comp_total_number * 100, 2)}"
            comp_ng_rate = f"{round(comp_ng_number / comp_total_number * 100, 2)}"
            comp_fc_rate = f"{round(comp_falsecall_number / comp_total_number * 100, 2)}"
        else:
            comp_pass_rate = "0.0"
            comp_ng_rate = "0.0"
            comp_fc_rate = "0.0"

        global_data_board_info["board_total_number"] = board_total_number
        global_data_board_info["board_ok_number"] = board_ok_number
        global_data_board_info["board_ng_number"] = board_ng_number
        global_data_board_info["board_falsecall_number"] = board_falsecall_number
        global_data_board_info["board_pass_rate"] = board_pass_rate
        global_data_board_info["board_ng_rate"] = board_ng_rate
        global_data_board_info["board_fc_rate"] = board_fc_rate
        global_data_board_info["comp_total_number"] = comp_total_number
        global_data_board_info["comp_ok_number"] = comp_ok_number
        global_data_board_info["comp_ng_number"] = comp_ng_number
        global_data_board_info["comp_falsecall_number"] = comp_falsecall_number
        global_data_board_info["comp_pass_rate"] = comp_pass_rate
        global_data_board_info["comp_ng_rate"] = comp_ng_rate
        global_data_board_info["comp_fc_rate"] = comp_fc_rate

        log.info(f"{json.dumps(global_data_board_info, indent=4)}")

        return self.x_response()

    def send_check_project_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        return self.x_response()
