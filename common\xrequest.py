# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xrequest.py
# Time       ：2023/4/4 下午5:17
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import socket
import struct
import time
import traceback
from typing import Any, Union, Optional
import requests
from minio import Minio
from minio.error import S3Error
from requests.auth import HTTPBasicAuth
import paho.mqtt.client as mqtt
from common import xutil, xconfig, xcons
from common.xutil import log, x_response


def time_cost(func):
    """
    计算函数消耗的装饰器
    :param func:
    :return:
    """

    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        log.info(f"{func.__name__}执行时间: {execution_time} 秒")
        return result

    return wrapper


class RequestUtil(object):
    """
    请求相关操作
    """

    @staticmethod
    def log_common_param(url, headers, params, body_data, log_number: int):
        """
        请求日志输出
        :param url:
        :param headers:
        :param params:
        :param body_data:
        :param log_number: 打印多少参数
        :return:
        """
        # if type(body_data) is str:
        #     body_param = body_data
        # else:
        #     body_param = json.dumps(body_data, ensure_ascii=False)
        if type(body_data) is dict:
            body_data = json.dumps(body_data, ensure_ascii=False)

        if body_data is not str:
            body_data = str(body_data)

        log.info(f"-->请求URL：{url}  请求体参数：\n{body_data[:log_number]}")
        if headers:
            log.info(f"-->请求头：\n{headers}")

        if params:
            log.info(f"-->请求参数：\n{params}")

    @staticmethod
    @time_cost
    def get(url, params, to_json=True, headers: dict = None, body_data=None,
            check_res_code=True, timeout=5) -> Any:
        """
        Get请求
        :param url: 请求API
        :param params: 请求参数
        :param to_json: 是否需要将返回参数转成 `python dict` 类型
        :param headers
        :param body_data
        :param check_res_code 检查状态码
        :param timeout
        :return:
        """
        # log.info(f"-->GET 请求URL：{url}  Params 参数如下：")
        # for k, v in params.items():
        #     log.info(f"KEY: {k}  VALUE: {v} ")
        log.info(f"----get------")

        param_list = []
        for k, v in params.items():
            # log.info(f"KEY: {k}  VALUE: {v} ")
            param_list.append(f"{k}={v}")

        if param_list:
            param_str = f"?{'&'.join(param_list)}"
        else:
            param_str = ""

        log.info(f"-->GET 请求URL：{url}{param_str}")

        if body_data:
            log.info(f"请求体Form参数：{body_data}")

        if headers:
            log.info(f"-->请求头：\n{headers}")

        res = requests.get(url, params, timeout=timeout, headers=headers, data=body_data)
        log.info(f"get res code: {res} msg: {res.text}")

        if check_res_code:
            if not res:
                # log.warning(f"get code: {res}  error: {res.text}")
                raise Exception("接口调用出错2002，请检查接口服务是否正常！")

        if to_json:
            return res.json()
        else:
            return res.text

    @classmethod
    @time_cost
    def post_json(cls,
                  url,
                  body_data,
                  headers: dict = None,
                  params: dict = None,
                  to_json=True,
                  log_number: int = 300000,
                  timeout=5,
                  auth=None,
                  files: dict = None,
                  check_res_code=True,
                  raw_response=False
                  ) -> Any:
        """
        post 接口，并默认返回json数据
        请求头：content-type: application/json
        :param url: 请求API
        :param headers: 请求头
        :param params: 请求参数
        :param body_data: 请求体参数
        :param to_json: 是否需要将返回参数转成 `python dict` 类型
        :param log_number: 打印多少参数
        :param auth: auth
        :param timeout: timeout
        :param check_res_code: 是否检查状态码
        :param raw_response: 是否返回原始的Response
        :param files
        :return:
        """
        # cls.log_common_param(url, headers, params, body_data, log_number)
        log.info(f"----post_json------")

        body_data_str = ""
        if type(body_data) in [dict, list]:
            body_data_str = json.dumps(body_data, ensure_ascii=False, indent=4)

        param_list = []

        if params:
            for k, v in params.items():
                param_list.append(f"{k}={v}")

        req_param = ""
        if param_list:
            req_param = f"?{'&'.join(param_list)}"

        if log_number:
            log.info(f"-->请求URL：{url}{req_param}  【Body Json】参数：\n{body_data_str[:log_number]}")

        if headers:
            log.info(f"-->请求头：\n{headers}")

        if auth:
            res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, auth=auth,
                                files=files)
        else:
            res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, files=files)

        log.info(f"post_json 响应参数 code: {res} msg: {res.text}")

        if check_res_code:
            if not res:
                # log.warning(f"post_json code: {res}  error: {res.text}")
                raise Exception("接口调用出错2003，请检查接口服务是否正常！")

        if to_json:
            return res.json()
        elif raw_response:
            return res
        else:
            return res.text

    @classmethod
    def post_json_with_retries(cls,
                               url,
                               body_data,
                               headers: dict = None,
                               params: dict = None,
                               to_json=True,
                               log_number: int = 300000,
                               timeout=5,
                               auth=None,
                               files: dict = None,
                               check_res_code=True,
                               max_retries: int = 1
                               ):
        retry_delay = 0.2
        for retry in range(max_retries + 1):
            log.info(f"retry={retry}")
            try:
                # 使用try-except块包装发送数据的操作
                ret = cls.post_json(url, body_data, headers, params, to_json, log_number, timeout,
                                    auth, files, check_res_code)
                log.info(f"数据发送成功（第{retry + 1}次尝试）")
                return ret
                # break  # 如果发送成功，则退出循环
            except Exception as err:
                if retry < max_retries:
                    # 如果还有重试机会，则等待一段时间后再试
                    time.sleep(retry_delay)
                    log.warning(f"第{retry + 1}次重发失败，错误信息：{err}")
                else:
                    # 如果达到最大重试次数，则记录最终错误并退出
                    # log_func(f"达到最大重试次数（{max_retries + 1}次），最终错误信息：{err}")
                    raise Exception(f"达到最大重试次数（{max_retries}次），最终错误信息：{err}")

    @classmethod
    @time_cost
    def post_form(cls,
                  url,
                  body_data: dict,
                  headers: dict = None,
                  params: dict = None,
                  to_json=True,
                  log_number: int = 300000,
                  timeout=5,
                  files: dict = None,
                  check_res_code=True,
                  raw_response=False
                  ) -> Any:
        """
        post 接口，并默认返回json数据
        请求头：content-type: application/x-www-form-urlencoded
        :param url: 请求API
        :param headers: 请求头
        :param params: 请求参数
        :param body_data: 请求体参数
        :param to_json: 是否需要将返回参数转成 `python dict` 类型
        :param log_number:
        :param timeout:
        :param files:
        :param check_res_code:
        :param raw_response：返回原始响应
        :return:
        """
        log.info(f"----post_form------")
        cls.log_common_param(url, headers, params, body_data, log_number)

        res = requests.post(url, data=body_data, headers=headers, params=params, timeout=timeout, files=files)
        log.info(f"post_form 响应参数 code: {res} msg: {res.text}")

        if check_res_code and not res:
            # log.warning(f"post_form code: {res} error: {res.text}")
            raise Exception("接口调用出错2004，请检查接口服务是否正常！")

        if to_json:
            return res.json()
        elif raw_response:
            return res
        else:
            return res.text

    @classmethod
    @time_cost
    def post_xml(cls, url, body_data: str, log_number=300000, headers=None, timeout: int = 5,
                 check_res_code=True):
        """
        post 接口
        请求头：content-type: application/xml
        :param url: 请求API
        :param body_data: 请求体
        :param log_number: 打印多少参数
        :param headers
        :param timeout
        :param check_res_code
        """
        log.info(f"----post_xml------")
        cls.log_common_param(url, {}, {}, body_data, log_number)

        h = {"Content-Type": "application/xml"}
        if headers:
            h.update(headers)

            log.info(f"请求头：{json.dumps(h, ensure_ascii=False)}")

        body_data = body_data.encode("utf-8")
        res = requests.post(url, data=body_data, headers=h, timeout=timeout)
        log.info(f"post_xml 响应参数 code:{res} msg:{res.text}")

        if check_res_code and not res:
            # log.warning(f"post_form code: {res} error: {res.text}")
            raise Exception("接口调用出错2004，请检查接口服务是否正常！")

        return res.text

    @classmethod
    @time_cost
    def post_soap(cls, url, body_data: str, soap_action, log_number=300000,
                  check_res_code=True):
        """
        post 接口
        请求头：content-type: text/xml; charset=utf-8
        """
        log.info(f"----post_soap------")
        headers = {
            "Content-Type": "text/xml; charset=utf-8",
            "Host": xutil.OtherUtil.match_host(url),
            "Content-Length": str(len(body_data)),
            "SOAPAction": soap_action
        }
        log.info(f"请求头：{headers}")

        if log_number:
            cls.log_common_param(url, {}, {}, body_data, log_number)

        body_data = body_data.encode("utf-8")
        res = requests.post(url, data=body_data, headers=headers, timeout=5)
        log.info(f"post_soap 响应参数 code:{res} msg:{res.text}")

        if check_res_code and not res:
            # log.warning(f"post_form code: {res} error: {res.text}")
            raise Exception("接口调用出错2004，请检查接口服务是否正常！")

        return res.text


class SocketUtil(object):

    @staticmethod
    @time_cost
    def check_window_port(socket_host: str, port: int = 9099) -> bool:
        """
        看window中转程序是否能连上
        :param socket_host:
        :param port:
        :return:
        """
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            return s.connect_ex((socket_host, port)) == 0

    @staticmethod
    @time_cost
    def x_socket_send_data(socket_host, packet_data: dict, timeout: int = 5):
        """
        发送数据给window中转站
        :param socket_host socket 主机
        :param packet_data 数据
        :param timeout
        包数据结构：  包头[12]+长度[8]+包类型[2]+数据[N]+包尾[10]
        :return:
        """
        packet_data = json.dumps(packet_data, ensure_ascii=False).encode('utf8')
        packet_len = struct.pack('!q', len(packet_data))

        packet_start = b'PACKET_START'
        packet_end = b'PACKET_END'
        packet_type = b'02'

        log.info(f">>>>>>>>>>>>>>>>>请求window地址: {socket_host}, 请求参数: {packet_data}")

        sk = socket.socket()
        sk.settimeout(timeout)

        sk.connect((socket_host, xconfig.window_dll_server_port))

        full_param = packet_start + packet_len + packet_type + packet_data + packet_end

        # print(">>>>>>>>>>>>>>>>> 开始发送socket数据 send data: --->", full_param)
        log.info("--------------------------------------ret data start--------------")
        sk.sendall(full_param)
        ret_start = sk.recv(12)
        log.info(f"ret_start: {ret_start}")
        ret_len = sk.recv(8)
        ret_type = sk.recv(2)
        log.info(f"ret_type: {ret_type}")
        data_len = struct.unpack("!q", ret_len)[0]
        log.info(f"data_len: {data_len}")
        ret_data = sk.recv(data_len)
        # log.info("ret_data: ", ret_data)
        ret_end = sk.recv(10)
        log.info(f"ret_end: {ret_end}")
        log.info("--------------------------------------ret data end--------------")

        ret = json.loads(ret_data.decode('utf8'))
        log.info(f"<<<<< window 响应参数: {ret}")

        # {'key': '1988', 'result': False, 'string': 'Post Failed.\n ret:Connection refused'} <class 'dict'>
        # print(f">>>> {ret} {type(ret)}")

        sk.close()
        return ret

    @classmethod
    def send_data_to_window_station(cls, socket_host, packet_data: dict, timeout: int = 5):
        """
        多起一个接口名称
        :param socket_host:
        :param packet_data:
        :param timeout:
        :return:
        """
        return cls.x_socket_send_data(socket_host, packet_data, timeout)

    @staticmethod
    @time_cost
    def send_data_to_socket_server(socket_ip: str, socket_port: int, param: str, timeout=5) -> str:
        """
        发送数据到socket服务端
        """
        log.info(f"请求地址: {socket_ip}:{socket_port} 请求参数:{param}")

        sk = socket.socket()
        sk.settimeout(timeout)
        sk.connect((socket_ip, socket_port))
        sk.sendall(param.encode('utf-8'))
        ret_str = sk.recv(1024)
        ret_str = ret_str.decode('utf-8')
        log.info(f"接口响应: {ret_str}")
        sk.close()
        return ret_str

    @staticmethod
    @time_cost
    def send_data_to_socket_server_v2(socket_ip: str, socket_port: int, param: str, timeout=5) -> str:
        """
        发送数据到socket服务端
        """
        log.info(f"tcp请求地址: {socket_ip}:{socket_port} 请求参数:{param}")

        sk = socket.socket()
        sk.settimeout(timeout)
        sk.connect((socket_ip, socket_port))
        sk.sendall(param.encode('utf-8'))

        ret_str = sk.recv(1024 * 8)
        ret_str = ret_str.decode('utf-8')
        log.info(f"接口响应: {ret_str}")
        log.info(f"len: {len(ret_str)}")
        sk.close()
        return ret_str

    @classmethod
    def send_data_with_retries(cls, tcp_host: str, tcp_port: int, param: str, max_retries: int = 3):
        """
        增加重发机制
        :param tcp_host:
        :param tcp_port:
        :param param:
        :param max_retries:
        :return:
        """
        retry_delay = 0.2
        for retry in range(max_retries + 1):
            log.info(f"retry={retry}")
            try:
                # 使用try-except块包装发送数据的操作
                ret = cls.send_data_to_socket_server(tcp_host, tcp_port, param)
                log.info(f"数据发送成功（第{retry + 1}次尝试）")
                return ret
                # break  # 如果发送成功，则退出循环
            except Exception as err:
                if retry < max_retries:
                    # 如果还有重试机会，则等待一段时间后再试
                    time.sleep(retry_delay)
                    log.warning(f"第{retry + 1}次重发失败，错误信息：{err}")
                else:
                    # 如果达到最大重试次数，则记录最终错误并退出
                    # log_func(f"达到最大重试次数（{max_retries + 1}次），最终错误信息：{err}")
                    raise Exception(f"达到最大重试次数（{max_retries}次），最终错误信息：{err}")

    @staticmethod
    @time_cost
    def send_bytes_to_socket_server(socket_ip: str, socket_port: int, param: bytes, timeout=5, is_log=True) -> str:
        """
        发送数据到socket服务端
        """
        sk = socket.socket()

        sk.settimeout(timeout)

        sk.connect((socket_ip, socket_port))
        if is_log:
            log.info(f"请求地址: {socket_ip}:{socket_port} 请求参数:{param}")

        sk.sendall(param)
        ret_str = sk.recv(1024)
        ret_str = ret_str.decode('utf-8')

        log.info(f"接口响应: {ret_str}")

        sk.close()
        return ret_str

    @classmethod
    @time_cost
    def api_check_project(
            cls,
            aoi_project_name: str,
            top_side_version: str = "",
            bot_side_version: str = "",
            auto_start: str = "0",
            lane: str = "0",
            timeout: int = 10,
    ):
        """
        自动切换板式

        参考资料：ATAOI_2019-31860     ---> 《手输工单号通过MES调程序功能设计文档-复杂功能.docx》
        MES配置器发送给主软件（json格式）：
        {
            "Request": {
                "Guid": "xxxxxxxx",  // 必填，唯一消息ID
                "ProgramName": "BBB.001",  // 必填，板式名（必须包含BOM）
                "ProtocolVersion": "V2",  // 必填，协议版本，此版本固定为V2
                "Lane": "0",  // 可选，轨道，字符串，“0”表示一轨，“1”表示二轨
                "TopSideVersion": "v001",  // 可选，顶面版本
                "BottomSideVersion": "v002",  // 可选，底面版本
                "AutoStart": "1"  // 可选，字符串，是否自动开始运行
            }
        }

        主软件返回给MES配置器（json格式）：
        {
            "Response": {
                "Guid": "xxxxxxxx",  // 唯一消息ID
                "Result": "1",  // 字符串，“1”则表示切换成功，“0”表示失败，旧协议，已经废弃
                "Message": "OK",  // 旧协议的错误信息，已经废弃
                "ProtocolVersion": "V2",  // 协议版本，此版本固定为V2
                "ErrorCodeV2": "0",  // 新协议的错误码（见下表），字符串类型
                "ErrorMessageV2": "no error"  // 新协议的错误信息
            }
        }

        :param aoi_project_name: 需要切换的板式名
        :param lane: 轨道 0表示一轨，1表示二轨
        :param top_side_version: 可选参数，顶面版本   默认打开最后修改的版本
        :param bot_side_version: 可选参数，底面版本   默认打开最后修改的版本
        :param auto_start: 1，表示自动切换完板式后自动运行，   0，表示自动切换完板式后不自动运行
        :param timeout: 切换板式超时时间
        :return:
        """
        data = json.dumps({
            "Request": {
                "Guid": xutil.OtherUtil.get_uuid4_str(),  # 接口请求标识（暂无意义）
                "ProgramName": aoi_project_name,  # 机台程序名

                # v2版本参数
                "ProtocolVersion": "V2",
                "Lane": lane,
                "TopSideVersion": top_side_version,
                "BottomSideVersion": bot_side_version,
                "AutoStart": auto_start,
            }
        }, ensure_ascii=False)

        ret_aoi = cls.send_data_to_socket_server("127.0.0.1", 9098, data, timeout=timeout)
        # log.info(f"ret aoi:{ret_aoi}")
        ret_aoi_json = json.loads(ret_aoi)
        ret_res = ret_aoi_json.get("Response", {})

        return ret_res

    @classmethod
    @time_cost
    def aoi_alert_alarm(cls, alert_info: str, need_stop_device: str = "1", timeout: str = 5):
        """
        发送停机报警给主软件

        注意：203P机型才有此接口

        :param alert_info: 错误信息，会显示在主软件上
        :param need_stop_device: 是否需要停机，0表示不停机（只弹窗），1表示停机，目前固定是1
        :param timeout:
        :return:
        """
        data = json.dumps({
            "Request": {
                "GUID": xutil.OtherUtil.get_origin_uuid4_str(),  # 唯一消息ID
                "Func": "ExternalErrorReport",  # 方法名，固定为ExternalErrorReport（外部错误报告）
                "Code": "1000",  # 错误类型，目前固定为1000（MES配置器错误）
                "Message": alert_info,  # 错误信息，会显示在主软件上
                "AutoStop": need_stop_device  # 是否需要停机，0表示不停机（只弹窗），1表示停机，目前固定是1
            }
        }, ensure_ascii=False)

        ret_aoi = cls.send_data_to_socket_server("127.0.0.1", 9087, data, timeout=timeout)
        # log.info(f"ret aoi:{ret_aoi}")
        ret_aoi_json = json.loads(ret_aoi)
        ret_res = ret_aoi_json.get("Response", {})

        return ret_res


def post_msg_to_repair(repair_ip: str, status: bool, msg: str):
    """
    发送结果给维修站
    :return:
    """
    time_now = xutil.DateUtil.get_datetime_now("%Y.%m.%d %H:%M:%S")

    if not repair_ip:
        log.warning(f"未配置维修站IP，本次不同步错误信息到维修站！")
        return

    repair_url = f"http://{repair_ip}:8196/mes"
    ret = {
        "key": time_now,
        "result": status,
        "string": msg
    }

    try:
        # http post , content-type=application/json
        RequestUtil.post_json(repair_url, ret, to_json=False)

    except Exception as e:
        log.warning(traceback.format_exc())
        log.warning(f"发送数据到维修站失败, error: {e}")


def close_mes_socket_server(mes_config_port=9090):
    """
    关闭mes的socket服务的线程
    """
    sk = socket.socket()

    sk.connect(("127.0.0.1", mes_config_port))
    sk.sendall(xcons.SOCKET_CLOSE_KEY)

    ret = sk.recv(1024)
    sk.close()

    # log.info(f"mes socket response: {ret}")


def send_backed_task(task_key: bytes):
    sk = socket.socket()

    sk.connect(("127.0.0.1", xconfig.backed_task_port))
    sk.sendall(task_key)

    sk.close()


def upload_data_to_minio_server(
        minio_server: str,
        access_key: str,
        secret_key: str,
        dst_bucket_name: str,
        src_filepath: str,
        dst_filename: str,
):
    t1 = time.time()
    log.info(f"开始上传文件到minio服务器，源文件路径：{src_filepath}"
             f"目标位置：bucket name:{dst_bucket_name} filename:{dst_filename}")
    try:
        # 创建MinIO客户端对象
        client = Minio(
            minio_server,
            access_key=access_key,
            secret_key=secret_key,
            secure=False
        )

        ret = client.fput_object(dst_bucket_name, dst_filename, src_filepath)
        log.info(f"上传成功，花费时间：{time.time() - t1}")

    except S3Error as err:
        log.error(f"Error occurred: {err=}")
    except Exception as err:
        log.error(f"上传失败，其他异常：error:{err}")


def send_device_start_or_stop(status_code: str, is_alert_info: str = '0', info: str = ''):
    """
    远程启动/停止设备

    注意：非标版本主软件才有：如东莞台达、科博达、安科讯

    协议：tcp/socket协议  主软件是服务端（发起者），mes配置器/客户的mes是客户端（调用者）
    HOST:127.0.0.1
    端口：9100
    请求参数：
    {
        "funcName": "controlMachineStatus",
        "funcArgs": status_code,  # 0是停机，1是启动
        "isAlertInfo": is_alert_info, # 0为不弹窗 1为主软件需要弹窗报警
        "info": "error" # 弹窗时的报警的描述信息
    }
    """
    # 固定的ip和端口
    host_ip = '127.0.0.1'
    port = 9100

    data_req = {
        "funcName": "controlMachineStatus",
        "funcArgs": status_code,
        "isAlertInfo": is_alert_info,
        "info": info
    }
    data_req = json.dumps(data_req)
    SocketUtil.send_data_to_socket_server(host_ip, port, data_req)


def send_error_info_to_repair(inner_func):
    """
    装饰器：分发不同轨道的报错数据到不同维修站

    需求场景：
    东莞群光使用两个维修站，目前MES报错信息只能反馈到一台维修站，两个轨道分别传不同的维修站，
    不同的人员需要看到当前轨道的报错

    jira： https://jira.cvte.com/browse/ATAOI_2019-31867
    """

    def wrapper(*args, **kwargs):
        data_dao = args[1]

        pcb_entity = data_dao.pcb_entity
        track_index = pcb_entity.track_index

        send_type_repair = data_dao.get_value_by_cons_key("send_type_repair")
        repair_ip1 = data_dao.get_value_by_cons_key("repair_ip1")
        repair_ip2 = data_dao.get_value_by_cons_key("repair_ip2")

        repair_user = pcb_entity.repair_user

        log.info(f"repair user: {repair_user}")

        # if track_index == 1:
        #     repair_ip = repair_ip1
        # else:
        #     repair_ip = repair_ip2

        if repair_user == "repair1":
            repair_ip = repair_ip1
        elif repair_user == "repair2":
            repair_ip = repair_ip2
        else:
            log.warning(f"未识别到复判用户[repair1, repair2]， 本次不同步错误信息到维修站！")
            repair_ip = ""

        try:
            result = inner_func(*args, **kwargs)
            if not result.get("result"):
                # 发送错误信息到维修站
                log.info(f"将同步错误消息到维修站：{repair_ip} track index: {track_index}")
                err_info = result.get("string")

                if track_index == 1:
                    if send_type_repair in ["1轨", "1轨+2轨"]:
                        post_msg_to_repair(repair_ip, False, err_info)

                else:
                    if send_type_repair in ["2轨", "1轨+2轨"]:
                        post_msg_to_repair(repair_ip, False, err_info)

            return result

        except Exception as err:
            log.warning(f"{traceback.format_exc()}")

            log.info(f"将同步错误消息到维修站：{repair_ip} track index: {track_index}")
            err_info = f"其他异常，上传数据失败，error：{err}"

            if track_index == 1:
                if send_type_repair in ["1轨", "1轨+2轨"]:
                    post_msg_to_repair(repair_ip, False, err_info)

            else:
                if send_type_repair in ["2轨", "1轨+2轨"]:
                    post_msg_to_repair(repair_ip, False, err_info)

            return x_response("false", err_info)

    return wrapper


def publish_mqtt_message(broker: str, port: int, client_id: str, topic: str, payload: Union[str, bytes],
                         username: Optional[str] = None, password: Optional[str] = None) -> tuple:
    """
    发布一条 MQTT 消息到指定 Broker 和 Topic。

    :param broker: MQTT Broker 地址
    :param port: MQTT 端口
    :param client_id: 客户端 ID
    :param topic: 主题
    :param payload: 消息内容
    :param username: 用户名
    :param password: 密码
    :return: tuple[bool, str] - 是否成功发送 + 错误信息（成功为空）
    """

    log.info(f"------发布MQTT消息------")
    log.info(f"broker：'{broker}' port:{port} client_id:'{client_id}' topic:'{topic}' payload:{payload}")

    client = mqtt.Client(client_id=client_id)
    if username and password:
        client.username_pw_set(username, password)

    # 连接回调函数
    def on_connect(client, userdata, flags, rc): # noqa
        if rc == 0:
            log.info("MQTT 连接成功")
        else:
            log.info(f"MQTT 连接失败，错误码: {rc}")

    # 断开连接回调
    def on_disconnect(client, userdata, rc): # noqa
        log.info("MQTT 已断开连接")

    # 绑定回调函数
    client.on_connect = on_connect
    client.on_disconnect = on_disconnect
    connected = False
    try:
        client.connect(broker, port=port)
        connected = True
        client.loop_start()
        result = client.publish(topic, payload)
        result.wait_for_publish()
        log.info("已发布消息到主题")
        return True, ""

    except Exception as e:
        err_msg = f"MQTT 发送失败: {e}"
        log.info(err_msg)
        return False, err_msg

    finally:
        client.loop_stop()
        if connected:
            client.disconnect()


if __name__ == '__main__':
    # auth1 = HTTPBasicAuth("admin", "public")
    # RequestUtil.post_json("http://127.0.0.1:8081/test_json3", {}, auth=auth1)
    ret = SocketUtil.api_check_project("333.999bak")
    # print(ret)

    # ret1 = SocketUtil.aoi_alert_alarm("Mes发送异常，error:xxxx")
    # print(ret1)
    # post_msg_to_repair("127.0.0.1", False, "mes异常！")
    # SocketUtil.send_data_to_socket_server("127.0.0.1", 8085, "123456")
