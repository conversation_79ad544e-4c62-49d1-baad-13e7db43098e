# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/5/27 上午10:09
# Author     ：sch
# version    ：python 3.8
# Description：雅达
"""
import json
import os
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

get_sn_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <OTO_GetIntSerialNo xmlns="http://eTraceOracleERP.org/">
      <PanelID>{pcb_sn}</PanelID>
      <CustModel>{cust_model}</CustModel>
    </OTO_GetIntSerialNo>
  </soap:Body>
</soap:Envelope>"""

# data_template = """<dsWIPTD>
#     <WIPTDHeader>
#         <IntSerial>{barcode}</IntSerial>
#         <Process>{process}</Process>
#         <Result>{status}</Result>
#         <OperatorName>{operator}</OperatorName>
#         <Tester>{device_name}</Tester>
#         <ProgramName>{program_name}</ProgramName>
#         <ProgramVersion>v1.0.0.1</ProgramVersion>
#         <IPSNo>{ips_no}</IPSNo>
#         <IPSVersion>{ips_version}</IPSVersion>
#         <Remark>{remark}</Remark>
#     </WIPTDHeader>{comp_data_str}
# </dsWIPTD>
# """
#
# comp_template = """
#     <WIPTDItem>
#         <Item>{ix}</Item>
#         <TestStep>{test_step}</TestStep>
#         <TestName>{test_name}</TestName>
#         <OutputName />
#         <InputCondition />
#         <OutputLoading />
#         <LowerLimit>{lower_limit}</LowerLimit>
#         <Result>{result}</Result>
#         <UpperLimit>{upper_limit}</UpperLimit>
#         <Unit />
#         <Status>{status}</Status>
#         <IPSReference />
#         <TestID>{ix}</TestID>
#     </WIPTDItem>"""

ini_template = """Path={path}
SN={sn}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "yada release v1.0.0.7",
        "device": "AIS303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-05-27 10:12  init
date: 2024-05-27 16:36  条码校验，上传数据
date: 2024-06-12 12:44  兼容MES返回
date: 2024-06-12 14:25  去除条码的空格
date: 2024-06-13 14:17  数据的参数格式修改为json
date: 2024-08-22 15:19  增加【上传FOV信息超时时间】配置项
date: 2024-09-18 16:01  bugfix: 无法打开mes配置器
""", }

    form = {
        "api_url_mes_get_sn": {
            "ui_name": "获取条码(Mes服务器)",
            "value": "http://127.0.0.1:8081/eTrace_OracleERP/eTraceOracleERP.asmx"
        },
        "api_url_mes_check": {
            "ui_name": "条码校验(Mes服务器)",
            "value": ""
        },
        "api_url_mes_data": {
            "ui_name": "上传数据(Mes服务器)",
            "value": ""
        },
        "cust_model_mes": {
            "ui_name": "CustModel(Mes服务器)",
            "value": ""
        },
        "ate_name_mes": {
            "ui_name": "装备编号(Mes服务器)",
            "value": ""
        },
        "process_code_mes": {
            "ui_name": "工序编码(Mes服务器)",
            "value": "AOI"
        },
        "site_code_mes": {
            "ui_name": "工位编码(Mes服务器)",
            "value": "MP1"
        },
        "username_mes": {
            "ui_name": "用户账号(Mes服务器)",
            "value": "MP1"
        },
        "device_name_mes": {
            "ui_name": "设备名称(Mes服务器)",
            "value": "ATEName"
        },
        "ips_no_mes": {
            "ui_name": "IPSNo(Mes服务器)",
            "value": ""
        },
        "ips_version": {
            "ui_name": "IPSVersion(Mes服务器)",
            "value": ""
        },
        "remark": {
            "ui_name": "Remark(Mes服务器)",
            "value": ""
        },
        "api_url_ai": {
            "ui_name": "接口URL(AI服务器)",
            "value": "http://127.0.0.1:1803"
        },
        "machine_id": {
            "ui_name": "线体(AI服务器)",
            "value": ""
        },
        "station": {
            "ui_name": "站点名称(AI服务器)",
            "value": "PreAOI",
        }
    }

    combo = {
        "get_mes_result": {
            "ui_name": "从AI获取复判结果",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "upload_data_to_ai": {
            "ui_name": "上传数据到AI服务器",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "api_timeout": {
            "ui_name": "获取AI结果超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "30", "60", "100", "300"],
            "value": "1",
        },
        "api_timeout_req": {
            "ui_name": "上传FOV信息超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "15", "20", "25", "30", "35", "60", "100", "300"],
            "value": "5",
        },
        "comp_ng_list1": {
            "ui_name": "上传检测结果范围(fov图)",
            "item": ["全部", "仅检测NG"],
            "value": "全部",
        },
        "reserved_str_mes": {
            "ui_name": "进出站类型(Mes服务器)",
            "item": ["EnterStation", "ExitStation"],
            "value": "EnterStation",
        },
    }

    path = {
        "save_path_comp": {
            "ui_name": "不良图",
            "value": "",
        },
        "save_path_pcb": {
            "ui_name": "整板图",
            "value": "",
        },
        "ini_file_path": {
            "ui_name": "ini文件",
            "value": "",
        },
    }

    def __init__(self):
        pass
        # self.log.info(f"正在删除缓存在本地的fov图...")
        #
        # f = '/home/<USER>/aoi/run/fovs'
        #
        # count = 0
        # for _dir in os.listdir(f):
        #     for _dir2 in os.listdir(f"{f}/{_dir}"):
        #         for _dir3 in os.listdir(f"{f}/{_dir}/{_dir2}"):
        #             fov_p = f"{f}/{_dir}/{_dir2}/{_dir3}/0"
        #             for _file in os.listdir(fov_p):
        #                 f_file = f"{fov_p}/{_file}"
        #                 if os.path.exists(f_file):
        #                     count += 1
        #                     os.remove(f_file)
        #                     self.log.info(f"{f_file} 已删除！")
        #
        # self.log.info(f"共删除fov图：{count}张")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # AI服务器参数
        api_url_ai = data_vo.get_value_by_cons_key("api_url_ai")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        station = data_vo.get_value_by_cons_key("station")
        upload_data_to_ai = data_vo.get_value_by_cons_key("upload_data_to_ai")
        comp_ng_list1 = data_vo.get_value_by_cons_key("comp_ng_list1")

        # mes
        api_url_mes_data = data_vo.get_value_by_cons_key("api_url_mes_data")
        process_code_mes = data_vo.get_value_by_cons_key("process_code_mes")
        device_name_mes = data_vo.get_value_by_cons_key("device_name_mes")
        username_mes = data_vo.get_value_by_cons_key("username_mes")
        ips_no_mes = data_vo.get_value_by_cons_key("ips_no_mes")
        ips_version = data_vo.get_value_by_cons_key("ips_version")
        remark = data_vo.get_value_by_cons_key("remark")

        save_path_comp = data_vo.get_value_by_cons_key("save_path_comp", not_null=True)
        save_path_pcb = data_vo.get_value_by_cons_key("save_path_pcb", not_null=True)
        ini_file_path = data_vo.get_value_by_cons_key("ini_file_path", not_null=True)

        api_timeout_req = data_vo.get_value_by_cons_key("api_timeout_req", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        inspect_type = other_data.get('inspect_type')  # (inspector,主软件检测完发送)  (repair,维修站复判后发送)
        pcb_sn = pcb_entity.pcb_barcode

        upstream_json_data = other_data.get("upstream_json_data")
        aoi_type = upstream_json_data.get("ReviewFlag", "0")  # 0是人工, 1是AI复判
        self.log.info(f"维修站传过来的复判标识为：{aoi_type}   (ps:没传则默认取0)")

        repair_comp_list_ai = []
        ret_res = self.x_response()

        comp_data_info = {}

        file_date = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            barcode = barcode.strip()

            if not barcode:
                barcode = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data = []
            comp_data_mes = []

            ix = 0

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                # ---------- ai复判需求
                comp_data_info[comp_entity.comp_id] = {
                    "board_no": board_entity.board_no,
                    "is_robot_ng": comp_entity.is_robot_ng()
                }
                # ---------- ai复判需求

                if comp_entity.is_robot_ng():
                    comp_data.append({
                        "name": comp_entity.designator,
                        "code": comp_entity.robot_ng_code,
                        "confirm": comp_entity.robot_ng_str,
                        "r1": "",
                        "r2": "",
                        "r3": "",
                        "Name": "",
                        "Package": "",
                        "Confirm": ""
                    })

                    repair_comp_list_ai.append({
                        "board": board_no,
                        "part_name": comp_entity.designator,
                        "part_code": comp_entity.part,
                        "result": comp_entity.get_final_result("P", "P", "F"),
                    })

                    for alg_entity in comp_entity.yield_alg_entity():
                        ix += 1

                        if alg_entity.result != '0' and comp_entity.robot_ng_code != "0":
                            # comp_data_str += comp_template.format(**{
                            #     "ix": ix,
                            #     "test_step": process_code_mes,
                            #     "test_name": comp_entity.robot_ng_str,
                            #     "lower_limit": alg_entity.min_threshold,
                            #     "upper_limit": alg_entity.max_threshold,
                            #     "result": alg_entity.test_val,
                            #     "status": comp_entity.get_final_result("PASS", "PASS", "FAIL")
                            # })
                            comp_data_mes.append({
                                "Item": str(ix),
                                "TestStep": process_code_mes,
                                "TestName": comp_entity.robot_ng_str,
                                "OutputName": "",
                                "InputCondition": "",
                                "OutputLoading": "",
                                "LowerLimit": alg_entity.min_threshold,
                                "Result": "0",
                                "UpperLimit": alg_entity.max_threshold,
                                "Unit": "NA",
                                "Status": comp_entity.get_final_result("PASS", "PASS", "FAIL"),
                                "IPSReference": "",
                                "TestID": str(ix)
                            })


                if inspect_type != 'inspector':
                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        img_path = f"{save_path_comp}/{file_date}/{barcode}"
                        xutil.FileUtil.ensure_dir_exist(img_path)

                        comp_dst_img = f"{img_path}/{barcode}_{comp_tag}.png"
                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

                        ini_content = ini_template.format(**{
                            "path": comp_dst_img,
                            "sn": barcode
                        })
                        xutil.FileUtil.write_content_to_file(f"{ini_file_path}/{barcode}_{comp_tag}.ini",
                                                             ini_content)

            if inspect_type != 'inspector':
                self.log.info(f"上传数据到Mes服务器...")

                # mes_data = data_template.format(**{
                #     "barcode": barcode,
                #     "process": process_code_mes,
                #     "status": board_entity.get_repair_result("PASS", "FAIL"),
                #     "operator": username_mes,
                #     "device_name": device_name_mes,
                #     "program_name": pcb_entity.project_name,
                #     "ips_no": ips_no_mes,
                #     "ips_version": ips_version,
                #     "remark": remark,
                #     "comp_data_str": comp_data_str
                # })

                mes_param = {
                    "IntSerial": barcode,
                    "Process": process_code_mes,
                    "Result": board_entity.get_repair_result("PASS", "FAIL"),
                    "OperatorName": username_mes,
                    "Tester": device_name_mes,
                    "ProgramName": pcb_entity.project_name,
                    "ProgramVersion": "v1.0.0.1",
                    "IPSNo": ips_no_mes,
                    "IPSVersion": ips_version,
                    "Remark": remark,
                    "DetailInfos": comp_data_mes
                }

                ret = xrequest.RequestUtil.post_json(api_url_mes_data, mes_param)

                if not ret.get("result"):
                    error_msg = ret.get("errorInfo")
                    if not error_msg:
                        error_msg = ret.get('msg')

                    ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{error_msg}")

        if inspect_type != 'inspector':
            pcb_src_img = pcb_entity.get_unknown_t_pcb_image()

            if pcb_src_img:
                pcb_path = f"{save_path_pcb}/{file_date}/{pcb_sn}"
                xutil.FileUtil.ensure_dir_exist(pcb_path)

                pcb_dst_path = f"{pcb_path}/{pcb_sn}.jpg"
                xutil.FileUtil.copy_file(pcb_src_img, pcb_dst_path)

                ini_content = ini_template.format(**{
                    "path": pcb_dst_path,
                    "sn": pcb_sn
                })
                xutil.FileUtil.write_content_to_file(f"{ini_file_path}/{pcb_sn}.ini", ini_content)

        if upload_data_to_ai == "Yes":
            if inspect_type == 'inspector':
                self.log.info(f"传输FOV信息给AI...")
                # self.log.info(f"comp info: {comp_data_info}")
                comp_image = pcb_entity.get_pcb_pcb_t_review_path()
                self.log.info(f"{comp_image=}")

                fov_path = comp_image.replace('results', 'fovs')

                fov_path = fov_path.replace("_NG", "")
                self.log.info(f"{fov_path=}")

                fov_json = f"{fov_path}/fovs.json"

                if not os.path.exists(fov_json):
                    return self.x_response("false", f"请打开主软件的保存fov图设置！")

                fov_data = xutil.FileUtil.load_json_file(fov_json)
                # self.log.info(f"fov json: {fov_data}")

                fov_list = fov_data.get('fov', [])
                self.log.info(f"将上传fov数据，fov数量：{len(fov_list)}")

                count = 0
                for item in fov_list:
                    fov_comp_list = item.get('comp', [])
                    fov_index = item.get('index')
                    filename = str(fov_index).rjust(4, '0')
                    fov_src_img = f"{fov_path}/0/{filename}.jpg"

                    if not os.path.exists(fov_src_img):
                        return self.x_response("未找到需要上传的fov图片，请主软件勾选上【保存FOV图像】设置！")

                    fov_data_1 = []
                    fov_result = True
                    for fov_comp in fov_comp_list:
                        comp_uuid = fov_comp.get('comp_uuid').replace('{', '').replace('}', '')
                        board_no_ = comp_data_info.get(comp_uuid).get('board_no', '1')

                        is_robot_ng = comp_data_info.get(comp_uuid).get('is_robot_ng')
                        if is_robot_ng:
                            fov_result = False

                        fov_data_1.append({
                            "board": board_no_,
                            "part_name": fov_comp.get('designator', ''),
                            "part_code": fov_comp.get('part', ''),
                            "a": fov_comp.get('angle', ''),
                            "x": fov_comp.get('x', ''),
                            "y": fov_comp.get('y', ''),
                            "w": fov_comp.get('width', ''),
                            "h": fov_comp.get('height', ''),
                        })

                    self.log.info(f"fov result: {fov_result}")

                    if (comp_ng_list1 == "仅检测NG" and not fov_result) or (comp_ng_list1 == "全部"):
                        self.log.info(f"正在上传fov图....")

                        aoi_param = {
                            "line": machine_id,
                            "station": station,
                            "name": pcb_entity.project_name,
                            "sn": pcb_sn,
                            "image_info": {
                                "image_step": str(fov_index),
                                "image_type": 1,
                                # "image_body": "",
                            },
                            "part_list": fov_data_1
                        }
                        aoi_url = f"{api_url_ai}/predict_data/"
                        self.log.info(f"请求url：{aoi_url} 请求参数：{json.dumps(aoi_param, indent=4)}")
                        self.log.info(f"图片已转成base64编码附加到[image_info:image_body]参数里，因参数过大，并没有打印！图片地址：{fov_src_img}")

                        aoi_param['image_info']['image_body'] = xutil.ImageUtil.file_to_base64_content(fov_src_img)
                        ret = xrequest.RequestUtil.post_json(aoi_url, aoi_param, log_number=0, timeout=api_timeout_req)
                        if str(ret.get('error_code')) != '0':
                            ret_res = self.x_response("false", f"AI接口异常，上传fov数据失败，error：{ret.get('error_describe')}")

                        count += 1

                    if os.path.exists(fov_src_img):
                        os.remove(fov_src_img)
                        self.log.info(f"fov图已删除！")

                self.log.info(f"成功上传fov图数量：{count}")

            else:
                repair_param = {
                    "line": machine_id,
                    "station": station,
                    "name": pcb_entity.project_name,
                    "sn": pcb_sn,
                    "op_result": repair_comp_list_ai
                }

                repair_url = f"{api_url_ai}/op_result/"
                ret = xrequest.RequestUtil.post_json(repair_url, repair_param)
                if str(ret.get('error_code')) != '0':
                    ret_res = self.x_response("false", f"AI接口异常，上传复判数据失败，error：{ret.get('error_describe')}")

        else:
            self.log.info(f"无需上传数据到AI服务器！")

        return ret_res

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_mes_get_sn = other_vo.get_value_by_cons_key("api_url_mes_get_sn")
        cust_model_mes = other_vo.get_value_by_cons_key("cust_model_mes")

        pcb_sn = other_vo.get_pcb_sn()
        pcb_sn = pcb_sn.strip()

        param = get_sn_template.format(**{
            "pcb_sn": pcb_sn,
            "cust_model": cust_model_mes
        })

        ret_str = xrequest.RequestUtil.post_soap(api_url_mes_get_sn, param,
                                                 "http://eTraceOracleERP.org/OTO_GetIntSerialNo")

        root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        ret_result = root[0][0][0].text

        return self.x_response("true", ret_result)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_mes_check = other_vo.get_value_by_cons_key("api_url_mes_check")
        ate_name_mes = other_vo.get_value_by_cons_key("ate_name_mes")
        process_code_mes = other_vo.get_value_by_cons_key("process_code_mes")
        site_code_mes = other_vo.get_value_by_cons_key("site_code_mes")
        username_mes = other_vo.get_value_by_cons_key("username_mes")
        reserved_str_mes = other_vo.get_value_by_cons_key("reserved_str_mes")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            sn = sn.strip()
            check_param = {
                "ateName": ate_name_mes,
                "barcode": sn,
                "processCode": process_code_mes,
                "siteCode": site_code_mes,
                "reservedStr": reserved_str_mes,
                "userName": username_mes
            }

            ret_check = xrequest.RequestUtil.post_json(api_url_mes_check, check_param)

            if not ret_check.get("result"):
                error_msg = ret_check.get("errorInfo")
                if not error_msg:
                    error_msg = ret_check.get('msg')

                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{error_msg}")

        return ret_res


if __name__ == '__main__':
    ret_111 = """<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><OTO_GetIntSerialNoResponse xmlns="http://eTraceOracleERP.org/"><OTO_GetIntSerialNoResult>&lt;NewDataSet&gt;
  &lt;Table&gt;
    &lt;InterialNo&gt;1&lt;/InterialNo&gt;
  &lt;/Table&gt;
&lt;/NewDataSet&gt;</OTO_GetIntSerialNoResult></OTO_GetIntSerialNoResponse></soap:Body></soap:Envelope>"""

    r1 = xutil.XmlUtil.get_xml_root_by_str(ret_111)
    ret_result111 = r1[0][0][0].text
    print(ret_result111)
