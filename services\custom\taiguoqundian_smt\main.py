# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/30 上午9:20
# Author     ：sch
# version    ：python 3.8
# Description：泰国群电SMT
"""
import time
from datetime import datetime, timed<PERSON>ta
from typing import Any

from common import xcons, xutil, xrequest, xenum
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

txt_template = """Program Name:{project_name}
Machine ID:{machine_id}
Lane:{track_index}
test time:{test_time}
PCB_ID:{pcb_sn}
Working Side:{board_side}
Cycle Time:{cycle_time}
Result by AOI:{robot_result}
Result by OP_Judgment:{repair_result}
Final Result:{final_result}
total NG Count:{total_ng_count}
NG count from AOI:{ng_count_from_aoi}
NG count from OP:{ng_count_from_op}
Result, PC<PERSON><PERSON>, PC<PERSON><PERSON>, Array No, Ref name, Part name, NG Type, By AOI, By OP{comp_data_str}
"""

comp_template = """
{comp_result},{barcode},{pcb_sn},{board_no},{comp_tag},{comp_part},{comp_ng_str},{robot_ng_result},{repair_ng_result}"""

global_data = {}

circular_list = xutil.CircularList(200)


def cache_maintain_info(review_path: str, cycle_time: int = 0):
    """
    缓存维修保养信息
    维护保养信息, 改成使用了多少天

    ----------统计信息----------
    ## 按天数统计
    相机   5年
    光源   3年
    镜头   5年
    轨道皮带   1年
    伺服电机   5年

    ## 按使用次数统计
    气缸   10万次
    电磁阀  10万次

    :param review_path: 缓存哪个数据包
    :param cycle_time: CT时间
    :return:
    """
    if review_path:
        if circular_list.is_exist_item(review_path):
            log.warning(f"review path: {review_path} 该数据包数据已统计保养信息！")
            return
        else:
            circular_list.add_item(review_path)
    else:
        log.warning(f"review path为空，本次缓存丢弃！")
        return

    # 记录CT时间和发送次数
    cache_data = xutil.CacheUtil.get_cache_data()

    total_cycle_time = cache_data.get("total_cycle_time", 0)
    total_qty = cache_data.get("total_qty", 0)

    cache_data["total_cycle_time"] = total_cycle_time + cycle_time
    cache_data["total_qty"] = total_qty + 1

    maintain_cylinder = cache_data.get("maintain_cylinder", 0)  # 气缸，10万次
    maintain_solenoid_valve = cache_data.get("maintain_solenoid_valve", 0)  # 电磁阀，10万次

    date_str = xutil.DateUtil.get_datetime_now(xcons.FMT_DATE)

    def append_date_str_to_cache_data(key_str: str):
        """
        不存在则添加到列表
        """
        old_list = cache_data.get(key_str, [])

        if date_str not in old_list:
            old_list.append(date_str)
            cache_data[key_str] = old_list

    append_date_str_to_cache_data("maintain_all_used_date")
    append_date_str_to_cache_data("maintain_camera")  # 相机
    append_date_str_to_cache_data("maintain_light_source")  # 光源
    append_date_str_to_cache_data("maintain_lens")  # 镜头
    append_date_str_to_cache_data("maintain_track_belt")  # 轨道
    append_date_str_to_cache_data("maintain_servo_motor")  # 伺服电机

    cache_data["maintain_cylinder"] = maintain_cylinder + 1
    cache_data["maintain_solenoid_valve"] = maintain_solenoid_valve + 1

    xutil.CacheUtil.save_cache_data(cache_data)
    log.info(
        f"缓存保养信息成功： total_qty: {cache_data['total_qty']}  total_cycle_time: {cache_data['total_cycle_time']}"
    )
    return cache_data


class Engine(ErrorMapEngine):
    version = {
        "title": "taiguoqundian_smt release v1.0.0.2",
        "device": "AIS401",
        "feature": ["设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-30 14:20  上传数据到MES
date: 2024-08-21 14:04  上传数据到TPMS+MES部分参数修改
""", }

    other_form = {
        "api_url_tpms": {
            "ui_name": "接口URL(TPMS)",
            "ui_name_en": "APIURL(TPMS)",
            "value": "",
        },
        "device_id_tpms": {
            "ui_name": "设备ID(TPMS)",
            "ui_name_en": "DeviceId(TPMS)",
            "value": "",
        },
        "device_name_tpms": {
            "ui_name": "设备名称(TPMS)",
            "ui_name_en": "DeviceName(TPMS)",
            "value": "",
        },
        "fixture_id_tpms": {
            "ui_name": "FixtureId(TPMS)",
            "ui_name_en": "FixtureId(TPMS)",
            "value": "",
        },
    }

    form = {
        "machine_id": {
            "ui_name": "工作站",
            "ui_name_ui": "MachineId",
            "value": "",
        },
        "operator_id_tpms": {
            "ui_name": "作业员工号(TPMS)",
            "ui_name_en": "OperatorId(TPMS)",
            "value": "",
        },
    }

    combo = {
        "tpms_switch": {
            "ui_name": "TPMS接口总开关",
            "ui_name_en": "IsUseTPMS",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "track_index_ui": {
            "ui_name": "轨道",
            "ui_name_ui": "TrackLane",
            "item": ["1", "2"],
            "value": "1",
        },
        "board_side_ui": {
            "ui_name": "面别",
            "ui_name_ui": "BoardSide",
            "item": ["BOT", "TOP", "BOT&TOP"],
            "value": "BOT",
        },
    }

    path = {
        "save_path_txt": {
            "ui_name": "保存路径",
            "ui_name_ui": "SavaPath",
            "value": "",
        },
    }

    other_combo = {
        "is_cron_connect_tpms": {
            "ui_name": "心跳检测开启(TPMS)",
            "ui_name_en": "HeartBeatSwitch(TPMS)",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "cron_second": {
            "ui_name": "心跳间隔/s(TPMS)",
            "ui_name_en": "HeartBeatInterval/s(TPMS)",
            "item": ["10", "20", "30", "60", "120", "300", "600", "1500", "3000", "6000", "12000", "24000"],
            "value": "60",
        },
        "in_board_fix": {
            "ui_name": "进板时间调整(秒)",
            "ui_name_en": "BoardInTimeFix(s)",
            "item": ["-10", "-8", "-6", "-4", "-3", "-2", "-1", "0", "+1", "+2", "+3", "+4", "+6", "+8", "+10"],
            "value": "0",
        },
        "out_board_fix": {
            "ui_name": "出板时间调整(秒)",
            "ui_name_en": "BoardOutTimeFix(s)",
            "item": ["-10", "-8", "-6", "-4", "-3", "-2", "-1", "0", "+1", "+2", "+3", "+4", "+6", "+8", "+10"],
            "value": "0",
        },
    }

    button = {
        "maintain_clear": {
            "ui_name": "保养",
            "ui_name_en": "MaintainClear",
        },
    }

    def __init__(self):
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def init_main_window(self, main_window, other_vo: OtherVo):
        other_vo = OtherVo({}, main_window.config_data)

        is_cron_connect_tpms = other_vo.get_value_by_cons_key("is_cron_connect_tpms")
        cron_second = other_vo.get_value_by_cons_key("cron_second")

        if is_cron_connect_tpms == "Yes":
            interval_cron = True
        else:
            interval_cron = False

        main_window.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        main_window.config_data["app_setting"]["custom_interval_time"] = int(cron_second)  # 3s
        self.log.info("init main window done!")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_txt = data_vo.get_value_by_cons_key("save_path_txt", not_null=True)
        track_index_ui = data_vo.get_value_by_cons_key("track_index_ui")
        board_side_ui = data_vo.get_value_by_cons_key("board_side_ui")
        machine_id = data_vo.get_value_by_cons_key("machine_id")

        api_url_tpms = data_vo.get_value_by_cons_key("api_url_tpms")
        device_id_tpms = data_vo.get_value_by_cons_key("device_id_tpms")
        operator_id_tpms = data_vo.get_value_by_cons_key("operator_id_tpms")
        device_name_tpms = data_vo.get_value_by_cons_key("device_name_tpms")
        fixture_id_tpms = data_vo.get_value_by_cons_key("fixture_id_tpms")
        tpms_switch = data_vo.get_value_by_cons_key("tpms_switch")

        inspect_type = other_data.get("inspect_type")
        review_path = other_data.get("review_path")

        cache_maintain_info(review_path)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_data_list = []

        start_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time1 = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        pcb_sn_file = pcb_sn

        total_ng_count = 0
        ng_count_from_aoi = 0

        comp_data_str = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn_file and barcode:
                pcb_sn_file = barcode

            total_ng_count += board_entity.comp_repair_ng_number
            ng_count_from_aoi += board_entity.comp_robot_ng_number

            dc_list = []
            lc_list = []
            fw_list = []
            img_list = []

            for comp_entity in board_entity.yield_comp_entity():

                comp_final_result = comp_entity.get_final_result()

                if comp_final_result == "PASS":
                    # 直通PASS
                    comp_ng_str = "OK"
                elif comp_final_result == "REPASS":
                    # 误报
                    comp_ng_str = comp_entity.robot_ng_str
                else:
                    # 真实NG
                    comp_ng_str = comp_entity.repair_ng_str

                comp_data_str += comp_template.format(**{
                    "comp_result": comp_entity.get_final_result("FALSE", "FALSE", "NG"),
                    "pcb_sn": pcb_sn,
                    "barcode": barcode,
                    "board_no": board_no,
                    "comp_tag": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_ng_str": comp_ng_str,
                    "robot_ng_result": comp_entity.get_final_result("PASS", "NG", "NG"),
                    "repair_ng_result": comp_entity.get_final_result("PASS", "PASS", "NG"),
                })

                if comp_entity.is_repair_ng():
                    dc_list.append(comp_entity.designator)
                    lc_list.append(comp_entity.repair_ng_code)
                    fw_list.append(comp_entity.repair_ng_str)
                    img_list.append(comp_entity.image_path)

            board_data_list.append({
                "sequence": int(board_no),
                "sn": barcode,
                "startTime": start_time1,
                "endTime": end_time1,
                "cycleTime": pcb_entity.get_cycle_time(),
                "skip": board_entity.get_final_result(False, False, False, True),
                "result": board_entity.get_repair_result(True, False),
                "defectCode": "",
                "message": "",
                "parameter": {
                    "DC": ",".join(dc_list),
                    "LC": ",".join(lc_list),
                    "FW": ",".join(fw_list),
                    "picture": ",".join(img_list),
                }
            })

        test_time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        pcb_content = txt_template.format(**{
            "project_name": pcb_entity.project_name,
            "machine_id": machine_id,
            "track_index": track_index_ui,
            "test_time": test_time_file,
            "pcb_sn": pcb_sn,
            "board_side": board_side_ui,
            "cycle_time": pcb_entity.get_cycle_time(),
            "robot_result": pcb_entity.get_robot_result("OK", "NG"),
            "repair_result": pcb_entity.get_repair_result("OK", "NG"),
            "final_result": pcb_entity.get_final_result("OK", "OK", "NG"),
            "total_ng_count": total_ng_count,
            "ng_count_from_aoi": ng_count_from_aoi,
            "ng_count_from_op": total_ng_count,
            "comp_data_str": comp_data_str,
        })

        filepath = f"{save_path_txt}/{pcb_sn_file}_{test_time_file}.txt"
        xutil.FileUtil.write_content_to_file(filepath, pcb_content)

        project_name = pcb_entity.project_name
        board_count = pcb_entity.board_count
        pcb_image_list = pcb_entity.list_all_pcb_image()

        tpms_param = {
            "deviceId": device_id_tpms,
            "fixtureId": fixture_id_tpms,
            "operatorId": operator_id_tpms,
            "data": {
                "sn": pcb_sn,
                "parameter": {
                    "DC": project_name,
                    "LC": device_name_tpms,
                    "FW": board_count,
                    "picture": ",".join(pcb_image_list),

                },
                "result": pcb_entity.get_repair_result(True, False),
                "detail": board_data_list
            }
        }

        error_msg_list = []
        if inspect_type == "repair":
            try:
                if tpms_switch == "Yes":
                    log.info(f"开始上传产品信息...")
                    data_url = f"{api_url_tpms}/api/dap/productionresult"
                    ret = xrequest.RequestUtil.post_json(data_url, tpms_param)
                    if not ret.get('result'):
                        err_msg = f"TPMS接口调用异常，上传数据失败，error：{ret.get('message')}"

                        if other_param.lang_is_en():
                            err_msg = f"TPMS API error，upload data failed，error：{ret.get('message')}"

                        error_msg_list.append(err_msg)
                else:
                    self.log.warning(f"未启用TPMS，不调用 /api/dap/productionresult 接口！")

            except Exception as err:
                err_msg = f"TPMS接口调用异常，上传数据失败，error：{err}"

                if other_param.lang_is_en():
                    err_msg = f"TPMS API error，upload data failed，error：{err}"

                error_msg_list.append(err_msg)

        else:
            cache_data = xutil.CacheUtil.get_cache_data()

            # 检测后发送！
            # 1. 从本地缓存的数据中获取 保养维护信息、直通率等信息、故障和时间
            maintain_camera = len(cache_data.get("maintain_camera", []))
            maintain_light_source = len(cache_data.get("maintain_light_source", []))
            maintain_lens = len(cache_data.get("maintain_lens", []))
            maintain_track_belt = len(cache_data.get("maintain_track_belt", []))
            maintain_servo_motor = len(cache_data.get("maintain_servo_motor", []))
            maintain_cylinder = cache_data.get("maintain_cylinder", 0)
            maintain_solenoid_valve = cache_data.get("maintain_solenoid_valve", 0)

            if tpms_switch == "Yes":
                log.info(f"开始上传设备工艺...")
                param_url = f"{api_url_tpms}/api/dap/parameter"
                dap_param = {
                    "deviceId": device_id_tpms,
                    "data": {
                        "sn": pcb_sn,
                        "maintain_camera": maintain_camera,
                        "maintain_light_source": maintain_light_source,
                        "maintain_lens": maintain_lens,
                        "maintain_track_belt": maintain_track_belt,
                        "maintain_servo_motor": maintain_servo_motor,
                        "maintain_cylinder": maintain_cylinder,
                        "maintain_solenoid_valve": maintain_solenoid_valve,
                    }
                }

                ret = xrequest.RequestUtil.post_json(param_url, dap_param)
                if not ret.get('result'):
                    err_msg = f"TPMS接口调用异常，上传设备信息失败，error：{ret.get('message')}"
                    if other_param.lang_is_en():
                        err_msg = f"TPMS API error，upload dap parameter failed，error：{ret.get('message')}"

                    error_msg_list.append(err_msg)
            else:
                self.log.warning(f"未启用TPMS，不调用 /api/dap/parameter 接口！")

        if error_msg_list:
            err_msg_str = "\n".join(error_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_msg_str}")

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_tpms = other_vo.get_value_by_cons_key("api_url_tpms")
        device_id_tpms = other_vo.get_value_by_cons_key("device_id_tpms")
        tpms_switch = other_vo.get_value_by_cons_key("tpms_switch")

        old_code = other_vo.get_old_device_status_code()

        if tpms_switch == "No":
            log.warning(f"TPMS接口总开关未打开，无需上传设备状态")
            return self.x_response()

        event_id = 0
        if old_code == "02":
            event_id = 3
        elif old_code == "03":
            event_id = 4
        elif old_code == "01":
            event_id = 1
        elif old_code == "04":
            event_id = 6
        elif old_code == "11":
            event_id = 17
        else:
            pass

        device_code_v3 = other_vo.get_status_code_v3()
        device_str_v3 = other_vo.get_status_desc_v3()

        if event_id == 0:
            if device_code_v3 == "1001":
                event_id = 1
            elif device_code_v3 == "1002":
                event_id = 3
            elif device_code_v3 == "1003":
                event_id = 4
            elif device_code_v3 == "1004":
                event_id = 6

        time_now = datetime.now()

        if event_id != 0:
            in_board_fix = other_vo.get_value_by_cons_key("in_board_fix")
            out_board_fix = other_vo.get_value_by_cons_key("out_board_fix")

            if device_code_v3 == "1001":
                fix_time = in_board_fix
            elif device_code_v3 == "1004":
                fix_time = out_board_fix
            else:
                fix_time = "0"

            log.info(f"fix time: {fix_time}")

            if "+" in fix_time:
                fix_int = int(fix_time.replace("+", ""))
                time_now = time_now + timedelta(seconds=fix_int)

            elif "-" in fix_time:
                fix_int = int(fix_time.replace("-", ""))
                time_now = time_now - timedelta(seconds=fix_int)

            time_now_str = time_now.strftime(xcons.FMT_TIME_DEFAULT)

            status_param = {
                "deviceId": device_id_tpms,
                "eventId": event_id,
                "timeStamp": time_now_str,
                "parameter": ""
            }
            log.info(f"开始上传设备状态...")
            status_url = f"{api_url_tpms}/api/dap/event"
            xrequest.RequestUtil.post_json(status_url, status_param)
        else:
            self.log.warning(f"其他设备状态，不调用DeviceEventNotice接口！")

        if device_code_v3 in ["1001", "1003", "1004", "1005", "3001", "3002", "0001", "3004", "4003"]:
            self.log.warning(f"该状态不上传数据到接口：DeviceAlarmNotice, status:{device_code_v3}")
            return self.x_response()
        elif device_code_v3 == '1002':
            # 开始
            last_status_code = global_data.get('last_status_code', None)
            last_status_str = global_data.get('last_status_str', None)
            if last_status_code:
                # 有出现过异常状态, 上报解除异常
                if last_status_code in ["2001", "2002", "3003", "3005", "3006", "3007", "4001",
                                        "4002", "5001", "5002"]:
                    # 影响生产的 解除异常
                    _type = 4
                else:
                    _type = 2

                alarm_param = {
                    "deviceId": device_id_tpms,
                    "type": _type,
                    "code": last_status_code,
                    "message": last_status_str,
                }
                log.info(f"开始上传设备报警[解除报警]...")
                alarm_url = f"{api_url_tpms}/api/dap/alarm"
                xrequest.RequestUtil.post_json(alarm_url, alarm_param)

                # 异常已解除，清空
                global_data['last_status_code'] = None
                global_data['last_status_str'] = None

        elif device_code_v3 in xcons.DEVICE_STATUS_V3:
            log.info(f"异常状态")
            # 异常
            if device_code_v3 in ["2001", "2002", "3003", "3005", "3006", "3007", "4001",
                                  "4002", "5001", "5002"]:
                _type = 3
            else:
                _type = 1

            alarm_param = {
                "deviceId": device_id_tpms,
                "type": _type,
                "code": device_code_v3,
                "message": device_str_v3,
            }
            log.info(f"开始上传设备报警[开始报警]...")

            alarm_url = f"{api_url_tpms}/api/dap/alarm"
            xrequest.RequestUtil.post_json(alarm_url, alarm_param)

            global_data['last_status_code'] = device_code_v3
            global_data['last_status_str'] = device_str_v3
        else:
            log.warning(f"未知的设备状态： {device_code_v3}，不处理！")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_tpms = other_vo.get_value_by_cons_key("api_url_tpms")
        device_id_tpms = other_vo.get_value_by_cons_key("device_id_tpms")
        tpms_switch = other_vo.get_value_by_cons_key("tpms_switch")

        body_param = {
            "deviceId": device_id_tpms,
            "timestamp": int(time.time() * 1000)
        }

        if tpms_switch == "Yes":
            heart_url = f"{api_url_tpms}/api/dap/heartbeat"
            log.info(f"开始上传设备心跳...")
            xrequest.RequestUtil.post_json(heart_url, body_param)
        else:
            self.log.warning(f"未启用TPMS，不调用 /api/dap/heartbeat 接口！")

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):

        btn_key = btn_vo.get_btn_key()

        if btn_key == "maintain_clear":
            cache_data = xutil.CacheUtil.get_cache_data()

            cache_data["total_qty"] = 0
            cache_data["total_cycle_time"] = 0
            cache_data["maintain_camera"] = []
            cache_data["maintain_light_source"] = []
            cache_data["maintain_lens"] = []
            cache_data["maintain_track_belt"] = []
            cache_data["maintain_servo_motor"] = []
            cache_data["maintain_cylinder"] = 0
            cache_data["maintain_solenoid_valve"] = 0

            xutil.CacheUtil.save_cache_data(cache_data)

        return self.x_response()
