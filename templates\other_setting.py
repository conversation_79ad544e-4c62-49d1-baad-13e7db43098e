# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/other_setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_OtherSettingDialog(object):
    def setupUi(self, OtherSettingDialog):
        OtherSettingDialog.setObjectName("OtherSettingDialog")
        OtherSettingDialog.resize(398, 328)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(OtherSettingDialog)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.setting_filter_repeat_send = QtWidgets.QCheckBox(OtherSettingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.setting_filter_repeat_send.sizePolicy().hasHeightForWidth())
        self.setting_filter_repeat_send.setSizePolicy(sizePolicy)
        self.setting_filter_repeat_send.setObjectName("setting_filter_repeat_send")
        self.verticalLayout.addWidget(self.setting_filter_repeat_send)
        self.setting_merge_send = QtWidgets.QCheckBox(OtherSettingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.setting_merge_send.sizePolicy().hasHeightForWidth())
        self.setting_merge_send.setSizePolicy(sizePolicy)
        self.setting_merge_send.setObjectName("setting_merge_send")
        self.verticalLayout.addWidget(self.setting_merge_send)
        self.setting_send_error_info = QtWidgets.QCheckBox(OtherSettingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.setting_send_error_info.sizePolicy().hasHeightForWidth())
        self.setting_send_error_info.setSizePolicy(sizePolicy)
        self.setting_send_error_info.setObjectName("setting_send_error_info")
        self.verticalLayout.addWidget(self.setting_send_error_info)
        self.setting_send_info_to_repair = QtWidgets.QCheckBox(OtherSettingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.setting_send_info_to_repair.sizePolicy().hasHeightForWidth())
        self.setting_send_info_to_repair.setSizePolicy(sizePolicy)
        self.setting_send_info_to_repair.setObjectName("setting_send_info_to_repair")
        self.verticalLayout.addWidget(self.setting_send_info_to_repair)
        self.setting_http_server_run = QtWidgets.QCheckBox(OtherSettingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.setting_http_server_run.sizePolicy().hasHeightForWidth())
        self.setting_http_server_run.setSizePolicy(sizePolicy)
        self.setting_http_server_run.setObjectName("setting_http_server_run")
        self.verticalLayout.addWidget(self.setting_http_server_run)
        self.frame_2 = QtWidgets.QFrame(OtherSettingDialog)
        self.frame_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_2.setContentsMargins(-1, 1, -1, 1)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_2 = QtWidgets.QLabel(self.frame_2)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_2.addWidget(self.label_2)
        self.setting_lang = QtWidgets.QComboBox(self.frame_2)
        self.setting_lang.setObjectName("setting_lang")
        self.setting_lang.addItem("")
        self.setting_lang.addItem("")
        self.horizontalLayout_2.addWidget(self.setting_lang)
        self.verticalLayout.addWidget(self.frame_2)
        self.frame = QtWidgets.QFrame(OtherSettingDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(self.frame)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.edit_repair_ip = QtWidgets.QLineEdit(self.frame)
        self.edit_repair_ip.setObjectName("edit_repair_ip")
        self.horizontalLayout.addWidget(self.edit_repair_ip)
        self.verticalLayout.addWidget(self.frame)
        self.verticalLayout_2.addLayout(self.verticalLayout)
        self.buttonBox = QtWidgets.QDialogButtonBox(OtherSettingDialog)
        self.buttonBox.setOrientation(QtCore.Qt.Horizontal)
        self.buttonBox.setStandardButtons(QtWidgets.QDialogButtonBox.Cancel|QtWidgets.QDialogButtonBox.Ok)
        self.buttonBox.setObjectName("buttonBox")
        self.verticalLayout_2.addWidget(self.buttonBox)

        self.retranslateUi(OtherSettingDialog)
        self.buttonBox.accepted.connect(OtherSettingDialog.accept) # type: ignore
        self.buttonBox.rejected.connect(OtherSettingDialog.reject) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(OtherSettingDialog)

    def retranslateUi(self, OtherSettingDialog):
        _translate = QtCore.QCoreApplication.translate
        OtherSettingDialog.setWindowTitle(_translate("OtherSettingDialog", "其他设置"))
        self.setting_filter_repeat_send.setText(_translate("OtherSettingDialog", "去除重复数据(发送mes)"))
        self.setting_merge_send.setText(_translate("OtherSettingDialog", "合并上下板面数据"))
        self.setting_send_error_info.setText(_translate("OtherSettingDialog", "异常弹窗提示"))
        self.setting_send_info_to_repair.setText(_translate("OtherSettingDialog", "发送结果给维修工站"))
        self.setting_http_server_run.setText(_translate("OtherSettingDialog", "启动http服务"))
        self.label_2.setText(_translate("OtherSettingDialog", "语言(修改后重启生效)"))
        self.setting_lang.setItemText(0, _translate("OtherSettingDialog", "ZH"))
        self.setting_lang.setItemText(1, _translate("OtherSettingDialog", "EN"))
        self.label.setText(_translate("OtherSettingDialog", "维修工站IP"))
