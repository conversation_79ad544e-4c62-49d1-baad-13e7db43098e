# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/07/17 下午15:31
# Author     ：gyr
# version    ：python 3.8
# Description：宁波拓普
"""
from datetime import datetime
from typing import Any

from common import xrequest, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "customer": ["宁波拓普", "ningbotuopu"],
        "version": "release v1.0.0.1",
        "device": "40x,43x,63x",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-07-17 16:37 jira:ATAOI_2019-41106,条码校验+发送设备状态+发送数据
""", }

    form = {
        "sn_api_url": {
            "ui_name": "SN接口地址",
            "value": "https://127.0.0.1:50001/api/InterLockCheck",
        },
        "mes_api_url": {
            "ui_name": "MES接口地址",
            "value": "https://127.0.0.1:50001/api/UploadResult",
        },
        "status_api_url": {
            "ui_name": "设备状态接口地址",
            "value": "https://127.0.0.1:50001/api/UploadMachineCondition",
        },
        "work_node_id": {
            "ui_name": "站点号",
            "value": ""
        },
        "text_info": {
            "ui_name": "机器状态备注信息",
            "value": ""
        },
        "serial_number_type": {
            "ui_name": "SerialNumberType",
            "value": ""
        },
        "mes_text_info": {
            "ui_name": "Remark备注信息",
            "value": ""
        },
        "test_ext_data": {
            "ui_name": "测试补充数据",
            "value": "null"
        },
        "work_node_type": {
            "ui_name": "WorkNodeType(站点类型)",
            "value": ""
        },
        "device_no": {
            "ui_name": "DeviceNo(测试设备名称)",
            "value": ""
        },
        "description": {
            "ui_name": "Description(说明)",
            "value": ""
        }
    }

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        status_api_url = other_vo.get_value_by_cons_key("status_api_url")
        work_node_id = other_vo.get_value_by_cons_key("work_node_id")
        text_info = other_vo.get_value_by_cons_key("text_info")

        device_code = other_vo.get_status_code_v3()
        device_status = other_vo.get_status_desc_v3()

        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        req_param = {
            "WorkNodeID": work_node_id,
            "condition_code": device_code,
            "condition_name": device_status,
            "text": text_info,
            "date_from": start_time
        }

        ret = xrequest.RequestUtil.post_json(status_api_url, req_param)

        if ret.get("valueReturn") == "0":
            return self.x_response()
        else:
            error = ret.get("errorMessage", "发送设备状态到MES失败")
            return self.x_response("false", error)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        sn_api_url = other_vo.get_value_by_cons_key("sn_api_url")
        work_node_id = other_vo.get_value_by_cons_key("work_node_id")
        serial_number_type = other_vo.get_value_by_cons_key("serial_number_type")

        serial_number = other_vo.get_pcb_sn()
        program_name = other_vo.get_project_name()

        session_id = xutil.OtherUtil.get_uuid4_str()

        req_param = {
            "SessionID": session_id,
            "SerialNumber": serial_number,
            "WorkNodeID": work_node_id,
            "ProgramName": program_name,
            "SerialNumberType": serial_number_type
        }

        ret = xrequest.RequestUtil.post_json(sn_api_url, req_param)

        if ret.get("Result"):
            return self.x_response()
        else:
            error = ret.get("Message", "条码校验失败")
            return self.x_response("false", error)

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        mes_api_url = data_dao.get_value_by_cons_key("mes_api_url")
        work_node_id = data_dao.get_value_by_cons_key("work_node_id")
        mes_text_info = data_dao.get_value_by_cons_key("mes_text_info")
        test_ext_data = data_dao.get_value_by_cons_key("test_ext_data")
        serial_number_type = data_dao.get_value_by_cons_key("serial_number_type")
        work_node_type = data_dao.get_value_by_cons_key("work_node_type")
        device_no = data_dao.get_value_by_cons_key("device_no")
        description = data_dao.get_value_by_cons_key("description")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)

        mes_uuid = xutil.OtherUtil.get_uuid4_str()

        start_time = pcb_entity.get_start_time()
        end_time = pcb_entity.get_end_time()

        if hasattr(start_time, 'strftime'):
            start_time = start_time.strftime('%Y-%m-%d %H:%M:%S.%f')
        if hasattr(end_time, 'strftime'):
            end_time = end_time.strftime('%Y-%m-%d %H:%M:%S.%f')

        project_name = pcb_entity.project_name

        test_data_details = []
        panel_x_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_final_result = board_entity.get_final_result()
            if board_final_result == "BadBoard":
                panel_x_list.append(str(board_entity.board_no))
                self.log.info(f"拼版{board_entity.board_no}最终结果为X，添加到PanelXList")

            for comp_entity in board_entity.yield_comp_entity():
                for alg_entity in comp_entity.yield_alg_entity():
                    test_item = {
                        "TestIndex": None,
                        "TestItem": alg_entity.test_name,
                        "MaxValue": alg_entity.max_threshold,
                        "MinValue": alg_entity.min_threshold,
                        "TestValue": alg_entity.test_val,
                        "unit": "V",  # 单位
                        "TestResult": comp_entity.robot_result,
                        "ReviseResult": comp_entity.repair_result,
                        "Location": f"{comp_entity.x_pos}_{comp_entity.y_pos}",
                        "PartNumber": comp_entity.part,
                        "ErrorCode": comp_entity.robot_ng_code,
                        "ReviseErrorCode": comp_entity.repair_ng_code,
                        "BlockID": board_entity.board_no,
                        "Remark": mes_text_info,
                        "testExtData": test_ext_data
                    }
                    test_data_details.append(test_item)

        req_param = {
            "SessionID": mes_uuid,
            "SerialNumber": pcb_entity.pcb_barcode,
            "SerialNumberType": serial_number_type,
            "WorkNodeID": work_node_id,
            "WorkNodeType": work_node_type,
            "DeviceNo": device_no,
            "StartTime": start_time,
            "EndDate": end_time,
            "ProgramName": project_name,
            "TestResult": pcb_entity.get_robot_result(),
            "ReviseResult": pcb_entity.get_repair_result(),  # 人工确认结果
            "Description": description,
            "Remark": mes_text_info,
            "User": pcb_entity.repair_user,
            "PartSNList": [],
            "PanelXList": panel_x_list,
            "TestDataDetails": test_data_details,
            "InsPanel": True
        }

        ret = xrequest.RequestUtil.post_json(mes_api_url, req_param)

        if ret.get("Result"):
            return self.x_response()
        else:
            error = ret.get("Message", "数据上传失败")
            return self.x_response("false", error)