# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : backed_task_service.py
# Time       ：2023/5/15 上午11:21
# Author     ：sch
# version    ：python 3.8
# Description：后台任务; 如：发送邮件
"""
import socket

from PyQt5.QtCore import QThread

from common import xconfig, xutil
from common.xutil import log
from services.route import engine


class TaskThread(QThread):

    def __init__(self, main_window):
        super(TaskThread, self).__init__()
        self.main_window = main_window
        self.sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        # TCP释放连接后实现端口的立即复用
        self.sk.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    def run(self) -> None:
        port = xconfig.backed_task_port
        log.info(f"backed task service running in port: {port}")

        self.sk.bind(("0.0.0.0", port))
        self.sk.listen()

        while True:
            conn, addr = self.sk.accept()
            data = conn.recv(4086)
            log.info(f"task type: {data}")

            if data == b'SEND_EMAIL':
                err_msg = ""

                try:
                    self.main_window.log_info("pymes.log发送中...")
                    ret = xutil.send_logfile_by_email(engine.get_release())
                    if ret:
                        self.main_window.log_info("pymes.log发送成功！")
                    else:
                        err_msg = "未找到日志文件"

                except socket.gaierror as net_err:
                    err_msg = "网络连接失败"

                except Exception as err:
                    err_msg = err

                if err_msg:
                    self.main_window.log_info(f"pymes.log发送失败！err:{err_msg}", False)

            # conn.send(b'Task Done')
            conn.close()
