#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2023/4/5
# Author: sunchangheng
# import json
#
# from common import xparse
#
# if __name__ == '__main__':
#     # 201
#     mes_data201 = "/Users/<USER>/Downloads/大华/20221126-043830_id2.1_NG"
#
#     # 301
#     mes_data301 = "/Users/<USER>/Downloads/大华/B_20230111054409473_1_NG"
#     d1, d2 = xparse.parse_mes_data(mes_data301)
#     print(json.dumps(d2, ensure_ascii=False))
#     print(json.dumps(d1, ensure_ascii=False))
#     print("done")
#     # print(d2)
