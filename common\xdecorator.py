# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xdecorator.py
# Time       ：2025/6/23 下午3:23
# Author     ：sch
# version    ：python 3.8
# Description：装饰器
"""
import os
from functools import wraps

from common import xrequest, xcons
from common.xutil import log, CircularList, x_response
from vo.mes_vo import DataVo

limit_list_data = CircularList(500)


def send_data_to_mes_custom1(inner_func):
    """
    装饰器：
    1. 整板PASS(直通)或者REPASS(误报)的板卡，只发一次
    2. 检测NG的板卡可以检测完(NG)发送一次，复判完(REPASS)再发一次
    """

    def wrapper(*args, **kwargs):
        log.info(f"----custom function 1----")
        data_vo: DataVo = args[1]
        other_data = args[2]

        origin_review_path = other_data.get("review_path", "")

        if ";" in origin_review_path:
            origin_review_path = origin_review_path.split(";")[0]

        basename_review_path = os.path.basename(origin_review_path)

        pcb_final_result = data_vo.pcb_entity.get_final_result()
        log.info(f"pcb final result: {pcb_final_result}")

        if limit_list_data.is_exist_item(basename_review_path):
            log.warning(f"PASS/REPASS BOARD HAD SEND!")
            return x_response("true", "Pass/Repass Data Had Send!")

        result = inner_func(*args, **kwargs)  # send_data_to_mes

        # PASS, REPASS 需要记录！
        if pcb_final_result in ["PASS", "REPASS"]:
            limit_list_data.add_item(basename_review_path)

        return result

    return wrapper


def send_data_to_mes_custom2(inner_func):
    """
    装饰器：
    1. 如果是检测PASS，检测完不发送Mes，等维修站触发PASS板发送Mes时，再发送Mes
    """

    def wrapper(*args, **kwargs):
        log.info(f"----custom function 2----")
        data_vo: DataVo = args[1]
        inspect_type = data_vo.get_inspect_type()

        pcb_final_result = data_vo.pcb_entity.get_final_result()
        log.info(f"pcb final result: {pcb_final_result}")

        if pcb_final_result == "PASS" and inspect_type == xcons.INSPECTOR:
            log.warning(f"pass board, but not repair!")
            return x_response("true", "发送成功")

        result = inner_func(*args, **kwargs)  # send_data_to_mes

        return result

    return wrapper


def send_data_to_mes_custom3(inner_func):
    """
    装饰器：
    1. 整板PASS(直通)只发一次Mes
    """

    def wrapper(*args, **kwargs):
        log.info(f"----custom function 3----")
        data_vo: DataVo = args[1]
        other_data = args[2]

        origin_review_path = other_data.get("review_path", "")

        if ";" in origin_review_path:
            origin_review_path = origin_review_path.split(";")[0]

        basename_review_path = os.path.basename(origin_review_path)

        pcb_final_result = data_vo.pcb_entity.get_final_result()
        log.info(f"pcb final result: {pcb_final_result}")

        if limit_list_data.is_exist_item(basename_review_path):
            log.warning(f"PASS BOARD HAD UPLOAD!")
            return x_response("true", "Pass BOARD HAD UPLOAD!")

        result = inner_func(*args, **kwargs)  # send_data_to_mes

        # PASS, REPASS 需要记录！
        if pcb_final_result in ["PASS"]:
            limit_list_data.add_item(basename_review_path)

        return result

    return wrapper


def alarm_info(func):
    """
    主软件弹窗报警 装饰器
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        # 执行被装饰函数

        try:
            result = func(*args, **kwargs)

            # 执行固定的业务操作
            if not result.get("result"):

                try:
                    err_msg = result.get('string')
                    xrequest.SocketUtil.aoi_alert_alarm(err_msg)  # 主软件接口
                except Exception as err:
                    err1 = f"发送弹窗报警失败，error：{err}"
                    return {
                        'result': False, 'string': err1
                    }

            # 返回被装饰函数的返回值
            return result
        except Exception as err:

            err_info = f"其他异常, err:{err}"

            try:
                xrequest.SocketUtil.aoi_alert_alarm(err_info)  # 主软件接口
            except Exception as err:
                err1 = f"发送弹窗报警失败，error：{err}"
                return {
                    'result': False, 'string': err1
                }

            return x_response("false", err_info)

    return wrapper
