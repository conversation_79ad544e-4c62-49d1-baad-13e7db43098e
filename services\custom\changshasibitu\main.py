# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/28 下午4:31
# Author     ：sch
# version    ：python 3.8
# Description：长沙斯比图
"""
import json
import logging
from typing import Any

from common import xutil, xcons, xrequest
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "changshasibitu release v1.0.0.1",
        "device": "630",
        "feature": ["发送设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-28 16:32  设备状态，上传数据
""", }

    form = {
        "tcp_host": {
            "ui_name": "TcpHost",
            "value": "",
        },
        "tcp_port": {
            "ui_name": "TcpPort",
            "value": "",
        },
        "device_sn": {
            "ui_name": "设备编号",
            "value": "",
        },
    }

    path = {
        "save_path_img": {
            "ui_name": "保存路径(整板图)",
        },
        "save_path_comp": {
            "ui_name": "保存路径(元件图)",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        tcp_host = data_vo.get_value_by_cons_key("tcp_host")
        tcp_port = data_vo.get_value_by_cons_key("tcp_port")
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img")
        save_path_comp = data_vo.get_value_by_cons_key("save_path_comp")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        project_name = pcb_entity.project_name

        try:
            tcp_port = int(tcp_port)
        except Exception as err:
            return self.x_response("false", f"TcpPort必须为数字！err:{err}")

        t_pcb_image = pcb_entity.get_pcb_t_image()

        if t_pcb_image:
            xutil.FileUtil.copy_file(t_pcb_image, f"{save_path_img}/{project_name}_{time_file}",
                                     is_auto_add_suffix=True)
        else:
            self.log.warning(f"未找到大图，不保存大图！")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_image = comp_entity.image_path
                comp_tag = comp_entity.designator

                if comp_image:
                    comp_dst_img = f"{save_path_comp}/{project_name}_{comp_tag}_{time_file}"
                    xutil.FileUtil.copy_file(comp_image, comp_dst_img, is_auto_add_suffix=True)
                else:
                    comp_dst_img = ""

                comp_json = comp_entity.to_standard_board_data_v1()
                comp_json["comp_image"] = comp_dst_img

                comp_data_list.append(comp_json)

            board_json = board_entity.to_standard_board_data_v1()
            board_json['comp_data'] = comp_data_list

            board_json_str = json.dumps(board_json, ensure_ascii=False, separators=(",", ':'))
            ret = xrequest.SocketUtil.send_data_to_socket_server(tcp_host, tcp_port, board_json_str)

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        tcp_host = other_vo.get_value_by_cons_key("tcp_host")
        tcp_port = other_vo.get_value_by_cons_key("tcp_port")

        try:
            tcp_port = int(tcp_port)
        except Exception as err:
            return self.x_response("false", f"TcpPort必须为数字！err:{err}")

        is_connect = xrequest.SocketUtil.check_window_port(tcp_host, tcp_port)
        if not is_connect:
            return self.x_response("false", f"连接mes失败，请检查网络/mes服务是否正常！")

        old_status_code = other_vo.get_old_device_status_code()
        old_status_str = other_vo.get_device_status_str()

        device_status_param = {
            "device_sn": device_sn,
            "status_code": old_status_code,
            "status_desc": old_status_str,
            "upload_time": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
        }

        device_status_param_str = json.dumps(device_status_param, ensure_ascii=False, separators=(",", ':'))
        ret = xrequest.SocketUtil.send_data_to_socket_server(tcp_host, tcp_port, device_status_param_str)

        return self.x_response()
