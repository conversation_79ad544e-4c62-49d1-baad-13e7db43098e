# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : http_server.py
# Time       ：2024/3/21 下午6:00
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
from http.server import HTTPServer, CGIHTTPRequestHandler

from PyQt5.QtCore import QThread

from common.xutil import log, CacheUtil
from services.route import engine


class MyRequestHandler(CGIHTTPRequestHandler):

    def do_GET(self) -> None:
        if self.path == '/ping':
            response = json.dumps({"code": 200, "message": "connect successful!"})

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(response.encode('utf-8'))
        else:
            response = json.dumps({"error": f"Content-Type Must be application/json!"})
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(response.encode('utf-8'))
            return

    def do_POST(self) -> None:
        path = self.path
        log.info(f"<<<<<<来自客户端的请求-----{path}------")

        if self.headers['Content-Type'] != 'application/json':
            response = json.dumps({"error": f"Content-Type Must be application/json!"})

            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(response.encode('utf-8'))
            return

        # 获取请求体内容长度
        content_length = int(self.headers['Content-Length'])

        # 读取请求体内容
        post_body = self.rfile.read(content_length)
        # 解析请求体中的JSON数据
        try:
            req_data = json.loads(post_body)
            log.info(f"请求参数: {post_body.decode('utf-8')}")
        except json.JSONDecodeError as e:
            # 处理JSON解析错误
            response = json.dumps({"error": f"Invalid JSON in request body, {e}"})

            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(response.encode('utf-8'))
            return

        if self.path == "/leichen/ruijie/update_data":
            self.ruijie_update_data(req_data)
        elif self.path == "/leichen/ruijie/device_status":
            self.ruijie_device_status(req_data)
        else:
            data = {
                "Result": "ERROR",
                "errorException": f"路由不匹配, 没有该条路由：{self.path}",
                "error": f"路由不匹配, 没有该条路由：{self.path}",
            }
            self.return_json(data)

    def return_json(self, ret_data: dict):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()

        ret_data_str = json.dumps(ret_data, ensure_ascii=False)
        log.info(f">>>>>>http响应参数：{ret_data_str}")
        self.wfile.write(ret_data_str.encode('utf-8'))

    def ruijie_update_data(self, req_data: dict):
        try:
            line_ui = engine.main_window.config_data.get('other_form', {}).get('line_code', {}).get('value')

            req_line = req_data.get('Line')

            if line_ui != req_line:
                ret_res = {
                    "Result": "NG",
                    "errorException": f"线体号[{req_line}]不匹配，期待Line={line_ui}",
                    "ErrorException": f"线体号[{req_line}]不匹配，期待Line={line_ui}",
                }
                return self.return_json(ret_res)

            order_id = req_data.get('MfgOrder')
            product_id = req_data.get('Product')

            ui_item1 = getattr(engine.main_window, 'form_order_id')
            ui_item2 = getattr(engine.main_window, 'form_product_id')

            ui_item1.setText(order_id)
            ui_item2.setText(product_id)

            engine.main_window.config_data['form']['order_id']['value'] = order_id
            engine.main_window.config_data['form']['product_id']['value'] = product_id
            engine.main_window.save_config_data_to_file()

            ret_res = {
                "Result": "OK",
                "errorException": f"更新工单和产品名称成功！",
                "ErrorException": f"更新工单和产品名称成功！",
            }
            return self.return_json(ret_res)
        except Exception as err:
            ret_res = {
                "Result": "ERROR",
                "ErrorException": f"其他异常：{err}",
            }
            return self.return_json(ret_res)

    def ruijie_device_status(self, req_data: dict):
        try:
            device_code_ui = engine.main_window.config_data.get('other_form', {}).get('device_code', {}).get('value')
            resource = req_data.get('Resource', '')

            if device_code_ui != resource:
                ret_res = {
                    "Result": "ERROR",
                    "ErrorException": f"设备号[{resource}]不匹配，期待Resource={device_code_ui}",
                    "State": "设备空闲",  # 设备运行，设备停止，设备空闲，设备故障
                    "OEE": "",
                    "AlarmList": [],
                    "FaultList": []
                }

                return self.return_json(ret_res)

            cache_data = CacheUtil.get_cache_data()
            device_status_current_state = cache_data.get('device_status_current_state')
            device_status_current_str = cache_data.get('device_status_current_str')

            alarm_list = []

            if device_status_current_str:
                alarm_list.append(device_status_current_str)

            ret_res = {
                "Result": "OK",
                "ErrorException": "",
                "State": device_status_current_state,  # 设备运行，设备停止，设备空闲，设备故障
                "OEE": CacheUtil.cal_oee(),
                "AlarmList": alarm_list,
                "FaultList": []
            }
            return self.return_json(ret_res)
        except Exception as err:
            return self.return_json({
                "Result": "ERROR",
                "errorException": f"其他异常：{err}",
                "ErrorException": f"其他异常：{err}",
            })


class HttpServerQThread(QThread):
    def run(self) -> None:
        port = 8082
        with HTTPServer(("0.0.0.0", port), MyRequestHandler) as httpd:
            log.info(f"http服务已启动 -----> http://127.0.0.1:{port}  test connect: http://127.0.0.1:{port}/ping")  # noqa
            log.info(f"AOI对外接口列表-------")
            log.info(f"http://<设备IP>:8082/leichen/ruijie/device_status")
            log.info(f"http://<设备IP>:8082/leichen/ruijie/update_data")
            log.info(f"AOI对外接口列表-------")
            httpd.serve_forever()
