# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : list_file.py
# Time       ：2024/12/2 上午11:03
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import boto3
from botocore.client import Config
from botocore.exceptions import ClientError

# endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
# aws_access_key_id_ = "enp6eA=="
# aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"

endpoint_url_ = "http://lxeds.sunwoda.com:12001"
aws_access_key_id_ = '4EQCFXZ4MTTYSSYM8BHE'
aws_secret_access_key_ = 'pYbgu0Kw7VV0G9T0RJHqlLygW9JE2dlpD1W4RTqT'

# 创建一个会话（如果你已经在环境变量中设置了凭证，这一步可以省略）
session = boto3.Session(
    aws_access_key_id=aws_access_key_id_,  # 替换为你的访问密钥
    aws_secret_access_key=aws_secret_access_key_  # 替换为你的秘密密钥
)

# 创建 S3 客户端，并指定自定义终端节点和其他配置选项
s3_client = session.client(
    's3',
    endpoint_url=endpoint_url_,  # 确保URL包含协议部分 (http:// 或 https://)
    # config=Config(
    #     s3={'addressing_style': 'path'},  # 使用路径样式 URL
    #     # signature_version='s3v4'  # 如果适用，可以指定签名版本
    # ),
    region_name=''  # 如果适用，可以指定区域名称
)

location = s3_client.get_bucket_location(Bucket="ddb")
print("桶位置：", location)


def list_bucket_objects(bucket_name, prefix=''):
    """列出指定存储桶中的所有对象"""
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(Bucket=bucket_name, Prefix=prefix)

        for page in page_iterator:
            if 'Contents' in page:
                for obj in page['Contents']:
                    print(f"Key: {obj['Key']}, Last Modified: {obj['LastModified']}, Size: {obj['Size']} bytes")
            else:
                print("No objects found.")
    except ClientError as e:
        print(f"Error listing objects in bucket '{bucket_name}': {e}")


# 示例调用
if __name__ == "__main__":

    bucket_name = 'ddb'
    prefix = ''  # 如果你只想列出特定前缀的对象，请在这里设置前缀，例如 'images/'

    bucket_key = "20241215/COMP1039_test2.png"

    print(f"Listing objects in bucket '{bucket_name}' with prefix '{prefix}'...")
    list_bucket_objects(bucket_name, prefix)

    response = s3_client.generate_presigned_url('get_object',
                                                Params={'Bucket': bucket_name,
                                                        'Key': bucket_key},
                                                ExpiresIn=100000000)
    print(f"临时访问链接：{response}")
