# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/5 下午3:30
# Author     ：sch
# version    ：python 3.8
# Description：麦博韦尔
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

pcb_template = """程序名,{pcb_project_name}
测试时间,{pcb_test_time}
操作员,{pcb_repair_user}
整板条码,{pcb_sn}
整板结果,{pcb_final_result}
拼板数量,{pcb_board_number}
拼板复判NG数量,{pcb_board_user_ng_number}
器件总数,{pcb_comp_number}
器件复判不良总数,{pcb_comp_user_ng_number}
生产线,{line}
设备名,{device_name}

BoardNo,Barcode,BoardFinalResult,CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult,value,CompImage{CompData}
"""

comp_template = """
{board_no},{board_sn},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_alg_str},{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "maiboerier release v1.0.0.1",
        "device": "203,501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-05 16:19  生成本地csv文档
""", }

    form = {
        "line": {
            "ui_name": "生产线",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名",
            "value": "",
        }
    }

    path = {
        "save_path": {
            "ui_name": "文件保存路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        line = data_vo.get_value_by_cons_key("line")
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path = data_vo.get_value_by_cons_key("save_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if not save_path:
            return self.x_response("false", f"请先选择文件保存路径！")

        pcb_board_user_ng_number = 0
        pcb_comp_number = 0
        pcb_comp_user_ng_number = 0

        comp_data_str = ""

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not board_entity.repair_result:
                pcb_board_user_ng_number += 1

            pcb_comp_number += board_entity.comp_total_number
            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number

            for comp_entity in board_entity.yield_comp_entity():

                alg_str_list = []

                for alg in comp_entity.yield_alg_entity():
                    alg_str_list.append(f"{alg.test_name}:{alg.max_threshold}/{alg.min_threshold}/{alg.test_val}")

                comp_data_str += comp_template.format(**{
                    "board_no": board_no,
                    "board_sn": barcode,
                    "board_final_result": board_entity.get_final_result(),
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_alg_str": ";".join(alg_str_list),
                    "comp_image": comp_entity.image_path,
                })

        pcb_content = pcb_template.format(**{
            "pcb_project_name": pcb_entity.project_name,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_sn": pcb_entity.pcb_barcode,
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_comp_number": pcb_comp_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "line": line,
            "device_name": device_name,
            "CompData": comp_data_str
        })

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        filepath = f"{save_path}/{pcb_sn}_{start_time}.csv"
        xutil.FileUtil.write_content_to_file(filepath, pcb_content)

        return self.x_response()
