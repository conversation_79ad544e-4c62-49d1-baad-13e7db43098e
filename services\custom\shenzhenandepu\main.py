# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/5/27 下午5:01
# Author     ：sch
# version    ：python 3.8
# Description：深圳安德普，在九江恒通的基础上增加从MES获取条码
"""

from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenandepu release v1.0.0.3",
        "device": "203,303",
        "feature": ["获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-05-27 17:03  init
date: 2024-05-30 09:55  获取条码，条码校验，上传数据，上传图片
""", }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "",
        },
        "api_url_check_sn": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
        "api_url_img": {
            "ui_name": "接口URL(上传图片)",
            "value": "",
        },
    }

    combo = {
        "is_upload_img": {
            "ui_name": "是否上传图片",
            "item": ["Yes", "No"],
            "value": "",
        },
        "bok_ui": {
            "ui_name": "BOK",
            "item": ["0", "1"],
            "value": "0",
        },
    }

    form = {
        "process_id": {
            "ui_name": "制程代号",
            "value": "",
        },
        "username": {
            "ui_name": "作业员工号",
            "value": "",
        },
        "line": {
            "ui_name": "生产线体",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
        "device_id": {
            "ui_name": "生产设备号",
            "value": "",
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        process_id = other_vo.get_value_by_cons_key("process_id")
        username = other_vo.get_value_by_cons_key("username")
        line = other_vo.get_value_by_cons_key("line")
        order_id = other_vo.get_value_by_cons_key("order_id")
        device_id = other_vo.get_value_by_cons_key("device_id")

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "zcno": process_id,
            "op": username,
            "prtno": pcb_sn,
            "lnno": line,
            "slkid": order_id,
            "sbid": device_id
        }

        ret2 = xrequest.RequestUtil.post_json(api_url_get_sn, param)

        if str(ret2.get('id')) != '0':
            return self.x_response("false", f"mes接口异常，从MES获取条码失败，error：{ret2.get('message')}")

        return self.x_response("true", ",".join(ret2.get('data', [])))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check_sn = other_vo.get_value_by_cons_key("api_url_check_sn")
        process_id = other_vo.get_value_by_cons_key("process_id")
        username = other_vo.get_value_by_cons_key("username")
        line = other_vo.get_value_by_cons_key("line")
        order_id = other_vo.get_value_by_cons_key("order_id")
        device_id = other_vo.get_value_by_cons_key("device_id")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "zcno": process_id,
                "op": username,
                "prtno": sn,
                "lnno": line,
                "slkid": order_id,
                "sbid": device_id
            }

            ret1 = xrequest.RequestUtil.post_json(api_url_check_sn, check_param)
            if str(ret1.get('id')) != '0':
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret1.get('message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        api_url_img = data_vo.get_value_by_cons_key("api_url_img")
        bok_ui = data_vo.get_value_by_cons_key("bok_ui")
        process_id = data_vo.get_value_by_cons_key("process_id")
        username = data_vo.get_value_by_cons_key("username")
        line = data_vo.get_value_by_cons_key("line")
        order_id = data_vo.get_value_by_cons_key("order_id")
        device_id = data_vo.get_value_by_cons_key("device_id")
        is_upload_img = data_vo.get_value_by_cons_key("is_upload_img")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        t1 = pcb_entity.get_start_time()
        start_time = t1.strftime(xcons.FMT_TIME_DEFAULT)
        time_file = t1.strftime(xcons.FMT_TIME_FILE)

        board_robot_ng_count = 0
        board_repair_ng_count = 0

        comp_total_count = 0
        comp_robot_ng_count = 0
        comp_repair_ng_count = 0

        need_upload_img_list = []

        board_data_list = []

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_total_count += board_entity.comp_total_number
            comp_robot_ng_count += board_entity.comp_robot_ng_number
            comp_repair_ng_count += board_entity.comp_repair_ng_number

            if board_entity.is_robot_ng():
                board_robot_ng_count += 1

            if board_entity.is_repair_ng():
                board_repair_ng_count += 1

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                self.log.info(comp_entity)

                comp_src_img = comp_entity.image_path
                comp_tag = comp_entity.designator
                robot_ng_str = comp_entity.robot_ng_str

                if comp_entity.is_robot_ng():

                    if comp_src_img:
                        comp_dst_img = f"{barcode}_{comp_tag}_{robot_ng_str}.png"
                    else:
                        comp_dst_img = ""

                    comp_data_list.append({
                        "position": comp_tag,
                        "spc_no": comp_entity.robot_ng_code,
                        "spc_name": comp_entity.robot_ng_str,
                        "opspc_no": comp_entity.repair_ng_code,
                        "opspc_name": comp_entity.repair_ng_str,
                        "prd_no": comp_entity.part,
                        "packpage": comp_entity.package,
                        "sortno": comp_entity.type,
                        "image": comp_dst_img,
                    })

                    # 3. 上传NG图片
                    if comp_dst_img:
                        need_upload_img_list.append(comp_src_img)

            board_data_list.append({
                "prtno": barcode,
                "panelno": int(board_no),
                "sbbok": board_entity.get_robot_result(0, 1),
                "opbok": board_entity.get_repair_result(0, 1),
                "bok": int(bok_ui),
                "remark": "",
                "items": comp_data_list
            })

        pcb_param = {
            "zcno": process_id,
            "op": username,
            "prtno": pcb_sn,
            "lnno": line,
            "slkid": order_id,
            "sbid": device_id,
            "checktime": start_time,
            "programid": pcb_entity.project_name,
            "trackid": pcb_entity.track_index,
            "sbbok": pcb_entity.get_robot_result(0, 1),
            "opbok": pcb_entity.get_repair_result(0, 1),
            "bok": int(bok_ui),
            "unprtnum": pcb_entity.board_count,
            "sbngprtnum": board_robot_ng_count,
            "opngprtnum": board_repair_ng_count,
            "misprtnum": board_robot_ng_count - board_repair_ng_count,
            "checkallqty": comp_total_count,
            "sbngqty": comp_robot_ng_count,
            "opngqty": comp_repair_ng_count,
            "misngqty": comp_robot_ng_count - comp_repair_ng_count,
            "datas": board_data_list
        }

        ret2 = xrequest.RequestUtil.post_json(api_url_data, pcb_param)
        if str(ret2.get('id')) != '0':
            ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret2.get('message')}")

        sid = ret2.get('data', {}).get('sid', '')
        self.log.info(f"获取到单据编号：{sid}")

        error_msg = ""
        self.log.info(f"共需上传图片：{len(need_upload_img_list)}")

        if is_upload_img == "Yes":
            for comp_src_img_ in need_upload_img_list:

                img_param = {
                    "sid": sid,
                    "slkid": order_id,
                }
                self.log.info(f"正在上传图片：{comp_src_img_}")
                with open(comp_src_img_, 'rb') as f:
                    ret3 = xrequest.RequestUtil.post_form(api_url_img, img_param, files={'file': f})

                    if str(ret3.get('id')) != '0':
                        error_msg = f"mes接口异常，上传图片失败，error：{ret3.get('message')}"
                        self.log.warning(f"图片上传失败！")
                        continue

                self.log.info(f"图片上传成功")
        else:
            self.log.warning(f"无需上传图片！")

        if error_msg:
            return self.x_response("false", error_msg)

        return ret_res
