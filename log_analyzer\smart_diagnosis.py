#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能错误诊断模块
基于业务场景和历史经验提供智能诊断和解决建议
"""

import re
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
from datetime import datetime, timedelta

try:
    from .log_parser import LogEntry
    from .business_analyzer import BusinessAnalyzer
except ImportError:
    from log_parser import LogEntry
    from business_analyzer import BusinessAnalyzer


@dataclass
class DiagnosisResult:
    """诊断结果"""
    problem_type: str
    severity: str
    confidence: float
    root_cause: str
    symptoms: List[str]
    solutions: List[str]
    prevention: List[str]
    related_issues: List[str] = None
    
    def __post_init__(self):
        if self.related_issues is None:
            self.related_issues = []


class SmartDiagnosis:
    """智能诊断系统"""
    
    def __init__(self):
        self.business_analyzer = BusinessAnalyzer()
        self.diagnosis_rules = self._build_diagnosis_rules()
        self.symptom_patterns = self._build_symptom_patterns()
        
    def _build_diagnosis_rules(self) -> Dict[str, Dict]:
        """构建诊断规则库"""
        return {
            # MES通信问题
            'mes_communication_failure': {
                'triggers': [
                    'post_json.*失败',
                    'mes.*连接.*失败',
                    'connection.*timeout',
                    'http.*request.*failed',
                    '网络.*异常'
                ],
                'severity': '严重',
                'root_cause': 'MES服务器通信异常',
                'symptoms': [
                    '数据无法上传到MES系统',
                    '接口调用超时或失败',
                    '网络连接不稳定'
                ],
                'solutions': [
                    '1. 立即检查网络连接状态',
                    '2. 确认MES服务器是否正常运行',
                    '3. 检查防火墙和网络配置',
                    '4. 联系网络管理员检查网络设备',
                    '5. 如问题持续，联系MES系统管理员'
                ],
                'prevention': [
                    '定期检查网络连接质量',
                    '监控MES服务器运行状态',
                    '建立网络冗余机制',
                    '设置连接超时重试机制'
                ]
            },
            
            # 条码校验问题
            'barcode_validation_error': {
                'triggers': [
                    '条码.*校验.*失败',
                    '条码.*不存在',
                    '条码.*格式.*错误',
                    'barcode.*validation.*failed',
                    'sn.*校验.*失败'
                ],
                'severity': '中等',
                'root_cause': '条码识别或数据库查询问题',
                'symptoms': [
                    '扫码无法识别产品信息',
                    '条码格式不符合规范',
                    '数据库中找不到对应记录'
                ],
                'solutions': [
                    '1. 检查条码打印质量是否清晰',
                    '2. 确认扫码设备工作正常',
                    '3. 验证条码格式是否正确',
                    '4. 检查产品是否已在系统中建档',
                    '5. 联系生产管理员确认工单状态'
                ],
                'prevention': [
                    '定期校准扫码设备',
                    '检查条码打印质量',
                    '建立条码规范标准',
                    '定期清理扫码镜头'
                ]
            },
            
            # 复判流程问题
            'recheck_process_error': {
                'triggers': [
                    '复判.*失败',
                    '复判.*数据.*上传.*失败',
                    'recheck.*failed',
                    '复判.*超时'
                ],
                'severity': '中等',
                'root_cause': '复判流程执行异常',
                'symptoms': [
                    '复判结果无法保存',
                    '复判数据上传失败',
                    '复判流程卡住不动'
                ],
                'solutions': [
                    '1. 检查复判工位设备状态',
                    '2. 确认复判人员操作是否正确',
                    '3. 检查网络连接和数据传输',
                    '4. 重启复判软件重试',
                    '5. 联系质量工程师确认复判标准'
                ],
                'prevention': [
                    '培训复判操作人员',
                    '定期维护复判设备',
                    '建立复判流程监控',
                    '设置复判数据备份机制'
                ]
            },
            
            # 设备故障问题
            'device_malfunction': {
                'triggers': [
                    '设备.*故障',
                    '设备.*离线',
                    '相机.*异常',
                    '光源.*异常',
                    '温度.*异常',
                    'device.*offline'
                ],
                'severity': '严重',
                'root_cause': '生产设备硬件故障',
                'symptoms': [
                    '设备无法正常工作',
                    '检测精度下降',
                    '设备频繁报警',
                    '生产线停机'
                ],
                'solutions': [
                    '1. 立即停止设备运行',
                    '2. 检查设备电源和连接线',
                    '3. 查看设备报警信息',
                    '4. 联系设备维护工程师',
                    '5. 如需要，启用备用设备'
                ],
                'prevention': [
                    '制定设备预防性维护计划',
                    '定期检查设备运行状态',
                    '建立设备备件库存',
                    '培训设备操作人员'
                ]
            },
            
            # 数据上传问题
            'data_upload_failure': {
                'triggers': [
                    '数据.*上传.*失败',
                    'upload.*failed',
                    '数据.*同步.*失败',
                    '发送.*数据.*失败'
                ],
                'severity': '中等',
                'root_cause': '数据传输通道异常',
                'symptoms': [
                    '检测数据无法保存',
                    '生产记录丢失',
                    '报表数据不完整'
                ],
                'solutions': [
                    '1. 检查网络连接状态',
                    '2. 确认服务器存储空间',
                    '3. 检查数据格式是否正确',
                    '4. 重试数据上传操作',
                    '5. 联系IT人员检查服务器状态'
                ],
                'prevention': [
                    '监控网络传输质量',
                    '定期清理服务器存储',
                    '建立数据备份机制',
                    '设置数据上传重试机制'
                ]
            },
            
            # 工单流程问题
            'work_order_error': {
                'triggers': [
                    '工单.*不存在',
                    '工艺.*路线.*错误',
                    '生产.*计划.*异常',
                    '工序.*锁定'
                ],
                'severity': '高',
                'root_cause': '生产计划或工艺配置问题',
                'symptoms': [
                    '无法开始生产',
                    '工艺流程错误',
                    '生产计划混乱'
                ],
                'solutions': [
                    '1. 确认工单号是否正确',
                    '2. 检查工单是否已下达',
                    '3. 联系生产计划员确认',
                    '4. 检查工艺路线配置',
                    '5. 确认产品型号匹配'
                ],
                'prevention': [
                    '建立工单审核机制',
                    '定期检查工艺配置',
                    '加强生产计划管理',
                    '建立工单变更流程'
                ]
            }
        }
    
    def _build_symptom_patterns(self) -> Dict[str, List[str]]:
        """构建症状模式库"""
        return {
            '网络问题': [
                'timeout', 'connection.*failed', '网络.*异常', 'network.*error'
            ],
            '数据库问题': [
                'database.*error', 'sql.*error', '数据库.*连接.*失败'
            ],
            '硬件问题': [
                '设备.*故障', '相机.*异常', '光源.*异常', '温度.*异常'
            ],
            '软件问题': [
                'application.*error', '程序.*异常', 'software.*error'
            ],
            '配置问题': [
                'config.*error', '配置.*错误', '参数.*异常'
            ]
        }
    
    def diagnose(self, entries: List[LogEntry]) -> List[DiagnosisResult]:
        """执行智能诊断"""
        diagnoses = []
        
        # 分析错误模式
        error_patterns = self._analyze_error_patterns(entries)
        
        # 对每种错误模式进行诊断
        for pattern, occurrences in error_patterns.items():
            if occurrences['count'] > 0:
                diagnosis = self._diagnose_pattern(pattern, occurrences, entries)
                if diagnosis:
                    diagnoses.append(diagnosis)
        
        # 按严重程度和置信度排序
        diagnoses.sort(key=lambda x: (self._severity_weight(x.severity), x.confidence), reverse=True)
        
        return diagnoses
    
    def _analyze_error_patterns(self, entries: List[LogEntry]) -> Dict[str, Dict]:
        """分析错误模式"""
        patterns = {}
        
        for rule_name, rule_config in self.diagnosis_rules.items():
            pattern_info = {
                'count': 0,
                'recent_count': 0,
                'examples': [],
                'time_distribution': defaultdict(int)
            }
            
            # 统计最近1小时的错误
            recent_time = datetime.now() - timedelta(hours=1)
            
            for entry in entries:
                if entry.level in ['ERROR', 'CRITICAL', 'WARNING']:
                    for trigger in rule_config['triggers']:
                        if re.search(trigger, entry.message, re.IGNORECASE):
                            pattern_info['count'] += 1
                            
                            if entry.timestamp and entry.timestamp > recent_time:
                                pattern_info['recent_count'] += 1
                            
                            if len(pattern_info['examples']) < 3:
                                pattern_info['examples'].append(entry.message[:100])
                            
                            if entry.timestamp:
                                hour_key = entry.timestamp.strftime('%H')
                                pattern_info['time_distribution'][hour_key] += 1
                            
                            break
            
            if pattern_info['count'] > 0:
                patterns[rule_name] = pattern_info
        
        return patterns
    
    def _diagnose_pattern(self, pattern_name: str, occurrences: Dict, 
                         entries: List[LogEntry]) -> Optional[DiagnosisResult]:
        """诊断特定模式"""
        if pattern_name not in self.diagnosis_rules:
            return None
        
        rule = self.diagnosis_rules[pattern_name]
        
        # 计算置信度
        confidence = self._calculate_confidence(occurrences)
        
        # 查找相关问题
        related_issues = self._find_related_issues(pattern_name, entries)
        
        return DiagnosisResult(
            problem_type=pattern_name.replace('_', ' ').title(),
            severity=rule['severity'],
            confidence=confidence,
            root_cause=rule['root_cause'],
            symptoms=rule['symptoms'],
            solutions=rule['solutions'],
            prevention=rule['prevention'],
            related_issues=related_issues
        )
    
    def _calculate_confidence(self, occurrences: Dict) -> float:
        """计算诊断置信度"""
        count = occurrences['count']
        recent_count = occurrences['recent_count']
        
        # 基础置信度基于错误频率
        base_confidence = min(count * 0.1, 0.8)
        
        # 如果最近有错误，增加置信度
        if recent_count > 0:
            base_confidence += 0.2
        
        # 如果错误集中在某个时间段，增加置信度
        time_dist = occurrences['time_distribution']
        if time_dist:
            max_hour_count = max(time_dist.values())
            if max_hour_count > count * 0.5:  # 超过50%的错误集中在某个小时
                base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _find_related_issues(self, pattern_name: str, entries: List[LogEntry]) -> List[str]:
        """查找相关问题"""
        related = []
        
        # 基于时间相关性查找
        pattern_entries = []
        rule = self.diagnosis_rules[pattern_name]
        
        for entry in entries:
            for trigger in rule['triggers']:
                if re.search(trigger, entry.message, re.IGNORECASE):
                    pattern_entries.append(entry)
                    break
        
        # 查找在相同时间段内的其他错误
        for pattern_entry in pattern_entries[-5:]:  # 只检查最近5个
            if pattern_entry.timestamp:
                time_window = timedelta(minutes=5)
                start_time = pattern_entry.timestamp - time_window
                end_time = pattern_entry.timestamp + time_window
                
                for entry in entries:
                    if (entry.timestamp and 
                        start_time <= entry.timestamp <= end_time and
                        entry.level in ['ERROR', 'CRITICAL'] and
                        entry != pattern_entry):
                        
                        # 简化错误消息
                        simplified_msg = entry.message[:50] + "..."
                        if simplified_msg not in related:
                            related.append(simplified_msg)
        
        return related[:3]  # 最多返回3个相关问题
    
    def _severity_weight(self, severity: str) -> int:
        """严重程度权重"""
        weights = {
            '严重': 4,
            '高': 3,
            '中等': 2,
            '低': 1
        }
        return weights.get(severity, 0)
    
    def get_quick_fix_suggestions(self, entries: List[LogEntry]) -> List[str]:
        """获取快速修复建议"""
        diagnoses = self.diagnose(entries)
        suggestions = []
        
        for diagnosis in diagnoses[:3]:  # 只处理前3个最重要的问题
            if diagnosis.confidence > 0.6:  # 只有高置信度的才给出快速建议
                # 取第一个解决方案作为快速建议
                if diagnosis.solutions:
                    quick_fix = diagnosis.solutions[0]
                    suggestions.append(f"[{diagnosis.problem_type}] {quick_fix}")
        
        return suggestions
    
    def generate_diagnosis_report(self, entries: List[LogEntry]) -> str:
        """生成诊断报告"""
        diagnoses = self.diagnose(entries)
        
        report = []
        report.append("=" * 60)
        report.append("🔍 智能诊断报告")
        report.append("=" * 60)
        report.append(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"分析日志条数: {len(entries)}")
        report.append(f"发现问题类型: {len(diagnoses)}种")
        report.append("")
        
        if not diagnoses:
            report.append("✅ 未发现明显问题，系统运行正常")
            return "\n".join(report)
        
        # 总体评估
        critical_count = sum(1 for d in diagnoses if d.severity == '严重')
        high_count = sum(1 for d in diagnoses if d.severity == '高')
        
        if critical_count > 0:
            report.append("🚨 系统状态: 严重 - 需要立即处理")
        elif high_count > 0:
            report.append("⚠️  系统状态: 警告 - 需要关注")
        else:
            report.append("ℹ️  系统状态: 正常 - 有轻微问题")
        report.append("")
        
        # 详细诊断结果
        for i, diagnosis in enumerate(diagnoses, 1):
            report.append(f"📋 问题 {i}: {diagnosis.problem_type}")
            report.append(f"   严重程度: {diagnosis.severity}")
            report.append(f"   置信度: {diagnosis.confidence:.1%}")
            report.append(f"   根本原因: {diagnosis.root_cause}")
            report.append("")
            
            report.append("   🔍 主要症状:")
            for symptom in diagnosis.symptoms:
                report.append(f"     • {symptom}")
            report.append("")
            
            report.append("   💡 解决方案:")
            for j, solution in enumerate(diagnosis.solutions, 1):
                report.append(f"     {j}. {solution}")
            report.append("")
            
            if diagnosis.related_issues:
                report.append("   🔗 相关问题:")
                for issue in diagnosis.related_issues:
                    report.append(f"     • {issue}")
                report.append("")
            
            report.append("   🛡️  预防措施:")
            for prevention in diagnosis.prevention:
                report.append(f"     • {prevention}")
            report.append("")
            report.append("-" * 50)
            report.append("")
        
        # 总结建议
        report.append("📝 总结建议:")
        quick_fixes = self.get_quick_fix_suggestions(entries)
        if quick_fixes:
            report.append("   立即执行:")
            for fix in quick_fixes:
                report.append(f"     • {fix}")
        else:
            report.append("   • 继续监控系统运行状态")
            report.append("   • 定期检查设备和网络连接")
        
        return "\n".join(report)
    
    def get_maintenance_recommendations(self, entries: List[LogEntry]) -> List[str]:
        """获取维护建议"""
        diagnoses = self.diagnose(entries)
        recommendations = set()
        
        for diagnosis in diagnoses:
            if diagnosis.confidence > 0.5:
                for prevention in diagnosis.prevention:
                    recommendations.add(prevention)
        
        return list(recommendations)[:5]  # 最多返回5个建议
