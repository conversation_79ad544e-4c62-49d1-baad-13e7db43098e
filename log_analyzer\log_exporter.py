#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志导出模块
支持多种格式的日志导出和报告生成
"""

import csv
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter

try:
    from .log_parser import LogEntry
    from .error_analyzer import ErrorAnalyzer
except ImportError:
    from log_parser import LogEntry
    from error_analyzer import ErrorAnalyzer


class LogExporter:
    """日志导出器"""
    
    def __init__(self):
        self.analyzer = ErrorAnalyzer()
        
    def export_to_csv(self, entries: List[LogEntry], file_path: str, 
                     include_headers: bool = True) -> bool:
        """导出到CSV文件"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                if include_headers:
                    writer.writerow([
                        '时间', '级别', '文件路径', '行号', '错误码', '消息', '原始内容'
                    ])
                
                for entry in entries:
                    writer.writerow([
                        entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else '',
                        entry.level,
                        entry.file_path,
                        entry.line_number,
                        entry.error_code or '',
                        entry.message,
                        entry.raw_line
                    ])
            
            return True
        except Exception as e:
            print(f"导出CSV失败: {e}")
            return False
    
    def export_to_excel(self, entries: List[LogEntry], file_path: str) -> bool:
        """导出到Excel文件"""
        try:
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "日志数据"
            
            # 设置表头
            headers = ['时间', '级别', '文件名', '行号', '错误码', '消息']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # 填充数据
            for row, entry in enumerate(entries, 2):
                ws.cell(row=row, column=1, value=entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else '')
                
                # 级别单元格着色
                level_cell = ws.cell(row=row, column=2, value=entry.level)
                if entry.level == 'ERROR':
                    level_cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
                elif entry.level == 'CRITICAL':
                    level_cell.fill = PatternFill(start_color="FF9999", end_color="FF9999", fill_type="solid")
                elif entry.level == 'WARNING':
                    level_cell.fill = PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")
                
                ws.cell(row=row, column=3, value=Path(entry.file_path).name)
                ws.cell(row=row, column=4, value=entry.line_number)
                ws.cell(row=row, column=5, value=entry.error_code or '')
                ws.cell(row=row, column=6, value=entry.message[:500])  # 限制长度
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 创建错误统计工作表
            if any(entry.level in ['ERROR', 'CRITICAL'] for entry in entries):
                self._create_error_summary_sheet(wb, entries)
            
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"导出Excel失败: {e}")
            return False
    
    def _create_error_summary_sheet(self, workbook, entries: List[LogEntry]):
        """创建错误统计工作表"""
        ws = workbook.create_sheet("错误统计")
        
        # 分析错误
        error_summary = self.analyzer.get_error_summary(entries)
        
        # 标题
        ws.cell(row=1, column=1, value="错误统计报告").font = Font(size=16, bold=True)
        ws.cell(row=2, column=1, value=f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        row = 4
        
        # 总体统计
        ws.cell(row=row, column=1, value="总体统计").font = Font(bold=True)
        row += 1
        ws.cell(row=row, column=1, value=f"总错误数: {error_summary['total_errors']}")
        row += 2
        
        # 严重程度分布
        ws.cell(row=row, column=1, value="严重程度分布").font = Font(bold=True)
        row += 1
        for severity, count in error_summary['severity_distribution'].items():
            ws.cell(row=row, column=1, value=severity)
            ws.cell(row=row, column=2, value=count)
            row += 1
        row += 1
        
        # 错误分类
        ws.cell(row=row, column=1, value="错误分类").font = Font(bold=True)
        row += 1
        for category, count in error_summary['error_categories'].items():
            ws.cell(row=row, column=1, value=category)
            ws.cell(row=row, column=2, value=count)
            row += 1
        row += 1
        
        # 最频繁错误
        ws.cell(row=row, column=1, value="最频繁错误 (Top 10)").font = Font(bold=True)
        row += 1
        ws.cell(row=row, column=1, value="错误码").font = Font(bold=True)
        ws.cell(row=row, column=2, value="描述").font = Font(bold=True)
        ws.cell(row=row, column=3, value="次数").font = Font(bold=True)
        row += 1
        
        for error in error_summary['top_errors']:
            ws.cell(row=row, column=1, value=error['code'])
            ws.cell(row=row, column=2, value=error['description'])
            ws.cell(row=row, column=3, value=error['count'])
            row += 1
    
    def export_to_json(self, entries: List[LogEntry], file_path: str) -> bool:
        """导出到JSON文件"""
        try:
            data = []
            for entry in entries:
                data.append({
                    'timestamp': entry.timestamp.isoformat() if entry.timestamp else None,
                    'level': entry.level,
                    'file_path': entry.file_path,
                    'line_number': entry.line_number,
                    'error_code': entry.error_code,
                    'message': entry.message,
                    'raw_line': entry.raw_line
                })
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"导出JSON失败: {e}")
            return False
    
    def export_to_xml(self, entries: List[LogEntry], file_path: str) -> bool:
        """导出到XML文件"""
        try:
            root = ET.Element("LogEntries")
            
            for entry in entries:
                log_elem = ET.SubElement(root, "LogEntry")
                
                ET.SubElement(log_elem, "Timestamp").text = (
                    entry.timestamp.isoformat() if entry.timestamp else ""
                )
                ET.SubElement(log_elem, "Level").text = entry.level
                ET.SubElement(log_elem, "FilePath").text = entry.file_path
                ET.SubElement(log_elem, "LineNumber").text = str(entry.line_number)
                ET.SubElement(log_elem, "ErrorCode").text = entry.error_code or ""
                ET.SubElement(log_elem, "Message").text = entry.message
                ET.SubElement(log_elem, "RawLine").text = entry.raw_line
            
            tree = ET.ElementTree(root)
            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            
            return True
        except Exception as e:
            print(f"导出XML失败: {e}")
            return False
    
    def generate_error_report(self, entries: List[LogEntry], file_path: str) -> bool:
        """生成错误分析报告"""
        try:
            report = self.analyzer.generate_error_report(entries)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            return True
        except Exception as e:
            print(f"生成错误报告失败: {e}")
            return False
    
    def generate_html_report(self, entries: List[LogEntry], file_path: str) -> bool:
        """生成HTML格式的报告"""
        try:
            error_summary = self.analyzer.get_error_summary(entries)
            
            html_content = self._create_html_template(entries, error_summary)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return True
        except Exception as e:
            print(f"生成HTML报告失败: {e}")
            return False
    
    def _create_html_template(self, entries: List[LogEntry], error_summary: Dict) -> str:
        """创建HTML报告模板"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志分析报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .error {{ color: #d32f2f; }}
        .warning {{ color: #f57c00; }}
        .info {{ color: #1976d2; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .chart {{ margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>日志分析报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>分析日志条目数: {len(entries)}</p>
        <p>发现错误总数: {error_summary['total_errors']}</p>
    </div>
    
    <div class="section">
        <h2>严重程度分布</h2>
        <table>
            <tr><th>严重程度</th><th>数量</th></tr>
"""
        
        for severity, count in error_summary['severity_distribution'].items():
            css_class = severity.lower()
            html += f'<tr><td class="{css_class}">{severity}</td><td>{count}</td></tr>'
        
        html += """
        </table>
    </div>
    
    <div class="section">
        <h2>错误分类统计</h2>
        <table>
            <tr><th>分类</th><th>数量</th></tr>
"""
        
        for category, count in error_summary['error_categories'].items():
            html += f'<tr><td>{category}</td><td>{count}</td></tr>'
        
        html += """
        </table>
    </div>
    
    <div class="section">
        <h2>最频繁错误 (Top 10)</h2>
        <table>
            <tr><th>错误码</th><th>描述</th><th>次数</th><th>分类</th></tr>
"""
        
        for error in error_summary['top_errors']:
            html += f"""
            <tr>
                <td>{error['code']}</td>
                <td>{error['description']}</td>
                <td>{error['count']}</td>
                <td>{error['category']}</td>
            </tr>
"""
        
        html += """
        </table>
    </div>
    
    <div class="section">
        <h2>最近严重错误</h2>
        <table>
            <tr><th>错误码</th><th>描述</th><th>次数</th><th>最后发生时间</th></tr>
"""
        
        for error in error_summary['recent_critical']:
            html += f"""
            <tr class="error">
                <td>{error.code}</td>
                <td>{error.description}</td>
                <td>{error.count}</td>
                <td>{error.last_occurrence}</td>
            </tr>
"""
        
        html += """
        </table>
    </div>
    
    <div class="section">
        <h2>日志详情 (最近100条错误)</h2>
        <table>
            <tr><th>时间</th><th>级别</th><th>文件</th><th>消息</th></tr>
"""
        
        error_entries = [e for e in entries if e.level in ['ERROR', 'CRITICAL']][-100:]
        for entry in error_entries:
            css_class = entry.level.lower()
            time_str = entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else ''
            file_name = Path(entry.file_path).name
            message = entry.message[:100] + "..." if len(entry.message) > 100 else entry.message
            
            html += f"""
            <tr class="{css_class}">
                <td>{time_str}</td>
                <td>{entry.level}</td>
                <td>{file_name}</td>
                <td>{message}</td>
            </tr>
"""
        
        html += """
        </table>
    </div>
</body>
</html>
"""
        
        return html
    
    def export_filtered_logs(self, entries: List[LogEntry], file_path: str, 
                           format_type: str = 'csv') -> bool:
        """根据格式类型导出过滤后的日志"""
        format_type = format_type.lower()
        
        if format_type == 'csv':
            return self.export_to_csv(entries, file_path)
        elif format_type == 'excel':
            return self.export_to_excel(entries, file_path)
        elif format_type == 'json':
            return self.export_to_json(entries, file_path)
        elif format_type == 'xml':
            return self.export_to_xml(entries, file_path)
        elif format_type == 'html':
            return self.generate_html_report(entries, file_path)
        elif format_type == 'txt':
            return self.generate_error_report(entries, file_path)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
    
    def get_export_summary(self, entries: List[LogEntry]) -> Dict:
        """获取导出摘要信息"""
        if not entries:
            return {}
        
        summary = {
            'total_entries': len(entries),
            'date_range': {},
            'file_count': len(set(entry.file_path for entry in entries)),
            'level_distribution': {},
            'error_count': 0
        }
        
        timestamps = [entry.timestamp for entry in entries if entry.timestamp]
        if timestamps:
            summary['date_range'] = {
                'start': min(timestamps).strftime('%Y-%m-%d %H:%M:%S'),
                'end': max(timestamps).strftime('%Y-%m-%d %H:%M:%S')
            }
        
        for entry in entries:
            level = entry.level
            summary['level_distribution'][level] = summary['level_distribution'].get(level, 0) + 1
            
            if level in ['ERROR', 'CRITICAL']:
                summary['error_count'] += 1
        
        return summary
