{"1005": {"alg_name": ";InspBilateralDistance", "alg_val": {"InspBilateralDistance": [{"distanceA": {"maxDistanceAThreshold": 10000, "minDistanceAThreshold": 0, "posX1": 11.892952105803033, "posX2": 11.892952105803033, "posY1": 23, "posY2": 0, "rect": {"angle": 0, "centerX": 15, "centerY": 12, "height": 25, "width": 25}, "value": 345.72026800000003}, "distanceB": {"maxDistanceBThreshold": 10000, "minDistanceBThreshold": 0, "posX1": 15, "posX2": 15, "posY1": 23, "posY2": 23, "rect": {"angle": 0, "centerX": 15, "centerY": 12, "height": 25, "width": 25}, "value": 0}, "rect": {"angle": 0, "centerX": 0, "centerY": 0, "height": 0, "width": 0}, "type": 33}]}}, "1007": {"alg_name": ";InspHeightTilted;InspHeightTilted;InspHeight;InspHeightTilted", "alg_val": {"InspHeight": [{"maxThreshold": 1000, "method": "heightRange", "minThreshold": 0, "result": 1, "type": 15, "value": 163.35726928710938}], "InspHeightTilted": [{"CornerAbsHeight": {"maxThreshold": 50, "minThreshold": 0, "result": false, "resultValue": 0, "state": true}, "CornerHeight": {"maxThreshold": 0, "minThreshold": 0, "result": false, "resultValue": 0, "state": true}, "CrossAbsHeightX": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 18.907900029962704, "state": true}, "CrossAbsHeightY": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 2.8473574898459617, "state": true}, "methodMode": "Cross", "type": 15}, {"CornerAbsHeight": {"maxThreshold": 50, "minThreshold": 0, "result": false, "resultValue": 52.884691354000196, "state": true}, "CornerHeight": {"maxThreshold": 0, "minThreshold": 0, "result": false, "resultValue": 0, "state": true}, "CrossAbsHeightX": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 23.724949923428625, "state": true}, "CrossAbsHeightY": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 2.6839965473521943, "state": true}, "methodMode": "Corner", "type": 15}]}}, "1008": {"alg_name": ";InspDimensionDetect;InspMeasureTwoCompPos;InspHeight", "alg_val": {"InspDimensionDetect": [{"rateValue": {"area": 100, "areaMax": 116.2699966430664, "areaMin": 85.26000213623047, "height": 100.1102066040039, "heightMax": 115, "heightMin": 85, "long": 100, "longMax": 115, "longMin": 85, "volume": 100.1102066040039, "volumeMax": 108.38999938964844, "volumeMin": 77.41999816894531, "width": 100, "widthMax": 115, "widthMin": 85}, "result": 1, "type": 23, "value": {"area": 0.38703596591949463, "areaMax": 0.44999998807907104, "areaMin": 0.33000001311302185, "height": 167.05137634277344, "heightMax": 191.89999389648438, "heightMin": 141.83999633789062, "long": 420.8768310546875, "longMax": 484.010009765625, "longMin": 357.75, "volume": 0.0646548941731453, "volumeMax": 0.07000000029802322, "volumeMin": 0.05000000074505806, "width": 992.0668334960938, "widthMax": 1140.8800048828125, "widthMin": 843.260009765625}}], "InspHeight": [{"maxThreshold": 1000, "method": "heightPeak", "minThreshold": 0, "result": 1, "type": 15, "value": 180.91049194335938}], "InspMeasureTwoCompPos": [{"distanceRate": 0, "distanceValue": 0, "measureMode": 5, "thresholdRate": 1, "thresholdRateMax": 60, "thresholdRateMin": 60, "thresholdValue": 0, "thresholdValueMax": 60, "thresholdValueMin": 60, "type": 26}]}}, "1006": {"alg_name": ";InspDimensionDetect;InspDimensionDetect;InspHeightScale;InspHeightTilted", "alg_val": {"InspDimensionDetect": [{"rateValue": {"area": 100.06239318847656, "areaMax": 114.97000122070312, "areaMin": 85.01000213623047, "height": 100.07665252685547, "heightMax": 115, "heightMin": 85.01000213623047, "long": 99.82744598388672, "longMax": 115, "longMin": 85, "volume": 100.13909149169922, "volumeMax": 115.77999877929688, "volumeMin": 84.20999908447266, "width": 98.6288070678711, "widthMax": 115, "widthMin": 85}, "result": 2, "type": 21, "value": {"area": 11.958576202392578, "areaMax": 13.739999771118164, "areaMin": 10.15999984741211, "height": 39.777679443359375, "heightMax": 45.709999084472656, "heightMin": 33.790000915527344, "long": 4145.43505859375, "longMax": 4775.490234375, "longMin": 3529.7099609375, "volume": 0.47568440437316895, "volumeMax": 0.550000011920929, "volumeMin": 0.4000000059604645, "width": 1709.1007080078125, "widthMax": 1992.7900390625, "widthMin": 1472.9300537109375}}], "InspHeightTilted": [{"CornerAbsHeight": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 19.114348848255, "state": true}, "CornerHeight": {"maxThreshold": 0, "minThreshold": 0, "result": false, "resultValue": 0, "state": true}, "CrossAbsHeightX": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 5.810945863610168, "state": true}, "CrossAbsHeightY": {"maxThreshold": 50, "minThreshold": 0, "result": true, "resultValue": 0.679046447316269, "state": true}, "methodMode": "<PERSON><PERSON><PERSON><PERSON>", "type": 15}]}}}