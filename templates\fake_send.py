# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/fake_send.ui'
#
# Created by: PyQt5 UI code generator 5.10.1
#
# WARNING! All changes made in this file will be lost!

from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets

class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(764, 823)
        self.gridLayout = QtWidgets.QGridLayout(Form)
        self.gridLayout.setObjectName("gridLayout")
        self.frame = QtWidgets.QFrame(Form)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setFrameShadow(QtWidgets.QFrame.Plain)
        self.label.setObjectName("label")
        self.verticalLayout_2.addWidget(self.label)
        self.frame_6 = QtWidgets.QFrame(self.frame)
        self.frame_6.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.frame_6)
        self.horizontalLayout_3.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.horizontalLayout_3.setContentsMargins(0, 5, 0, 5)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.label_2 = QtWidgets.QLabel(self.frame_6)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_3.addWidget(self.label_2)
        self.line_pcb_sn = QtWidgets.QLineEdit(self.frame_6)
        self.line_pcb_sn.setObjectName("line_pcb_sn")
        self.horizontalLayout_3.addWidget(self.line_pcb_sn)
        self.verticalLayout_2.addWidget(self.frame_6)
        self.btn_get_sn = QtWidgets.QPushButton(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_get_sn.sizePolicy().hasHeightForWidth())
        self.btn_get_sn.setSizePolicy(sizePolicy)
        self.btn_get_sn.setObjectName("btn_get_sn")
        self.verticalLayout_2.addWidget(self.btn_get_sn)
        self.gridLayout.addWidget(self.frame, 0, 0, 1, 1)
        self.frame_4 = QtWidgets.QFrame(Form)
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.frame_4)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.label_5 = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setObjectName("label_5")
        self.verticalLayout_5.addWidget(self.label_5)
        self.frame_8 = QtWidgets.QFrame(self.frame_4)
        self.frame_8.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_8.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_8.setObjectName("frame_8")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.frame_8)
        self.horizontalLayout_5.setContentsMargins(0, 5, 0, 5)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.label_8 = QtWidgets.QLabel(self.frame_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_8.sizePolicy().hasHeightForWidth())
        self.label_8.setSizePolicy(sizePolicy)
        self.label_8.setObjectName("label_8")
        self.horizontalLayout_5.addWidget(self.label_8)
        self.combo_device_status = QtWidgets.QComboBox(self.frame_8)
        self.combo_device_status.setObjectName("combo_device_status")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.combo_device_status.addItem("")
        self.horizontalLayout_5.addWidget(self.combo_device_status)
        self.verticalLayout_5.addWidget(self.frame_8)
        self.btn_send_device_status = QtWidgets.QPushButton(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_send_device_status.sizePolicy().hasHeightForWidth())
        self.btn_send_device_status.setSizePolicy(sizePolicy)
        self.btn_send_device_status.setObjectName("btn_send_device_status")
        self.verticalLayout_5.addWidget(self.btn_send_device_status)
        self.gridLayout.addWidget(self.frame_4, 3, 0, 1, 1)
        self.frame_3 = QtWidgets.QFrame(Form)
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.frame_3)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.label_6 = QtWidgets.QLabel(self.frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        self.label_6.setObjectName("label_6")
        self.verticalLayout_4.addWidget(self.label_6)
        self.send_type_inspector = QtWidgets.QRadioButton(self.frame_3)
        self.send_type_inspector.setObjectName("send_type_inspector")
        self.verticalLayout_4.addWidget(self.send_type_inspector)
        self.send_type_repair = QtWidgets.QRadioButton(self.frame_3)
        self.send_type_repair.setChecked(True)
        self.send_type_repair.setObjectName("send_type_repair")
        self.verticalLayout_4.addWidget(self.send_type_repair)
        self.frame_7 = QtWidgets.QFrame(self.frame_3)
        self.frame_7.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_7)
        self.horizontalLayout_2.setContentsMargins(0, 5, 0, 5)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_7 = QtWidgets.QLabel(self.frame_7)
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_2.addWidget(self.label_7)
        self.line_review_path = QtWidgets.QLineEdit(self.frame_7)
        self.line_review_path.setObjectName("line_review_path")
        self.horizontalLayout_2.addWidget(self.line_review_path)
        self.verticalLayout_4.addWidget(self.frame_7)
        self.frame_9 = QtWidgets.QFrame(self.frame_3)
        self.frame_9.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame_9.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_9.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_9.setObjectName("frame_9")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.frame_9)
        self.horizontalLayout_4.setContentsMargins(0, 5, 0, 5)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.btn_select_review_path = QtWidgets.QPushButton(self.frame_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_select_review_path.sizePolicy().hasHeightForWidth())
        self.btn_select_review_path.setSizePolicy(sizePolicy)
        self.btn_select_review_path.setObjectName("btn_select_review_path")
        self.horizontalLayout_4.addWidget(self.btn_select_review_path)
        self.btn_send_data = QtWidgets.QPushButton(self.frame_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_send_data.sizePolicy().hasHeightForWidth())
        self.btn_send_data.setSizePolicy(sizePolicy)
        self.btn_send_data.setObjectName("btn_send_data")
        self.horizontalLayout_4.addWidget(self.btn_send_data)
        self.verticalLayout_4.addWidget(self.frame_9)
        self.gridLayout.addWidget(self.frame_3, 2, 0, 1, 1)
        self.frame_2 = QtWidgets.QFrame(Form)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.label_3 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setObjectName("label_3")
        self.verticalLayout_3.addWidget(self.label_3)
        self.frame_5 = QtWidgets.QFrame(self.frame_2)
        self.frame_5.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_5)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 5)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label_4 = QtWidgets.QLabel(self.frame_5)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout.addWidget(self.label_4)
        self.line_sn_list = QtWidgets.QLineEdit(self.frame_5)
        self.line_sn_list.setObjectName("line_sn_list")
        self.horizontalLayout.addWidget(self.line_sn_list)
        self.verticalLayout_3.addWidget(self.frame_5)
        self.btn_check_sn = QtWidgets.QPushButton(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_check_sn.sizePolicy().hasHeightForWidth())
        self.btn_check_sn.setSizePolicy(sizePolicy)
        self.btn_check_sn.setObjectName("btn_check_sn")
        self.verticalLayout_3.addWidget(self.btn_check_sn)
        self.gridLayout.addWidget(self.frame_2, 1, 0, 1, 1)
        self.frame_11 = QtWidgets.QFrame(Form)
        self.frame_11.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame_11.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_11.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_11.setObjectName("frame_11")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.frame_11)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.label_10 = QtWidgets.QLabel(self.frame_11)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setObjectName("label_10")
        self.verticalLayout_6.addWidget(self.label_10)
        self.text_log_print = QtWidgets.QPlainTextEdit(self.frame_11)
        self.text_log_print.setEnabled(True)
        self.text_log_print.setMaximumBlockCount(1000)
        self.text_log_print.setObjectName("text_log_print")
        self.verticalLayout_6.addWidget(self.text_log_print)
        self.btn_clear_log = QtWidgets.QPushButton(self.frame_11)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_clear_log.sizePolicy().hasHeightForWidth())
        self.btn_clear_log.setSizePolicy(sizePolicy)
        self.btn_clear_log.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.btn_clear_log.setObjectName("btn_clear_log")
        self.verticalLayout_6.addWidget(self.btn_clear_log)
        self.gridLayout.addWidget(self.frame_11, 0, 1, 7, 1)
        self.frame_12 = QtWidgets.QFrame(Form)
        self.frame_12.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_12.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_12.setObjectName("frame_12")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.frame_12)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.label_11 = QtWidgets.QLabel(self.frame_12)
        self.label_11.setObjectName("label_11")
        self.verticalLayout_7.addWidget(self.label_11)
        self.frame_13 = QtWidgets.QFrame(self.frame_12)
        self.frame_13.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_13.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_13.setObjectName("frame_13")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.frame_13)
        self.horizontalLayout_6.setContentsMargins(1, 1, 1, 1)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label_12 = QtWidgets.QLabel(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_12.sizePolicy().hasHeightForWidth())
        self.label_12.setSizePolicy(sizePolicy)
        self.label_12.setObjectName("label_12")
        self.horizontalLayout_6.addWidget(self.label_12)
        self.combo_device_status_v3 = QtWidgets.QComboBox(self.frame_13)
        self.combo_device_status_v3.setObjectName("combo_device_status_v3")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.combo_device_status_v3.addItem("")
        self.horizontalLayout_6.addWidget(self.combo_device_status_v3)
        self.verticalLayout_7.addWidget(self.frame_13)
        self.btn_device_status_v3 = QtWidgets.QPushButton(self.frame_12)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_device_status_v3.sizePolicy().hasHeightForWidth())
        self.btn_device_status_v3.setSizePolicy(sizePolicy)
        self.btn_device_status_v3.setObjectName("btn_device_status_v3")
        self.verticalLayout_7.addWidget(self.btn_device_status_v3)
        self.gridLayout.addWidget(self.frame_12, 5, 0, 1, 1)
        self.frame_10 = QtWidgets.QFrame(Form)
        self.frame_10.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_10.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_10.setObjectName("frame_10")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame_10)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label_9 = QtWidgets.QLabel(self.frame_10)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy)
        self.label_9.setObjectName("label_9")
        self.verticalLayout.addWidget(self.label_9)
        self.btn_send_idle_status = QtWidgets.QPushButton(self.frame_10)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_send_idle_status.sizePolicy().hasHeightForWidth())
        self.btn_send_idle_status.setSizePolicy(sizePolicy)
        self.btn_send_idle_status.setObjectName("btn_send_idle_status")
        self.verticalLayout.addWidget(self.btn_send_idle_status)
        self.gridLayout.addWidget(self.frame_10, 4, 0, 1, 1)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "模拟主软件触发"))
        self.label.setText(_translate("Form", "从Mes获取条码"))
        self.label_2.setText(_translate("Form", "条码:"))
        self.line_pcb_sn.setText(_translate("Form", "pcb_barcode001"))
        self.btn_get_sn.setText(_translate("Form", "模拟触发"))
        self.label_5.setText(_translate("Form", "发送设备状态"))
        self.label_8.setText(_translate("Form", "设备状态:"))
        self.combo_device_status.setItemText(0, _translate("Form", "进板"))
        self.combo_device_status.setItemText(1, _translate("Form", "开始检测"))
        self.combo_device_status.setItemText(2, _translate("Form", "停止检查"))
        self.combo_device_status.setItemText(3, _translate("Form", "出板"))
        self.combo_device_status.setItemText(4, _translate("Form", "安全门"))
        self.combo_device_status.setItemText(5, _translate("Form", "调试"))
        self.combo_device_status.setItemText(6, _translate("Form", "数据超时"))
        self.combo_device_status.setItemText(7, _translate("Form", "板卡 NG"))
        self.combo_device_status.setItemText(8, _translate("Form", "紧急故障"))
        self.combo_device_status.setItemText(9, _translate("Form", "流程错误"))
        self.combo_device_status.setItemText(10, _translate("Form", "直通率告警"))
        self.combo_device_status.setItemText(11, _translate("Form", "Marker 错误"))
        self.combo_device_status.setItemText(12, _translate("Form", "其他错误"))
        self.btn_send_device_status.setText(_translate("Form", "模拟触发"))
        self.label_6.setText(_translate("Form", "发送数据 (数据包路径也可直接复制过来)"))
        self.send_type_inspector.setText(_translate("Form", "检测完发送"))
        self.send_type_repair.setText(_translate("Form", "复判完发送"))
        self.label_7.setText(_translate("Form", "数据包路径:"))
        self.btn_select_review_path.setText(_translate("Form", "选择文件夹"))
        self.btn_send_data.setText(_translate("Form", "模拟触发"))
        self.label_3.setText(_translate("Form", "条码校验"))
        self.label_4.setText(_translate("Form", "条码:"))
        self.line_sn_list.setText(_translate("Form", "sn001,sn002,sn003"))
        self.btn_check_sn.setText(_translate("Form", "模拟触发"))
        self.label_10.setText(_translate("Form", "日志打印"))
        self.btn_clear_log.setText(_translate("Form", "清除日志"))
        self.label_11.setText(_translate("Form", "发送设备状态v3"))
        self.label_12.setText(_translate("Form", "设备状态:"))
        self.combo_device_status_v3.setItemText(0, _translate("Form", "1001-->进板"))
        self.combo_device_status_v3.setItemText(1, _translate("Form", "1002-->启动"))
        self.combo_device_status_v3.setItemText(2, _translate("Form", "1003-->暂停"))
        self.combo_device_status_v3.setItemText(3, _translate("Form", "1004-->出板"))
        self.combo_device_status_v3.setItemText(4, _translate("Form", "1005-->换线"))
        self.combo_device_status_v3.setItemText(5, _translate("Form", "2001-->急停"))
        self.combo_device_status_v3.setItemText(6, _translate("Form", "2002-->安全门报警"))
        self.combo_device_status_v3.setItemText(7, _translate("Form", "3001-->待料（空闲）"))
        self.combo_device_status_v3.setItemText(8, _translate("Form", "3002-->出板超时"))
        self.combo_device_status_v3.setItemText(9, _translate("Form", "3003-->条码校验过站失败"))
        self.combo_device_status_v3.setItemText(10, _translate("Form", "3004-->上传检测信息失败"))
        self.combo_device_status_v3.setItemText(11, _translate("Form", "3005-->磁盘已满"))
        self.combo_device_status_v3.setItemText(12, _translate("Form", "3006-->掉板"))
        self.combo_device_status_v3.setItemText(13, _translate("Form", "3007-->EAP告警"))
        self.combo_device_status_v3.setItemText(14, _translate("Form", "4001-->直通率告警"))
        self.combo_device_status_v3.setItemText(15, _translate("Form", "4002-->Mark点错误"))
        self.combo_device_status_v3.setItemText(16, _translate("Form", "4003-->板卡NG"))
        self.combo_device_status_v3.setItemText(17, _translate("Form", "5001-->风扇停转"))
        self.combo_device_status_v3.setItemText(18, _translate("Form", "5002-->相机连接失败"))
        self.combo_device_status_v3.setItemText(19, _translate("Form", "0001-->蜂鸣报警解除"))
        self.combo_device_status_v3.setItemText(20, _translate("Form", "1011-->检测中"))
        self.combo_device_status_v3.setItemText(21, _translate("Form", "1012-->检测第二段"))
        self.combo_device_status_v3.setItemText(22, _translate("Form", "1013-->人工复检"))
        self.combo_device_status_v3.setItemText(23, _translate("Form", "1014-->人工复检"))
        self.combo_device_status_v3.setItemText(24, _translate("Form", "3010-->等待进板"))
        self.combo_device_status_v3.setItemText(25, _translate("Form", "1021-->主软件启动"))
        self.combo_device_status_v3.setItemText(26, _translate("Form", "1022-->主软件退出"))
        self.combo_device_status_v3.setItemText(27, _translate("Form", "4010-->扫码失败"))
        self.btn_device_status_v3.setText(_translate("Form", "模拟触发"))
        self.label_9.setText(_translate("Form", "发送空闲状态"))
        self.btn_send_idle_status.setText(_translate("Form", "模拟触发"))

