# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/17 下午4:16
# Author     ：sch
# version    ：python 3.8
# Description：中强
"""
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "zhongqiang release v1.0.0.1",
        "device": "203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-17 16:40  上传数据
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/WebService/MesATEApi.asmx/CollectATESN"
        },
        "emp_no": {
            "ui_name": "工号",
            "value": ""
        },
        "resource": {
            "ui_name": "资源名称",
            "value": ""
        },
        "station": {
            "ui_name": "工序名称",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        resource = data_vo.get_value_by_cons_key("resource")
        station = data_vo.get_value_by_cons_key("station")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            ng_code_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    ng_code_list.append(comp_entity.repair_ng_code)

            result = board_entity.get_repair_result()
            board_param = {
                "empNo": emp_no,
                "sn": barcode,
                "resource": resource,
                "station": station,
                "ateNcData": f"{result};{';'.join(ng_code_list)}",
            }
            ret = xrequest.RequestUtil.post_form(api_url, board_param, to_json=False)
            if ret != "OK":
                return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret}")

        return self.x_response()
