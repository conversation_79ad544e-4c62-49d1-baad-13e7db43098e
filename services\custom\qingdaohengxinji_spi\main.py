# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/6 上午8:47
# Author     ：sch
# version    ：python 3.8
# Description：青岛恒新基SPI
"""

from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "qingdaohengxinji_spi release v1.0.0.5",
        "device": "630",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-06 09:37  上传测试数据到mes
date: 2023-09-20 11:51  需求变更，详情见文档
date: 2023-09-21 16:25  根据success来判断接口是否返回OK
date: 2023-09-25 16:16  bugfix
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "machine_name": {
            "ui_name": "机器名称",
            "value": ""
        },

        "login_username": {
            "ui_name": "登录账户",
            "value": ""
        },
        "program_ps": {
            "ui_name": "程序说明",
            "value": ""
        },
        # "check_obj": {
        #     "ui_name": "闸口校验对象",
        #     "value": ""
        # },
        "station": {
            "ui_name": "当前工作站编码",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员工编号",
            "value": ""
        },
    }

    combo = {
        "board_side": {
            "ui_name": "面别",
            "item": ["T", "B"],
            "value": "T"
        },
    }

    other_form = {
        "log_number_hx": {
            "ui_name": "单次日志输出(字符)",
            "value": "300000"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        board_side = data_vo.get_value_by_cons_key("board_side")
        login_username = data_vo.get_value_by_cons_key("login_username")
        program_ps = data_vo.get_value_by_cons_key("program_ps")
        station = data_vo.get_value_by_cons_key("station")
        operator = data_vo.get_value_by_cons_key("operator")
        log_number_hx = data_vo.get_value_by_cons_key("log_number_hx")

        try:
            log_number_hx = int(log_number_hx)
        except Exception as err:
            return self.x_response("false", f"单次日志输出(字符)必须为数字，error：{err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_sn_list = []
        comp_total_number = 0
        comp_pass_number = 0
        comp_repass_number = 0
        comp_ng_number = 0

        comp_pad_data, fixture_barcode = pcb_entity.get_pad_test_data()  # 锡膏数据
        self.log.info(f"共有主pad点：{len(comp_pad_data)}")

        # 各种上下限值
        a_min = ""
        a_max = ""
        v_min = ""
        v_max = ""
        h_min = ""
        h_max = ""
        sx_min = ""
        sx_max = ""
        sy_min = ""
        sy_max = ""

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            comp_list = []

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_total_number += board_entity.comp_total_number
            comp_pass_number += (board_entity.comp_total_number - board_entity.comp_robot_ng_number)
            comp_repass_number += (board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number)
            comp_ng_number += board_entity.comp_repair_ng_number

            # if not pcb_sn and barcode:
            #     pcb_sn = barcode

            board_sn_list.append(f"{barcode}_{board_no}")

            for comp_entity in board_entity.yield_comp_entity():

                # 获取 上下限
                if not all((a_min, a_max,
                            v_min, v_max,
                            h_min, h_max,
                            sx_min, sx_max,
                            sy_min, sy_max,
                            )):
                    # 但凡有一个空值，就会进来
                    self.log.info(f"获取上下限值....")
                    for alg in comp_entity.yield_alg_entity():
                        test_name = alg.test_name
                        alg_max = alg.max_threshold
                        alg_min = alg.min_threshold

                        if test_name == "Area":
                            a_min = alg_min
                            a_max = alg_max
                        elif test_name == "Volume":
                            v_min = alg_min
                            v_max = alg_max
                        elif test_name == "RealHeight":
                            h_min = alg_min
                            h_max = alg_max
                        elif test_name == "XOffset":
                            sx_min = alg_min
                            sx_max = alg_max
                        elif test_name == "YOffset":
                            sy_min = alg_min
                            sy_max = alg_max

                comp_id = comp_entity.comp_id
                comp_uuid = comp_id.replace("{", "").replace("}", "")

                pad_list = comp_pad_data.get(comp_uuid, [])

                for pad in pad_list:
                    a = pad.get('a')
                    v = pad.get('v')
                    h = pad.get('rh')
                    sx = pad.get('sx')
                    sy = pad.get('sy')

                    comp_list.append({
                        "ModuleNo": board_no,
                        "ComponentId": comp_entity.designator,
                        "BaocodeId": pcb_sn,
                        "Result": comp_entity.get_final_result("G", "P", "N"),
                        "Norm": "",
                        "Volume": v,
                        "MaxHeight": h_max,
                        "Height": h,
                        "Area": a,
                        "OffsetX": sx,
                        "OffsetY": sy,
                        "Bridge": "",
                        "VOL_MIN": v_min,
                        "VOL_MAX": v_max,
                        "HEIGHT_LOWRate": "",
                        "HEIGHT_HIGHRate": "",
                        "HEIGHT_LOW": h_min,
                        "HEIGHT_HIGH": h_max,
                        "AREA_MIN": a_min,
                        "AREA_MAX": a_max,
                        "ProgramOffsetX": f"{sx_min}-{sx_max}",
                        "ProgramOffsetY": f"{sy_min}-{sy_max}",
                        "ProgramBridge": ""
                    })

            data_param = {
                "SPI_RESULTFLAG": pcb_entity.get_repair_result("PASS", "FAIL"),
                "PanelSn": f"{pcb_sn}_1",
                # "ChildSn": ",".join(board_sn_list),
                "ChildSn": f"{barcode}_{board_no}",
                "MachineName": machine_name,
                "ProgramName": pcb_entity.project_name,
                "ProcessFace": board_side,
                "TrackNo": str(pcb_entity.track_index),
                "Account": login_username,
                "ProgramTotalElements": comp_total_number,
                "ProgramTotalPad": comp_total_number,
                "TotalBoards": pcb_entity.board_count,
                "DeviceGoodElements": comp_pass_number,
                "LocalGoodElements": comp_repass_number,
                "LocalNGElements": comp_ng_number,
                "DeviceGoodPad": comp_pass_number,
                "LocalGoodPad": comp_repass_number,
                "LocalNGPad": comp_ng_number,
                "StartTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "EndTime": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
                "TotalBoardCheckCT": pcb_entity.get_cycle_time(),
                "PCBLongAndWide": "",
                "ProgramDes": program_ps,
                "DetailEntries": comp_list,
                "WorkStation": station,
                "EmpCode": operator
            }

            ret = xrequest.RequestUtil.post_json(api_url, data_param, log_number=log_number_hx)

            # if str(ret.get("code")) != "0":
            if not ret.get("success"):
                error_msg = f"mes接口异常，上传测试数据失败，error：{ret.get('message')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
