# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2024/11/29 上午9:58
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
  <soap12:Body>
    <BindCarrierAndPcbV2Response xmlns="WWW.SUNWODA.COM">
      <BindCarrierAndPcbV2Result>FALSENG:获取制令单/设备的工艺流程信息出错:11</BindCarrierAndPcbV2Result>
    </BindCarrierAndPcbV2Response>
  </soap12:Body>
</soap12:Envelope>"""

if __name__ == '__main__':
    root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    print(root1[0][0][0].text)
