#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2024/12/3
# Author: sunchangheng
import urllib.parse
from urllib.parse import urljoin

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError
from botocore.exceptions import NoCredentialsError

# 配置信息 -----sch-----------
# endpoint_url = "https://s3.ap-southeast-2.amazonaws.com"
# aws_access_key_id = '********************'
# aws_secret_access_key = 'TXBHnwlec0y8X0KZVcKRPzbWU7sgUyHvrYroonaB'
# region_name = 'ap-southeast-2'

# 配置信息 -----shenzhenxinwangda-----------
# 地址：lxeds.sunwoda.com:12001
# 访问秘钥：4EQCFXZ4MTTYSSYM8BHE
# 安全秘钥：pYbgu0Kw7VV0G9T0RJHqlLygW9JE2dlpD1W4RTqT
endpoint_url = "http://lxeds.sunwoda.com:12001"
aws_access_key_id = '4EQCFXZ4MTTYSSYM8BHE'
aws_secret_access_key = 'pYbgu0Kw7VV0G9T0RJHqlLygW9JE2dlpD1W4RTqT'
region_name = 'us-east-1'

# endpoint_url = "http://zzzx.zhhcp.sunwoda.com"
# aws_access_key_id = "enp6eA=="
# aws_secret_access_key = "47f992d5f604ef025470821d86da8317"
#
# region_name = 'us-east-1'

# 创建S3客户端
# s3_client = boto3.client(
#     's3',
#     aws_access_key_id=aws_access_key_id,
#     aws_secret_access_key=aws_secret_access_key,
#     endpoint_url=endpoint_url,
# )

session = boto3.Session(
    aws_access_key_id=aws_access_key_id,  # 替换为你的访问密钥
    aws_secret_access_key=aws_secret_access_key  # 替换为你的秘密密钥
)

s3_client = session.client(
    's3',
    endpoint_url=endpoint_url,  # 如果是 AWS S3，则不需要此参数
    config=Config(
        s3={'addressing_style': 'path'},  # 使用路径样式 URL
        # signature_version='s3v4'  # 如果适用，可以指定签名版本
    ),
    region_name=region_name,  # 替换为你的实际区域
    # aws_access_key_id=aws_access_key_id,  # 替换为你的访问密钥
    # aws_secret_access_key=aws_secret_access_key  # 替换为你的秘密密钥
)

# ret = s3_client.get_object_acl(Bucket="my-new-bucket", Key="COMP1039_1039_2.png")
# print("ret", ret)


# 查看存储桶列表
def list_buckets():
    try:
        response = s3_client.list_buckets()
        buckets = [bucket['Name'] for bucket in response['Buckets']]
        return buckets
    except (NoCredentialsError, ClientError) as e:
        print(f"Error listing buckets: {e}")
        return []


# 查看某个存储桶下的文件
def list_objects_in_bucket(bucket_name):
    try:
        response = s3_client.list_objects_v2(Bucket=bucket_name)
        if 'Contents' in response:
            return [obj['Key'] for obj in response['Contents']]
        else:
            return []
    except (NoCredentialsError, ClientError) as e:
        print(f"Error listing objects in bucket {bucket_name}: {e}")
        return []


# 生成无签名的URL（仅适用于公开访问的存储桶和对象）
def generate_unsigned_url(bucket_name, object_key):
    return f"{endpoint_url}/{bucket_name}/{urllib.parse.quote_plus(object_key)}"


# 生成有签名的URL（适用于私有存储桶和对象）
def generate_signed_url(bucket_name, object_key, expiration=3600):  # 默认有效期为1小时
    try:
        response = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': object_key},
            ExpiresIn=expiration
        )
        return response
    except (NoCredentialsError, ClientError) as e:
        print(f"Error generating signed URL: {e}")
        return None


# def upload_file(file_path, bucket_name, object_key):
#     """上传文件到指定的存储桶中"""
#     try:
#         if not os.path.isfile(file_path):
#             print(f"File '{file_path}' does not exist.")
#             return None
#
#         with open(file_path, 'rb') as data:
#             response = s3_client.put_object(
#                 Bucket=bucket_name,
#                 Key=object_key,
#                 Body=data,
#                 ACL='public-read'  # 设置对象的 ACL 为公共可读
#             )
#             print("File uploaded successfully.")
#             print(response)
#             return response
#     except ClientError as e:
#         print(f"Error uploading file: {e}")
#         return None


def upload_to_s3(
        file_name,
        bucket,
        object_name=None,
        aws_access_key_id=None,
        aws_secret_access_key=None,
        region_name='us-east-1'
):
    """
    上传文件到指定的 S3 Bucket.

    :param file_name: 要上传的文件路径
    :param bucket: S3 Bucket 的名称
    :param object_name: 在 S3 中保存文件的对象名称 (默认为文件名)
    :param aws_access_key_id: AWS Access Key ID
    :param aws_secret_access_key: AWS Secret Access Key
    :param region_name
    :return: 如果上传成功，返回 True；否则返回 False
    """
    print(f"上传图片中，{bucket=} {object_name=}")

    try:
        # 如果没有指定 object_name，则使用 file_name 作为对象名
        if object_name is None:
            object_name = file_name

        # 创建 S3 客户端
        s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
        )

        try:
            # 上传文件
            with open(file_name, 'rb') as data:
                s3_client.upload_fileobj(data, bucket, object_name)
                print(f"File {file_name} uploaded to bucket {bucket_name} as {object_name}.")
                print("上传完毕")

            # 设置对象ACL为公共读取
            s3_client.put_object_acl(Bucket=bucket, Key=object_name, ACL='public-read')

            # 构建永久下载链接
            print("格式1------------")
            download_url = f"http://{bucket}.s3.{region_name}.amazonaws.com/{object_name}"
            print(f"download url: {download_url}")

            # 生成无签名的URL（注意：对象必须是公开的）
            print("格式2------------")
            unsigned_url = generate_unsigned_url(bucket, object_name)
            print("Unsigned URL:", unsigned_url)

            # 生成有签名的URL（适用于私有对象）
            print("格式3------------")
            signed_url = generate_signed_url(bucket, object_name, expiration=3600)  # 1小时有效期
            print("Signed URL:", signed_url)

            print("================end====")
            # print(f"File {file_name} uploaded to {bucket}/{object_name} and made public.")
            # print(f"download url: {download_url}")
            # print(f"File {file_name} uploaded to {bucket}/{object_name}")
            return True
        except FileNotFoundError:
            print(f"The file {file_name} was not found.")
            return False
        except NoCredentialsError:
            print("Credentials not available.")
            return False
    except Exception as err:
        print(f"上传图片失败,error: {err}")
        return False


# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xwd_module.py
# Time       ：2024/12/1 下午11:32
# Author     ：sch
# version    ：python 3.8
# Description：存储桶模块
"""


def check_bucket_exists(s3_client, bucket_name):
    """检查桶是否存在"""
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        print(f"Bucket '{bucket_name}' exists.")
        return True
    except ClientError as e:
        print(e.response)
        error_code = int(e.response['Error']['Code'])
        if error_code == 404:
            print(f"Bucket '{bucket_name}' does not exist.")
            return False
        else:
            raise Exception(f"检查存储桶出错，error: {e.response}")  # 如果是其他错误，重新抛出异常


def create_bucket(s3_client, bucket_name):
    """创建一个新的桶（不设置任何特殊权限）"""
    try:
        response = s3_client.create_bucket(Bucket=bucket_name)
        print(f"Bucket '{bucket_name}' created successfully. response: {response}")
        return True
    except ClientError as e:
        print(f"Error creating bucket: {e}")
        return False


def upload_file_and_get_public_url(
        s3_client,
        file_path,
        bucket_name,
        object_key,
        endpoint_url=""
):
    """上传文件到指定的存储桶中，并返回文件的永久访问链接"""
    try:
        with open(file_path, 'rb') as data:
            response = s3_client.put_object(
                Bucket=bucket_name,
                Key=object_key,
                Body=data,
                ACL='public-read'  # 设置对象的 ACL 为公共可读
            )
        print(f"File uploaded successfully. response: {response}")

        # 构建非签名的公共 URL
        public_url = urljoin(endpoint_url + '/', f'{bucket_name}/{object_key}')
        print(f"Public URL: {public_url}")
        return public_url
    except Exception as e:
        print(f"Error uploading file: {e}")
        return ""


def upload_image_to_s3(
        endpoint_url,
        aws_access_key_id,
        aws_secret_access_key,
        file_path,
        bucket_name,
        object_key,
        region_name="",

) -> str:
    """
    主函数：上传图片到 S3 并获取永久访问链接

    :param endpoint_url: 服务器地址
    :param file_path: 要上传的文件路径
    :param bucket_name: S3 Bucket 的名称
    :param object_key: 在 S3 中保存文件的对象名称 (默认为文件名)
    :param aws_access_key_id: AWS Access Key ID
    :param aws_secret_access_key: AWS Secret Access Key
    :param region_name: AWS Region (默认是 'us-east-1')
    """

    # 创建 S3 客户端，并指定自定义终端节点和其他配置选项
    s3_client = boto3.client(
        's3',
        endpoint_url=endpoint_url,  # 自定义终端节点
        config=Config(
            s3={'addressing_style': 'path'},  # 使用路径样式 URL
            # signature_version='s3v4'  # 如果适用，可以指定签名版本
        ),
        region_name=region_name,  # 如果适用，可以指定区域名称
        aws_access_key_id=aws_access_key_id,  # 替换为你的访问密钥
        aws_secret_access_key=aws_secret_access_key  # 替换为你的秘密密钥
    )

    print("链接成功", s3_client)

    # 检查桶是否存在，如果不存在则创建

    is_exists = check_bucket_exists(s3_client, bucket_name)
    print(is_exists)

    if not check_bucket_exists(s3_client, bucket_name):
        if not create_bucket(s3_client, bucket_name):
            print("Failed to create bucket. Exiting.")
            return ""

    # 上传文件并获取永久访问链接
    return upload_file_and_get_public_url(
        s3_client,
        file_path,
        bucket_name,
        object_key,
        endpoint_url
    )


# # # 示例调用
# if __name__ == "__main__":
#     endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
#     aws_access_key_id_ = "enp6eA=="
#     aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"
#
#     file_path_ = './COMP1039_1039.png'  # 替换为你的图片路径
#     bucket_name_ = 'my-public-bucket'
#     object_key_ = 'COMP1039_1039_public.png'
#
#

"""
v4：
这是当前推荐的标准签名版本，适用于大多数新的 AWS 区域和服务。
它提供更强的安全性，并且能够处理更大的资源路径和查询字符串。
s3v4：
专门为 Amazon S3 设计的 V4 签名版本，适用于所有新区域。
当您需要对 S3 请求进行签名时，通常会使用此版本。
v2：
较旧的签名版本，主要用于一些早期的 AWS 区域和服务。
随着时间推移，越来越多的服务不再支持 V2，转而支持更安全的 V4。
s3：
这个选项对应于为 S3 设计的 V2 签名版本。
对于某些特定的老版本 S3 操作或旧区域可能仍然需要使用它。


测试时，传到桶ddb中
"""

# 示例使用
if __name__ == "__main__":
    # 0. 上传图片
    file_path = "./COMP1039_test.png"
    bucket_name = "ddb"
    object_key = "20241215/COMP1039_test.png"
    upload_to_s3(file_path, bucket_name,
                 object_key,
                 aws_access_key_id,
                 aws_secret_access_key,
                 region_name)
    #
    # ret_url = upload_image_to_s3(
    #     endpoint_url,
    #     aws_access_key_id,
    #     aws_secret_access_key,
    #     file_path,
    #     bucket_name,
    #     object_key,
    #     region_name
    # )
    #
    # print("格式1--------")
    # if ret_url:
    #     print(f"Image uploaded and available at: {ret_url}")
    #
    # print("格式2-----------")
    # # 生成无签名的URL（注意：对象必须是公开的）
    # unsigned_url = generate_unsigned_url(bucket_name, object_key)
    # print("Unsigned URL:", unsigned_url)
    #
    # # 生成有签名的URL（适用于私有对象）
    # signed_url = generate_signed_url(bucket_name, object_key, expiration=3600)  # 1小时有效期
    # print("Signed URL:", signed_url)
    # print("格式2-----------")

    # # # 查看存储桶列表
    # buckets = list_buckets()
    # print("Buckets:", buckets)
    # #
    # # 假设我们有一个名为'my-bucket'的存储桶
    # # bucket_name = 'sunchangheng-test'
    #
    # for bucket_name in buckets:
    #     print("------------每一个存储桶下的文件-----------")
    #
    #     # 查看存储桶中的文件
    #     objects = list_objects_in_bucket(bucket_name)
    #     print("Objects in bucket:", objects)
    #     print("----该存储桶下的文件为---------")
    #     for object_key in objects:
    #         # 假设我们有一个对象名为'my-object.txt'
    #         # object_key = 'my-object.txt'
    #         print(f"-----------文件-------------{object_key}")
    #
    #         # 生成无签名的URL（注意：对象必须是公开的）
    #         unsigned_url = generate_unsigned_url(bucket_name, object_key)
    #         print("Unsigned URL:", unsigned_url)
    #
    #         # 生成有签名的URL（适用于私有对象）
    #         signed_url = generate_signed_url(bucket_name, object_key, expiration=3600)  # 1小时有效期
    #         print("Signed URL:", signed_url)

# download url: https://sunchangheng-test.s3.ap-southeast-2.amazonaws.com//2024/12/15/avatar.jpeg
#     bucket_name = "ddb"
#     objects = list_objects_in_bucket(bucket_name)
#     print("Objects in bucket:", objects)
#     print("----该存储桶下的文件为---------")
#     for object_key in objects:
#         # 假设我们有一个对象名为'my-object.txt'
#         # object_key = 'my-object.txt'
#         print(f"-----------文件-------------{object_key}")
#
#         # 生成无签名的URL（注意：对象必须是公开的）
#         unsigned_url = generate_unsigned_url(bucket_name, object_key)
#         print("Unsigned URL:", unsigned_url)
#
#         # 生成有签名的URL（适用于私有对象）
#         signed_url = generate_signed_url(bucket_name, object_key, expiration=3600)  # 1小时有效期
#         print("Signed URL:", signed_url)


