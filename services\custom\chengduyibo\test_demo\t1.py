# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t1.py
# Time       ：2025/3/13 上午10:33
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json

from common import xutil

if __name__ == '__main__':
    ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ns2:excuteResponse xmlns:ns2="http://tempuri.org/">
            <return>
                {
                "code":"0000",
                "message":"xxxx",
                "pcbChecks":[
                {
                "lbId":"LB1001001",
                "num":"1"
                },
                {"lbId":"LB1001002",
                "num":"2"
                },
                {"lbId":"skip",
                "num":"3"
                }
                ]
                }
            </return>
        </ns2:excuteResponse>
    </soap:Body>
</soap:Envelope>"""

    root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    ret_str = root[0][0][0].text
    ret_json = json.loads(ret_str)
    print(ret_json)
