# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t2.py
# Time       ：2025/6/13 下午2:35
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <BindCarrierAndPcbResponse xmlns="WWW.SUNWODA.COM">
      <BindCarrierAndPcbResult>true</BindCarrierAndPcbResult>
    </BindCarrierAndPcbResponse>
  </soap:Body>
</soap:Envelope>"""

root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
ret_str = root[0][0][0].text
print(ret_str)
