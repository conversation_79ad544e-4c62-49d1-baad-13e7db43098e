# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : jw_module.py
# Time       ：2025/4/29 上午11:11
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import os
import struct
import zlib

from common.xutil import log


def compress_image_to_hex(image_path: str) -> bytes:
    """
    读取图片文件，压缩后返回十六进制字符串

    Args:
        image_path (str): 图片文件路径

    Returns:
        str: 压缩后的十六进制字符串
    """
    try:
        log.info(f"upload image: {image_path}")
        with open(image_path, 'rb') as file:
            image_data = file.read()  # 读取二进制数据

        compressed_data = zlib.compress(image_data)  # zlib 压缩
        header = struct.pack(">I", len(image_data))
        compressed_data = header + compressed_data

        hex_str = compressed_data.hex().encode('utf-8')

        file_extension = os.path.splitext(image_path)[1].replace(".", "")

        full_ret = b'EXT\v' + file_extension.encode('utf-8') + b'\tFILE' + b'\v' + hex_str
        log.info(f"single img bytes len: {len(full_ret)}")

        return full_ret

    except FileNotFoundError:
        log.warning(f"错误：文件 '{image_path}' 不存在！")
        return b''
    except Exception as e:
        log.warning(f"处理失败：{e}")
        return b''


def compress_image_list_to_hex(image_path_list: list) -> bytes:
    """
    读取图片文件，压缩后返回十六进制字符串
    Args:
        image_path_list (list): 图片文件路径
    Returns:
        str: 压缩后的十六进制字符串
    """
    ret_hex_s = b''

    for src_img in image_path_list:
        if not os.path.exists(src_img):
            log.info(f"图片文件 '{src_img}' 不存在！")
            continue

        ret_hex = compress_image_to_hex(src_img)
        ret_hex_s += (ret_hex + b'\r\n')

    ret_hex_s = ret_hex_s[:-2]
    log.info(f"img bytes len: {len(ret_hex_s)}")
    return ret_hex_s


if __name__ == '__main__':
    # img_list = [
    #     "/home/<USER>/aoi/run/results/333.999bak/20250512/T_20250512112412589_1_NG/images/ng/Chip_R/0/COMP1399_1399.png",
    #     "/home/<USER>/aoi/run/results/333.999bak/20250512/T_20250512112412589_1_NG/images/ng/Chip_R/0/COMP1379_1379.png",
    # ]
    # img_list = [
    #     "/home/<USER>/Desktop/jiweng/thumb.jpg",
    #     "/home/<USER>/Desktop/jiweng/COMP1004_1004.png",
    # ]
    img_list = [
        "/home/<USER>/Downloads/jiweng/2/b2.png"
    ]

    ret = compress_image_list_to_hex(img_list).decode('utf8')
    # print(ret)

    # with open("./remark_param.txt", "w") as f:
    #     f.write(ret)
    # print("done")

    data_param = {
        "runcard": "Test002",
        "user": "12345",
        "station": "AOITest",
        "testresult": "",
        "ateresult": "",
        "remark": ret,
    }

    data_str = json.dumps(data_param, ensure_ascii=False)
    print(data_str)

    with open("./remark_param2.txt", "w") as f:
        f.write(data_str)
