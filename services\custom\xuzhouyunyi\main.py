"""
# File       : main.py
# Time       ：2025/05/16 17:29
# Author     ："wxc"
# version    ：python 3.8
# Description：徐州云意
"""
from typing import Any

from zeep import Client

from common import xrequest, xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["徐州云意", "xuzhouyunyi"],
        "version": "release v1.0.0.4",
        "device": "AIS430 AIS630",
        "feature": ["获取条码", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-16 17:29  ATAOI_2019-39544：获取条码，上传数据
date: 2025-05-23 13:51  wevservice接口请求协议变更
date: 2025-05-16 17:29  接口报错信息字段修改
date: 2025-06-10 16:31  上传数据接口参数修改
"""
    }
    other_form = {
        "get_sn_url": {
            "ui_name": "条码验证接口 （获取条码）",
            "value": "",
        },
        "upload_url": {
            "ui_name": "结果上传接口",
            "value": "",
        }
    }
    form = {
        "res_code": {
            "ui_name": "资源号",
            "value": "",
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        get_sn_url = other_vo.get_value_by_cons_key("get_sn_url")
        res_code = other_vo.get_value_by_cons_key("res_code")

        pcb_sn = other_vo.get_pcb_sn()
        get_sn_param = {
            "barcode_b": pcb_sn,
            "res_code": res_code
        }

        client = Client(get_sn_url)
        ret = client.service.aoiBarcodeCheck(get_sn_param)
        if str(ret.code) != "1":
            return self.x_response("false", f"mes接口异常， error：{ret.msg}")

        items = ret.list_sbarcode
        sn_list = [item.barcode_s for item in items]
        return self.x_response("true", ",".join(sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        upload_url = data_vo.get_value_by_cons_key("upload_url")
        res_code = data_vo.get_value_by_cons_key("res_code")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "component_code": comp_entity.designator,
                    "ng_code": comp_entity.repair_ng_code,
                    "line_remark": comp_entity.repair_ng_str
                })

            board_param = {
                "barcode_b": pcb_sn,
                "barcode_s": barcode,
                "res_code": res_code,
                "result_date": test_time,
                "result": board_entity.get_final_result("GOOD", "GOOD", "NG"),
                "remark": "",
                "result_list": comp_data
            }
            client = Client(upload_url)
            ret = client.service.ictResultSubmit(kpInfo=board_param)
            if str(ret.result) != "success":
                return self.x_response("false", f"mes接口异常，上传数据失败！ error：{ret.message}")
        return self.x_response()
