
from flask import Flask, request, jsonify

app = Flask(__name__)


@app.route('/swiftmom/api/clientLogin', methods=['POST'])
def clientLogin():
    data = request.get_json()
    print(f'请求参数为:{data}')

    resp_ok = {
        "msgStr": "管理员|Swiftmes|edwin755473ABC|ZS|智硕|249||null|1",
        "msgId": '0',
        "token":
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2NjY2ODA4NjcsInVzZXJuYW1lIjoiMTIifQ.Od_"
            "UO4N3nJodps_6m_sAk_gWp02cdE1bA7LR9w2puQU"
    }

    resp_no = {
        "msgStr": "管理员|Swiftmes|edwin755473ABC|ZS|智硕|249||null|1",
        "msgId": 1,
        "token": ""
    }

    return jsonify(resp_ok)


@app.route('/swiftmom/api/getRelationPcbSeq', methods=['POST'])
def getRelationPcbSeq():
    data = request.get_json()
    print(f'请求参数为:{data}')

    response_ok = {
        "data": [
            {
                "pcbSeq": "barcode01"
            },
            {
                "pcbSeq": "barcode02"
            },
            {
                "pcbSeq": "barcode03"
            },
        ],
        "msgId": 0,
        "msgStr": ""
    }

    response_no = {
        "data": [],
        "msgId": 1,
        "msgStr": "条码不存在，请核实！"
    }

    return jsonify(response_ok)


@app.route('/swiftmom/api/checkRoute', methods=['POST'])
def checkRoute():
    data = request.get_json()
    print(f'请求参数为:{data}')
    response_ok = {"msgId": 0, "msgStr": ""}
    response_no = {"msgId": 1, "msgStr": "条码不存在，请核实"}
    return jsonify(response_ok)


@app.route('/swiftmom/api/createAOIData', methods=['POST'])
def createAOIData():
    data = request.get_json()
    print(f'请求参数为:{data}')
    response_ok = {
        "result": 0,
        "taskID": "createAoiData",
        "errcode": "",
        "message": ""
    }
    response_no = {
        "result": 1,
        "taskID": "createAoiData",
        "errcode": "",
        "message": "发送数据错误，请核实！"
    }
    return jsonify(response_ok)


if __name__ == '__main__':
    app.run()  # 运行app，默认端口为5000
