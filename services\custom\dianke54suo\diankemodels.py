import math

from common import xutil
from common.xutil import log


def parse_printer_xml(file_path):
    """
    解析印刷机的文档
    :param file_path:
    :return:
    """
    ret = xutil.XmlUtil.get_xml_root_by_file(file_path)
    process = ret.find("Process")
    product_id = process.find("Product_ID").text
    panel_id = process.find("Panel_ID").text
    print_direction = process.find("Print_Direction").text

    return product_id, panel_id, print_direction


def rotation_angle(offset_x: float, offset_y: float, *args):
    """
    计算相对于整版的偏移, 可能有两个角度的偏移
    :param offset_x: 单位mm
    :param offset_y: 单位mm
    :param args: 角度
    :return:
    """
    if not offset_x and not offset_y:
        return 0.0, 0.0

    r = math.sqrt(offset_x ** 2 + offset_y ** 2)

    s1 = offset_y / r
    s2 = offset_x / r

    a = math.asin(s1)
    a = a / math.pi * 180
    if s2 < 0:
        a = 180 - a

    a3 = a
    for angle in args:
        a3 += float(angle)

    a4 = a3 / 180 * math.pi  # 角度转弧度

    gx = math.cos(a4) * r
    gy = math.sin(a4) * r
    return gx, gy


def parse_comp(comp, pixel_width, pixel_height, comp_angle_parent=0, comp_uuid_parent=""):
    """
    解析器件数据
    :param comp:
    :return:
    """
    comp_uuid = comp.find("uuid").text

    if not comp_uuid_parent:
        comp_uuid_parent = comp_uuid  # 如果没有父节点，那把自己当成父节点

    comp_id = comp.find("id").text
    is_main_marker = comp.find("isMainMarker").text

    solder_paste_param = comp.find("SolderpasteParam")

    comp_angle_child = comp.find("geometry").find("angle").text

    has_inspect_detect = solder_paste_param.find("hasInspectSpecSPIDetect").text
    bridge = solder_paste_param.find("bridge").text
    result = solder_paste_param.find("result").text

    cx = float(comp.find("geometry").find("cx").text)
    cy = float(comp.find("geometry").find("cy").text)

    # 器件的宽与高，如果锡膏的偏移是%，则需要根据这两个参数算出um
    wid = float(comp.find("geometry").find("width").text)
    hei = float(comp.find("geometry").find("height").text)

    # 偏移坐标
    offset_x = comp.find("image-offset").find("x").text
    offset_y = comp.find("image-offset").find("y").text

    # 锡膏的偏移坐标 -------------start----
    solder_offset = solder_paste_param.find("offset").text
    # log.logger.debug(f"--------------------1111111: {solder_offset}, comp: {comp_id}")
    solder_offset_str = solder_offset.split(" ")
    x_offset, y_offset = solder_offset_str[0], solder_offset_str[-1]
    x_offset = x_offset.rsplit(":", 1)[1]
    y_offset = y_offset.rsplit(":", 1)[1]

    solder_direction = solder_paste_param.find("offsetDirection").text
    solder_direction_str = solder_direction.split(" ")
    x_direct, y_direct = solder_direction_str[0], solder_direction_str[-1]
    x_direct = x_direct.rsplit(":", 1)[1]
    y_direct = y_direct.rsplit(":", 1)[1]

    if "um" in x_offset:
        x_offset = float(x_offset.replace("um", ""))
        y_offset = float(y_offset.replace("um", ""))
    else:
        x_offset = wid * (float(x_offset.replace("%", "")) / 100)
        y_offset = hei * (float(y_offset.replace("%", "")) / 100)

    # 角度需要矫正
    # log.logger.info(f"----comp id: {comp_id}")
    # log.logger.info(f"矫正前参数：{x_offset=} {y_offset=}  {comp_angle_child=} {comp_angle_parent=}")
    x_offset, y_offset = rotation_angle(x_offset, y_offset, comp_angle_child, comp_angle_parent)
    # log.logger.info(f"校正后的参数：{x_offset=} {y_offset=}")

    if "-" in x_direct:
        # 相减
        solder_offset_x = cx - x_offset
        gx = 0 - x_offset
    else:
        # 相加
        solder_offset_x = cx + x_offset
        gx = 0 + x_offset

    if "-" in y_direct:
        solder_offset_y = cy - y_offset
        gy = 0 - y_offset
    else:
        solder_offset_y = cy + y_offset
        gy = 0 + y_offset

    if is_main_marker == "true":
        if "-" in offset_x:
            # 需要相减
            x_pixel = float(offset_x.replace("-", "")) * pixel_width

            offset_x_ret = cx - x_pixel
        else:
            x_pixel = float(offset_x) * pixel_width
            offset_x_ret = cx + x_pixel

        if "-" in offset_y:
            # 需要相+
            y_pixel = float(offset_y.replace("-", "")) * pixel_height
            offset_y_ret = cy - y_pixel
        else:
            y_pixel = float(offset_y) * pixel_height
            offset_y_ret = cy + y_pixel
    else:
        offset_x_ret = 0
        offset_y_ret = 0

    # log.logger.info(f"-----------------------------------------------{comp_id}")
    # log.logger.info(f"cx: {cx}  cy:{cy}")
    # log.logger.info(f"gx: {gx}  gy:{gy}")

    return {
        "comp_id": comp_id,
        "comp_uuid_parent": comp_uuid_parent,
        "comp_uuid": comp_uuid,
        "is_main_marker": True if is_main_marker == "true" else False,
        "has_inspect_detect": True if has_inspect_detect == "true" else False,
        "is_bridge": True if bridge != "OK" else False,
        "is_no_paste": True if result == "11" else False,
        "cx": round(cx / 1000, 4),
        "cy": round(cy / 1000, 4),
        "offset_x": round(offset_x_ret / 1000, 4),
        "offset_y": round(offset_y_ret / 1000, 4),
        "solder_offset_x": round(solder_offset_x / 1000, 4),
        "solder_offset_y": round(solder_offset_y / 1000, 4),
        "gx": round(gx / 1000, 4),
        "gy": round(gy / 1000, 4),
        "area": round(wid * hei / 1000, 4)  # 器件大小， 面积
    }


def parse_report_xml(report_xml):
    """
    解析report.xml
    :param report_xml:
    :return:
    """
    ret_data = {}
    root = xutil.XmlUtil.get_xml_root_by_file(report_xml)
    boards = root.find("boards")

    pixel_width = float(root.find("orignal-pixel-size").find("width").text)
    pixel_height = float(root.find("orignal-pixel-size").find("height").text)

    # 偏移与旋转中心从AOI获取
    offset_item = root.find("Offset_Correction")

    if offset_item:
        oc_x = offset_item.find("X").text
        oc_y = offset_item.find("Y").text
        theta_item = offset_item.find("Theta")
        theta_attr = theta_item.attrib

        oc_cor_x = theta_attr.get("CoR_X")
        oc_cor_y = theta_attr.get("CoR_Y")
        oc_theta = theta_item.text
    else:
        log.warning(f"从report.xml没有解析出<Offset_Correction>，相关值直接填0")

        oc_x = 0
        oc_y = 0
        oc_cor_x = 0
        oc_cor_y = 0
        oc_theta = 0

    # 旋转中心：
    board_geometry = root.find("geometry")
    b_cx = round(float(board_geometry.find("cx").text) / 1000, 3)
    b_cy = round(float(board_geometry.find("cy").text) / 1000, 3)

    for board in boards:
        comps = board.find("components")

        for comp in comps:
            comp_data = parse_comp(comp, pixel_width, pixel_height)

            comp_angle = comp.find("geometry").find("angle").text

            comp_uuid_parent = comp_data["comp_uuid"]

            ret_data[comp_uuid_parent] = comp_data
            children_list = comp.find("children")

            for children in children_list:
                # 角度需要往上找
                comp_data = parse_comp(children, pixel_width, pixel_height, comp_angle, comp_uuid_parent)
                comp_uuid = comp_data["comp_uuid"]
                ret_data[comp_uuid] = comp_data

    return ret_data, (b_cx, b_cy), (oc_x, oc_y, oc_cor_x, oc_cor_y, oc_theta)