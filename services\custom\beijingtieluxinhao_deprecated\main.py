# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/28 上午9:36
# Author     ：sch
# version    ：python 3.8
# Description：北京铁路信号 Deprecated!!! 由于需要配合C++配置器保存标记的整板图，所以次版本作废
"""
import os
import shutil
from collections import defaultdict
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine

# filter_sn_list = []

global_data = {}


class Engine(BaseEngine):
    version = {
        "title": "beijingtieluxinhao deprecated v1.0.0.13",
        "device": "430,501",
        "feature": ["获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-28 10:56  获取条码，条码校验，上传数据
date: 2023-10-04 21:58  保存图片,json
date: 2023-10-11 11:27  需求变更
date: 2023-10-11 14:53  bugfix 拷贝底面大图
date: 2023-10-13 14:53  不良描述传中文，本地保存压缩包
date: 2023-11-17 15:25  条码校验失败，不发送数据到mes
date: 2023-11-30 18:21  需求变更
date: 2023-12-12 15:17  条码校验失败然后又条码校验成功后，可以发数据到Mes
""", }

    form = {
        "get_sn_url": {
            "ui_name": "获取条码接口URL",
            "value": ""
        },
        "check_sn_url": {
            "ui_name": "条码校验接口URL",
            "value": ""
        },
        "data_url": {
            "ui_name": "数据接口URL",
            "value": ""
        },
        "file_url": {
            "ui_name": "文件接口URL",
            "value": ""
        },

        "username": {
            "ui_name": "用户ID",
            "value": ""
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": ""
        },
        "factory_id": {
            "ui_name": "工厂ID",
            "value": ""
        },
        "station_id": {
            "ui_name": "工位ID",
            "value": ""
        },
        "line_id": {
            "ui_name": "线体",
            "value": "LINE_F"
        },
        "repair_station": {
            "ui_name": "复判工位",
            "value": "LINE_B_AOI_1"
        },
    }

    combo = {
        "board_side": {
            "ui_name": "板面",
            "item": ["A", "B"],
            "value": "A"
        },
        "upload_type1": {
            "ui_name": "校验失败上传mes",
            "item": ["上传", "不上传"],
            "value": "上传"
        },
        "upload_image_to_ftp": {
            "ui_name": "上传压缩包到FTP",
            "item": ["上传", "不上传"],
            "value": "上传"
        },
    }

    path = {
        "save_path": {
            "ui_name": "存储路径",
            "value": ""
        }
    }

    other_form = {
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "*************"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "sch_password"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "value": "/MES/SMT"
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        get_sn_url = other_vo.get_value_by_cons_key("get_sn_url")
        username = other_vo.get_value_by_cons_key("username")
        factory_id = other_vo.get_value_by_cons_key("factory_id")
        station_id = other_vo.get_value_by_cons_key("station_id")
        device_code = other_vo.get_value_by_cons_key("device_code")

        pcb_sn = other_vo.get_pcb_sn()

        param1 = {
            "factoryId": factory_id,
            "userId": username,
            "stationId": station_id,
            "deviceId": device_code,
            "snNumber": pcb_sn,
        }

        ret1 = xrequest.RequestUtil.post_json(get_sn_url, param1)
        if not ret1.get("flag"):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret1.get('msg')}")

        data_list = ret1.get('dataList', [])

        ret_sn = [item.get('snNumber', '') for item in data_list]

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_sn_url = other_vo.get_value_by_cons_key("check_sn_url")
        username = other_vo.get_value_by_cons_key("username")
        factory_id = other_vo.get_value_by_cons_key("factory_id")
        station_id = other_vo.get_value_by_cons_key("station_id")
        device_code = other_vo.get_value_by_cons_key("device_code")

        sn_list = other_vo.list_sn()

        sn_list2 = sn_list

        filter_sn_list = global_data.get("filter_sn_list", [])
        for sn in sn_list:
            param2 = {
                "factoryId": factory_id,
                "userId": username,
                "stationId": station_id,
                "deviceId": device_code,
                "snNumber": sn,
            }

            ret2 = xrequest.RequestUtil.post_json(check_sn_url, param2)
            if not ret2.get("flag"):
                self.log.info(f"old len: {len(filter_sn_list)}")

                filter_sn_list.extend(sn_list2)
                global_data["filter_sn_list"] = filter_sn_list[-500:]

                self.log.info(f"数据已缓存,len: {len(filter_sn_list)}")

                return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret2.get('msg')}")

            self.log.warning(f"整板请求一次接口做校验即可")
            break

        for ok_sn in sn_list2:
            if ok_sn in filter_sn_list:
                filter_sn_list.remove(ok_sn)

        global_data["filter_sn_list"] = filter_sn_list

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_url = data_vo.get_value_by_cons_key("data_url")
        username = data_vo.get_value_by_cons_key("username")
        factory_id = data_vo.get_value_by_cons_key("factory_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        device_code = data_vo.get_value_by_cons_key("device_code")
        save_path = data_vo.get_value_by_cons_key("save_path")
        board_side = data_vo.get_value_by_cons_key("board_side")
        line_id = data_vo.get_value_by_cons_key("line_id")
        repair_station = data_vo.get_value_by_cons_key("repair_station")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        upload_image_to_ftp = data_vo.get_value_by_cons_key("upload_image_to_ftp")
        file_url = data_vo.get_value_by_cons_key("file_url")

        upload_type1 = data_vo.get_value_by_cons_key("upload_type1")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name

        pcb_sn = pcb_entity.pcb_barcode

        if not save_path:
            return self.x_response("false", "请先选择存储路径!")

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"ftp端口号必须为数字，error：{err}")

        sn_list = []
        if pcb_sn and pcb_sn not in sn_list:
            sn_list.append(pcb_sn)

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            if barcode not in sn_list:
                sn_list.append(barcode)

        filter_sn_list = global_data.get("filter_sn_list", [])

        self.log.info(f"sn list: {sn_list}")
        for sn in sn_list:
            if sn in filter_sn_list:
                if upload_type1 == "不上传":
                    self.log.warning(f"校验失败后不上传数据到mes！")
                    return self.x_response()
                else:
                    self.log.warning(f"上传设置：{upload_type1}")

        comp_ng_img_data = defaultdict(list)

        board_data = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if barcode and not pcb_sn:
                pcb_sn = barcode

            if barcode and barcode not in sn_list:
                sn_list.append(barcode)

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():

                    repair_ng_str = comp_entity.repair_ng_str
                    repair_ng_code = comp_entity.repair_ng_code

                    custom_ng_str = xcons.AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("standard", repair_ng_str)

                    comp_data.append({
                        "badCode": repair_ng_code,
                        "badDescript": custom_ng_str,
                        "badMaterialCode": comp_entity.designator,
                        "badMaterialPosition": comp_entity.designator,
                    })

                    src_comp_img = comp_entity.image_path

                    if src_comp_img:
                        comp_ng_img_data[barcode].append({
                            "src_comp_img": src_comp_img,
                            "barcode": barcode,
                            "ng_str": custom_ng_str,
                            "ng_tag": comp_entity.designator
                        })

            board_data.append({
                "snNumber": barcode,
                "state": board_entity.get_repair_result("OK", "NG"),
                "badInfoLst": comp_data
            })

        param3 = {
            "factoryId": factory_id,
            "userId": username,
            "stationId": station_id,
            "deviceId": device_code,
            "snLst": board_data
        }

        time_start = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        p_name = f"{project_name}@{line_id}@{time_start}"
        full_path = f"{save_path}/{p_name}"

        xutil.FileUtil.ensure_dir_exist(full_path)

        # 1. 保存整板图 and json file
        pcb_image_list = pcb_entity.pcb_image
        self.log.info(f"pcb image list: {pcb_image_list}")

        if len(pcb_image_list) >= 1:
            self.log.info(f"开始保存整板图...")
            src_pcb_image1 = pcb_image_list[0]

            pcb_filename = f"{pcb_sn}_A_side.jpg"
            dst_pcb_image = f"{full_path}/{pcb_filename}"
            xutil.FileUtil.copy_file(src_pcb_image1, dst_pcb_image)

            pcb_filename2_tmp = ""
            if len(pcb_image_list) >= 2:
                # 存在第二张图片，保存第二张图
                self.log.info(f"开始保存第二张整板大图")
                src_pcb_image2 = pcb_image_list[1]

                pcb_filename2 = f"{pcb_sn}_B_side.jpg"
                dst_pcb_image2 = f"{full_path}/{pcb_filename2}"

                xutil.FileUtil.copy_file(src_pcb_image2, dst_pcb_image2)

                pcb_filename2_tmp = f",{pcb_filename2}"

                board_side = "A+B"

            pcb_json = {
                "snNumberList": sn_list,
                "remark": "整版图上传",
                "judgeStation": repair_station,
                "pcbSurface": board_side,
                "picFileName": f"{pcb_filename}{pcb_filename2_tmp}",
            }

            dst_pcb_json = f"{full_path}/{project_name}.json"

            xutil.FileUtil.dump_json_to_file(dst_pcb_json, pcb_json)

        # 2. 保存器件图
        if comp_ng_img_data:
            # self.log.info(f"开始保存不良图片, 数量[{len(comp_ng_img_list)}]...")

            for barcode, ng_list_ in comp_ng_img_data.items():
                ng_img_path = f"{full_path}/{barcode}"
                xutil.FileUtil.ensure_dir_exist(ng_img_path)

                ng_file_list = []

                for item in ng_list_:
                    ng_str = item.get("ng_str")
                    ng_tag = item.get("ng_tag")

                    comp_dst_file = f"{barcode}_{ng_str}_{ng_tag}_@231_side.png"
                    ng_file_list.append(comp_dst_file)
                    comp_dst_filepath = f"{ng_img_path}/{comp_dst_file}"

                    src_comp_img1 = item.get("src_comp_img")

                    # 保存ng器件图
                    xutil.FileUtil.copy_file(src_comp_img1, comp_dst_filepath)

                # 保存 json file
                comp_json = {
                    "snNumberList": [barcode],
                    "remark": "器件图上传",
                    "judgeStation": repair_station,
                    "pcbSurface": board_side,
                    "picFileName": ",".join(ng_file_list),
                }
                xutil.FileUtil.dump_json_to_file(f"{ng_img_path}/{time_start}_{barcode}.json", comp_json)

        # 开始压缩，并上传到ftp服务器
        cwd_path = os.getcwd()

        # xutil.FileUtil.ensure_dir_exist(f"{cwd_path}/cache_data")
        # 1. 压缩
        # zip_path = f"{cwd_path}/cache_data/{p_name}"
        zip_path = f"{save_path}/{p_name}"
        shutil.make_archive(zip_path, 'zip', full_path)
        self.log.info(f"文件夹：{full_path} 已压缩至 {f'{save_path}/{p_name}.zip'}")

        error_msg = ""
        try:
            if upload_image_to_ftp == "上传":
                # 2. 上传至ftp服务器,上传完删除
                ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
                ftp_client.login()
                ftp_client.cd_or_mkdir(ftp_path)

                ftp_client.upload_file(f"{zip_path}.zip", f"{p_name}.zip")
                ftp_client.close()
                self.log.info(f"ftp路径：{ftp_path}/{p_name}.zip")

            with open(f"{zip_path}.zip", "rb") as f:
                xrequest.RequestUtil.post_json(file_url, {}, files={"files": f})
                self.log.info(f"文件已通过http上传至接口，文件：{zip_path}.zip")

        except Exception as err:
            error_msg = f"ftp上传压缩包数据失败，error：{err}"

        try:
            shutil.rmtree(full_path)
            self.log.info(f"Directory '{full_path}' has been successfully deleted.")
        except Exception as err:
            self.log.warning(f"Error deleting directory: {err}")

        ret3 = xrequest.RequestUtil.post_json(data_url, param3)
        if not ret3.get("flag"):
            error_msg = f"mes接口异常，上传数据失败，error：{ret3.get('msg')}"
            # return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret3.get('msg')}")

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
