# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/2 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：浙江欣旺达 机型401C
"""
import json
import os
import sqlite3
import threading
import time
import traceback
from datetime import datetime
from queue import Queue
from typing import Any

import urllib3

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import x_response, log
from engine.MesEngine import ErrorMapEngine
from services.custom.shenzhenxinwangda_jrx_4l_431.xwd_module import upload_image_to_s3, concat_ng_info_to_pcb_image, \
    concat_ng_info_to_board_image
from vo.mes_vo import DataVo, OtherVo, ButtonVo, ComboVo

# 超时时间
DEFAULT_TIMEOUT = 20

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_alg_data(review_path: str) -> dict:
    """
    获取算法数据
    """
    alg_db_path = f"{review_path}/AlgorithmInfo.db"

    if not os.path.exists(alg_db_path):
        log.warning(f"{alg_db_path}不存在！")
        return {}

    conn = sqlite3.connect(alg_db_path)
    data_map = {}

    cur = conn.cursor()

    try:
        cur.execute("SELECT * FROM compalgorithmdata;")

        # 获取所有行的结果列表
        rows = cur.fetchall()

        # 遍历结果并打印每一行
        for row in rows:
            data_map[str(row[1])] = {
                "alg_name": row[3],
                "alg_val": json.loads(row[4])
            }
    finally:
        cur.close()
        conn.close()

    log.info(f"算法数据： {len(data_map)}")

    return data_map


class ThreadPool:
    def __init__(self, max_workers=5):
        self.max_workers = max_workers  # 控制最大线程数
        self.task_queue = Queue()  # 任务队列
        self.workers = []  # 工作线程列表
        # self.results = {}  # 存储每个任务的执行结果
        self.lock = threading.Lock()  # 线程安全
        self.completed_event = threading.Event()
        self.active_tasks = 0
        self._init_workers()  # 初始化工作线程
        # self.result_queue = Queue()
        self.done_queue = Queue()  # 专门用来计数，用来等待所有任务完成

    def _init_workers(self):
        """初始化工作线程池"""
        for _ in range(self.max_workers):
            worker = threading.Thread(target=self._worker, daemon=True)
            worker.start()
            self.workers.append(worker)

    def _worker(self):
        """工作线程的主循环"""
        while True:
            try:
                task_id, func, args, kwargs = self.task_queue.get()
                with self.lock:
                    self.active_tasks += 1
                # try:
                # 重试失败的任务,最多重试3次
                for attempt in range(3):
                    try:
                        log.info(f"第{attempt + 1}次上传该文件！")
                        ret_url = func(*args, **kwargs)  # 找不到图片
                        # self.results[task_id] = {
                        #     'status': 'success',
                        #     'result': ret_url
                        # }

                        self.done_queue.put((task_id, ret_url))
                        break
                    except Exception as e:
                        if attempt == 2:  # 最后一次尝试失败
                            # self.results[task_id] = {
                            #     'status': 'error',
                            #     'result': ""
                            # }
                            log.warning(f"三次都上传失败，error: {e}")
                            self.done_queue.put((task_id, ""))

                        time.sleep(0.1)
                        log.warning(f"上传失败，error:{traceback.format_exc()}")

                self.task_queue.task_done()
                with self.lock:
                    self.active_tasks -= 1
                    if self.active_tasks == 0 and self.task_queue.empty():
                        self.completed_event.set()
            except Exception as e:
                log.error(f"Worker thread error: {e}")
                log.warning(traceback.format_exc())
                continue

    def add_task(self, task_id, func, *args, **kwargs):
        """添加任务到队列"""
        self.task_queue.put((task_id, func, args, kwargs))

    def clear_cache(self):
        """
        删除一些状态
        """
        self.done_queue = Queue()
        # self.results.clear()
        log.info(f"线程缓存已清除！")


class Engine(ErrorMapEngine):
    version = {
        "customer": ["浙江欣旺达", "zhejiangxinwang-401"],
        "version": "release v1.0.0.4",
        "device": "AIS401B-C",
        "feature": ["上传数据"],
        "author": "sch",
        "release": """
date: 2025-06-19 10:43  init
date: 2025-06-19 16:54  ATAOI_2019-40034: 基于深圳欣旺达401版本修改，只保留上传图片功能
date: 2025-06-25 15:00  上传整板原图，不需要上传标注图了
date: 2025-06-26 10:48  优化上传整板图
""", }

    other_combo = {

    }

    other_form = {
        "aws_endpoint_url": {
            "ui_name": "AWS Service Url",
            "value": "zzzxhcp.hzhcp.sunwoda.com",
        },
        "aws_access_key_id": {
            "ui_name": "AWS Access Key ID",
            "value": "enp6eGhjcA==",
        },
        "aws_secret_access_key": {
            "ui_name": "AWS Secret Access Key",
            "value": "24785287ae393901fe8c08477dbf7450",
        },
        "region_name": {
            "ui_name": "AWS RegionName",
            "value": "us-west-2",
        },
        "bucket_name": {
            "ui_name": "AWS BucketName",
            "value": "ZZZX-BL-5D1L-BL-SMT-01-YS-DEK-01",  # us-east-1
        },
    }

    form = {

    }

    combo = {

    }

    def __init__(self):
        super().__init__()
        self.thread_pool = ThreadPool(max_workers=8)

    def init_main_window(self, main_window, other_vo: OtherVo):
        pass

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:

        return self.x_response()

    def send_data_to_mes_deprecated(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        aws_access_key_id = data_vo.get_value_by_cons_key("aws_access_key_id")
        aws_secret_access_key = data_vo.get_value_by_cons_key("aws_secret_access_key")
        is_upload_img = data_vo.get_value_by_cons_key("is_upload_img")
        region_name = data_vo.get_value_by_cons_key("region_name")
        aws_endpoint_url = data_vo.get_value_by_cons_key("aws_endpoint_url")
        bucket_name_ui = data_vo.get_value_by_cons_key("bucket_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        err_msg_list = []

        date_tmp = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE1)
        date1 = date_tmp[:8]  # 年月日
        date2 = date_tmp[8:]  # 时分秒

        only_one_ng_code = ""
        ng_board_info = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # 拼板条码
            barcode = board_entity.barcode
            board_no = board_entity.board_no
            board_image_path = board_entity.board_image_path

            if not pcb_sn and barcode:
                pcb_sn = barcode

            # 收集该拼版的不良代码和不良描述
            ng_codes = []
            ng_descriptions = []

            for comp_entity in board_entity.yield_comp_entity():
                if not only_one_ng_code:
                    only_one_ng_code = comp_entity.repair_ng_str or comp_entity.robot_ng_str
                    self.log.info(f"only_one_ng_code : {only_one_ng_code}")

                # 用于收集整板图的不良器件的不良代码
                if comp_entity.is_repair_ng() and comp_entity.repair_ng_code not in ng_codes:
                    ng_codes.append(comp_entity.repair_ng_code)
                    ng_descriptions.append(f"{comp_entity.repair_ng_code}-{comp_entity.repair_ng_str}")

            if ng_codes:
                ng_board_info.append({
                    'board_no': board_no,
                    'ng_descriptions': ng_descriptions
                })

            if is_upload_img == "Yes":
                if board_image_path and os.path.exists(board_image_path):
                    board_image_path2 = ""

                    # 打开拼版图并创建新图片
                    try:
                        processed_filename = concat_ng_info_to_board_image(
                            board_image_path,
                            board_entity
                        )
                    except Exception as err:
                        self.log.error(f"第一次处理拼版图出错: {str(err)}")
                        self.log.error(traceback.format_exc())

                        try:
                            self.log.info(f"正在重新处理图片...")
                            board_image_path2 = os.path.splitext(board_image_path)[0] + "_mes2.jpg"
                            xutil.FileUtil.copy_file(
                                board_image_path,
                                board_image_path2
                            )
                            processed_filename = concat_ng_info_to_board_image(
                                board_image_path2,
                                board_entity
                            )

                        except Exception as err:
                            err_msg_list.append(f"第二次处理拼版{board_no}图片失败！err: {err}")
                            self.log.error(traceback.format_exc())

                            processed_filename = ""

                    board_result = board_entity.get_repair_result("OK", "NG")
                    if board_result == "OK":
                        dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{board_no}_{board_result}_{date1}{date2}.jpg"
                        self.log.info(f"board_result == OK 则 dst_filename : {dst_filename}")
                    else:
                        dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{board_no}_{board_result}_{date1}{date2}_{only_one_ng_code}.jpg"
                        self.log.info(f"board_result == NG 则 dst_filename : {dst_filename}")

                    try:
                        ret_url = upload_image_to_s3(
                            aws_endpoint_url,
                            aws_access_key_id,
                            aws_secret_access_key,
                            processed_filename,
                            bucket_name_ui,
                            dst_filename,
                            region_name,
                        )
                        self.log.info(f"拼板图上传成功！")

                        if ret_url:
                            xutil.FileUtil.remove_file(processed_filename)
                            xutil.FileUtil.remove_file(board_image_path)
                            xutil.FileUtil.remove_file(board_image_path.replace('_rect.jpg', '.jpg'))

                            if board_image_path2:
                                xutil.FileUtil.remove_file(board_image_path2)

                    except Exception as err:
                        err_msg_list.append(f"上传拼板图异常，err: {err}")
                        self.log.warning(traceback.format_exc())

                else:
                    self.log.warning(f"找不到需要上传的拼板图！")

        if is_upload_img == "Yes":
            # 获取带标注的401整板图
            original_filename = pcb_entity.get_image_rect_401()
            pcb_result = pcb_entity.get_repair_result("OK", "NG")
            original_filename2 = ""

            if original_filename:
                if "/T_" in original_filename:
                    board_side = "TOP"
                else:
                    board_side = "BOT"

                try:
                    processed_filename = concat_ng_info_to_pcb_image(
                        original_filename,
                        ng_board_info
                    )

                except Exception as err:
                    self.log.error(f"初次处理整板图失败: {str(err)}")
                    self.log.error(traceback.format_exc())

                    try:
                        self.log.info(f"正在重新处理图片...")
                        original_filename2 = os.path.splitext(original_filename)[0] + "_mes2.jpg"
                        xutil.FileUtil.copy_file(
                            original_filename,
                            original_filename2
                        )
                        processed_filename = concat_ng_info_to_pcb_image(
                            original_filename2,
                            ng_board_info
                        )

                    except Exception as err:
                        self.log.error(f"再次处理整板图片失败: {str(err)}")
                        self.log.error(traceback.format_exc())
                        err_msg_list.append(f"处理整板图失败！err: {err}")

                        processed_filename = ""

                if pcb_result == "OK":
                    dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{pcb_result}_{date1}{date2}.jpg"
                    self.log.info(f"pcb_result == OK 则 dst_filename : {dst_filename}")
                else:
                    dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{pcb_result}_{date1}{date2}_{only_one_ng_code}.jpg"
                    self.log.info(f"pcb_result == NG 则 dst_filename : {dst_filename}")

                try:
                    ret_url = upload_image_to_s3(
                        aws_endpoint_url,
                        aws_access_key_id,
                        aws_secret_access_key,
                        processed_filename,
                        bucket_name_ui,
                        dst_filename,
                        region_name,
                    )

                    if ret_url:
                        try:
                            xutil.FileUtil.remove_file(processed_filename)
                            xutil.FileUtil.remove_file(original_filename)
                            if original_filename2:
                                xutil.FileUtil.remove_file(original_filename2)

                            self.log.info(
                                f"成功删除带白板的整板图与原始整板图 :{processed_filename}: {original_filename}"
                            )
                        except Exception as del_err:
                            self.log.warning(
                                f"删除带白板的整板图与原始整板图失败: {processed_filename}: {original_filename}: {del_err}"
                            )

                except Exception as err:
                    err_msg_list.append(f"上传整板图失败，boardSide:{board_side}！err: {err}")
                    self.log.warning(traceback.format_exc())
            else:
                self.log.warning(f"找不到整板图！")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"MES响应异常，error：{err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        aws_access_key_id = data_vo.get_value_by_cons_key("aws_access_key_id")
        aws_secret_access_key = data_vo.get_value_by_cons_key("aws_secret_access_key")
        is_upload_img = data_vo.get_value_by_cons_key("is_upload_img")
        region_name = data_vo.get_value_by_cons_key("region_name")
        aws_endpoint_url = data_vo.get_value_by_cons_key("aws_endpoint_url")
        bucket_name_ui = data_vo.get_value_by_cons_key("bucket_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_src_img = pcb_entity.get_pcb_t_image()

        date_tmp = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE1)
        date1 = date_tmp[:8]  # 年月日
        date2 = date_tmp[8:]  # 时分秒

        pcb_result = pcb_entity.get_repair_result("OK", "NG")
        unique_sn = pcb_entity.get_unique_sn()

        only_one_ng_code = ""

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng() and not only_one_ng_code:
                    only_one_ng_code = comp_entity.repair_ng_str
                    self.log.info(f"only_one_ng_code : {only_one_ng_code}")

        if not os.path.exists(pcb_src_img):
            t_review_path = pcb_entity.get_pcb_pcb_t_review_path()
            pcb_src_img = f"{t_review_path}/thumbnail/1/thumb.jpg"

        log.info(f"{pcb_src_img=}")

        if os.path.exists(pcb_src_img):
            self.log.info(f"正在上传整板图...")

            dst_filename = f"{date1}/{unique_sn}/{unique_sn}_{pcb_result}_{date1}{date2}_{only_one_ng_code}.jpg"

            ret_url = upload_image_to_s3(
                aws_endpoint_url,
                aws_access_key_id,
                aws_secret_access_key,
                pcb_src_img,
                bucket_name_ui,
                dst_filename,
                region_name,
            )

            self.log.info(f"ret url: {ret_url}")
        else:
            self.log.info(f"找不到需要上传的整板图！")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:

        return self.x_response()


if __name__ == '__main__':
    #     ret_str1 = """<?xml version="1.0" encoding="utf-8"?>
    # <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    #                xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    #     <soap:Body>
    #         <GroupTestCommonResponse xmlns="WWW.SUNWODA.COM">
    #             <GroupTestCommonResult>string</GroupTestCommonResult>
    #         </GroupTestCommonResponse>
    #     </soap:Body>
    # </soap:Envelope>"""
    #
    #     root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    #     print(root1[0][0][0].text)
    # def fake_upload():
    #     print("start upload\n")
    #     time.sleep(0.2)
    #     print("end upload\n")
    #
    #
    # print("time cost", time.time() - t1)
    pass
