# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/28 下午2:21
# Author     ：sch
# version    ：python 3.8
# Description：深圳吉翁电子
"""
import json
from collections import defaultdict
from typing import Any

from common import xrequest
from engine.MesEngine import ErrorMapEngine
from services.custom.shenzhenjiweng.jw_module import compress_image_list_to_hex
from vo.mes_vo import DataVo


class Engine(ErrorMapEngine):
    version = {
        "version": "release v1.0.0.4",
        "customer": ["深圳吉翁", "shenzhenjiweng"],
        "device": "AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-04-29 11:43  ATAOI_2019-39140：上传数据
date: 2025-05-16 16:02  ATAOI_2019-39140：不检查httpCode
date: 2025-05-22 11:04  修改图片上传格式
date: 2025-05-22 16:55  修改压缩函数
""", }

    other_form = {
        "api_url_data": {
            "ui_name": "一般作业站接口",
            "value": "http://127.0.0.1:9000/api/wip/Test",
        }
    }

    form = {
        "username": {
            "ui_name": "MES账号",
            "value": "admin",
        },
        "station": {
            "ui_name": "站点IKEY",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        username = data_vo.get_value_by_cons_key("username")
        station = data_vo.get_value_by_cons_key("station")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        comp_ng_data_map = defaultdict(list)

        pcb_src_img = pcb_entity.get_pcb_t_image()

        upload_img_list = [pcb_src_img]

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    comp_repair_ng_code = comp_entity.repair_ng_code
                    comp_ng_data_map[comp_repair_ng_code].append(comp_tag)

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        upload_img_list.append(comp_src_img)

        comp_ng_list = []
        for comp_repair_ng_code, comp_tag_list in comp_ng_data_map.items():
            self.log.info(f"{comp_repair_ng_code} {comp_tag_list}")
            comp_ng_list.append(f"{comp_repair_ng_code},{'|'.join(comp_tag_list)},{len(comp_tag_list)}")

        comp_ng_str = ";".join(comp_ng_list)

        data_param = {
            "runcard": pcb_entity.get_unique_sn(),
            "user": username,
            "station": station,
            "testresult": comp_ng_str,
            "ateresult": "",
            "remark": "",
        }

        self.log.info(f"请求URL：{api_url_data}")
        self.log.info(
            f"未添加文件流(因文件流参数过大,不打印在日志文件里)的请求参数：\n{json.dumps(data_param, indent=4)}")
        self.log.info(f"上传图片数量：{len(upload_img_list)}")

        data_param["remark"] = compress_image_list_to_hex(upload_img_list).decode('utf8')
        ret = xrequest.RequestUtil.post_json(api_url_data, data_param, log_number=0, check_res_code=False)
        if str(ret.get("result")) != "1":
            return self.x_response("false", f"mes接口异常，error：{ret.get('error')}")

        return self.x_response()
