# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/6/25 上午10:17
# Author     ：sch
# version    ：python 3.8
# Description：https://jira.cvte.com/browse/ATAOI_2019-40435
"""
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

row_template = """{comp_tag},{comp_part},{repair_ng_code},{project_name},{test_time},{barcode},{pcb_sn},{line},{repair_user},{order_id},{board_no}"""


class Engine(ErrorMapEngine):
    version = {
        "version": "release v1.0.0.7",
        "customer": ["中科鸿泰", "zhongkehongtai"],
        "device": "AIS430",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-06-25 11:10  生成Log文件到本地
date: 2025-06-26 16:10  修改文件格式
date: 2025-06-26 17:19  修改格式，CarrieID改为传载具条码
date: 2025-06-27 08:54  修改文件格式
date: 2025-07-14 16:53  ATAOI_2019-40980: 按器件条码再多生成一个Log文档
date: 2025-07-16 10:55  ATAOI_2019-40980: bugfix: 修改器件条码文档内容格式
date: 2025-07-17 09:58  bugfx: other_form和path无法正常加载
""", }

    form = {
        "line_id": {
            "ui_name": "线体",
            "value": "",
        },
        "station_id": {
            "ui_name": "站别",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
    }

    combo = {
        "send_type_log": {
            "ui_name": "Log生成方式",
            "item": [
                "整板",
                "单板"
            ],
            "value": "整板",
        }
    }

    path = {
        "save_path_log": {
            "ui_name": "Log保存路径",
            "value": "",
        },
        "comp_log_save_path": {
            "ui_name": "元件Log保存路径",
            "value": "",
        },

    }

    other_form = {

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        line_id = data_vo.get_value_by_cons_key("line_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        order_id = data_vo.get_value_by_cons_key("order_id")
        send_type = data_vo.get_value_by_cons_key("send_type_log")
        save_path_log = data_vo.get_value_by_cons_key("save_path_log", not_null=True)
        comp_log_save_path = data_vo.get_value_by_cons_key("comp_log_save_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_comp_list = []

        unique_sn = pcb_entity.get_unique_sn()

        project_name = pcb_entity.project_name
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        # pcb_sn = pcb_entity.pcb_barcode
        fixture_sn = pcb_entity.fixture_barcode

        if not unique_sn:
            unique_sn = fixture_sn

        comp_barcode_map = pcb_entity.get_all_comp_barcode()
        self.log.info(f"{comp_barcode_map=}")

        repair_user = pcb_entity.repair_user

        for comp_list in comp_barcode_map.values():
            for item in comp_list:
                comp_barcode = item.get("comp_barcode", "")
                if comp_barcode:
                    comp_log_filepath = f"{comp_log_save_path}/{unique_sn}_{comp_barcode}.Log"
                    comp_context = f"{station_id},{unique_sn},3,{repair_user},{line_id},,OK,{comp_barcode},,"
                    xutil.FileUtil.write_content_to_file_pro(
                        comp_log_filepath,
                        comp_context
                    )

        self.log.info(f"生成方式：{send_type}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = f"{test_time}_{board_no}"

            board_comp_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    item = {
                        "comp_tag": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "repair_ng_code": comp_entity.repair_ng_code,
                        "project_name": project_name,
                        "test_time": test_time,
                        "barcode": barcode,
                        "line": line_id,
                        "pcb_sn": fixture_sn,
                        "repair_user": pcb_entity.repair_user,
                        "order_id": order_id,
                        "board_no": board_no
                    }
                    pcb_comp_list.append(row_template.format(**item))
                    board_comp_list.append(row_template.format(**item))

            if send_type == "单板":

                if not board_comp_list:
                    # PASS/REPASS的需要输出一行元件数据
                    board_comp_list.append(row_template.format(**{
                        "comp_tag": "GOOD",
                        "comp_part": "",
                        "repair_ng_code": "",
                        "project_name": project_name,
                        "test_time": test_time,
                        "barcode": barcode,
                        "line": line_id,
                        "pcb_sn": fixture_sn,
                        "repair_user": pcb_entity.repair_user,
                        "order_id": order_id,
                        "board_no": ""
                    }))

                log_content = ";".join(board_comp_list) + ";"
                filepath = f"{save_path_log}/{barcode}.Log"

                xutil.FileUtil.write_content_to_file_pro(
                    filepath,
                    log_content
                )

        if send_type == "整板":
            # 取扫到的任一条码，没有则用时间
            unique_sn = pcb_entity.get_unique_sn()

            if not unique_sn:
                unique_sn = test_time

            if not pcb_comp_list:
                pcb_comp_list.append(row_template.format(**{
                    "comp_tag": "GOOD",
                    "comp_part": "",
                    "repair_ng_code": "",
                    "project_name": project_name,
                    "test_time": test_time,
                    "barcode": unique_sn,
                    "line": line_id,
                    "pcb_sn": fixture_sn,
                    "repair_user": pcb_entity.repair_user,
                    "order_id": order_id,
                    "board_no": ""
                }))

            log_content = ";".join(pcb_comp_list) + ";"

            filepath = f"{save_path_log}/{unique_sn}.Log"
            xutil.FileUtil.write_content_to_file_pro(
                filepath,
                log_content
            )

        return self.x_response()
