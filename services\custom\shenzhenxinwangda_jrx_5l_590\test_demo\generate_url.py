# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : generator_url.py
# Time       ：2024/12/2 上午11:07
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from urllib.parse import urljoin

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError

endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
aws_access_key_id_ = "enp6eA=="
aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"

# 创建 S3 客户端，并指定自定义终端节点和其他配置选项（如果需要）
s3_client = boto3.client(
    's3',
    endpoint_url=endpoint_url_,  # 如果是 AWS S3，则不需要此参数
    config=Config(
        s3={'addressing_style': 'path'},  # 使用路径样式 URL
        # signature_version='s3v4'  # 如果适用，可以指定签名版本
    ),
    region_name='us-west-2',  # 替换为你的实际区域
    aws_access_key_id=aws_access_key_id_,  # 替换为你的访问密钥
    aws_secret_access_key=aws_secret_access_key_  # 替换为你的秘密密钥
)


def get_public_download_url(bucket_name, object_key):
    """为公开可读的对象生成无签名的下载 URL"""
    try:
        # 构建非签名的公共 URL
        endpoint_url = endpoint_url_  # 如果是 AWS S3，则使用默认域名
        public_url = urljoin(endpoint_url + '/', f'{bucket_name}/{object_key}')
        print(f"Public download URL: {public_url}")
        return public_url
    except Exception as e:
        print(f"Error generating public download URL: {e}")
        return None


def get_presigned_download_url(bucket_name, object_key, expiration=3600):
    """为私有对象生成带签名的下载 URL，默认有效期为1小时"""
    try:
        # 生成预签名 URL
        # presigned_url = s3_client.generate_presigned_url(
        #     'get_object',
        #     Params={'Bucket': bucket_name, 'Key': object_key},
        #     ExpiresIn=expiration
        # )
        # print(f"Presigned download URL: {presigned_url}")

        url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': object_key},
            ExpiresIn=expiration  # URL有效期为1小时
        )
        print(f"下载链接：{url}")

        return url
    except ClientError as e:
        print(f"Error generating presigned URL: {e}")
        return None


# 示例调用
if __name__ == "__main__":
    bucket_name = 'my-new-bucket'
    public_object_key = 'images/COMP1039_1039333.png'
    private_object_key = 'images/COMP1039_1039333.png'

    # 获取公开对象的无签名下载链接
    public_url = get_public_download_url(bucket_name, public_object_key)
    if public_url:
        print(f"Public object '{public_object_key}' available at: {public_url}")

    # 获取私有对象的有效期为1小时的带签名下载链接
    presigned_url = get_presigned_download_url(bucket_name, private_object_key, 10000000)
    if presigned_url:
        print(f"Private object '{private_object_key}' available at: {presigned_url} for the next hour.")
