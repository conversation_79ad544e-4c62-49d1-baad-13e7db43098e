# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/31 下午15:38
# Author     ：chencb
# version    ：python 3.8
# Description：苏州京东方 https://jira.cvte.com/browse/ATAOI_2019-38406
"""
from typing import Any
from common import xcons
from common.xutil import log, x_response, XmlUtil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine
from suds.client import Client


class Engine(ErrorMapEngine):
    version = {
        "title": "suzhoujingdongfang release v1.0.0.1",
        "device": "AIS501B",
        "feature": ["发送检测数据"],
        "author": "chenchongbing",
        "release": """
date: 2025-04-03 18:00  jira:38406 发送检测数据
""",
    }

    form = {
        "mes_user": {
            "ui_name": "MES用户名",
            "value": ""
        },
        "mes_pwd": {
            "ui_name": "MES密码",
            "value": ""
        },
        "jodno_id": {
            "ui_name": "调度号",
            "value": ""
        },
        "crate_id": {
            "ui_name": "筐号",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
        "station": {
            "ui_name": "站点编号",
            "value": ""
        },
        "mes_url": {
            "ui_name": "MES地址",
            "value": ""
        },
    }

    combo = {
        "device_type": {
            "ui_name": "设备类型",
            "item": ["AOI", "SPI"],
            "value": "SPI",
        }
    }

    pcb_template = f'''
        <?xml version="1.0" encoding="utf-8"?>
        <STD_IN>
            <EDI_user>{{mes_user}}</EDI_user>
            <EDI_pwd>{{mes_pwd}}</EDI_pwd>
            <ObjectID>{{object_id}}</ObjectID>
            <Service>
                <PQCS>{{boards}}</PQCS>
            </Service>
        </STD_IN>
        '''
    board_template = f'''
    <PQC>
        <JODNO>{{jodno_id}}</JODNO>
        <CRATEID>{{crate_id}}</CRATEID>
        <PANELID>{{pcb_sn}}</PANELID>
        <PROSN>{{board_sn}}</PROSN>
        <PROSEQ>{{board_no}}</PROSEQ>
        <PQCQUALSTATE>{{repair_result}}</PQCQUALSTATE>
        <PQCTESTDT>{{inspect_time}}</PQCTESTDT>
        <PQCOPERATOR>{{operator}}</PQCOPERATOR>
        <PQCSTATION>{{station}}</PQCSTATION>
        <PQCMACHINERESULT>{{robot_result}}</PQCMACHINERESULT>
        <SNLABEL>{{sn_label}}</SNLABEL>
        <ITEMS>{{comps}}</ITEMS>
    </PQC>
    '''

    comp_template = f'''
    <ITEM>
        <ERRCODE>{{ng_code}}</ERRCODE>
        <REFS>{{designators}}</REFS>
        <IMAGELINKS>{{images}}</IMAGELINKS>
    </ITEM>
    '''

    designator_template = f'''
    <REF>{{designator}}</REF>
    '''

    image_template = f'''
    <IMAGELINK>{{image_path}}</IMAGELINK>
    '''

    def _parse_response_xml(self, xml_data: str):
        root = XmlUtil.get_xml_root_by_str(xml_data)
        status_element = root.find("Status")
        error_element = root.find("Error")

        status = ""
        if status_element is not None:
            status = status_element.text

        error = ""
        if error_element is not None:
            error = error_element.text

        return status, error

    def _send_xml_to_mes(self, mes_url: str, object_id: str, xml_data: str):
        try:
            formatted_xml = XmlUtil.format_xml(xml_data)
            self.log.info(f"请求url：{mes_url}\n请求参数：{formatted_xml}")
            client = Client(mes_url, location=mes_url)
            ret = client.service.DataService(object_id, formatted_xml)
            self.log.info(f"接口响应:{ret}")
            if 'Status' not in ret:
                return x_response("false", f"mes返回错误信息：{ret}")
            else:
                status, error = self._parse_response_xml(ret)
                if status != "0":
                    return x_response("false", f"mes返回错误信息：{error}")
                else:
                    return x_response()
        except Exception as e:
            return x_response('false', f'网络连不上:{e}')

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        mes_url = data_vo.get_value_by_cons_key('mes_url', not_null=True)
        mes_user = data_vo.get_value_by_cons_key('mes_user')
        mes_pwd = data_vo.get_value_by_cons_key('mes_pwd')
        jodno_id = data_vo.get_value_by_cons_key('jodno_id')
        crate_id = data_vo.get_value_by_cons_key('crate_id')
        operator = data_vo.get_value_by_cons_key('operator')
        station = data_vo.get_value_by_cons_key('station')
        device_type = data_vo.get_value_by_cons_key('device_type')

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        object_id = 'ic_aoi_record_multi' if device_type == 'AOI' else 'ic_pqc_record_multi'
        pcb_param = {
            'mes_user': mes_user,
            'mes_pwd': mes_pwd,
            'object_id': object_id,
            'boards': ''
        }

        # 2018-11-12 09:30:12
        inspect_time_1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        # 20250311055421
        inspect_time_2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode
        if not pcb_sn:
            pcb_sn = inspect_time_2

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)

            board_sn = board_entity.barcode
            sn_label = 'Y'
            if not board_sn:
                board_sn = inspect_time_2
                sn_label = 'N'

            robot_result = board_entity.get_robot_result('0', '1')
            repair_result = board_entity.get_repair_result('0', '1')

            board_param = {
                'jodno_id': jodno_id,
                'crate_id': crate_id,
                'pcb_sn': pcb_sn,
                'board_sn': board_sn,
                'board_no': board_entity.board_no,
                'repair_result': repair_result,
                'inspect_time': inspect_time_1,
                'operator': operator,
                'station': station,
                'robot_result': robot_result,
                'sn_label': sn_label,  # 有SN为Y，无SN为N
                'comps': '',
            }

            # 复判为ng才需生成器件数据
            ng_comp_list = []
            ng_comp_dict = {}
            if board_entity.is_repair_ng():
                for comp_entity in board_entity.yield_comp_entity():
                    if not comp_entity.is_repair_ng():
                        continue

                    ng_code = comp_entity.robot_ng_code
                    if ng_code not in ng_comp_dict:
                        ng_comp_dict[ng_code] = {}
                    # 缓存为{'ng_code':{'designator':'image_path',..},..}
                    ng_comp_dict[ng_code][comp_entity.designator] = comp_entity.image_path

                for ng_code, comp_data in ng_comp_dict.items():
                    designator_list = []
                    image_list = []
                    for designator, image in comp_data.items():
                        designator_str = self.designator_template.format(designator=designator)
                        designator_list.append(designator_str)
                        image_str = self.image_template.format(image_path=image)
                        image_list.append(image_str)

                    comp_param = {
                        'ng_code': ng_code,
                        'designators': '\n'.join(designator_list),
                        'images': '\n'.join(image_list)
                    }
                    comp_str = self.comp_template.format(**comp_param)
                    ng_comp_list.append(comp_str)

            board_param['comps'] = '\n'.join(ng_comp_list)
            board_str = self.board_template.format(**board_param)
            board_data_list.append(board_str)

        pcb_param['boards'] = '\n'.join(board_data_list)
        send_xml = self.pcb_template.format(**pcb_param)
        ret = self._send_xml_to_mes(mes_url, object_id, send_xml)
        return ret
