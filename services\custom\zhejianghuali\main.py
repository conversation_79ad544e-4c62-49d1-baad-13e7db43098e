# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/26 上午10:51
# Author     ：sch
# version    ：python 3.8
# Description：浙江华立
"""
import os
import time
from datetime import datetime
from typing import Any

from common import xsql, xcons, xutil
from common.xutil import CircularList, log, cal_device_status_time, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

global_data = {}

circular_list = CircularList(1000)

insert_board_template = """INSERT INTO {table_name} (MachineName, SerialNumber, StationID, ScanType, ScanTime, Result, SubSns, DefectsID, Attribute01)
VALUES {board_data};"""

insert_comp_template = """INSERT INTO {table_name} (MachineName, DefectsID, SerialNumber, PCBNumber, Location, DefID, DefectCode, DefectDesc, DefectPicturePath)  
VALUES {comp_data_str};"""

insert_status_template = """INSERT INTO {table_name} (MachineID, LineID, TimeDuration, StandbyTime, WorkTime, ErrorTime, ErrorBegin, ErrorOver, MachineType, ErrorReason)
VALUES {status_data};"""


class Engine(ErrorMapEngine):
    version = {
        "title": "zhejianghuali release v1.0.0.2",
        "device": "303,501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-26 10:52  init
date: 2024-07-03 14:42  修改图片命名规则
""", }

    other_combo = {
        "is_cron_upload": {
            "ui_name": "是否定时上传设备状态",
            "item": ["Yes", "No"],
            "value": "No"
        },
        "upload_device_frequency": {
            "ui_name": "上传频率(分钟)",
            "item": ["1", "5", "10", "20", "30", "60", "120", "300", "600"],
            "value": "10"
        },
    }

    # button = {
    #     "getDevice": {
    #         "ui_name": "getDevice"
    #     },
    #     "get_range_time": {
    #         "ui_name": "getRangeTime"
    #     },
    # }

    other_form = {
        "sql_server": {
            "ui_name": "数据库服务器IP地址",
            "value": "",
        },
        "sql_port": {
            "ui_name": "数据库端口",
            "value": "1433",
        },
        "sql_db": {
            "ui_name": "数据库名称",
            "value": "",
        },
        "sql_username": {
            "ui_name": "数据库账号",
            "value": "",
        },
        "sql_password": {
            "ui_name": "数据库密码",
            "value": "",
        },
        "sql_board_table": {
            "ui_name": "数据库拼板表",
            "value": "aoi_wip",
        },
        "sql_comp_ng_table": {
            "ui_name": "数据库器件明细表",
            "value": "aoi_defects",
        },
        "sql_device_status_table": {
            "ui_name": "数据库设备状态表",
            "value": "machinestatus",
        },
    }

    form = {
        "device_type": {
            "ui_name": "设备种类",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "line_name": {
            "ui_name": "线体名称",
            "value": "",
        },
        "station_id": {
            "ui_name": "工位编号",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
    }

    path = {
        "save_path_1": {
            "ui_name": "NG器件图存储路径",
            "value": "",
        }
    }

    password_style = ["sql_password"]

    def init_main_window(self, main_window, other_vo: OtherVo):
        other_vo = OtherVo({}, main_window.config_data)

        is_cron_upload = other_vo.get_value_by_cons_key("is_cron_upload")
        upload_device_frequency = other_vo.get_value_by_cons_key("upload_device_frequency", to_int=True)

        if is_cron_upload == "Yes":
            interval_cron = True
        else:
            interval_cron = False

        main_window.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        main_window.config_data["app_setting"]["custom_interval_time"] = int(upload_device_frequency * 60)  # 3s
        self.log.info("init main window done!")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        sql_server = data_vo.get_value_by_cons_key('sql_server', not_null=True)
        sql_port = data_vo.get_value_by_cons_key('sql_port', to_int=True, not_null=True)
        sql_db = data_vo.get_value_by_cons_key('sql_db', not_null=True)
        sql_username = data_vo.get_value_by_cons_key('sql_username', not_null=True)
        sql_password = data_vo.get_value_by_cons_key('sql_password', not_null=True)
        sql_board_table = data_vo.get_value_by_cons_key('sql_board_table', not_null=True)
        sql_comp_ng_table = data_vo.get_value_by_cons_key('sql_comp_ng_table', not_null=True)

        device_name = data_vo.get_value_by_cons_key("device_name")
        station_id = data_vo.get_value_by_cons_key("station_id")
        operator = data_vo.get_value_by_cons_key("operator")
        save_path_1 = data_vo.get_value_by_cons_key("save_path_1", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        try:
            sql_conn = xsql.get_mssql_cursor(sql_server, sql_username, sql_password, sql_db, sql_port)
        except Exception as err:
            return self.x_response("false", f"连接数据库失败，error：{err}")

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        start_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        start_time2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        start_time3 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT13)
        project_name = pcb_entity.project_name

        ret_res = self.x_response()

        pcb_save_path = ""

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            save_full_path = f"{save_path_1}/{project_name}/{start_time1}/{barcode}_{start_time2}"
            xutil.FileUtil.ensure_dir_exist(save_full_path)

            if not pcb_save_path:
                pcb_save_path = save_full_path

            uuid_key = xutil.OtherUtil.get_uuid4_str()

            board_data = (
                device_name,
                pcb_sn,
                station_id,
                1,
                start_time,
                board_entity.get_repair_result("OK", "NG"),
                barcode,
                uuid_key,
                operator
            )

            insert_sql1 = insert_board_template.format(**{
                "table_name": sql_board_table,
                "board_data": board_data
            })

            try:
                xsql.insert_row_into_table(sql_conn, insert_sql1)
            except Exception as err:
                ret_res = self.x_response("false", f"写入数据到board表失败，error：{err}")

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():

                    comp_src_img = comp_entity.image_path
                    comp_tag = comp_entity.designator
                    repair_ng_str = comp_entity.repair_ng_str

                    if comp_src_img:
                        dst_img = f"{save_full_path}/{barcode}_{comp_tag}_{start_time3}"
                        suffix = xutil.FileUtil.copy_file(comp_src_img, dst_img, is_auto_add_suffix=True)

                        dst_img1 = f"{dst_img}{suffix}"
                    else:
                        dst_img1 = ""

                    comp_data_list.append(str((
                        device_name,
                        uuid_key,
                        barcode,
                        pcb_sn,
                        comp_tag,
                        comp_entity.part,
                        comp_entity.repair_ng_code,
                        repair_ng_str,
                        dst_img1[:255]
                    )))

            if comp_data_list:
                self.log.info(f"insert comp data number: {len(comp_data_list)}")
                comp_data_str = ",\n".join(comp_data_list)
                insert_sql2 = insert_comp_template.format(comp_data_str=comp_data_str, table_name=sql_comp_ng_table)
                xsql.insert_row_into_table(sql_conn, insert_sql2)

            self.log.info(f"拼板[{board_entity.board_no}]数据写入成功！")

        sql_conn.close()

        for src_img in pcb_entity.list_all_pcb_image():
            if os.path.exists(src_img):
                if "B_" in src_img:
                    board_side = "B"
                else:
                    board_side = "T"

                dst_img_2 = f"{pcb_save_path}/{pcb_sn}_{board_side}_{start_time3}.jpg"
                xutil.FileUtil.copy_file(src_img, dst_img_2)

            else:
                self.log.warning(f"找不到需要上传的整板图， {src_img}")

        return ret_res

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        """
        设备点击启动测试到暂停测试的时间定义为正常工作时间；
        设备点击暂停测试到再次点击启动测试的时间定义为等待时间；
        设备报警时间截至到点击启动测试的时间定义为故障时间;
        报警结束时间传0；

        :param other_vo:设备点击启动测试到暂停测试的时间定义为正常工作时间
        :param other_param:
        :return:
        """
        old_device_code = other_vo.get_old_device_status_code()

        if old_device_code not in ["02", "03", "10", "11", "20", "21", "12", "13", "22", "23", "99"]:
            self.log.warning(f"该设备状态暂不统计时间！")
            return self.x_response()

        self.log.info(f"device status: {old_device_code}")

        time_now = int(time.time())

        if old_device_code == "02":
            # 开始检测
            cur_state = "START"

        elif old_device_code == "03":
            # 停止检查
            cur_state = "STOP"
            if global_data.get('x_state_last', '') == "ERROR":
                self.log.warning(f"本次状态是停止，但上一次设备状态是故障，忽略本次设备状态触发！")
                return self.x_response()

            if global_data.get('x_state_last', '') != "START":
                self.log.warning(f"忽略本次设备状态触发！")
                return self.x_response()

        else:
            # 故障
            cur_state = "ERROR"
            if global_data.get('x_state_last', '') == "STOP":
                self.log.warning(f"本次状态是故障，但上一次状态是停止，忽略本次设备状态触发！")
                return self.x_response()

            if global_data.get('x_state_last', '') != "START":
                self.log.warning(f"忽略本次设备状态触发！")
                return self.x_response()

        if global_data.get('x_state_last', '') == cur_state:
            self.log.warning(f"设备状态没有切换，忽略本次设备状态触发！")
            return self.x_response()

        circular_list.add_item({
            "state_last": global_data.get('x_state_last', ''),
            "time_last": global_data.get('x_time_last', ''),
            "state_current": cur_state,
            "time_current": time_now
        })

        global_data['x_state_last'] = cur_state
        global_data['x_time_last'] = time_now
        log.info("done")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        upload_device_frequency = other_vo.get_value_by_cons_key("upload_device_frequency", to_int=True)
        sql_device_status_table = other_vo.get_value_by_cons_key("sql_device_status_table")
        device_name = other_vo.get_value_by_cons_key("device_name")
        line_name = other_vo.get_value_by_cons_key("line_name")
        device_type = other_vo.get_value_by_cons_key("device_type")

        sql_server = other_vo.get_value_by_cons_key('sql_server', not_null=True)
        sql_port = other_vo.get_value_by_cons_key('sql_port', to_int=True, not_null=True)
        sql_db = other_vo.get_value_by_cons_key('sql_db', not_null=True)
        sql_username = other_vo.get_value_by_cons_key('sql_username', not_null=True)
        sql_password = other_vo.get_value_by_cons_key('sql_password', not_null=True)

        range_end = int(time.time())
        range_start = int(time.time()) - upload_device_frequency * 60

        time_run, time_wait, time_err = cal_device_status_time(circular_list.buffer, range_start, range_end,
                                                               default_ret=upload_device_frequency * 60)

        t1 = datetime.fromtimestamp(range_start).strftime('%Y-%m-%d %H:%M:%S')
        t2 = datetime.fromtimestamp(range_end).strftime('%Y-%m-%d %H:%M:%S')

        insert_sql3 = insert_status_template.format(**{
            "table_name": sql_device_status_table,
            "status_data": (
                device_name,
                line_name,
                str(upload_device_frequency),
                str(time_wait),
                str(time_run),
                str(time_err),
                t1,
                t2,
                device_type,
                "",
            )
        })

        try:
            sql_conn = xsql.get_mssql_cursor(sql_server, sql_username, sql_password, sql_db, sql_port)

            xsql.insert_row_into_table(sql_conn, insert_sql3)
            sql_conn.close()
        except Exception as err:
            log.warning(f"写入数据到status表失败，error：{err}")

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        # btn_key = btn_dao.get_btn_key()

        # if btn_key == "getDevice":
        #     buffer = circular_list.buffer
        #
        #     log.info(json.dumps(buffer, indent=4, ensure_ascii=False))
        # elif btn_key == "get_range_time":
        #     upload_device_frequency = btn_dao.get_value_by_cons_key("upload_device_frequency", to_int=True)
        #
        #     range_end = int(time.time())
        #     range_start = int(time.time()) - 1 * 60
        #
        #     cal_device_status_time(circular_list.buffer, range_start, range_end)

        return self.x_response()
