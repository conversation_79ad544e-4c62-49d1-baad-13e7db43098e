# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/10/24 上午9:31
# Author     ：sch
# version    ：python 3.8
# Description：东莞康舒
"""

from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import BaseEngine

AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "W152", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "W045", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "W040", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "C034", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "WT34", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "T107", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "WT35", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "W047", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "W014", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "W121", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "W038", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "T029", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "W049", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "WT36", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "WT37", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "S032", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "T149", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "WT38", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "S001", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "W062", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "WT39", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "T128", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "T113", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "S027", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "WT40", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "WT41", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "WT42", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "WT43", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "WT44", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "WT45", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "WT60", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "WT46", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}

txt_template = "{foo1};{foo2};{foo3};{foo4};{foo5};{foo6};{foo7};{foo8};{foo9};{foo10};{foo11};{foo12};{foo13};{foo14};{foo15};{foo16};{foo17};{foo18};{foo19};{foo20};{foo21};{foo22};\n"

global_data = {}


class Engine(BaseEngine):
    version = {
        "title": "dongguankangshu release v1.0.0.3",
        "device": "203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-10-25 15:16  init
date: 2023-11-02 17:24  每个器件数据上传一次
""", }

    form = {
        "window_ip": {
            "ui_name": "WindowIp",
            "value": ""
        },
        "compute_sn": {
            "ui_name": "电脑编号",
            "value": ""
        },
        "line_id": {
            "ui_name": "线别",
            "value": ""
        },
        "station_id": {
            "ui_name": "站位",
            "value": "060"
        },
        "user_id": {
            "ui_name": "工号",
            "value": "21104689"
        },
        "order_id": {
            "ui_name": "工令",
            "value": ""
        },
        "fixture_sn": {
            "ui_name": "治具编号",
            "value": ""
        },
        "pcb_side": {
            "ui_name": "PCB面",
            "value": ""
        },
        "device_mode": {
            "ui_name": "设备型号",
            "value": ""
        },
    }

    path = {
        "txt_path": {
            "ui_name": "txt保存路径",
            "value": ""
        },
        "img_path": {
            "ui_name": "图片保存路径",
            "value": ""
        }
    }

    button = {
        "connect_dll": {
            "ui_name": "连接Mes"
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        station_id = other_vo.get_value_by_cons_key("station_id")
        user_id = other_vo.get_value_by_cons_key("user_id")
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        ret_db = global_data.get("connect_db")
        if not ret_db:
            return self.x_response("false", "请先连接Mes，再开始检测！")

        sn_list = other_vo.list_sn()

        error_msg = ""
        for sn in sn_list:
            pocket_data3 = {
                "type": 3,
                "request_param": {
                    "iDb": ret_db,
                    "iBarcode": sn,
                    "iInfo": f"{station_id}{user_id}",
                }
            }
            ret2 = xrequest.SocketUtil.x_socket_send_data(window_ip, pocket_data3)

            ret_str = ret2.get("string", "")
            if ret_str.startswith("0"):
                error_msg = ret_str

        if error_msg:
            return self.x_response("false", f"dll接口异常，条码校验失败，error：{error_msg}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        txt数据文件，命名规则：程式名_时间戳_拼板条码
        机器测试NG的器件图片保存在按天生成的文件夹里，一天生成一个文件夹。命名规则：拼板条码_不良位号_不良代码_检测时间戳_拼版号.jpg
        :param data_vo:
        :param other_data:
        :param other_param:
        :return:
        """
        compute_sn = data_vo.get_value_by_cons_key("compute_sn")
        line_id = data_vo.get_value_by_cons_key("line_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        user_id = data_vo.get_value_by_cons_key("user_id")
        order_id = data_vo.get_value_by_cons_key("order_id")
        fixture_sn = data_vo.get_value_by_cons_key("fixture_sn")
        pcb_side = data_vo.get_value_by_cons_key("pcb_side")
        device_mode = data_vo.get_value_by_cons_key("device_mode")
        txt_path = data_vo.get_value_by_cons_key("txt_path")
        img_path = data_vo.get_value_by_cons_key("img_path")
        window_ip = data_vo.get_value_by_cons_key("window_ip")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        start_date = start_time[:8]

        project_name = pcb_entity.project_name

        full_img_path = f"{img_path}/{start_date}"
        xutil.FileUtil.ensure_dir_exist(full_img_path)

        board_number = pcb_entity.board_count

        ret_db = global_data.get("connect_db")
        error_msg = ""

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            no = board_entity.board_no

            txt_content = ""
            ix = 0

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                ix += 1

                comp_src_img = comp_entity.image_path
                comp_tag = comp_entity.designator
                robot_ng_code = comp_entity.robot_ng_code
                repair_ng_code = comp_entity.repair_ng_code

                repair_ng_str = comp_entity.repair_ng_str
                robot_ng_str = comp_entity.robot_ng_str

                r_ng_code = AIS_40X_ERROR_MAP.get(robot_ng_code, {}).get("custom_code", robot_ng_code)
                u_ng_code = AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("custom_code", repair_ng_code)

                repair_ng_str = AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("custom_str", repair_ng_str)
                robot_ng_str = AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("custom_str", robot_ng_str)

                if comp_entity.is_robot_ng():
                    comp_dst_img = f"{full_img_path}/{barcode}_{comp_tag}_{r_ng_code}_{start_time}_{no}.png"

                    if comp_src_img:
                        # 保存器件图片
                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

                txt_row = txt_template.format(**{
                    'foo1': ix,
                    'foo2': comp_tag,
                    'foo3': '',
                    'foo4': compute_sn,
                    'foo5': line_id,
                    'foo6': barcode,
                    'foo7': len(barcode),
                    'foo8': f"{station_id}{user_id}",
                    'foo9': "NG" if comp_entity.is_repair_ng() else "OK",
                    'foo10': order_id,
                    'foo11': fixture_sn,
                    'foo12': r_ng_code,
                    'foo13': u_ng_code,
                    'foo14': repair_ng_str,
                    'foo15': start_date,
                    'foo16': start_time[8:],
                    'foo17': robot_ng_str,
                    'foo18': pcb_side,
                    'foo19': project_name,
                    'foo20': 'AOI',
                    'foo21': device_mode,
                    'foo22': board_number
                })
                txt_content += txt_row

                comp_data_list.append(txt_row)

            # 保存txt
            txt_filename = f"{txt_path}/{project_name}_{start_time}_{barcode}.txt"
            xutil.FileUtil.write_content_to_file(txt_filename, txt_content)

            # 通过dll接口上传数据到mes
            if ret_db:

                self.log.info(f"开始上传器件数据，数量：{len(comp_data_list)}")

                for row in comp_data_list:
                    pocket_data4 = {
                        "type": 5,
                        "request_param": {
                            "iDb": ret_db,
                            "iOrder": user_id,
                            "iData": row.strip(),
                        }
                    }
                    ret4 = xrequest.SocketUtil.x_socket_send_data(window_ip, pocket_data4)

                    ret_str = ret4.get("string", "")
                    if ret_str.startswith("0"):
                        error_msg = ret_str
            else:
                self.log.warning(f"未链接mes，不上传数据到Mes！")

        if error_msg:
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{error_msg}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        order_id = btn_vo.get_value_by_cons_key("order_id")
        station_id = btn_vo.get_value_by_cons_key("station_id")
        user_id = btn_vo.get_value_by_cons_key("user_id")
        line_id = btn_vo.get_value_by_cons_key("line_id")
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "connect_dll":
            # 1. 请求mes的dll库，看连接是否正常，正常的话会返回数据库表
            pocket_data1 = {
                "type": 1,
                "request_param": {}
            }
            ret = xrequest.SocketUtil.x_socket_send_data(window_ip, pocket_data1)

            ret_db = ret.get("string", "")

            self.log.info(f"获取到的数据库表：{ret_db}")

            if ret_db.startswith("0"):
                return self.x_response("false", f"连接dll失败，error：{ret_db}")

            # 2. 请求校验检测信息是否正确
            pocket_data2 = {
                "type": 2,
                "request_param": {
                    "iDb": ret_db,
                    "iOrder": order_id,
                    "iInfo": f"{station_id}{user_id}",
                    "iLine": line_id
                }
            }
            ret2 = xrequest.SocketUtil.x_socket_send_data(window_ip, pocket_data2)

            ret_str = ret2.get("string", "")
            if ret_str.startswith("0"):
                return self.x_response("false", f"校验请求信息失败，error：{ret_db}")

            global_data["connect_db"] = ret_db

        return self.x_response()
