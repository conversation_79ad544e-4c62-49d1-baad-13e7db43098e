"""
# File       : main.py
# Time       ：2025/04/30 14:49
# Author     ：wxc
# version    ：python 3.8
# Description：太仓奕瑞
"""
import json
from collections import Counter
from typing import Any

from common import xutil, xrequest, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

xml_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CmosSpi>
            <!--Optional:-->
            <tem:joinString>{data}</tem:joinString>
            <!--Optional:-->
            <tem:operators>admin</tem:operators>
        </tem:CmosSpi>
    </soapenv:Body>
</soapenv:Envelope>
"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["太仓奕瑞", "taicangyirui"],
        "version": "release v1.0.0.4",
        "device": "AIS630",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-04-30 14:50  ATAOI_2019-39238：上传数据
date: 2025-05-07 11:35  ATAOI_2019-39238：GNA/F和G/F数据库无法识别修改为GNAF和GF，PCB_TEST_TIME、PCB_CYCLE_TIME拼写错误修改
date: 2025-05-07 19:21  GNAF和GF改为GNA_F和G_F，G_F为器件总数
date: 2025-05-09 11:16  PCB_CYCLE_TIME后面加上单位s，GNA_F取复判不良描述repair_ng_str
"""}

    form = {
        "upload_api_url": {
            "ui_name": "上传数据接口",
            "value": "http://10.30.150.192:56305/CmosAutoInterfaceService.svc",
        },
        "worker": {
            "ui_name": "操作人",
            "value": "",
        },
        "work_order": {
            "ui_name": "工单",
            "value": ""
        },
        # "project_name": {
        #     "ui_name": "机种名称",
        #     "value": ""
        # },
        "machine_id": {
            "ui_name": "设备ID",
            "value": ""
        },
        "repair_user": {
            "ui_name": "维修站复判用户名",
            "value": ""
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.custom_str_to_chinese()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        upload_api_url = data_vo.get_value_by_cons_key("upload_api_url")
        worker = data_vo.get_value_by_cons_key("worker")
        work_order = data_vo.get_value_by_cons_key("work_order")
        repair_user = data_vo.get_value_by_cons_key("repair_user")
        machine_id = data_vo.get_value_by_cons_key("machine_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        pcb_sn = pcb_entity.pcb_barcode

        ng_data = []
        comp_total_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            comp_total_number += board_entity.comp_total_number
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    ng_data.append(comp_entity.repair_ng_str)

        # 1. 统计每个唯一字符串的出现次数
        counter = Counter(ng_data)
        # 2. 按 "key_count" 格式拼接
        final_ng_data = [f"{key}_{count}" for key, count in counter.items()]
        comp_ng_str = "_".join(final_ng_data)

        pcb_data = {
            "WORKER": worker,
            "WORK_ORDER": work_order,
            "PROJECT_NAME": pcb_entity.project_name,
            "PCBA_SN": pcb_sn,
            "MACHINE_ID": machine_id,
            "PCB_TEST_TIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "PCB_CYCLE_TIME": str(pcb_entity.get_cycle_time())+'s',
            "PCB_ROBOT_RESULT": pcb_entity.get_robot_result(),
            "PCB_USER_RESULT": pcb_entity.get_repair_result(),
            "PCB_FINAL_RESULT": pcb_entity.get_final_result(),
            "PCB_REPAIR_USER": repair_user,
            "GNA_F": comp_ng_str,
            "G_F": comp_total_number
        }

        content = xml_template.format(data=json.dumps(pcb_data, ensure_ascii=False))
        ret_text = xrequest.RequestUtil.post_soap(
            upload_api_url,
            content,
            soap_action="http://tempuri.org/ICmosAutoInterfaceService/CmosSpi"
        )

        ret = xutil.XmlUtil.get_xml_root_by_str(ret_text)
        if str(ret[0][0][0][0].text) != "100":
            return self.x_response("false", f"上传数据接口响应失败，error：{ret[0][0][0][3].text}")

        return self.x_response()


if __name__ == '__main__':
    ret = xutil.XmlUtil.get_xml_root_by_str('''<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CmosSpiResponse xmlns="http://tempuri.org/">
            <CmosSpiResult xmlns:a="http://schemas.datacontract.org/2004/07/BiLin.R2E.Framework.Result" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:code>100</a:code>
                <a:content i:nil="true"/>
                <a:logID i:nil="true"/>
                <a:msg>OK</a:msg>
                <a:token i:nil="true"/>
            </CmosSpiResult>
        </CmosSpiResponse>
    </s:Body>
</s:Envelope>''')

    print(ret[0][0][0][0].text)

    """
    {
    "OPERATOR_ID": "123:123",
    "WORK_ORDER": "123456",
    "PROJECT_NAME": null,
    "PCBA_ID": "no code",
    "GOS_ID": null,
    "EQP_ID": null,
    "START_TIME": "2025/04/14 19:33:41",
    "END_TIME": "2025//04//14 19:34:25",
    "PICK_DELAY": "0.5s",
    "BOND_DELAY": "2s",
    "PR_DELAY": "400ms",
    "UV_POWER": "0%",
    "UV_TIME": "10s",
    "DSP_PREASURE": "0kPa",
    "G_F": "G",
    "GNA_F": "GNA"
}"""
