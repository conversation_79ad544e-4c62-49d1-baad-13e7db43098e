<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <ActivityId CorrelationId="90870c3f-7f88-4b40-879a-8f0c0b910807"
                    xmlns="http://schemas.microsoft.com/2004/09/ServiceModel/Diagnostics">
            00000000-0000-0000-0000-000000000000
        </ActivityId>
    </s:Header>
    <s:Body>
        <GetMeterInfoBySNResponse xmlns="http://tempuri.org/">
            <GetMeterInfoBySNResult xmlns:a="http://schemas.datacontract.org/2004/07/Soas.Model.Entities"
                                    xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:AccuracyLevel>有功2.0级</a:AccuracyLevel>
                <a:Constant>2000</a:Constant>
                <a:Current>0.25-0.5(60)</a:Current>
                <a:DoWhatList/>
                <a:Frequency>50</a:Frequency>
                <a:MaterialNo>2301SZ003-10</a:MaterialNo>
                <a:MeterModel>DDZY131</a:MeterModel>
                <a:OrderName>DDZY131C-Z型单相费控智能电能表(CPU卡-开关内置，A级)</a:OrderName>
                <a:OrderNo>2301FJSZ001</a:OrderNo>
                <a:Phase i:nil="true"/>
                <a:SN>2301PP110007</a:SN>
                <a:SNType>@@</a:SNType>
                <a:SchemeList i:nil="true"/>
                <a:Voltage>220</a:Voltage>
                <a:WorkOrder>240002_01</a:WorkOrder>
            </GetMeterInfoBySNResult>
            <basedata>true</basedata>
            <result>true</result>
            <message/>
        </GetMeterInfoBySNResponse>
    </s:Body>
</s:Envelope>