# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/28 下午4:00
# Author     ：sch
# version    ：python 3.8
# Description：昆山微盟
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "kunshanweimeng release v1.0.0.1",
        "device": "501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-28 17:44  条码校验，上传数据
""", }

    form = {
        "user_no": {
            "ui_name": "人员工号",
            "value": "60038352",
        },

        "line_id": {
            "ui_name": "线别",
            "value": "C3F-01A",
        },
        "station_id": {
            "ui_name": "站别",
            "value": "6526",
        },
    }

    other_form = {
        "api_url": {
            "ui_name": "接口地址",
            "value": "https://www.msik.com.cn/MES.WebApi.Test/api/Barcode/WipControlForBarcodeAppend",
        },
        "client_id": {
            "ui_name": "ClientId",
            "value": "mes_FromCEM_XIGAO_Vda12ur_gHn",
        },
        "client_information": {
            "ui_name": "设备信息",
            "value": "ClientName:C2F-A-AutoPasterLabel,IP:192.18.35.*",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url", not_null=True)
        client_id = other_vo.get_value_by_cons_key("client_id")
        user_no = other_vo.get_value_by_cons_key("user_no")
        client_information = other_vo.get_value_by_cons_key("client_information")
        line_id = other_vo.get_value_by_cons_key("line_id")
        station_id = other_vo.get_value_by_cons_key("station_id", to_int=True)

        ret_res = self.x_response()

        headers = {
            "ClientId": client_id
        }

        for sn in other_vo.list_sn():
            check_param = {
                "BarcodeNo": sn,
                "UserNo": user_no,
                "ClientInformation": client_information,
                "Append": {
                    "StationPass": {
                        "LineNo": line_id,
                        "StationId": station_id
                    }
                }
            }

            ret = xrequest.RequestUtil.post_json(api_url, check_param, headers=headers)
            if str(ret.get('StatusCode')) != '0':
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('Message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url", not_null=True)
        client_id = data_vo.get_value_by_cons_key("client_id")
        user_no = data_vo.get_value_by_cons_key("user_no")
        client_information = data_vo.get_value_by_cons_key("client_information")
        line_id = data_vo.get_value_by_cons_key("line_id")
        station_id = data_vo.get_value_by_cons_key("station_id", to_int=True)

        ret_res = self.x_response()

        headers = {
            "ClientId": client_id
        }

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_list.append({
                        "LocationNo": comp_entity.designator,
                        "DefectNo": comp_entity.repair_ng_code,
                        "NGPinList": [{
                            "PinNo": comp_entity.designator,
                            "NgDesc": comp_entity.repair_ng_str
                        }]
                    })

            if board_entity.is_repair_ng():
                data_param = {
                    "BarcodeNo": barcode,
                    "UserNo": user_no,
                    "ClientInformation": client_information,
                    "Append": {
                        "StationPass": {
                            "LineNo": line_id,
                            "StationId": station_id
                        },
                        "NgInfo": {
                            "NgType": "SMT",
                            "NGComponentList": comp_list
                        }
                    }
                }
                ret = xrequest.RequestUtil.post_json(api_url, data_param, headers)
                if str(ret.get('StatusCode')) != '0':
                    ret_res = self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret.get('Message')}")

            else:
                self.log.warning(f"根据客户要求，复判PASS的不发送测试数据！")

        return ret_res
