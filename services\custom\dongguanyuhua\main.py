# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/15 下午5:07
# Author     ：sch
# version    ：python 3.8
# Description：东莞毓华
"""
import json
from typing import Any

from common import xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

data_template = """<Tb_Test_Record>
    <Tb_Result>
        <TotalResult>{pcb_repair_result}</TotalResult>{board_data}
    </Tb_Result>
</Tb_Test_Record>"""

board_template = """
        <Tb_Array>
            <Tb_Barcode>{barcode}</Tb_Barcode>
            <PCBID>{board_no}</PCBID>
            <Array_Result>{board_result}</Array_Result>{comp_data}
        </Tb_Array>"""

comp_template = """
            <Pad>
                <DefectLocation>{comp_tag}</DefectLocation>
                <DefectCode>{comp_ng_code}</DefectCode>
                <ResultDetail>{comp_ng_str}</ResultDetail>
            </Pad>"""


class Engine(BaseEngine):
    version = {
        "title": "dongguanyuhua release v1.0.0.3",
        "device": "40x",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-16 10:11  init
date: 2023-06-17 11:20  获取token，获取条码，上传数据
date: 2023-07-10 16:51  修改接口参数
date: 2023-07-11 11:34  超时时间改为8s，以及打印报错信息
""", }

    form = {
        "get_token_url": {
            "ui_name": "获取Token接口URL",
            "value": "http://supplier.gip4u.com:8899/OrBitWCFServiceR13test/OrBitWebAPI.ashx"
        },
        "check_url": {
            "ui_name": "条码检查接口URL",
            "value": "http://supplier.gip4u.com:8899/OrBitWCFServiceR13/OrBitWebAPI.ashx"
        },
        "data_url": {
            "ui_name": "提交数据接口URL",
            "value": "http://supplier.gip4u.com:8181/MyService.asmx"
        },
        "ip_address": {
            "ui_name": "设备IP",
            "value": "127.0.0.1"
        },
        "username": {
            "ui_name": "用户名",
            "value": "srm"
        },
        "password": {
            "ui_name": "密码",
            "value": "srm"
        },
        "user_parameter": {
            "ui_name": "UserParameter",
            "value": "**************"
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_url = other_vo.get_value_by_cons_key("check_url")
        get_token_url = other_vo.get_value_by_cons_key("get_token_url")

        username = other_vo.get_value_by_cons_key("username")
        password = other_vo.get_value_by_cons_key("password")
        user_parameter = other_vo.get_value_by_cons_key("user_parameter")

        # 1. 先获取token
        token_param = {
            "UserName": username,
            "UserPassword": password
        }
        self.log.info(f"开始获取token....")
        ret_token = xrequest.RequestUtil.get(get_token_url, token_param, to_json=False)
        if not ret_token:
            return self.x_response("false", f"接口异常，获取token失败，error：{ret_token}")
        self.log.info(f"获取token成功")

        # 2. 获取条码
        self.log.info(f"开始获取条码...")
        pcb_sn = other_vo.get_pcb_sn()
        body_data = [
            {
                "LotSN": pcb_sn
            }
        ]

        get_sn_param = {
            "API": "CheckLotSNByATETest",
            "UserParameter": user_parameter,
            "UserData": json.dumps(body_data, ensure_ascii=False),
            "UserTicket": ret_token,
            "OutType": "JSON"
        }
        ret_data = xrequest.RequestUtil.post_form(check_url, get_sn_param, to_json=False, timeout=8)
        if not ret_data:
            return self.x_response("false", f"mes接口异常，未获取到条码！error_code:1")

        ret_json = json.loads(ret_data)
        sql_data_set = ret_json.get("SQLDataSet", [])
        if not sql_data_set:
            return self.x_response("false", f"mes接口异常，未获取到条码！error_code:2")

        data1 = sql_data_set[0]
        lot_sn = data1.get("LotSN", "")
        error_msg = data1.get("ReturnMessage", "")
        if not lot_sn:
            return self.x_response("false", f"mes接口异常，未获取到条码！error_code:3,error:{error_msg}")

        ret_lot_sn = lot_sn.replace(";", ",")
        return self.x_response("true", ret_lot_sn)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:

        data_url = data_vo.get_value_by_cons_key("data_url")
        ip_address = data_vo.get_value_by_cons_key("ip_address")
        username = data_vo.get_value_by_cons_key("username")
        password = data_vo.get_value_by_cons_key("password")
        data_url = f"{data_url}/ADCSubmitTestData"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        pcb_sn = pcb_entity.pcb_barcode

        board_data = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data = ""
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_data += comp_template.format(**{
                        "comp_tag": comp_entity.designator,
                        "comp_ng_code": comp_entity.repair_ng_code,
                        "comp_ng_str": comp_entity.repair_ng_str
                    })

            board_data += board_template.format(**{
                "barcode": barcode,
                "board_no": board_entity.board_no,
                "board_result": board_entity.get_repair_result("PASS", "FAIL"),
                "comp_data": comp_data
            })

        pcb_data = data_template.format(**{
            "pcb_repair_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "board_data": board_data
        })

        data_param = {
            "LotSN": pcb_sn,
            "IPAddress": ip_address,
            "UserName": username,
            "UserPassword": password,
            "TestData": pcb_data
        }

        result = xrequest.RequestUtil.post_form(data_url, data_param, to_json=False)
        ret_1 = xutil.XmlUtil.get_xml_root_by_str(result).text
        if not ret_1.startswith("0"):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret_1}")

        return self.x_response()


if __name__ == '__main__':
    ret_str = """<string xmlns="http://tempuri.org/">-1_[MES]IP[127.0.0.1]不存在绑定的规程，请检查</string>"""
    root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    print(root.text)
