# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/18 上午9:32
# Author     ：sch
# version    ：python 3.8
# Description：江阴众合
"""
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo, ButtonVo, ComboVo
from engine.MesEngine import ErrorMapEngine

login_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:CheckBaseData>
         <tem:userName>{username}</tem:userName>
         <tem:password>{password}</tem:password>
         <tem:pointCode>{point_code}</tem:pointCode>
         <tem:stationCode>{station_code}</tem:stationCode>
         <tem:eqpCode>{eqp_code}</tem:eqpCode>
         <tem:isCheckAccess>false</tem:isCheckAccess>
      </tem:CheckBaseData>
   </soapenv:Body>
</soapenv:Envelope>"""

get_sn_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetMeterInfoBySN>
         <tem:sn>{barcode}</tem:sn>
         <tem:snType></tem:snType>
         <tem:currentStep>{process_id}</tem:currentStep>
         <tem:schemeStep>{process_id}</tem:schemeStep>
         <tem:point>{point}</tem:point>
         <tem:isGetScheme>false</tem:isGetScheme>
         <tem:isCheckStep>true</tem:isCheckStep>
         <tem:isGetDoWhat>true</tem:isGetDoWhat>
         <tem:sys_user_id>{username}</tem:sys_user_id>
      </tem:GetMeterInfoBySN>
   </soapenv:Body>
</soapenv:Envelope>"""

data_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/"
                  xmlns:soas="http://schemas.datacontract.org/2004/07/Soas.Model.Entities">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:Save_Tr_Common_DataInfo>
            <tem:tr_Common_DataInfos>
                <soas:Tr_Common_DataInfo>
                    <soas:Header>
                        <soas:LineNum>0</soas:LineNum>
                        <soas:Object1/>
                        <soas:Object10/>
                        <soas:Object2/>
                        <soas:Object3/>
                        <soas:Object4/>
                        <soas:Object5/>
                        <soas:Object6>false</soas:Object6>
                        <soas:Object7>false</soas:Object7>
                        <soas:Object8/>
                        <soas:Object9/>
                        <soas:Created_by>LC</soas:Created_by>
                        <soas:Creation_date>{create_time}</soas:Creation_date>
                        <soas:Defect_code>ERR0099</soas:Defect_code>
                        <soas:Eqp_code>{eqp_code}</soas:Eqp_code>
                        <soas:Ext1>{create_time}</soas:Ext1>
                        <soas:Ext10/>
                        <soas:Ext2>{create_time}</soas:Ext2>
                        <soas:Ext3/>
                        <soas:Ext4>{work_code}</soas:Ext4>
                        <soas:Ext5/>
                        <soas:Ext6/>
                        <soas:Ext7>15</soas:Ext7>
                        <soas:Ext8/>
                        <soas:Ext9/>
                        <soas:Ict_defect_rawtext/>
                        <soas:Ict_retestcount/>
                        <soas:Id>{guid}</soas:Id>
                        <soas:Ini_password/>
                        <soas:Line_type/>
                        <soas:Location>{board_no}</soas:Location>
                        <soas:Mr_power_begin/>
                        <soas:Mr_power_deviation/>
                        <soas:Mr_power_end/>
                        <soas:Mr_running_deviation/>
                        <soas:Point_code>{point_code}</soas:Point_code>
                        <soas:R_sn>{barcode}</soas:R_sn>
                        <soas:Result>{result}</soas:Result>
                        <soas:S_sn>{barcode}</soas:S_sn>
                        <soas:Scheme_code/>
                        <soas:Station_code>{station_code}</soas:Station_code>
                        <soas:T_sn>{barcode}</soas:T_sn>
                        <soas:Wo_code>{work_code}</soas:Wo_code>
                    </soas:Header>
                    <soas:Lines>
                        <soas:Tr_common_lInfo>
                            <soas:LineNum>0</soas:LineNum>
                            <soas:Object1/>
                            <soas:Object10/>
                            <soas:Object2/>
                            <soas:Object3/>
                            <soas:Object4/>
                            <soas:Object5/>
                            <soas:Object6>false</soas:Object6>
                            <soas:Object7>false</soas:Object7>
                            <soas:Object8/>
                            <soas:Object9/>
                            <soas:Actual_deviation/>
                            <soas:Allow_deviation/>
                            <soas:Created_by>LC</soas:Created_by>
                            <soas:Creation_date>{create_time}</soas:Creation_date>
                            <soas:Ext1>{create_time}</soas:Ext1>
                            <soas:Ext10/>
                            <soas:Ext2>{create_time}</soas:Ext2>
                            <soas:Ext3/>
                            <soas:Ext4/>
                            <soas:Ext5/>
                            <soas:Ext6/>
                            <soas:Ext7/>
                            <soas:Ext8/>
                            <soas:Ext9/>
                            <soas:Fct_com_port/>
                            <soas:Header_id>{header_id}</soas:Header_id>
                            <soas:Ict_actual_value/>
                            <soas:Ict_deviation/>
                            <soas:Ict_measured_value/>
                            <soas:Ict_part_name/>
                            <soas:Ict_pin_negactiveid/>
                            <soas:Ict_pin_postiveid/>
                            <soas:Id>{detail_guid}</soas:Id>
                            <soas:Sequence>{board_no}</soas:Sequence>
                            <soas:Test_name>{test_name}</soas:Test_name>
                            <soas:Test_result>{result}</soas:Test_result>
                            <soas:Test_seq>{board_no}</soas:Test_seq>
                            <soas:Test_value>{test_value}</soas:Test_value>
                        </soas:Tr_common_lInfo>
                    </soas:Lines>
                    <soas:Links/>
                </soas:Tr_Common_DataInfo>
            </tem:tr_Common_DataInfos>
            <tem:sys_user_id>LC</tem:sys_user_id>
        </tem:Save_Tr_Common_DataInfo>
    </soapenv:Body>
</soapenv:Envelope>"""

global_data2 = xutil.LimitedDict(100)

global_data1 = {}


class Engine(ErrorMapEngine):
    version = {
        "title": "jiangyinzhonghe release v1.0.0.10",
        "device": "AIS303,AIS40X",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-18 18:27  init
date: 2024-01-19 14:33  修复登录bug
date: 2024-01-23 15:23  修改接口参数
date: 2024-01-24 15:10  拼板上传整板结果
""", }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://61.130.101.123:9201/Soas.Services/TPSInterfaceSer/",
        },
    }

    form = {
        "username": {
            "ui_name": "用户名",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "board_count": {
            "ui_name": "拼板数(补录条码)",
            "value": "1",
        },

    }

    combo = {
        "process_name": {
            "ui_name": "工序名称",
            "item": ["AOI", "选择焊AOI"],
            "value": "AOI",
        },
        "process_id": {
            "ui_name": "工序编码",
            "item": ["S0000007", "S0000194"],
            "value": "S0000007",
        },
        "collection_point": {
            "ui_name": "采集点",
            "item": ["50ZH01HJ0202", "50ZH01BF0203"],
            "value": "50ZH01HJ0202",
        },
        "device_code": {
            "ui_name": "设备编码",
            "item": ["ZH0101023", "ZH0102013"],
            "value": "ZH0101023",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登录",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        item1 = getattr(main_window, "combo_process_id")
        item2 = getattr(main_window, "combo_collection_point")
        item3 = getattr(main_window, "combo_device_code")

        item1.setEnabled(False)
        item2.setEnabled(False)
        item3.setEnabled(False)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        process_id = data_vo.get_value_by_cons_key("process_id")
        collection_point = data_vo.get_value_by_cons_key("collection_point")
        device_code = data_vo.get_value_by_cons_key("device_code")

        if not global_data1.get('is_login'):
            return self.x_response("false", "未登录，请先登录Mes！")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        all_barcode = pcb_entity.all_barcode
        the_first_sn = all_barcode[0]

        work_order = global_data2.get_value(the_first_sn, '')

        time_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT3)

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            ng_code_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    ng_str = comp_entity.repair_ng_str

                    ng_code_list.append(f"{comp_tag}@{ng_str}")

            main_guid = xutil.OtherUtil.get_uuid4_str()

            if board_entity.is_repair_ng():
                test_name = "ERR0099"
            else:
                test_name = ""

            data_param = data_template.format(**{
                "create_time": time_now,
                "eqp_code": device_code,
                "work_code": work_order,
                "guid": main_guid,
                "board_no": board_no,
                "point_code": collection_point,
                "barcode": barcode,
                "result": pcb_entity.get_repair_result("1", "0"),
                "test_name": test_name,
                "station_code": process_id,
                "header_id": main_guid,
                "detail_guid": xutil.OtherUtil.get_uuid4_str(),
                "test_value": ";".join(ng_code_list),
            })

            print ("data_param = ",data_param)

            ret_str = xrequest.RequestUtil.post_soap(api_url, data_param,
                                                     "http://tempuri.org/ITPSInterfaceSer/Save_Tr_Common_DataInfo")

            root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

            result = root[1][0].find('{http://tempuri.org/}Save_Tr_Common_DataInfoResult').text
            message = root[1][0].find('{http://tempuri.org/}message').text
            if result != 'true':
                error_msg = f"mes接口异常，上传数据失败，error：{message}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        username = other_vo.get_value_by_cons_key("username")
        process_id = other_vo.get_value_by_cons_key("process_id")
        collection_point = other_vo.get_value_by_cons_key("collection_point")
        board_count = other_vo.get_value_by_cons_key("board_count")

        pcb_sn = other_vo.get_pcb_sn()

        if not global_data1.get('is_login'):
            return self.x_response("false", "未登录，请先登录Mes！")

        get_param = get_sn_template.format(**{
            "username": username,
            "point": collection_point,
            "process_id": process_id,
            "barcode": pcb_sn
        })

        ret_str = xrequest.RequestUtil.post_soap(api_url, get_param,
                                                 "http://tempuri.org/ITPSInterfaceSer/GetMeterInfoBySN")

        root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

        info_item = root[1][0].find('{http://tempuri.org/}GetMeterInfoBySNResult')

        order_no = info_item.find('{http://schemas.datacontract.org/2004/07/Soas.Model.Entities}WorkOrder').text
        result = root[1][0].find('{http://tempuri.org/}result').text
        message = root[1][0].find('{http://tempuri.org/}message').text

        if result != 'true':
            return self.x_response("false", f"mes接口异常，校验条码失败，error：{message}")

        global_data2.add_item(pcb_sn, order_no)

        self.log.info(f"SN:{pcb_sn}  WorkOrder:{order_no} 已缓存！")

        barcode_map = other_vo.get_barcode_map()

        sn_number = len(barcode_map)

        if sn_number < 2:
            try:
                sn_number = int(board_count) + 2
            except Exception as err:
                return self.x_response("false", f"拼板数必须为数字，error：{err}")
            # return self.x_response("false", f"请查看AOI主软件是否传递了参数`barcodeList`！")

        board_number = sn_number - 2
        self.log.info(f"根据pcb_sn：{pcb_sn} 去 拼接 条码...")

        main_sn = pcb_sn[:-6]
        start_ix = int(pcb_sn[-6:])

        self.log.info(f"{main_sn=}, {start_ix=}")

        sn_list = []
        for i in range(board_number):
            barcode = f"{main_sn}{str(start_ix).rjust(6, '0')}"
            sn_list.append(barcode)

            start_ix += 1

        return self.x_response("true", ",".join(sn_list))

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url = btn_vo.get_value_by_cons_key("api_url")
        username = btn_vo.get_value_by_cons_key("username")
        password = btn_vo.get_value_by_cons_key("password")
        process_id = btn_vo.get_value_by_cons_key("process_id")
        collection_point = btn_vo.get_value_by_cons_key("collection_point")
        device_code = btn_vo.get_value_by_cons_key("device_code")
        btn_key = btn_vo.get_btn_key()

        if btn_key == 'login_btn':
            login_param = login_template.format(**{
                "username": username,
                "password": password,
                "point_code": collection_point,
                "station_code": process_id,
                "eqp_code": device_code,
            })

            ret_str = xrequest.RequestUtil.post_soap(api_url, login_param,
                                                     "http://tempuri.org/ITPSInterfaceSer/CheckBaseData")

            # root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
            #
            # result = root[0][0].find('{http://tempuri.org/}result').text
            # message = root[0][0].find('{http://tempuri.org/}message').text
            root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

            result = root[1][0].find('{http://tempuri.org/}result').text
            message = root[1][0].find('{http://tempuri.org/}message').text

            if result != 'true':
                return self.x_response("false", f"mes接口异常，登录失败，error：{message}")

            global_data1['is_login'] = True

        return self.x_response()

    def combo_index_changed(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()
        select_value = combo_vo.get_combo_value()

        data_map = {
            "AOI": 0,
            "选择焊AOI": 1,
        }

        if combo_key == "process_name":
            item1 = getattr(main_window, "combo_process_id")
            item2 = getattr(main_window, "combo_collection_point")
            item3 = getattr(main_window, "combo_device_code")

            select_index = data_map.get(select_value)

            item1.setCurrentIndex(select_index)
            item2.setCurrentIndex(select_index)
            item3.setCurrentIndex(select_index)

            main_window.config_data['combo']['process_id']['value'] = item1.currentText()
            main_window.config_data['combo']['collection_point']['value'] = item2.currentText()
            main_window.config_data['combo']['device_code']['value'] = item3.currentText()

            main_window.save_config_data_to_file()
