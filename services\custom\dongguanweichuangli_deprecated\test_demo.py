# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2023/12/20 上午10:48
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json

from common import xutil

if __name__ == '__main__':
    check_ret = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetUnitInfoResponse xmlns="http://tempuri.org/">
            <GetUnitInfoResult
                    xmlns:a="http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities"
                    xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:FFPartID i:nil="true"/>
                <a:FFTestID i:nil="true"/>
                <a:FFUnitID i:nil="true"/>
                <a:Id>0</a:Id>
                <a:Value><![CDATA[<UnitInfo><UnitData><Name>PartNumber</Name><Val
ue>TestPart1</Value><UserID>admin</UserID></UnitData><UnitData><Na
me>StationID</Name><Value>FFTester_AutoTest1</Value><UserID>admin
</UserID></UnitData><UnitData><Name>ProductionOrder</Name><Value>
Test001</Value><UserID>admin</UserID></UnitData></UnitInfo>]]></a:Value>
            </GetUnitInfoResult>
        </GetUnitInfoResponse>
    </s:Body>
</s:Envelope>"""

    #     data_ret = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    # <s:Body>
    # <SaveResultResponse xmlns="http://tempuri.org/">
    # <SaveResultResult
    # xmlns:a="http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities"
    # xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
    # <a:FFPartID>1</a:FFPartID>
    # <a:FFTestID>26</a:FFTestID>
    # <a:FFUnitID>1004</a:FFUnitID>
    # <a:Id>0</a:Id>
    # <a:Value>Success</a:Value>
    # </SaveResultResult>
    # </SaveResultResponse>
    # </s:Body>
    # </s:Envelope>"""
    #     root = xutil.XmlUtil.get_xml_root_by_str(data_ret)
    #     result_item = root[0][0][0]
    #     result_id = result_item.find("{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Id").text
    #     print(result_id, type(result_id))

#     get_sn_ret = """<flx>
#     <SN Value="G52687MMO" />
#     <SN Value="G52687MM1" />
#     <SN Value="G52687MM2" />
# </flx>"""
#     root = xutil.XmlUtil.get_xml_root_by_str(get_sn_ret)
#     ret_sn = []
#     for i in root:
#         sn = i.attrib.get('Value')
#         ret_sn.append(sn)
#     print(ret_sn)

#     ret_str = """<?xml version="1.0" encoding="utf-8"?>
# <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
#   <s:Body>
#     <GetUnitInfoResponse xmlns="http://tempuri.org/">
#       <GetUnitInfoResult xmlns:a="http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
#         <a:Data i:nil="true" />
#         <a:FFPartID i:nil="true" />
#         <a:FFTestID i:nil="true" />
#         <a:FFUnitID i:nil="true" />
#         <a:Id>6000</a:Id>
#         <a:Value>FFTesterHelper.GetSlaveConnection failed. Ensure station "AOI(A)_C203" is valid</a:Value>
#       </GetUnitInfoResult>
#     </GetUnitInfoResponse>
#   </s:Body>
# </s:Envelope>"""
#     root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
#     result_item = root[0][0][0]
#
#     result_id = result_item.find("{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Id").text
#     result_value = result_item.find("{http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities}Value").text
#     print(result_id)
#     print(result_value)

#     ret_str2 = """<?xml version="1.0" encoding="utf-8"?>
# <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
#     <soap:Body>
#         <GetUnitsByPanelResponse xmlns="http://tempuri.org/">
#             <GetUnitsByPanelResult />
#         </GetUnitsByPanelResponse>
#     </soap:Body>
# </soap:Envelope>"""
#     root = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
#     result_item = root[0][0][0]
#     print(result_item.text)

    # ret_str3 = '[{"ID":244,"Value":"JAMES9221070"},{"ID":245,"Value":"JAMES9221071"},{"ID":246,"Value":"JAMES9221072"},{"ID":247,"Value":"JAMES9221073"}]'
    # sn_list_ret = json.loads(ret_str3)
    #
    # sn_li = []
    # for item in sn_list_ret:
    #     sn_li.append(item.get('Value', ''))
    #
    # print(sn_li)
    ret_str4 = """<?xml version="1.0" encoding="utf-16"?>
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
  <s:Body>
    <ExecuteGenericFunctionResponse xmlns="http://tempuri.org/">
      <ExecuteGenericFunctionResult xmlns:a="http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
        <a:Data i:nil="true" />
        <a:FFPartID i:nil="true" />
        <a:FFTestID i:nil="true" />
        <a:FFUnitID i:nil="true" />
        <a:Id>0</a:Id>
        <a:Value i:nil="true" />
      </ExecuteGenericFunctionResult>
      <output>&lt;flx&gt;&lt;SN Value="LP401AVB2L26R"/&gt;&lt;SN Value="LP401AVB2L26S"/&gt;&lt;SN Value="LP401AVB2L26T"/&gt;&lt;SN Value="LP401AVB2L26U"/&gt;&lt;SN Value="LP401AVB2L26V"/&gt;&lt;SN Value="LP401AVB2L26W"/&gt;&lt;SN Value="LP401AVB2L26X"/&gt;&lt;SN Value="LP401AVB2L26Y"/&gt;&lt;SN Value="LP401AVB2L26Z"/&gt;&lt;SN Value="LP401AVB2L270"/&gt;&lt;/flx&gt;</output>
      <errorDescription />
    </ExecuteGenericFunctionResponse>
  </s:Body>
</s:Envelope>"""

    root = xutil.XmlUtil.get_xml_root_by_str(ret_str4)
    result_item = root[0][0]
    output_item = result_item.find('{http://tempuri.org/}output')
    print(output_item.text)

    ret_str = output_item.text
    print(ret_str)

    flx_root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

    ret_sn = []
    for item in flx_root:
        ret_sn.append(item.attrib.get('Value', ''))
    print(ret_sn)
    #
    # for item in output_item:
    #     print(item.tag)
    #
