# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : task_selection_dialog.py
# Time       ：2025/07/16 14:00
# Author     ：chencb
# version    ：python 3.8
# Description：任务列表选择对话框
"""
from PyQt5.QtWidgets import (QPushButton, QDialog,
                             QTableWidget, QTableWidgetItem, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QRadioButton, QButtonGroup,
                             QFrame, QMessageBox, QApplication)
from PyQt5.QtCore import Qt


class TaskSelectionDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.setModal(True)
        self.setWindowTitle("任务选择")
        self.setGeometry(100, 100, 800, 600)

        # 创建筛选布局
        filter_layout = QHBoxLayout()
        filter_label = QLabel("筛选:")
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("输入关键字搜索...")

        self.filter_button = QPushButton("筛选")
        self.filter_button.setDefault(True)
        self.filter_button.clicked.connect(self.filter_tasks)

        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_filter)

        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.filter_input)
        filter_layout.addWidget(self.filter_button)
        filter_layout.addWidget(self.reset_button)
        filter_layout.addStretch()

        self.table_widget = QTableWidget(self)
        headers = ["工序名", "任务号", "产品料号", "规格型号", "产品名称", "数量", "选择"]
        self.table_widget.setColumnCount(len(headers))
        self.table_widget.setHorizontalHeaderLabels(headers)

        self.table_widget.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_widget.setStyleSheet("""
            QTableWidget::item:selected {
                background-color: rgb(220, 230, 241);
                color: black;
            }
        """)

        self.original_data = []
        self.filtered_data = []
        self.selected_row = -1
        self.radio_buttons = []
        self.radio_group = QButtonGroup(self)

        self.table_widget.cellClicked.connect(self.on_table_clicked)

        # 创建底部按钮
        self.confirm_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.confirm_button.setFixedHeight(35)
        self.cancel_button.setFixedHeight(35)
        self.confirm_button.setMinimumWidth(80)
        self.cancel_button.setMinimumWidth(80)
        self.confirm_button.setStyleSheet("padding: 0 20px;")
        self.cancel_button.setStyleSheet("padding: 0 20px;")

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # 伸缩项推挤按钮到右侧
        button_layout.addWidget(self.cancel_button)
        button_layout.addSpacing(15)  # 按钮间距保持15px
        button_layout.addWidget(self.confirm_button)
        # 按钮布局边距（控制按钮行高度）
        button_layout.setContentsMargins(0, 10, 10, 10)

        # 主布局
        layout = QVBoxLayout()
        layout.addLayout(filter_layout)
        layout.addWidget(self.table_widget)
        layout.addLayout(button_layout)
        self.setLayout(layout)

        # 连接信号
        self.confirm_button.clicked.connect(self.on_confirm)
        self.cancel_button.clicked.connect(self.reject)

    def showEvent(self, event):
        super().showEvent(event)

        # 对话框在电脑窗口中居中显示
        if self.parent():
            self.move(self.parent().frameGeometry().center() - self.frameGeometry().center())
        else:
            screen_geometry = QApplication.desktop().availableGeometry()
            dialog_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            dialog_geometry.moveCenter(center_point)
            self.move(dialog_geometry.topLeft())

        # 对表格进行宽度自适应
        self.adjust_column_widths()

    # 表格数据从外部写入
    def set_data(self, data: list):
        table_data = []
        for task in data:
            # ["工序名", "任务号", "产品料号", "规格型号", "产品名称", "数量"]
            row_data = [task["process_name"], task["task_code"], task["material_code"],
                        task["specification"], task["material_name"], task["suggest_number"]]
            table_data.append(row_data)

        self.original_data = table_data
        self.filtered_data = table_data
        self.update_table(self.filtered_data)

    def update_table(self, data):
        for button in self.radio_buttons:
            self.radio_group.removeButton(button)
        self.radio_buttons.clear()

        self.table_widget.setRowCount(len(data))

        for row, item in enumerate(data):
            for col, value in enumerate(item):
                cell = QTableWidgetItem(str(value))
                self.table_widget.setItem(row, col, cell)

            radio_button = QRadioButton()
            radio_button.setObjectName(f"radio_{row}")
            radio_button.toggled.connect(lambda checked, r=row: self.on_radio_toggled(r, checked))
            self.radio_buttons.append(radio_button)
            self.radio_group.addButton(radio_button)

            container = QFrame()
            container_layout = QHBoxLayout(container)
            container_layout.addWidget(radio_button, alignment=Qt.AlignCenter)
            container_layout.setContentsMargins(0, 0, 0, 0)
            self.table_widget.setCellWidget(row, 6, container)

        if 0 <= self.selected_row < len(data):
            self.table_widget.selectRow(self.selected_row)
            self.radio_buttons[self.selected_row].setChecked(True)

        self.adjust_column_widths()

    def on_radio_toggled(self, row, checked):
        if checked:
            self.selected_row = row
            self.table_widget.selectRow(row)

    def filter_tasks(self):
        keyword = self.filter_input.text().strip().lower()

        if not keyword:
            self.filtered_data = self.original_data.copy()
            self.update_table(self.filtered_data)
            return

        self.filtered_data = []
        for row_data in self.original_data:
            if any(keyword in str(cell).lower() for cell in row_data):
                self.filtered_data.append(row_data)

        prev_selected_data = None
        if 0 <= self.selected_row < len(self.original_data):
            prev_selected_data = self.original_data[self.selected_row]

        self.update_table(self.filtered_data)

        if prev_selected_data and prev_selected_data in self.filtered_data:
            new_index = self.filtered_data.index(prev_selected_data)
            self.selected_row = new_index
            self.radio_buttons[new_index].setChecked(True)
        else:
            self.selected_row = -1

        if not self.filtered_data:
            QMessageBox.information(self, "筛选结果", "未找到匹配的任务")

    def reset_filter(self):
        self.filter_input.clear()
        self.filtered_data = self.original_data.copy()
        self.update_table(self.filtered_data)
        self.selected_row = -1

    def on_table_clicked(self, row, column):
        self.selected_row = row
        self.radio_buttons[row].setChecked(True)

    def on_confirm(self):
        if self.selected_row >= 0:
            self.accept()
        else:
            QMessageBox.warning(self, "警告", "请选择一个任务")

    def get_selected_task(self):
        # 返回任务的索引
        if 0 <= self.selected_row < len(self.filtered_data):
            return self.selected_row
        return None

    def resizeEvent(self, event):
        self.adjust_column_widths()
        return super().resizeEvent(event)

    def adjust_column_widths(self):
        self.table_widget.resizeColumnsToContents()
        self.table_widget.setColumnWidth(6, 40)

        table_width = self.table_widget.viewport().width()
        last_col_width = self.table_widget.columnWidth(6)
        remaining_width = table_width - last_col_width

        data_col_total = 0
        for col in range(6):
            data_col_total += self.table_widget.columnWidth(col)

        if data_col_total < remaining_width:
            ratio = remaining_width / data_col_total
            for col in range(6):
                new_width = int(self.table_widget.columnWidth(col) * ratio)
                self.table_widget.setColumnWidth(col, new_width)
