# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/10/31 上午9:15
# Author     ：sch
# version    ：python 3.8
# Description：珠海一博
"""
import json
import os
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine

txt_template = """{project_name}
{barcode}
{line_id}
{ship}
{repair_user}
{order_id}
{test_date}
{test_time}
{test_result}
{board_side}
{comp_robot_ng_number}{COMP_DATA}
"""

soap_template = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soap:Header/>
    <soap:Body>
        <tem:excute>
            <functionId>{func_name}</functionId>
            <body>{req_param}
            </body>
        </tem:excute>
    </soap:Body>
</soap:Envelope>"""


def xx_request(api_url: str, req_param: dict, func_name: str) -> dict:
    """
    统一的请求入口
    :param api_url:
    :param req_param:
    :param func_name:
    :return:
    """
    req_param_str = json.dumps(req_param, ensure_ascii=False, indent=4)

    body_data = soap_template.format(**{
        "func_name": func_name,
        "req_param": req_param_str,
    })

    ret1 = xrequest.RequestUtil.post_xml(api_url, body_data)

    root = xutil.XmlUtil.get_xml_root_by_str(ret1)
    ret_str1 = root[0][0][0].text
    ret_dict = json.loads(ret_str1)
    return ret_dict


class Engine(BaseEngine):
    version = {
        "title": "zhuhaiyibo release v1.0.0.6",
        "device": "501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-10-31 09:15  init
date: 2023-11-02 16:07  条码校验，上传数据 
date: 2023-11-08 16:10  修改接口请求参数
date: 2023-11-15 09:36  机种名称传板式名，lbChecks传复判后的器件列表
date: 2023-11-15 14:57  finalResult改成传递PASS,RPASS,FAIL
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081",
        },
        "device_sn": {
            "ui_name": "设备编码",
            "value": "",
        },
        "process_sn": {
            "ui_name": "工序编码",
            "value": "",
        },
        "username": {
            "ui_name": "用户编码",
            "value": "",
        },
        # "project_name": {
        #     "ui_name": "机种名称",
        #     "value": "",
        # },
        "line_id": {
            "ui_name": "线体",
            "value": "",
        },
        "ship": {
            "ui_name": "SHIP",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
    }

    other_form = {
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "value": "/ftp_mes/3F-SMT01-AOI"
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        process_sn = other_vo.get_value_by_cons_key("process_sn")
        username = other_vo.get_value_by_cons_key("username")

        project_name = other_vo.get_project_name()

        sn_list = other_vo.list_sn()
        check_sn = [{"lbId": i} for i in sn_list]
        check_param = {
            "devCode": device_sn,
            "wpCode": process_sn,
            "userCode": username,
            "proName": project_name,
            "pcbChecks": check_sn,
        }

        check_url = f"{api_url}/Service/SuportMlAoiService"
        # ret = xrequest.RequestUtil.post_json(check_url, check_param)
        ret = xx_request(check_url, check_param, "lbCheck")

        if ret.get("code") != "0000":
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        process_sn = data_vo.get_value_by_cons_key("process_sn")
        username = data_vo.get_value_by_cons_key("username")
        # project_name = data_dao.get_value_by_cons_key("project_name")
        line_id = data_vo.get_value_by_cons_key("line_id")
        ship = data_vo.get_value_by_cons_key("ship")
        order_id = data_vo.get_value_by_cons_key("order_id")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP端口号必须为数字，error：{err}")

        project_name = pcb_entity.project_name
        datetime_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        date_file = datetime_file[:8]
        time_file = datetime_file[8:]

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        ftp_log_path = f"{ftp_path}/{project_name}/{date_file}/log"
        ftp_picture_path = f"{ftp_path}/{project_name}/{date_file}/Picture"

        board_data = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data = []
            comp_data_str = ""

            is_cd_ftp_path = False

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator
                    comp_part = comp_entity.part

                    robot_ng_code = comp_entity.robot_ng_code
                    robot_ng_str = comp_entity.robot_ng_str
                    robot_ng_str1 = robot_ng_str

                    repair_result = comp_entity.get_final_result("REPASS", "REPASS", "REPAIR")
                    comp_data_str += f"""\n{comp_tag}_{board_no};{comp_part};{robot_ng_str1};{repair_result}"""

                    if not is_cd_ftp_path:
                        ftp_client.cd_or_mkdir(ftp_picture_path)
                        self.log.info(f"ftp路径已切换：{ftp_picture_path}")

                        is_cd_ftp_path = True

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        _, suffix = os.path.splitext(comp_src_img)
                        comp_dst_img = f"{barcode}_{comp_tag}_{robot_ng_code}_{datetime_file}{suffix}"
                        ftp_client.upload_file(comp_src_img, comp_dst_img)

                        ftp_filepath = f"{ftp_picture_path}/{comp_dst_img}"
                    else:
                        ftp_filepath = ""

                    if not comp_entity.is_repair_ng():
                        robot_ng_code = ""
                        robot_ng_str = ""

                    if comp_entity.is_repair_ng():
                        comp_data.append({
                            "badItemCode": robot_ng_code,
                            "badItemName": robot_ng_str,
                            "badPoint": comp_entity.designator,
                            "imagespath": ftp_filepath,
                        })

            # 1. 生成txt文档
            txt_content = txt_template.format(**{
                "project_name": project_name,
                "barcode": barcode,
                "line_id": line_id,
                "ship": ship,
                "repair_user": pcb_entity.repair_user,
                "order_id": order_id,
                "test_date": date_file,
                "test_time": time_file,
                "test_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_side": pcb_entity.board_side,
                "comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "COMP_DATA": comp_data_str,
            })

            # 上传至ftp服务器
            ftp_client.cd_or_mkdir(ftp_log_path)
            self.log.info(f"ftp路径已切换：{ftp_log_path}")

            log_filename = f"{barcode}_{time_file}.txt"
            ftp_client.upload_content(log_filename, txt_content)

            board_data.append({
                "lbId": barcode,
                "pcbNum": board_no,
                # "finalResult": board_entity.get_repair_result("OK", "NG"),
                "finalResult": board_entity.get_final_result("PASS", "RPASS", "FAIL"),
                "logpath": f"{ftp_log_path}/{log_filename}",
                "lbChecks": comp_data,
            })

        # 2. 保存ng器件图

        data_param = {
            "devCode": device_sn,
            "wpCode": process_sn,
            "userCode": username,
            "proName": pcb_entity.project_name,
            "pcbChecks": board_data,
        }

        data_url = f"{api_url}/Service/SuportMlAoiService"
        # ret = xrequest.RequestUtil.post_json(data_url, data_param)

        ret = xx_request(data_url, data_param, "uploadResult")

        if ret.get("code") != "0000":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

        return self.x_response()


if __name__ == '__main__':
    pass
#     ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
#     <soap:Body>
#         <ns2:excuteResponse xmlns:ns2="http://tempuri.org/">
#             <return>{"code":"0000","message":"OK","pcbChecks":[{"lbId":"DKL-2308072-0138","num":"1"},{"lbId":"DKL-2308072-0139","num":"2"}]}</return>
#         </ns2:excuteResponse>
#     </soap:Body>
# </soap:Envelope>"""
#
#     ret = xutil.XmlUtil.get_xml_root_by_str(ret_str)
#     print(ret[0][0][0].text)
