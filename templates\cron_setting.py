# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/cron_setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_CronSettingDialog(object):
    def setupUi(self, CronSettingDialog):
        CronSettingDialog.setObjectName("CronSettingDialog")
        CronSettingDialog.resize(441, 233)
        self.verticalLayout = QtWidgets.QVBoxLayout(CronSettingDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame = QtWidgets.QFrame(CronSettingDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.check_is_cron_clear = QtWidgets.QCheckBox(self.frame)
        self.check_is_cron_clear.setObjectName("check_is_cron_clear")
        self.verticalLayout_2.addWidget(self.check_is_cron_clear)
        self.frame_2 = QtWidgets.QFrame(self.frame)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.formLayout = QtWidgets.QFormLayout(self.frame_2)
        self.formLayout.setObjectName("formLayout")
        self.radio_fixed_time_clear = QtWidgets.QRadioButton(self.frame_2)
        self.radio_fixed_time_clear.setChecked(True)
        self.radio_fixed_time_clear.setObjectName("radio_fixed_time_clear")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.radio_fixed_time_clear)
        self.frame_3 = QtWidgets.QFrame(self.frame_2)
        self.frame_3.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.check_fixed_status_1 = QtWidgets.QCheckBox(self.frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.check_fixed_status_1.sizePolicy().hasHeightForWidth())
        self.check_fixed_status_1.setSizePolicy(sizePolicy)
        self.check_fixed_status_1.setText("")
        self.check_fixed_status_1.setChecked(True)
        self.check_fixed_status_1.setObjectName("check_fixed_status_1")
        self.horizontalLayout_2.addWidget(self.check_fixed_status_1)
        self.edit_fixed_time_1 = QtWidgets.QTimeEdit(self.frame_3)
        self.edit_fixed_time_1.setTime(QtCore.QTime(8, 0, 0))
        self.edit_fixed_time_1.setObjectName("edit_fixed_time_1")
        self.horizontalLayout_2.addWidget(self.edit_fixed_time_1)
        self.check_fixed_status_2 = QtWidgets.QCheckBox(self.frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.check_fixed_status_2.sizePolicy().hasHeightForWidth())
        self.check_fixed_status_2.setSizePolicy(sizePolicy)
        self.check_fixed_status_2.setText("")
        self.check_fixed_status_2.setChecked(True)
        self.check_fixed_status_2.setObjectName("check_fixed_status_2")
        self.horizontalLayout_2.addWidget(self.check_fixed_status_2)
        self.edit_fixed_time_2 = QtWidgets.QTimeEdit(self.frame_3)
        self.edit_fixed_time_2.setTime(QtCore.QTime(18, 0, 0))
        self.edit_fixed_time_2.setObjectName("edit_fixed_time_2")
        self.horizontalLayout_2.addWidget(self.edit_fixed_time_2)
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.frame_3)
        self.line = QtWidgets.QFrame(self.frame_2)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.SpanningRole, self.line)
        self.radio_interval_time_clear = QtWidgets.QRadioButton(self.frame_2)
        self.radio_interval_time_clear.setObjectName("radio_interval_time_clear")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.radio_interval_time_clear)
        self.frame_4 = QtWidgets.QFrame(self.frame_2)
        self.frame_4.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.spin_interval_time = QtWidgets.QSpinBox(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spin_interval_time.sizePolicy().hasHeightForWidth())
        self.spin_interval_time.setSizePolicy(sizePolicy)
        self.spin_interval_time.setProperty("value", 1)
        self.spin_interval_time.setObjectName("spin_interval_time")
        self.horizontalLayout.addWidget(self.spin_interval_time)
        self.label = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.frame_4)
        self.label_2 = QtWidgets.QLabel(self.frame_2)
        self.label_2.setStyleSheet("color:red")
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.SpanningRole, self.label_2)
        self.verticalLayout_2.addWidget(self.frame_2)
        self.verticalLayout.addWidget(self.frame)
        self.buttonBox = QtWidgets.QDialogButtonBox(CronSettingDialog)
        self.buttonBox.setOrientation(QtCore.Qt.Horizontal)
        self.buttonBox.setStandardButtons(QtWidgets.QDialogButtonBox.Cancel|QtWidgets.QDialogButtonBox.Ok)
        self.buttonBox.setObjectName("buttonBox")
        self.verticalLayout.addWidget(self.buttonBox)

        self.retranslateUi(CronSettingDialog)
        self.buttonBox.accepted.connect(CronSettingDialog.accept) # type: ignore
        self.buttonBox.rejected.connect(CronSettingDialog.reject) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(CronSettingDialog)

    def retranslateUi(self, CronSettingDialog):
        _translate = QtCore.QCoreApplication.translate
        CronSettingDialog.setWindowTitle(_translate("CronSettingDialog", "定时设置"))
        self.check_is_cron_clear.setText(_translate("CronSettingDialog", "开启定时清除功能"))
        self.radio_fixed_time_clear.setText(_translate("CronSettingDialog", "固定时间清除"))
        self.edit_fixed_time_1.setDisplayFormat(_translate("CronSettingDialog", "HH:mm"))
        self.edit_fixed_time_2.setDisplayFormat(_translate("CronSettingDialog", "HH:mm"))
        self.radio_interval_time_clear.setText(_translate("CronSettingDialog", "间隔时间清除"))
        self.label.setText(_translate("CronSettingDialog", "小时"))
        self.label_2.setText(_translate("CronSettingDialog", "修改定时设置后重启本软件生效！！！"))
