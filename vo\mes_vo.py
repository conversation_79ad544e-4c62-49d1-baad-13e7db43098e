#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2023/4/9
# Author: sunchangheng
"""
数据访问层
"""
import os.path
from collections import ChainMap
from typing import List

from common import xcons
from entity.MesEntity import PcbEntity


class DataVo(object):
    """
    数据访问层
    """

    def __init__(self, pcb_info: dict, board_data: dict, config_data: dict, pcb_entity_list: list = None,
                 json_data: dict = {}):
        self.config_data_json = config_data
        # combo_config = config_data.get("combo", {})
        # combo_config.update(config_data.get("form", {}))
        # combo_config.update(config_data.get("path", {}))
        # self.all_config_json = combo_config
        self.json_data = json_data

        self.all_config_json = ChainMap(
            config_data.get("combo", {}),
            config_data.get("form", {}),
            config_data.get("path", {}),
            config_data.get("other_form", {}),
            config_data.get("other_combo", {}),
            config_data.get("other_path", {}),
        )

        self.pcb_entity = PcbEntity(pcb_info, board_data)
        self.pcb_entity_list: List[PcbEntity] = pcb_entity_list

    def get_review_path(self):
        """
        获取主软件传过来的 ReviewPath
        :return:
        """
        return self.json_data.get("ReviewPath")

    def get_value_by_cons_key(self, cons_key, not_null: bool = False, to_int: bool = False):
        """
        获取界面上的配置参数
        """
        item = self.all_config_json.get(cons_key, {})
        cons_value = item.get("value", "未配置界面参数")

        if not_null:
            # 不可以为空
            if not cons_value:
                raise Exception(f"{item.get('ui_name', '')}不允许为空！")

        if to_int:
            try:
                cons_value = int(cons_value)
            except Exception as err:
                raise Exception(f"{item.get('ui_name', '')}必须为数字！err:{err}")

        return cons_value

    def get_inspect_type(self):
        """
        获取发送类型  （inspector,主软件检测完发送） （repair,维修站复判后发送）
        :return:
        """
        return self.pcb_entity.pcb_info_json.get('inspect_type', 'repair')


class OtherVo(object):
    """
    其他数据访问层
    """

    def __init__(self, json_data: dict, config_data: dict):
        # 配置文件相关
        self.config_data_json = config_data
        # combo_config = config_data.get("combo", {})
        # combo_config.update(config_data.get("form", {}))
        # combo_config.update(config_data.get("path", {}))
        # self.all_config_json = combo_config
        self.all_config_json = ChainMap(
            config_data.get("combo", {}),
            config_data.get("form", {}),
            config_data.get("path", {}),
            config_data.get("other_form", {}),
            config_data.get("other_combo", {}),
            config_data.get("other_path", {}),
        )

        # 接口调用参数相关
        self.json_data = json_data
        self.func_name = json_data.get("funcName")

    def get_barcode_map(self):
        """
        获取条码列表,格式如下：
        "barcodeList": {
            "-2": "治具条码",
            "-1": "板边条码",
            "1": "拼板1条码",
            "2": "",
            "3": "拼板3条码"
        }
        :return:
        """
        return self.json_data.get("barcodeList", {})

    def get_production_desc(self):
        """
        获取生产过程的描述
        :return:
        """
        return self.json_data.get('funcArgs')[0]

    def get_production_code(self):
        """
        获取生产过程的描述
        :return:
        """
        return self.json_data.get('statusCode')

    def get_origin_param(self, origin_key: str, default_value=None):
        """
        获取主软件传过来的参数
        :param origin_key:
        :param default_value:
        :return:
        """
        return self.json_data.get(origin_key, default_value)

    def get_value_by_cons_key(self, cons_key, not_null: bool = False, to_int: bool = False):
        """
        获取界面上的配置参数
        """
        item = self.all_config_json.get(cons_key, {})
        cons_value = item.get("value", "未配置界面参数")

        if not_null:
            # 不可以为空
            if not cons_value:
                raise Exception(f"{item.get('ui_name', '')}不允许为空！")

        if to_int:
            try:
                cons_value = int(cons_value)
            except Exception as err:
                raise Exception(f"{item.get('ui_name', '')}必须为数字！err:{err}")

        return cons_value

    def get_func_args(self):
        """
        获取funcArgs参数
        """
        return self.json_data.get("funcArgs", None)

    def list_sn(self) -> list:
        """
        获取条码校验需要校验的条码   [条码校验]
        格式：
        "funcArgs": [
            [
            "拼板1条码",
            "拼板3条码"
            ]
        ],
        """
        return self.json_data.get("funcArgs", [[]])[0]

    def get_pcb_sn(self) -> str:
        """
        设备上扫到的第一个条码   [从mes获取条码]
        格式：
         "funcArgs": [
            "pcb_barcode001"
        ],
        """
        return str(self.json_data.get("funcArgs", [])[0])

    def get_project_name(self) -> str:
        """
        获取板式名
        """
        project_path = self.json_data.get("projectName", "")
        return os.path.basename(project_path)

    def get_review_path(self) -> str:
        """
        条码校验：获取Mes路径，如果双面的则会用,号分隔
        """
        review_path = self.json_data.get("resultPaths", "")

        if "," in review_path:
            review_path = review_path.replace(",", ";")

        return review_path

    def get_track_index(self) -> int:
        """
        获取轨道信息， 有1轨，2轨
         (1, 1轨)  （2, 2轨）
        """
        return int(self.json_data.get("trackIndex", 0)) + 1

    def get_order_id(self):
        """
        获取批次号
        """
        return self.json_data.get("orderID", "").strip()

    def get_device_status_str(self):
        """
        获取设备状态 (v1版本)
        """
        return self.list_sn()

    def get_old_device_status_code(self):
        """
        获取设备状态码 （v1版本）
        :return:
        """
        old_status_str = self.get_device_status_str()
        old_status_code = xcons.DEVICE_STATUS.get(old_status_str, "99")

        if "safedoor" in old_status_str or "安全门" in old_status_str:
            old_status_code = "10"

        return old_status_code

    def get_status_code(self):
        """
        获取设备状态code
        2022-8-10更新
        :return:
        """

        old_code = self.get_old_device_status_code()

        old_code_map = {
            "01": "999-01",
            "02": "01",
            "03": "02",
            "04": "999-04",
        }

        return self.json_data.get("statusCode", old_code_map.get(old_code, old_code))

    def get_status_desc(self):
        """
        获取设备状态描述
        2022-8-10更新
        :return:
        """
        return self.json_data.get("statusDesc")

    def get_status_is_stop(self):
        """
        是否停机
        2022-09-08更新
        """
        return self.json_data.get("isStop")

    def get_status_code_v3(self):
        """
        V3版本状态码
        :return:
        """
        return self.json_data.get("statusCodeV3", "9999")

    def get_status_desc_v3(self):
        """
        V3版本状态描述/状态名称
        :return:
        """
        return self.json_data.get("statusDescV3")

    def get_login_info(self):
        """
        获取登录的账号和密码
        :return: (username, password)
        """
        return self.json_data.get("login_username"), self.json_data.get("login_password")

    def get_barcode(self):
        """
        获取条码
        :return:
        """
        return self.json_data.get('barcode', '')

    def get_panel_info(self):
        """
        获取面板数据
        {"funcArgs":"","funcName":"PanelData","info":{"CTTime":"1.41","fail_qty":403,"pass_qty":78,"pcbResult":false,"repass_qty":142,"total_qty":623},"trackIndex":0}
        :return:
        """
        return self.json_data.get("info", {})

    def get_device_username(self):
        """
        获取发送设备状态时，主软件传过来的用户名
        :return:
        """
        return self.json_data.get("userName", "")

    def get_btn_click_type(self):
        """
        获取主软件按钮点击类型

        type:
        ClearPanelData: 统计数据清零操作推送
        TrackWidthSet: 轨道设置宽度推送
        """
        return self.json_data.get("type", "")


class ComboVo(object):
    def __init__(self, json_data: dict, config_data: dict):
        self.json_data = json_data

        self.config_data_json = config_data
        # combo_config = config_data.get("combo", {})
        # combo_config.update(config_data.get("form", {}))
        # combo_config.update(config_data.get("path", {}))
        # self.all_config_json = combo_config
        self.all_config_json = ChainMap(
            config_data.get("combo", {}),
            config_data.get("form", {}),
            config_data.get("path", {}),
            config_data.get("other_form", {}),
            config_data.get("other_combo", {}),
            config_data.get("other_path", {}),
        )

    def get_value_by_cons_key(self, cons_key, not_null: bool = False, to_int: bool = False):
        """
        获取界面上的配置参数
        """
        item = self.all_config_json.get(cons_key, {})
        cons_value = item.get("value", "未配置界面参数")

        if not_null:
            # 不可以为空
            if not cons_value:
                raise Exception(f"{item.get('ui_name', '')}不允许为空！")

        if to_int:
            try:
                cons_value = int(cons_value)
            except Exception as err:
                raise Exception(f"{item.get('ui_name', '')}必须为数字！err:{err}")

        return cons_value

    def get_combo_key(self) -> str:
        """
        获取combo key
        :return:
        """
        return self.json_data.get("combo_key")

    def get_combo_value(self) -> str:
        """
        获取combo value
        :return:
        """
        return self.json_data.get("combo_value")

    def get_combo_items(self) -> list:
        """
        获取combo items
        :return:
        """
        pass


class ButtonVo(object):
    def __init__(self, json_data: dict, config_data: dict):
        self.json_data = json_data

        self.config_data_json = config_data
        # combo_config = config_data.get("combo", {})
        # combo_config.update(config_data.get("form", {}))
        # combo_config.update(config_data.get("path", {}))
        # self.all_config_json = combo_config
        self.all_config_json = ChainMap(
            config_data.get("combo", {}),
            config_data.get("form", {}),
            config_data.get("path", {}),
            config_data.get("other_form", {}),
            config_data.get("other_combo", {}),
            config_data.get("other_path", {}),
        )

    def get_value_by_cons_key(self, cons_key, not_null: bool = False, to_int: bool = False):
        """
        获取界面上的配置参数
        """
        item = self.all_config_json.get(cons_key, {})
        cons_value = item.get("value", "未配置界面参数")

        if not_null:
            # 不可以为空
            if not cons_value:
                raise Exception(f"{item.get('ui_name', '')}不允许为空！")

        if to_int:
            try:
                cons_value = int(cons_value)
            except Exception as err:
                raise Exception(f"{item.get('ui_name', '')}必须为数字！err:{err}")

        return cons_value

    def get_btn_key(self):
        """
        获取被点击按钮的 key

        btn_key  这个btn_key 已经去掉 `btn_` 前缀
        :return: 如： get_order
        """
        return self.json_data.get("pure_key")
