# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/27 下午2:23
# Author     ：sch
# version    ：python 3.8
# Description：深圳能效
"""
from typing import Any

from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine
from services.custom.nengxiao import dll_service


class Engine(ErrorMapEngine):
    version = {
        "title": "nengxiao release v1.0.0.4",
        "device": "203P,303B",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-27 14:52  上传数据
date: 2024-01-15 18:04  python直接调用dll上传
date: 2024-07-24 11:03  上传数据时，对调sn和user参数的位置
date: 2024-07-30 14:44  使用最新的dll
""", }

    form = {
        "mac_id": {
            "ui_name": "MACID",
            "value": "",
        },
        "user": {
            "ui_name": "User",
            "value": "",
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        mac_id = other_vo.get_value_by_cons_key("mac_id")
        user = other_vo.get_value_by_cons_key("user")

        error_msg = ''
        for sn in other_vo.list_sn():
            ret_str = dll_service.check_station(mac_id, sn, user)

            if ret_str != 'OK':
                error_msg = ret_str

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        mac_id = data_vo.get_value_by_cons_key("mac_id")
        user = data_vo.get_value_by_cons_key("user")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        error_msg = ''
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            error_code_list = []
            test_data = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    error_code_list.append(comp_entity.repair_ng_code)
                    test_data.append(f"{comp_entity.designator}:{comp_entity.repair_ng_str}")

            res = board_entity.get_repair_result("OK", "NG")
            ret_str = dll_service.upload_res(mac_id, user, barcode, res, ",".join(error_code_list),
                                             ";".join(test_data))
            if ret_str != 'OK':
                error_msg = ret_str

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
