# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : upload_img.py
# Time       ：2024/11/30 下午3:09
# Author     ：sch
# version    ：python 3.8
# Description：
"""

import boto3

"""
地址    zzzx.zhhcp.sunwoda.com
Access key:  enp6eA==
Secret key:  47f992d5f604ef025470821d86da8317
"""

# 从环境变量或安全的方式中获取访问密钥和秘密密钥
# access_key = 'enp6eA=='
# secret_key = '47f992d5f604ef025470821d86da8317'
# endpoint_url = ''http://zzzx.zhhcp.sunwoda.com''

endpoint_url = "http://lxeds.sunwoda.com:12001"
access_key = '4EQCFXZ4MTTYSSYM8BHE'
secret_key = 'pYbgu0Kw7VV0G9T0RJHqlLygW9JE2dlpD1W4RTqT'


# 创建会话
session = boto3.Session(
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key,
)

# 创建 S3 客户端，并指定自定义终端节点
s3_client = session.client(
    's3',
    endpoint_url=endpoint_url,  # 自定义终端节点
    # config=Config(signature_version='s3v4'),  # 如果适用，可以指定签名版本
    region_name='',  # 如果适用，可以指定区域名称
)

# 要上传的图片路径
file_path = './COMP1039_1039.png'  # 替换为你的图片路径
bucket_name = 'ddb'  # 替换为你的存储桶名称
object_name = "20241216/COMP1039_test3.png"  # 存储桶中的对象名称（即保存后的文件名）

try:
    # 上传文件
    with open(file_path, 'rb') as data:
        s3_client.put_object(
            Bucket=bucket_name,
            Key=object_name,
            Body=data,
            ACL='public-read'  # 设置对象的 ACL 为公共可读
        )
        print(f"File {file_path} uploaded to bucket {bucket_name} as {object_name}.")
        print("上传完毕")

        response = s3_client.generate_presigned_url('get_object',
                                                    Params={'Bucket': bucket_name,
                                                            'Key': object_name},
                                                    ExpiresIn=100000000)
        print(f"临时访问链接：{response}")
except Exception as e:
    print(f"Error uploading file: {e}")
