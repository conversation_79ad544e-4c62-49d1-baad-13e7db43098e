# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/5/14 上午9:24
# Author     ：sch
# version    ：python 3.8
# Description：Taxan ATAOI_2019-30100
"""
from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


template_a = """Program name,{project_name}
Test time,{test_time}
Operator,{operator}
Barcode,{barcode}
Cavity number,{cavity_number}
Cavity judgement number,{cavity_judgment_number}
Total inspection point,{total_inspection_point}
Total judgement point,{total_judgement_point}

BoardNo,Barcode,BoardFinalResult,CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult{comp_data_str}
"""

template_b = """检测日期,检测时间,板式名称,拼板条码,拼板序号,拼板结果
Date,time,program name,barcode,cavity,result"""

template_comp_row = """
{BoardNo},{Barcode},{BoardFinalResult},{CompDesignator},{CompPart},{CompPackage},{CompType},{CompRobotCode},{CompRobotResult},{CompUserCode},{CompUserResult}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "JTU release v1.0.0.2",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-05-14 14:08  上传数据
date: 2024-08-08 14:20  英文支持
""", }

    path = {
        "save_path_a": {
            "ui_name": "保存路径A",
            "ui_name_en": "SavePathA",
            "value": "",
        },
        "save_path_b": {
            "ui_name": "保存路径B",
            "ui_name_en": "SavePathB",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_a = data_vo.get_value_by_cons_key("save_path_a")
        save_path_b = data_vo.get_value_by_cons_key("save_path_b")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        cavity_judgment_number = 0
        total_inspection_point = 0
        total_judgement_point = 0

        comp_data_str = ""

        t3 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT9)
        project_name = pcb_entity.project_name

        b_content = template_b
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_no = board_entity.board_no
            barcode = board_entity.barcode

            if board_entity.is_robot_ng():
                cavity_judgment_number += 1

            total_inspection_point += board_entity.comp_total_number
            total_judgement_point += board_entity.comp_robot_ng_number

            result = board_entity.get_final_result()

            b_content += f"\n{t3[:10]},{t3[11:]},{project_name},{barcode},{board_no},{result}"

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += template_comp_row.format(**{
                    "BoardNo": board_no,
                    "Barcode": barcode,
                    "BoardFinalResult": board_entity.get_final_result(),
                    "CompDesignator": comp_entity.designator,
                    "CompPart": comp_entity.part,
                    "CompPackage": comp_entity.package,
                    "CompType": comp_entity.type,
                    "CompRobotCode": comp_entity.robot_ng_code,
                    "CompRobotResult": comp_entity.robot_ng_str,
                    "CompUserCode": comp_entity.repair_ng_code,
                    "CompUserResult": comp_entity.repair_ng_str
                })

        content_a = template_a.format(**{
            "project_name": project_name,
            "test_time": t3,
            "operator": pcb_entity.repair_user,
            "barcode": pcb_entity.pcb_barcode,
            "cavity_number": pcb_entity.board_count,
            "cavity_judgment_number": cavity_judgment_number,
            "total_inspection_point": total_inspection_point,
            "total_judgement_point": total_judgement_point,
            "comp_data_str": comp_data_str,
        })

        t1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        t2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        a_filepath = f"{save_path_a}/{t1}_{t2}_{project_name}.csv"
        content_a = content_a.replace("\n", "\r\n")
        xutil.FileUtil.write_content_to_file(a_filepath, content_a)

        b_filepath = f"{save_path_b}/{t1}_{t2}_{project_name}.csv"
        b_content = b_content.replace("\n", "\r\n")
        xutil.FileUtil.write_content_to_file(b_filepath, b_content)

        return self.x_response()
