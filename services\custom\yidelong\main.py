# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/22 上午9:14
# Author     ：sch
# version    ：python 3.8
# Description：易德龙
"""
import base64
import json
import time
from typing import Any

import requests

from common import xrequest, xutil, xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

board_template = """程序名：{pcb_project_name}
测试时间：{pcb_test_time}
操作员：{pcb_repair_user}
大板条码：{pcb_sn}
整板结果：{pcb_final_result}
拼板条码：{board_sn}
拼板序号：{board_no}
拼板结果：{board_final_result}
拼板器件数量：{board_comp_number}
拼板器件复判NG数量：{board_comp_user_ng_number}

CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult,CompImage{CompData}
"""

comp_template = """
---位号：{comp_designator}  料号：{comp_part}  封装：{comp_package} 　类型：{comp_type}　 检测不良代码：{comp_robot_code}  检测结果：{comp_robot_result}  复判不良代码：{comp_user_code}  复判结果：{comp_user_result}  器件图片：{comp_image}"""

xml_comp_template = """
    <ns1:TestXML name="{board_no}:{comp_tag}">
        <ns1:IndictmentXML algorithm="" indictmentType="">
            <ns1:RepairActionXML repairOperator="{repair_user}" repairTime="{repair_time}" repairActionType="-"
                                 indictmentType="{comp_ng_str}" comment="-" repairStatus="{repair_status}"/>
            <ns1:ComponentXML packageId="{comp_package}" partId="{comp_part}" designator="{board_no}:{comp_tag}"
                              imagepath="{comp_image}"/>
        </ns1:IndictmentXML>
    </ns1:TestXML>"""

xml_board_template = """<?xml version="1.0" encoding="UTF-8"?>
<ns1:BoardTestXMLExport numberOfIndictedComponents="{comp_robot_ng_number}" testerTestStartTime="{start_time}"
                        testTime="{start_time}" repairStationId="{repair_station}"
                        testStatus="{test_status}" testerTestEndTime="{end_time}"
                        xmlns:ns1="http://tempuri.org/BoardTestXMLExport.xsd" numberOfIndictedPins="0"
                        numberOfComponentsTested="{comp_total_number}" numberOfJointsTested="0" numberOfDefects="{robot_ng_type_number}"
                        repairStatus="{repair_status}" numberOfDefectBox="{comp_robot_ng_number}" numberOfDefectBoxAndPin="{comp_robot_ng_number}">
    <ns1:BoardXML imageId="{board_no}" serialNumber="{barcode}" assemblyRevision="{project_name}"
                  boardType="{board_type}" boardRevision="{board_revision}"/>
    <ns1:StationXML testerName="{tester_name}" stage="{stage}"/>
    <ns1:RepairEventXML numberOfVariationOkDefects="{comp_repass_number}" numberOfFalseCalledPins="{comp_repair_ng_number}" numberOfRepairedComponents="{comp_repair_ng_number}"
                        numberOfVariationOkPins="{comp_repass_number}" numberOfRepairedPins="{comp_repair_ng_number}" numberOfRepairLaterPins=""
                        numberOfFalseCalledDefects="{repair_repass_type_number}" numberOfActiveDefects="0" numberOfVariationOkComponents="0"
                        repairEndTime="{repair_time}" repairStartTime="{repair_time}"
                        numberOfRepairLaterDefects="0" repairOperator="{repair_user}" numberOfRepairLaterComponents="0"
                        numberOfActiveComponents="0" numberOfActivePins="0" numberOfRepairedDefects="{repair_ng_type_number}"
                        numberOfFalseCalledComponents="{comp_repass_number}"/>{comp_data_str}
</ns1:BoardTestXMLExport>"""


def x_post_json(
        url,
        body_data: dict,
        headers: dict = None,
        params: dict = None,
        to_json=True,
        timeout=5,
        auth=None,
        files: dict = None,
) -> Any:
    """
    post 接口，并默认返回json数据
    请求头：content-type: application/json
    :param url: 请求API
    :param headers: 请求头
    :param params: 请求参数
    :param body_data: 请求体参数
    :param to_json: 是否需要将返回参数转成 `python dict` 类型
    :param auth: auth
    :param timeout: timeout
    :param files
    :return:
    """

    # body_data_str = ""
    # if type(body_data) is dict:
    #     body_data_str = json.dumps(body_data, ensure_ascii=False)
    #
    # param_list = []
    #
    # if params:
    #     for k, v in params.items():
    #         param_list.append(f"{k}={v}")
    #
    # req_param = ""
    # if param_list:
    #     req_param = f"?{'&'.join(param_list)}"

    # log.info(f"-->请求URL：{url}  【Body Json】参数：\n{body_data_str[:log_number]}")
    # if headers:
    #     log.info(f"-->请求头：\n{headers}")
    # log.info(f"-->POST 请求URL：{url}{req_param}")

    if auth:
        res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, auth=auth,
                            files=files)
    else:
        res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, files=files)

    log.info(f"post_json 响应参数 code: {res} msg: {res.text}")

    if not res:
        # log.warning(f"post_json code: {res}  error: {res.text}")
        raise Exception("接口调用出错2003，请检查接口服务是否正常！")

    if to_json:
        return res.json()
    else:
        return res.text


def upload_file_or_image_to_mes(api_url: str, base_param: dict, filepath: str) -> dict:
    """
    上传文件/图片到服务器
    :param api_url:
    :param base_param:
    :param filepath:
    :return:
    """
    param_str = json.dumps(base_param, ensure_ascii=False, indent=4)

    log.info(f"-->上传图片请求URL：{api_url} 【Body Json】参数(warning：此处LogFileStream参数过大，未打印！)：\n{param_str}")
    log.info(f"LogFileStream参数为：{filepath} 的base64编码格式！")

    base_param["LogFileStream"] = xutil.ImageUtil.file_to_base64_content(filepath)

    ret = x_post_json(api_url, base_param)
    return ret


class Engine(ErrorMapEngine):
    version = {
        "title": "yidelong release v1.0.0.15",
        "device": "203,303,501",
        "feature": ["获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-22 10:49  条码校验，上传数据
date: 2023-12-26 16:41  文件内容改成base64编码
date: 2024-01-05 10:56  需求变更，详见需求文档
date: 2024-01-10 09:12  接口参数移动到其他参数设置窗口
date: 2024-02-27 09:44  501需要过两次站，且没有条码的话，需要自动获取对面板面的条码
date: 2024-03-04 11:22  只校验(check)A站点，不校验(check)B站点
date: 2025-06-30 15:21  ATAOI_2019-39377：获取条码
date: 2025-07-02 11:19  板卡无条码，不触发获取条码，修改放发mes里获取
date: 2025-07-02 17:43  修改获取条码参数strMONO
date: 2025-07-03 11:33  mes接口返回json有变更，重新解析
""",
    }

    other_form = {
        "aoi_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "",
        },
        "api_url_check_other": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data_other": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
        "api_url_data_file_other": {
            "ui_name": "接口URL(上传数据FILE)",
            "value": "",
        },
        "api_url_data_img_other": {
            "ui_name": "接口URL(上传图片IMAGE)",
            "value": "",
        },
    }

    form = {
        "station_t": {
            "ui_name": "站点号(T)",
            "value": "",
        },
        "station_b": {
            "ui_name": "站点号(B)",
            "value": "",
        },
        "customer_name": {
            "ui_name": "客户名称",
            "value": "",
        },
        "product_model": {
            "ui_name": "产品机种",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "line": {
            "ui_name": "线别",
            "value": "",
        },
        "aoi_station": {
            "ui_name": "AOI站别",
            "value": "",
        },
        "repair_station": {
            "ui_name": "维修站名称",
            "value": "",
        },
        "tester_name": {
            "ui_name": "测试机种",
            "value": "",
        },
        "board_revision": {
            "ui_name": "boardRevision",
            "value": "",
        },
        "stage": {
            "ui_name": "stage",
            "value": "",
        },
        "str_mo_no": {
            "ui_name": "工单号",
            "value": "",
        },
        "str_station": {
            "ui_name": "站位",
            "value": "",
        },
    }

    path = {
        "xml_save_path": {
            "ui_name": "存储路径",
            "value": "",
        },
    }

    combo = {
        "is_get_sn": {
            "ui_name": "是否获取条码",
            "item": ["是", "否"],
            "value": "是",
        },
        "device_type_1": {
            "ui_name": "设备机型",
            "item": ["20X/30X", "501"],
            "value": "20X/30X",
        },
        "board_side_1": {
            "ui_name": "板面(501自动识别)",
            "item": ["T", "B"],
            "value": "T",
        },
        "board_side_501": {
            "ui_name": "501过站类型",
            "item": ["过T+B站", "只过T站"],
            "value": "过T+B站",
        }
    }

    other_combo = {
        "time_sleep": {
            "ui_name": "过站间隔(秒)",
            "item": ["0.5", "1", "2", "5"],
            "value": "1",
        }
    }

    def __init__(self):
        self.main_window = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.main_window = main_window

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check_other = other_vo.get_value_by_cons_key("api_url_check_other")
        station_t = other_vo.get_value_by_cons_key("station_t")
        station_b = other_vo.get_value_by_cons_key("station_b")
        device_type_1 = other_vo.get_value_by_cons_key("device_type_1")
        board_side_1 = other_vo.get_value_by_cons_key("board_side_1")
        board_side_501 = other_vo.get_value_by_cons_key("board_side_501")

        error_msg = ""
        log.info(f"device type: {device_type_1}")

        if device_type_1 == "501":
            log.info(f"501过站....")
            for sn in other_vo.list_sn():
                check_param1 = {
                    "SerialNumber": sn,
                    "StationNumber": station_t,
                }
                check_param2 = {
                    "SerialNumber": sn,
                    "StationNumber": station_b,
                }

                log.info(f"正在过T站点...")
                ret = xrequest.RequestUtil.get(api_url_check_other, check_param1)
                if str(ret.get('ErrorCode')) != '0':
                    error_msg = f"mes接口异常，条码校验失败，error：{ret.get('ErrorMessage')}"

                # if board_side_501 == '过T+B站':
                #     log.info(f"正在过B站点...")
                #     ret = xrequest.RequestUtil.get(api_url_check_other, check_param2)
                #     if str(ret.get('ErrorCode')) != '0':
                #         error_msg = f"mes接口异常，条码校验失败，error：{ret.get('ErrorMessage')}"
        else:
            log.info(f"常规过站...")
            if board_side_1 == "T":
                station_ = station_t
            else:
                station_ = station_b

            for sn in other_vo.list_sn():
                check_param1 = {
                    "SerialNumber": sn,
                    "StationNumber": station_,
                }

                ret = xrequest.RequestUtil.get(api_url_check_other, check_param1)
                if str(ret.get('ErrorCode')) != '0':
                    error_msg = f"mes接口异常，条码校验失败，error：{ret.get('ErrorMessage')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data_other = data_vo.get_value_by_cons_key("api_url_data_other")
        # station = data_dao.get_value_by_cons_key("station")
        customer_name = data_vo.get_value_by_cons_key("customer_name")
        product_model = data_vo.get_value_by_cons_key("product_model")
        api_url_data_file_other = data_vo.get_value_by_cons_key("api_url_data_file_other")
        device_name = data_vo.get_value_by_cons_key("device_name")
        line = data_vo.get_value_by_cons_key("line")
        aoi_station = data_vo.get_value_by_cons_key("aoi_station")
        xml_save_path = data_vo.get_value_by_cons_key("xml_save_path")
        repair_station = data_vo.get_value_by_cons_key("repair_station")
        tester_name = data_vo.get_value_by_cons_key("tester_name")
        board_revision = data_vo.get_value_by_cons_key("board_revision")
        stage = data_vo.get_value_by_cons_key("stage")
        api_url_data_img_other = data_vo.get_value_by_cons_key("api_url_data_img_other")
        device_type_1 = data_vo.get_value_by_cons_key("device_type_1")
        board_side_1 = data_vo.get_value_by_cons_key("board_side_1")
        station_t = data_vo.get_value_by_cons_key("station_t")
        station_b = data_vo.get_value_by_cons_key("station_b")
        board_side_501 = data_vo.get_value_by_cons_key("board_side_501")
        time_sleep = data_vo.get_value_by_cons_key("time_sleep")
        api_get_sn = data_vo.get_value_by_cons_key("aoi_get_sn")
        str_mo_no = data_vo.get_value_by_cons_key("str_mo_no")
        str_station = data_vo.get_value_by_cons_key("str_station")
        is_get_sn = data_vo.get_value_by_cons_key("is_get_sn")
        if not xml_save_path:
            return self.x_response("false", f"请先选择存储路径！")

        pcb_entity1 = data_vo.pcb_entity
        self.log.info(pcb_entity1)
        board_count = pcb_entity1.board_count

        if is_get_sn == "是":
            req_param = {
                "strMONO": str_mo_no,
                "strStation": str_station,
                "numQty": board_count
            }
            ret = xrequest.RequestUtil.post_json(api_get_sn, req_param)

            if str(ret.get("ErrorCode")) != '0':
                return self.x_response("false", f"获取条码mes接口异常，error：{ret.get('ErrorMessage')}")
            ret_sn = ret.get("SNList", [])
            if board_count != len(ret_sn):
                return self.x_response("false", f"获取条码数量不匹配，拼板数：{board_count}, SNList:{ret_sn}")
            barcode_map = {str(i + 1): sn for i, sn in enumerate(ret_sn)}
            self.main_window.log_info(f"获取到的条码列表:{barcode_map}", pop_prompt=False)

        # 2. 每个大板单独生成一个文件夹
        time_file = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT4)
        data_full_path = f"{xml_save_path}/{device_name}_{time_file}"

        xutil.FileUtil.ensure_dir_exist(data_full_path)

        start_time = pcb_entity1.get_start_time().strftime(xcons.FMT_TIME_DEFAULT3)
        end_time = pcb_entity1.get_end_time().strftime(xcons.FMT_TIME_DEFAULT3)
        review_time = pcb_entity1.get_review_time().strftime(xcons.FMT_TIME_DEFAULT3)

        project_name = pcb_entity1.project_name

        # board_side = pcb_entity.board_side

        # if board_side == "B" and device_type_1 == "501":
        #     log.info(f"板面是B面，正在自动获取T面条码中...")
        #     sql_service = xsql.MySQLService('127.0.0.1', 'AIS400', 3306, 'root', 'aoi2014')
        #
        #     report_uuid = pcb_entity.get_report_uuid()
        #     t_barcode_map, b_barcode_map = sql_service.select_t_b_barcode_list(
        #         report_uuid,
        #         pcb_entity.pcb,
        #         pcb_entity.bom
        #     )
        #     log.info(f"T面条码列表：{t_barcode_map}")
        #     log.info(f"B面条码列表：{b_barcode_map}")
        #
        #     all_barcode = ChainMap(t_barcode_map, b_barcode_map)
        # else:
        #     all_barcode = {}

        def send_pcb_entity_to_mes(pcb_entity, board_side, station):
            error_msg = ""

            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)

                barcode = board_entity.barcode
                board_no = board_entity.board_no
                if is_get_sn == "是":
                    if board_no in barcode_map:
                        barcode = barcode_map[board_no]
                    elif board_no == "0":
                        barcode = barcode_map["1"]  # 整板情况

                # -----------------
                robot_ng_type_list = []
                repair_repass_type_list = []
                repair_ng_type_list = []

                comp_repass_number = 0
                comp_repair_ng_number = 0
                # -----------------

                defect_code_list = []
                # comp_data_str = ""

                xml_comp_data_str = ""
                for comp_entity in board_entity.yield_comp_entity():

                    if comp_entity.is_robot_ng():
                        # robot_ng
                        robot_ng_code = comp_entity.robot_ng_code

                        comp_src_image = comp_entity.image_path
                        comp_tag = comp_entity.designator

                        if comp_src_image:
                            img_dst_name = f"{board_side}_{barcode}_{board_no}_{comp_tag}.png"
                            comp_dst_image = f"{data_full_path}/{img_dst_name}"
                            xutil.FileUtil.copy_file(comp_src_image, comp_dst_image)

                            image_param = {
                                "SerialNumber": barcode,
                                "fileInfoPic": img_dst_name,
                                "StationNumber": station,
                                "strRepairStatus": comp_entity.get_final_result("Pass", "False call", "False"),
                                "strindictmentType": "ocvfail",
                                "strdesignator": comp_tag,
                                "strpartId": comp_entity.part,
                            }

                            try:
                                ret = upload_file_or_image_to_mes(api_url_data_img_other, image_param, comp_src_image)
                                if str(ret.get('ErrorCode')) != '0':
                                    error_msg = f"mes接口异常，上传图片失败，error：{ret.get('ErrorMessage')}"
                                self.log.info(f"上传图片成功！")
                            except Exception as err:
                                error_msg = f"mes接口异常，上传图片失败，error：{err}"

                        else:
                            comp_dst_image = ""

                        xml_comp_data_str += xml_comp_template.format(**{
                            "board_no": board_no,
                            "comp_tag": comp_entity.designator,
                            "repair_user": pcb_entity.repair_user,
                            "repair_time": review_time,
                            "comp_ng_str": comp_entity.robot_ng_str,
                            "repair_status": comp_entity.get_final_result("Pass", "False Call", "Repaired"),
                            "comp_package": comp_entity.package,
                            "comp_part": comp_entity.part,
                            "comp_image": comp_dst_image,
                        })

                        if robot_ng_code not in robot_ng_type_list:
                            robot_ng_type_list.append(robot_ng_code)

                        if comp_entity.is_repair_ng():
                            # real_ng
                            defect_code_list.append(comp_entity.repair_ng_code)
                            comp_repair_ng_number += 1

                            if robot_ng_code not in repair_ng_type_list:
                                repair_ng_type_list.append(robot_ng_code)
                        else:
                            # repass
                            comp_repass_number += 1
                            if robot_ng_code not in repair_repass_type_list:
                                repair_repass_type_list.append(robot_ng_code)

                    else:
                        # pass
                        pass

                    # comp_json = comp_entity.to_standard_board_data_v1()
                    # comp_data_str += comp_template.format(**comp_json)

                data_param = {
                    "SerialNumber": barcode,
                    "StationNumber": station,
                    "Result": board_entity.get_repair_result("OK", "NG"),
                    "DefectCode": ",".join(defect_code_list)
                }
                # board_json = board_entity.to_standard_board_data_v1()
                # board_json['CompData'] = comp_data_str

                # board_content = board_template.format(**board_json)

                ret = xrequest.RequestUtil.get(api_url_data_other, data_param)
                if str(ret.get('ErrorCode')) != '0':
                    error_msg = f"mes接口异常，上传数据失败，error：{ret.get('ErrorMessage')}"

                board_param2 = {
                    "SerialNumber": barcode,
                    "StationNumber": station,
                    "TestingResult": board_entity.get_repair_result("OK", "NG"),
                    "CustomerName": customer_name,
                    "ProductModel": product_model,
                }

                xml_board_content = xml_board_template.format(**{
                    "comp_robot_ng_number": board_entity.comp_robot_ng_number,
                    "start_time": start_time,
                    "repair_station": repair_station,
                    "test_status": board_entity.get_final_result("Passed", "Reviewed Passed", "Repaired"),
                    "end_time": end_time,
                    "comp_total_number": board_entity.comp_total_number,
                    "robot_ng_type_number": len(robot_ng_type_list),
                    "repair_status": board_entity.get_final_result("Repair None", "Reviewed Passed", "Repaired"),
                    "board_no": board_no,
                    "barcode": barcode,
                    "project_name": project_name,
                    "board_type": project_name,
                    "board_revision": board_revision,
                    "tester_name": tester_name,
                    "stage": stage,
                    "comp_repass_number": comp_repass_number,
                    "repair_repass_type_number": len(repair_repass_type_list),
                    "repair_time": review_time,
                    "repair_user": pcb_entity.repair_user,
                    "repair_ng_type_number": len(repair_ng_type_list),
                    "comp_repair_ng_number": board_entity.comp_repair_ng_number,
                    "comp_data_str": xml_comp_data_str,
                })

                xml_filename = f"{data_full_path}/{board_side}_{barcode}#{board_no}#{project_name}#{device_name}#{line}#{board_no}#{aoi_station}.xml"
                xutil.FileUtil.write_content_to_file(xml_filename, xml_board_content)

                param_str = json.dumps(board_param2, ensure_ascii=False, indent=4)

                log.info(
                    f"-->上传文件请求URL：{api_url_data_file_other} 【Body Json】参数(warning：此处LogFileStream参数过大，未打印！)：\n{param_str}"
                )
                log.info(f"LogFileStream参数为：{xml_filename} 的base64编码格式！")

                board_param2["LogFileStream"] = base64.b64encode(xml_board_content.encode('utf-8')).decode("utf-8")

                ret = x_post_json(api_url_data_file_other, board_param2)
                if str(ret.get('ErrorCode')) != '0':
                    error_msg = f"mes接口异常，上传文件失败，error：{ret.get('ErrorMessage')}"

                self.log.info(f"xml文档上传成功！")
            return error_msg

        pcb_entity_list = data_vo.pcb_entity_list
        log.info(f"pcb entity list {pcb_entity_list}, {len(pcb_entity_list)}")

        err_msg = ""
        # 必须勾选合并发送
        for _pcb_entity in pcb_entity_list:
            if device_type_1 == '501':
                real_board_side = _pcb_entity.board_side
                if real_board_side == 'B' and board_side_501 == '只过T站':
                    log.warning(f"只过T站，B站将不过站...")
                    continue
            else:
                real_board_side = board_side_1

            if real_board_side == 'T':
                station_ = station_t
            else:
                station_ = station_b

            error_msg1 = send_pcb_entity_to_mes(_pcb_entity, _pcb_entity.board_side, station_)
            if error_msg1:
                err_msg = error_msg1

            time.sleep(float(time_sleep))

        if err_msg:
            return self.x_response("false", err_msg)

        return self.x_response()
