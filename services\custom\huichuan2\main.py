# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/26 下午2:56
# Author     ：sch
# version    ：python 3.8
# Description：汇川联合动力 ---定制版
"""
from typing import Any

from common import xrequest, xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "huichuan2 release v1.0.0.3",
        "device": "401",
        "feature": ["从mes获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-26 14:57  init
date: 2025-02-17 11:48  bugfix: 无法获取条码
date: 2025-02-21 11:24  optimize: 兼容接口返回数据格式
""",
    }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "https://inosamesuat.inovance.com/mes/restapi/common/workorder/getSnByPartSn",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "https://inosamesuat.inovance.com/mes/restapi/elecontrol/aoi/handlestation",
        }
    }

    form = {
        "operation_code": {
            "ui_name": "工序编码",
            "value": "",
        },
        "workstation_code": {
            "ui_name": "工位编码",
            "value": "",
        },
        "user_name": {
            "ui_name": "用户",
            "value": "",
        },
        "layout": {
            "ui_name": "面别",
            "item": ["T", "B"],
            "value": "",
        },
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": ""
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")

        pcb_sn = other_vo.get_pcb_sn()

        get_sn_param = {
            "partSn": pcb_sn,
            "level": 1
        }
        ret = xrequest.RequestUtil.post_json(api_url_get_sn, get_sn_param)

        msg = ret.get('message')
        if not msg:
            msg = ret.get("exception")

        if str(ret.get("code")) != "1":
            return self.x_response("false", f"mes接口异常，error：{msg}")

        ret_sn = ret.get('data', {}).get('sn', "")
        if not ret_sn:
            return self.x_response("false", f"mes接口异常，未获取到条码！")

        return self.x_response("true", ret_sn)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        operation_code = data_vo.get_value_by_cons_key("operation_code")
        workstation_code = data_vo.get_value_by_cons_key("workstation_code")
        user_name = data_vo.get_value_by_cons_key("user_name")
        layout = data_vo.get_value_by_cons_key("layout")

        save_path = data_vo.get_value_by_cons_key("save_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        pcb_sn = pcb_entity.pcb_barcode

        pcb_t_src_img = pcb_entity.get_unknown_t_pcb_image()
        dst_img_image = f"{layout}_{time_file}_{pcb_sn}.jpg"

        project_name = pcb_entity.project_name

        time_date = time_file[:8]

        full_path = f"{save_path}/{project_name}/{time_date}"
        xutil.FileUtil.ensure_dir_exist(full_path)

        dst_filepath = f"{full_path}/{dst_img_image}"
        xutil.FileUtil.copy_file(pcb_t_src_img, dst_filepath)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            # board_no = board_entity.board_no

            comp_tag_list = []
            comp_ng_str_list = []

            is_had_err = "0"
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_tag_list.append(comp_entity.designator)
                    comp_ng_str_list.append(comp_entity.repair_ng_str)

                    is_had_err = "1"

            board_param = {
                "sn": barcode,
                "operationCode": operation_code,
                "workStationCode": workstation_code,
                "userName": user_name,
                "date": start_time,
                "layout": layout,
                "testResult": board_entity.get_repair_result("0", "1"),
                "isErrorMessage": is_had_err,
                "ngPosition": ",".join(comp_tag_list),
                "ngType": ",".join(comp_ng_str_list),
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, board_param)

            msg = ret.get('message')
            if not msg:
                msg = ret.get("exception")

            if str(ret.get("code")) != "1":
                return self.x_response("false", f"mes接口异常，error：{msg}")

        return self.x_response()
