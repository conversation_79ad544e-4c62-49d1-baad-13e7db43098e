# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/05/27 09:22
# Author     ：chencb
# version    ：python 3.8
# Description：长春德赛西威 https://jira.cvte.com/browse/ATAOI_2019-39813
"""
import json
import os
import shutil
import threading
from typing import Any
from common import xcons, xrequest, xutil
from common.xconfig import home_dir
from common.xcons import INSPECTOR, REPAIR
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

# 来源类型
FROM_MOVE_IN = 'moveIn'  # 进站
FROM_MOVE_STD = 'moveStd'  # 出站
FROM_SEND_DATA = 'sendData'  # 上传数据

# 上传失败缓存目录以及缓存信息文件
FAIL_CACHE_DIR = f"{home_dir}/.aoi/mes_fail_cache"
FAIL_DATA_INFO_FILE = f'{FAIL_CACHE_DIR}/fail_data.json'
# 接口超时统一调整为30s
API_TIME_OUT = 30


class Engine(ErrorMapEngine):
    version = {
        "customer": ["长春德赛西威", "changchundesaixiwei"],
        "version": "release v1.0.0.5",
        "device": "AIS40X",
        "feature": ["入站", "出站", "上传测试数据", "条码转换", "FTP上传图片", "测试数据失败重传"],
        "author": "chenchongbing",
        "release": """
date: 2025-06-05 17:00  jira:39813 入站、出站、上传测试数据、条码转换、FTP上传NG图片、本地保存NG图片、测试数据失败重传
date: 2025-06-06 11:40  修复mes服务接口返回text使用json转换导致报错；增加复判时入站、出站失败时保存要发送的数据到本地
date: 2025-06-12 19:20  按产线要求接口超时统一调整为30s
date: 2025-06-13 16:00  新增需求：当拼板条码为'0',表示拼板为空或者上游已经标记板为NG，不再需要上传MES进行校验
date: 2025-06-14 22:30  拼版条码为0时不上传mes
""", }

    form = {
        "resource": {
            "ui_name": "机台",
            "value": ""
        },
        "station": {
            "ui_name": "设备号",
            "value": ""
        },
        "spec": {
            "ui_name": "工位",
            "value": ""
        },
        "socket": {
            "ui_name": "工位编号",
            "value": ""
        },
        "employee": {
            "ui_name": "操作人",
            "value": ""
        },
        "operator": {
            "ui_name": "工号",
            "value": ""
        },
        "api_url_move_in": {
            "ui_name": "接口地址（入站）",
            "value": "http://10.214.131.146:1040/FA/MoveIn"
        },
        "api_url_move_std": {
            "ui_name": " 接口地址（出站）",
            "value": "http://10.214.131.146:1040/FA/MoveStd"
        },
        "api_url_sn_convert": {
            "ui_name": " 接口地址（条码转换）",
            "value": "http://10.214.131.146:1040/api/Labview/AMDTransferToPRD"
        },
        "api_url_send_data": {
            "ui_name": " 接口地址（上传数据）",
            "value": "http://10.214.130.25:8080/api/v1/TestSave/telemetry"
        },
    }

    combo = {
        "ng_pic_save_type": {
            "ui_name": "不良图片保存方式",
            "item": [
                "FTP上传",
                "本地保存"
            ],
            "value": "FTP上传"
        },
        "sn_convert": {
            "ui_name": "条码转换",
            "item": [
                "不转换",
                "转换并调接口",
                "转换不调接口",
            ],
            "value": "不转换"
        },
    }

    other_form = {
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": ""
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP账号",
            "value": ""
        },
        "ftp_password": {
            "ui_name": "FTP密码",
            "value": ""
        },
        "ftp_root": {
            "ui_name": "FTP根路径",
            "value": ""
        },
    }

    password_style = ["ftp_password"]

    path = {
        "ng_pic_local_path": {
            "ui_name": "不良图片保存路径",
            "value": ""
        },
    }

    def __init__(self):
        self.other_vo = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.other_vo = other_vo

    def _convert_sn(self, src_sn: str, from_type: str):
        sn_convert = self.other_vo.get_value_by_cons_key("sn_convert")
        resource = self.other_vo.get_value_by_cons_key("resource")
        employee = self.other_vo.get_value_by_cons_key("employee")

        resp = self.x_response()
        if len(src_sn) != 28:
            # 条码不为28位可能是异常条码，直接返回原条码不进行条码转换处理
            self.log.info('条码不等于28位，请核实条码是否正确')
            new_sn = src_sn
        elif sn_convert == '不转换':
            new_sn = src_sn
        elif sn_convert == '转换不调接口':
            # 业务逻辑为：条码转换（28转19）->【进（19位）】->【出（19位）】
            # 截取条码的前12后7进行拼接
            new_sn = src_sn[:12] + src_sn[-7:]
            # 进站和出站都是返回19位
        else:  # "转换并调接口"
            # 业务逻辑为：【进（28位）】->条码转换（28转19）-调用接口->【出（19位）】-> 发送数据【19位】
            if from_type == FROM_MOVE_IN:
                new_sn = src_sn
            else:
                # 出站时需条码转换，转换后需要调用接口
                new_sn = src_sn[:12] + src_sn[-7:]
                # 出站的条码转换需调用接口，发送数据时不用
                if from_type == FROM_MOVE_STD:
                    self.log.info('调用条码转换接口')
                    try:
                        param = {
                            "sn": src_sn,
                            "resource": resource,
                            "employee": employee
                        }
                        api_url_sn_convert = self.other_vo.get_value_by_cons_key("api_url_sn_convert", not_null=True)
                        ret = xrequest.RequestUtil.post_json(api_url_sn_convert, param, timeout=API_TIME_OUT)
                        if ret.get('resultCode') != 'Success':
                            resp = self.x_response('false', f"条码转换接口返回失败，resultMsg={ret.get('resultMsg')}")
                    except Exception as e:
                        resp = self.x_response(f"本地网络异常，条码转换接口访问失败，resultMsg={e}")

        self.log.info(f'【{from_type}】进行条码转换：【{src_sn}】--> 【{new_sn}】')
        return new_sn, resp

    def _move_in(self, sn_list: list):
        self.log.info('-------开始入站--------')
        if not sn_list:
            return self.x_response("false", f"条码列表为空，入站失败")

        api_url_move_in = self.other_vo.get_value_by_cons_key("api_url_move_in", not_null=True)
        resource = self.other_vo.get_value_by_cons_key("resource")
        spec = self.other_vo.get_value_by_cons_key("spec")
        employee = self.other_vo.get_value_by_cons_key("employee")

        try:
            for index, sn in enumerate(sn_list):
                if sn == '0':
                    self.log.info(f'拼板【{index+1}】条码为0，表示空板或者NG板，不发送MES校验')
                    continue

                new_sn, resp = self._convert_sn(sn, FROM_MOVE_IN)
                if not resp.get('result'):
                    return self.x_response("false", resp.get("string"))

                param = {
                    "containerName": new_sn,
                    "resource": resource,
                    "spec": spec,
                    "employee": employee
                }
                ret = xrequest.RequestUtil.post_json(api_url_move_in, param, to_json=False, timeout=API_TIME_OUT)
                # 返回结果为string，格式：状态(Pass/Fail)|消息(message)
                parts = ret.split("|")
                if 'Pass' not in parts[0]:
                    # 有一个校验失败就直接返回停机报警
                    return self.x_response("false", f"条码【{sn}】入站失败，入站服务返回：{parts[1]}")
        except Exception as e:
            return self.x_response("false", f"入站失败，本地网络异常：{e}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        sn_list = other_vo.list_sn()
        ret = self._move_in(sn_list)
        return ret

    def move_station(self, data_vo: DataVo, inspect_type: str):
        """
        出站，在发送完测试数据前调用
        """
        self.log.info('-------开始出站--------')
        api_url_move_std = self.other_vo.get_value_by_cons_key("api_url_move_std", not_null=True)
        resource = data_vo.get_value_by_cons_key("resource")
        employee = data_vo.get_value_by_cons_key("employee")

        pcb_entity = data_vo.pcb_entity
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        err_msg = ''
        try:
            for board_entity in pcb_entity.yield_board_entity():

                if board_entity.barcode == '0':
                    self.log.info(f'拼板【{board_entity.barcode}】条码为0，表示空板或者NG板，不发送MES校验')
                    continue

                if inspect_type == INSPECTOR:
                    test_result = board_entity.get_robot_result('Pass', 'Fail')
                else:
                    test_result = board_entity.get_repair_result('Pass', 'Fail')


                convert_sn, resp = self._convert_sn(board_entity.barcode, FROM_MOVE_STD)
                if not resp.get('result'):
                    return self.x_response("false", resp.get("string"))

                param = {
                    "containerName": convert_sn,
                    "resource": resource,
                    "testTime": test_time,
                    "testResult": test_result,
                    "turnoverBox": "",
                    "isUserBox": "false",
                    "employee": employee
                }
                ret = xrequest.RequestUtil.post_json(api_url_move_std, param, to_json=False, timeout=API_TIME_OUT)
                # 返回string,格式: Pass| 操作成功！|是否需要扫描新的周转容器 （1：是、0：否）
                parts = ret.split("|")
                if 'Pass' not in parts[0]:
                    # 只要有一个拼板出站失败就停机报警
                    err_msg = f"拼板【{board_entity.barcode}】出站失败，error：{parts[1]}"
                    break
        except Exception as e:
            err_msg = f"本地网络异常，出站失败：{e}"

        if err_msg:
            return self.x_response('false', err_msg)
        else:
            return self.x_response()

    def _save_fail_data(self, fail_data_list: list):
        xutil.FileUtil.ensure_dir_exist(FAIL_CACHE_DIR)
        if not os.path.exists(FAIL_DATA_INFO_FILE):
            # 没有记录过fail_data或者是重传又失败的，则直接写入
            new_fail_data_list = fail_data_list
        else:
            # 把新的数据追加到原有的记录里
            new_fail_data_list = xutil.FileUtil.load_json_file2(FAIL_DATA_INFO_FILE)
            new_fail_data_list.extend(fail_data_list)

        data_str = json.dumps(new_fail_data_list, indent=4, ensure_ascii=False)
        xutil.FileUtil.write_content_to_file(FAIL_DATA_INFO_FILE, data_str)

    def _upload_fail_data(self):
        # 没有失败数据
        if not os.path.exists(FAIL_DATA_INFO_FILE):
            return

        self.log.info('mes恢复正常，失败缓存的数据重新进行上传！')
        fail_data_list = xutil.FileUtil.load_json_file2(FAIL_DATA_INFO_FILE)
        # 数据加载出来后，直接删除文件，如果重传失败会重新保存
        xutil.FileUtil.remove_file(FAIL_DATA_INFO_FILE)
        resp = self._send_data(fail_data_list)
        if not resp.get('result'):
            self.log.info(f"重传上次失败数据失败，error:{resp.get('string')}")

    def _upload_ng_pic_to_ftp(self, upload_pics: dict):
        """
        上传所有ng图片数据到指定的FTP路径
        upload_pics格式如下：
        {
        "ftp_path": ftp_path,
        "pic_list": board_ng_pic_list
        }
        pic_list里的数据格式如下：
        {
        'upload_file': xxxx.png, ftp上传的文件名
        'aoi_file': /xxx/xxx.png 本地缓存的路径和文件名
        }
        """
        ftp_host = self.other_vo.get_value_by_cons_key("ftp_host")
        ftp_port = self.other_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = self.other_vo.get_value_by_cons_key("ftp_user")
        ftp_password = self.other_vo.get_value_by_cons_key("ftp_password")
        ftp_root = self.other_vo.get_value_by_cons_key("ftp_root")
        if not ftp_host or not ftp_port or not ftp_user or not ftp_password or not ftp_root:
            return self.x_response("false", f"FTP参数未完整配置，请到【设置-其它参数设置】中填写")

        err_msg_list = []
        upload_dir = upload_pics.get('ftp_path', '')
        ng_pic_list = upload_pics.get('pic_list', [])
        try:
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            resp = ftp_client.login()
            if '230' in resp:  # 230 登录成功
                # 路径都一样，cd_or_mkdir一次就可以了
                ftp_client.cd_or_mkdir(upload_dir)
                for ng_pic in ng_pic_list:
                    filename = ng_pic.get('upload_file')
                    aoi_file = ng_pic.get('aoi_file')
                    try:
                        resp = ftp_client.upload_file(aoi_file, filename, False)
                        if '226' not in resp:  # 上传失败
                            err_str = f'图片【{filename}】上传失败：{resp}'
                            err_msg_list.append(err_str)
                    except Exception as e:
                        # 单个文件上传失败时，需捕获异常，记录失败信息后，继续上传其它图片
                        err_str = f'图片【{filename}】上传失败：{e}'
                        err_msg_list.append(err_str)
            else:
                err_msg_list.append(f"ftp登录失败，不良图片未能正常上传：{resp}")
            ftp_client.close()
        except Exception as e:
            err_msg_list.append(f"FTP登录异常，不良图片未能正常上传：{e}")

        if err_msg_list:
            return self.x_response("false", '\n'.join(err_msg_list))
        else:
            return self.x_response()

    def _upload_ng_pic_to_local(self, upload_pics: dict):
        """
        保存所有ng图片数据到指定的图片保存路径
        upload_pics格式如下：
        {
        "local_path": local_path,
        "pic_list": board_ng_pic_list
        }
        pic_list里的数据格式如下：
        {
        'upload_file': xxxx.png, 保存的文件名
        'aoi_file': /xxx/xxx.png 本地缓存的路径和文件名
        }
        """
        save_dir = upload_pics.get('local_path', '')
        xutil.FileUtil.ensure_dir_exist(save_dir)

        err_msg_list = []
        ng_pic_list = upload_pics.get('pic_list', [])
        for ng_pic in ng_pic_list:
            filename = ng_pic.get('upload_file')
            aoi_file = ng_pic.get('aoi_file')
            dst_file_path = f'{save_dir}/{filename}'
            try:
                shutil.copy(aoi_file, dst_file_path)
            except Exception as e:
                err_msg_list.append(f"图片拷贝出错, src: {aoi_file}  dst: {dst_file_path}，error:{e}")

        if err_msg_list:
            return self.x_response("false", '\n'.join(err_msg_list))
        else:
            return self.x_response()

    def _send_data(self, upload_data_list: list):
        """
        upload_data_list中数据项格式如下：
        {
            "ftp": {
                "ftp_path": ftp_path,
                "pic_list": board_ng_pic_list
            },
            或者：
            "local": {
                "local_path": local_path,
                "pic_list": board_ng_pic_list
            },
            "mes": board_data
        }
        """
        api_url_send_data = self.other_vo.get_value_by_cons_key("api_url_send_data", not_null=True)

        err_msg_list = []
        fail_data_list = []
        # 先上传NG图片数据，再上传MES数据，确保MES数据中记录的图片信息都已正常上传
        for upload_data in upload_data_list:
            ftp_data = upload_data.get('ftp', {})
            local_data = upload_data.get('local', {})
            if ftp_data:
                upload_resp = self._upload_ng_pic_to_ftp(ftp_data)
            elif local_data:
                upload_resp = self._upload_ng_pic_to_local(local_data)
            else:
                upload_resp = self.x_response()
            # 图片上传成功，才可以发送数据，避免出现mes数据上传了到时取不到ng图片情况
            if upload_resp.get('result'):
                # 上传成功后，数据置空，后续mes失败缓存时，就不用再上传ftp数据了
                if ftp_data:
                    upload_data['ftp'] = {}
                elif local_data:
                    upload_data['local'] = {}
                board_data = upload_data.get('mes', [])
                try:
                    ret = xrequest.RequestUtil.post_json(api_url_send_data, board_data, to_json=False,
                                                         raw_response=True, timeout=API_TIME_OUT)
                    if ret.status_code != 200:
                        err_msg = f"拼板【{board_data.get('SN')}】MES上传数据失败，失败错误码：{ret.status_code}"
                        err_msg_list.append(err_msg)
                        fail_data_list.append(upload_data)
                except Exception as e:
                    err_msg = f'MES上传数据失败,本地网络异常：{e}'
                    err_msg_list.append(err_msg)
                    fail_data_list.append(upload_data)
            else:
                fail_data_list.append(upload_data)
                err_msg_list.append(f'FTP上传NG图片失败：{upload_resp.get("string")}')

        if fail_data_list:
            self._save_fail_data(fail_data_list)

        if err_msg_list:
            return self.x_response('false', '\n'.join(err_msg_list))
        else:
            return self.x_response()

    def _generate_upload_data(self, data_vo: DataVo):
        station = data_vo.get_value_by_cons_key("station")
        socket = data_vo.get_value_by_cons_key("socket")
        operator = data_vo.get_value_by_cons_key("operator")
        ftp_root = data_vo.get_value_by_cons_key("ftp_root")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ng_pic_save_type = data_vo.get_value_by_cons_key("ng_pic_save_type")
        inspect_type = data_vo.get_inspect_type()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        test_time = pcb_entity.get_start_time()
        test_time_str = test_time.strftime(xcons.FMT_TIME_DEFAULT)
        # 生成图片存储路径, 按年月日创建文件夹
        if ng_pic_save_type == 'FTP上传':
            if not ftp_root or not ftp_host:
                return self.x_response("false", f"FTP参数未完整配置，请到【设置-其它参数设置】中填写")
            pic_upload_path = f"{ftp_root}/{test_time.year}/{test_time.month:02d}/{test_time.day:02d}"
        else:
            ng_pic_local_path = data_vo.get_value_by_cons_key("ng_pic_local_path", not_null=True)
            pic_upload_path = f"{ng_pic_local_path}/{test_time.year}/{test_time.month:02d}/{test_time.day:02d}"

        upload_data_list = []  # 包括mes和ftp一起的数据
        for board_entity in pcb_entity.yield_board_entity():
            if board_entity.barcode == "0":
                self.log.info(f'拼板【{board_entity.barcode}】条码为0，表示空板或者NG板，不发送MES')
                continue

            # 发送数据都在出站后，统一拿出站后的条码，发送数据的条码不需要调用接口，所以这边可以不用判断返回结果
            board_sn, resp = self._convert_sn(board_entity.barcode, FROM_SEND_DATA)
            if inspect_type == INSPECTOR:
                test_result = board_entity.get_robot_result('Passed', 'Failed')
            else:
                test_result = board_entity.get_repair_result('Passed', 'Failed')

            comp_list = []
            ng_pic_upload_path_list = []  # NG图片上传路径列表，用于生成CodeFile
            board_ng_pic_list = []  # 需保存的拼板的NG图片
            for comp_entity in board_entity.yield_comp_entity():
                if inspect_type == INSPECTOR:
                    if comp_entity.robot_result:
                        continue
                    ng_code = comp_entity.robot_ng_code
                    ng_code_desc = comp_entity.robot_ng_str
                else:
                    if comp_entity.repair_result:
                        continue
                    ng_code = comp_entity.repair_ng_code
                    ng_code_desc = comp_entity.repair_ng_str

                # 上传FTP或者保存本地的文件名为：SN+小板序号+点位+结果+时间（HH:mm:ss），例：P1004332-2-C1-OK-14:01:04）
                suffix = comp_entity.image_path.split('.')[-1]
                pic_file_name = f'{board_sn}-{board_entity.board_no}-{comp_entity.designator}-NG-' \
                                f'{test_time.hour:02d}:{test_time.minute:02d}:{test_time.second:02d}.{suffix}'
                board_ng_pic_list.append({
                    'upload_file': pic_file_name,
                    'aoi_file': comp_entity.image_path
                })

                if ng_pic_save_type == 'FTP上传':
                    # 需要把地址添加上
                    upload_file_path = f'{ftp_host}/{pic_upload_path}/{pic_file_name}'
                else:
                    upload_file_path = f'{pic_upload_path}/{pic_file_name}'
                ng_pic_upload_path_list.append(upload_file_path)

                ng_alg = None
                for alg in comp_entity.yield_alg_entity():
                    if alg.result == ng_code:
                        ng_alg = alg
                        break

                # 测试指标比较（上下限加起来除以2）
                test_compare = str((float(ng_alg.min_threshold) + float(ng_alg.max_threshold)) / 2)
                comp_list.append({
                    "Step": ng_alg.test_name,
                    "SubItem": "",
                    "Data": ng_alg.test_val,
                    "Units": "%",
                    "Low_lim": ng_alg.min_threshold,
                    "Hign_lim": ng_alg.max_threshold,
                    "Status": comp_entity.get_final_result('Passed', 'Passed', 'Failed'),
                    "Comp": test_compare,
                    # 当前无法知道PassFailTest(只有pass和fail情况)场景，默认填写NumericLimitTest(有上下限的情况)
                    "Me14": 'NumericLimitTest',
                    "Time": str(pcb_entity.get_cycle_time()),
                    "Report": test_result,
                    "ErrCode": ng_code,
                    "ErrMsg": ng_code_desc,
                    "Step_CodeFile": upload_file_path
                })

            uuid = xutil.OtherUtil.get_origin_uuid4_str()
            board_data = {
                "Uuid": uuid,
                "Station": station,
                "Socket": socket,
                "SN": board_sn,
                "Batch_SN": "",
                "Operator": operator,
                "Start_Datetime": test_time_str,
                "Exec_Time": str(pcb_entity.get_cycle_time()),
                "Status": test_result,
                "ErrCode": "",
                "ErrMsg": "",
                "CodeFile": ','.join(ng_pic_upload_path_list),
                "TestDataDetailList": comp_list
            }

            upload_data = {"mes": board_data}
            # ng图片数据只在机器检测且检测结果NG时才需上传
            if inspect_type == INSPECTOR and board_entity.is_robot_ng():
                if ng_pic_save_type == 'FTP上传':
                    upload_data["ftp"] = {
                        "ftp_path": pic_upload_path,
                        "pic_list": board_ng_pic_list
                    }
                else:
                    upload_data["local"] = {
                        "local_path": pic_upload_path,
                        "pic_list": board_ng_pic_list
                    }
            upload_data_list.append(upload_data)

        return upload_data_list

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()
        upload_data_list = self._generate_upload_data(data_vo)

        # 如果是维修站复判，需走一遍【入站-出站-发送数据】流程
        if inspect_type == REPAIR:
            sn_list = []
            pcb_entity = data_vo.pcb_entity
            for board_entity in pcb_entity.yield_board_entity():
                sn_list.append(board_entity.barcode)

            resp = self._move_in(sn_list)
            if not resp.get('result'):
                # 入站失败需保存要发送的数据
                self._save_fail_data(upload_data_list)
                self.log.info(f"入站失败，向主软件发送停机报警！")
                err_msg = resp.get("string")
                xrequest.send_device_start_or_stop("0", '1', err_msg)
                return self.x_response('false', err_msg)

        # 发送数据前先执行出站
        resp = self.move_station(data_vo, inspect_type)
        # 出站失败停机报警
        if not resp.get('result'):
            # 出站失败需保存要发送的数据
            self._save_fail_data(upload_data_list)
            self.log.info(f"出站失败，向主软件发送停机报警！")
            err_msg = resp.get("string")
            xrequest.send_device_start_or_stop("0", '1', err_msg)
            return self.x_response('false', err_msg)

        resp = self._send_data(upload_data_list)
        if resp.get('result'):
            # mes恢复正常，如果有失败缓存的数据则异步进行下上传
            timer = threading.Timer(1, self._upload_fail_data, args=[])
            timer.start()
            return self.x_response()
        else:
            self.log.info(f"MES上传数据失败，向主软件发送停机报警！")
            err_msg = resp.get("string")
            xrequest.send_device_start_or_stop("0", '1', err_msg)
            return self.x_response('false', err_msg)
