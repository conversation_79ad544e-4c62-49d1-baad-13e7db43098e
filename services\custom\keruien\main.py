# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/1 下午3:36
# Author     ：sch
# version    ：python 3.8
# Description：科瑞恩-抚州比亚迪
"""
import binascii
from typing import Any

from common import xutil, xrequest, xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

global_data = {}


def hex16_to_str(hex_str) -> str:
    """
    16进制转换成字符串
    """
    if hex_str:
        log.info(f"16进制：{hex_str}")
        hex1 = hex_str.encode("utf-8")
        str_bin = binascii.unhexlify(hex1)

        s3 = str_bin.replace(b'\x00', b'')
        s4 = s3.decode('utf-8')

        log.info(f"转换后：{s4}")

    else:
        s4 = ""

    return s4


def parse_rfid_data(rfid_data: str, start_ix: int, end_ix: int, parse_data_number: str = "0"):
    """
    解析rfid数据
    :param parse_data_number:
    :param rfid_data:
    :param start_ix:
    :param end_ix:
    :return:
    """
    if parse_data_number == "1":
        sn_hex = rfid_data[start_ix:end_ix]
        new_sn = hex16_to_str(sn_hex)
    elif parse_data_number == "2":
        data_hex = hex16_to_str(rfid_data)
        sn_hex = data_hex[start_ix:end_ix]
        new_sn = hex16_to_str(sn_hex)
    else:
        new_sn = rfid_data

    log.info(f"parse data: {new_sn}")

    return new_sn


class Engine(ErrorMapEngine):
    version = {
        "title": "keruien release v1.0.0.26",
        "device": "401B",
        "feature": ["从Mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-01 15:34  init
date: 2023-08-09 09:10  解析RFID数据
date: 2023-08-15 19:40  NG RELEASE
date: 2023-08-16 11:13  条码状态为3的不发送数据到mes
date: 2023-09-07 09:31  修改rfid数据解析规则
date: 2023-11-06 16:03  State改成复判后的结果

date: 2023-11-13 10:44  复制抚州比亚迪脚本
date: 2023-11-13 14:37  扫到什么条码就校验什么条码
date: 2023-11-17 09:29  非零的状态码都条码校验失败
date: 2023-12-01 16:46  改回条码校验会有三种状态
date: 2023-12-04 17:54  将条码校验改到从mes获取坏板的流程里调用
date: 2024-01-30 16:52  增加上传数据
date: 2024-02-29 15:40  修改上传的时间格式
date: 2024-02-29 17:39  修改参数
date: 2024-02-29 18:02  修改参数
date: 2024-03-01 14:34  修改参数的枚举值，f_OK,f_NOK,f_NOOP,f_BYPASS
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "operator_account": {
            "ui_name": "操作员",
            "value": "OP130-1"
        },
        # "station": {
        #     "ui_name": "工站号",
        #     "value": "OP130-1"
        # },
        # "layer": {
        #     "ui_name": "",
        #     "value": "OP130-1"
        # },
    }

    other_form = {
        "start_ix": {
            "ui_name": "托盘号起始位",
            "value": "0"
        },
        "end_ix": {
            "ui_name": "托盘号结束位",
            "value": "7"
        },
        "valid_len": {
            "ui_name": "RFID长度校验",
            "value": "10"
        },
        "station_name": {
            "ui_name": "工站名",
            "value": "OP130-1"
        },
    }

    other_combo = {
        "parse_data_number": {
            "ui_name": "解析几次RFID数据",
            "item": ["0", "1", "2"],
            "value": "0"
        }
    }

    def __init__(self):
        self.ERROR_MAP['AIS203/AIS303/AIS40X/AIS50x'] = {
            "1": {
                "standard": "漏件",
                "custom_code": "1",
                "custom_str": "MissingPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "2": {
                "standard": "错件",
                "custom_code": "2",
                "custom_str": "WrongPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "3": {
                "standard": "反件",
                "custom_code": "3",
                "custom_str": "ReversePart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "4": {
                "standard": "立碑",
                "custom_code": "4",
                "custom_str": "Tomstone",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "5": {
                "standard": "偏移",
                "custom_code": "5",
                "custom_str": "ShiftPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "6": {
                "standard": "翻转",
                "custom_code": "6",
                "custom_str": "UpsideDown",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "7": {
                "standard": "浮高",
                "custom_code": "7",
                "custom_str": "LiftedPackage",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "8": {
                "standard": "损件",
                "custom_code": "8",
                "custom_str": "Broken",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "9": {
                "standard": "露铜",
                "custom_code": "CM07068",
                "custom_str": "ExposeCopper",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "10": {
                "standard": "少锡",
                "custom_code": "CN110008",
                "custom_str": "InsufficientSolder",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "11": {
                "standard": "多锡",
                "custom_code": "11",
                "custom_str": "ExcessSolder",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "12": {
                "standard": "未出脚",
                "custom_code": "CM07065",
                "custom_str": "NoPin",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "13": {
                "standard": "孔洞",
                "custom_code": "CN110005",
                "custom_str": "PinHole",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "14": {
                "standard": "连锡",
                "custom_code": "CN110006",
                "custom_str": "Bridge",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "15": {
                "standard": "锡珠",
                "custom_code": "15",
                "custom_str": "SolderBall",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "16": {
                "standard": "翘脚",
                "custom_code": "16",
                "custom_str": "LiftedLead",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "17": {
                "standard": "弯脚",
                "custom_code": "17",
                "custom_str": "ShiftedLead",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "18": {
                "standard": "异物",
                "custom_code": "18",
                "custom_str": "ForeignMaterial",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "19": {
                "standard": "条码识别",
                "custom_code": "19",
                "custom_str": "BarcodeRecognition",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "20": {
                "standard": "Marker搜索",
                "custom_code": "20",
                "custom_str": "MarkerSearch",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "21": {
                "standard": "多件",
                "custom_code": "21",
                "custom_str": "ForeignPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "22": {
                "standard": "溢胶",
                "custom_code": "22",
                "custom_str": "Overflow",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "23": {
                "standard": "虚焊",
                "custom_code": "23",
                "custom_str": "IncompleteWeld",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "24": {
                "standard": "脏污",
                "custom_code": "24",
                "custom_str": "Dirty",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "25": {
                "standard": "坏板",
                "custom_code": "25",
                "custom_str": "BadPanel",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "26": {
                "standard": "定位",
                "custom_code": "26",
                "custom_str": "Locate",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "27": {
                "standard": "数目错误",
                "custom_code": "27",
                "custom_str": "CountError",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "28": {
                "standard": "少涂/多涂",
                "custom_code": "28",
                "custom_str": "LessMoreCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "29": {
                "standard": "少涂",
                "custom_code": "CN110011",
                "custom_str": "LessCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "30": {
                "standard": "多涂",
                "custom_code": "CN110010",
                "custom_str": "MoreCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "31": {
                "standard": "气泡",
                "custom_code": "31",
                "custom_str": "Bubble",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "32": {
                "standard": "划痕",
                "custom_code": "32",
                "custom_str": "Scratch",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "33": {
                "standard": "距离",
                "custom_code": "33",
                "custom_str": "Distance",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "34": {
                "standard": "锡膏检测",
                "custom_code": "34",
                "custom_str": "SPIDetect",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "35": {
                "standard": "共线性",
                "custom_code": "35",
                "custom_str": "Collinearity",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "36": {
                "standard": "CPU插针检测",
                "custom_code": "36",
                "custom_str": "CPUPinDetect",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            }
        }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        start_ix = other_vo.get_value_by_cons_key("start_ix")
        end_ix = other_vo.get_value_by_cons_key("end_ix")
        valid_len = other_vo.get_value_by_cons_key("valid_len")
        # is_parse_hex = other_dao.get_value_by_cons_key("is_parse_hex")
        parse_data_number = other_vo.get_value_by_cons_key("parse_data_number")

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        try:
            valid_len = int(valid_len)
        except Exception as err:
            self.log.info(f"[RFID长度校验]必须为数字！err:{err}")

        get_sn_url = f"{api_url}/api/Home/GetSerialNumberByCarrierCode"

        pcb_sn = other_vo.get_pcb_sn()

        if len(pcb_sn) < valid_len:
            return self.x_response("false", f"没有获取到RFID数据！")

        new_sn = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        param = {
            "Token": xutil.DateUtil.get_datetime_now(),
            "CarrierCode": new_sn,
        }
        ret = xrequest.RequestUtil.post_json(get_sn_url, param)

        if ret.get("ReturnCode"):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('ErrorMessage')}")

        data = ret.get('Data', "")

        return self.x_response("true", data)

    def get_bad_board_info(self, other_vo: OtherVo, other_param: Any):
        """
        条码校验时的状态
        if statusCode == 0:
            状态1
        elif statusCode < -100 or statusCode > 0:
            状态3
        else:
            状态2

        1. 状态1       正常状态，进板检测                      mes接口返回：0
        2. 状态2       状态异常，停机报警                      mes接口返回：-100~0
        3. 状态3       直接流板，不需要检测也不需要有记录         mes接口返回：<-100     >0
        :param other_vo:
        :param other_param:
        :return:
        """
        api_url = other_vo.get_value_by_cons_key("api_url")
        # station = other_dao.get_value_by_cons_key("station")

        start_ix = other_vo.get_value_by_cons_key("start_ix")
        end_ix = other_vo.get_value_by_cons_key("end_ix")
        parse_data_number = other_vo.get_value_by_cons_key("parse_data_number")
        station_name = other_vo.get_value_by_cons_key("station_name")

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        sn_list = other_vo.list_sn()
        # if len(sn_list) < 2:
        #     return self.x_response("false", f"条码数量不足！")

        pcb_sn = sn_list[0]

        carry_sn_str = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        check_url = f"{api_url}/api/Home/CheckSerialNumberState"
        for sn in sn_list:
            check_param = {
                "stationNumber": station_name,
                "serialNumber": sn,
                "layer": 2,
            }
            ret = xrequest.RequestUtil.post_json(check_url, check_param)

            return_code = ret.get("ReturnCode", -1)
            self.log.info(f"return code: {return_code}")

            if return_code < -100 or return_code > 0:
                # ret_data = {
                #     "code": 200,
                #     "msg": "NG_RELEASE, 板子直接流下去, 不做其他处理"
                # }
                self.log.info(f"mes接口返回：<-100 or >0, 直接流板!")
                barcode_map = other_vo.get_origin_param('barcodeList')

                # bad_no = []
                # for i in range(1, len(barcode_map) - 1):
                #     bad_no.append(str(i))
                del barcode_map['-1']
                del barcode_map['-2']

                bad_no = list(barcode_map.keys())

                status_code = 3
                ret_res = self.x_response("true", ",".join(bad_no))  # 直接流板，不需要检测也不需要有记录
                # 20231117 需求变更：非0的returnCode都报错
                # status_code = 3
                # ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('ErrorMessage')}")
            elif return_code == 0:
                self.log.info(f"mes接口返回：0, 正常状态，进板检测")
                status_code = 1
                ret_res = self.x_response("true", "")  # 正常状态
            else:
                self.log.info(f"mes接口返回：-100~0, 状态异常，停机报警")
                status_code = 2
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('ErrorMessage')}")

            if sn:
                # 记录最近10块板子的状态
                carry_sn_status = global_data.get('carry_sn_status', {})
                carry_sn_list = global_data.get('carry_sn_list', [])

                carry_sn_status[carry_sn_str] = status_code
                carry_sn_list.append(carry_sn_str)

                carry_sn_list = carry_sn_list[-10:]

                all_status_list = list(carry_sn_status.keys())

                for k in all_status_list:
                    if k not in carry_sn_list:
                        del carry_sn_status[k]

                global_data["carry_sn_status"] = carry_sn_status
                global_data["carry_sn_list"] = carry_sn_list
                self.log.info(f"托盘号的状态【{status_code}】已缓存")

            return ret_res

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        operator_account = data_vo.get_value_by_cons_key("operator_account")
        station_name = data_vo.get_value_by_cons_key("station_name")
        start_ix = data_vo.get_value_by_cons_key("start_ix")
        end_ix = data_vo.get_value_by_cons_key("end_ix")
        parse_data_number = data_vo.get_value_by_cons_key("parse_data_number")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        pcb_sn = pcb_entity.pcb_barcode
        carry_sn_str = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        carry_sn_status = global_data.get("carry_sn_status", {})
        self.log.info(f"carry sn status: {carry_sn_status}")
        sn_status = carry_sn_status.get(carry_sn_str, 888)  # 如果条码状态为3，则不发送mes

        data_url = f"{api_url}/api/Home/UploadStateAndMeasurementData"
        data_url2 = f"{api_url}/api/Home/UploadFactoryMesRecordData"

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_list = []
            ix = 0

            ng_code_list = []
            for comp_entity in board_entity.yield_comp_entity():
                ix += 1
                repair_ng_code = comp_entity.repair_ng_code
                comp_list.append({
                    "MeasureName": comp_entity.designator,
                    "MeasureValue": "",
                    "TestStepSequence": ix,
                    "FailureCode": repair_ng_code if comp_entity.is_repair_ng() else "",
                    "State": 1 if comp_entity.is_repair_ng() else 0,
                })

                if comp_entity.is_repair_ng():
                    if repair_ng_code not in ng_code_list:
                        ng_code_list.append(repair_ng_code)

            data_param = {
                "stationNumber": station_name,
                "serialNumber": barcode,
                "layer": 2,
                "state": board_entity.get_repair_result(0, 1),
                "measurementDataArray": comp_list,
                "operatorAccount": operator_account,
                "cycleTime": pcb_entity.get_cycle_time(),
                "productionDateTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "Token": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE),
            }

            time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
            data_param2 = {
                "f_PRODUCTCODE": barcode,
                "f_FAULTCODE": ",".join(ng_code_list),
                "f_NCYCLETIME": "60",
                "f_CCYCLETIME": str(pcb_entity.get_cycle_time()),
                "f_BLOCKTIME": "",
                "f_FREETIME": "",
                "f_PRESENTTIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "f_LEAVETIME": time_now,
                "f_COUNT": 1,
                "f_OK": board_entity.get_final_result(1, 0, 0),
                "f_NOK": board_entity.get_final_result(0, 0, 1),
                "f_NOOP": 0,
                "f_BYPASS": board_entity.get_final_result(0, 1, 0),
                "f_STATIONNAME": station_name,
                "f_OEMTIME": time_now,
                "f_SAILED": 0,
                "f_USELESS": 0,
                "f_REPAIRED": 0,
                "f_LINECODE": "IGBLine",
                "f_STATIONTRAYNUM": "",
                "f_PARTCODE": "",
                "f_IsSync": 0,
                "f_PIECE": 1,
            }

            if sn_status != 3:
                self.log.info(f"1上传过站记录和检测项结果....")
                ret = xrequest.RequestUtil.post_json(data_url, data_param)
                if ret.get("ReturnCode"):
                    error_msg = f"mes接口异常，上传数据失败，error：{ret.get('ErrorMessage')}"

                self.log.info(f"2上传过站记录和检测项结果....")
                ret = xrequest.RequestUtil.post_json(data_url2, data_param2)
                if ret.get("ReturnCode"):
                    error_msg = f"mes接口异常，上传数据失败，error：{ret.get('ErrorMessage')}"

            else:
                self.log.warning(f"条码状态为3，不发送数据到mes！")

            # 20231117 需求变更：非0的returnCode都报错
            # ret = xrequest.RequestUtil.post_json(data_url, data_param)
            # if ret.get("ReturnCode"):
            #     return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('ErrorMessage')}")

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        return self.x_response()
