# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/07/08 11:43
# Author     ：chencb
# version    ：python 3.8
# Description：越南峻凌 https://jira.cvte.com/browse/ATAOI_2019-40410
"""
from typing import Any
from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "customer": ["越南峻凌", "yuenanjunling"],
        "version": "release v1.0.0.1",
        "device": "AIS50X",
        "feature": ["获取条码，发送数据"],
        "author": "chenchongbing",
        "release": """
date: 2025-07-08 16:20  jira->ATAOI_2019-40410: 获取条码，发送数据
""", }

    barcode_type_map = {
        "客户条码(CUSTBARCODE)": "CUSTBARCODE",
        "板边条码(PANEL)": "PANEL",
        "载具条码(LOADERBOARD)": "LOADERBOARD",
        "来料板边(PCBSN)": "PCBSN",
        "来料条码(VENDERCODE)": "VENDERCODE",
    }
    post_type_map = {
        "1(整板良品过账)": "1",
        "2(整板过账)": "2",
        "3(单PCS过账)": "3",
    }

    combo = {
        "barcode_type": {
            "item": [
                "CUSTBARCODE(客户条码)",
                "PANEL(板边条码)",
                "LOADERBOARD(载具条码)",
                "PCBSN(来料板边)",
                "VENDERCODE(来料条码)",
                "N/A",
            ],
            "value": "N/A",
            "ui_name": "条码类型"
        },
        "post_type": {
            "item": [
                "1(整板良品过账)",
                "2(整板过账)",
                "3(单PCS过账)",
                "N/A",
            ],
            "value": "N/A",
            "ui_name": "过账模式"
        },
    }

    form = {
        "part_no": {
            "ui_name": "机种料号",
            "value": "",
        },
        "line_no": {
            "ui_name": "线体",
            "value": "",
        },
        "machine_no": {
            "ui_name": "设备编号",
            "value": "",
        },
        "station": {
            "ui_name": "作业编号",
            "value": "",
        },
        "op_mes": {
            "ui_name": "MES账号",
            "value": "",
        },
        "get_sn_url": {
            "ui_name": "检查接口地址",
            "value": "",
        },
        "send_data_url": {
            "ui_name": "过账接口地址",
            "value": "",
        },
    }

    def __init__(self):
        self.loader_board = ''

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        part_no = other_vo.get_value_by_cons_key("part_no")
        barcode_type = other_vo.get_value_by_cons_key("barcode_type")
        post_type = other_vo.get_value_by_cons_key("post_type")
        station = other_vo.get_value_by_cons_key("station")
        line_no = other_vo.get_value_by_cons_key("line_no")
        machine_no = other_vo.get_value_by_cons_key("machine_no")
        op_mes = other_vo.get_value_by_cons_key("op_mes")
        get_sn_url = other_vo.get_value_by_cons_key("get_sn_url", not_null=True)

        if barcode_type == 'N/A' or post_type == 'N/A':
            return self.x_response("false", "请先选择正确的条码类型和过账模式值！！！！")

        barcode_type_value = self.barcode_type_map[barcode_type]
        progrom_name = other_vo.get_project_name()
        pcb_sn = other_vo.get_pcb_sn()
        self.loader_board = other_vo.get_barcode_map().get('-2', '')

        param = {
            "PARTNO": part_no,
            "BARCODE": pcb_sn,
            "BARCODETYPE": barcode_type_value,
            "LINENO": line_no,
            "MACHINE": machine_no,
            "STATION": station,
            "OP": op_mes,
            "LOADERBOARD": self.loader_board,
            "PROGROMNAME": progrom_name,
            "PROGROMVERSION": ""
        }
        try:
            ret = xrequest.RequestUtil.post_json(get_sn_url, param)
            if ret.get("Result") == 'OK':
                barcode_list = ret.get("BARCODELIST", [])
                # 按 NUM 字段升序排序
                barcode_list = sorted(barcode_list, key=lambda x: int(x["NUM"]))
                # 提取排序后的 BARCODE 值
                sn_list = [item["BARCODE"] for item in barcode_list]
                return self.x_response("true", ",".join(sn_list))
            else:
                return self.x_response("false", f"获取条码失败，error：{ret.get('Message')}")
        except Exception as e:
            return self.x_response("false", f"本地网络异常，error：{e}")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        send_data_url = data_vo.get_value_by_cons_key("send_data_url", not_null=True)
        part_no = data_vo.get_value_by_cons_key("part_no")
        post_type = data_vo.get_value_by_cons_key("post_type")
        station = data_vo.get_value_by_cons_key("station")
        line_no = data_vo.get_value_by_cons_key("line_no")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        op_mes = data_vo.get_value_by_cons_key("op_mes")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        inspect_type = data_vo.get_inspect_type()
        if inspect_type == xcons.INSPECTOR:
            return self.x_response('false', '只发送复判后结果，若要发送机器初判结果请提变更需求！')

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # 格式：不良位置1;不良代码1|不良位置2;不良代码2|
            ng_code_list = []
            if board_entity.is_repair_ng():
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        ng_code_list.append(f'{comp_entity.designator};{comp_entity.repair_ng_code}')

            if inspect_type == xcons.INSPECTOR:
                board_result = board_entity.get_robot_result()
            else:
                board_result = board_entity.get_repair_result()

            board_data_list.append({
                "SREIAL": board_entity.board_no,
                "BARCODE": board_entity.barcode,
                "RESULT": board_result,
                "NGCODE": "|".join(ng_code_list),
                "TESTDETAIL": ""
            })

        # 格式：2019-09-19 13:22:00
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)
        post_type_value = self.post_type_map[post_type]
        pcb_data = {
            "PARTNO": part_no,
            "LINENO": line_no,
            "MACHINE": machine_no,
            "STATION": station,
            "OP": op_mes,
            "STARTTIME": start_time,
            "ENDTIME": end_time,
            "LOADERBOARD": self.loader_board,
            "PROGROMNAME": pcb_entity.project_name,
            "PROGROMVERSION": "",
            "LOGFILE": "",
            "POSTTYPE": post_type_value,
            "BARCODELIST": board_data_list
        }

        err_msg = ''
        try:
            ret = xrequest.RequestUtil.post_json(send_data_url, pcb_data)
            # 返回结果：OK/NG
            if ret.get("Result") == 'NG':
                err_msg = f"过账失败，error：{ret.get('Message')}"
        except Exception as e:
            err_msg = f"本地网络异常，error：{e}"

        if err_msg:
            return self.x_response("false", err_msg)
        else:
            return self.x_response()
