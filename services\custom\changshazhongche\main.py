# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/18 上午9:10
# Author     ：sch
# version    ：python 3.8
# Description：长沙中车
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "changshazhongche release v1.0.0.9",
        "device": "401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-18 10:55  上传数据
date: 2023-07-24 09:42  修复上传的数据格式
date: 2023-07-24 15:22  defectType预留在界面填写，defectCode传中文不良描述
date: 2023-07-26 16:43  Defects传复判后的不良器件
date: 2023-12-06 10:26  增加上传设备状态到mes
date: 2023-12-07 16:31  修改上传数据的参数
date: 2023-12-14 11:15  上传数据根据CODE=OK来判断是否请求成功
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/WIP/Complete2"
        },

        "order_id": {
            "ui_name": "工单号码",
            "value": ""
        },
        "station": {
            "ui_name": "工位名称",
            "value": ""
        },
        "user_id": {
            "ui_name": "用户ID",
            "value": ""
        },
        "process": {
            "ui_name": "工序名称",
            "value": ""
        },
        "defect_type": {
            "ui_name": "defectType",
            "value": ""
        },
        "device_api_url": {
            "ui_name": "设备状态接口URL",
            "value": "http://***********/meswebservice/OPC/OPCInterface.asmx?op=OPC_Send_Data_To_Mes"
        },
        "line_id": {
            "ui_name": "设备状态-产线",
            "value": "M1"
        },
        "device_id": {
            "ui_name": "设备状态-设备ID",
            "value": "RES001"
        },
    }

    other_form = {
        "data_interface_id": {
            "ui_name": "数据INTERFACE_ID",
            "value": "OPC_TO_MES_SEND_AOI_INFO"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        order_id = data_vo.get_value_by_cons_key("order_id")
        station = data_vo.get_value_by_cons_key("station")
        user_id = data_vo.get_value_by_cons_key("user_id")
        process = data_vo.get_value_by_cons_key("process")
        defect_type = data_vo.get_value_by_cons_key("defect_type")
        api_username = data_vo.get_value_by_cons_key("api_username")
        api_password = data_vo.get_value_by_cons_key("api_password")
        data_interface_id = data_vo.get_value_by_cons_key("data_interface_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        input_serials = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            ix_x = False

            barcode = board_entity.barcode

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    if comp_entity.robot_ng_code == "25":
                        ix_x = True

                    if comp_entity.is_repair_ng():
                        comp_data.append({
                            "defectType": defect_type,
                            "defectCode": comp_entity.robot_ng_str,
                            "defectLocation": comp_entity.designator,
                            "defectPartNumber": comp_entity.part
                        })

            input_serials.append({
                "parentSerialNumber": pcb_sn,
                "serialNmuber": barcode,
                "index": int(board_entity.board_no),
                "isX": ix_x,
                "wipID": "",
                "defects": comp_data,
            })

        data_param = {
            "lotName": order_id,
            "station_name": station,
            "userID": user_id,
            "stepName": process,
            "SCAN_TYPE": 22,
            "inputSerials": input_serials
        }

        full_param = {
            "FACTORY": "1010",
            "INTERFACE_ID": data_interface_id,
            "USER": api_username,
            "PASSWORD": api_password,
            "TRAN_USER_ID": user_id,
            "TRAN_TIME": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE),
            "DATA": data_param
        }

        # ret = xrequest.RequestUtil.post_json(api_url, full_param)
        # if str(ret.get('errorcode')) != "0":
        #     return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('content')}")

        res = xrequest.RequestUtil.post_form(api_url, {"Request": json.dumps(full_param, ensure_ascii=False)},
                                             to_json=False)
        res_text = xutil.XmlUtil.get_xml_root_by_str(res).text
        ret = json.loads(res_text)
        # if str(ret.get('errorcode')) != "0":
        #     return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('content')}")

        if ret.get("CODE") != "OK":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('MESSAGE')}")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_api_url = other_vo.get_value_by_cons_key("device_api_url")
        user_id = other_vo.get_value_by_cons_key("user_id")
        line_id = other_vo.get_value_by_cons_key("line_id")
        process = other_vo.get_value_by_cons_key("process")
        device_id = other_vo.get_value_by_cons_key("device_id")

        device_status = other_vo.get_device_status_str()

        if device_status == "开始检测":
            # event_id = "生产PROD"
            event_id = "PROD_01"
            res_msg = "生产PROD"
        elif device_status == "停止检查":
            event_id = "STAN_01"
            res_msg = "待机STAN"
        else:
            self.log.warning(f"其他状态不上传至Mes！")
            return self.x_response()

        device_param = {
            "INTERFACE_ID": "OPC_TO_MES_SEND_EQUIP_STATUS",
            "FACTORY": "1010",
            "USER": "OPC",
            "PASSWORD": "MESOPC",
            "TRAN_USER_ID": user_id,
            "TRAN_TIME": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE),
            "DATA": {
                "LINE_ID": line_id,
                "OPER": process,
                "EVENT_ID": event_id,
                "RES_SEQ": "",
                "RES_MSG": res_msg,
                "RES_ID": device_id
            }
        }

        res = xrequest.RequestUtil.post_form(device_api_url, {"Request": json.dumps(device_param, ensure_ascii=False)},
                                             to_json=False)
        res_text = xutil.XmlUtil.get_xml_root_by_str(res).text
        ret = json.loads(res_text)

        if ret.get("CODE") != "OK":
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('MESSAGE')}")

        return self.x_response()
