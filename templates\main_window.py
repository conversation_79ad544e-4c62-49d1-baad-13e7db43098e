# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/main_window.ui'
#
# Created by: PyQt5 UI code generator 5.10.1
#
# WARNING! All changes made in this file will be lost!

from PyQt5 import QtCore, QtGui, QtWidgets

class Ui_MesMainWindow(object):
    def setupUi(self, MesMainWindow):
        MesMainWindow.setObjectName("MesMainWindow")
        MesMainWindow.resize(800, 600)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MesMainWindow.sizePolicy().hasHeightForWidth())
        MesMainWindow.setSizePolicy(sizePolicy)
        MesMainWindow.setStyleSheet("")
        self.main_widget = QtWidgets.QWidget(MesMainWindow)
        self.main_widget.setObjectName("main_widget")
        self.gridLayout = QtWidgets.QGridLayout(self.main_widget)
        self.gridLayout.setObjectName("gridLayout")
        self.frame_left_bottom = QtWidgets.QFrame(self.main_widget)
        self.frame_left_bottom.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.frame_left_bottom.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_left_bottom.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_left_bottom.setObjectName("frame_left_bottom")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.frame_left_bottom)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.frame_6 = QtWidgets.QFrame(self.frame_left_bottom)
        self.frame_6.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame_6.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.frame_6)
        self.verticalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.label_4 = QtWidgets.QLabel(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setObjectName("label_4")
        self.verticalLayout_6.addWidget(self.label_4)
        self.log_edit = QtWidgets.QPlainTextEdit(self.frame_6)
        self.log_edit.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.log_edit.sizePolicy().hasHeightForWidth())
        self.log_edit.setSizePolicy(sizePolicy)
        self.log_edit.setMaximumBlockCount(1000)
        self.log_edit.setObjectName("log_edit")
        self.verticalLayout_6.addWidget(self.log_edit)
        self.frame_4 = QtWidgets.QFrame(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_4.sizePolicy().hasHeightForWidth())
        self.frame_4.setSizePolicy(sizePolicy)
        self.frame_4.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_3.setContentsMargins(-1, 0, -1, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.btn_detail_log = QtWidgets.QPushButton(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_detail_log.sizePolicy().hasHeightForWidth())
        self.btn_detail_log.setSizePolicy(sizePolicy)
        self.btn_detail_log.setObjectName("btn_detail_log")
        self.horizontalLayout_3.addWidget(self.btn_detail_log)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.btn_clear_log_info = QtWidgets.QPushButton(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_clear_log_info.sizePolicy().hasHeightForWidth())
        self.btn_clear_log_info.setSizePolicy(sizePolicy)
        self.btn_clear_log_info.setObjectName("btn_clear_log_info")
        self.horizontalLayout_3.addWidget(self.btn_clear_log_info)
        self.verticalLayout_6.addWidget(self.frame_4)
        self.verticalLayout_5.addWidget(self.frame_6)
        self.gridLayout.addWidget(self.frame_left_bottom, 1, 0, 1, 1)
        self.frame_left_top = QtWidgets.QFrame(self.main_widget)
        self.frame_left_top.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_left_top.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_left_top.setObjectName("frame_left_top")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame_left_top)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label = QtWidgets.QLabel(self.frame_left_top)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.verticalLayout_2.addWidget(self.label)
        self.frame_2 = QtWidgets.QFrame(self.frame_left_top)
        self.frame_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.frame_7 = QtWidgets.QFrame(self.frame_2)
        self.frame_7.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.formLayout_2 = QtWidgets.QFormLayout(self.frame_7)
        self.formLayout_2.setContentsMargins(9, 9, 9, 9)
        self.formLayout_2.setVerticalSpacing(15)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label_5 = QtWidgets.QLabel(self.frame_7)
        self.label_5.setObjectName("label_5")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.send_total_number = QtWidgets.QLabel(self.frame_7)
        self.send_total_number.setAlignment(QtCore.Qt.AlignCenter)
        self.send_total_number.setObjectName("send_total_number")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.send_total_number)
        self.label_7 = QtWidgets.QLabel(self.frame_7)
        self.label_7.setObjectName("label_7")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_7)
        self.send_err_number = QtWidgets.QLabel(self.frame_7)
        self.send_err_number.setStyleSheet("color:red")
        self.send_err_number.setAlignment(QtCore.Qt.AlignCenter)
        self.send_err_number.setObjectName("send_err_number")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.send_err_number)
        self.label_13 = QtWidgets.QLabel(self.frame_7)
        self.label_13.setObjectName("label_13")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_13)
        self.status_total_number = QtWidgets.QLabel(self.frame_7)
        self.status_total_number.setAlignment(QtCore.Qt.AlignCenter)
        self.status_total_number.setObjectName("status_total_number")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.status_total_number)
        self.label_15 = QtWidgets.QLabel(self.frame_7)
        self.label_15.setObjectName("label_15")
        self.formLayout_2.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_15)
        self.status_err_number = QtWidgets.QLabel(self.frame_7)
        self.status_err_number.setStyleSheet("color:red")
        self.status_err_number.setAlignment(QtCore.Qt.AlignCenter)
        self.status_err_number.setObjectName("status_err_number")
        self.formLayout_2.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.status_err_number)
        self.horizontalLayout_2.addWidget(self.frame_7)
        self.frame_8 = QtWidgets.QFrame(self.frame_2)
        self.frame_8.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_8.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_8.setObjectName("frame_8")
        self.formLayout_3 = QtWidgets.QFormLayout(self.frame_8)
        self.formLayout_3.setContentsMargins(9, 9, 9, 9)
        self.formLayout_3.setVerticalSpacing(15)
        self.formLayout_3.setObjectName("formLayout_3")
        self.label_17 = QtWidgets.QLabel(self.frame_8)
        self.label_17.setObjectName("label_17")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_17)
        self.check_total_number = QtWidgets.QLabel(self.frame_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.check_total_number.sizePolicy().hasHeightForWidth())
        self.check_total_number.setSizePolicy(sizePolicy)
        self.check_total_number.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.check_total_number.setAlignment(QtCore.Qt.AlignCenter)
        self.check_total_number.setObjectName("check_total_number")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.check_total_number)
        self.label_19 = QtWidgets.QLabel(self.frame_8)
        self.label_19.setObjectName("label_19")
        self.formLayout_3.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_19)
        self.check_err_number = QtWidgets.QLabel(self.frame_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.check_err_number.sizePolicy().hasHeightForWidth())
        self.check_err_number.setSizePolicy(sizePolicy)
        self.check_err_number.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.check_err_number.setStyleSheet("color:red")
        self.check_err_number.setAlignment(QtCore.Qt.AlignCenter)
        self.check_err_number.setObjectName("check_err_number")
        self.formLayout_3.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.check_err_number)
        self.label_21 = QtWidgets.QLabel(self.frame_8)
        self.label_21.setObjectName("label_21")
        self.formLayout_3.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_21)
        self.get_sn_number = QtWidgets.QLabel(self.frame_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.get_sn_number.sizePolicy().hasHeightForWidth())
        self.get_sn_number.setSizePolicy(sizePolicy)
        self.get_sn_number.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.get_sn_number.setAlignment(QtCore.Qt.AlignCenter)
        self.get_sn_number.setObjectName("get_sn_number")
        self.formLayout_3.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.get_sn_number)
        self.label_23 = QtWidgets.QLabel(self.frame_8)
        self.label_23.setObjectName("label_23")
        self.formLayout_3.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_23)
        self.get_sn_err_number = QtWidgets.QLabel(self.frame_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.get_sn_err_number.sizePolicy().hasHeightForWidth())
        self.get_sn_err_number.setSizePolicy(sizePolicy)
        self.get_sn_err_number.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.get_sn_err_number.setStyleSheet("color:red")
        self.get_sn_err_number.setAlignment(QtCore.Qt.AlignCenter)
        self.get_sn_err_number.setObjectName("get_sn_err_number")
        self.formLayout_3.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.get_sn_err_number)
        self.horizontalLayout_2.addWidget(self.frame_8)
        self.verticalLayout_2.addWidget(self.frame_2)
        self.frame_3 = QtWidgets.QFrame(self.frame_left_top)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_3.sizePolicy().hasHeightForWidth())
        self.frame_3.setSizePolicy(sizePolicy)
        self.frame_3.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.frame_3.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.btn_reset_data = QtWidgets.QPushButton(self.frame_3)
        self.btn_reset_data.setObjectName("btn_reset_data")
        self.horizontalLayout.addWidget(self.btn_reset_data)
        self.btn_cron_clear = QtWidgets.QPushButton(self.frame_3)
        self.btn_cron_clear.setObjectName("btn_cron_clear")
        self.horizontalLayout.addWidget(self.btn_cron_clear)
        self.verticalLayout_2.addWidget(self.frame_3)
        self.gridLayout.addWidget(self.frame_left_top, 0, 0, 1, 1)
        self.frame_right = QtWidgets.QFrame(self.main_widget)
        self.frame_right.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.frame_right.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_right.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_right.setObjectName("frame_right")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_right)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.param_outer = QtWidgets.QFrame(self.frame_right)
        self.param_outer.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.param_outer.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.param_outer.setObjectName("param_outer")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.param_outer)
        self.verticalLayout_4.setContentsMargins(5, 5, 5, 5)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.label_2 = QtWidgets.QLabel(self.param_outer)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setObjectName("label_2")
        self.verticalLayout_4.addWidget(self.label_2)
        self.param_inner = QtWidgets.QFrame(self.param_outer)
        self.param_inner.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.param_inner.setFrameShadow(QtWidgets.QFrame.Raised)
        self.param_inner.setObjectName("param_inner")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.param_inner)
        self.verticalLayout.setContentsMargins(5, 5, 5, 5)
        self.verticalLayout.setSpacing(5)
        self.verticalLayout.setObjectName("verticalLayout")
        self.scrollArea = QtWidgets.QScrollArea(self.param_inner)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scrollArea.sizePolicy().hasHeightForWidth())
        self.scrollArea.setSizePolicy(sizePolicy)
        self.scrollArea.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 346, 382))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.setting_param_layout = QtWidgets.QFormLayout(self.scrollAreaWidgetContents)
        self.setting_param_layout.setObjectName("setting_param_layout")
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout.addWidget(self.scrollArea)
        self.verticalLayout_4.addWidget(self.param_inner)
        self.verticalLayout_3.addWidget(self.param_outer)
        self.frame_btn_1 = QtWidgets.QFrame(self.frame_right)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_btn_1.sizePolicy().hasHeightForWidth())
        self.frame_btn_1.setSizePolicy(sizePolicy)
        self.frame_btn_1.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame_btn_1.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_btn_1.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_btn_1.setObjectName("frame_btn_1")
        self.verticalLayout_3.addWidget(self.frame_btn_1)
        self.frame_btn_2 = QtWidgets.QFrame(self.frame_right)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_btn_2.sizePolicy().hasHeightForWidth())
        self.frame_btn_2.setSizePolicy(sizePolicy)
        self.frame_btn_2.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame_btn_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_btn_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_btn_2.setObjectName("frame_btn_2")
        self.verticalLayout_3.addWidget(self.frame_btn_2)
        self.btn_frame = QtWidgets.QFrame(self.frame_right)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_frame.sizePolicy().hasHeightForWidth())
        self.btn_frame.setSizePolicy(sizePolicy)
        self.btn_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.btn_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.btn_frame.setObjectName("btn_frame")
        self.btn_frame_layout = QtWidgets.QHBoxLayout(self.btn_frame)
        self.btn_frame_layout.setContentsMargins(5, 5, 5, 5)
        self.btn_frame_layout.setObjectName("btn_frame_layout")
        self.btn_save_setting = QtWidgets.QPushButton(self.btn_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_save_setting.sizePolicy().hasHeightForWidth())
        self.btn_save_setting.setSizePolicy(sizePolicy)
        self.btn_save_setting.setObjectName("btn_save_setting")
        self.btn_frame_layout.addWidget(self.btn_save_setting)
        self.verticalLayout_3.addWidget(self.btn_frame)
        self.gridLayout.addWidget(self.frame_right, 0, 1, 2, 1)
        self.gridLayout.setRowStretch(0, 1)
        self.gridLayout.setRowStretch(1, 2)
        MesMainWindow.setCentralWidget(self.main_widget)
        self.menubar = QtWidgets.QMenuBar(MesMainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 800, 28))
        self.menubar.setObjectName("menubar")
        self.menu_setting = QtWidgets.QMenu(self.menubar)
        self.menu_setting.setObjectName("menu_setting")
        self.menu_util = QtWidgets.QMenu(self.menubar)
        self.menu_util.setObjectName("menu_util")
        self.menu_func = QtWidgets.QMenu(self.menubar)
        self.menu_func.setObjectName("menu_func")
        self.menu = QtWidgets.QMenu(self.menubar)
        self.menu.setObjectName("menu")
        MesMainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MesMainWindow)
        self.statusbar.setObjectName("statusbar")
        MesMainWindow.setStatusBar(self.statusbar)
        self.action_other_setting = QtWidgets.QAction(MesMainWindow)
        self.action_other_setting.setObjectName("action_other_setting")
        self.action = QtWidgets.QAction(MesMainWindow)
        self.action.setObjectName("action")
        self.action_about = QtWidgets.QAction(MesMainWindow)
        self.action_about.setObjectName("action_about")
        self.action_auto_start = QtWidgets.QAction(MesMainWindow)
        self.action_auto_start.setObjectName("action_auto_start")
        self.action_test_send = QtWidgets.QAction(MesMainWindow)
        self.action_test_send.setObjectName("action_test_send")
        self.action_create_desktop_icon = QtWidgets.QAction(MesMainWindow)
        self.action_create_desktop_icon.setObjectName("action_create_desktop_icon")
        self.action_login = QtWidgets.QAction(MesMainWindow)
        self.action_login.setObjectName("action_login")
        self.action_param_setting = QtWidgets.QAction(MesMainWindow)
        self.action_param_setting.setObjectName("action_param_setting")
        self.action_send_email = QtWidgets.QAction(MesMainWindow)
        self.action_send_email.setObjectName("action_send_email")
        self.action_common_config = QtWidgets.QAction(MesMainWindow)
        self.action_common_config.setObjectName("action_common_config")
        self.action_custom_mes_code = QtWidgets.QAction(MesMainWindow)
        self.action_custom_mes_code.setObjectName("action_custom_mes_code")
        self.actionUsePassWord = QtWidgets.QAction(MesMainWindow)
        self.actionUsePassWord.setCheckable(True)
        self.actionUsePassWord.setChecked(False)
        self.actionUsePassWord.setObjectName("actionUsePassWord")
        self.menu_setting.addAction(self.action_other_setting)
        self.menu_setting.addAction(self.action_param_setting)
        self.menu_setting.addAction(self.action_common_config)
        self.menu_setting.addAction(self.action_custom_mes_code)
        self.menu_util.addAction(self.action_create_desktop_icon)
        self.menu_util.addAction(self.action_auto_start)
        self.menu_util.addAction(self.action_test_send)
        self.menu_util.addAction(self.action_send_email)
        self.menu_func.addAction(self.action_about)
        self.menu.addAction(self.action_login)
        self.menu.addAction(self.actionUsePassWord)
        self.menubar.addAction(self.menu.menuAction())
        self.menubar.addAction(self.menu_setting.menuAction())
        self.menubar.addAction(self.menu_util.menuAction())
        self.menubar.addAction(self.menu_func.menuAction())

        self.retranslateUi(MesMainWindow)
        QtCore.QMetaObject.connectSlotsByName(MesMainWindow)

    def retranslateUi(self, MesMainWindow):
        _translate = QtCore.QCoreApplication.translate
        MesMainWindow.setWindowTitle(_translate("MesMainWindow", "Mes System"))
        self.label_4.setText(_translate("MesMainWindow", "简要日志"))
        self.btn_detail_log.setText(_translate("MesMainWindow", "详细日志"))
        self.btn_clear_log_info.setText(_translate("MesMainWindow", "清除日志"))
        self.label.setText(_translate("MesMainWindow", "发送统计"))
        self.label_5.setText(_translate("MesMainWindow", "发送总次数"))
        self.send_total_number.setText(_translate("MesMainWindow", "0"))
        self.label_7.setText(_translate("MesMainWindow", "发送失败次数"))
        self.send_err_number.setText(_translate("MesMainWindow", "0"))
        self.label_13.setText(_translate("MesMainWindow", "设备状态总次数"))
        self.status_total_number.setText(_translate("MesMainWindow", "0"))
        self.label_15.setText(_translate("MesMainWindow", "设备状态失败次数"))
        self.status_err_number.setText(_translate("MesMainWindow", "0"))
        self.label_17.setText(_translate("MesMainWindow", "校验总次数"))
        self.check_total_number.setText(_translate("MesMainWindow", "0"))
        self.label_19.setText(_translate("MesMainWindow", "校验失败次数"))
        self.check_err_number.setText(_translate("MesMainWindow", "0"))
        self.label_21.setText(_translate("MesMainWindow", "获取条码总次数"))
        self.get_sn_number.setText(_translate("MesMainWindow", "0"))
        self.label_23.setText(_translate("MesMainWindow", "获取条码失败次数"))
        self.get_sn_err_number.setText(_translate("MesMainWindow", "0"))
        self.btn_reset_data.setText(_translate("MesMainWindow", "计数清零"))
        self.btn_cron_clear.setText(_translate("MesMainWindow", "定时清除"))
        self.label_2.setText(_translate("MesMainWindow", "参数配置"))
        self.btn_save_setting.setText(_translate("MesMainWindow", "保存"))
        self.menu_setting.setTitle(_translate("MesMainWindow", "设置"))
        self.menu_util.setTitle(_translate("MesMainWindow", "工具"))
        self.menu_func.setTitle(_translate("MesMainWindow", "帮助"))
        self.menu.setTitle(_translate("MesMainWindow", "系统"))
        self.action_other_setting.setText(_translate("MesMainWindow", "其他设置"))
        self.action.setText(_translate("MesMainWindow", "版本"))
        self.action_about.setText(_translate("MesMainWindow", "关于"))
        self.action_auto_start.setText(_translate("MesMainWindow", "设置开机自启"))
        self.action_test_send.setText(_translate("MesMainWindow", "模拟主软件触发"))
        self.action_create_desktop_icon.setText(_translate("MesMainWindow", "创建桌面快捷方式"))
        self.action_login.setText(_translate("MesMainWindow", "登录"))
        self.action_param_setting.setText(_translate("MesMainWindow", "其他参数设置"))
        self.action_send_email.setText(_translate("MesMainWindow", "一键发送日志"))
        self.action_common_config.setText(_translate("MesMainWindow", "个性化配置项"))
        self.action_custom_mes_code.setText(_translate("MesMainWindow", "自定义Mes不良代码"))
        self.actionUsePassWord.setText(_translate("MesMainWindow", "UsePassWord"))

