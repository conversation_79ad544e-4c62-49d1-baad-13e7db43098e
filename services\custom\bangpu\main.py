# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/13 下午3:34
# Author     ：sch
# version    ：python 3.8
# Description：邦普
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

csv_pcb_panel_template = """程序名,{pcb_project_name}
工单号,{order_id}
测试时间,{pcb_test_time}
操作员,{operator}
整板条码,{pcb_sn}
整板结果,{pcb_final_result}
拼板数量,{pcb_board_number}
拼板复判NG数量,{pcb_board_user_ng_number}
器件总数,{pcb_comp_number}
器件复判不良总数,{pcb_comp_user_ng_number}
设备ID,{device_id}

拼板号,条码,拼板结果,器件位号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_final_result},{comp_designator},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "bangpu release v1.0.0.2",
        "device": "AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-13 15:35  生成本地csv文档
date: 2024-07-22 11:04  project.xml获取不到批次号，则从report.xml里的order标签获取批次号
""", }

    form = {
        "order_id_ui": {
            "ui_name": "工单号",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "device_id": {
            "ui_name": "设备ID",
            "value": "",
        },
    }

    path = {
        "save_path": {
            "ui_name": "csv保存路径",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        order_id_ui = data_vo.get_value_by_cons_key("order_id_ui")
        operator = data_vo.get_value_by_cons_key("operator")
        device_id = data_vo.get_value_by_cons_key("device_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        comp_data_str = ""

        pcb_board_user_ng_number = 0
        pcb_comp_user_ng_number = 0

        only_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not only_sn and barcode:
                only_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += csv_comp_panel_template.format(**{
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_final_result": board_entity.get_final_result(),
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                })

        order_id = pcb_entity.order_id

        self.log.info(f"主软件传过来的批次号：{order_id}")

        if not order_id:
            order_id = order_id_ui

        pcb_result = pcb_entity.get_final_result()

        csv_content = csv_pcb_panel_template.format(**{
            "pcb_project_name": pcb_entity.project_name,
            "order_id": order_id,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1),
            "operator": operator,
            "pcb_sn": pcb_entity.pcb_barcode,
            "pcb_final_result": pcb_result,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "device_id": device_id,
            "CompData": comp_data_str
        })

        if not only_sn:
            only_sn = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        xutil.FileUtil.write_content_to_file(f"{save_path}/{only_sn}_{pcb_result}.csv", csv_content)

        return self.x_response()
