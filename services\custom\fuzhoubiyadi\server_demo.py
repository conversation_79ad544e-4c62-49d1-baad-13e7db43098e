# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : mesconfig_server_demo.py
# Time       ：2023/12/14 上午9:36
# Author     ：sch
# version    ：python 3.8
# Description：
"""
# !/usr/bin/env python
# -*-coding:utf-8 -*-
import json
import socket
import traceback
from datetime import datetime

"""
# File       : 
# Time       ：2023/12/14 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：模拟测试脚本
"""


def send_response(status: bool = True, msg: str = "ok") -> str:
    """
    返回标准的响应给主软件
    :param status:
    :param msg:
    {'Description': 'ok', 'MesStatus': True, 'SocketStatus': True}
    :return:
    """
    ret = {
        'Description': msg,
        'MesStatus': status,
        'SocketStatus': status
    }

    ret = json.dumps(ret, ensure_ascii=False)

    print(f"<<<<<<<<<数据返回给主软件：{ret}")
    return ret


def call_response(status: bool = True, msg: str = "ok") -> str:
    """
    返回标准的响应给主软件
    ps: {'key': '2023.03.14 17:16:32', 'result': True, 'string': 'ok'}
    :param status:
    :param msg:
    :return:
    """
    time_now = datetime.now().strftime("%Y.%m.%d %H:%M:%S")

    ret = {
        "key": time_now,
        "result": status,
        "string": msg
    }

    ret = json.dumps(ret, ensure_ascii=False)
    print(f"<<<<<<<<<数据返回给主软件：{ret}")

    return ret


if __name__ == '__main__':
    port = 9090
    print(f"mes配置器 socket服务将启动在: 0.0.0.0:{port} ...")
    sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    # TCP释放连接后实现端口的立即复用
    sk.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    sk.bind(("0.0.0.0", port))
    sk.listen()
    print(f"mes配置器 socket服务启动成功!")

    while True:
        conn, addr = sk.accept()
        try:
            data = conn.recv(4086)
            if data == b'connect Status.':
                # log.info(f"connect status...")
                continue

            if data == b'CLOSE_BY_EVENT':
                conn.send(b"the server had closed")
                conn.close()
                sk.close()
                print(f"socket服务已关闭~")
                break

            device_data = data.decode("utf-8")

            print(f">>>>>>>>>接收到的主软件数据: {device_data}")
            if not device_data.startswith("{"):
                print(f"不是规范的mes请求，不处理！")
                continue

            json_data = json.loads(device_data)  # 主软件传过来的数据

            # 两个接口，一个发送数据到mes，一个其他所有的接口
            if "ReviewPath" in json_data:
                ret_data = send_response()
            else:
                if json_data.get("funcName") == "GetProjectNameBySn":

                    project_name_from_mes = "LY02.002"  # 从Mes获取到的程序名
                    ret_data = call_response(True, project_name_from_mes)
                else:
                    ret_data = call_response(True, "其他接口默认返回ok")

            conn.send(ret_data.encode("utf-8"))
        except Exception as err:
            print(f"socket异常，error：{err}")
            print(f"error: {traceback.format_exc()}")
        finally:
            conn.close()
