# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/12 下午5:42
# Author     ：sch
# version    ：python 3.8
# Description：厦门新能安
"""
import json
from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "Ampace released v3.0.0.6",
        "device": "AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-14 16:32  厦门新能安
date: 2024-06-19 10:20  上传数据增加参数addUser
date: 2024-06-24 17:59  site参数预留配置
date: 2024-08-20 10:54  兼容客户接口返回
date: 2025-03-04 16:03  bugfix: 兼容客户接口返回
date: 2025-04-19 11:48  jira->ATAOI_2019-38873: 中转改为直接调用接口
""", }

    other_form = {
        "api_url_data": {
            "ui_name": "接口URL",
            "value": "http://************:85/TestingBLL/SaveAOITestResult",
        },
        "site_ui": {
            "ui_name": "site",
            "value": "AOI",
        }
    }

    form = {
        "pn": {
            "ui_name": "料号信息",
            "value": "********",
        },
        "wo": {
            "ui_name": "工单信息",
            "value": "*********",
        },
        "machine": {
            "ui_name": "工序信息",
            "value": "7301AOI-1",
        },
        "user_account": {
            "ui_name": "员工账号",
            "value": "M-08518\\\\hollyaoi",
        },
        "add_user": {
            "ui_name": "addUser",
            "value": "",
        },
    }

    path = {
        "log_path": {
            "ui_name": "Log路径",
            "value": "",
        },
        "ng_path": {
            "ui_name": "不良图路径",
            "value": "",
        },
        "pcb_path": {
            "ui_name": "整板图路径",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        pn = data_vo.get_value_by_cons_key("pn")
        wo = data_vo.get_value_by_cons_key("wo")
        machine = data_vo.get_value_by_cons_key("machine")
        user_account = data_vo.get_value_by_cons_key("user_account")

        log_path = data_vo.get_value_by_cons_key("log_path")
        ng_path = data_vo.get_value_by_cons_key("ng_path")
        pcb_path = data_vo.get_value_by_cons_key("pcb_path")
        add_user = data_vo.get_value_by_cons_key("add_user")
        site_ui = data_vo.get_value_by_cons_key("site_ui")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        project_name = pcb_entity.project_name

        comp_data_list = []
        comp_robot_ng_number = 0
        comp_repair_ng_number = 0
        comp_falsecall_number = 0

        total_block = 0
        bad_block = 0

        ret_res = self.x_response()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            board_final_result = board_entity.get_final_result()

            if not barcode and pcb_sn:
                barcode = pcb_sn

            ng_full_path = f"{ng_path}/{project_name}/{date_file}/{barcode}"
            xutil.FileUtil.ensure_dir_exist(ng_full_path)

            total_block += 1
            if board_final_result == "BadBoard":
                bad_block += 1

            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_repair_ng_number += board_entity.comp_repair_ng_number
            comp_falsecall_number += (board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number)

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    # 保存NG器件图
                    comp_tag = comp_entity.designator

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        pic_name = f"{barcode}_{comp_tag}.png"
                        dst_filename = f"{ng_full_path}/{pic_name}"
                        xutil.FileUtil.copy_file(comp_src_img, dst_filename)

                        pic_path = f"/{project_name}/{date_file}/{barcode}"
                    else:
                        pic_name = ""
                        pic_path = ""

                    comp_data_list.append({
                        "errorCode": comp_entity.robot_ng_code,
                        "errorFlag": comp_entity.get_final_result("PASS", "PASS", "NG"),
                        "id": comp_tag,
                        "multiNum": board_no,
                        "picName": pic_name,
                        "sub_barcode": barcode,
                        "picPath": pic_path,
                        "recipeName": project_name
                    })

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        data_param = {
            "pn": pn,
            "wo": wo,
            "site": site_ui,
            "adduser": add_user,
            "fileUrl": "D:\\\\data",
            "alarmQty": str(comp_robot_ng_number),
            "barcode": pcb_sn,
            "endTime": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_FILE),
            "machine": machine,
            "ngQty": str(comp_repair_ng_number),
            "passQty": str(comp_falsecall_number),
            "recipeName": project_name,
            "result": pcb_entity.get_final_result("PASS", "PASS", "NG"),
            "startTime": test_time,
            "userAccount": user_account,
            "totalBlock": total_block,
            "badBlock": bad_block,
            "PANELTEST": 1 if total_block > 1 else 0,  # 拼/单板测试模式
            "data": comp_data_list
        }

        for item in pcb_entity.list_all_pcb_image():
            if "B_" in item:
                board_side = "B"
            else:
                board_side = "T"

            full_pcb_path = f"{pcb_path}/{project_name}/{date_file}"
            xutil.FileUtil.ensure_dir_exist(full_pcb_path)

            dst_filename = f"{full_pcb_path}/{board_side}_{pcb_sn}_{time_file}.jpg"
            xutil.FileUtil.copy_file(item, dst_file=dst_filename)

        xutil.FileUtil.dump_json_to_file(f"{log_path}/{test_time}.json", data_param)

        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

        if str(ret.get("Result")) not in ["1", "true", "True", True, "ok", "OK"]:
            ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('Msg')}")

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        if btn_vo.get_btn_key() == "testConnectWindow":
            is_connect = xrequest.SocketUtil.check_window_port(window_ip)
            if not is_connect:
                return self.x_response("false", f"连接失败，请检查window网络是否连接，或者中转程序是否打开！")

        return self.x_response()
