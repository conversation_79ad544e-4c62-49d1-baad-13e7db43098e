# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t1.py
# Time       ：2024/12/17 下午6:56
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

if __name__ == '__main__':
    #     ret_str = """<?xml version="1.0" encoding="utf-8"?>
    # <string xmlns="WWW.SUNWODA.COM">sn1,sn2,sn3</string>"""
    #
    #     sn_str = xutil.XmlUtil.get_xml_root_by_str(ret_str).text
    #     print(sn_str)
    #
    #
    ret_str2 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <PcbAndFpcBindResponse xmlns="WWW.SUNWODA.COM">
      <PcbAndFpcBindResult>OK</PcbAndFpcBindResult>
    </PcbAndFpcBindResponse>
  </soap:Body>
</soap:Envelope>"""

    root = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
    ret_str = root[0][0][0].text
    print(ret_str)
