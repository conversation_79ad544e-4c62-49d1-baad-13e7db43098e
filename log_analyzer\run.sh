#!/bin/bash

echo "日志快速定位分析工具"
echo "===================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.7+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用Python: $PYTHON_CMD"

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Python版本: $PYTHON_VERSION"

# 检查依赖是否安装
echo "检查依赖包..."
$PYTHON_CMD -c "import PyQt5" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

# 启动程序
echo "启动日志分析工具..."
$PYTHON_CMD main.py

if [ $? -ne 0 ]; then
    echo "程序运行出错"
    read -p "按任意键继续..."
fi
