# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/17 下午15:50
# Author     ：gyr
# version    ：python 3.8
# Description：株洲中车汽车电子
"""
import os
from datetime import datetime
from typing import Any

from common import xrequest, xutil, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["株洲中车汽车电子", "zhuzhouzhongche"],
        "version": "release v1.0.0.6",
        "device": "AIS30X,AIS50X",
        "feature": ["获取条码,条码校验,上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-04-17 15:50  jira:ATAOI_2019-38806,获取条码,条码校验,发送mes
date: 2025-04-23 15:19  jira:ATAOI_2019-38806,发送mes如果是OK的板卡不需要baditem和badimage字段参数
date: 2025-04-23 17:23  jira:ATAOI_2019-38806,type放到其它参数中设置,只有collectPointCode有值的时候才会在message参数中增加该字段
date: 2025-04-23 17:23  bugfix:图片id唯一性修复
date: 2025-04-24 14:23  optimize:图片唯一ID的uuid部分只获取后六位
date: 2025-05-06 11:22  jira:ATAOI_2019-38806,baditem传检测ng的器件
""", }

    form = {
        "api_url_one": {
            "ui_name": "条码接口地址",
            "value": ""
        },
        "api_url_two": {
            "ui_name": "mes接口地址",
            "value": ""
        },
        "organizational_code": {
            "ui_name": "组织编码",
            "value": ""
        },
        "iotDevCode": {
            "ui_name": "设备编码",
            "value": ""
        },
        "process_code": {
            "ui_name": "工序编码",
            "value": ""
        },
        "label_printing_type": {
            "ui_name": "标签打印类型",
            "value": ""
        },
        "collectPointCode": {
            "ui_name": "采集点编码",
            "value": ""
        }
    }

    other_form = {
        "tYpe_content": {
            "ui_name": "type_content",
            "value": "content"
        }
    }

    combo = {
        "transmit_mode": {
            "ui_name": "发送模式",
            "item": ["大板模式", "小板模式"],
            "value": "大板模式"
        },
        "is_lbw_ByGrp": {
            "ui_name": "标签组过站",
            "item": ["y", "n"],
            "value": "y"
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        获取条码
        大板模式：需要获取条码
        小板模式：无需获取条码
        """
        transmit_mode = other_vo.get_value_by_cons_key("transmit_mode")

        if transmit_mode != "大板模式":
            self.log.warning("当前为小板模式,无需获取条码")
            return self.x_response()

        api_url_one = other_vo.get_value_by_cons_key("api_url_one")
        org_code = other_vo.get_value_by_cons_key("organizational_code")
        process_code = other_vo.get_value_by_cons_key("process_code")
        pcb_barcode = other_vo.get_pcb_sn()
        if not pcb_barcode:
            return self.x_response("false", "未获取到条码")

        check_param = {
            "orgCode": org_code,
            "wpCode": process_code,
            "subType": "GETPCB",
            "barCode": pcb_barcode
        }

        ret = xrequest.RequestUtil.post_json(api_url_one, check_param)
        if str(ret.get("code")) != "0":
            return self.x_response("false", f"获取条码失败: {ret.get('message', '未知错误')}")

        small_board_sns = ret.get("小板条码", {}).get("rows", [])
        if not small_board_sns:
            return self.x_response("false", "未获取到小板条码")

        sn_list = [item.get("lb_code") for item in small_board_sns if item.get("lb_code")]
        return self.x_response("true", ",".join(sn_list))

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        条码校验
        """
        api_url_one = other_vo.get_value_by_cons_key("api_url_one")
        org_code = other_vo.get_value_by_cons_key("organizational_code")
        process_code = other_vo.get_value_by_cons_key("process_code")

        sn_list = other_vo.list_sn()
        if not sn_list:
            return self.x_response("false", "没有需要校验的条码")

        # 校验条码
        for sn in sn_list:
            check_param = {
                "orgCode": org_code,
                "wpCode": process_code,
                "subType": "",
                "barCode": sn
            }
            ret = xrequest.RequestUtil.post_json(api_url_one, check_param)
            if str(ret.get("code")) != "0":
                return self.x_response("false", f"条码 {sn} 校验失败: {ret.get('message', '未知错误')}")

        return self.x_response()

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_two = data_dao.get_value_by_cons_key("api_url_two")
        org_code = data_dao.get_value_by_cons_key("organizational_code")
        iot_dev_code = data_dao.get_value_by_cons_key("iotDevCode")
        label_printing_type = data_dao.get_value_by_cons_key("label_printing_type")
        transmit_mode = data_dao.get_value_by_cons_key("transmit_mode")
        is_lbw_by_grp = data_dao.get_value_by_cons_key("is_lbw_ByGrp")
        collect_point_code = data_dao.get_value_by_cons_key("collectPointCode")
        ty_pe = data_dao.get_value_by_cons_key("tYpe_content")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)
        current_time = pcb_entity.get_start_time()
        if isinstance(current_time, datetime):
            current_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        message = {
            "id": xutil.OtherUtil.get_uuid4_str()[-6:],
            "type": ty_pe,
            "orgCode": org_code,
            "iotDevCode": iot_dev_code,
            "time": current_time,
            "deviceType": "QC",
            "body": {
                "subType": "AOI",
                "recipeName": pcb_entity.project_name,
                "deviceTime": current_time,
                "pcbChecks": [],
                "crtPrtType": label_printing_type,
                "isLbwpByGrp": is_lbw_by_grp
            },
            "atts": []
        }
        if collect_point_code:
            message["collectPointCode"] = collect_point_code

        if transmit_mode == "大板模式":
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)

                pcb_check = {
                    "barCode": board_entity.barcode,
                    "checkResult": board_entity.get_repair_result()
                }

                if board_entity.is_repair_ng():
                    pcb_check["badItems"] = []
                    pcb_check["badImages"] = []

                    # 处理元器件
                    for comp_entity in board_entity.yield_comp_entity():
                        if comp_entity.is_robot_ng():
                            bad_item = {
                                "loc": comp_entity.designator,
                                "code": comp_entity.repair_ng_code,
                                "testResult": comp_entity.robot_result,
                                "reviseResult": comp_entity.repair_result
                            }
                            pcb_check["badItems"].append(bad_item)

                            if comp_entity.image_path and os.path.exists(comp_entity.image_path):
                                image_id = f"{comp_entity.designator}_{start_time}_{xutil.OtherUtil.get_uuid4_str()[-6:]}"
                                image_name = f"{pcb_entity.pcb_barcode}_{comp_entity.designator}_{start_time}.jpg"

                                bad_image = {
                                    "id": image_id,
                                    "loc": comp_entity.designator,
                                    "imageFileName": image_name
                                }
                                pcb_check["badImages"].append(bad_image)

                                # with open(comp_entity.image_path, 'rb') as img_file:
                                #     img_data = img_file.read()
                                #     img_base64 = base64.b64encode(img_data).decode('utf-8')

                                att = {
                                    "id": image_id,
                                    "name": image_name,
                                    "data": xutil.OtherUtil.file_to_base64_content(comp_entity.image_path)
                                }
                                message["atts"].append(att)

                message["body"]["pcbChecks"].append(pcb_check)

            try:
                ret = xrequest.RequestUtil.post_json(api_url_two, message)
                if str(ret.get("code")) != "0":
                    return self.x_response("false", f"上传数据失败: {ret.get('message', '未知错误')}")
            except Exception as e:
                self.log.error(f"大板数据上传异常: {str(e)}")
                return self.x_response("false", f"大板数据上传异常: {str(e)}")

        else:
            self.log.info(f"当前为小板模式,按拼板上传")
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                message = {
                    "id": xutil.OtherUtil.get_uuid4_str()[-6:],
                    "collectPointCode": "",
                    "type": ty_pe,
                    "orgCode": org_code,
                    "iotDevCode": iot_dev_code,
                    "time": current_time,
                    "deviceType": "QC",
                    "body": {
                        "subType": "AOI",
                        "recipeName": pcb_entity.project_name,
                        "deviceTime": current_time,
                        "crtPrtType": label_printing_type,
                        "isLbwpByGrp": is_lbw_by_grp,
                        "pcbChecks": [
                            {
                                "barCode": board_entity.barcode,
                                "checkResult": board_entity.get_repair_result(),
                            }
                        ]
                    },
                    "atts": []
                }
                if collect_point_code:
                    message["collectPointCode"] = collect_point_code

                if board_entity.is_repair_ng():
                    message["body"]["pcbChecks"][0]["badItems"] = []
                    message["body"]["pcbChecks"][0]["badImages"] = []

                    for comp_entity in board_entity.yield_comp_entity():
                        if comp_entity.is_robot_ng():
                            bad_item = {
                                "loc": comp_entity.designator,
                                "code": comp_entity.repair_ng_code,
                                "testResult": comp_entity.robot_result,
                                "reviseResult": comp_entity.repair_result
                            }
                            message["body"]["pcbChecks"][0]["badItems"].append(bad_item)

                            if comp_entity.image_path and os.path.exists(comp_entity.image_path):
                                image_id = f"{comp_entity.designator}_{start_time}_{xutil.OtherUtil.get_uuid4_str()[-6:]}"
                                image_name = f"{pcb_entity.pcb_barcode}_{comp_entity.designator}_{start_time}.jpg"

                                bad_image = {
                                    "id": image_id,
                                    "loc": comp_entity.designator,
                                    "imageFileName": image_name
                                }
                                message["body"]["pcbChecks"][0]["badImages"].append(bad_image)

                                # with open(comp_entity.image_path, 'rb') as img_file:
                                #     img_data = img_file.read()
                                #     img_base64 = base64.b64encode(img_data).decode('utf-8')

                                att = {
                                    "id": image_id,
                                    "name": image_name,
                                    "data": xutil.OtherUtil.file_to_base64_content(comp_entity.image_path)
                                }
                                message["atts"].append(att)
                try:
                    ret = xrequest.RequestUtil.post_json(api_url_two, message)
                    if str(ret.get("code")) != "0":
                        return self.x_response("false", f"上传数据失败: {ret.get('message', '未知错误')}")
                except Exception as e:
                    self.log.error(f"拼版 {board_entity.board_no} 上传数据异常: {str(e)}")
                    return self.x_response("false", f"拼版 {board_entity.barcode} 上传数据异常: {str(e)}")

        return self.x_response()
