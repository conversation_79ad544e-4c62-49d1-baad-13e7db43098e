# !/usr/bin/env python
# -*-coding:utf-8 -*-


"""
# File       : main.py
# Time       ：2024/12/27 上午10:58
# Author     ：gyr
# version    ：python 3.8
# Description：蓝威新源
"""

import os
import time
from typing import Any

from common import xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

file_test_data = "[Data]\nType={equipment_model}\nTime={cur_time}\nLineNum={pro_line_num}\nMachineID={" \
                 "machine_id}\nChannelld={thoroughfare_id}\nQRcode={board_sn}\nTestResult={final_result}\n"


class Engine(ErrorMapEngine):
    version = {
        "title": "lanweixinyuan release v1.0.0.4",
        "device": "AIS501",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2024-12-19 条码校验+生成txt文件
date: 2025-01-07 检测OK时无需传test_data，修改时间戳格式
date: 2025-02-19 jira:35515,条码校验完后删除状态文件status_file_path
date: 2025-02-21 11:12 jira:37365,拼版之间生成文件需要配置延迟时间
"""
    }

    path = {
        "save_path_sn1": {
            "ui_name": "条码文件保存路径",
            "value": ""
        },
        "save_path_sn2": {
            "ui_name": "条码校验读取路径",
            "value": ""
        },
        "save_path_test": {
            "ui_name": "测试数据保存路径",
            "value": ""
        }
    }

    form = {
        "equipment_model": {
            "ui_name": "设备型号",
            "value": ""
        },
        "pro_line_num": {
            "ui_name": "产线号",
            "value": ""
        },
        "machine_id": {
            "ui_name": "机器ID",
            "value": ""
        },
        "thoroughfare_id": {
            "ui_name": "通道ID",
            "value": ""
        },
        "doc_timeout": {
            "ui_name": "文档超时时间",
            "value": ""
        },
        "check_times": {
            "ui_name": "检查次数",
            "value": ""
        },
        "board_delay": {
            "ui_name": "拼版文件生成间隔时间(秒)",
            "value": ""
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        # 获取相关配置参数
        save_path_sn1 = other_vo.get_value_by_cons_key("save_path_sn1", not_null=True)
        save_path_sn2 = other_vo.get_value_by_cons_key("save_path_sn2", not_null=True)
        doc_timeout = other_vo.get_value_by_cons_key("doc_timeout", to_int=True)  # 文档超时时间
        check_times = other_vo.get_value_by_cons_key("check_times", to_int=True)  # 检查次数

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            # 保存条码文件的完整路径
            sn_file_path = os.path.join(save_path_sn1, f"{sn}.txt")
            # 打开文件并写入条码
            with open(sn_file_path, 'w') as sn_file:
                sn_file.write(sn)
            self.log.info(f"生成二维码文件: {sn_file_path}")

            status_file_path = os.path.join(save_path_sn2, f"{sn}.txt")
            # 记录开始等待文件生成的时间
            start_time = time.time()
            # 初始检查次数为0
            attempts = 0
            # 初始结果为"ERROR"
            result = "ERROR"

            # 查文件生成，直到达到最大检查次数
            while attempts < check_times:
                # 检查文件是否存在
                if os.path.exists(status_file_path):
                    # 打开文件并读取内容
                    with open(status_file_path, 'r') as status_file:
                        status = status_file.read().strip()

                    # 如果文件内容为"OK"或"NG"，更新结果并跳出循环
                    if status in ["OK", "NG"]:
                        result = status
                        break

                # 如果等待时间超过文档超时时间 更新结果为"TIMEOUT"并跳出循环
                if time.time() - start_time > doc_timeout:
                    result = "TIMEOUT"
                    break

                time.sleep(5)  # 等待5秒后再次检查
                attempts += 1  # 增加检查次数

            if result == "OK":
                self.log.info(f"条码校验成功: {sn}")
                os.remove(status_file_path)
                self.log.info(f"删除OK状态文件 : {status_file_path}")
            elif result == "NG":
                self.log.error(f"条码校验失败: {sn}")
                os.remove(status_file_path)
                self.log.info(f"删除NG状态文件 : {status_file_path}")
                return self.x_response("false", f"条码校验失败，条码: {sn}")
            else:
                self.log.error(f"条码校验超时或错误: {sn}")
                return self.x_response("false", f"条码校验超时或错误，条码: {sn}")

        return self.x_response()

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        equipment_model = data_dao.get_value_by_cons_key("equipment_model")
        pro_line_num = data_dao.get_value_by_cons_key("pro_line_num")
        machine_id = data_dao.get_value_by_cons_key("machine_id")
        thoroughfare_id = data_dao.get_value_by_cons_key("thoroughfare_id")
        save_path = data_dao.get_value_by_cons_key("save_path_test")
        cur_time = time.strftime("%Y%m%d%H%M%S")
        board_delay = data_dao.get_value_by_cons_key("board_delay", to_int=True)
        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)

        self.log.info(f"测试数据保存路径: {save_path}")

        if not os.path.exists(save_path):
            self.log.error(f"路径不存在: {save_path}")
            return self.x_response("false", f"路径不存在: {save_path}")

        # 记录是否是第一个拼版
        is_first_board = True

        for board_entity in pcb_entity.yield_board_entity():

            # 若不是第一个拼版,则需等待配置的等待时间
            if not is_first_board and board_delay > 0:
                self.log.info(f"等待 {board_delay} 秒后生成下一个拼版文件")
                time.sleep(board_delay)
            else:
                is_first_board = False

            board_sn = board_entity.barcode  # 条码
            final_result = board_entity.get_final_result("OK", "OK", "NG")

            file_name = f"{board_sn}_{cur_time}.txt"
            file_path = os.path.join(save_path, file_name)

            file_content = file_test_data.format(**{
                "equipment_model": equipment_model,
                "cur_time": cur_time,
                "pro_line_num": pro_line_num,
                "machine_id": machine_id,
                "thoroughfare_id": thoroughfare_id,
                "board_sn": board_sn,
                "final_result": final_result
            })

            # comp_entity是元器件信息
            test_data_index = 1
            for comp_entity in board_entity.yield_comp_entity():
                actual_data = ""
                min_data = ""
                max_data = ""

                for alg in comp_entity.yield_alg_entity():
                    min_data = alg.min_threshold
                    max_data = alg.max_threshold
                    actual_data = alg.test_val
                    break

                final_result = comp_entity.get_final_result("OK", "OK", "NG")
                if final_result == "NG":
                    test_data = [
                        comp_entity.robot_ng_str,
                        min_data,
                        max_data,
                        actual_data,
                        comp_entity.get_final_result("OK", "OK", "NG")
                    ]
                    file_content += f"TestDate{test_data_index}={','.join(map(str, test_data))}\n"
                    test_data_index += 1

            xutil.FileUtil.write_content_to_file(file_path, file_content)

        return self.x_response()
