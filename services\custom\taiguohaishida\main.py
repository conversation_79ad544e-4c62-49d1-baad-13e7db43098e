# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/04/15 11:49
# Author     ：chencb
# version    ：python 3.8
# Description：泰国海仕达 https://jira.cvte.com/browse/ATAOI_2019-38735
"""
import json
import socket
from typing import Any
from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "taiguohaishida release v1.0.0.7",
        "device": "AIS20X, AIS30X, AIS40X",
        "feature": ["条码校验，发送数据"],
        "author": "chenchongbing",
        "release": """
date: 2025-04-15 15:40  jira->38735: 条码校验，发送数据
date: 2025-04-15 21:20  jira->ATAOI_2019-38735: 增加条码获取功能，按扫到的拼板编号no(xxxx_no)进行前后顺序补录
date: 2025-04-16 16:15  jira->ATAOI_2019-38735: 根据新的协议文档更新发送参数、字段以及返回响应
date: 2025-04-16 18:00  条码校验和发送数据需要使用multipart/form-data发送格式，json数据需改成字符串
date: 2025-04-16 19:50  更改条码获取功能拼接规则，直接获取条码前缀结合拼板数量直接生成prefix_1~prefix_n
date: 2025-04-17 16:10  两个参数key值去掉ed：confirmedResult、和confirmedResultCode
date: 2025-04-17 17:39  增加网络超时时间配置
""", }

    form = {
        "work_station": {
            "ui_name": "工作中心",
            "ui_name_en": "Work Station",
            "value": "",
        },
        "EMP": {
            "ui_name": "操作员工号",
            "ui_name_en": "EMP",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "ui_name_en": "Operator",
            "value": "",
        },
        "programmer": {
            "ui_name": "编程员",
            "ui_name_en": "Programmer",
            "value": "",
        },
        "brand_type": {
            "ui_name": "设备品牌",
            "ui_name_en": "Brand Type",
            "value": "",
        },
        "machine_name": {
            "ui_name": "机器名称",
            "ui_name_en": "Machine Name",
            "value": "",
        },
        "model_name": {
            "ui_name": "机种名称",
            "ui_name_en": "Model Name",
            "value": "",
        },
        "customer_name": {
            "ui_name": "客户名",
            "ui_name_en": "Customer Name",
            "value": "",
        },
        "check_url": {
            "ui_name": "条码校验地址",
            "ui_name_en": "Sn Check URL",
            "value": "",
        },
        "data_url": {
            "ui_name": "发送数据地址",
            "ui_name_en": "Send Data URL",
            "value": "",
        },
        "timeout": {
            "ui_name": "网络超时时间",
            "ui_name_en": "Timeout",
            "value": "10",
            "is_int": True
        },
    }

    combo = {
        "side": {
            "ui_name": "板面",
            "ui_name_en": "Side",
            "item": [
                "T",
                "B"
            ],
            "value": "T",
        }
    }

    other_form = {
        "libraryModel": {
            "ui_name": "libraryModel",
            "ui_name_en": "libraryModel",
            "value": ""
        },
    }

    def __init__(self):
        # 默认为英文版
        self.set_lang_to_en()

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        check_url = other_vo.get_value_by_cons_key("check_url", not_null=True)
        work_station = other_vo.get_value_by_cons_key("work_station")
        timeout = other_vo.get_value_by_cons_key("timeout")
        timeout = int(timeout) if timeout else 10

        sn_list = other_vo.list_sn()
        sn_count = len(sn_list)
        if sn_count == 1:
            sn_val = sn_list[0]
        else:
            sn_list_dict = {"snList": sn_list}
            sn_val = json.dumps(sn_list_dict)

        param = {
            "workstation": work_station,
            "mainSN": sn_val,
        }
        try:
            ret = xrequest.RequestUtil.post_form(check_url, param, to_json=False, timeout=timeout)
            # 返回OK或ok即为过站成功，NG或ng，则过站失败
            if 'NG' in ret or 'ng' in ret:
                return self.x_response("false", f"条码校验失败，error：{ret}")
            else:
                return self.x_response()

        except Exception as e:
            return self.x_response("false", f"本地网络异常，error：{e}")

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        scan_sn = other_vo.list_sn()
        if not scan_sn:
            return self.x_response("false", "未扫到任何条码，无法补录！")
        elif '_' not in scan_sn:
            return self.x_response("false", "条码中没有'_'，无法完成补录！")

        barcode_map = other_vo.get_barcode_map()
        if not barcode_map:
            return self.x_response("false", f"未获取到barcodeList参数，无法获取拼板数量，需要升级主软件！")

        self.log.info(f"补全前的条码列表：{scan_sn}")

        if "-2" in barcode_map:
            del barcode_map["-2"]
        if "-1" in barcode_map:
            del barcode_map["-1"]
        self.log.info(f"barcode map: {barcode_map}")
        board_cnt = len(barcode_map)
        # 条码格式：P0320408712009819_4，为了避免可能多个_，使用从右向左分割一次
        prefix = scan_sn.rsplit('_', 1)[0]
        new_sn_list = [f'{prefix}_{i}' for i in range(1, board_cnt + 1)]
        self.log.info(f"补全后的条码列表：{new_sn_list}")
        return self.x_response("true", ",".join(new_sn_list))

    def _get_local_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            ip = s.getsockname()[0]
        except Exception:
            ip = ""
        finally:
            s.close()
        return ip

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_url = data_vo.get_value_by_cons_key("data_url", not_null=True)
        work_station = data_vo.get_value_by_cons_key("work_station")
        emp = data_vo.get_value_by_cons_key("EMP")
        brand_type = data_vo.get_value_by_cons_key("brand_type")
        model_name = data_vo.get_value_by_cons_key("model_name")
        side = data_vo.get_value_by_cons_key("side")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        customer_name = data_vo.get_value_by_cons_key("customer_name")
        operator = data_vo.get_value_by_cons_key("operator")
        programmer = data_vo.get_value_by_cons_key("programmer")
        library_model = data_vo.get_value_by_cons_key("libraryModel")
        timeout = data_vo.get_value_by_cons_key("timeout")
        timeout = int(timeout) if timeout else 10

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_date = pcb_entity.get_start_time().strftime(xcons.FMT_DATE1)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME)
        cycle_time = pcb_entity.get_cycle_time()

        ip_addr = self._get_local_ip()

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            detail_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data = {
                    "componentName": comp_entity.type,
                    "mainSN": pcb_entity.pcb_barcode,
                    "panelSN": board_entity.barcode,
                    "panelID": board_entity.board_no,
                    "libraryModel": library_model,
                    "pN": comp_entity.part,
                    "pack": comp_entity.package,
                    "angle": "",
                    "reportResult": comp_entity.get_final_result("P", "F", "F"),
                    "reportResultCode": comp_entity.robot_ng_code,
                    "confirmResult": comp_entity.get_final_result("P", "P", "F"),
                    "confirmResultCode": comp_entity.repair_ng_code,
                    "img_Path": comp_entity.image_path
                }
                detail_list.append(comp_data)

            board_data = {
                "brandType": brand_type,
                "mainSN": pcb_entity.pcb_barcode,
                "panelSN": board_entity.barcode,
                "panelID": board_entity.board_no,
                "modelName": model_name,
                "side": side,
                "machineName": machine_name,
                "customerName": customer_name,
                "operator": operator,
                "programmer": programmer,
                "inspectionDate": test_date,
                "beginTime": start_time,
                "endTime": end_time,
                "cycleTimeSec": cycle_time,
                "inspectionBatch": "",
                "reportResult": board_entity.get_robot_result("P", "F"),
                "confirmedResult": board_entity.get_repair_result("P", "F"),
                "totalComponent": board_entity.comp_total_number,
                "reportFailComponent": board_entity.comp_robot_ng_number,
                "addressIP": ip_addr,
                "confirmedFailComponent": board_entity.comp_repair_ng_number,
                "list_Detail": detail_list
            }

            board_param = {
                "workstation": work_station,
                "emp": emp,
                "jsonData": json.dumps(board_data)
            }
            try:
                ret = xrequest.RequestUtil.post_form(data_url, board_param, to_json=False, timeout=timeout)
                # 返回OK或ok即为过站成功，⾮OK或ok，则过站失败
                if 'NG' in ret or 'ng' in ret:
                    err_msg_list.append(f"发送数据结果返回失败，error：{ret}")
            except Exception as e:
                err_msg_list.append(f"本地网络异常，error：{e}")

        if err_msg_list:
            return self.x_response("false", '\n'.join(err_msg_list))
        else:
            return self.x_response()
