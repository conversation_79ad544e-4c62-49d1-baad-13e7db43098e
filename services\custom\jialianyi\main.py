# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/18 下午5:05
# Author     ：sch
# version    ：python 3.8
# Description：嘉联益
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


data_template = """project name:{project_name}
inspect time:{inspect_time}
line name:{line_name}
barcode:{barcode}
inspect result:{board_result}
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "jialianyi release v1.0.0.5",
        "device": "401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-19 09:50  生成txt文档 
date: 2024-03-19 14:45  需求变更 
date: 2024-03-19 15:25  换成window换行符,无条码格式修改
date: 2024-03-25 11:30  修改文件命名的时间格式
date: 2024-03-25 12:23  修改文件命名的时间格式 bugfix
""",
    }

    path = {
        "save_path1": {
            "ui_name": "保存路径(1轨)",
            "value": "",
        },
        "save_path2": {
            "ui_name": "保存路径(2轨)",
            "value": "",
        },
    }

    form = {
        "line_code1": {
            "ui_name": "线体号(1轨)",
            "value": "",
        },
        "line_code2": {
            "ui_name": "线体号(2轨)",
            "value": "",
        },
    }

    combo = {
        "is_save_file1": {
            "ui_name": "是否保存文件(1轨)",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "is_save_file2": {
            "ui_name": "是否保存文件(2轨)",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path1 = data_vo.get_value_by_cons_key("save_path1")
        save_path2 = data_vo.get_value_by_cons_key("save_path2")
        line_code1 = data_vo.get_value_by_cons_key("line_code1")
        line_code2 = data_vo.get_value_by_cons_key("line_code2")
        is_save_file1 = data_vo.get_value_by_cons_key("is_save_file1")
        is_save_file2 = data_vo.get_value_by_cons_key("is_save_file2")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if pcb_entity.all_barcode:
            the_first_barcode = pcb_entity.all_barcode[0]
        else:
            the_first_barcode = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        self.log.info(f"the first barcode: {the_first_barcode}")

        if pcb_entity.track_index == 2:
            save_path = save_path2
            is_save_file = is_save_file2
            line_code = line_code2
        else:
            save_path = save_path1
            is_save_file = is_save_file1
            line_code = line_code1

        if is_save_file == 'No':
            self.log.warning(f"根据界面设置，此轨道无需保存txt文档！")
            return self.x_response()

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        test_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1)
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no

            if pcb_entity.all_barcode:
                barcode = the_first_barcode
            else:
                barcode = test_time1

            data_content = data_template.format(**{
                "project_name": pcb_entity.project_name,
                "inspect_time": test_time,
                "line_name": line_code,
                "barcode": barcode,
                "board_result": board_entity.get_final_result("OK", "OK", "NG", "SKIP"),
            })

            filepath = f"{save_path}/{the_first_barcode}_{board_no.zfill(3)}.txt"

            data_content = data_content.replace("\n", '\r\n')
            xutil.FileUtil.write_content_to_file(filepath, data_content)

        return self.x_response()
