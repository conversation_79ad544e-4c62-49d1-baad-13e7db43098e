# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : byd_socket_server.py
# Time       ：2025/5/16 上午9:36
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import socket
import threading
import traceback
from datetime import datetime

from PyQt5.QtCore import QThread

from common import xcons, xrequest, xutil
from common.xutil import log
from templates.main_window import Ui_MesMainWindow


class SockerServerThread(QThread):
    def __init__(
            self,
            main_window: Ui_MesMainWindow,
            run_port: int,
            machine_id: str,
            station_name: str,
            line_id: str,
            **kwargs
    ):
        """
        main_window: 主窗口
        run_port: socket 服务运行端口
        """
        super(SockerServerThread, self).__init__()
        self.main_window = main_window
        self.port = run_port
        self.machine_id = machine_id
        self.station_name = station_name
        self.line_id = line_id
        self.kwargs = kwargs

        self.sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        # TCP释放连接后实现端口的立即复用
        self.sk.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    def log_info(self, msg: str, status=True):
        self.main_window.log_info(msg, status)  # noqa

    @staticmethod
    def _get_message_id() -> str:
        """
        获取消息ID
        :return:
        """
        return datetime.now().strftime(xcons.FMT_TIME_FILE1)[:-3]

    def run(self):
        self.sk.bind(("0.0.0.0", self.port))
        # 开始监听连接，设置最大连接数为 5
        self.sk.listen(5)
        self.log_info(f"切换程序服务已运行在端口：{self.port}")

        try:
            while True:
                conn, addr = self.sk.accept()

                # 为每个客户端创建一个新的线程来处理连接
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(conn, addr)
                )
                client_thread.start()
        except KeyboardInterrupt:
            log.warning("byd服务器正在关闭...")

        finally:
            # 关闭服务器 socket
            self.sk.close()
            log.warning("byd服务器已关闭")

    def handle_client(self, conn, addr):
        """
        处理单个客户端的连接。

        Args:
            conn: 客户端的 socket 连接对象。
            addr: 客户端的地址信息（IP地址和端口）。
        """
        self.log_info(f"已连接客户端: {addr}")

        try:
            while True:
                # 接收客户端数据
                data = conn.recv(1024 * 8)
                receive_data = data.decode("utf-8").strip()

                self.log_info(f"接收到客户端数据：{receive_data}")

                if not data:
                    # 如果没有接收到数据，表示客户端已断开连接
                    break

                # 将接收到的数据解码为字符串
                self.handle_receive_data(
                    conn,
                    receive_data
                )

        except Exception as e:
            self.log_info(f"处理客户端 {addr} 时发生错误: {e}", False)
            log.warning(f"error: {traceback.format_exc()}")

        finally:
            # 关闭连接
            self.log_info(f"断开与客户端 {addr} 的连接")
            conn.close()

    def handle_receive_data(self, conn, receive_data: str):
        json_data = json.loads(receive_data)  # 主软件传过来的数据

        cmd = json_data.get("cmd")
        try:
            if cmd == "afterConnected":
                # 主控系统连接上运控设备软件后主动发送
                data = {
                    "cmd": "afterConnected",
                    "messageId": self._get_message_id(),
                    "machineID": self.machine_id,
                    "stationName": self.station_name,
                    "lineID": self.line_id,
                    "enableFun": 1,
                    "result": "OK",
                    "errMsg": "",
                    "attr": ""
                }

            elif cmd == "onChangeProgram":
                # 主控系统发送一键换型指令给运控软件
                # 1. 拿到条码之后先去获取料号，看一下根据条码获取到的料号和上一次料号是否相同
                # 不相同的话，根据料号去mes获取程序名回来
                # v2_check_project_url = self.kwargs.get("v2_check_project_url")
                v2_check_project_url2 = self.kwargs.get("v2_check_project_url2")
                v2_station = self.kwargs.get("v2_station")

                barcode = json_data.get("sn", "")

                self.log_info(f"接收到的条码是：{barcode}，正在获取板式名...")

                ret_str = xrequest.RequestUtil.get(
                    v2_check_project_url2,
                    {
                        "SN": barcode,
                        "processName": v2_station
                    }, to_json=False
                )

                ret_str2 = xutil.XmlUtil.get_xml_root_by_str(ret_str).text

                ret_json = json.loads(ret_str2)

                result2 = "OK"
                ret_msg2 = ""

                if ret_json.get("Result") != "PASS":
                    result2 = "NG"
                    ret_msg2 = f"mes接口异常，获取程序名失败，error：{ret_json.get('Message')}"
                else:
                    ret_list = ret_json.get("DATA", [])

                    program_name = ""

                    for ret_obj in ret_list:
                        if "softWareVersion" in ret_obj:
                            program_name = ret_obj.get("softWareVersion", "").replace(".rar", "")
                            break

                    self.log_info(f"从mes获取到的程序名是：{program_name}")

                    # 3. 使用程序名取调用主软件的 主动换程序接口
                    ret3 = xrequest.SocketUtil.api_check_project(
                        program_name,
                        timeout=60
                    )

                    if ret3.get("ErrorCodeV2") != "0":
                        result2 = "NG"
                        ret_msg2 = f"主软件接口异常，切换板式失败，error：{ret3.get('ErrorMessageV2')}"

                data = {
                    "cmd": "onChangeProgram",
                    "messageId": self._get_message_id(),
                    "machineID": self.machine_id,
                    "stationName": self.station_name,
                    "lineID": self.line_id,
                    "result": result2,
                    "errMsg": ret_msg2,  # 设备切换失败具体原因
                    "attr": ""
                }

                if ret_msg2:
                    self.log_info(ret_msg2, False)

            else:
                log.warning(f"未知指令：{cmd}")
                data = {
                    "result": "OK",
                    "errMsg": "",
                    "attr": ""
                }

            ret_data = json.dumps(data, ensure_ascii=False) + "\r\n"
            log.info(f"响应客户端信息: {ret_data}")

            ret_data1 = ret_data.encode("utf-8")
            conn.send(ret_data1)
        except Exception as err2:
            self.log_info(f"切换异常，error：{err2}", False)
            log.warning(f"error: {traceback.format_exc()}")
            data1 = json.dumps({
                "cmd": "onChangeProgram",
                "messageId": self._get_message_id(),
                "machineID": self.machine_id,
                "stationName": self.station_name,
                "lineID": self.line_id,
                "result": "NG",
                "errMsg": f"切换异常，error：{err2}",
                "attr": ""
            }, ensure_ascii=False) + "\r\n"
            log.info(f"响应客户端信息: {data1}")

            data2 = data1.encode("utf-8")

            conn.send(data2)

# if __name__ == '__main__':
#     server_thread = SockerServerThread(None, 8888)
#     server_thread.start()

# while 1:
#     print("1")
#     time.sleep(2)
