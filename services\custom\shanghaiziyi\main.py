# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/3 下午2:29
# Author     ：sch
# version    ：python 3.8
# Description：上海梓一
"""
import os
from typing import Any

from PIL import Image

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

csv_board_board_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
整板器件总数,{pcb_comp_number}
拼板序号,{board_no}
拼板条码,{board_sn}
拼板检测结果,{board_robot_result}
拼板复判结果,{board_user_result}
拼板最终结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件检测NG总数,{board_comp_robot_ng_number}
拼板器件复判NG总数,{board_comp_user_ng_number}
拼板器件误报总数,{board_comp_repass_number}
整板图路径,{pcb_image_path}

器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,下限,上限,标准值,实测值,器件图片路径{CompData}
"""

csv_comp_board_template = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{min_val},{max_val},{std_val},{test_val},{comp_image}"""

get_sn_template = """<Cluster>
    <Name>123</Name>
    <NumElts>1</NumElts>
    <String>
        <Name>ClampNO</Name>
        <Val>{pcb_sn}</Val>
    </String>
</Cluster>"""

checkout_template = """<Cluster>
    <Name>123</Name>
    <NumElts>{tag_number}</NumElts>
    <String>
        <Name>ClampNO</Name>
        <Val>{pcb_sn}</Val>
    </String>{board_data_str}
    <Array>
        <Name>InspectInfo</Name>
        <Dimsize>{comp_number}</Dimsize>{comp_data_api}
    </Array>
</Cluster>"""

board_template = """
    <Boolean>
        <Name>Result{board_no}</Name>
        <Val>{result}</Val>
    </Boolean>"""

comp_data_template = """
        <Cluster>
            <Name></Name>
            <NumElts>4</NumElts>
            <String>
                <Name>PointNO</Name>
                <Val>{comp_tag}</Val>
            </String>
            <String>
                <Name>Area</Name>
                <Val>{test_val}</Val>
            </String>
            <String>
                <Name>Height</Name>
                <Val></Val>
            </String>
            <String>
                <Name>Result</Name>
                <Val>{result}</Val>
            </String>
        </Cluster>"""

get_sn_data_filter_db = xutil.CircularList(200)


class Engine(ErrorMapEngine):
    version = {
        "title": "shanghaiziyi release v1.0.0.17",
        "device": "401",
        "feature": ["从MES获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-03 15:40  从MES获取条码，上传数据
date: 2024-09-03 18:07  增加解析接口参数  
date: 2024-09-03 20:03  bugfix
date: 2024-09-03 20:48  bugfix
date: 2024-09-05 19:23  需求变更
date: 2024-09-05 21:08  需求变更+1
date: 2024-09-06 09:15  修复: `Dimsize`为器件数组大小
date: 2024-09-06 17:55  需求变更：更改 文件/图片 的命名规则  +  获取条码失败不需要发送数据
date: 2024-09-07 10:29  增加配置项：csv器件输出/是否启用ftp上传
date: 2024-09-07 15:59  ClampNO --> pcb_sn
date: 2024-09-07 17:34  add comp result
date: 2024-09-07 20:23  update param
date: 2024-09-08 13:42  use lightSource1 image
date: 2024-09-09 15:41  增加接口超时配置项
date: 2024-10-12 19:47  update param
date: 2024-11-04 12:06  20241017需求变更
date: 2024-12-25 14:34  [bugfix] 是否启用Mes交互：选择No时，无需跟mes做交互
""",
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "AOI001",
        }
    }

    path = {
        "csv_path": {
            "ui_name": "CSV文档保存路径",
            "value": "",
        },
        "img_path": {
            "ui_name": "图片保存路径",
            "value": "",
        },
    }

    combo = {
        "board_face": {
            "ui_name": "面别",
            "item": ["F", "B"],
            "value": "F",
        }
    }

    other_form = {
        "api_url1": {
            "ui_name": "接口URL(入站/获取条码)",
            "value": "",
        },
        "api_url2": {
            "ui_name": "接口URL(出站)",
            "value": "",
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    other_combo = {
        "ng_upload_type": {
            "ui_name": "csv器件输出",
            "item": ["全部", "仅检测NG", "仅复判NG"],
            "value": "仅检测NG"
        },
        "is_upload_ftp": {
            "ui_name": "是否启用ftp上传",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
        "is_upload_mes": {
            "ui_name": "是否启用Mes交互",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
        "light_src": {
            "ui_name": "光源",
            "item": ["0", "1"],
            "value": "1"
        },
        "api_timeout": {
            "ui_name": "接口响应等待时间(s)",
            "item": ["1", "3", "5", "6", "7", "8", "9", "10", "15", "20", "30", "50", "100"],
            "value": "5"
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        is_upload_mes = other_vo.get_value_by_cons_key("is_upload_mes")
        api_url1 = other_vo.get_value_by_cons_key("api_url1")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout", to_int=True)
        pcb_sn = other_vo.get_pcb_sn()

        if is_upload_mes == "No":
            barcode_map = other_vo.get_barcode_map()
            del barcode_map["-2"]
            del barcode_map["-1"]

            sn_list = list(barcode_map.values())

            self.log.warning(f"无需跟mes做交互！")

            return self.x_response("true", f",".join(sn_list))

        try:
            req_param = get_sn_template.format(**{
                "pcb_sn": pcb_sn
            })

            ret_str = xrequest.RequestUtil.post_xml(api_url1, req_param, timeout=api_timeout)

            ret_sn_list = []

            root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
            sn_array = root.find("Cluster").find("Array").findall("String")

            for item in sn_array:
                i1 = item.find("Val")
                v1 = i1.text
                if v1:
                    ret_sn_list.append(v1)

        except Exception as err:
            get_sn_data_filter_db.add_item(pcb_sn)
            return self.x_response("false", f"解析响应参数失败，未获取到条码！err: {err}")

        if not ret_sn_list:
            get_sn_data_filter_db.add_item(pcb_sn)
            return self.x_response("false", f"MES响应异常，未获取到条码！")

        get_sn_data_filter_db.remove_item(pcb_sn)

        return self.x_response("true", ",".join(ret_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        csv_path = data_vo.get_value_by_cons_key("csv_path", not_null=True)
        img_path = data_vo.get_value_by_cons_key("img_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        board_face_ui = data_vo.get_value_by_cons_key("board_face")
        api_url2 = data_vo.get_value_by_cons_key("api_url2")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        ng_upload_type = data_vo.get_value_by_cons_key("ng_upload_type")
        is_upload_ftp = data_vo.get_value_by_cons_key("is_upload_ftp")
        light_src = data_vo.get_value_by_cons_key("light_src")

        is_upload_mes = data_vo.get_value_by_cons_key("is_upload_mes")

        api_timeout = data_vo.get_value_by_cons_key("api_timeout", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        if get_sn_data_filter_db.is_exist_item(pcb_sn):
            self.log.warning(f"本次发送跳过，如果获取不到拼板条码，不用再生成CSV文件、保存图片和请求过站接口!")
            return self.x_response()

        last_sn = pcb_sn[-1:]
        self.log.info(f"last_sn: {last_sn}")

        if last_sn == "F":
            board_face = "F"
        elif last_sn == "B":
            board_face = "B"
        else:
            self.log.warning(f"治具条码最后一位不是F/B，使用界面配置的板面!")
            board_face = board_face_ui

        self.log.info(f"board side: {board_face}")

        pcb_src_image = pcb_entity.get_unknown_t_pcb_image()
        data_path = pcb_entity.get_pcb_pcb_t_review_path()

        pcb_src_image2 = f"{data_path}/thumbnail/{light_src}/thumb.jpg"

        if os.path.exists(pcb_src_image2):
            pcb_src_image = pcb_src_image2

        self.log.info(f"{pcb_src_image=}")

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        time1 = time_file[:8]

        csv_save_path = f"{csv_path}/{time1}"
        img_full_path = f"{img_path}/{board_face}/{time1}"
        xutil.FileUtil.ensure_dir_exist(csv_save_path)
        xutil.FileUtil.ensure_dir_exist(img_full_path)

        if is_upload_ftp == "Yes":
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()
        else:
            ftp_client = None

        # ftp_client.upload_file(pcb_src_image, f"{pcb_sn}_{board_face}_{time_file}.jpg")

        # 3. 保存拼板图
        box_pos_map = pcb_entity.get_board_box_position()
        pcb_image_obj = Image.open(pcb_src_image)

        board_data_str = ""

        comp_data_api = ""
        arr_number = 0

        tag_count = 2

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            ftp_full_path = f"{ftp_path}/{board_face}/{time1}/{barcode}"

            if ftp_client:
                ftp_client.cd_or_mkdir(ftp_full_path)

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                ftp_dst_filepath = ""

                if comp_entity.is_robot_ng():
                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        comp_filename = f"{barcode}_{board_face}_{time_file}_{comp_tag}.png"
                        comp_dst_img = f"{img_full_path}/{comp_filename}"
                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

                        if ftp_client:
                            ftp_dst_filename = f"{barcode}_{comp_tag}_{comp_entity.robot_ng_str}.png"
                            ftp_client.upload_file(comp_src_img, ftp_dst_filename)

                            ftp_dst_filepath = f"{ftp_full_path}/{ftp_dst_filename}"

                comp_final_result = comp_entity.get_final_result()

                for alg_entity in comp_entity.yield_alg_entity():

                    if (ng_upload_type == "全部") or (
                            ng_upload_type == "仅检测NG" and comp_final_result not in ["PASS"]
                    ) or (
                            ng_upload_type == "仅复判NG" and comp_final_result not in ["PASS", "REPASS"]
                    ):
                        comp_data_str += csv_comp_board_template.format(**{
                            "comp_designator": comp_tag,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": ftp_dst_filepath,
                            "max_val": alg_entity.max_threshold,
                            "min_val": alg_entity.min_threshold,
                            "std_val": f"{alg_entity.min_threshold}~{alg_entity.max_threshold}",
                            "test_val": alg_entity.test_val,
                        })

                    mark_flag = ""

                    if comp_entity.type == "Marker":
                        mark_flag = f"_mark"

                    comp_data_api += comp_data_template.format(**{
                        "comp_number": pcb_entity.comp_count,
                        "comp_tag": f"{board_no}_{comp_entity.designator}{mark_flag}",
                        "test_val": alg_entity.test_val,
                        "result": comp_entity.get_final_result("OK", "OK", "NG")
                    })

                    arr_number += 1

            # 保存拼板图
            box = box_pos_map.get(board_no)
            cropped_image = pcb_image_obj.crop(box)

            board_dst_img = f"{img_full_path}/{barcode}_{board_no}_{time_file}.jpg"
            cropped_image.save(board_dst_img)
            self.log.info(f"拼板{board_no}拼板图保存成功！")

            ftp_board_dst_filepath = ""
            if ftp_client:
                ftp_client.upload_file(board_dst_img, f"{barcode}.jpg")
                ftp_board_dst_filepath = f"{ftp_full_path}/{barcode}.jpg"

            board_data_fmt = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "pcb_image_path": ftp_board_dst_filepath,
                "CompData": comp_data_str
            }

            board_content = csv_board_board_template.format(**board_data_fmt)
            xutil.FileUtil.write_content_to_file(
                f"{csv_save_path}/{barcode}_{board_face}_{time_file}.csv",
                board_content
            )

            board_data_str += board_template.format(**{
                "board_no": board_no,
                "result": board_entity.get_repair_result("1", "0"),
            })

            tag_count += 1

        if ftp_client:
            ftp_client.close()

        checkout_xml = checkout_template.format(**{
            "board_data_str": board_data_str,
            "comp_data_api": comp_data_api,
            "comp_number": arr_number,
            "pcb_sn": pcb_sn,
            "tag_number": tag_count
        })

        if is_upload_mes == "No":
            ret_str2 = xrequest.RequestUtil.post_xml(
                api_url2,
                checkout_xml,
                timeout=api_timeout
            )

            try:
                root2 = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
                status = root2.find("I32").find("Val").text
                msg = root2.find("String").find("Val").text
                if status != "200":
                    return self.x_response("false", f"接口响应异常，error：{msg}")

            except Exception as err:
                return self.x_response("false", f"解析响应参数失败, err: {err}")

        return self.x_response()
