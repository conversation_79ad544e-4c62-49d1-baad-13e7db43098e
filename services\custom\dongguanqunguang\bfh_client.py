# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : bfh_client.py
# Time       ：2025/5/13 下午17:16
# Author     ：chencb
# version    ：python 3.8
# Description：东莞群光  jira：ATAOI_2019-31867
"""
import ipaddress
import json
import socket
import threading
from common.xutil import log

# 心跳间隔5s
HEARTBEAT_INTERVAL = 5
# BFH连不上重试间隔(1分钟)以及重试次数（3次）
RECONNECT_INTERVAL = 60
MAX_RECONNECT_CNT = 3


class BFHClient:
    def __init__(self, main_window, host: str, port: str):
        self.main_window = main_window
        # 连接成功后再赋值它们
        self.host = None
        self.port = None
        self.sk = None
        self.reconnect_cnt = 0
        # 发送失败时，缓存发送的检测数据，如果重试成功后再发送
        self.cache_aoi_data = None
        # 用于是否记录心跳日志，只记录第一次的心跳连接
        self.heartbeat_log = True

        # 0.5s后再连接波峰焊，让初始化流程走完
        self.timer = threading.Timer(0.5, self.connect, args=[host, port])
        self.timer.start()

    def validate_ip_port(self, ip: str, port: str):
        # 验证IP和端口地址填写的正确性
        if ip:
            try:
                ipaddress.ip_address(ip)
            except ValueError:
                return "波峰焊连接失败，IP地址填写错误"
        else:
            return "波峰焊连接失败，波峰焊地址未填写"

        if port:
            try:
                port_num = int(port)
                if not (0 <= port_num <= 65535):
                    return "波峰焊连接失败，端口范围必须在0-65535之间"
            except ValueError:
                return "波峰焊连接失败，端口必须是整数"
        else:
            return "波峰焊连接失败，波峰焊端口未填写"

        return ''

    def connect(self, host: str, port: str, from_reconnect: bool = False):
        err_msg = self.validate_ip_port(host, port)
        if err_msg:
            self.main_window.log_info(err_msg, False, pop_prompt=False)
            return False

        if not from_reconnect:
            # 非重试时，如果是同一个地址且已连接，则直接返回
            if self.sk and ((self.host, self.port) == (host, port)):
                # 已在连接中
                self.main_window.log_info(f'波峰焊：{host}:{port}已在连接中')
                return True

        try:
            # 重新连接之前先清空之前的连接信息
            self.diconnect(from_reconnect)

            if self.reconnect_cnt > 0:
                self.main_window.log_info(f'波峰焊连接重试，重试第{self.reconnect_cnt}次 ')
            self.sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            # 默认设置5s超时
            self.sk.settimeout(5)
            self.sk.connect((host, int(port)))
            self.main_window.log_info(f'波峰焊：{host}:{port} 连接成功')
            self.host = host
            self.port = port
            self.reconnect_cnt = 0

            if self.cache_aoi_data:
                # 如果有缓存的检测数据，重新连接上后，重新发送
                self.send_aoi_data(self.cache_aoi_data)
            else:
                # 连接成功后，5s进行一次心跳数据发送
                self.start_heartbeat()
            return True

        except Exception as err:
            self.sk = None
            self.host = None
            self.port = None
            self.main_window.log_info(f"波峰焊：{host}:{port} 连接失败，error：{err}", False, pop_prompt=False)
            self.reconnect(host, port)
            return False

    def reconnect(self, host: str, port: str, interval=RECONNECT_INTERVAL):
        # 连接失败，每隔1分钟重试一次，总共重试3次
        if self.reconnect_cnt < MAX_RECONNECT_CNT:
            # 如果是发送数据失败的重新连接，则interval会为0，马上启动重连
            self.timer = threading.Timer(interval, self.connect, args=[host, port, True])
            self.timer.start()
            self.reconnect_cnt += 1
        else:
            self.main_window.log_info(f'已重试3次，断开波峰焊连接，不再重试！')
            self.timer = None
            self.reconnect_cnt = 0

    def diconnect(self, from_reconnect=False):
        if self.sk:
            self.sk.close()
            self.sk = None
            log.info(f'断开上一个波峰焊连接：{self.host}:{self.port}')
        if self.timer:
            self.timer.cancel()
            self.timer = None
        self.host = None
        self.port = None
        # 如果是重试，重试计数器不能清空
        if not from_reconnect:
            self.reconnect_cnt = 0
        self.heartbeat_log = True

    def start_heartbeat(self):
        self.timer = threading.Timer(HEARTBEAT_INTERVAL, self.send_heartbeat_data, args=[])
        self.timer.start()

    def send_heartbeat_data(self):
        if not self.sk:
            return

        ret_str = ''
        try:
            if self.heartbeat_log:
                log.info('发送心跳数据')
            heartbeat_data = {
                "eMessageType": 1,
                "ProductionData": None
            }
            heartbeat_data_str = json.dumps(heartbeat_data, ensure_ascii=False)
            self.sk.sendall(heartbeat_data_str.encode('utf-8'))
            ret_str = self.sk.recv(1024)
            if ret_str:
                ret_str = ret_str.decode('utf-8')
                if self.heartbeat_log:
                    log.info(f"心跳接口响应: {ret_str}")
                heartbeat_resp = json.loads(ret_str)
                # 格式：{"eMessageType": 1, "sReceiveData": "Sucess"}
                result = heartbeat_resp['sReceiveData']
                if 'Sucess' in result:
                    if self.heartbeat_log:
                        log.info(f"心跳成功")
                    # 发起下一次心跳
                    self.start_heartbeat()
                    # 后续的心跳就不再记录日志
                    self.heartbeat_log = False
                else:
                    self.main_window.log_info(f"波峰焊返回心跳失败，停止心跳：sReceiveData={result}", False, pop_prompt=False)
            else:
                # 返回空，表示服务主动关闭了连接，则进行重连
                self.main_window.log_info(f"波峰焊主动关闭了连接，心跳失败，进行重连……", False, pop_prompt=False)
                self.reconnect(self.host, self.port, 0)
        except json.JSONDecodeError:
            self.main_window.log_info(f'波峰焊心跳返回的json格式不正确，停止心跳：{ret_str}', False, pop_prompt=False)
        except KeyError:
            self.main_window.log_info(f'波峰焊心跳返回的json中没有sReceiveData字段，停止心跳', False, pop_prompt=False)
        except Exception as e:
            self.main_window.log_info(f'波峰焊心跳失败，网络异常：{e}', False, pop_prompt=False)
            # 心跳失败，BFH连接可能异常，马上进行重连试试
            self.reconnect(self.host, self.port, 0)

    def send_aoi_data(self, data: dict):
        """
        发送成功时，返回空字符串，发送数据失败时，返回失败信息
        """
        if not self.sk:
            return ''

        log.info("准备发送检测数据到波峰焊……")
        ret_str = ''
        ret_msg = ''
        try:
            # 先暂停心跳，等待数据发送成功后再启动
            if self.timer:
                self.heartbeat_log = True
                log.info("先暂停心跳功能，待数据发送完成后再重新启动")
                self.timer.cancel()
                self.timer = None

            log.info(f"发送数据到波峰焊: {data}")
            mes_data_str = json.dumps(data, ensure_ascii=False)
            self.sk.sendall(mes_data_str.encode('utf-8'))
            ret_str = self.sk.recv(1024)
            if ret_str:
                ret_str = ret_str.decode('utf-8')
                log.info(f"波峰焊接口响应: {ret_str}")
                mes_resp = json.loads(ret_str)
                # 格式：{"eMessageType": 1, "sReceiveData": "Sucess"}
                result = mes_resp['sReceiveData']
                if 'Sucess' not in result:
                    ret_msg = f'检测结果数据发送失败，波峰焊服务返回：sReceiveData={result}'
                else:
                    self.main_window.log_info(f'波峰焊数据发送成功！')
                # 重新启动心跳
                self.start_heartbeat()
                self.cache_aoi_data = None
            else:
                # 返回空，表示服务关闭了连接
                ret_msg = "波峰焊主动关闭了连接，开始重连……"
                self.cache_aoi_data = data
                self.reconnect(self.host, self.port, 0)
        except json.JSONDecodeError:
            ret_msg = f'波峰焊返回的json格式不正确：{ret_str}'
        except KeyError:
            ret_msg = '波峰焊返回的json中没有sReceiveData字段'
        except Exception as err:
            # 只重试一次，还是失败就不再发送
            if not self.cache_aoi_data:
                self.cache_aoi_data = data
                # 数据发送失败，BFH连接可能异常，马上进行重连试试
                self.reconnect(self.host, self.port, 0)
            else:
                self.cache_aoi_data = None
            ret_msg = f"波峰焊发送检测数据失败，网络异常：{err}，开始重连……"

        if ret_msg: # 主界面提示出错信息
            self.main_window.log_info(ret_msg, False, pop_prompt=False)

        return ret_msg
