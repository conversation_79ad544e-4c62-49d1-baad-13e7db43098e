# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/22 下午3:10
# Author     ：sch
# version    ：python 3.8
# Description：汇川、匈牙利汇川联合动力431     ---> 汇川标准版: 和SPI/501版本90%相似
"""
from datetime import datetime
from typing import Any

from common import xcons, xutil, xrequest
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

txt_template = """MODEL:{project_name}
Inspect DateTime:{test_time}
ConfirmTime:{confirm_time}
User:{username}
Barcode:{barcode}
StationID:{station_id}
PART:{part}
status:{status}
{board_data_str}"""


# board_template = """
# Board NO:{board_no};Result:{status};{comp_data_str}"""


def format_board_data2(board_no, status, comp_data_list, max_line_length=99):
    """
    格式化板数据字符串，确保每行不超过指定的最大长度，并且每行都以固定的板信息开头和分号结尾。

    参数:
    - board_no (str): 板编号。
    - status (str): 检测结果状态（例如 "PASS", "Repass", "Repair"）。
    - comp_data_list (list of str): 组件数据列表，每个元素代表一个组件的信息。
    - max_line_length (int, optional): 每行的最大字符数，默认为100。

    返回:
    - str: 格式化后的板数据字符串，其中每一行都包含板信息并以分号结尾。
    """

    # 创建固定部分的模板字符串，它将出现在每一行的开始
    fixed_part = f"Board NO:{board_no};Result:{status};"
    fixed_part_length = len(fixed_part)

    # 可用空间为最大行长度减去固定部分的长度，再减去一个字符给分号
    available_space = max_line_length - fixed_part_length - 1

    # 存储分割后的组件数据部分
    lines = []
    current_line = ""

    if not comp_data_list:
        return f"{fixed_part}\n"  # 如果没有组件数据，则直接返回固定部分

    # 遍历组件数据列表，确保每行不超过可用空间
    for item in comp_data_list:
        # 如果加上当前item后超过可用空间，则开始新行（已考虑分号和空格）
        if len(current_line) + len(item) + 2 > available_space:
            lines.append(current_line + ";")
            current_line = item
        else:
            if current_line:
                current_line += ";" + item
            else:
                current_line = item

    # 确保最后一行以分号结尾
    if current_line:
        lines.append(current_line + ";")

    # 在每行前添加固定部分，并检查是否超过最大长度
    formatted_lines = []
    for line in lines:
        full_line = fixed_part + line

        # 如果超过最大长度，则找到最近的一个分号位置进行分割
        while len(full_line) > max_line_length:
            split_pos = full_line.rfind(';', 0, max_line_length)
            if split_pos == -1 or split_pos < fixed_part_length:
                # 如果没有找到合适位置，则强制在最大长度处分割
                split_pos = max_line_length - 1

            # 添加分割后的前半部分到结果列表，并处理后半部分

            new_line = full_line[:split_pos + 1]

            if not new_line.endswith(';'):
                new_line += ';'

            formatted_lines.append(new_line)  # 包含分号
            full_line = fixed_part + full_line[split_pos + 1:]

        # 确保最后一行也以分号结尾
        if not full_line.endswith(';'):
            full_line += ';'

        # 添加最后一行（已经确保不会超出长度）
        formatted_lines.append(full_line)

    # 返回格式化后的多行字符串
    ret_msg = '\n'.join(formatted_lines)
    return f"{ret_msg}\n"


class Engine(ErrorMapEngine):
    version = {
        "title": "huichuan431 release v1.0.0.19",
        "device": "431",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-22 16:26  生成本地文档
date: 2024-01-05 11:23  需求变更
date: 2024-01-05 16:00  MODEL字段增加输出T/B
date: 2024-01-09 15:05  linux换行符改成window换行符
date: 2024-01-09 17:31  文档格式修改
date: 2024-01-24 14:39  保存整板图
date: 2024-01-25 11:20  整板图命名，兼容分开发送场景
date: 2024-09-26 10:23  改成整板发送
date: 2024-12-30 17:47  线别改为输入框输入+没拼版条码时要输出拼板序号
date: 2025-01-08 12:11  每一行的数据不能超过100字符，以及增加文件编码
date: 2025-01-13 10:38  board_side改为T面和B面
date: 2025-01-14 15:22  兼容431数据结构
date: 2025-02-25 09:09  txt内容第一行去除板面，板编号如果是0的话改为1
date: 2025-02-26 16:51  增加Board No生成逻辑
date: 2025-02-27 11:04  bugfix:pass没有生成数据，不同board no之间没有换行
date: 2025-03-04 15:06  jira->33234: 需求变更
date: 2025-03-15 10:07  增加条码校验功能
date: 2025-03-15 11:47  bugfix: 修复431拷贝整板图异常
date: 2025-03-17 17:06  jira:33234,条码校验增加接口数据
""", }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "ui_name_en": "SavePath",
            "value": "",
        },
        "save_path_img": {
            "ui_name": "整板图保存路径",
            "ui_name_en": "ImageSavePath",
            "value": "",
        },
    }

    form = {
        "username": {
            "ui_name": "作业员工号",
            "ui_name_en": "UserName",
            "value": "",
        },
        "line_v2": {
            "ui_name": "线别",
            "ui_name_en": "Line",
            "value": "SMT",
        },
        "workStationCode": {
            "ui_name": "工位编号",
            "ui_name_en": "WorkStationCode",
            "value": "",
        },
        "operationCode": {
            "ui_name": "工序编号",
            "ui_name_en": "OperationCode",
            "value": "",
        }
    }

    other_form = {
        "utf_encoding": {
            "ui_name": "文件编码",
            "ui_name_en": "FileEncoding",
            "value": "utf-8",
        },
        "api_url_check": {
            "ui_name": "AOI过站查询接口",
            "ui_name_en": "AOICheck",
            "value": "http://127.0.0.1:8081/api/checkBarcode",
        },
    }

    combo = {
        "flag_type": {
            "ui_name": "Board No生成逻辑",
            "ui_name_en": "Board No Type",
            "item": ["SMT", "PA"],
            "value": "SMT",
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        workstationcode = other_vo.get_value_by_cons_key("workStationCode")
        operationcode = other_vo.get_value_by_cons_key("operationCode")

        sn_list = other_vo.list_sn()

        check_param = {
            "workStationCode": workstationcode,
            "operationCode": operationcode,
            "serialNumber": sn_list
        }

        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
        if str(ret.get("code")) != "1":
            return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        username = data_vo.get_value_by_cons_key("username")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img")
        line_v2 = data_vo.get_value_by_cons_key("line_v2")
        utf_encoding = data_vo.get_value_by_cons_key("utf_encoding", not_null=True)
        flag_type = data_vo.get_value_by_cons_key("flag_type")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        project_name = pcb_entity.pcb

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        time_now = datetime.now()
        board_side = pcb_entity.board_side

        barcode_list = pcb_entity.all_barcode

        # 保存整板图
        pcb_src_img_431 = pcb_entity.get_image_path_431()

        time_date = start_time[:8]
        pcb_full_path = f"{save_path_img}/{project_name}/{time_date}"
        xutil.FileUtil.ensure_dir_exist(pcb_full_path)

        if pcb_src_img_431:
            t_pcb_filename = f"{board_side}_{start_time}_{'_'.join(barcode_list)}.jpg"
            t_dst_image = f"{pcb_full_path}/{t_pcb_filename}"
            xutil.FileUtil.copy_file(pcb_src_img_431, t_dst_image)

        only_sn = pcb_entity.pcb_barcode

        board_data_str = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_no == "0":
                board_no = "1"  # 0号板改为1号板，客户从1开始

            if not only_sn and barcode:
                only_sn = barcode

            # comp_data_list = []
            # for comp_entity in board_entity.yield_comp_entity():
            #
            #     if comp_entity.is_robot_ng():
            #         comp_tag = comp_entity.designator
            #         comp_part = comp_entity.part
            #         comp_ng_str = comp_entity.robot_ng_str
            #         result = comp_entity.get_final_result("PASS", "Repass", "Repair")
            #
            #         comp_data_list.append(f"{comp_tag},{comp_part},{comp_ng_str},{result}")

            if flag_type == "SMT":
                board_flag = board_no
            else:
                board_flag = barcode

            if not board_flag:
                board_flag = board_no

            # board_data_str += format_board_data2(
            #     board_flag,
            #     board_entity.get_final_result("PASS", "Repass", "Repair"),
            #     comp_data_list
            # )

            board_status = board_entity.get_final_result("PASS", "Repass", "Repair")
            board_data_str += f"Board NO:{board_flag};Result:{board_status};\n"

        txt_content = txt_template.format(**{
            "board_side": board_side,
            "project_name": project_name,
            "test_time": start_time,
            "confirm_time": time_now.strftime(xcons.FMT_TIME_FILE),
            "username": username,
            "barcode": only_sn,
            "station_id": line_v2,
            "part": pcb_entity.comp_count,
            "status": pcb_entity.get_final_result("PASS", "Repass", "Repair"),
            "board_data_str": board_data_str
        })

        time_file = time_now.strftime(xcons.FMT_TIME_FILE)
        filename = f"{save_path}/{only_sn}_{line_v2}_{time_file}_{board_side}.txt"

        board_content = txt_content.replace("\n", "\r\n")
        xutil.FileUtil.write_content_to_file(filename, board_content, encoding=utf_encoding)

        return self.x_response()
