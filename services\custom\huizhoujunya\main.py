# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/1/17 下午12:01
# Author     ：sch
# version    ：python 3.8
# Description：惠州骏亚  base jira: 36608
"""
import base64
import os
from typing import Any
from common import xrequest, xutil, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["惠州骏亚", "huizhoujunya"],
        "version": "release v1.0.0.5",
        "device": "AIS303",
        "feature": ["条码校验", "上传数据", "设备状态", "条码获取"],
        "author": "sunchangheng",
        "release": """
date: 2025-01-17 19:13  jira:36608  条码校验、上传数据、设备状态
date: 2025-02-11 16:33  pcbBarcode->pcbBarCode, boardBarcode->boardBarCode, 条码校验逻辑修改
date: 2025-02-22 11:30  需求变更：图片地址直接传源地址就好
date: 2025-04-23 10:20  jira->36608: 增加条码获取功能
date: 2025-06-03 11:35  jira->36608: 合并深技电子的获取token接口、工步校验接口、保存测试数据接口，并增加MES服务器类型下拉选项
""", }

    other_form = {
        "email": {
            "ui_name": "【CMES】账号",
            "value": "",
        },
        "password": {
            "ui_name": "【CMES】密码",
            "value": "",
        },
        "factory_code": {
            "ui_name": "【CMES】工厂短码",
            "value": "",
        },
        "station_code": {
            "ui_name": "【CMES】工步编号/测试工位",
            "value": "",
        },
        "work_center": {
            "ui_name": "【CMES】产线编号(WorkCenter)",
            "value": "",
        },
        "test_no": {
            "ui_name": "【CMES】测试架编号",
            "value": "",
        },
        "worker_no": {
            "ui_name": "【CMES】测试员编号",
            "value": "",
        },
        "key_info": {
            "ui_name": "【CMES】TV测试KEY信息",
            "value": "",
        },
        "test_version": {
            "ui_name": "【CMES】TV测试版本",
            "value": "",
        },
        "cmes_api_url_login": {
            "ui_name": "【CMES】接口URL(获取Token)",
            "value": "http://cmes-test.gz.cvte.cn/frontApi/mfg/api/Account/CreateToken",
        },
        "cmes_api_url_check_sn": {
            "ui_name": "【CMES】接口URL(工步校验)",
            "value": "http://cmes-test.prod.gz.cvte.cn/api/services/test/TestScan/CheckLotSnCode",
        },
        "cmes_api_url_data": {
            "ui_name": "【CMES】接口URL(上传数据)",
            "value": "http://cmes-test.prod.gz.cvte.cn/api/services/test/TestScan/SaveProduceTestByAutoTest",
        },
    }

    form = {
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "org_code": {
            "ui_name": "组织编码",
            "value": "",
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(条码获取)",
            "value": "",
        },
        "api_url_device": {
            "ui_name": "接口URL(设备状态)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
    }

    combo = {
        "mes_type": {
            "ui_name": "MES服务器类型",
            "item": [
                "MES",
                "CMES"
            ],
            "value": "MES",
        },
        "board_side2": {
            "ui_name": "板面",
            "item": [
                "A",
                "B"
            ],
            "value": "A",
        }
    }

    def __init__(self):
        self.token = ''

    def _request_token(self, mes_vo):
        cmes_api_url_login = mes_vo.get_value_by_cons_key("cmes_api_url_login", not_null=True)
        email = mes_vo.get_value_by_cons_key("email")
        password = mes_vo.get_value_by_cons_key("password")

        if not email or not password:
            return self.x_response("false", f"密码或账号为空，请先填写！")

        if "@" not in email:
            pwd_encrypt = base64.b64encode(password.encode('utf8')).decode("utf8")
        else:
            pwd_encrypt = xutil.OtherUtil.get_md5_sign(password).lower()

        login_param = {"Email": email, "Pwd": pwd_encrypt}

        try:
            ret = xrequest.RequestUtil.get(cmes_api_url_login, login_param, headers=login_param)
            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                return self.x_response("false", msg)
            else:
                self.token = ret.get("result")
                return self.x_response()
        except Exception as e:
            return self.x_response("false", f'本地网络异常：{e}')

    def _check_cmes_sn(self, other_vo: OtherVo):
        cmes_api_url_check_sn = other_vo.get_value_by_cons_key("cmes_api_url_check_sn", not_null=True)
        factory_code = other_vo.get_value_by_cons_key("factory_code")
        station_code = other_vo.get_value_by_cons_key("station_code")

        # 为了避免token失效，每次条码校验都重新获取一次
        self.token = ''
        ret = self._request_token(other_vo)
        if not ret.get('result'):
            return self.x_response("false", f"获取Token失败：{ret.get('string')}")

        header = {
            "Content-Type": "application/json",
            f"cmes_{factory_code}_info": self.token
        }

        error_msg_list = []
        try:
            for ix, sn in enumerate(other_vo.list_sn()):
                body = {
                    "lotSnCode": sn,
                    "stationCode": station_code,
                }
                ret = xrequest.RequestUtil.post_json(cmes_api_url_check_sn, body, headers=header)
                result = ret.get("result", {})
                is_success = result.get("isSuccess")
                if not is_success:
                    msg = result.get("msg")
                    error_msg_list.append(f"CEMS接口异常：条码:({ix + 1},{sn}) error:{msg}")
        except Exception as e:
            error_msg_list.append(f'本地网络异常：{e}')

        if error_msg_list:
            error_msg = "\n".join(error_msg_list)
            return self.x_response("false", error_msg)
        else:
            return self.x_response()

    def _check_mes_sn(self, other_vo: OtherVo):
        api_url_check = other_vo.get_value_by_cons_key("api_url_check", not_null=True)
        device_code = other_vo.get_value_by_cons_key("device_code")
        board_side2 = other_vo.get_value_by_cons_key("board_side2")

        track_index = other_vo.get_track_index()
        sn_list = other_vo.list_sn()

        board_sn_list = []  # 拼板条码列表
        pcb_sn = ""
        if len(sn_list) == 1:
            pcb_sn = sn_list[0]
            board_sn_list = sn_list
        elif len(sn_list) >= 2:
            pcb_sn = sn_list[0]
            board_sn_list = sn_list[1:]

        check_param = {
            "devCode": device_code,
            "side": board_side2,
            "trackId": track_index,
            "boardBarCode": pcb_sn,
            "pcbBarCodes": board_sn_list,
        }

        ret = self.x_response()
        try:
            resp = xrequest.RequestUtil.post_json(api_url_check, check_param)
            if str(resp.get("resultCode")) != "0000":
                ret = self.x_response("false", f"mes接口异常，error：{resp.get('message')}")
        except Exception as e:
            ret = self.x_response("false", f"本地网络异常，error：{e}")

        return ret

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        mes_type = other_vo.get_value_by_cons_key("mes_type")
        if mes_type == 'CMES':
            ret = self._check_cmes_sn(other_vo)
        else:
            ret = self._check_mes_sn(other_vo)
        return ret

    def _get_sn_by_cmes(self, other_vo: OtherVo):
        barcode_map = other_vo.get_barcode_map()
        if "-2" in barcode_map:
            del barcode_map["-2"]
        if "-1" in barcode_map:
            del barcode_map["-1"]
        sn_list = list(barcode_map.values())
        return self.x_response("true", ",".join(sn_list))

    def _get_sn_by_mes(self, other_vo: OtherVo):
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn", not_null=True)
        org_code = other_vo.get_value_by_cons_key("org_code")

        pcb_sn = other_vo.list_sn()
        # 无整板条码则取拼板条码，否则获取其中一个拼板条码
        if pcb_sn:
            lb_code = pcb_sn
        else:
            lb_code = ''
            barcode_map = other_vo.get_barcode_map()
            for key, value in barcode_map.items():
                if key not in ['-1', '-2'] and value:
                    lb_code = value
                    break

        if not lb_code:
            return self.x_response("false", f"未获取到可用的条码")

        param = {
            "value": {
                "orgCode": org_code,
                "lbCode": lb_code
            }
        }
        try:
            ret = xrequest.RequestUtil.post_json(api_url_get_sn, param)
            if ret.get("code") == 0:
                rows = ret.get("data", {}).get('rows', [])
                if rows:
                    sn_list = []
                    for item in sorted(rows, key=lambda x: x["seq"]):
                        sn_list.append(item["lb_code"])
                    return self.x_response("true", ",".join(sn_list))
                else:
                    return self.x_response("false", f"mes接口返回拼板列表rows为空")

            else:
                err_msg = f"message:{ret.get('message')}, stackTrace:{ret.get('stackTrace')}"
                return self.x_response("false", f"mes接口返回错误，{err_msg}")
        except Exception as e:
            return self.x_response("false", f"本地网络异常，error：{e}")

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        mes_type = other_vo.get_value_by_cons_key("mes_type")
        if mes_type == 'CMES':
            ret = self._get_sn_by_cmes(other_vo)
        else:
            ret = self._get_sn_by_mes(other_vo)
        return ret

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        mes_type = other_vo.get_value_by_cons_key("mes_type")
        # CMES不需要发送设备状态，直接返回
        if mes_type == 'CMES':
            return self.x_response()

        api_url_device = other_vo.get_value_by_cons_key("api_url_device", not_null=True)
        device_code = other_vo.get_value_by_cons_key("device_code")

        status_code_v3 = other_vo.get_status_code_v3()

        if status_code_v3 == "1002":
            dev_state = "run"
        elif status_code_v3 == "1003":
            dev_state = "stop"
        elif status_code_v3 == "3001":
            dev_state = "wait"
        elif status_code_v3 in ["2001", "2002", "3002", "3003", "3004",
                                "3005", "3006", "3007", "4001", "4002", "4003",
                                "5001", "5002"]:
            dev_state = "error"
        else:
            self.log.info(f"该设备状态不上传到Mes！")
            return self.x_response()

        device_param = {
            "devCode": device_code,
            "devState": dev_state,
            "time": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
        }
        ret = xrequest.RequestUtil.post_json(
            api_url_device,
            device_param
        )

        if str(ret.get("resultCode")) != "0000":
            return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")

        return self.x_response()

    def _send_data_to_cmes(self, data_vo: DataVo):
        factory_code = data_vo.get_value_by_cons_key("factory_code")
        station_code = data_vo.get_value_by_cons_key("station_code")
        work_center = data_vo.get_value_by_cons_key("work_center")
        test_no = data_vo.get_value_by_cons_key("test_no")
        worker_no = data_vo.get_value_by_cons_key("worker_no")
        key_info = data_vo.get_value_by_cons_key("key_info")
        test_version = data_vo.get_value_by_cons_key("test_version")
        cmes_api_url_data = data_vo.get_value_by_cons_key("cmes_api_url_data", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        if not self.token:
            ret = self._request_token(data_vo)
            if not ret.get('result'):
                return self.x_response("false", f"获取Token失败：{ret.get('string')}")

        header = {
            "Content-Type": "application/json",
            f"cmes_{factory_code}_info": self.token
        }

        error_msg_list = []
        try:
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                comp_data_list = []
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        comp_tag = comp_entity.designator
                        repair_ng_str = comp_entity.repair_ng_str
                        comp_data_list.append(f"{comp_tag},{repair_ng_str}")

                barcode = board_entity.barcode
                board_no = board_entity.board_no
                param = {
                    "lotSnCode": barcode,
                    "isPass": board_entity.repair_result,
                    "workCenter": work_center,
                    "stationCode": station_code,
                    "testFrameidNo": test_no,
                    "testTime": start_time,
                    "workerNo": worker_no,
                    "keyInfo": key_info,
                    "autoTestVersion": test_version,
                    "testStartDate": start_time,
                    "testEndDate": end_time,
                    "testItem": ";".join(comp_data_list),
                }
                ret = xrequest.RequestUtil.post_json(cmes_api_url_data, param, headers=header)
                result = ret.get("result", {})
                is_success = result.get("isSuccess")
                if not is_success:
                    msg = result.get("msg")
                    error_msg_list.append(f"CMES返回异常: 条码{board_no}:{barcode} error:{msg}")
        except Exception as e:
            error_msg_list.append(f'本地网络异常：{e}')

        if error_msg_list:
            error_msg = "\n".join(error_msg_list)
            return self.x_response("false", error_msg)

        return self.x_response()

    def _send_data_to_mes(self, data_vo: DataVo):
        api_url_data = data_vo.get_value_by_cons_key("api_url_data", not_null=True)
        device_code = data_vo.get_value_by_cons_key("device_code")
        board_side2 = data_vo.get_value_by_cons_key("board_side2")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        track_index = pcb_entity.get_track_index()

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_list = []
            comp_ix = 0
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_ix += 1
                    comp_src_img = comp_entity.image_path
                    comp_tag = comp_entity.designator
                    if os.path.exists(comp_src_img):
                        comp_file2 = comp_src_img
                    else:
                        comp_file2 = ""
                        self.log.warning(f"未找到该器件[{comp_tag}]的图片")

                    comp_ng_list.append({
                        "componentId": comp_entity.designator,
                        "errorCode": comp_entity.repair_ng_code,
                        "errorImage": comp_file2
                    })

            board_data_list.append({
                "pcbId": board_no,
                "pcbStatus": board_entity.get_final_result("GOOD", "PASS", "FAIL"),
                "pcbBarCode": barcode,
                "badItems": comp_ng_list
            })

        data_param = {
            "devCode": device_code,
            "operator": operator,
            "side": board_side2,
            "trackId": track_index,
            "boardBarCode": pcb_entity.pcb_barcode,
            "boardStatus": pcb_entity.get_final_result("GOOD", "PASS", "FAIL"),
            "dateTime": test_time,
            "pcbChecks": board_data_list
        }

        err_msg = ''
        try:
            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if str(ret.get("resultCode")) != "0000":
                err_msg = f"mes接口异常，error：{ret.get('message')}"
        except Exception as e:
            err_msg = f"本地网络异常：{e}"

        if err_msg:
            return self.x_response("false", err_msg)
        else:
            return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        mes_type = data_vo.get_value_by_cons_key("mes_type")
        if mes_type == 'CMES':
            ret = self._send_data_to_cmes(data_vo)
        else:
            ret = self._send_data_to_mes(data_vo)
        return ret
