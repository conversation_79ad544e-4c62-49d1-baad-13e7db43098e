# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/19 下午4:27
# Author     ：sch
# version    ：python 3.8
# Description：宁波精华
"""
import json
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "ningbojinghua release v1.0.0.4",
        "device": "430",
        "feature": ["从mes获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-19 16:27  init
date: 2024-09-21 11:02  修改请求参数以及兼容返回参数
date: 2024-09-21 11:55  修改参数
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://**************:8090/WebService/MesWebApi.asmx/GetFCT_ArrangeThePlateWebApi",
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")

        pcb_sn = other_vo.get_pcb_sn()

        get_sn_param = {
            "SN": pcb_sn,
            "Status": "OK",
            "Type": "2"
        }
        ret = xrequest.RequestUtil.post_json(api_url, get_sn_param)

        # if ret.get("stu") != "OK":
        #     return self.x_response("false", f"接口响应异常，error：{ret.get('msg')}")
        #
        # ret_list_str = ret.get('msg')
        # if not ret_list_str:
        #     return self.x_response("false", f"未从mes获取到条码！")

        # if ret_list_str is str:
        #     ret_list = json.loads(ret_list_str)
        # else:
        #     ret_list = ret_list_str

        ret_sn = []
        for item in ret:
            ret_sn.append(item.get('SN'))

        if not ret_sn:
            return self.x_response("false", f"未从mes获取到条码！")

        return self.x_response("true", ",".join(ret_sn))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            board_data_list.append({
                "SN": barcode,
                "Status": board_entity.get_repair_result("OK", "NG")
            })

        upload_data_param = {
            "SN": json.dumps(board_data_list, separators=(",", ":")),
            "Status": "",
            "Type": "1-AOI"
        }

        ret = xrequest.RequestUtil.post_json(api_url, upload_data_param)
        if ret.get("stu") != "OK":
            return self.x_response("false", f"接口响应异常，error：{ret.get('msg')}")

        return self.x_response()
