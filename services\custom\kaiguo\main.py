# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/27 下午5:27
# Author     ：sch
# version    ：python 3.8
# Description：铠国
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


def parse_ret_str(ret_str):
    try:
        json_str = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        ret_dict = json.loads(json_str.text)
    except Exception as err:
        raise Exception(f"解析数据失败，请检查接口返回的数据，error：{err}")
    return ret_dict


class Engine(BaseEngine):
    version = {
        "title": "kaiguo release v1.0.0.4",
        "device": "203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-27 18:10  条码校验，上传数据到mes
date: 2023-10-16 16:26  重新解析接口返回的数据
date: 2023-10-17 09:26  IsPass改成 true/false
date: 2023-10-17 15:28  bugfix：FileName传:条码_时间戳.jpg
""", }

    form = {
        "api_url": {
            "ui_name": "数据接口URL",
            "value": "http://localhost/WebService/CommonService.asmx"
        },
        "file_api_url": {
            "ui_name": "文件接口URL",
            "value": "http://localhost/WebService/FileService.asmx"
        },
        "station": {
            "ui_name": "工位",
            "value": ""
        },
        "resource": {
            "ui_name": "资源",
            "value": ""
        },
        "username": {
            "ui_name": "用户名",
            "value": ""
        }
    }

    combo = {
        "is_multi_plate": {
            "ui_name": "多联板",
            "item": ["true", "false"],
            "value": "false"
        }
    }

    other_form = {
        "log_number": {
            "ui_name": "单次日志输出(字符)",
            "value": "10000"
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        station = other_vo.get_value_by_cons_key("station")
        resource = other_vo.get_value_by_cons_key("resource")
        username = other_vo.get_value_by_cons_key("username")
        is_multi_plate = other_vo.get_value_by_cons_key("is_multi_plate")

        sn_list = other_vo.list_sn()

        check_url = f"{api_url}/RoutingCheck"
        for sn in sn_list:
            param = {
                "Barcode": sn,
                "StationName": station,
                "RscName": resource,
                "UserName": username,
                "IsMultiPlate": True if is_multi_plate == "true" else False
            }

            ret_xml = xrequest.RequestUtil.post_form(check_url, param, to_json=False)

            ret = parse_ret_str(ret_xml)

            if ret.get("Result") != "OK":
                return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('Msg')}")

            self.log.info("传其中一个条码做校验即可")
            break

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        file_api_url = data_vo.get_value_by_cons_key("file_api_url")
        station = data_vo.get_value_by_cons_key("station")
        resource = data_vo.get_value_by_cons_key("resource")
        username = data_vo.get_value_by_cons_key("username")
        is_multi_plate = data_vo.get_value_by_cons_key("is_multi_plate")
        log_number = data_vo.get_value_by_cons_key("log_number")

        try:
            log_number = int(log_number)
        except Exception as err:
            return self.x_response("false", f"配置项：[单次日志输出]必须为数字！error: {err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        defect_data_list = []
        ix = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                ix += 1

                if comp_entity.is_repair_ng():
                    ng_code = comp_entity.repair_ng_code

                    defect_data_list.append({
                        "SerialNumber": str(ix),
                        "NcCode": ng_code,
                        "Location": comp_entity.designator
                    })

        error_msg = ""

        # 1. 上传整板图
        self.log.info(f"开始上传整板图...")
        src_t_image = pcb_entity.get_unknown_t_pcb_image()
        if src_t_image:
            file_content = xutil.ImageUtil.file_to_base64_content(src_t_image)

            file_url = f"{file_api_url}/FileUpload"
            data_param = {
                "Barcode": pcb_sn,
                "StationName": station,
                "RscName": resource,
                "FileName": f"{pcb_sn}_{start_time}.jpg",
                "FileContent": file_content,
                "IsMultiPlate": True if is_multi_plate == "true" else False,
            }

            ret_xml = xrequest.RequestUtil.post_form(file_url, data_param, log_number=log_number, to_json=False)

            ret1 = parse_ret_str(ret_xml)
            if ret1.get("Result") != "OK":
                error_msg = f"mes接口异常，上传图片失败，error：{ret1.get('Msg')}"
        else:
            self.log.warning(f"大图不存在，无法上传！")

        # 2. 上传数据
        self.log.info(f"开始上传数据...")
        data_url = f"{api_url}/SaveTestResult"

        data_param = {
            "Barcode": pcb_sn,
            "StationName": station,
            "RscName": resource,
            "UserName": username,
            "IsMultiPlate": True if is_multi_plate == "true" else False,
            "IsPass": pcb_entity.get_repair_result(True, False),
            "DefectCode": json.dumps(defect_data_list, ensure_ascii=False),
            "Str_Json": ""
        }

        ret_xml = xrequest.RequestUtil.post_form(data_url, data_param, to_json=False)

        ret2 = parse_ret_str(ret_xml)
        if ret2.get("Result") != "OK":
            error_msg = f"mes接口异常，上传数据失败，error：{ret2.get('Msg')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()


if __name__ == '__main__':
    ret_s = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">{
  "Result": "OK",
  "Msg": ""
}</string>"""

    # root = xutil.XmlUtil.get_xml_root_by_str(ret_s)
    # print(root.text)

    ret_dict1 = parse_ret_str(ret_s)
    print(ret_dict1)
