# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/25 下午2:11
# Author     ：sch
# version    ：python 3.8
# Description：台湾旭隼
"""

from typing import Any

import pymssql

from common import xcons
from common.xutil import log
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import BaseEngine
from services.custom.shenzhenriyueyuan.main import insert_template

insert_template = """INSERT INTO {table_name} (
[pcb_sn],
[test_result], 
[repair_result], 
[create_time], 
[update_time] 
)
VALUES
(
    '{pcb_sn}',
    '{test_result}',
    '{repair_result}',
    '{create_time}',
    '{create_time}'
);"""


class Engine(BaseEngine):
    version = {
        "title": "taiwanxusun release v1.0.0.1",
        "device": "40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-25 14:25  init
""", }

    form = {
        "db_host": {
            "ui_name": "服务器IP",
            "value": "127.0.0.1"
        },
        "db_port": {
            "ui_name": "端口号",
            "value": "1433"
        },
        "database": {
            "ui_name": "数据库",
            "value": "ALDSPC"
        },
        "username": {
            "ui_name": "用户名",
            "value": "sa"
        },
        "password": {
            "ui_name": "密码",
            "value": "linshimima123"
        },

    }
    button = {
        "test_connect": {
            "ui_name": "测试连接",
        }
    }

    def insert_test_row(self,
                        db_host,
                        db_port,
                        database,
                        username,
                        password,
                        param_map
                        ):
        """
        插入一条测试记录到数据库
        :return:
        """
        conn = pymssql.connect(server=db_host, user=username, password=password, database=database, port=db_port)
        self.log.info(f"conn: {conn}")
        cur = conn.cursor()

        insert_sql = insert_template.format(**param_map)

        log.info(f"数据库插入语句：\n{insert_sql}")
        cur.execute(insert_sql)
        conn.commit()
        conn.close()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        db_host = data_vo.get_value_by_cons_key("db_host")
        database = data_vo.get_value_by_cons_key("database")
        username = data_vo.get_value_by_cons_key("username")
        password = data_vo.get_value_by_cons_key("password")
        db_port = data_vo.get_value_by_cons_key("db_port")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            row_param = {
                "table_name": "aoi_log",
                "pcb_sn": barcode,
                "test_result": board_entity.get_robot_result("PASS", "NG"),
                "repair_result": board_entity.get_repair_result("PASS", "NG"),
                "create_time": test_time,
            }

            self.insert_test_row(db_host, db_port, database, username, password, row_param)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "test_connect":
            db_host = btn_vo.get_value_by_cons_key("db_host")
            database = btn_vo.get_value_by_cons_key("database")
            username = btn_vo.get_value_by_cons_key("username")
            password = btn_vo.get_value_by_cons_key("password")
            db_port = btn_vo.get_value_by_cons_key("db_port")

            self.log.info(f"{db_host=}, {username=}, {password=}, {database=}, {db_port=}")

            conn = pymssql.connect(server=db_host, user=username, password=password, database=database, port=db_port)

            self.log.info(f"conn: {conn}")
            conn.close()

            return self.x_response("true", "连接成功")

        return self.x_response()
