# # !/usr/bin/env python
# # -*-coding:utf-8 -*-
#
# """
# # File       : http_service.py
# # Time       ：2024/3/21 上午10:21
# # Author     ：sch
# # version    ：python 3.8
# # Description：
# """
# import json
# import multiprocessing
# import time
#
# from flask import Flask, jsonify, request
#
# from common import xutil
# from common.xutil import log
#
# # app = FastAPI()
# #
# # ret_ok = {"code": 200, "message": "ok", "data": "ok"}
# #
# #
# # @app.get("/ping")
# # def ping():
# #     print('ping')
# #
# #     return ret_ok
# #
# #
# # @app.get('/check_something')
# # def check_something():
# #     return ret_ok
# #
# #
# # class HttpThreadQThread(QThread):
# #
# #     def run(self) -> None:
# #         time.sleep(0.2)  # warning: 必须加这个等待时间，不加的话有可能导致http服务运行不起来
# #         host = "0.0.0.0"
# #         port = 8082
# #         log.info(f"http服务已启动 -----> http://{host}:{port}  test connect: http://127.0.0.1:{port}/ping")  # noqa
# #         uvicorn.run(app, host=host, port=port)
# from services.route import engine
#
# app = Flask(__name__)
#
#
# @app.route('/ping', methods=['GET'])
# def ping():
#     # 构造一个包含问候消息的字典
#     # all_data = request.get_json()
#     # print(all_data)
#     greeting = {
#         'message': f'Hello,!'
#     }
#
#     # 使用 jsonify 将字典转换为 JSON 响应
#     return jsonify(greeting)
#
#
# @app.route('/leichen/device_status', methods=['POST', 'GET'])
# def ruijiewangluo_device_status():
#     """
#     锐捷网络-设备状态读取
#
#     {
#     "Result": OK,             //返回结果，OK（成功），NG（失败），ERROR(错误)
#     "ErrorException": "XXX故障",     //错误信息，正常情况为空
#     "State": “运行状态”，          //设备运行状态代码
#     "OEE": “OEE”，   //设备稼动率OEE，设备正常检测的时间/设备运行的时间
#     "AlarmList":                     //设备Alarm列表
#     [
#        “Alarm1”                 //设备Alarm
#     ],
#     "FaultList":                     //设备Fault列表
#     [
#        “Fault1”                   //设备Fault
#     ]
#     }
#     :return:
#     """
#     log.info(f"<<<<<<来自客户端的请求-----/leichen/device_status------")
#
#     try:
#         if request.content_type == 'application/json':
#             req_data = request.json
#         else:
#             req_data = {}
#
#         log.info(f"请求参数：{json.dumps(req_data, ensure_ascii=False)}")
#
#         device_code_ui = engine.main_window.config_data.get('other_form', {}).get('device_code', {}).get('value')
#         resource = req_data.get('Resource', '')
#
#         if device_code_ui != resource:
#             ret_res = {
#                 "Result": "ERROR",
#                 "ErrorException": f"设备号[{resource}]不匹配，期待Resource={device_code_ui}",
#                 "State": "设备空闲",  # 设备运行，设备停止，设备空闲，设备故障
#                 "OEE": "",
#                 "AlarmList": [],
#                 "FaultList": []
#             }
#
#             log.info(f">>>>>>http响应参数：{json.dumps(ret_res, ensure_ascii=False)}")
#             return jsonify(ret_res)
#
#         cache_data = xutil.CacheUtil.get_cache_data()
#         device_status_current_state = cache_data.get('device_status_current_state')
#         device_status_current_str = cache_data.get('device_status_current_str')
#
#         alarm_list = []
#
#         if device_status_current_str:
#             alarm_list.append(device_status_current_str)
#
#         ret_res = {
#             "Result": "OK",
#             "ErrorException": "",
#             "State": device_status_current_state,  # 设备运行，设备停止，设备空闲，设备故障
#             "OEE": xutil.CacheUtil.cal_oee(),
#             "AlarmList": alarm_list,
#             "FaultList": []
#         }
#         # 使用 jsonify 将字典转换为 JSON 响应
#
#     except Exception as err:
#         ret_res = {
#             "Result": "ERROR",
#             "ErrorException": f"其他异常：{err}",
#         }
#
#     log.info(f">>>>>>http响应参数：{json.dumps(ret_res, ensure_ascii=False)}")
#
#     return jsonify(ret_res)
#
#
# @app.route('/leichen/update_data', methods=['POST', 'GET'])
# def ruijiewangluo_update_data():
#     log.info(f"<<<<<<来自客户端的请求-----/leichen/update_data------")
#
#     try:
#         if request.content_type == 'application/json':
#             req_data = request.json
#         else:
#             req_data = {}
#
#         log.info(f"请求参数：{json.dumps(req_data, ensure_ascii=False)}")
#
#         line_ui = engine.main_window.config_data.get('other_form', {}).get('line_code', {}).get('value')
#         pp = engine.main_window.config_data.get('form', {}).get('product_id', {}).get('value')
#         print(f"{pp}")
#
#         req_line = req_data.get('Line')
#
#         if line_ui != req_line:
#             ret_res = {
#                 "Result": "ERROR",
#                 "ErrorException": f"线体号[{req_line}]不匹配，期待Line={line_ui}",
#             }
#
#             log.info(f">>>>>>http响应参数：{json.dumps(ret_res, ensure_ascii=False)}")
#             return jsonify(ret_res)
#
#         order_id = req_data.get('MfgOrder')
#         product_id = req_data.get('Product')
#
#         ui_item1 = getattr(engine.main_window, 'form_order_id')
#         ui_item2 = getattr(engine.main_window, 'form_product_id')
#
#         ui_item1.setText(order_id)
#         ui_item2.setText(product_id)
#
#         engine.main_window.config_data['form']['order_id']['value'] = order_id
#         engine.main_window.config_data['form']['product_id']['value'] = product_id
#         engine.main_window.save_config_data_to_file()
#
#         ret_res = {
#             "Result": "OK",
#             "ErrorException": f"更新工单和产品名称成功！",
#         }
#
#     except Exception as err:
#         ret_res = {
#             "Result": "ERROR",
#             "ErrorException": f"其他异常：{err}",
#         }
#
#     log.info(f">>>>>>http响应参数：{json.dumps(ret_res, ensure_ascii=False)}")
#
#     return jsonify(ret_res)
#
#
# def start_flask_server():
#     time.sleep(2)
#     port = 8082
#     log.info(f"http服务已启动 -----> http://127.0.0.1:{port}  test connect: http://127.0.0.1:{port}/ping")  # noqa
#     app.run(port=port)
#
#
# def run_server_in_process():
#     p = multiprocessing.Process(target=start_flask_server)
#     p.daemon = True
#     p.start()
