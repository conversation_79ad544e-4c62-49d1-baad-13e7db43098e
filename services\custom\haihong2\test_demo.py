# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/11/13 下午2:18
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

if __name__ == '__main__':
#     ret1 = """<?xml version="1.0" encoding="utf-8"?>
# <string xmlns="http://mesateapi.com/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""

    ret2 = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://mesateapi.com/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""

    root1 = xutil.XmlUtil.get_xml_root_by_str(ret2)
    root2 = xutil.XmlUtil.get_xml_root_by_str(root1.text)

    ret_code = root2[0].text
    ret_msg = root2[1].text
    print(ret_code)
    print(ret_msg)
