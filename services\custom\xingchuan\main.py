# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/14 上午9:39
# Author     ：sch
# version    ：python 3.8
# Description：兴川
"""

from typing import Any

from common import xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "xingchuan release v1.0.0.3",
        "device": "430,630",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-14 09:40  init
date: 2023-12-12 16:34  增加不良代码自定义功能
date: 2024-01-02 12:16  wpRs改为复判结果
""", }

    combo = {
        "dev_type": {
            "ui_name": "设备分类",
            "item": ["SPI", "AOI", "烧录", "线体MES"],
            "value": "AOI"
        }
    }

    other_form = {
        "user_id": {
            "ui_name": "操作人",
            "value": "WEBSERVICES"
        }
    }

    form = {
        "check_url": {
            "ui_name": "前工序校验接口URL",
            "value": "",
        },
        "data_url": {
            "ui_name": "工序过站接口URL",
            "value": "",
        },
        "dev_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "wp_code": {
            "ui_name": "工序编码",
            "value": "",
        },
        "qce_code": {
            "ui_name": "测试人员编码",
            "value": "",
        },
        "qce_name": {
            "ui_name": "测试人员姓名",
            "value": "",
        },
    }

    path = {
        "ng_path": {
            "ui_name": "图片路径",
            "value": ""
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        req_id = xutil.OtherUtil.get_origin_uuid4_str()

        dev_type = other_vo.get_value_by_cons_key("dev_type")
        dev_code = other_vo.get_value_by_cons_key("dev_code")
        user_id = other_vo.get_value_by_cons_key("user_id")
        wp_code = other_vo.get_value_by_cons_key("wp_code")
        check_url = other_vo.get_value_by_cons_key("check_url")

        project_name = other_vo.get_project_name()

        sn_list = other_vo.list_sn()

        cur_lbs = [{"lbId": sn} for sn in sn_list]

        check_param = {
            "requestId": req_id,
            "requestNo": req_id,
            "requestType": "HTTP",
            "sourceId": dev_type,
            "targetId": "MES",
            "userId": user_id,
            "data": {
                "devCode": dev_code,
                "devType": dev_type,
                "wpCode": wp_code,
                "recipeName": project_name,
                # "recipeVersion": project_name,
                "curLbs": cur_lbs
            },
            "extend": {

            }
        }

        ret = xrequest.RequestUtil.post_json(check_url, check_param)
        if ret.get("resultCode") != "0000":
            return self.x_response("false", f"mes接口异常,条码校验失败,error:{ret.get('resultMsg')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        dev_type = data_vo.get_value_by_cons_key("dev_type")
        dev_code = data_vo.get_value_by_cons_key("dev_code")
        user_id = data_vo.get_value_by_cons_key("user_id")
        wp_code = data_vo.get_value_by_cons_key("wp_code")
        data_url = data_vo.get_value_by_cons_key("data_url")
        qce_code = data_vo.get_value_by_cons_key("qce_code")
        qce_name = data_vo.get_value_by_cons_key("qce_name")
        ng_path = data_vo.get_value_by_cons_key("ng_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if dev_type == "SPI":
            true_flag = "1"
        else:
            true_flag = "0"

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    robot_ng_code = comp_entity.robot_ng_code
                    comp_tag = comp_entity.designator

                    alg_list = []
                    for alg in comp_entity.yield_alg_entity():
                        alg_list.append({
                            "maxThreshold": alg.max_threshold,
                            "minThreshold": alg.min_threshold,
                            "testVal": alg.test_val,
                            "result": "1" if alg.result == true_flag else "2",  # 1:0K，2: NG
                            "testName": alg.test_name
                        })

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        dst_file = f"{ng_path}/{barcode}_{comp_tag}_{robot_ng_code}"
                        suffix = xutil.FileUtil.copy_file(comp_src_img, dst_file, is_auto_add_suffix=True)
                    else:
                        dst_file = ""
                        suffix = ""

                    comp_data_list.append({
                        "ngItemCode": robot_ng_code,
                        "seq": board_no,
                        "splPointLoc": comp_tag,
                        "splItemValue": f"{dst_file}{suffix}",
                        "splItemRs": board_entity.get_robot_result("1", "0"),
                        "Compart": comp_entity.part,
                        "comptype": comp_entity.type,
                        "compxPos": comp_entity.x_pos,
                        "compYpos": comp_entity.y_pos,
                        "compWidth": comp_entity.width,
                        "compHeight": comp_entity.height,
                        "AlgorithmList": alg_list
                    })

            board_data_list.append({
                "lbId": barcode,
                # "wpRs": 1 if board_entity.robot_result else 0,
                "wpRs": 1 if board_entity.repair_result else 0,
                "lbwpQcList": comp_data_list,
            })

        req_id = xutil.OtherUtil.get_origin_uuid4_str()

        s_datetime = pcb_entity.get_start_time()
        e_datetime = pcb_entity.get_end_time()
        data_param = {
            "requestId": req_id,
            "requestNo": req_id,
            "requestType": "HTTP",
            "sourceId": dev_type,
            "targetId": "MES",
            "userId": user_id,
            "data": {
                "devCode": dev_code,
                "devType": dev_type,
                "wpCode": wp_code,
                "recipeName": pcb_entity.project_name,
                "curLbs": board_data_list,
            },
            "extend": {
                "sDate": int(s_datetime.timestamp() * 1000),
                "eDate": int(e_datetime.timestamp() * 1000),
                "qceCode": qce_code,
                "qceName": qce_name,
            }
        }
        ret = xrequest.RequestUtil.post_json(data_url, data_param)
        if ret.get("resultCode") != "0000":
            return self.x_response("false", f"mes接口异常,上传数据失败,error:{ret.get('resultMsg')}")

        return self.x_response()
