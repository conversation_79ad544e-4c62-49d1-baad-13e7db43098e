#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志分析工具测试脚本
"""

import os
import sys
import tempfile
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from log_parser import LogParser, LogEntry
from log_searcher import LogSearcher, SearchCriteria
from error_analyzer import ErrorAnalyzer
from log_exporter import LogExporter
from config_manager import ConfigManager


def create_test_log_file():
    """创建测试日志文件"""
    test_logs = [
        "2024-01-01 10:00:00 - [line:1] - INFO: 系统启动成功",
        "2024-01-01 10:01:00 - [line:2] - INFO: 连接数据库成功",
        "2024-01-01 10:02:00 - [line:3] - WARNING: 内存使用率较高: 85%",
        "2024-01-01 10:03:00 - [line:4] - ERROR: 连接MES服务器失败, error255",
        "2024-01-01 10:04:00 - [line:5] - INFO: 重试连接MES服务器",
        "2024-01-01 10:05:00 - [line:6] - ERROR: 工单不存在, error18",
        "2024-01-01 10:06:00 - [line:7] - CRITICAL: 数据库连接丢失",
        "2024-01-01 10:07:00 - [line:8] - INFO: 数据库重连成功",
        "2024-01-01 10:08:00 - [line:9] - ERROR: AOI检测发现漏件, error1",
        "2024-01-01 10:09:00 - [line:10] - WARNING: 设备温度过高: 75°C",
        "<span style='color:red'>2024-01-01 10:10:00 网络连接超时</span>",
        "<span style='color:green'>2024-01-01 10:11:00 网络连接恢复</span>",
        "2024-01-01 10:12:00 ---Error--- 文件读取失败: permission denied",
        "2024-01-01 10:13:00 ---Info--- 文件读取重试成功",
        "2024-01-01 10:14:00 - [line:15] - ERROR: 产品已下线, error244",
    ]
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False, encoding='utf-8')
    for log_line in test_logs:
        temp_file.write(log_line + '\n')
    temp_file.close()
    
    return temp_file.name


def test_log_parser():
    """测试日志解析器"""
    print("=== 测试日志解析器 ===")
    
    # 创建测试日志文件
    test_file = create_test_log_file()
    
    try:
        parser = LogParser()
        entries = parser.parse_log_file(test_file)
        
        print(f"解析了 {len(entries)} 条日志")
        
        # 检查解析结果
        for i, entry in enumerate(entries[:5]):
            print(f"条目 {i+1}:")
            print(f"  时间: {entry.timestamp}")
            print(f"  级别: {entry.level}")
            print(f"  消息: {entry.message}")
            print(f"  错误码: {entry.error_code}")
            print()
        
        # 获取摘要
        summary = parser.get_log_summary(entries)
        print("日志摘要:")
        print(f"  总条目数: {summary['total_entries']}")
        print(f"  级别统计: {summary['level_counts']}")
        print(f"  错误码统计: {summary['error_codes']}")
        
        return entries
        
    finally:
        # 清理临时文件
        os.unlink(test_file)


def test_log_searcher(entries):
    """测试日志搜索器"""
    print("\n=== 测试日志搜索器 ===")
    
    searcher = LogSearcher()
    searcher.load_entries(entries)
    
    # 测试快速搜索
    print("快速搜索 'error':")
    results = searcher.quick_search("error")
    print(f"找到 {len(results)} 条结果")
    
    # 测试高级搜索
    print("\n高级搜索 - 错误级别:")
    criteria = SearchCriteria(levels=['ERROR', 'CRITICAL'])
    results = searcher.search(criteria)
    print(f"找到 {len(results)} 条错误日志")
    
    # 测试关键词搜索
    print("\n关键词搜索 - 'MES':")
    criteria = SearchCriteria(keywords=['MES'])
    results = searcher.search(criteria)
    print(f"找到 {len(results)} 条包含'MES'的日志")
    
    # 测试错误码搜索
    print("\n错误码搜索 - '255':")
    criteria = SearchCriteria(error_codes=['255'])
    results = searcher.search(criteria)
    print(f"找到 {len(results)} 条错误码为255的日志")
    
    # 获取统计信息
    stats = searcher.get_statistics()
    print("\n搜索统计:")
    print(f"  总条目: {stats['total']}")
    print(f"  级别分布: {stats['levels']}")
    
    return results


def test_error_analyzer(entries):
    """测试错误分析器"""
    print("\n=== 测试错误分析器 ===")
    
    analyzer = ErrorAnalyzer()
    
    # 分析错误
    errors = analyzer.analyze_errors(entries)
    print(f"识别出 {len(errors)} 种错误类型")
    
    for error_key, error_info in list(errors.items())[:5]:
        print(f"错误: {error_key}")
        print(f"  描述: {error_info.description}")
        print(f"  分类: {error_info.category}")
        print(f"  次数: {error_info.count}")
        print()
    
    # 获取错误摘要
    summary = analyzer.get_error_summary(entries)
    print("错误摘要:")
    print(f"  总错误数: {summary['total_errors']}")
    print(f"  严重程度分布: {summary['severity_distribution']}")
    print(f"  错误分类: {summary['error_categories']}")
    
    # 生成错误报告
    report = analyzer.generate_error_report(entries)
    print("\n错误报告预览:")
    print(report[:500] + "...")
    
    return errors


def test_log_exporter(entries):
    """测试日志导出器"""
    print("\n=== 测试日志导出器 ===")
    
    exporter = LogExporter()
    
    # 测试CSV导出
    csv_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    csv_file.close()
    
    try:
        success = exporter.export_to_csv(entries, csv_file.name)
        print(f"CSV导出: {'成功' if success else '失败'}")
        
        if success:
            with open(csv_file.name, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()
                print(f"导出了 {len(lines)-1} 条记录（不含表头）")
    finally:
        os.unlink(csv_file.name)
    
    # 测试JSON导出
    json_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    json_file.close()
    
    try:
        success = exporter.export_to_json(entries, json_file.name)
        print(f"JSON导出: {'成功' if success else '失败'}")
        
        if success:
            import json
            with open(json_file.name, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"导出了 {len(data)} 条JSON记录")
    finally:
        os.unlink(json_file.name)
    
    # 测试错误报告导出
    report_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
    report_file.close()
    
    try:
        success = exporter.generate_error_report(entries, report_file.name)
        print(f"错误报告导出: {'成功' if success else '失败'}")
        
        if success:
            with open(report_file.name, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"报告长度: {len(content)} 字符")
    finally:
        os.unlink(report_file.name)


def test_config_manager():
    """测试配置管理器"""
    print("\n=== 测试配置管理器 ===")
    
    # 使用临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        config_mgr = ConfigManager(temp_dir)
        
        # 测试添加最近搜索
        config_mgr.add_recent_search("error")
        config_mgr.add_recent_search("warning")
        config_mgr.add_recent_search("timeout")
        
        recent_searches = config_mgr.get_recent_searches()
        print(f"最近搜索: {recent_searches}")
        
        # 测试添加最近路径
        config_mgr.add_recent_path("/var/log")
        config_mgr.add_recent_path("/tmp/logs")
        
        recent_paths = config_mgr.get_recent_paths()
        print(f"最近路径: {recent_paths}")
        
        # 测试自定义错误码
        config_mgr.add_custom_error_code("9001", "测试错误", "测试分类", "ERROR")
        config_mgr.add_custom_error_code("9002", "另一个测试错误", "测试分类", "WARNING")
        
        custom_codes = config_mgr.get_custom_error_codes()
        print(f"自定义错误码: {custom_codes}")
        
        # 测试配置摘要
        summary = config_mgr.get_config_summary()
        print("配置摘要:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        # 测试配置导出/导入
        export_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        export_file.close()
        
        try:
            success = config_mgr.export_config(export_file.name)
            print(f"配置导出: {'成功' if success else '失败'}")
            
            if success:
                # 创建新的配置管理器测试导入
                temp_dir2 = tempfile.mkdtemp()
                try:
                    config_mgr2 = ConfigManager(temp_dir2)
                    success = config_mgr2.import_config(export_file.name)
                    print(f"配置导入: {'成功' if success else '失败'}")
                    
                    if success:
                        imported_searches = config_mgr2.get_recent_searches()
                        print(f"导入的搜索记录: {imported_searches}")
                finally:
                    import shutil
                    shutil.rmtree(temp_dir2)
        finally:
            os.unlink(export_file.name)
            
    finally:
        import shutil
        shutil.rmtree(temp_dir)


def run_all_tests():
    """运行所有测试"""
    print("开始运行日志分析工具测试...")
    print("=" * 60)
    
    try:
        # 测试日志解析
        entries = test_log_parser()
        
        # 测试日志搜索
        search_results = test_log_searcher(entries)
        
        # 测试错误分析
        error_results = test_error_analyzer(entries)
        
        # 测试日志导出
        test_log_exporter(entries)
        
        # 测试配置管理
        test_config_manager()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
