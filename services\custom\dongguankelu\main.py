# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/21 上午9:35
# Author     ：sch
# version    ：python 3.8
# Description：东莞科陆
"""

from typing import Any

from common import xsql, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguankelu release v1.0.0.3",
        "device": "",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-21 09:35  从mes获取条码,条码校验,上传数据
date: 2024-11-12 16:10  从mes获取条码，剔除空条码
date: 2024-11-12 12:19  上传数据流程和格式变更  
""",
    }

    other_form = {
        "sql_server": {
            "ui_name": "数据库IP",
            "value": "127.0.0.1",
            "is_ip": True
        },
        "sql_port": {
            "ui_name": "数据库端口",
            "value": "1433",
            "is_port": True
        },
        "sql_user": {
            "ui_name": "数据库用户名",
            "value": "sa",
        },
        "sql_pwd": {
            "ui_name": "数据库密码",
            "value": "",
        },
        "sql_db": {
            "ui_name": "数据库名称",
            "value": "master",
        },
        "sql_table": {
            "ui_name": "数据库表名(载具产品关系表)",
            "value": "FixtureQrRelation",
        },
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://**********:8190/api/OrBitMesInterface/PutData",
        },
    }

    form = {
        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "order_id": {
            "ui_name": "生产工单",
            "value": "",
        },
        "process_id": {
            "ui_name": "工序编码",
            "value": "",
        },
    }

    password_style = [
        "sql_pwd"
    ]

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        sql_server = other_vo.get_value_by_cons_key("sql_server")
        sql_port = other_vo.get_value_by_cons_key("sql_port", to_int=True)
        sql_user = other_vo.get_value_by_cons_key("sql_user")
        sql_pwd = other_vo.get_value_by_cons_key("sql_pwd")
        sql_db = other_vo.get_value_by_cons_key("sql_db")
        sql_table = other_vo.get_value_by_cons_key("sql_table")

        pcb_sn = other_vo.get_pcb_sn()

        conn = xsql.get_mssql_cursor(sql_server, sql_user, sql_pwd, sql_db, sql_port)

        sql_str = f"""
        SELECT 
            [QrCode1], 
            [QrCode2], 
            [QrCode3], 
            [QrCode4]
        FROM 
            [{sql_table}]
        WHERE 
            [FixtureId] = '{pcb_sn}';
        """

        row = xsql.select_one_by_sql_str(conn, sql_str)

        conn.close()

        barcode_map = other_vo.get_barcode_map()
        if barcode_map:
            board_count = len(barcode_map) - 2
            row = row[:board_count]

        row = [i for i in row if i]  # 剔除空条码

        return self.x_response("true", ",".join(row))

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        device_code = other_vo.get_value_by_cons_key("device_code")
        device_name = other_vo.get_value_by_cons_key("device_name")
        order_id = other_vo.get_value_by_cons_key("order_id")
        process_id = other_vo.get_value_by_cons_key("process_id")

        check_content = []
        for sn in other_vo.list_sn():
            check_content.append({
                "LotSN": sn
            })

        check_param = {
            "InterfaceServiceName": "EnterASideAOIResult",
            "InterfaceType": "校验",
            "EquipmentNo": device_code,
            "EquipmentName": device_name,
            "MOName": order_id,
            "Process": process_id,
            "CheckContent": check_content,
        }

        ret = xrequest.RequestUtil.post_json(api_url, check_param)
        if str(ret.get("code")) != "1":
            return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        device_code = data_vo.get_value_by_cons_key("device_code")
        device_name = data_vo.get_value_by_cons_key("device_name")
        order_id = data_vo.get_value_by_cons_key("order_id")
        process_id = data_vo.get_value_by_cons_key("process_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_content = []
        board_content_ng = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_data.append({
                        "Designator": comp_entity.designator,
                        "Part": comp_entity.part,
                        "Type": comp_entity.type,
                        "RobotCode": comp_entity.repair_ng_code,
                        "RobotResult": comp_entity.get_final_result("OK", "OK", "NG"),
                    })

            board_content.append({
                "LotSN": barcode,
                "Result": board_entity.get_repair_result("OK", "NG"),
                "CompData": comp_data,
            })

            board_content_ng.append({
                "LotSN": barcode,
            })

        pcb_result = pcb_entity.get_repair_result("OK", "NG")

        put_data_param = {
            "InterfaceServiceName": "GetASideAOIResult",
            "InterfaceType": "校验",
            "EquipmentNo": device_code,
            "EquipmentName": device_name,
            "MOName": order_id,
            "Process": process_id,
            "CheckContent": board_content
        }

        ret = xrequest.RequestUtil.post_json(api_url, put_data_param)
        if str(ret.get("code")) != "1":
            return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")

        if pcb_result != "OK":
            # 如果有NG的数据需要再调用一遍接口
            put_data_param2 = {
                "InterfaceServiceName": "NgASideAOIResult",
                "InterfaceType": "校验",
                "EquipmentNo": device_code,
                "EquipmentName": device_name,
                "MOName": order_id,
                "Process": process_id,
                "CheckContent": board_content_ng
            }

            ret = xrequest.RequestUtil.post_json(api_url, put_data_param2)
            if str(ret.get("code")) != "1":
                return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")

        return self.x_response()
