# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/22 上午10:29
# Author     ：sch
# version    ：python 3.8
# Description：太仓同维 6.0  已废弃！！！使用python脚本太仓同维v2
"""
from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "40000001", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "40000017", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "40000007", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "40000003", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "40000009", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "40000011", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "40000016", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "40000002", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "40000025", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "40000008", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "40000026", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "40000020", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "40000024", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "40000005", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "40000018", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "40000010", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "40000022", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "40000015", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "40000028", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "40000022", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "40000023", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "40000027", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "40000004", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "40000015", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "40000029", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "40000032", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "40000030", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "40000019", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}


class Engine(BaseEngine):
    version = {
        "title": "taicangtongwei(deprecated) release v1.0.0.4",
        "device": "50x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-22 11:07  init
date: 2023-05-23 15:17  条码校验，上传数据
date: 2023-05-30 15:34  测试结果改为传1和0
date: 2023-07-21 11:13  使用客户的不良代码上传
""", }

    form = {
        "check_api_url": {
            "ui_name": "工序校验接口",
            "value": "http://mes.tc.gj.com/ate/test/call",
        },
        "data_api_url": {
            "ui_name": "过站接口",
            "value": "http://mes.tc.gj.com/ate/test/call",
        },
        "wo_id": {
            "ui_name": "工单",
            "value": ""
        },
        "process_name": {
            "ui_name": "工序名称",
            "value": ""
        },
        "user": {
            "ui_name": "操作人",
            "value": ""
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        },
        "line": {
            "ui_name": "线体",
            "value": ""
        },
    }

    other_form = {
        "host_ip": {
            "ui_name": "hostIp",
            "value": "127.0.0.1",
        },
        "host_name": {
            "ui_name": "hostName",
            "value": "TC542N",
        },
        "app_id": {
            "ui_name": "appId",
            "value": "S7mGezbm",
        },
        "app_key": {
            "ui_name": "appKey",
            "value": "Jc7yF2GWuTxxxxxxxxx",
        },
        "org_id": {
            "ui_name": "orgId",
            "value": "1",
        },
        "check_code": {
            "ui_name": "工序校验code",
            "value": "ATE211217037",
        },
        "data_code": {
            "ui_name": "过站code",
            "value": "ATE2112171049",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # data_api_url = data_vo.get_value_by_cons_key("data_api_url")
        # wo_id = data_vo.get_value_by_cons_key("wo_id")
        # process_name = data_vo.get_value_by_cons_key("process_name")
        # user = data_vo.get_value_by_cons_key("user")
        # device_name = data_vo.get_value_by_cons_key("device_name")
        # line = data_vo.get_value_by_cons_key("line")
        # data_code = data_vo.get_value_by_cons_key("data_code")
        #
        # host_ip = data_vo.get_value_by_cons_key("host_ip")
        # host_name = data_vo.get_value_by_cons_key("host_name")
        #
        # app_id = data_vo.get_value_by_cons_key("app_id")
        # app_key = data_vo.get_value_by_cons_key("app_key")
        # org_id = data_vo.get_value_by_cons_key("org_id")
        #
        # pcb_entity = data_vo.pcb_entity
        # self.log.info(pcb_entity)
        #
        # for board_entity in pcb_entity.yield_board_entity():
        #     self.log.info(board_entity)
        #
        #     barcode = board_entity.barcode
        #
        #     if not barcode:
        #         barcode = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        #
        #     comp_repair_ng_code = []
        #     comp_repair_ng_tag = []
        #
        #     for comp_entity in board_entity.yield_comp_entity():
        #
        #         if comp_entity.is_repair_ng():
        #             comp_ng_code = comp_entity.repair_ng_code
        #             custom_ng_code = AIS_40X_ERROR_MAP.get(comp_ng_code, {}).get("custom_code", comp_ng_code)
        #
        #             comp_repair_ng_code.append(custom_ng_code)
        #             comp_repair_ng_tag.append(comp_entity.designator)
        #
        #     test_result = board_entity.get_repair_result('1', '0')
        #
        #     data_param_str = f"{wo_id},{process_name},{user},{'|'.join(comp_repair_ng_code)},{';'.join(comp_repair_ng_tag)},{barcode},{test_result},{device_name},{line}"
        #
        #     param = {
        #         "hostIp": host_ip,
        #         "hostName": host_name,
        #         "param": data_param_str,
        #         "code": data_code
        #     }
        #
        #     headers = {
        #         "appId": app_id,
        #         "appKey": app_key,
        #         "orgId": org_id
        #     }
        #
        #     ret = xrequest.RequestUtil.post_form(data_api_url, {}, params=param, headers=headers)
        #     if ret.get('code') != 200:
        #         return self.x_response("false", f"mes接口响应异常，上传测试数据失败，error：{ret.get('message')}")
        self.log.warning(f"deprecated")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        # list_sn = other_vo.list_sn()
        #
        # check_api_url = other_vo.get_value_by_cons_key("check_api_url")
        # wo_id = other_vo.get_value_by_cons_key("wo_id")
        # process_name = other_vo.get_value_by_cons_key("process_name")
        # user = other_vo.get_value_by_cons_key("user")
        # device_name = other_vo.get_value_by_cons_key("device_name")
        # host_ip = other_vo.get_value_by_cons_key("host_ip")
        # host_name = other_vo.get_value_by_cons_key("host_name")
        # check_code = other_vo.get_value_by_cons_key("check_code")
        #
        # app_id = other_vo.get_value_by_cons_key("app_id")
        # app_key = other_vo.get_value_by_cons_key("app_key")
        # org_id = other_vo.get_value_by_cons_key("org_id")
        #
        # for sn in list_sn:
        #     param_str = f"{wo_id},{process_name},{user},{sn},{device_name}"
        #
        #     param = {
        #         "hostIp": host_ip,
        #         "hostName": host_name,
        #         "param": param_str,
        #         "code": check_code
        #     }
        #
        #     headers = {
        #         "appId": app_id,
        #         "appKey": app_key,
        #         "orgId": org_id
        #     }
        #
        #     ret = xrequest.RequestUtil.post_form(check_api_url, {}, params=param, headers=headers)
        #     if ret.get('code') != 200:
        #         return self.x_response("false", f"mes接口响应异常，工序校验失败，error：{ret.get('message')}")
        self.log.warning(f"deprecated")

        return self.x_response()
