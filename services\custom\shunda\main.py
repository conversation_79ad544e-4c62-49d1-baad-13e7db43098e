# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/24 下午2:30
# Author     ：sch
# version    ：python 3.8
# Description：顺达
"""
import time
from typing import Any

from common import xcons, xutil
from common.xsql import MySQLService
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

txt_template = """{barcode}
{machine_id}
{op_id}
{project_name}
{repair_result}
{start_time}
{end_time}
{comp_ng_number}
0
{test_time}
Error flag,Recipe name,Model,Component ID,Pin,Error code,Multi Number,original error code{comp_data}
"""

AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "B19", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "B09", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "B16", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "B39", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "B22", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "B46", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "B24", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "B10", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "B01", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "B06", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "B07", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "B27", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "B04-1", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "B02", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "B08", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "B77", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "B71", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "B21", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "B04", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}


class Engine(BaseEngine):
    version = {
        "title": "shunda release v1.0.0.4",
        "device": "501-L",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-24 14:31  init
date: 2023-07-27 10:52  程序名不传bom号
date: 2023-08-24 11:30  获取不到条码则从数据库获取
""", }

    form = {
        "machine_id": {
            "ui_name": "Machine Id",
            "value": "YG4000_09"
        },
        "op_id": {
            "ui_name": "OP ID",
            "value": "M0073"
        },
    }

    path = {
        "save_path": {
            "ui_name": "txt路径",
            "value": ""
        }
    }

    other_combo = {
        "send_sleep": {
            "ui_name": "发送延时",
            "item": ["0", "0.5", "1", "2", "5", "10", "15", "25"],
            "value": "0.5"
        }
    }

    other_form = {

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        op_id = data_vo.get_value_by_cons_key("op_id")
        save_path = data_vo.get_value_by_cons_key("save_path")
        send_sleep = data_vo.get_value_by_cons_key("send_sleep")
        if not save_path:
            return self.x_response("false", f"未选择txt路径！")

        self.log.info(f"延迟发送....")
        time.sleep(float(send_sleep))
        self.log.info(f"开始发送....")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        project_name = pcb_entity.project_name
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_FILE)
        time_cost = pcb_entity.get_cycle_time()

        sql_service = MySQLService("127.0.0.1", "makerray")

        comp_row_str = ""
        comp_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_uuid = board_entity.board_uuid
            comp_ng_number += board_entity.comp_repair_ng_number

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not pcb_sn:
                pcb_sn = sql_service.get_unique_sn_by_board_uuid(board_uuid)

            for comp_entity in board_entity.yield_comp_entity():
                # if comp_entity.is_repair_ng():
                comp_result = "GOOD" if comp_entity.repair_result else "BAD"
                comp_package = comp_entity.package
                comp_tag = comp_entity.designator
                ng_code1 = comp_entity.robot_ng_code
                ng_code2 = comp_entity.repair_ng_code

                ng_code1_1 = AIS_40X_ERROR_MAP.get(ng_code1, {}).get('custom_code', ng_code1)
                ng_code2_1 = AIS_40X_ERROR_MAP.get(ng_code2, {}).get('custom_code', ng_code2)

                comp_row_str += f"\n{comp_result};{project_name};{comp_package};{comp_tag};0;{ng_code2_1};0;{ng_code1_1}"

        sql_service.close()
        if not pcb_sn:
            pcb_sn = xutil.DateUtil.get_datetime_now()

        txt_content = txt_template.format(**{
            "barcode": pcb_sn,
            "machine_id": machine_id,
            "op_id": op_id,
            "project_name": pcb_entity.pcb,
            "repair_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "start_time": start_time,
            "end_time": end_time,
            "comp_ng_number": comp_ng_number,
            "test_time": time_cost,
            "comp_data": comp_row_str,
        })

        filepath = f"{save_path}/{pcb_sn}.txt"
        xutil.FileUtil.write_content_to_file(filepath, txt_content)

        return self.x_response()
