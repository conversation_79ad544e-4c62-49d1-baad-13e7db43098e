from suds.client import Client

# 定义服务的 WSDL URL
wsdl_url = "http://10.239.128.28:8092/api/center.asmx?wsdl"

# 创建一个 Client 实例
client = Client(wsdl_url)

# 调用 getData 方法
# 创建 DtoGetData 参数实例
dto_get_data = client.factory.create("DtoGetData")
dto_get_data.timestamp = "2024-10-26T10:00:00"  # 示例时间戳
dto_get_data.random = "123456"
dto_get_data.panelSN = ["Panel_001", "Panel_002"]  # 示例列表

# 调用 getData 方法
response = client.service.getData(dto_get_data)
print("getData Response:", response)

# 调用 uploadData 方法
# 创建 DtoUploadData 参数实例
dto_upload_data = client.factory.create("DtoUploadData")
dto_upload_data.timestamp = "2024-10-26T10:00:00"  # 示例时间戳
dto_upload_data.random = "654321"
dto_upload_data_item = client.factory.create("DtoUploadDataAOIData")
dto_upload_data_item.pcbSN = "PCB_001"
dto_upload_data_item.line = "Line_A"
dto_upload_data_item.aoiType = "Type_1"
dto_upload_data_item.totalPoint = "100"
dto_upload_data_item.aoiPoint = "90"
dto_upload_data_item.opPoint = "10"
dto_upload_data_item.machine = "Machine_001"

# 将实例添加到 uploadData 的 data 属性中
dto_upload_data.data = [dto_upload_data_item]

# 调用 uploadData 方法
response = client.service.uploadData(dto_upload_data)
print("uploadData Response:", response)