# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2024/11/27 上午8:50
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import httpx
import requests

if __name__ == '__main__':
    api_url_data = "http://127.0.0.1:8081/mgmt/data"
    data_param = {}
    headers = {
        "Authorization": "Bearer 1",
        "Content-Type": "application/json"
    }

    ret = requests.post(api_url_data, json=data_param, headers=headers).json()
    print(f"接口响应：{ret}")

    ret = httpx.post(api_url_data, json=data_param, headers=headers).json()
    print(f"接口响应：{ret}")
