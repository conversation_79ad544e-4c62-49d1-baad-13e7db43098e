# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xmodule.py
# Time       ：2025/3/27 下午12:15
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import datetime
import json
import os
import shutil  # 导入 shutil 模块
from typing import Any

from common.xutil import log


class PersistentLimitedDict:
    """
    有限空间的Dict，支持持久化到本地文件
    """

    def __init__(self, capacity: int, storage_path: str = f"{os.getcwd()}/cache_path/limited_dict.json"):
        """
        初始化有限字典

        :param capacity: 最大容量
        :param storage_path: 存储文件路径
        """
        self.capacity = capacity
        self.storage_path = storage_path
        self.dict = {}
        self.keys_queue = []

        # 如果存储文件存在，则加载数据
        if os.path.exists(self.storage_path):
            self._load_from_file()

    def _save_to_file(self):
        """将当前数据保存到文件"""
        data = {
            'dict': self.dict,
            'keys_queue': self.keys_queue
        }
        with open(self.storage_path, 'w') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

    def _load_from_file(self):
        """从文件加载数据"""
        try:
            with open(self.storage_path, 'r') as f:
                data = json.load(f)
                self.dict = data.get('dict', {})
                self.keys_queue = data.get('keys_queue', [])
        except (json.JSONDecodeError, FileNotFoundError):
            # 如果文件损坏或读取失败，初始化空数据
            self.dict = {}
            self.keys_queue = []

    def add_item(self, key: str, value: Any):
        """
        添加键值对，如果达到容量限制会删除最早添加的项

        :param key: 键
        :param value: 值
        """
        if key in self.dict:
            # 如果键已存在，更新值并将键移到队列末尾
            self.keys_queue.remove(key)
        elif len(self.dict) >= self.capacity:
            # 如果达到容量限制，删除最早添加的键值对
            oldest_key = self.keys_queue.pop(0)
            del self.dict[oldest_key]

        # 添加新键值对
        self.dict[key] = value
        self.keys_queue.append(key)

        # 保存到文件
        self._save_to_file()

    def get_value(self, key: str, default_value: Any = None) -> Any:
        """
        获取键对应的值

        :param key: 键
        :param default_value: 如果键不存在返回的默认值
        :return: 键对应的值或默认值
        """
        return self.dict.get(key, default_value)

    def is_exist_item(self, item: str) -> bool:
        """
        检查字典中是否存在指定键

        :param item: 要检查的键
        :return: 如果键存在返回True，否则返回False
        """
        return item in self.dict

    def remove_item(self, item: str):
        """
        从字典中移除指定键值对

        :param item: 要移除的键
        """
        if self.is_exist_item(item):
            key_ix = self.keys_queue.index(item)
            del self.keys_queue[key_ix]
            del self.dict[item]
            self._save_to_file()

    def clear(self):
        """
        清空字典
        """
        self.dict.clear()
        self.keys_queue.clear()
        self._save_to_file()

    def __len__(self) -> int:
        """返回字典当前大小"""
        return len(self.dict)

    def __contains__(self, key: str) -> bool:
        """检查键是否存在"""
        return key in self.dict

    def __repr__(self) -> str:
        """返回字典的字符串表示"""
        return f"PersistentLimitedDict(capacity={self.capacity}, size={len(self)}, storage='{self.storage_path}')"


def clean_cache_folder(cache_path):
    # 读取limit_dict_file.json
    limit_file_path = os.path.join(cache_path, "limit_dict_file.json")

    try:
        with open(limit_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        log.info(f"Error: {limit_file_path} not found.")
        return
    except json.JSONDecodeError:
        log.info(f"Error: {limit_file_path} is not a valid JSON file.")
        return

    # 获取需要保留的文件列表
    files_to_keep = set(data.get("keys_queue", []))
    files_to_keep.add("limit_dict_file.json")  # 确保不删除json文件本身

    # 遍历cache_path文件夹
    for filename in os.listdir(cache_path):
        file_path = os.path.join(cache_path, filename)

        # 跳过需要保留的文件
        if filename in files_to_keep:
            continue

        # 删除不在保留列表中的文件
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                log.info(f"Deleted: {filename}")
        except Exception as e:
            log.info(f"Error deleting {filename}: {str(e)}")


def clean_old_folders_by_date(directory, days=30, dry_run=False):
    """
    清理指定目录下，基于子文件夹日期命名的旧文件夹。

    Args:
        directory (str):  要清理的根目录的路径。
        days (int):  旧文件夹的天数阈值。默认值：30天。
        dry_run (bool):  是否进行“试运行”。如果为 True，则只打印将要删除的文件夹，而不实际删除。默认值：False。

    Returns:
        int:  删除的文件夹数量。
    """

    deleted_folders_count = 0
    log.info(f"开始清理根目录: {directory}")
    log.info(f"清理 {days} 天以前的文件夹.")

    if not directory:
        return

    for folder_name in os.listdir(directory):
        folder_path = os.path.join(directory, folder_name)

        # 检查是否是目录
        if not os.path.isdir(folder_path):
            log.info(f"跳过非目录项: {folder_path}")
            continue

        # 尝试将文件夹名称解析为日期
        try:
            folder_date = datetime.datetime.strptime(folder_name, "%Y%m%d").date()
        except ValueError:
            log.info(f"跳过，文件夹名称不符合 YYYYMMDD 格式: {folder_name}")
            continue

        # 计算截止日期
        cutoff_date = datetime.date.today() - datetime.timedelta(days=days)

        # 比较文件夹日期和截止日期
        if folder_date < cutoff_date:
            log.info(f"文件夹 {folder_name} 日期早于 {cutoff_date}")
            if dry_run:
                log.info(f"[Dry-run] 将删除文件夹: {folder_path}")
            else:
                try:
                    shutil.rmtree(folder_path)  # 使用 shutil.rmtree 删除文件夹及其内容
                    log.info(f"已删除文件夹: {folder_path}")
                    deleted_folders_count += 1
                except OSError as e:
                    log.info(f"无法删除文件夹 {folder_path}: {e}")
                except Exception as e:
                    log.info(f"处理文件夹 {folder_path} 时发生错误: {e}")

    log.info(f"清理完成. 共删除 {deleted_folders_count} 个文件夹.")
    return deleted_folders_count
