# !/usr/bin/env python
# -*-coding:utf-8 -*-
"""
# File       : main.py
# Time       ：2023/4/17 上午9:46
# Author     ：sch
# version    ：python 3.8
# Description：百威
"""
import time
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import x_response, log, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

global_data = {}


class BaiWeiEngine(BaseEngine):
    version = {
        "title": "baiwei release v1.1.1.4",
        "device": "203B,400D,401B",
        "feature": ["登录", "条码校验", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-07 12:38  init
date: 2023-04-17 14:45  登录,条码校验,上传数据,设备状态
date: 2023-05-30 10:49  接口参数调整，详情见需求文档
""",
    }

    combo = {
        "board_side": {
            "item": [
                "板面",
                "板底",
                "板面+板底"
            ],
            "value": "板面",
            "ui_name": "板底板面"
        },
        "control_mode": {
            "ui_name": "controlMode",
            "item": [
                "sn_control",
                "bn_control",
            ],
            "value": "",
        }
    }

    form = {
        "api_host": {
            "ui_name": "接口地址",
            "value": "http://127.0.0.1:8081"
        },
        "order_id1": {
            "ui_name": "工单号(1轨)",
            "value": ""
        },
        "order_id2": {
            "ui_name": "工单号(2轨)",
            "value": ""
        },
        "device_sn": {
            "ui_name": "设备编码",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        operator = data_vo.get_value_by_cons_key("operator")

        token = global_data.get("token")
        if not token:
            return x_response("false", "未登录，请先登录")

        pcb_entity = data_vo.pcb_entity
        track_index = pcb_entity.track_index

        if track_index == 1:
            order_id = data_vo.get_value_by_cons_key("order_id1")
        else:
            order_id = data_vo.get_value_by_cons_key("order_id2")

        log.info(pcb_entity)

        sn_list = []
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_ng_code != "0":
                    comp_data_list.append({
                        "test_type": comp_entity.type,
                        "test_item": comp_entity.designator,
                        "test_result": "ok" if comp_entity.robot_result else "ng",
                        "test_result_mes": comp_entity.get_final_result("ok", "pass", "ng"),
                        "defect_code": comp_entity.repair_ng_code,
                        "defect_name": comp_entity.repair_ng_str
                    })

            sn_list.append({
                "no": board_no,
                "sn": barcode,
                "sn_pcb_side": pcb_entity.board_side,
                "sn_result": board_entity.get_final_result("ok", "pass", "ng"),
                "sn_remark": "",
                "sn_items": comp_data_list
            })

        start_time = str(pcb_entity.get_start_time())
        end_time = str(pcb_entity.get_end_time())

        param = {
            "type": "TestReportV2EX",
            "start_time": start_time,
            "auth_id": global_data.get("auth_id", -1),
            "end_time": end_time,
            "equipment_code": device_sn,
            "wo_no": order_id,
            "uni_code": f"{device_sn}_{int(time.time())}",
            "track": "Y" if track_index == 2 else "",
            "pcb_side": "1" if pcb_entity.board_side == "T" else "2",
            "operator": operator,
            "result": "pass" if pcb_entity.pcb_repair_result else "ng",
            "sn_list": sn_list,
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        test_report_url = f"{api_host}/api/AssemblyOperation/TestReportV2EX"

        ret = xrequest.RequestUtil.post_json(test_report_url, param, headers=headers)

        status = ret.get("res")
        msg = ret.get("reason")

        if not status:
            return x_response("false", f"mes接口异常，上传数据失败，error：{msg}")

        return x_response()

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        username, password = other_dao.get_login_info()

        api_host = other_dao.get_value_by_cons_key("api_host")
        login_url = f"{api_host}/api/Auth/GetToken"

        get_token_param = {
            "UserName": username,
            "Password": password
        }

        ret = xrequest.RequestUtil.post_json(login_url, get_token_param)
        error_info = ret.get("ErrorInfo")

        status = error_info.get("Status")

        if not status:
            # 登陆成功
            token = ret.get("Result").get("Token")
            global_data["token"] = token

        else:
            msg = error_info.get("Message")
            return x_response("false", f"mes接口异常，登录失败，error：{msg}")

        return x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        board_side = other_vo.get_value_by_cons_key("board_side")
        control_mode = other_vo.get_value_by_cons_key("control_mode")
        track_index = other_vo.get_track_index()

        token = global_data.get("token")
        if not token:
            return x_response("false", "未登录，请先登录")

        if track_index == 1:
            order_id = other_vo.get_value_by_cons_key("order_id1")
        else:
            order_id = other_vo.get_value_by_cons_key("order_id2")

        check_sn_list = []
        for ix, sn in enumerate(other_vo.list_sn()):
            check_sn_list.append({
                "equipment_code": device_sn,
                "track": "Y" if track_index == 2 else "",
                "wo_no": order_id,
                "no": ix + 1,
                "sn": sn,
                "sn_pcb_side": board_side,
            })

        param = {
            "type": "CheckSnV2EX",
            "control_mode": control_mode,
            "checkSnlist": check_sn_list
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }
        check_sn_url = f"{api_host}/api/AssemblyOperation/CheckSnV2EX"

        ret = xrequest.RequestUtil.post_json(check_sn_url, param, headers=headers)

        status = ret.get("res")
        msg = ret.get("reason")

        if not status:
            return x_response("false", f"mes接口异常，条码校验失败，error：{msg}")

        auth_id = ret.get('auth_id', -1)
        global_data["auth_id"] = auth_id
        self.log.info(f"auth id: {auth_id} 已缓存")

        return x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        token = global_data.get("token")
        if not token:
            return x_response("false", "未登录，请先登录")

        api_host = other_vo.get_value_by_cons_key("api_host")
        device_sn = other_vo.get_value_by_cons_key("device_sn")

        status_str = other_vo.get_device_status_str()

        if "safedoor" in status_str:
            status_str = "安全门"

        if status_str in ["开始检测", "进板", "出板"]:
            state = "1"
        elif status_str == "停止检查":
            state = "2"
        elif status_str in ["安全门", "调试"]:
            state = "3"
        else:
            # 报警
            state = "4"

        device_url = f"{api_host}/api/AssemblyOperation/UploadDeviceStatus"

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
        param = {
            "type": "UploadDeviceStatus",
            "equipment_code": device_sn,
            "start_time": time_now,
            "end_time": time_now,
            "uni_code": f"{device_sn}{int(time.time())}",
            "operation_items": [
                {"key": "machine_status", "value": state, "remark": status_str}
            ]
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }
        ret = xrequest.RequestUtil.post_json(device_url, param, headers=headers)

        status = ret.get("res")
        msg = ret.get("reason")

        if not status:
            return x_response("false", f"mes接口异常，上传设备状态失败，error：{msg}")

        return x_response()
