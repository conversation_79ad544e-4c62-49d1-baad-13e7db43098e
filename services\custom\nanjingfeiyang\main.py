# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/25 下午3:24
# Author     ：sch
# version    ：python 3.8
# Description： 南京飞洋
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "nanjingfeiyang release v1.0.0.1",
        "device": "AIS203,AIS303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-25 15:41  上传数据到Socket服务器
""", }

    form = {
        "socket_ip": {
            "ui_name": "SocketHost",
            "value": "",
        },
        "socket_port": {
            "ui_name": "SocketPort",
            "value": "",
        }
    }

    button = {
        "test_connect": {
            "ui_name": "testConnectSocket"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        socket_ip = data_vo.get_value_by_cons_key("socket_ip", not_null=True)
        socket_port = data_vo.get_value_by_cons_key("socket_port", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            comp_err_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_err_list.append(comp_entity.repair_ng_str)

            board_no = board_entity.board_no
            board_result = board_entity.get_repair_result("OK", "NG")

            board_data_list.append(f"拼板{board_no},{board_result},{'|'.join(comp_err_list)}")

        pcb_str = ";".join(board_data_list)
        xrequest.SocketUtil.send_data_to_socket_server(socket_ip, socket_port, pcb_str)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):

        if btn_vo.get_btn_key() == 'test_connect':
            socket_ip = btn_vo.get_value_by_cons_key("socket_ip", not_null=True)
            socket_port = btn_vo.get_value_by_cons_key("socket_port", to_int=True)

            is_connect = xrequest.SocketUtil.check_window_port(socket_ip, socket_port)
            if not is_connect:
                return self.x_response("false", f"socket服务器连接失败！")

        return self.x_response()
