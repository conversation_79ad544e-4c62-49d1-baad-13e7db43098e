# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/17 上午9:36
# Author     ：sch
# version    ：python 3.8
# Description：万家乐
"""
import time
from datetime import datetime
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import log, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

global_data = {}

limit_global_data = xutil.LimitedDict(200)


def get_utilization_time():
    """
    获取稼动时间, 设备运行时间
    :return: 稼动时间，设备运行时间
    """
    time_run_count = int(time.time() - global_data.get('device_run_time'))

    time_test_count = time_run_count - global_data.get('time_except_count', 0) - global_data.get('time_stop_count', 0)
    log.info(f"稼动时间：{time_test_count}  设备运行时间：{time_run_count}")

    return time_test_count, time_run_count


def cal_utilization_rate():
    """
    计算当前时间点的稼动率
    """
    # 计算运行时间
    log.info('----------稼动率计算中...')
    time_test_count, time_run_count = get_utilization_time()

    if not time_run_count:
        return 1

    utilization_rate = round(time_test_count / time_run_count, 2)

    log.info(f"稼动率：{utilization_rate}")

    return utilization_rate


board_side_map = {
    "板面": "1",
    "板底": "2",
    "板面+板底": "3",
}


class Engine(ErrorMapEngine):
    version = {
        "title": "wanjiale release v1.0.0.8",
        "device": "203，303，401",
        "feature": ["条码校验", "发送设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-17 17:45  done!
date: 2024-01-19 10:41  板面取界面上填写的
date: 2024-01-19 16:24  板面使用枚举值上传
date: 2024-01-31 10:01  优化缓存auth_id逻辑
date: 2024-02-02 17:36  增加输出`DEFECT_LOCATION`料号
date: 2024-07-19 17:38  修改安全门为报警状态+新增上传数据到警报接口
date: 2024-07-23 10:14  增加接口等待超时时间
date: 2024-07-24 17:34  bugfix
""", }

    combo = {
        "board_side": {
            "item": [
                "板面",
                "板底",
                "板面+板底"
            ],
            "value": "板面",
            "ui_name": "板底板面"
        },
    }

    other_form = {
        "api_host": {
            "ui_name": "接口地址",
            "value": "http://127.0.0.1:8081"
        },
        "device_sn": {
            "ui_name": "设备编码",
            "value": ""
        },
    }

    other_combo = {
        "api_timeout": {
            "ui_name": "接口等待超时时间(秒)",
            "item": ["1", "5", "10", "30", "50", "60", "100"],
            "value": "5"
        },
        "is_cron_upload": {
            "ui_name": "定时上传稼动率",
            "item": ["Yes", "No"],
            "value": "No"
        },
        "cron_frequency": {
            "ui_name": "上传间隔(秒)",
            "item": ["10", "60", "120", "300", "600", "1800", "3600", "7200"],
            "value": "120"
        },
        "control_mode": {
            "ui_name": "controlMode",
            "item": [
                "sn_control",
                "bn_control",
            ],
            "value": "",
        }
    }

    form = {
        "order_id1": {
            "ui_name": "工单号(1轨)",
            "value": ""
        },
        "order_id2": {
            "ui_name": "工单号(2轨)",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
    }

    def __init__(self):
        time_now = time.time()
        global_data["device_run_time"] = int(time_now)  # 设备的启动时间

    def init_main_window(self, main_window, other_vo: OtherVo):
        other_combo = main_window.config_data.get('other_combo')
        is_cron_upload = other_combo.get('is_cron_upload', {}).get('value')
        upload_time = other_combo.get('cron_frequency', {}).get('value')

        if is_cron_upload == "Yes":
            interval_cron = True
        else:
            interval_cron = False

        main_window.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        main_window.config_data["app_setting"]["custom_interval_time"] = int(upload_time)  # 3s
        self.log.info("init main window done!")

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        username, password = other_dao.get_login_info()

        api_host = other_dao.get_value_by_cons_key("api_host")
        api_timeout = other_dao.get_value_by_cons_key("api_timeout", to_int=True)

        login_url = f"{api_host}/api/Auth/GetToken"

        get_token_param = {
            "UserName": username,
            "Password": password
        }

        ret = xrequest.RequestUtil.post_json(login_url, get_token_param, timeout=api_timeout)
        error_info = ret.get("ErrorInfo")

        status = error_info.get("Status")
        log.info(f"status： {status}")

        if not status:
            # 登陆成功
            token = ret.get("Result").get("Token")
            global_data["token"] = token
            log.info(f"登录成功！")

        else:
            msg = error_info.get("Message")
            return self.x_response("false", f"mes接口异常，登录失败，error：{msg}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        board_side = other_vo.get_value_by_cons_key("board_side")
        control_mode = other_vo.get_value_by_cons_key("control_mode")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout", to_int=True)

        track_index = other_vo.get_track_index()

        token = global_data.get("token")
        if not token:
            return self.x_response("false", "未登录，请先登录！")

        if track_index == 1:
            order_id = other_vo.get_value_by_cons_key("order_id1")
        else:
            order_id = other_vo.get_value_by_cons_key("order_id2")

        check_sn_list = []

        sn_list = other_vo.list_sn()
        for ix, sn in enumerate(sn_list):
            check_sn_list.append({
                "no": ix + 1,
                "sn": sn,
                "sn_pcb_side": board_side_map.get(board_side, '1'),
            })

        param = {
            "type": "CheckSnV2",
            "equipment_code": device_sn,
            "track": "Y" if track_index == 2 else "",
            "wo_no": order_id,
            "control_mode": control_mode,
            "checkSnlist": check_sn_list
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }
        check_sn_url = f"{api_host}/api/AssemblyOperation/CheckSnV2"

        ret = xrequest.RequestUtil.post_json(check_sn_url, param, headers=headers, timeout=api_timeout)

        status = ret.get("res")
        msg = ret.get("reason")

        if not status:
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{msg}")

        auth_id = ret.get('auth_id', -1)
        # global_data["auth_id"] = auth_id

        for sn in sn_list:
            limit_global_data.add_item(sn, auth_id)

        self.log.info(f"sn list:{sn_list} auth id: {auth_id} 已缓存")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        operator = data_vo.get_value_by_cons_key("operator")
        board_side = data_vo.get_value_by_cons_key("board_side")
        api_timeout = data_vo.get_value_by_cons_key("api_timeout", to_int=True)

        token = global_data.get("token")
        if not token:
            return self.x_response("false", "未登录，请先登录！")

        pcb_entity = data_vo.pcb_entity

        self.log.info(pcb_entity)

        sn_list = []

        any_sn = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not any_sn and barcode:
                any_sn = barcode

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_ng_code != "0":
                    comp_data_list.append({
                        "test_type": comp_entity.type,
                        "test_item": comp_entity.designator,
                        "test_result": "ok" if comp_entity.robot_result else "ng",
                        "test_result_mes": comp_entity.get_final_result("ok", "pass", "ng"),
                        "defect_code": comp_entity.repair_ng_code,
                        "defect_name": comp_entity.repair_ng_str,
                        "DEFECT_LOCATION": comp_entity.part
                    })

            sn_list.append({
                "no": board_no,
                "sn_pcb_side": board_side_map.get(board_side, "1"),
                "sn": barcode,
                "sn_result": board_entity.get_final_result("ok", "pass", "ng"),
                "sn_remark": "",
                "sn_items": comp_data_list,
            })

        start_time = str(pcb_entity.get_start_time())
        end_time = str(pcb_entity.get_end_time())

        auth_id = limit_global_data.get_value(any_sn, -1)
        log.info(f"使用条码：{any_sn} 获取到缓存的 auth_id 为：{auth_id}")

        param = {
            "type": "TestReportV2",
            "auth_id": auth_id,
            "equipment_code": device_sn,
            "start_time": start_time,
            "end_time": end_time,
            "operator": operator,
            "result": pcb_entity.get_final_result("ok", "pass", "ng"),
            "sn_list": sn_list,
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        test_report_url = f"{api_host}/api/AssemblyOperation/TestReportV2"

        ret = xrequest.RequestUtil.post_json(test_report_url, param, headers=headers, timeout=api_timeout)

        status = ret.get("res")
        msg = ret.get("reason")

        if not status:
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{msg}")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        ret_res = self.x_response()

        token = global_data.get("token")
        if not token:
            return self.x_response("false", "未登录，请先登录！")

        api_host = other_vo.get_value_by_cons_key("api_host")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout", to_int=True)

        status_str = other_vo.get_device_status_str()

        old_status_code = other_vo.get_old_device_status_code()

        time_now = time.time()

        if "safedoor" in status_str or "安全门" in status_str:
            status_str = "安全门"

        if status_str == "开始检测":
            time_except = global_data.get("time_except", None)  # 异常时间点
            time_stop = global_data.get("time_stop", None)  # 停止检查时间点

            if time_except:
                time_except_delta = time_now - time_except
                global_data['time_except_count'] = int(global_data.get('time_except_count', 0) + time_except_delta)
                global_data['time_except'] = None  # 时间点用过了之后需要删掉

            if time_stop:
                time_stop_delta = time_now - time_stop
                global_data['time_stop_count'] = int(global_data.get('time_stop_count', 0) + time_stop_delta)
                global_data['time_stop'] = None  # 时间点用过了之后需要删掉

        elif status_str == "停止检查":
            global_data['time_stop'] = time_now
        elif status_str in ["进板", "出板"]:
            pass
        else:
            # 异常的
            global_data['time_except'] = time_now

        if status_str in ["开始检测", "进板", "出板"]:
            state = "1"
        elif status_str == "停止检查":
            state = "2"
        elif status_str in ["调试"]:
            state = "3"
        elif status_str in xcons.DEVICE_STATUS:
            # 报警
            state = "4"
        else:
            state = "0"

        device_url = f"{api_host}/api/AssemblyOperation/UploadDeviceStatus"

        tmp_datetime = datetime.now()

        time_now1 = tmp_datetime.strftime(xcons.FMT_TIME_DEFAULT)
        time_now2 = tmp_datetime.strftime(xcons.FMT_TIME_DEFAULT1)

        param = {
            "type": "UploadDeviceStatus",
            "equipment_code": device_sn,
            "start_time": time_now1,
            "end_time": time_now1,
            "uni_code": f"{device_sn}{int(time.time())}",
            "operation_items": [
                {"key": "machine_status", "value": state, "remark": status_str}
            ]
        }

        if state in ["1", "2", "3"]:
            warn_value = ""
        else:
            warn_value = old_status_code

        alarm_param = {
            "type": "SendWarnData ",
            "equipment_code": device_sn,
            "warn_time": time_now2,
            "param_name": "SpeedPV",
            "warn_value": warn_value,
            "warn_content": status_str
        }

        headers = {
            "Authorization": f"Bearer {token}"
        }

        if state != "0":
            ret = xrequest.RequestUtil.post_json(device_url, param, headers=headers, timeout=api_timeout)

            status = ret.get("res")
            msg = ret.get("reason")

            if not status:
                ret_res = self.x_response("false", f"mes接口异常，上传设备状态失败，error：{msg}")

            # 上传警报
            log.info(f"开始上传警报....")
            warn_url = f"{api_host}/api/AssemblyOperation/SendWarnData"

            ret = xrequest.RequestUtil.post_json(warn_url, alarm_param, headers=headers, timeout=api_timeout)

        else:
            log.warning(f"未知的设备状态，不需要上传数据！")

        return ret_res

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_host = other_vo.get_value_by_cons_key("api_host")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        api_timeout = other_vo.get_value_by_cons_key("api_timeout", to_int=True)

        activation_data_url = f"{api_host}/api/SfcsEcndoc/ActivationRateData"

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT1)

        time_test_count, time_run_count = get_utilization_time()
        utilization_rate = cal_utilization_rate()

        param = {
            "type": "ActivationRateData",
            "equipment_code": device_sn,
            "activation_time": time_now,
            "jiadong_time": str(time_test_count),
            "load_time": str(time_run_count),
            "activation_rate": str(int(utilization_rate * 100)),
        }

        token = global_data.get("token")
        if not token:
            log.warning("定时上传稼动率失败，error：未登录，请先登录！")
            return

        headers = {
            "Authorization": f"Bearer {token}"
        }

        xrequest.RequestUtil.post_json(activation_data_url, param, headers=headers, timeout=api_timeout)
