# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/29 上午9:08
# Author     ：wxc
# version    ：python 3.8
# Description：武汉海康LED工厂
"""

from datetime import datetime
from typing import Any
from common import xcons, xutil, xrequest, xglobal
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


def post_device_status(status: str, status_str: str,status_code: str, url: str, headers: dict) -> dict:
    """
    发送设备状态到mes
    """
    now = datetime.now()
    message_id = xutil.OtherUtil.get_origin_uuid4_str()
    status_param = {
        "msgId": message_id,
        "cldate": now.strftime(xcons.FMT_TIME_DEFAULT)+f".{now.microsecond // 1000:03d}",
        "runStatus": status
    }
    xglobal.global_data["last_status"] = status_str
    xglobal.global_data["last_status_code"] = status_code
    return xrequest.RequestUtil.post_json(url, status_param, headers=headers)


def upload_alarm(alarm_data: dict, api_url_fault: str, headers: dict, status_str: str,  status_code:str):
    """上传报警信息"""
    now = datetime.now()
    fail_param = {
        "msgId": xutil.OtherUtil.get_origin_uuid4_str(),
        "cldate": now.strftime(xcons.FMT_TIME_DEFAULT)+f".{now.microsecond // 1000:03d}",
        "alarmData": alarm_data
    }
    xglobal.global_data["last_status"] = status_str
    xglobal.global_data["last_status_code"] = status_code

    return xrequest.RequestUtil.post_json(api_url_fault, fail_param, headers=headers)


circle_list = xutil.CircularList(200)


class Engine(ErrorMapEngine):
    version = {
        "customer": ["武汉海康LED", "wuhanhaikang_led"],
        "version": "release v1.0.0.2",
        "device": "AIS40X,AIS63X",
        "feature": ["设备状态", "心跳上传", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-04-29 09:09  ATAOI_2019-36339：设备状态变化上传，设备报警上传，心跳上传,上传产量数据
date: 2025-05-09 15:11  cldate毫米保留3位，心跳上传增加自定义时间，设备状态使用V1
""", }

    other_form = {
        "api_url_hk": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://meshz-sit.hikvision.com.cn:8000/hiscada/dc/process",
        },
        "api_url_status": {
            "ui_name": "接口URL(设备状态)",
            "value": "http://meshz-sit.hikvision.com.cn:8000/hiscada/dc/runStatus",
        },
        "api_url_fault": {
            "ui_name": "接口URL(设备报警)",
            "value": "http://meshz-sit.hikvision.com.cn:8000/hiscada/dc/alarm",
        },
        "api_url_heart": {
            "ui_name": "接口URL(心跳)",
            "value": "http://meshz-sit.hikvision.com.cn:8000/hiscada/dc/heartbeat",
        },
        "authorization": {
            "ui_name": "Authorization头",
            "value": "Basic ODkwMC0wMTxxxxx",
        },
    }

    form = {
        "machine_id": {
            "ui_name": "设备ID(MachineId)",
            "value": "8900-0111-220541A",
        },

    }
    other_combo = {
        "cron_second": {
            "ui_name": "心跳间隔/s",
            "ui_name_en": "HeartBeatInterval/s",
            "item": ["10", "20", "30", "60", "120", "300", "600", "1500", "3000", "6000", "12000", "24000"],
            "value": "10",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        cron_second = other_vo.get_value_by_cons_key("cron_second")
        main_window.set_cron_setting(True, int(cron_second))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_hk = data_vo.get_value_by_cons_key("api_url_hk")
        authorization = data_vo.get_value_by_cons_key("authorization")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        now = datetime.now()
        review_path = data_vo.get_review_path()
        # 是否重复
        if not circle_list.is_exist_item(review_path):
            circle_list.add_item(review_path)
            headers = {
                "MachineId": machine_id,
                "Authorization": authorization
            }
            pcb_entity = data_vo.pcb_entity
            self.log.info(pcb_entity)

            if pcb_entity.pcb_repair_result:
                product_type = "OK"
            else:
                product_type = "NG"

            product = {
                "msgId": xutil.OtherUtil.get_origin_uuid4_str(),
                "cldate": now.strftime(xcons.FMT_TIME_DEFAULT)+f".{now.microsecond // 1000:03d}",
                "productQty": 1,
                "productType": product_type,
                "barcd": pcb_entity.pcb_barcode,
                "wknum": pcb_entity.order_id,
                "programName": pcb_entity.project_name
            }
            ret = xrequest.RequestUtil.post_json(api_url_hk, product, headers=headers)

            if ret.get("code") != "000000":
                return self.x_response("false", f"上传产量数据接口异常，error：{ret.get('message')}")
        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_status = other_vo.get_value_by_cons_key("api_url_status")
        api_url_fault = other_vo.get_value_by_cons_key("api_url_fault")
        authorization = other_vo.get_value_by_cons_key("authorization")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        last_status = xglobal.global_data.get("last_status")
        last_status_code = xglobal.global_data.get("last_status_code")
        status_str = other_vo.get_device_status_str()
        status_code = other_vo.json_data.get("statusCode")

        if "safedoor" in status_str:
            status_str = "安全门"

        if status_str in ["开始检测", "进板", "出板"]:
            status = "RUN"
        elif status_str == "停止检查":
            status = "STOP"
        else:
            # 报警
            status = "FAULT"

        headers = {
            "MachineId": machine_id,
            "Authorization": authorization
        }

        # 设备报警关闭时上传
        if status_str == "开始检测" and last_status not in ["开始检测", "进板", "出板", "停止检查"] and last_status:
            alarm_data = {
                "alarmCode": last_status_code,
                "alarmName": last_status,
                "alarmStatus": "CLOSE"
            }
            ret = upload_alarm(alarm_data, api_url_fault, headers, status_str, status_code)
            if ret.get("code") != "000000":
                return self.x_response("false", f"上传设备报警接口返回异常，error: {ret.get('message')}")
            self.log.info("设备报警关闭，设备报警上传接口成功！")
        ret_status = None
        # 当前设备状态发生改变则上传
        if status_str != last_status:
            ret_status = post_device_status(status, status_str,status_code, api_url_status, headers)

        # 当前设备报警与上次报警不一样时
        if status == "FAULT" and status_str != last_status:
            alarm_data = {
                "alarmCode": status_code,
                "alarmName": status_str,
                "alarmStatus": "OPEN"
            }
            ret = upload_alarm(alarm_data, api_url_fault, headers, status_code, status_str)
            if ret.get("code") != "000000":
                return self.x_response("false", f"上传设备报警接口返回异常，error: {ret.get('message')}")
            self.log.info("设备报警上传接口成功！")

        if not ret_status:
            self.log.info(f"设备状态仍是：{last_status}  设备状态变化接口，无需上传")
        elif ret_status.get("code") != "000000":
            return self.x_response("false", f"上传设备状态接口返回异常，error: {ret_status.get('message')}")
        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        """
        定时器上传心跳
        """
        api_url_heart = other_vo.get_value_by_cons_key("api_url_heart")
        authorization = other_vo.get_value_by_cons_key("authorization")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        headers = {
            "MachineId": machine_id,
            "Authorization": authorization
        }
        now = datetime.now()
        ret = xrequest.RequestUtil.post_json(api_url_heart, {
            "msgId": xutil.OtherUtil.get_origin_uuid4_str(),
            "cldate": now.strftime(xcons.FMT_TIME_DEFAULT)+f".{now.microsecond // 1000:03d}"
        }, headers=headers)

        if ret.get("code") != "000000":
            self.log.warning(f"上传心跳接口返回异常，error: {ret.get('message')}")
        else:
            self.log.info("上传心跳数据成功........")
