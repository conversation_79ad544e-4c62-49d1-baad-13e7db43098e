# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : get_data_from_db.py
# Time       ：2025/3/11 下午3:43
# Author     ：sch
# version    ：python 3.8
# Description：
"""
# import os
# import sqlite3
#
#
# def get_image_data_by_review_path(
#         review_path: str = '/home/<USER>/ais/results/160-145.zzw/20250109/T_20250109092133_1'
# ):
#     """
#     获取图像转换比，以及缩放比
#     """
#     db_file = ""
#     for item in os.listdir(review_path):
#         if item.endswith(".db"):
#             db_file = os.path.join(review_path, item)
#
#     if os.path.exists(db_file):
#         # 连接到数据库（如果不存在则会自动创建）
#         conn = sqlite3.connect(db_file)
#
#         # 创建游标对象
#         cursor = conn.cursor()
#
#         cursor.execute("""
#                 SELECT name
#                 FROM sqlite_master
#                 WHERE type='table'
#                 AND name=?
#             """, ("Report",))
#         is_exist = cursor.fetchone()
#         if is_exist:
#             # 查询数据
#             cursor.execute("SELECT pixelSizeWidth, pixelSizeHeight, imageScale  FROM Report;")
#             row = cursor.fetchone()
#         else:
#             print("Report 表不存在")
#             row = None
#
#         # 关闭连接
#         conn.close()
#         return row
#     else:
#         return None
#
#
# def table_exists(conn, table_name):
#     """判断表是否存在"""
#     cursor = conn.cursor()
#     # 查询 sqlite_master 表
#     cursor.execute("""
#         SELECT name
#         FROM sqlite_master
#         WHERE type='table'
#         AND name=?
#     """, (table_name,))
#     result = cursor.fetchone()
#     return result is not None
#
#
# if __name__ == '__main__':
#     ret = get_image_data_by_review_path()
#     print(f"{ret=}")
#     pixel_width = ret[0]
#     pixel_height = ret[1]
#     scale_size = ret[2]
#     print(pixel_width, type(pixel_width))
#     print(pixel_height, type(pixel_height))
#     print(scale_size, type(scale_size))

    # test2
    # 示例用法
    # with sqlite3.connect('/home/<USER>/ais/results/160-145.zzw/20250109/T_20250109092133_1/20250109092133.db') as conn:
    #     if table_exists(conn, 'Report1'):
    #         print("表 'users' 存在")
    #     else:
    #         print("表 'users' 不存在")
