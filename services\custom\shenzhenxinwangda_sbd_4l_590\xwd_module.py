# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xwd_module.py
# Time       ：2024/12/1 下午11:32
# Author     ：sch
# version    ：python 3.8
# Description：存储桶模块
"""

import boto3
from botocore.exceptions import ClientError

from common.xutil import log


def check_bucket_exists(s3_client, bucket_name):
    """检查桶是否存在"""
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        log.info(f"Bucket '{bucket_name}' exists.")
        return True
    except ClientError as e:
        print(e.response)
        error_code = int(e.response['Error']['Code'])
        if error_code == 404:
            log.info(f"Bucket '{bucket_name}' does not exist.")
            return False
        else:
            raise Exception(f"检查存储桶出错，error: {e.response}")  # 如果是其他错误，重新抛出异常


def create_bucket(s3_client, bucket_name):
    """创建一个新的桶（不设置任何特殊权限）"""
    try:
        response = s3_client.create_bucket(Bucket=bucket_name)
        log.info(f"Bucket '{bucket_name}' created successfully. response: {response}")
        return True
    except ClientError as e:
        log.info(f"Error creating bucket: {e}")
        return False


def upload_file_and_get_public_url(
        s3_client,
        file_path,
        bucket_name,
        object_key,
):
    """上传文件到指定的存储桶中，并返回文件的永久访问链接"""
    try:
        with open(file_path, 'rb') as data:
            response = s3_client.put_object(
                Bucket=bucket_name,
                Key=object_key,
                Body=data,
                ACL='public-read'  # 设置对象的 ACL 为公共可读
            )
        log.info(f"File uploaded successfully. response: {response}")

        # 构建非签名的公共 URL
        # public_url = urljoin(endpoint_url + '/', f'{bucket_name}/{object_key}')
        # log.info(f"Public URL: {public_url}")

        presigned_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': object_key},
            ExpiresIn=315360000  # URL有效期为10年
        )
        print(f"下载链接：{presigned_url}")
        return presigned_url
    except Exception as e:
        log.info(f"Error uploading file: {e}")
        return ""


def upload_image_to_s3(
        endpoint_url,
        aws_access_key_id,
        aws_secret_access_key,
        file_path,
        bucket_name,
        object_key,
        region_name="us-west-2",

) -> str:
    """
    主函数：上传图片到 S3 并获取永久访问链接

    :param endpoint_url: 服务器地址
    :param file_path: 要上传的文件路径
    :param bucket_name: S3 Bucket 的名称
    :param object_key: 在 S3 中保存文件的对象名称 (默认为文件名)
    :param aws_access_key_id: AWS Access Key ID
    :param aws_secret_access_key: AWS Secret Access Key
    :param region_name: AWS Region (默认是 'us-east-1')
    """
    log.info(f"upload_image_to_s3正在上传图片： src:{file_path}    dst:{object_key}")
    # 创建 S3 客户端，并指定自定义终端节点和其他配置选项
    s3_client = boto3.client(
        's3',
        endpoint_url=endpoint_url,  # 自定义终端节点
        # config=Config(
        #     s3={'addressing_style': 'path'},  # 使用路径样式 URL
        #     # signature_version='s3v4'  # 如果适用，可以指定签名版本
        # ),
        region_name=region_name,  # 如果适用，可以指定区域名称
        aws_access_key_id=aws_access_key_id,  # 替换为你的访问密钥
        aws_secret_access_key=aws_secret_access_key  # 替换为你的秘密密钥
    )

    # good --------------
    # s3_client = boto3.client(
    #     's3',
    #     endpoint_url=endpoint_url_,  # 如果是 AWS S3，则不需要此参数
    #     config=Config(
    #         s3={'addressing_style': 'path'},  # 使用路径样式 URL
    #         # signature_version='s3v4'  # 如果适用，可以指定签名版本
    #     ),
    #     region_name='us-west-2',  # 替换为你的实际区域
    #     aws_access_key_id=aws_access_key_id_,  # 替换为你的访问密钥
    #     aws_secret_access_key=aws_secret_access_key_  # 替换为你的秘密密钥
    # )
    # good --------------

    print("链接成功", s3_client)

    # 检查桶是否存在，如果不存在则创建

    is_exists = check_bucket_exists(s3_client, bucket_name)
    print(is_exists)

    if not check_bucket_exists(s3_client, bucket_name):
        if not create_bucket(s3_client, bucket_name):
            log.warning("Failed to create bucket. Exiting.")
            return ""

    # 上传文件并获取永久访问链接
    return upload_file_and_get_public_url(
        s3_client,
        file_path,
        bucket_name,
        object_key,
    )


# # 示例调用
if __name__ == "__main__":
    endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
    aws_access_key_id_ = "enp6eA=="
    aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"

    file_path_ = './COMP1039_1039.png'  # 替换为你的图片路径
    bucket_name_ = 'my-new-bucket'
    object_key_ = '20241216/COMP1039_1039_test13.png'

    ret_url = upload_image_to_s3(
        endpoint_url_,
        aws_access_key_id_,
        aws_secret_access_key_,
        file_path_,
        bucket_name_,
        object_key_
    )
    if ret_url:
        print(f"Image uploaded and available at: {ret_url}")
