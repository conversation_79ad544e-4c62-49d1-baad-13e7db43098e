# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/1 下午3:36
# Author     ：sch
# version    ：python 3.8
# Description：抚州比亚迪
"""
import binascii
import json
import os
from typing import Any

from common import xutil, xrequest, xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

global_data = {}


def hex16_to_str(hex_str) -> str:
    """
    16进制转换成字符串
    """
    if hex_str:
        log.info(f"16进制：{hex_str}")
        hex1 = hex_str.encode("utf-8")
        str_bin = binascii.unhexlify(hex1)

        s3 = str_bin.replace(b'\x00', b'')
        s4 = s3.decode('utf-8')

        log.info(f"转换后：{s4}")

    else:
        s4 = ""

    return s4


def parse_rfid_data(rfid_data: str, start_ix: int, end_ix: int, parse_data_number: str = "0"):
    """
    解析rfid数据
    :param parse_data_number:
    :param rfid_data:
    :param start_ix:
    :param end_ix:
    :return:
    """
    if parse_data_number == "1":
        sn_hex = rfid_data[start_ix:end_ix]
        new_sn = hex16_to_str(sn_hex)
    elif parse_data_number == "2":
        data_hex = hex16_to_str(rfid_data)
        sn_hex = data_hex[start_ix:end_ix]
        new_sn = hex16_to_str(sn_hex)
    else:
        new_sn = rfid_data

    log.info(f"parse data: {new_sn}")

    return new_sn


class Engine(BaseEngine):
    version = {
        "title": "fuzhoubiyadi release v1.0.0.14",
        "device": "401B",
        "feature": ["从Mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-01 15:34  init
date: 2023-08-09 09:10  解析RFID数据
date: 2023-08-15 19:40  NG RELEASE
date: 2023-08-16 11:13  条码状态为3的不发送数据到mes
date: 2023-09-07 09:31  修改rfid数据解析规则
date: 2023-11-06 16:03  State改成复判后的结果
date: 2023-12-15 10:57  增加从mes切换板式功能
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "operator_account": {
            "ui_name": "操作员",
            "value": "OP130-1"
        },
        # "station": {
        #     "ui_name": "工站号",
        #     "value": "OP130-1"
        # },
        # "layer": {
        #     "ui_name": "",
        #     "value": "OP130-1"
        # },
    }

    other_form = {
        "start_ix": {
            "ui_name": "托盘号起始位",
            "value": "0"
        },
        "end_ix": {
            "ui_name": "托盘号结束位",
            "value": "7"
        },
        "valid_len": {
            "ui_name": "RFID长度校验",
            "value": "1"
        },
        "station_name": {
            "ui_name": "工站名",
            "value": "OP130-1"
        },
        "attribute_code": {
            "ui_name": "attributeCode",
            "value": "PCBTYPE"
        },
    }

    other_combo = {
        "parse_data_number": {
            "ui_name": "解析几次RFID数据",
            "item": ["0", "1", "2"],
            "value": "0"
        }
    }

    path = {
        "project_map_path": {
            "ui_name": "料号映射文件路径",
            "value": ""
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        start_ix = other_vo.get_value_by_cons_key("start_ix")
        end_ix = other_vo.get_value_by_cons_key("end_ix")
        valid_len = other_vo.get_value_by_cons_key("valid_len")
        parse_data_number = other_vo.get_value_by_cons_key("parse_data_number")

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        try:
            valid_len = int(valid_len)
        except Exception as err:
            self.log.info(f"[RFID长度校验]必须为数字！err:{err}")

        get_sn_url = f"{api_url}/api/Home/GetSerialNumberByCarrierCode"

        pcb_sn = other_vo.get_pcb_sn()

        if len(pcb_sn) < valid_len:
            return self.x_response("false", f"没有获取到RFID数据！")

        new_sn = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        param = {
            "Token": xutil.DateUtil.get_datetime_now(),
            "CarrierCode": new_sn,  # 托盘号
        }
        ret = xrequest.RequestUtil.post_json(get_sn_url, param)

        if ret.get("ReturnCode"):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('ErrorMessage')}")

        serial_number = ret.get('Data', "")  # 序列号

        return self.x_response("true", serial_number)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        """
        条码校验时的状态
        if statusCode == 0:
            状态1
        elif statusCode < -100 or statusCode > 0:
            状态3
        else:
            状态2

        1. 状态1       正常状态，进板检测                      mes接口返回：0
        2. 状态2       状态异常，停机报警                      mes接口返回：-100~0
        3. 状态3       直接流板，不需要检测也不需要有记录         mes接口返回：<-100     >0
        :param other_vo:
        :param other_param:
        :return:
        """
        api_url = other_vo.get_value_by_cons_key("api_url")
        # station = other_dao.get_value_by_cons_key("station")

        start_ix = other_vo.get_value_by_cons_key("start_ix")
        end_ix = other_vo.get_value_by_cons_key("end_ix")
        parse_data_number = other_vo.get_value_by_cons_key("parse_data_number")

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        sn_list = other_vo.list_sn()
        if len(sn_list) < 2:
            return self.x_response("false", f"条码数量不足！")

        pcb_sn = sn_list[0]

        carry_sn_str = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        check_url = f"{api_url}/api/Home/CheckSerialNumberState"
        for sn in sn_list[1:]:
            check_param = {
                "stationNumber": "OP130-1",
                "serialNumber": sn,
                "layer": 1,
            }
            ret = xrequest.RequestUtil.post_json(check_url, check_param)

            return_code = ret.get("ReturnCode", -1)
            self.log.info(f"return code: {return_code}")

            if return_code < -100 or return_code > 0:
                ret_data = {
                    "code": 200,
                    "msg": "NG_RELEASE, 板子直接流下去, 不做其他处理"
                }
                status_code = 3
                ret_res = self.x_response("true", json.dumps(ret_data, ensure_ascii=False))
            elif return_code == 0:
                status_code = 1
                ret_res = self.x_response()
            else:
                status_code = 2
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('ErrorMessage')}")

            if sn:
                # 记录最近10块板子的状态
                carry_sn_status = global_data.get('carry_sn_status', {})
                carry_sn_list = global_data.get('carry_sn_list', [])

                carry_sn_status[carry_sn_str] = status_code
                carry_sn_list.append(carry_sn_str)

                carry_sn_list = carry_sn_list[-10:]

                all_status_list = list(carry_sn_status.keys())

                for k in all_status_list:
                    if k not in carry_sn_list:
                        del carry_sn_status[k]

                global_data["carry_sn_status"] = carry_sn_status
                global_data["carry_sn_list"] = carry_sn_list
                self.log.info(f"托盘号的状态【{status_code}】已缓存")

            return ret_res

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        operator_account = data_vo.get_value_by_cons_key("operator_account")
        station_name = data_vo.get_value_by_cons_key("station_name")
        start_ix = data_vo.get_value_by_cons_key("start_ix")
        end_ix = data_vo.get_value_by_cons_key("end_ix")
        parse_data_number = data_vo.get_value_by_cons_key("parse_data_number")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        pcb_sn = pcb_entity.pcb_barcode
        carry_sn_str = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        carry_sn_status = global_data.get("carry_sn_status", {})
        self.log.info(f"carry sn status: {carry_sn_status}")
        sn_status = carry_sn_status.get(carry_sn_str, 888)  # 如果条码状态为3，则不发送mes

        data_url = f"{api_url}/api/Home/UploadStateAndMeasurementData"

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_list = []
            ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                ix += 1
                repair_ng_code = comp_entity.repair_ng_code
                comp_list.append({
                    "MeasureName": comp_entity.designator,
                    "MeasureValue": "",
                    "TestStepSequence": ix,
                    "FailureCode": repair_ng_code if comp_entity.is_repair_ng() else "",
                    "State": 1 if comp_entity.is_repair_ng() else 0,
                })

            data_param = {
                "stationNumber": station_name,
                "serialNumber": barcode,
                "layer": 1,
                "state": board_entity.get_repair_result(0, 1),
                "measurementDataArray": comp_list,
                "operatorAccount": operator_account,
                "cycleTime": pcb_entity.get_cycle_time(),
                "productionDateTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "Token": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE),
            }

            if sn_status != 3:
                ret = xrequest.RequestUtil.post_json(data_url, data_param)
                if ret.get("ReturnCode"):
                    return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('ErrorMessage')}")
            else:
                self.log.warning(f"条码状态为3，不发送数据到mes！")

        return self.x_response()

    def get_project_name_by_sn(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        api_url = other_vo.get_value_by_cons_key("api_url")
        start_ix = other_vo.get_value_by_cons_key("start_ix")
        end_ix = other_vo.get_value_by_cons_key("end_ix")
        valid_len = other_vo.get_value_by_cons_key("valid_len")
        parse_data_number = other_vo.get_value_by_cons_key("parse_data_number")
        attribute_code = other_vo.get_value_by_cons_key("attribute_code")
        project_map_path = other_vo.get_value_by_cons_key("project_map_path")

        try:
            start_ix = int(start_ix)
        except Exception as err:
            self.log.info(f"[托盘号起始位]必须为数字！err:{err}")

        try:
            end_ix = int(end_ix)
        except Exception as err:
            self.log.info(f"[托盘号结束位]必须为数字！err:{err}")

        try:
            valid_len = int(valid_len)
        except Exception as err:
            self.log.info(f"[RFID长度校验]必须为数字！err:{err}")

        get_sn_url = f"{api_url}/api/Home/GetSerialNumberByCarrierCode"

        pcb_sn = other_vo.get_barcode()

        if len(pcb_sn) < valid_len:
            return self.x_response("false", f"没有获取到RFID数据！")

        new_sn = parse_rfid_data(pcb_sn, start_ix, end_ix, parse_data_number)

        param = {
            "Token": xutil.DateUtil.get_datetime_now(),
            "CarrierCode": new_sn,  # 托盘号
        }

        # 1. 使用托盘号从mes获取序列号
        ret = xrequest.RequestUtil.post_json(get_sn_url, param)

        if ret.get("ReturnCode"):
            return self.x_response("false", f"mes接口异常，根据托盘号获取序列号失败，error：{ret.get('ErrorMessage')}")

        serial_number = ret.get('Data', "")  # 序列号

        # 2. 使用序列号从mes获取料号
        get_project_url = f"{api_url}/api/Home/GetProductTypeBySerialNumber"
        get_project_name_param = {
            "Token": xutil.DateUtil.get_datetime_now(),
            "serialNumber": serial_number,
            "attributeCode": attribute_code
        }
        ret = xrequest.RequestUtil.post_json(get_project_url, get_project_name_param)

        if ret.get("ReturnCode"):
            return self.x_response("false", f"mes接口异常，根据序列号获取料号失败，error：{ret.get('ErrorMessage')}")

        product_type = ret.get('Data', "")  # 料号

        # 3. 使用料号从本地映射文件获取板式名
        project_map_file = f"{project_map_path}/product_type_map.json"
        if not os.path.exists(project_map_file):
            return self.x_response("false", f"映射文件：{project_map_file}不存在！")

        try:
            project_map_data = xutil.FileUtil.load_json_file(project_map_file)
        except Exception as err:
            m = f"json格式有误，请将[{project_map_file}]里的文件内容复制到在线网站（如：https://www.bejson.com/）检查Json格式是否正确！error，{err}"
            return self.x_response("false", m)

        project_name = project_map_data.get(product_type, '')
        if not project_name:
            return self.x_response("false", f"请在映射文件[{project_map_file}]配置料号[{product_type}]与板式名的映射关系，以用于自动切换程序！")

        return self.x_response("true", project_name)
