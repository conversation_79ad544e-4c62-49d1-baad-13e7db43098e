# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/error_code_map_widget.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ErrorCodeWidget(object):
    def setupUi(self, ErrorCodeWidget):
        ErrorCodeWidget.setObjectName("ErrorCodeWidget")
        ErrorCodeWidget.resize(910, 523)
        self.verticalLayout = QtWidgets.QVBoxLayout(ErrorCodeWidget)
        self.verticalLayout.setContentsMargins(1, 1, 1, 1)
        self.verticalLayout.setSpacing(1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame = QtWidgets.QFrame(ErrorCodeWidget)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.combo_device_mode = QtWidgets.QComboBox(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.combo_device_mode.sizePolicy().hasHeightForWidth())
        self.combo_device_mode.setSizePolicy(sizePolicy)
        self.combo_device_mode.setObjectName("combo_device_mode")
        self.combo_device_mode.addItem("")
        self.combo_device_mode.addItem("")
        self.combo_device_mode.addItem("")
        self.combo_device_mode.addItem("")
        self.horizontalLayout.addWidget(self.combo_device_mode)
        self.verticalLayout.addWidget(self.frame)
        self.scrollArea = QtWidgets.QScrollArea(ErrorCodeWidget)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 906, 473))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents)
        self.verticalLayout_2.setContentsMargins(2, 2, 2, 2)
        self.verticalLayout_2.setSpacing(2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.error_table_widget = QtWidgets.QTableWidget(self.scrollAreaWidgetContents)
        self.error_table_widget.setObjectName("error_table_widget")
        self.error_table_widget.setColumnCount(8)
        self.error_table_widget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.error_table_widget.setHorizontalHeaderItem(7, item)
        self.verticalLayout_2.addWidget(self.error_table_widget)
        self.frame_2 = QtWidgets.QFrame(self.scrollAreaWidgetContents)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_2.sizePolicy().hasHeightForWidth())
        self.frame_2.setSizePolicy(sizePolicy)
        self.frame_2.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.frame_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_2.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.btn_save = QtWidgets.QPushButton(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_save.sizePolicy().hasHeightForWidth())
        self.btn_save.setSizePolicy(sizePolicy)
        self.btn_save.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.btn_save.setObjectName("btn_save")
        self.horizontalLayout_2.addWidget(self.btn_save)
        self.btn_import_config = QtWidgets.QPushButton(self.frame_2)
        self.btn_import_config.setObjectName("btn_import_config")
        self.horizontalLayout_2.addWidget(self.btn_import_config)
        self.btn_output_config = QtWidgets.QPushButton(self.frame_2)
        self.btn_output_config.setObjectName("btn_output_config")
        self.horizontalLayout_2.addWidget(self.btn_output_config)
        self.verticalLayout_2.addWidget(self.frame_2)
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout.addWidget(self.scrollArea)

        self.retranslateUi(ErrorCodeWidget)
        QtCore.QMetaObject.connectSlotsByName(ErrorCodeWidget)

    def retranslateUi(self, ErrorCodeWidget):
        _translate = QtCore.QCoreApplication.translate
        ErrorCodeWidget.setWindowTitle(_translate("ErrorCodeWidget", "自定义Mes不良代码"))
        self.label.setText(_translate("ErrorCodeWidget", "机型:"))
        self.combo_device_mode.setItemText(0, _translate("ErrorCodeWidget", "AIS203/AIS303/AIS40X/AIS50x"))
        self.combo_device_mode.setItemText(1, _translate("ErrorCodeWidget", "AIS301"))
        self.combo_device_mode.setItemText(2, _translate("ErrorCodeWidget", "AIS201"))
        self.combo_device_mode.setItemText(3, _translate("ErrorCodeWidget", "AIS63X"))
        item = self.error_table_widget.horizontalHeaderItem(0)
        item.setText(_translate("ErrorCodeWidget", "机型"))
        item = self.error_table_widget.horizontalHeaderItem(1)
        item.setText(_translate("ErrorCodeWidget", "默认Code"))
        item = self.error_table_widget.horizontalHeaderItem(2)
        item.setText(_translate("ErrorCodeWidget", "默认描述"))
        item = self.error_table_widget.horizontalHeaderItem(3)
        item.setText(_translate("ErrorCodeWidget", "自定义Code"))
        item = self.error_table_widget.horizontalHeaderItem(4)
        item.setText(_translate("ErrorCodeWidget", "自定义描述"))
        item = self.error_table_widget.horizontalHeaderItem(5)
        item.setText(_translate("ErrorCodeWidget", "整体上传Mes"))
        item = self.error_table_widget.horizontalHeaderItem(6)
        item.setText(_translate("ErrorCodeWidget", "单个上传Mes"))
        item = self.error_table_widget.horizontalHeaderItem(7)
        item.setText(_translate("ErrorCodeWidget", "配置项3"))
        self.btn_save.setText(_translate("ErrorCodeWidget", "保存"))
        self.btn_import_config.setText(_translate("ErrorCodeWidget", "导入配置文件"))
        self.btn_output_config.setText(_translate("ErrorCodeWidget", "导出配置文件"))
