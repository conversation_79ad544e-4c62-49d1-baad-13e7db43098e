{"0": {"barcode": "", "board_no": "0", "robot_result": false, "user_result": true, "final_result": "REPASS", "comp_total_number": 216, "comp_robot_ng_number": 0, "comp_repair_ng_number": 0, "comp_data": {"ddd4e541-e3ab-4960-bc17-97e9835445b8": {"part": "", "image_path": "/home/<USER>/aoi/run/results/907_024.00133/20230111/B_20230111054409473_1_NG/images/ng/SolderJoint/0/COMP1000_1000.jpg", "robot_ng_str": "未出脚", "type": "SolderJoint", "designator": "COMP1000", "robot_ng_code": "12", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "63.90", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "17.88", "result": "12", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "31.91", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.06", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "REPASS"}, "dc6617fd-dba8-4808-b28b-266457a21107": {"part": "", "image_path": "/home/<USER>/aoi/run/results/907_024.00133/20230111/B_20230111054409473_1_NG/images/ng/SolderJoint/0/COMP1060_1060.jpg", "robot_ng_str": "未出脚", "type": "SolderJoint", "designator": "COMP1060", "robot_ng_code": "12", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "80.48", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.52", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "41.11", "result": "12", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "26.48", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "REPASS"}, "10a36061-5601-4385-91e8-86fc63723a3d": {"part": "", "image_path": "/home/<USER>/aoi/run/results/907_024.00133/20230111/B_20230111054409473_1_NG/images/ng/Chip/0/COMP1286_1286.jpg", "robot_ng_str": "漏件", "type": "Chip", "designator": "COMP1286", "robot_ng_code": "1", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "41.57", "result": "1", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "REPASS"}, "13efd8aa-bb1f-44a2-91d8-2ae9db8bd37c": {"part": "", "image_path": "/home/<USER>/aoi/run/results/907_024.00133/20230111/B_20230111054409473_1_NG/images/ng/SolderJoint/0/COMP1018_1263.jpg", "robot_ng_str": "连锡", "type": "SolderJoint", "designator": "COMP1018", "robot_ng_code": "14", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.77", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.82", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "0.25", "result": "14", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "5.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.34", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "REPASS"}, "2bf0511d-dcb7-429b-a7a8-6b2d1ebcdc53": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1036", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.10", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.71", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.42", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "0a705988-dbf1-4b85-8344-57b431c6d8f3": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1042", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "84.25", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.64", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "7.26", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "4.12", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "76a417b6-8b63-44e4-b910-edf86d33e88d": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1012", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "62.65", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.98", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.14", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c8170d59-4b0a-4e4f-a111-577c88ca6a0e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1021", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "82.50", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.64", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "4.13", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "4.60", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2c6805fb-6bd8-492f-81dc-9b32f3dda3a7": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1006", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "53.74", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.63", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.11", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.68", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "b5626a01-e5dc-4bd8-8fa2-27ea51649e48": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1033", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.25", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.74", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.49", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "791540d5-9af6-4d93-a28b-948e4f4d87a0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1009", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "81.54", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.35", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "18.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e6fd0ff8-1a5a-472b-916f-4dc41eef3e21": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1066", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "88.35", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "25.25", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.01", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7358e143-e9ce-4202-84a9-b0ef754892c0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1045", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "70.82", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.91", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "20.80", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.40", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "a091ac4a-e5bb-4c63-a426-48ca7e4dafb1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1048", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "68.90", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.53", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "30.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.19", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2100d25c-2e26-4f38-b13e-0d03df1597cb": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1024", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "75.92", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.71", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "fd595bcc-0221-48c5-a08b-7a315fd1d2b8": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1030", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.21", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.06", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.26", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "465ce32d-e40c-4106-a69c-a5b61a158ac0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1063", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "81.99", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.94", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "20.47", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "24.89", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "25822ec4-e731-45a2-9df7-ad4dd90efd92": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1039", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "85.87", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "95.93", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "4.57", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6cb0865c-5768-4286-95a1-0674b654d562": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1027", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "73.18", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.93", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.31", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.75", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "47ca087b-8916-4284-a0cf-39a14f91e4dc": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1069", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "90.64", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.27", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "ac7f5f51-07f6-4ba8-a7c9-41516fd69455": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1075", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "80.49", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "11.40", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.30", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "53cc7b4f-9772-477b-912e-f65223ad05b6": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1057", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.94", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.62", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "4.17", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.83", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "0ce03cce-ddf3-4fda-8f80-cabd2f00f311": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1072", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.38", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.85", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "30.37", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.52", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7ef2f7a2-b1fa-43b1-8504-0e9fb82ff75f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1078", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.31", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.52", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.38", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "24f3f2dc-994c-436b-948d-56156648d20c": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1093", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "69.70", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.60", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.67", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.15", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "911a786e-98e5-4017-bdd9-fc089291476f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1144", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "86.07", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.94", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "11.22", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d569941a-7f40-484d-8387-456d08683b60": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1102", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.46", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.34", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "19.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.48", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "377a8fe8-7c80-4a4c-8af2-b5b8724060eb": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1081", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "69.74", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "44.37", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "25.14", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "19.74", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6520097a-eefa-4316-91cd-d2cb04bfc671": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1135", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "83.98", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "66.71", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.84", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "abf52af4-f955-4984-906b-6180e9f81d6e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1096", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "73.79", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.92", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "39.26", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "23.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "185ed97d-e33f-460e-8fcc-ecce34aaa913": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1105", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "64.07", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "66.75", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.38", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.63", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d1737737-2e9c-445a-8980-2eef71909855": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1087", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "86.47", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.98", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4d67bd2b-7366-4694-b4c7-e6bfea8213a9": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1054", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "55.30", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.21", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.40", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "44ee1b96-ba76-4452-bd75-6950a86ae9c6": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1246", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9374794d-25fb-4df4-991c-4ed8155067db": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1141", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "57.29", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.66", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "24.96", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "22.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7c479f8c-1497-4610-b381-8b5ccc95789e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1114", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.38", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "97.88", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.95", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.71", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "19.73", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9fdcb636-44c7-4a22-8ff8-bd143e63384e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1111", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.45", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.65", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.12", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.92", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e3c883fd-fc08-4e5b-90e8-953ff331ae59": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1250", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.84", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2545a6cb-b8c6-4845-bc90-908508b08023": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1248", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "93.04", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3568c109-6ebb-4e6c-8693-5f95446c52f6": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1254", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.57", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "cf95392d-1d24-4843-a14e-0276756a0962": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1147", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.42", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.72", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "26.87", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.18", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "38ca6b7e-ef58-40a9-b16f-2b9cad1a6ac0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1256", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.93", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6153ec0d-2d1b-4638-8b19-e62e15044270": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1165", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.43", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.81", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.79", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "14.84", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "40499536-7c96-4e1c-ae64-71a14a6c9499": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1252", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "98.59", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9f7481f3-676b-41e1-bb85-bec208ef811f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1258", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.91", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2556d576-09c0-48e7-b78c-4ccdfdd046ce": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1262", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.47", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2215ff1d-b076-4e5b-9d75-ca8aa3107bc9": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1266", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.96", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6ea0c074-0439-4c2a-b495-f8c2c7025be0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1264", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.79", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e8120285-2144-4dcc-8aba-f489503ea0ee": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1260", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "79cfd885-c8a7-4d8f-ba02-19d1f3ccd117": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1268", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "157d4926-1705-447f-b685-d5c17b64a3e9": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1150", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.39", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "97.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "21.69", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.71", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3b0f8428-fc85-41f2-af61-85075c61497b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1156", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.45", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.62", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "22.98", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.88", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "a28d22e7-ef7e-47b9-a545-05107180f08b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1270", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "681d5a1e-59b0-42f5-b047-100fa3015b3b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1272", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "cac79290-5c5f-425c-b350-7e26b6a94e75": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1276", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "98.35", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "67257740-6732-4996-a7c1-c3c9c8ca3204": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1278", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.35", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2b866fd6-365d-42c0-8453-67ca5bae5166": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1284", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e1ebd927-a636-470c-88ae-8a0a3b5eeb95": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1274", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.63", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "bf355670-6d5b-4699-a3be-fb2dd67ded53": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1280", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "98.73", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "ee5e7955-2bf2-45e9-92ff-9fb0ae4ddf5c": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1247", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "b047e84f-6af7-4036-afb1-340e2f52fba0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1292", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.89", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "eef97884-66ea-420c-90b1-dd5425a76101": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1282", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "98.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2df3086c-a4a7-40cb-bbff-1567ae792b79": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1249", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "264cb5fc-130c-422d-90aa-07d9e6f55fc2": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1290", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.47", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "5917acd5-4f0a-4418-8c96-6a0650fedea2": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1253", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "b061af4a-6750-4811-a52b-0334096b51c9": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1294", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6128e3d5-6e44-42a3-bac8-0f51e3777e27": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1255", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "dd354229-53ee-4a95-93fb-44b1454ae61d": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1180", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "71.25", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "91.74", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.20", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.14", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "710cb68f-5258-43fd-b827-3d8e49a90899": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1177", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "64.96", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.25", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.92", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8868a227-ebcd-4c3f-b7d7-522da796c047": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1174", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "56.32", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.55", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.94", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "28.55", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.52", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "319164e7-3fde-49df-8944-0409f3ca0e6e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1171", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "57.12", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.87", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.63", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.88", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e23234ba-54b7-4582-a7fe-b5feee501345": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1168", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "85.51", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "90.11", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.28", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "670dd8f7-c095-48cd-89c8-cff90df25b6a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1251", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.95", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "58d14118-4de7-464f-9f0e-7938a80439e4": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1123", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "73.46", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.92", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.02", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d007c8a7-8228-4451-bd96-4743a8eb0197": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1186", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "62.39", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.67", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "27.01", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.62", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6da8ee79-71c2-4ebd-8083-e4b90d3cd887": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1225", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.98", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.93", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "30.18", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "20.84", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7c62f665-52bd-4298-a66c-645bcc00caa9": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1228", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "86.16", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "30.34", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "17.28", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "cafd803d-0902-429c-84d9-00af626015a0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1204", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "82.16", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "27.42", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.83", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "706e0962-29ea-498f-8dfa-af1ffd74e601": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1219", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "68.39", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.96", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "4.18", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.01", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "87c753a6-a701-430e-898a-7d814cf84371": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1213", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.44", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.74", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "19.93", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "21.49", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "ad3df4f5-eba1-4c94-81bd-732b983342c7": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1237", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "62.13", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "26.55", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "86d3255e-224b-4ed4-aa80-ef36f415e82b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1207", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "85.88", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.80", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "0.89", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.18", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7550d399-ae7b-4302-ac45-fe32a13d5754": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1240", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.40", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "11.54", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "4.91", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "85cd40b3-2833-4884-9878-b3abee6ca368": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1138", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.93", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.91", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.03", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.64", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2906d356-9160-4799-8df0-1c3f255c3dd5": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1201", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "55.93", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.29", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "18.99", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "214bf041-5461-43a0-bd31-0a8511d1593f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1000", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "60.33", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.06", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.06", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.98", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f5600d8f-08fa-495c-8869-0815e1240609": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1015", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "75.57", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.26", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.18", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "fd8d704b-3fc9-4d64-80ae-64dde535fcc3": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1030", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.50", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.87", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.70", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.55", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "0651a984-475e-4b6d-81b4-c9d58207f25f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1231", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "63.32", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.84", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.91", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.24", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "b186c226-6066-4ceb-bc60-021cc5ba6ab8": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1027", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "69.89", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.83", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "3.83", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.29", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "0b1dc90c-1db9-4e00-9731-2257e5ba233b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1222", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "81.97", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.92", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.16", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.83", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "77883533-e8ce-432f-887c-c3316968f39f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1036", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "84.17", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "96.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "6.48", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.85", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8ed98051-a6f0-4e25-afbb-d682c51a1fa7": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1084", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.86", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.89", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "4.37", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e1ad26ed-ae0e-4842-8feb-888d75a97711": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1066", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "91.65", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "4.81", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.01", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "2.69", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "cb17344f-5311-487c-8b80-368f6bfa7058": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1234", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "67.71", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.74", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.64", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.25", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "1ceabe8a-64a0-4630-90ad-85550085c0fe": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1003", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "63.33", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.97", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.89", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.26", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "979f7f43-f8a4-4eba-8e3b-fb0e6d850760": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1015", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.83", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.91", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "5.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.75", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9db8b2e6-e8b8-494b-9cf9-bfbf5954b818": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1243", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.56", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.89", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.38", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "24.55", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "bfde17bb-2849-4e99-8fb1-378a7878006f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1093", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.28", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.45", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "93aaa4a6-838f-4075-ab5b-85a0354235c3": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1129", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "83.42", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.80", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.19", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "11da52a6-41c8-499e-b6fc-2406bf8bebbd": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1054", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "83.92", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "21.15", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.97", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.17", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d2ea9d02-c69e-4882-adf1-53f99130c154": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1081", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "64.69", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.80", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "17.03", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e079dd5b-4616-41b1-ab62-a2f9cd61f8fb": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1099", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "83.16", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.89", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "2.72", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.61", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "60922f1a-7a23-461d-b478-0d1c317c77ad": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1042", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.86", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.36", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.60", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.42", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "53322324-a567-4d64-8467-d981f7e7e683": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1120", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "68.58", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.33", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "19.80", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.60", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f7e17b69-ac66-4830-a54f-533429b64401": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1111", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "83.33", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.37", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.67", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.60", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d5a55946-7b62-488a-9701-1c640debf224": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1039", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "80.50", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "2.13", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.26", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "652bfed7-9de2-4b74-a853-b65d5415e93b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1105", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.97", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "19.90", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "10.00"}, {"test_name": "InspColorScale", "test_val": "33.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "da62ed71-6416-435e-886c-b2f369239d14": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1114", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "71.09", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.96", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.59", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.89", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4bffc5b2-a6b1-4df1-9f97-14a8e710b736": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1102", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.48", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "12.94", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "3.23", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "96bb900b-5d02-45df-8383-d0df702585d3": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1096", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "88.60", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.93", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "1.98", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.51", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "737d572c-a277-4584-b5b6-7d456e3f4b5a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1126", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.03", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.91", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "22.16", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.06", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8a585b4e-8379-43b1-a7c2-e85da1eed973": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1087", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.29", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.53", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.35", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "92e87082-bc74-41c7-974f-a0d57121a01d": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1123", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "85.03", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.97", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.71", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.30", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e399bc6a-e0a6-4401-88a7-7ff84e699286": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1129", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "69.53", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "95.64", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.86", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.37", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "92081c8d-b229-480e-9360-a595e745f4bd": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1138", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "82.35", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "2.18", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "4.94", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "003e1a25-2400-4c0c-b090-9004d186c167": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1141", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "81.17", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.94", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.25", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.32", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9fc9dd8a-fdba-4bbd-ad3c-9fcc4eab464a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1144", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "85.47", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "7.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.96", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f0b33eca-6657-48e3-bf84-ae8fa5d310ed": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1135", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "94.66", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "4.33", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.28", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7abe5146-a2cf-4cce-8bd1-5801861d8c14": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1162", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.32", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.58", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.69", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.70", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "a49a2a50-abae-4858-a718-0650ee17d9d2": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1174", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "73.21", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.83", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.17", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "572ede2d-c23d-4658-93bd-875026cfa6df": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1153", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.12", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "40.35", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "12.75", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.23", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "7e9dc0f0-e5e3-42fe-a207-d5355089aa8d": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1210", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.36", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.74", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "39.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "18.02", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "db86983f-e0be-4797-aab0-2548e1b44a5b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1246", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.85", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "5526c54b-0bf2-48bf-afd0-8b0a2711d34c": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1248", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.84", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "25caaf27-e211-43ac-bbfa-a21c2cd122bc": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1150", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "59.02", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.45", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "24.94", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.69", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "911c9ef2-72cf-4ba3-9eea-e95bb958fc20": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1250", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8032032e-dc60-457c-a269-2356dc9859bf": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1252", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e6661bc6-3bf9-43ed-8608-ece5f975723b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1117", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "60.59", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.22", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.40", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "aabb27cc-777d-457f-9269-e8aa44955297": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1254", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.88", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d74b2180-a2d7-43e3-8b37-459edc299283": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1258", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.92", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "aae6a555-3f56-4563-baa2-71bb5e509996": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1256", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.46", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "18301265-9f6c-4198-b585-f96c6e8ce092": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1260", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3eba3234-0ab8-4728-86e1-5cd1a71a3dc1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1264", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.55", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "686558f1-809c-4b79-8620-f2fcfd47d8ad": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1270", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e2b70172-a480-4449-b94e-0c52fdc89ed1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1262", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.86", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3eb082c2-469f-4f58-babd-72cdf5379562": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1272", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "62a4b416-ada4-45b4-bfda-a03c9dd4cfcd": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1274", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.86", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2d7fe2b7-9f13-43c9-9256-224b7254ed46": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1268", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f5352bd6-4472-458f-ab8c-bda44fcf6540": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1278", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "95a4580b-a36c-40ff-9699-b30545cf449a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1266", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "98c3f86e-494f-41d7-8fe1-a313a0810a26": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1159", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "93.15", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "1.47", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "b425cee2-f0bc-4cdc-8776-665b5790bc1f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1280", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c662b040-d3f7-4b82-b268-4dcc70477c4f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1282", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.93", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8d077bb5-ec4d-46b5-8252-0126db3a9868": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1276", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "82.30", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6fc1e694-69c9-478d-9bf7-cb4210de1afa": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1288", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "98.36", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6d653525-c1c3-4fb9-8b04-a65214045446": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1290", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.94", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8b7dea55-d2f8-465e-a816-64ba0353dc94": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1292", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.89", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "93800fee-2ef5-4d20-bb2c-eea80574c243": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1286", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "98.89", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4179dd8c-90f0-4f1d-ae5e-41256074b645": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1294", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "057cccd3-99c3-45d1-94bc-46f684e9102b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1249", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2812aa34-9574-4d0d-aa7c-ba0118486fc4": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1018", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.56", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.36", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.31", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.27", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c49aedc8-faf8-43b3-a3d0-e45b6a2ca0b1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1284", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4283f08c-8b75-4d93-8dc5-fe6b5c31b65e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1247", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "1606a5bf-bd5a-48e9-9c36-1a16ffdef41f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1253", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "eb345ce7-594d-4dac-a171-f21de6dd3ea9": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1255", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "fae91674-b779-4f27-ab69-a551ccf74b0d": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1251", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.97", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "de4dbb54-ca98-4178-94b7-03023ea57a7a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1183", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "59.01", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d545b5fc-3eda-4759-9006-95dfdfb3fbbe": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1012", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.91", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.98", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.81", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.82", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4ba35723-ba80-4625-afc0-59f0a68a4d93": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1069", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.70", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "46.32", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "25.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.60", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "01a4917b-199a-465b-aa03-c663d56f5a31": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1003", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "70.65", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.85", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.06", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2b568916-59e5-4354-a175-3cceb8efdf9e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1186", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.62", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.96", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.42", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.24", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "94aaf349-574b-44b7-9604-4685cbe87258": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "<PERSON><PERSON>", "designator": "COMP1726", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMarkerMatchTemplate", "test_val": "99.30", "result": "0", "mix_threshold": "60.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "782104f9-c5d7-4f60-952e-f4b1de663600": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1189", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "79.52", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.97", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.95", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.65", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "867917a7-0b3d-44db-8d4d-342884a81a74": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1177", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.20", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.79", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "23.75", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c19294f4-4b0d-4611-84f0-4c39729f81d0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1165", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "75.86", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "24.44", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "17.09", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "cc6e030b-3d06-4aba-b608-1b0cf7335184": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1207", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "91.10", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "94.37", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "2.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.21", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8f815ba7-761e-449a-bfb7-236ff2d29f57": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1216", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "88.96", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "12.22", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "8.33", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4d915e54-0e7a-4845-af77-41a510b2552e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1210", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "67.28", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "20.13", "result": "0", "mix_threshold": "2.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "26.43", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.23", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3616b77c-2d7d-4ec8-9a37-3bdbc11dc77e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "Chip", "designator": "COMP1288", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMissAi", "test_val": "99.10", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9c0cb86f-04af-496b-bf43-aec14f5c3d6e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1132", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "79.96", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "98.09", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "13.32", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2df9baac-45a2-43cf-bc47-f319a88a9342": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1183", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.21", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "96.53", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "11.42", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.51", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f696907b-61b6-4e90-9e32-8ada0ebe0bf4": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "<PERSON><PERSON>", "designator": "COMP1727", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspMarkerMatchTemplate", "test_val": "99.29", "result": "0", "mix_threshold": "60.00", "max_threshold": "100.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "427a4422-8d44-47b0-86d9-1c51a778620c": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1243", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "60.27", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "96.66", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.23", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "24.62", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3e234759-5f87-4666-8310-e06ec902cf5d": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1225", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "79.11", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.39", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "01fbfed5-011c-4580-88a7-8a41ead05ede": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1204", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "68.35", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.97", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.55", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9f27eb71-f931-477b-87af-0fc9129a9cf1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1234", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.36", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.91", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "19.81", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.17", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9de4b15f-db34-4095-9a06-96e30954cc48": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1216", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.35", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "23.46", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.36", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "fc9fb3fc-89ab-45dc-b095-4b53dd21575f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1171", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "76.11", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.58", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.75", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.52", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "82274f3c-3f60-4dce-ac42-9cb5c4e17a43": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1228", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "69.35", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "97.42", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "23.55", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "27.82", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d539091e-6079-47ba-9d71-78184ed51498": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1156", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "71.48", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "31.84", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.02", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "a7d9cbcc-74f9-4775-b06f-f7cc9bc3f359": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1237", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.24", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.97", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.24", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.24", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "033ff35a-3be0-4484-aec8-31bb6b8a79ae": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1231", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.30", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "9.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "7.77", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "e9b0559f-03fb-402b-b734-b50170c3b2ac": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1009", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.73", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "24.48", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "18.13", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "83ba8c0d-e1de-4cf2-a997-68018528cf85": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1189", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "80.63", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "19.12", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.78", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4305e2d8-269e-4ee0-b86a-4f5a85bdfbb3": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1168", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "65.90", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "96.44", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.09", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "ea60caca-0ecd-4ad3-9b06-48788de75f12": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1222", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "81.60", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "22.80", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "5.46", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.92", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "511a3401-fc67-4ae4-8e1f-27ea5a4522d0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1099", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "90.07", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.94", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "3.80", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.64", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "1ae8b34c-b7ca-4fb3-bc3b-a102baa5dd13": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1159", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "71.38", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.80", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "22.88", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "18.52", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3840044d-f7df-4a05-881b-fac11b581fe1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1213", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "66.43", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.60", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.91", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "17.50", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "a7a96c05-23f0-416d-b90b-6280f149005a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1132", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "83.60", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.96", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "10.65", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "14.36", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "1fa8ed8d-2469-4332-b0cf-4f040ed92b7b": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1084", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "79.42", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "8.15", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.96", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4ade312e-3c8a-448f-8342-c22b3bd44f1a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1006", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.26", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.94", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "21.61", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "18.63", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "d3a0fe19-1dbe-4b98-b6cc-b2da6a0adfcd": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1075", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "85.52", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.23", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "6f53786c-ea15-4a60-abcf-15f634fb6dcf": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1024", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "75.91", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.62", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.77", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "11.05", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c8a04bd9-8b8f-4662-b5b2-1a9b05fb6eff": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1045", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "73.91", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.71", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.17", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.88", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "269a2694-8d78-4c23-94f2-87937313ed14": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1240", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "78.41", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.49", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "35.57", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "46aa120d-1d68-4ee5-9cd8-59b8d5b55aaa": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1117", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "60.58", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.60", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.72", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "21.47", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "14.90", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "8063174e-9475-4337-b6a4-04347a080d09": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1048", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "77.12", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.96", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "6.13", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.14", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "50697774-9784-4db7-9c67-880f4b3fa995": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1180", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "66.69", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "21.84", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.94", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "2379b864-32b1-43c2-aca2-56617cf5add1": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1219", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "81.52", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "16.18", "result": "0", "mix_threshold": "10.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.65", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "6.88", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f8166f63-735f-43d6-8fd8-0c274c0e117a": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1072", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "74.70", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.63", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.38", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "f7932b92-01cd-4af3-a462-fe242e4823c5": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1153", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "86.93", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "32.70", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "11.43", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "5.73", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "bea10bee-8b9d-44f8-92de-26c8741565d0": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1126", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "60.72", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.90", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "18.52", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "22706b5a-36f6-459f-9e53-64538c9c8497": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1060", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "73.18", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.95", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "15.24", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "19.47", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c9a462b1-4aec-4d1a-97b6-5ac5e89baf96": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1057", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "69.22", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.78", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "7.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.64", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "3e48431c-f5e9-434a-bddc-a69ea254f1dc": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1108", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.75", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.95", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "21.94", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "15.94", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "4eb2d9c0-a8b9-4a94-be36-6dda41f71c49": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1108", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "75.58", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.96", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "14.88", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "14.44", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "1f557788-55b8-4c9c-9d0a-4f00c3ff3716": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1201", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "59.47", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "21.02", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "16.14", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "de54a2b6-147f-465c-91f7-6fa3b501f210": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1120", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "61.52", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "92.19", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "16.56", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "13.45", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "c961737b-8d90-4cc3-9572-48ddde288ac2": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1033", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "84.06", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "17.39", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "10.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "db581d08-1e39-42e5-9b76-c7810c6eccd8": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1162", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "67.24", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.92", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "12.57", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "12.04", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "bdd24bf7-f269-45fc-b173-95ba69af09de": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1021", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "84.10", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.98", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "99.99", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "20.30", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.62", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "0d964363-04b9-4471-99d3-c7a3f9aaff8e": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1063", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "89.55", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "34.07", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "17.44", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "eeaf880b-25db-4c09-b58d-8ee7c1ab0a4f": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1078", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "92.41", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.99", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "4.63", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.10", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}, "9240b69f-88c2-4ddf-81fe-15efcb45af06": {"part": "", "image_path": "", "robot_ng_str": "OK", "type": "SolderJoint", "designator": "COMP1147", "robot_ng_code": "0", "package": "", "x_offset": "0", "y_offset": "0", "x_pos": null, "y_pos": null, "alg_data": [{"test_name": "InspSolderAi", "test_val": "100.00", "result": "0", "mix_threshold": "30.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "72.92", "result": "0", "mix_threshold": "15.00", "max_threshold": "100.00"}, {"test_name": "InspSolderAi", "test_val": "99.93", "result": "0", "mix_threshold": "20.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspBridgingAi", "test_val": "100.00", "result": "0", "mix_threshold": "50.00", "max_threshold": "100.00"}, {"test_name": "InspColorScale", "test_val": "6.89", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "0.00", "result": "0", "mix_threshold": "0.00", "max_threshold": "2.00"}, {"test_name": "InspColorScale", "test_val": "9.85", "result": "0", "mix_threshold": "0.00", "max_threshold": "40.00"}], "repair_ng_str": "OK", "repair_ng_code": "0", "repair_auto_ng": "Unknown", "repair_auto_ng_code": "-1", "repair_checked_flag": "true", "final_result": "PASS"}}}}