# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/25 上午10:00
# Author     ：sch
# version    ：python 3.8
# Description：丘钛微
"""
import os
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine
from services.custom.qiutaiwei.qiutaiwei_module import get_specs_by_report_xml, concat_item_by_threshold, list_chunks

row_template = """{order_id},{model_name},{pcb_sn},{comp_tag},{board_no},{part},{ng_str},{x_offset_threshold},{x_offset_val},{y_offset_threshold},{y_offset_val},{height_low},{height_upper},{height_val},{aoi_result},{aoi_ins_time},{repair_result},{repair_time},{time1},{ng_code}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "qiutaiwei release v1.0.0.11",
        "device": "AIS430",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-25 16:00  条码校验，上传数据
date: 2024-10-29 16:16  totalTestTime传int类型
date: 2024-11-12 21:02  输出 智能算法2 高度倾斜 尺寸检测的值
date: 2024-11-13 11:49  修改读取的report.xml参数
date: 2024-11-18 09:19  参数调整
date: 2024-11-18 16:16  参数调整 + 1
date: 2024-12-25 19:34  [feature]新增上传NG数据接口+保存NG器件图
date: 2025-01-05 13:06  修改不良图片命名方式
date: 2025-01-07 10:24  增加配置项：仅校验板边条码
""",
    }

    form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/CarFPCheckRoute",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据/过站)",
            "value": "http://127.0.0.1:8081/CarFPPassStation",
        },
        "api_url_ng_data": {
            "ui_name": "接口URL(NG不良记录)",
            "value": "http://127.0.0.1:8081/api/Car/carSMTAOINGlogSave",
        },
        "supplier_name": {
            "ui_name": "供应商",
            "value": "丘钛微",
        },
        "factory": {
            "ui_name": "厂区",
            "value": "",
        },
        "workshop": {
            "ui_name": "车间",
            "value": "",
        },
        "line_id": {
            "ui_name": "线别",
            "value": "",
        },
        "process_id": {
            "ui_name": "工序",
            "value": "",
        },
        "station_id": {
            "ui_name": "工站",
            "value": "",
        },
        "wo_id": {
            "ui_name": "工单",
            "value": "",
        },
        "device_code": {
            "ui_name": "机台号",
            "value": "",
        },
        "user_id": {
            "ui_name": "工号",
            "value": "",
        },
        "mac": {
            "ui_name": "测试电脑MAC",
            "value": "",
        },
        "ip": {
            "ui_name": "测试电脑IP",
            "value": "",
        },
        "test_item_name": {
            "ui_name": "TestItemName",
            "value": "",
        },
    }

    path = {
        "ng_comp_save_path": {
            "ui_name": "NG器件图保存路径",
            "value": "",
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        factory = other_vo.get_value_by_cons_key("factory")
        workshop = other_vo.get_value_by_cons_key("workshop")
        line_id = other_vo.get_value_by_cons_key("line_id")
        process_id = other_vo.get_value_by_cons_key("process_id")
        station_id = other_vo.get_value_by_cons_key("station_id")
        wo_id = other_vo.get_value_by_cons_key("wo_id")
        device_code = other_vo.get_value_by_cons_key("device_code")
        user_id = other_vo.get_value_by_cons_key("user_id")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            param = {
                "FactoryName": factory,
                "WorkShopCode": workshop,
                "Line": line_id,
                "OpCode": process_id,
                "TestStation": station_id,
                "WO": wo_id,
                "SNCODE": sn,
                "equipmentNumber": device_code,
                "empid": user_id,
                "PartsList": []
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, param)

            if str(ret.get("code")) != "200":
                ret_res = self.x_response("false", f"mes接口异常，error：{ret.get('msg')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        supplier_name = data_vo.get_value_by_cons_key("supplier_name")
        factory = data_vo.get_value_by_cons_key("factory")
        workshop = data_vo.get_value_by_cons_key("workshop")
        line_id = data_vo.get_value_by_cons_key("line_id")
        process_id = data_vo.get_value_by_cons_key("process_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        wo_id = data_vo.get_value_by_cons_key("wo_id")
        device_code = data_vo.get_value_by_cons_key("device_code")
        user_id = data_vo.get_value_by_cons_key("user_id")
        mac = data_vo.get_value_by_cons_key("mac")
        ip = data_vo.get_value_by_cons_key("ip")
        test_item_name = data_vo.get_value_by_cons_key("test_item_name")
        api_url_ng_data = data_vo.get_value_by_cons_key("api_url_ng_data")
        ng_comp_save_path = data_vo.get_value_by_cons_key("ng_comp_save_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        test_name = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT6)
        inspect_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        # review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT)

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        ret_res = self.x_response()

        test_item_list = []
        ng_code = ""

        report_xml = pcb_entity.get_pcb_t_report_xml()

        comp_spec_data = get_specs_by_report_xml(report_xml)

        start_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT6)
        start_time2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        mono_header = "工单,机型,板边码,位置,拼板,料号,不良原因,偏移值设定X,实测值X,偏移值设定Y,实测值Y,高度设定上限,高度设定下限,高度实测值,AOI判定结果,AOI判定时间,人工判定结果,人工判定时间,时间,不良代码"

        all_memo_row_list = []

        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)
        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT)

        comp_ng_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not barcode and pcb_sn:
                barcode = pcb_sn

            for comp_entity in board_entity.yield_comp_entity():

                comp_uuid = comp_entity.comp_id

                if comp_entity.is_robot_ng():
                    if not ng_code:
                        ng_code = comp_entity.robot_ng_code

                    # 拷贝NG图片
                    comp_src_img = comp_entity.image_path
                    robot_ng_code = comp_entity.robot_ng_code

                    comp_tag = comp_entity.designator
                    if comp_src_img and os.path.exists(comp_src_img):
                        dst_filepath = f"{ng_comp_save_path}/{barcode}_{comp_tag}_{board_no}_{start_time2}_{robot_ng_code}.png"
                        xutil.FileUtil.copy_file(comp_src_img, dst_filepath)

                    x_offset_threshold = "0"
                    x_offset_val = "0"
                    y_offset_threshold = "0"
                    y_offset_val = "0"

                    # height_low = "0"
                    height_upper = "0"
                    height_val = "0"

                    spec_list = comp_spec_data.get(comp_uuid, {}).get("spec_list", [])

                    self.log.info(f"spec len: {len(spec_list)}")

                    for spec in spec_list:
                        spec_name = spec.get("specName")
                        self.log.info(f"comp id: {comp_uuid}")
                        self.log.info(f"spec: {spec}")

                        if spec_name == "InspSkewAiObb":
                            # 智能偏移2
                            x_offset_threshold = spec.get("xSkewThreshold")
                            y_offset_threshold = spec.get("ySkewThreshold")
                            x_offset_val = spec.get("xSkewResult")
                            y_offset_val = spec.get("ySkewResult")

                        elif spec_name == "InspDimensionDetect":
                            # 尺寸检测
                            x_offset_val = spec.get("REAL_OFFSET_X")
                            y_offset_val = spec.get("REAL_OFFSET_Y")
                            x_offset_threshold = spec.get("x_offset_U")
                            y_offset_threshold = spec.get("y_offset_U")

                        elif spec_name == "InspHeightTilted":
                            # 高度倾斜
                            height_upper = spec.get("heightDiffResultThre")
                            height_val = spec.get("heightDiffResult")

                    row_content = row_template.format(**{
                        "order_id": wo_id,
                        "model_name": pcb_entity.project_name,
                        "pcb_sn": pcb_sn,
                        "comp_tag": comp_entity.designator,
                        "board_no": board_entity.board_no,
                        "part": comp_entity.part,
                        "ng_code": comp_entity.robot_ng_code,
                        "ng_str": comp_entity.robot_ng_str,

                        # 尺寸检测/智能偏移2
                        "x_offset_threshold": x_offset_threshold,
                        "x_offset_val": x_offset_val,
                        "y_offset_threshold": y_offset_threshold,
                        "y_offset_val": y_offset_val,

                        # 高度倾斜
                        "height_low": "0",
                        "height_upper": height_upper,
                        "height_val": height_val,

                        "aoi_result": "NG",
                        "aoi_ins_time": inspect_time,
                        "repair_result": comp_entity.get_final_result("OK", "OK", "NG"),
                        "repair_time": review_time,
                        "time1": time_now,
                    })

                    all_memo_row_list.append(row_content)

                    # test_item_list.append({
                    #     "testItemName": test_item_name,
                    #     "testSubItem": [{
                    #         "unit": "",
                    #         "subItemTestResult": comp_entity.get_final_result("OK", "NG", "NG"),
                    #         "specUpperLimit": "",
                    #         "subItemTestValue": "",
                    #         "specLowerLimit": "",
                    #         "remark": "",
                    #         "testSubItemName": "",
                    #         "testCondition": "",
                    #         "testTime": start_time1,
                    #         "memo1": mono_header,
                    #         "memo2": row_content,
                    #         "memo3": "",
                    #         "memo4": "",
                    #         "memo5": ""
                    #     }]
                    # })

                    # 上传检测NG的不良数据
                    comp_ng_data_list.append({
                        "barcode": barcode,
                        "location": comp_entity.designator,
                        "kpn": comp_entity.part,
                        "error_reason": comp_entity.robot_ng_str,
                        "temp01": board_no,
                        "temp02": comp_entity.designator,
                        "temp03": x_offset_threshold,
                        "temp04": x_offset_val,
                        "temp05": y_offset_threshold,
                        "temp06": y_offset_val,
                        "temp07": height_upper,
                        "temp08": "0",
                        "temp09": height_val,
                        "temp10": comp_entity.robot_ng_code,
                        "aoi_result": comp_entity.robot_ng_str,
                        "aoi_date": end_time,
                        "emp_result": comp_entity.repair_ng_str,
                        "emp_date": review_time,
                        "temp11": "",
                        "temp12": ""
                    })

        new_data_list = concat_item_by_threshold(all_memo_row_list, 1999)

        page_list = list_chunks(new_data_list, 4)

        for page in page_list:
            memo1 = ""
            memo2 = ""
            memo3 = ""
            memo4 = ""

            if len(page) >= 1:
                memo1 = page[0]

            if len(page) >= 2:
                memo2 = page[1]

            if len(page) >= 3:
                memo3 = page[2]

            if len(page) >= 4:
                memo4 = page[3]

            self.log.info(f"{len(memo1)=} {len(memo2)=} {len(memo3)=} {len(memo4)=}")

            test_item_list.append({
                "testItemName": test_item_name,
                "testSubItem": [{
                    "unit": "",
                    "subItemTestResult": "0",
                    "specUpperLimit": "",
                    "subItemTestValue": "",
                    "specLowerLimit": "",
                    "remark": "",
                    "testSubItemName": "",
                    "testCondition": "",
                    "testTime": start_time1,
                    "memo1": mono_header,
                    "memo2": memo1,
                    "memo3": memo2,
                    "memo4": memo3,
                    "memo5": memo4
                }]
            })

        if not test_item_list:
            test_item_list.append({
                "testItemName": test_item_name,
                "testSubItem": [{
                    "unit": "",
                    "subItemTestResult": "0",
                    "specUpperLimit": "",
                    "subItemTestValue": "",
                    "specLowerLimit": "",
                    "remark": "",
                    "testSubItemName": "",
                    "testCondition": "",
                    "testTime": start_time1,
                    "memo1": mono_header,
                    "memo2": row_template.format(**{
                        "order_id": wo_id,
                        "model_name": pcb_entity.project_name,
                        "pcb_sn": pcb_sn,
                        "comp_tag": "",
                        "board_no": "",
                        "part": "",
                        "ng_code": "",
                        "ng_str": "合格",

                        # 尺寸检测/智能偏移2
                        "x_offset_threshold": "",
                        "x_offset_val": "",
                        "y_offset_threshold": "",
                        "y_offset_val": "",

                        # 高度倾斜
                        "height_low": "",
                        "height_upper": "",
                        "height_val": "",

                        "aoi_result": "OK",
                        "aoi_ins_time": inspect_time,
                        "repair_result": "OK",
                        "repair_time": review_time,
                        "time1": time_now,
                    }),
                    "memo3": "",
                    "memo4": "",
                    "memo5": ""
                }]
            })

        pcb_result = pcb_entity.get_final_result("OK", "OK", "NG")

        if pcb_result == "OK":
            ng_code = ""

        # 改成按整板发送
        req_param = {
            "supplierName": supplier_name,
            "line": line_id,
            "testStation": station_id,
            "factoryName": factory,
            "WorkShopCode": workshop,
            "equipmentNumber": device_code,
            "programVersion": pcb_entity.project_name,
            "totalTestTime": int(pcb_entity.get_cycle_time()),
            "SNcode": pcb_sn,
            "partspec": "",
            "mac": mac,
            "ip": ip,
            "empid": user_id,
            "wo": wo_id,
            "opcode": process_id,
            "result": pcb_result,
            "ngtype": ng_code,
            "testItemList": test_item_list,
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, req_param)
        if str(ret.get("code")) != "200":
            ret_res = self.x_response("false", f"mes接口异常，error：{ret.get('msg')}")

        if comp_ng_data_list:
            self.log.info(f"开始上传NG不良数据")
            xrequest.RequestUtil.post_json(api_url_ng_data, comp_ng_data_list)
        else:
            self.log.info(f"没有NG不良数据上传...")

        return ret_res
