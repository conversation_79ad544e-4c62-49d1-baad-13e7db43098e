# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_server.py
# Time       ：2025/04/25 上午9:22
# Author     ：gyr
# version    ：python 3.8
# Description：测试服务端，用于接收和显示数据
"""

from flask import Flask, request, jsonify
import json
from datetime import datetime

app = Flask(__name__)

# 用于存储接收到的数据
received_data = []


@app.route('/mes', methods=['POST'])
def receive_data():
    """接收数据的接口"""
    try:
        # 获取请求数据
        data = request.get_json()

        # 记录接收时间
        receive_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        # 保存接收到的数据
        received_data.append({
            "time": receive_time,
            "data": data
        })

        # 打印详细的请求信息
        print("\n" + "=" * 50)
        print(f"接收时间: {receive_time}")
        print("请求头:")
        print(dict(request.headers))
        print("\n请求数据:")
        print(json.dumps(data, ensure_ascii=False, indent=2))

        # 数据格式验证
        if isinstance(data, dict):
            if "recordcount" in data and "data" in data:
                print("\n数据格式: MES标准格式")
            elif "key" in data and "result" in data and "string" in data:
                print("\n数据格式: 错误信息格式")
                # 如果是错误信息，返回特定响应
                return jsonify({
                    "code": "2003",
                    "msg": "接口调用出错2003，请检查接口服务是否正常！"
                })
            else:
                print("\n数据格式: 未知格式")
        else:
            print("\n数据格式: 非JSON对象")

        print("=" * 50 + "\n")

        # 返回成功响应
        return jsonify({
            "code": "100",
            "msg": "成功"
        })

    except Exception as e:
        error_msg = f"处理失败：{str(e)}"
        print(f"\n错误：{error_msg}")
        # 返回错误响应
        return jsonify({
            "code": "500",
            "msg": error_msg
        })


@app.route('/view', methods=['GET'])
def view_data():
    """查看所有接收到的数据"""
    return jsonify(received_data)


@app.route('/clear', methods=['POST'])
def clear_data():
    """清除所有接收到的数据"""
    received_data.clear()
    return jsonify({
        "code": "100",
        "msg": "数据已清除"
    })


if __name__ == '__main__':
    print("\n测试服务器配置信息：")
    print("-" * 30)
    print("监听地址：0.0.0.0")
    print("监听端口：8196")
    print("接收数据接口：http://localhost:8196/mes")
    print("查看数据接口：http://localhost:8196/view")
    print("清除数据接口：http://localhost:8196/clear")
    print("-" * 30)
    print("\n服务器启动中...\n")
    app.run(host='0.0.0.0', port=8196, debug=True)