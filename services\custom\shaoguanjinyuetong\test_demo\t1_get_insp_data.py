# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t1_get_insp_data.py
# Time       ：2025/5/10 上午11:22
# Author     ：sch
# version    ：python 3.8
# Description：获取主软件统计数据
"""
from services.custom.shaoguanjinyuetong.jyt_module import get_insp_data_by_track_index

"""
目录：
/home/<USER>/aoi/MesStatistic/lane1.json
/home/<USER>/aoi/MesStatistic/lane2.json

内容：
{
    "projectName": "1",  // 板式名   pcb.bom
    "passCount": 100,   // 板卡信息-直通数
    "repassCount": 100, // 板卡信息-误报数
    "ngCount": 100, // 板卡信息-不良数
    "compPassCount": 100,   // 器件直通数
    "compRepassCount": 100, // 器件误报数
    "compNgCount": 100, // 器件不良数
    "projectOpened": true, // 该轨道是否已打开板式
    "countMethod":  1   // 1拼板统计 2整板统计 3联板统计
}
"""

# def get_insp_data_by_track_index(track_index):
#     """
#     获取轨道统计数据
#     """
#     home_dir = os.path.expanduser("~")
#     mes_static_path = f"{home_dir}/aoi/MesStatistic"
#
#     ret_data = xutil.FileUtil.load_json_file(f"{mes_static_path}/lane{track_index}.json")
#     log.info(f"获取到 lane{track_index}.json 数据：{ret_data}")
#
#     return ret_data


if __name__ == '__main__':
    # home_dir = os.path.expanduser("~")
    # mes_static_path = f"{home_dir}/aoi/MesStatistic"
    #
    # # print(f"{mes_static_path}/lane1.json")
    # #
    # # print(mes_static_path)
    # ret = xutil.FileUtil.load_json_file(f"{mes_static_path}/lane1.json")
    # print(ret)

    get_insp_data_by_track_index(1)
