# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/01/21 19:49
# Author     ：chencb
# version    ：python 3.8
# Description：马来西亚维胜电子
"""
from datetime import datetime
from typing import Any
from common import xutil, xcons
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "malaixiyaweishengdianzi release v1.0.0.2",
        "device": "AIS501-C",
        "feature": ["生成txt文档到本地"],
        "author": "chenchongbing",
        "release": """
date: 2025-01-21 19:50  jira->36559: 生成txt文档到本地
date: 2025-02-18 10:50  jira->36559: 修改文件保存方式(增加日期目录)和命名规则（增加版面标记）
""", }

    # 参数配置：Mc Model、Mc brand、Paste ID、CAD link Gerber、文件保存路径
    form = {
        "mc_model": {
            "ui_name": "Mc Model",
            "ui_name_en": "Mc Model",
            "value": "",
        },
        "mc_brand": {
            "ui_name": "Mc brand",
            "ui_name_en": "Mc brand",
            "value": "",
        },
        "paste_id": {
            "ui_name": "Paste ID",
            "ui_name_en": "Paste ID",
            "value": "",
        },
        "cad_link_gerber": {
            "ui_name": "CAD link Gerber",
            "ui_name_en": "CAD link Gerber",
            "value": "",
        },
    }

    path = {
        "txt_path": {
            "ui_name": "文件保存路径",
            "ui_name_en": "File Save Path",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        mc_model = data_vo.get_value_by_cons_key("mc_model")
        mc_brand = data_vo.get_value_by_cons_key("mc_brand")
        paste_id = data_vo.get_value_by_cons_key("paste_id")
        cad_link_gerber = data_vo.get_value_by_cons_key("cad_link_gerber")
        txt_path = data_vo.get_value_by_cons_key("txt_path", not_null=True)

        # 界面未填写传NA
        if not paste_id:
            paste_id = "NA"

        if not cad_link_gerber:
            cad_link_gerber = "NA"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        program = pcb_entity.project_name

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_FILE)

        # 501双面检测时，底面没有条码，需要合并发送[客户自己系统设置里配置，默认不合并]，坏板不发送【客户自己系统设置里配置，默认不发送】
        # 如果设置【坏板不发送】，pcb_entity里则不会生成坏板的board_entity
        # 多拼板时，每个拼板生成一个文件
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # 如果系统设置配了坏板发送，这里做一层检验，进行提醒
            if board_entity.final_result == 'BadBoard':
                self.log.info('需求要求坏板不发送，请检查是不是配置项里进行了设置【发送坏板】')
                continue

            barcode = board_entity.barcode
            # 把测试结果转成客户指定的内容
            judgement = board_entity.get_final_result("GOOD", "PASS", "FAIL")

            # 设备检测结果，OK传1，NG传0
            mc_judge = board_entity.get_robot_result(1, 0)
            # 人工复判结果，OK传0，NG传1
            opt_judge = board_entity.get_repair_result(0, 1)

            rejudge_location = ''
            # 如果是PASS，默认输出NA
            if not board_entity.is_robot_ng():
                rejudge_location = 'NA'
            else:
                # 记录机器检测NG器件数，存在多个时换行记录
                for comp_entity in board_entity.yield_comp_entity():

                    if comp_entity.is_robot_ng():
                        # 把测试结果转成客户指定的内容
                        comp_result = comp_entity.get_final_result('GOOD', 'GOOD', 'BAD')

                        # 如果REPASS，不良代码默认填写0
                        repair_ng_code = comp_entity.repair_ng_code
                        # 数据格式： GOOD/BAD(复判后器件结果),程序名,Paste ID(界面填),CAD link Gerber(界面填),不良代码,拼板数量
                        rejudge_location += f'{comp_result};{program};{paste_id};{cad_link_gerber};{repair_ng_code};{pcb_entity.board_count}\n'

            '''
            txt保存内容：
            说明		字段		
            拼板条码	barcode ID	
            界面填写	Mc Model	
            界面填写	Mc brand
            程序名	program		
            GOOD/PASS/FAIL  judgement
            测试开始时间	time in		
            测试结束时间	time out			
            固定传值			NA
            设备复判结果	mc judge	
            人工复判结果： opt judge 		
            固定传值	Error flag,Recipe name,Paste ID,CAD link Gerber,Error code,Multi Number
            GOOD/BAD(复判后器件结果),程序名,Paste ID(界面填),CAD link Gerber(界面填),不良代码,拼板数量	Rejudge location
            '''
            test_result = (
                f"{barcode}\n"
                f"{mc_model}\n"
                f"{mc_brand}\n"
                f"{program}\n"
                f"{judgement}\n"
                f"{start_time}\n"
                f"{end_time}\n"
                f"NA\n"
                f"{mc_judge}\n"
                f"{opt_judge}\n"
                f"Error flag, Recipe name, Paste ID, CAD link Gerber, Error code, Multi Number\n"
                f"{rejudge_location}"
            )
            cur_time = datetime.now()
            time_now = cur_time.strftime("%Y-%m-%d_%H-%M-%S")
            current_date = cur_time.strftime("%Y/%m/%d")

            # 文件命名：拼板条码_时间戳_拼板号_版面
            txt_name = f'{barcode}_{time_now}_{board_entity.board_no}_{pcb_entity.board_side}.txt'
            # 文件目录：2025/02/18/xxx.txt
            filepath = f"{txt_path}/{current_date}/{txt_name}"
            xutil.FileUtil.write_content_to_file_pro(filepath, test_result)

        return self.x_response()
