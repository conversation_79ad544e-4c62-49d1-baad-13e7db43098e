# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/14 下午3:00
# Author     ：sch
# version    ：python 3.8
# Description：海霖一造
"""
from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "hailinyizao release v1.0.0.1",
        "device": "203,501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-14 17:36  条码校验，上传数据
""", }

    form = {
        "lina_name": {
            "ui_name": "线体名称",
            "value": "",
        },
        "machine_id": {
            "ui_name": "设备ID",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "",
        },
        "machine_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "work_no": {
            "ui_name": "工单号",
            "value": "",
        },
    }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/Aoi/AoiBarCodeCheck",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/Aoi/AoiMesOutPut",
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        lina_name = other_vo.get_value_by_cons_key("lina_name")
        machine_id = other_vo.get_value_by_cons_key("machine_id")
        operator = other_vo.get_value_by_cons_key("operator")
        machine_name = other_vo.get_value_by_cons_key("machine_name")
        work_no = other_vo.get_value_by_cons_key("work_no")

        project_name = other_vo.get_project_name()

        sn_list = other_vo.list_sn()

        if len(sn_list) == 1:
            barcode = sn_list[0]
            array_barcode = ""

            collage_num = "0"
        else:
            barcode = ""
            array_barcode = ",".join(sn_list)
            collage_num = str(len(sn_list))

        check_param = {
            "lineName": lina_name,
            "machineName": machine_name,
            "machineId": machine_id,
            "operator": operator,
            "workNo": work_no,
            "productName": project_name,
            "barCode": barcode,
            "collageNum": collage_num,
            "arrayBarCode": array_barcode
        }
        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
        if ret.get("result") != "OK":
            return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        lina_name = data_vo.get_value_by_cons_key("lina_name")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        operator = data_vo.get_value_by_cons_key("operator")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        work_no = data_vo.get_value_by_cons_key("work_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name

        board_count = pcb_entity.board_count

        pcb_sn = pcb_entity.pcb_barcode

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_list.append({
                    "compDesignator": comp_entity.designator,
                    "compPart": comp_entity.part,
                    "compPackage": comp_entity.package,
                    "compType": comp_entity.type,
                    "compRobotCode": comp_entity.robot_ng_code,
                    "compRobotResult": comp_entity.robot_ng_str,
                    "compUserCode": comp_entity.repair_ng_code,
                    "compUserResult": comp_entity.repair_ng_str
                })

            data_param = {
                "lineName": lina_name,
                "machineName": machine_name,
                "machineId": machine_id,
                "operator": operator,
                "workNo": work_no,
                "productName": project_name,
                "barCode": pcb_sn,
                "collageNum": str(board_count),
                "miniBarCode": barcode,
                "beginTime": start_time,
                "endTime": end_time,
                "result": board_entity.get_repair_result("OK", "NG"),
                "failCompNum": str(board_entity.comp_robot_ng_number),
                "confirmFailCompNum": str(board_entity.comp_repair_ng_number),
                "board": pcb_entity.board_side,
                "pointQty": str(board_entity.comp_total_number),
                "trackName": str(pcb_entity.track_index),
                "remark": [{
                    "boardSN": barcode,
                    "boardNO": board_no,
                    "boardUserResult": board_entity.get_repair_result(),
                    "boardFinalResult": board_entity.get_final_result(),
                    "compData": comp_data_list
                }]
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if ret.get("result") != "OK":
                err_msg_list.append(f"mes接口异常，error：{ret.get('message')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口异常，{err_str}")

        return self.x_response()
