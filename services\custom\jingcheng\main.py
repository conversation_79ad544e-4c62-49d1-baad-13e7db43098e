# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/8 上午9:27
# Author     ：sch
# version    ：python 3.8
# Description：东莞精成
"""
import json
import os
from datetime import datetime
from typing import Any

from common import xcons, xrequest, xutil, xenum
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo


class Engine(ErrorMapEngine):
    version = {
        "title": "jingcheng release v1.0.0.7",
        "device": "AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-08 09:27  init
date: 2024-10-29 17:14  生成txt文档到本地
date: 2024-12-16 14:30  get请求格式改成post请求格式 1
date: 2024-12-19 14:36  修改请求参数
date: 2025-02-08 17:42  修改上传的文件格式
date: 2025-02-21 15:16  jira:33698,增加客户接口返回报错信息
date: 2025-02-21 15:35  jira:33698  修改响应参数：code->retcode
""", }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/AoiDataSfcCheck.aspx",
        }
    }

    form = {
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
        "line_id": {
            "ui_name": "线别",
            "value": "",
        },
        "station_id": {
            "ui_name": "站别",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "work_id": {
            "ui_name": "工号",
            "value": "",
        },
    }

    path = {
        "log_path": {
            "ui_name": "Log保存路径",
            "value": "",
        }
    }

    def __init__(self):
        self.common_config["sendmes_setting3"] = xenum.SendMesSetting3.Send

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")

        order_id = data_vo.get_value_by_cons_key("order_id")
        line_id = data_vo.get_value_by_cons_key("line_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        device_name = data_vo.get_value_by_cons_key("device_name")
        work_id = data_vo.get_value_by_cons_key("work_id")
        log_path = data_vo.get_value_by_cons_key("log_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        track_index = pcb_entity.track_index

        project_name = pcb_entity.project_name

        total_number = pcb_entity.comp_count

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        cur_time = datetime.now().strftime("%Y%m%d")

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator
                    robot_ng_str = comp_entity.robot_ng_str
                    repair_ng_str = comp_entity.repair_ng_str
                    comp_data_list.append(f"{comp_tag},{robot_ng_str},{repair_ng_str}")

            comp_ng_str = ""
            if comp_data_list:
                comp_ng_str = f";{';'.join(comp_data_list)}"

            r1 = board_entity.get_robot_result()
            r2 = board_entity.get_repair_result()
            r3 = board_entity.get_final_result("OK", "OK", "OK", "SKIP")  # 是否是坏板

            board_data_str = f"{project_name},{barcode},{line_id},{r1},{r2},{r3},{work_id},{track_index},{total_number},{time_file},{board_no}{comp_ng_str}"

            board_data_list.append({
                f"board_sn{board_no}": board_data_str
            })

        pcb_param = {
            "MO": order_id,
            "LINE": line_id,
            "STATION": station_id,
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "board_data": board_data_list,
        }

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        os.makedirs(f"{log_path}/{cur_time}", exist_ok=True)

        filepath = f"{log_path}/{cur_time}/{time_file}.txt"
        xutil.FileUtil.write_content_to_file_pro(filepath, json.dumps(
            pcb_param,
            indent=4,
            ensure_ascii=False
        ))

        ret = xrequest.RequestUtil.post_json(api_url, pcb_param)
        if ret.get("retcode") != 200:
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

        return self.x_response()
