#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用户友好的日志分析界面
专为非IT用户设计的简化界面
"""

import sys
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QTabWidget, QTextEdit, 
                            QLineEdit, QPushButton, QLabel, QComboBox, QCheckBox,
                            QTableWidget, QTableWidgetItem, QSplitter, QGroupBox,
                            QDateTimeEdit, QSpinBox, QProgressBar, QMessageBox,
                            QFileDialog, QListWidget, QTreeWidget, QTreeWidgetItem,
                            QFrame, QScrollArea, QTextBrowser)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDateTime, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap, QPalette

try:
    from .log_parser import LogParser, LogEntry
    from .log_searcher import LogSearcher, SearchCriteria
    from .error_analyzer import ErrorAnalyzer
    from .business_analyzer import BusinessAnalyzer
    from .config_manager import ConfigManager
except ImportError:
    from log_parser import LogParser, LogEntry
    from log_searcher import LogSearcher, SearchCriteria
    from error_analyzer import ErrorAnalyzer
    from business_analyzer import BusinessAnalyzer
    from config_manager import ConfigManager


class BusinessAnalysisThread(QThread):
    """业务分析线程"""
    progress_updated = pyqtSignal(int)
    analysis_completed = pyqtSignal(list, dict, dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, log_files):
        super().__init__()
        self.log_files = log_files
        
    def run(self):
        try:
            parser = LogParser()
            business_analyzer = BusinessAnalyzer()
            all_entries = []
            
            for i, file_path in enumerate(self.log_files):
                self.progress_updated.emit(int((i / len(self.log_files)) * 100))
                entries = parser.parse_log_file(file_path)
                all_entries.extend(entries)
            
            # 业务分析
            business_summary = business_analyzer.get_business_summary(all_entries)
            quick_diagnosis = business_analyzer.get_quick_diagnosis(all_entries)
            
            self.analysis_completed.emit(all_entries, business_summary, quick_diagnosis)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class UserFriendlyMainWindow(QMainWindow):
    """用户友好的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.entries = []
        self.business_analyzer = BusinessAnalyzer()
        self.config_manager = ConfigManager()
        
        self.init_ui()
        self.setup_connections()
        self.setup_auto_refresh()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("MES系统日志监控助手")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            .warning-button {
                background-color: #ff9800;
            }
            .danger-button {
                background-color: #f44336;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题区域
        title_layout = self.create_title_area()
        main_layout.addLayout(title_layout)
        
        # 创建快速操作区域
        quick_actions = self.create_quick_actions()
        main_layout.addWidget(quick_actions)
        
        # 创建主要内容区域
        content_area = self.create_content_area()
        main_layout.addWidget(content_area)
        
        # 状态栏
        self.statusBar().showMessage("系统就绪 - 请选择日志文件开始监控")
        
    def create_title_area(self):
        """创建标题区域"""
        layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("MES系统日志监控助手")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # 状态指示器
        self.status_indicator = QLabel("●")
        self.status_indicator.setFont(QFont("Arial", 20))
        self.status_indicator.setStyleSheet("color: #95a5a6;")  # 灰色表示未连接
        layout.addWidget(QLabel("系统状态:"))
        layout.addWidget(self.status_indicator)
        
        return layout
        
    def create_quick_actions(self):
        """创建快速操作区域"""
        group = QGroupBox("快速操作")
        layout = QHBoxLayout(group)
        
        # 选择日志文件
        self.log_path_edit = QLineEdit()
        self.log_path_edit.setPlaceholderText("请选择MES系统日志文件夹...")
        layout.addWidget(QLabel("日志位置:"))
        layout.addWidget(self.log_path_edit)
        
        self.browse_btn = QPushButton("📁 浏览")
        layout.addWidget(self.browse_btn)
        
        self.start_monitor_btn = QPushButton("🔍 开始监控")
        layout.addWidget(self.start_monitor_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group
        
    def create_content_area(self):
        """创建主要内容区域"""
        tab_widget = QTabWidget()
        
        # 系统状态标签页
        status_tab = self.create_status_tab()
        tab_widget.addTab(status_tab, "📊 系统状态")
        
        # 问题诊断标签页
        diagnosis_tab = self.create_diagnosis_tab()
        tab_widget.addTab(diagnosis_tab, "🔧 问题诊断")
        
        # 业务监控标签页
        business_tab = self.create_business_tab()
        tab_widget.addTab(business_tab, "📈 业务监控")
        
        # 详细日志标签页（为开发人员准备）
        detail_tab = self.create_detail_tab()
        tab_widget.addTab(detail_tab, "📋 详细日志")
        
        return tab_widget
        
    def create_status_tab(self):
        """创建系统状态标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 快速诊断卡片
        diagnosis_card = QGroupBox("🚀 快速诊断")
        diagnosis_layout = QVBoxLayout(diagnosis_card)
        
        self.diagnosis_status = QLabel("等待分析...")
        self.diagnosis_status.setFont(QFont("Arial", 14, QFont.Bold))
        self.diagnosis_status.setStyleSheet("color: #7f8c8d; padding: 10px;")
        diagnosis_layout.addWidget(self.diagnosis_status)
        
        self.diagnosis_detail = QTextBrowser()
        self.diagnosis_detail.setMaximumHeight(100)
        self.diagnosis_detail.setStyleSheet("background-color: white; border: 1px solid #ddd;")
        diagnosis_layout.addWidget(self.diagnosis_detail)
        
        layout.addWidget(diagnosis_card)
        
        # 统计卡片区域
        stats_layout = QHBoxLayout()
        
        # 业务模块统计
        self.business_stats_card = self.create_stats_card("📦 业务模块", "等待数据...")
        stats_layout.addWidget(self.business_stats_card)
        
        # 问题统计
        self.problem_stats_card = self.create_stats_card("⚠️ 问题统计", "等待数据...")
        stats_layout.addWidget(self.problem_stats_card)
        
        # 运行状态
        self.runtime_stats_card = self.create_stats_card("⏱️ 运行状态", "等待数据...")
        stats_layout.addWidget(self.runtime_stats_card)
        
        layout.addLayout(stats_layout)
        
        # 最近活动
        recent_activity = QGroupBox("📝 最近活动")
        recent_layout = QVBoxLayout(recent_activity)
        
        self.recent_activity_list = QListWidget()
        self.recent_activity_list.setStyleSheet("background-color: white;")
        recent_layout.addWidget(self.recent_activity_list)
        
        layout.addWidget(recent_activity)
        
        return widget
        
    def create_diagnosis_tab(self):
        """创建问题诊断标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主要问题区域
        main_issues = QGroupBox("🔥 主要问题")
        main_layout = QVBoxLayout(main_issues)
        
        self.main_issues_table = QTableWidget()
        self.main_issues_table.setColumnCount(4)
        self.main_issues_table.setHorizontalHeaderLabels(["问题类型", "发生次数", "严重程度", "建议措施"])
        self.main_issues_table.setStyleSheet("background-color: white;")
        main_layout.addWidget(self.main_issues_table)
        
        layout.addWidget(main_issues)
        
        # 解决方案区域
        solutions = QGroupBox("💡 解决方案")
        solutions_layout = QVBoxLayout(solutions)
        
        self.solutions_text = QTextBrowser()
        self.solutions_text.setStyleSheet("background-color: white; font-size: 12px;")
        solutions_layout.addWidget(self.solutions_text)
        
        layout.addWidget(solutions)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 刷新分析")
        action_layout.addWidget(self.refresh_btn)
        
        self.export_report_btn = QPushButton("📄 导出报告")
        action_layout.addWidget(self.export_report_btn)
        
        self.contact_support_btn = QPushButton("📞 联系技术支持")
        self.contact_support_btn.setStyleSheet("background-color: #e74c3c;")
        action_layout.addWidget(self.contact_support_btn)
        
        action_layout.addStretch()
        layout.addLayout(action_layout)
        
        return widget
        
    def create_business_tab(self):
        """创建业务监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 业务流程监控
        process_monitor = QGroupBox("🔄 业务流程监控")
        process_layout = QGridLayout(process_monitor)
        
        # 创建业务流程状态卡片
        self.barcode_status = self.create_process_card("条码校验", "正常", "#4CAF50")
        process_layout.addWidget(self.barcode_status, 0, 0)
        
        self.recheck_status = self.create_process_card("复判流程", "正常", "#4CAF50")
        process_layout.addWidget(self.recheck_status, 0, 1)
        
        self.mes_comm_status = self.create_process_card("MES通信", "正常", "#4CAF50")
        process_layout.addWidget(self.mes_comm_status, 0, 2)
        
        self.data_upload_status = self.create_process_card("数据上传", "正常", "#4CAF50")
        process_layout.addWidget(self.data_upload_status, 1, 0)
        
        self.device_status = self.create_process_card("设备状态", "正常", "#4CAF50")
        process_layout.addWidget(self.device_status, 1, 1)
        
        self.quality_status = self.create_process_card("质量检测", "正常", "#4CAF50")
        process_layout.addWidget(self.quality_status, 1, 2)
        
        layout.addWidget(process_monitor)
        
        # 业务趋势
        trend_monitor = QGroupBox("📈 业务趋势")
        trend_layout = QVBoxLayout(trend_monitor)
        
        self.trend_text = QTextBrowser()
        self.trend_text.setMaximumHeight(200)
        self.trend_text.setStyleSheet("background-color: white;")
        trend_layout.addWidget(self.trend_text)
        
        layout.addWidget(trend_monitor)
        
        return widget
        
    def create_detail_tab(self):
        """创建详细日志标签页（为开发人员）"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 搜索区域
        search_group = QGroupBox("🔍 日志搜索（开发人员专用）")
        search_layout = QHBoxLayout(search_group)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索日志...")
        search_layout.addWidget(self.search_edit)
        
        self.search_btn = QPushButton("搜索")
        search_layout.addWidget(self.search_btn)
        
        self.level_filter = QComboBox()
        self.level_filter.addItems(["全部级别", "ERROR", "WARNING", "INFO", "DEBUG"])
        search_layout.addWidget(self.level_filter)
        
        layout.addWidget(search_group)
        
        # 日志表格
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(5)
        self.log_table.setHorizontalHeaderLabels(["时间", "级别", "文件", "行号", "消息"])
        self.log_table.setStyleSheet("background-color: white;")
        layout.addWidget(self.log_table)
        
        return widget
        
    def create_stats_card(self, title: str, content: str) -> QGroupBox:
        """创建统计卡片"""
        card = QGroupBox(title)
        card.setStyleSheet("""
            QGroupBox {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(card)
        content_label = QLabel(content)
        content_label.setFont(QFont("Arial", 12))
        content_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(content_label)
        
        return card
        
    def create_process_card(self, title: str, status: str, color: str) -> QGroupBox:
        """创建业务流程状态卡片"""
        card = QGroupBox(title)
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                font-weight: bold;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        status_label = QLabel(status)
        status_label.setFont(QFont("Arial", 14, QFont.Bold))
        status_label.setStyleSheet(f"color: {color};")
        status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(status_label)
        
        return card
        
    def setup_connections(self):
        """设置信号连接"""
        self.browse_btn.clicked.connect(self.browse_log_folder)
        self.start_monitor_btn.clicked.connect(self.start_monitoring)
        self.refresh_btn.clicked.connect(self.refresh_analysis)
        self.export_report_btn.clicked.connect(self.export_business_report)
        self.contact_support_btn.clicked.connect(self.contact_support)
        self.search_btn.clicked.connect(self.search_logs)
        
    def setup_auto_refresh(self):
        """设置自动刷新"""
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh)
        # 每5分钟自动刷新一次
        self.auto_refresh_timer.start(300000)
        
    def browse_log_folder(self):
        """浏览日志文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择MES系统日志文件夹")
        if folder:
            self.log_path_edit.setText(folder)
            self.config_manager.add_recent_path(folder)
            
    def start_monitoring(self):
        """开始监控"""
        path = self.log_path_edit.text().strip()
        if not path:
            QMessageBox.warning(self, "提示", "请先选择日志文件夹")
            return
            
        if not os.path.exists(path):
            QMessageBox.warning(self, "提示", "选择的路径不存在")
            return
            
        # 扫描日志文件
        parser = LogParser()
        log_files = parser.scan_log_directory(path)
        
        if not log_files:
            QMessageBox.warning(self, "提示", "在选择的文件夹中未找到日志文件")
            return
            
        # 启动分析线程
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.start_monitor_btn.setEnabled(False)
        self.start_monitor_btn.setText("分析中...")
        
        self.analysis_thread = BusinessAnalysisThread(log_files)
        self.analysis_thread.progress_updated.connect(self.progress_bar.setValue)
        self.analysis_thread.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_thread.error_occurred.connect(self.on_analysis_error)
        self.analysis_thread.start()
        
    def on_analysis_completed(self, entries, business_summary, quick_diagnosis):
        """分析完成处理"""
        self.entries = entries
        
        self.progress_bar.setVisible(False)
        self.start_monitor_btn.setEnabled(True)
        self.start_monitor_btn.setText("🔍 开始监控")
        
        # 更新状态指示器
        status = quick_diagnosis['status']
        if status == '正常':
            self.status_indicator.setStyleSheet("color: #27ae60;")  # 绿色
        elif status == '警告':
            self.status_indicator.setStyleSheet("color: #f39c12;")  # 橙色
        else:
            self.status_indicator.setStyleSheet("color: #e74c3c;")  # 红色
        
        # 更新各个界面
        self.update_status_tab(business_summary, quick_diagnosis)
        self.update_diagnosis_tab(business_summary, quick_diagnosis)
        self.update_business_tab(business_summary)
        self.update_detail_tab(entries)
        
        self.statusBar().showMessage(f"分析完成 - {quick_diagnosis['summary']}")
        
    def on_analysis_error(self, error_msg):
        """分析错误处理"""
        self.progress_bar.setVisible(False)
        self.start_monitor_btn.setEnabled(True)
        self.start_monitor_btn.setText("🔍 开始监控")
        QMessageBox.critical(self, "错误", f"分析日志时出错: {error_msg}")
        
    def update_status_tab(self, business_summary, quick_diagnosis):
        """更新系统状态标签页"""
        # 更新快速诊断
        status = quick_diagnosis['status']
        self.diagnosis_status.setText(f"系统状态: {status}")
        
        if status == '正常':
            self.diagnosis_status.setStyleSheet("color: #27ae60; padding: 10px;")
        elif status == '警告':
            self.diagnosis_status.setStyleSheet("color: #f39c12; padding: 10px;")
        else:
            self.diagnosis_status.setStyleSheet("color: #e74c3c; padding: 10px;")
        
        self.diagnosis_detail.setText(quick_diagnosis['summary'])
        
        # 更新统计卡片
        self.update_stats_cards(business_summary)
        
        # 更新最近活动
        self.update_recent_activity(business_summary)
        
    def update_stats_cards(self, business_summary):
        """更新统计卡片"""
        # 业务模块统计
        business_text = ""
        for category, count in business_summary['category_stats'].items():
            business_text += f"{category}: {count}次\n"
        if not business_text:
            business_text = "暂无数据"
        
        # 更新卡片内容
        for child in self.business_stats_card.children():
            if isinstance(child, QLabel):
                child.setText(business_text.strip())
                break
        
        # 问题统计
        problem_text = ""
        for severity, count in business_summary['severity_stats'].items():
            if count > 0:
                problem_text += f"{severity}: {count}次\n"
        if not problem_text:
            problem_text = "无问题"
        
        for child in self.problem_stats_card.children():
            if isinstance(child, QLabel):
                child.setText(problem_text.strip())
                break
        
        # 运行状态
        runtime_text = f"监控场景: {business_summary['total_scenarios']}种\n"
        runtime_text += f"分析时间: {datetime.now().strftime('%H:%M:%S')}"
        
        for child in self.runtime_stats_card.children():
            if isinstance(child, QLabel):
                child.setText(runtime_text)
                break
                
    def update_recent_activity(self, business_summary):
        """更新最近活动"""
        self.recent_activity_list.clear()
        
        for issue in business_summary['top_issues'][:5]:
            item_text = f"[{issue['severity']}] {issue['name']} - {issue['count']}次"
            self.recent_activity_list.addItem(item_text)
        
        if not business_summary['top_issues']:
            self.recent_activity_list.addItem("暂无异常活动")
            
    def update_diagnosis_tab(self, business_summary, quick_diagnosis):
        """更新问题诊断标签页"""
        # 更新主要问题表格
        issues = business_summary['top_issues']
        self.main_issues_table.setRowCount(len(issues))
        
        for row, issue in enumerate(issues):
            self.main_issues_table.setItem(row, 0, QTableWidgetItem(issue['name']))
            self.main_issues_table.setItem(row, 1, QTableWidgetItem(str(issue['count'])))
            self.main_issues_table.setItem(row, 2, QTableWidgetItem(issue['severity']))
            
            solutions = issue.get('solutions', [])
            solution_text = solutions[0] if solutions else "暂无建议"
            self.main_issues_table.setItem(row, 3, QTableWidgetItem(solution_text))
        
        self.main_issues_table.resizeColumnsToContents()
        
        # 更新解决方案
        solutions_html = "<h3>💡 系统建议</h3>"
        
        if quick_diagnosis['main_issue']:
            main_issue = quick_diagnosis['main_issue']
            solutions_html += f"<h4>主要问题: {main_issue['name']}</h4>"
            solutions_html += "<ol>"
            for solution in main_issue['solutions']:
                solutions_html += f"<li>{solution}</li>"
            solutions_html += "</ol>"
        
        if business_summary['recommendations']:
            solutions_html += "<h4>改进建议:</h4>"
            solutions_html += "<ul>"
            for rec in business_summary['recommendations']:
                solutions_html += f"<li>{rec}</li>"
            solutions_html += "</ul>"
        
        self.solutions_text.setHtml(solutions_html)
        
    def update_business_tab(self, business_summary):
        """更新业务监控标签页"""
        # 更新业务流程状态卡片
        category_stats = business_summary['category_stats']
        
        # 根据问题数量更新卡片颜色和状态
        self.update_process_card_status(self.barcode_status, "条码管理", category_stats)
        self.update_process_card_status(self.recheck_status, "复判流程", category_stats)
        self.update_process_card_status(self.mes_comm_status, "MES通信", category_stats)
        self.update_process_card_status(self.data_upload_status, "数据传输", category_stats)
        self.update_process_card_status(self.device_status, "设备监控", category_stats)
        self.update_process_card_status(self.quality_status, "质量控制", category_stats)
        
        # 更新业务趋势
        trend_html = "<h3>📈 业务运行趋势</h3>"
        
        if business_summary['top_issues']:
            trend_html += "<h4>主要关注点:</h4><ul>"
            for issue in business_summary['top_issues'][:3]:
                trend_html += f"<li><b>{issue['name']}</b>: 发生{issue['count']}次</li>"
            trend_html += "</ul>"
        else:
            trend_html += "<p>✅ 所有业务流程运行正常</p>"
        
        if business_summary['recommendations']:
            trend_html += "<h4>优化建议:</h4><ul>"
            for rec in business_summary['recommendations']:
                trend_html += f"<li>{rec}</li>"
            trend_html += "</ul>"
        
        self.trend_text.setHtml(trend_html)
        
    def update_process_card_status(self, card, category, category_stats):
        """更新业务流程卡片状态"""
        count = category_stats.get(category, 0)
        
        if count == 0:
            status = "正常"
            color = "#4CAF50"  # 绿色
        elif count <= 3:
            status = f"注意 ({count})"
            color = "#ff9800"  # 橙色
        else:
            status = f"异常 ({count})"
            color = "#f44336"  # 红色
        
        # 更新卡片样式
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                font-weight: bold;
            }}
        """)
        
        # 更新状态文本
        for child in card.children():
            if isinstance(child, QLabel):
                child.setText(status)
                child.setStyleSheet(f"color: {color};")
                break
                
    def update_detail_tab(self, entries):
        """更新详细日志标签页"""
        # 显示最近的100条日志
        recent_entries = entries[-100:] if len(entries) > 100 else entries
        
        self.log_table.setRowCount(len(recent_entries))
        
        for row, entry in enumerate(recent_entries):
            time_str = entry.timestamp.strftime('%H:%M:%S') if entry.timestamp else ""
            self.log_table.setItem(row, 0, QTableWidgetItem(time_str))
            
            level_item = QTableWidgetItem(entry.level)
            if entry.level == 'ERROR':
                level_item.setBackground(QColor(255, 200, 200))
            elif entry.level == 'WARNING':
                level_item.setBackground(QColor(255, 255, 200))
            self.log_table.setItem(row, 1, level_item)
            
            file_name = os.path.basename(entry.file_path)
            self.log_table.setItem(row, 2, QTableWidgetItem(file_name))
            self.log_table.setItem(row, 3, QTableWidgetItem(str(entry.line_number)))
            
            message = entry.message[:100] + "..." if len(entry.message) > 100 else entry.message
            self.log_table.setItem(row, 4, QTableWidgetItem(message))
        
        self.log_table.resizeColumnsToContents()
        
    def refresh_analysis(self):
        """刷新分析"""
        if self.log_path_edit.text().strip():
            self.start_monitoring()
        else:
            QMessageBox.information(self, "提示", "请先选择日志文件夹")
            
    def auto_refresh(self):
        """自动刷新"""
        if self.log_path_edit.text().strip() and self.entries:
            self.refresh_analysis()
            
    def export_business_report(self):
        """导出业务报告"""
        if not self.entries:
            QMessageBox.warning(self, "提示", "请先分析日志数据")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出业务报告", f"MES系统报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 
            "文本文件 (*.txt)")
            
        if file_path:
            try:
                report = self.business_analyzer.generate_business_report(self.entries)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                QMessageBox.information(self, "成功", f"报告已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
                
    def contact_support(self):
        """联系技术支持"""
        QMessageBox.information(self, "技术支持", 
                               "如需技术支持，请联系:\n\n"
                               "📧 邮箱: <EMAIL>\n"
                               "📞 电话: 400-xxx-xxxx\n"
                               "🕐 服务时间: 工作日 9:00-18:00")
                               
    def search_logs(self):
        """搜索日志"""
        if not self.entries:
            QMessageBox.warning(self, "提示", "请先加载日志数据")
            return
            
        keyword = self.search_edit.text().strip()
        if not keyword:
            return
            
        # 简单搜索实现
        results = []
        for entry in self.entries:
            if keyword.lower() in entry.message.lower():
                results.append(entry)
        
        # 更新表格显示搜索结果
        self.log_table.setRowCount(len(results))
        
        for row, entry in enumerate(results):
            time_str = entry.timestamp.strftime('%H:%M:%S') if entry.timestamp else ""
            self.log_table.setItem(row, 0, QTableWidgetItem(time_str))
            self.log_table.setItem(row, 1, QTableWidgetItem(entry.level))
            
            file_name = os.path.basename(entry.file_path)
            self.log_table.setItem(row, 2, QTableWidgetItem(file_name))
            self.log_table.setItem(row, 3, QTableWidgetItem(str(entry.line_number)))
            self.log_table.setItem(row, 4, QTableWidgetItem(entry.message))
        
        self.log_table.resizeColumnsToContents()
        self.statusBar().showMessage(f"搜索到 {len(results)} 条相关日志")


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("MES系统日志监控助手")
    
    window = UserFriendlyMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
