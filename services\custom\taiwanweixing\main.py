# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/2/21 上午11:36
# Author     ：sch
# version    ：python 3.8
# Description：台湾微星
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


txt_template = """{project_name}
{barcode}
{test_line}
{board_no}
{test_user}
{order_number}
{test_time}
{repair_time}
{repair_result}
{board_side}
{comp_number}
{comp_repair_ng_number}{comp_data_str}
"""


comp_template = """
{comp_tag};{comp_name};{robot_ng_code};{repair_ng_code};{repair_result}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "taiwanweixing release v1.0.0.1",
        "device": "401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-02-21 11:39  生成本地txt文档
""", }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        },
    }

    form = {
        "test_line": {
            "ui_name": "测试线别",
            "value": "",
        },
        "test_user": {
            "ui_name": "测试员工号",
            "value": "",
        },
        "order_number": {
            "ui_name": "工单号码",
            "value": "",
        },
    }

    combo = {
        "board_side": {
            "ui_name": "正背面",
            "item": ["T", "B", "T+B"],
            "value": "T",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        test_line = data_vo.get_value_by_cons_key("test_line")
        test_user = data_vo.get_value_by_cons_key("test_user")
        order_number = data_vo.get_value_by_cons_key("order_number")
        board_side = data_vo.get_value_by_cons_key("board_side")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time()

        project_name = pcb_entity.project_name

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += comp_template.format(**{
                    "comp_tag": comp_entity.designator,
                    "comp_name": comp_entity.type,
                    "robot_ng_code": comp_entity.robot_ng_code,
                    "repair_ng_code": comp_entity.repair_ng_code,
                    "repair_result": comp_entity.get_final_result("0", "0", "1")
                })

            board_content = txt_template.format(**{
                "project_name": project_name,
                "barcode": barcode,
                "board_no": board_no,
                "test_line": test_line,
                "test_user": test_user,
                "order_number": order_number,
                "board_side": board_side,
                "test_time": start_time.strftime(xcons.FMT_TIME_DEFAULT1),
                "repair_time": pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT1),
                "repair_result": board_entity.get_repair_result("PASS", "FAIL"),
                "comp_number": board_entity.comp_total_number,
                "comp_repair_ng_number": board_entity.comp_repair_ng_number,
                "comp_data_str": comp_data_str
            })

            t2 = start_time.strftime(xcons.FMT_TIME_FILE)
            filename = f"{save_path}/{test_line}_{t2}_{board_no}.txt"
            xutil.FileUtil.write_content_to_file(filename, board_content)

        return self.x_response()
