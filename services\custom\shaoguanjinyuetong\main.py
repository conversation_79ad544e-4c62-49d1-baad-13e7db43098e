# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/23 下午10:13
# Author     ：wxc
# version    ：python 3.8
# Description：韶关金悦通   韶关先进
"""
import json
import time
from typing import Any

from common import xcons, xutil, xrequest, xglobal
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from services.custom.shaoguanjinyuetong import jyt_module
from services.custom.shaoguanjinyuetong.jyt_data import queue1, jyt_data_list
from services.custom.shaoguanjinyuetong.jyt_module import get_insp_data_by_track_index
from services.custom.shaoguanjinyuetong.jyt_websocket_server import WebSocketServerThread, jrt_clients
from vo.mes_vo import DataVo, OtherVo, ButtonVo

circle_list = xutil.CircularList(200)

csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

txt_pcb_panel_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
拼板检测NG数量:{pcb_board_robot_ng_number}
拼板复判NG数量:{pcb_board_user_ng_number}
拼板误报数量:{pcb_board_repass_number}
器件总数:{pcb_comp_number}
器件检测NG总数:{pcb_comp_robot_ng_number}
器件复判NG总数:{pcb_comp_user_ng_number}
器件误报总数:{pcb_comp_repass_number}

{BoardData}
"""

txt_board_panel_template = """
========================
拼板号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}{CompData}
"""

txt_comp_panel_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""

xml_pcb_panel_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_board_side="{pcb_board_side}" pcb_test_time="{pcb_test_time}" pcb_cycle_time="{pcb_cycle_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_robot_ng_number="{pcb_board_robot_ng_number}" pcb_board_user_ng_number="{pcb_board_user_ng_number}" pcb_board_repass_number="{pcb_board_repass_number}"
  pcb_comp_number="{pcb_comp_number}" pcb_comp_robot_ng_number="{pcb_comp_robot_ng_number}" pcb_comp_user_ng_number="{pcb_comp_user_ng_number}" pcb_comp_repass_number="{pcb_comp_repass_number}" >
    <BoardList>{BoardData}
    </BoardList>
</Panel>
"""

xml_board_panel_template = """
        <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}">{CompData}
        </Board>"""

xml_comp_panel_template = """
            <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""

txt_board_board_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
整板器件总数:{pcb_comp_number}
拼板序号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}
拼板器件数量:{board_comp_number}
拼板器件检测NG总数:{board_comp_robot_ng_number}
拼板器件复判NG总数:{board_comp_user_ng_number}
拼板器件误报总数:{board_comp_repass_number}

{CompData}
"""

txt_comp_board_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""

csv_board_board_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
整板器件总数,{pcb_comp_number}
拼板序号,{board_no}
拼板条码,{board_sn}
拼板检测结果,{board_robot_result}
拼板复判结果,{board_user_result}
拼板最终结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件检测NG总数,{board_comp_robot_ng_number}
拼板器件复判NG总数,{board_comp_user_ng_number}
拼板器件误报总数,{board_comp_repass_number}

器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_board_template = """
{comp_designator},unknown{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

xml_board_board_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_board_side="{pcb_board_side}" pcb_test_time="{pcb_test_time}" pcb_cycle_time="{pcb_cycle_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_number="{pcb_board_number}" pcb_comp_number="{pcb_comp_number}">
    <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}"
    board_comp_number="{board_comp_number}" board_comp_robot_ng_number="{board_comp_robot_ng_number}" board_comp_user_ng_number="{board_comp_user_ng_number}" board_comp_repass_number="{board_comp_repass_number}" >{CompData}
    </Board>
</Panel>
"""

xml_comp_board_template = """
        <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""

error_code_map = {
    "1001": "进板",
    "1002": "等待",
    "1003": "停止",
    "1004": "出板",
    "1005": "换线",
    "2001": "急停",
    "2002": "安全门开",
    "3001": "等待",
    "3002": "出板",
    "3003": "校验失败",
    "3004": "上传失败",
    "3005": "磁盘已满",
    "3006": "掉板",
    "3007": "EAP告警",
    "4001": "直通告警",
    "4002": "Mark错误",
    "4003": "板卡NG",
    "5001": "风扇停转",
    "5002": "相机Error",
    "0001": "报警解除",

    # 新增
    "1011": "检测",
    "1012": "检测2",
    "1013": "人工复检",
    "1014": "人工复检",
    "3010": "等待",
    "1021": "主软件启动",

    "1022": "离线",
    "4010": "扫码失败",
}

error_code_map_431 = {
    "2018": "进板",  # 1轨进板
    "2035": "进板",  # 2轨进板
    "2020": "启动",
    "2021": "暂停",
    "2019": "出板",  # 1轨出板
    "2036": "出板",  # 2轨出板
    "2043": "换线",  # 1轨切换板式
    "2044": "换线",  # 2轨切换板式
    "2033": "检测中",  # 1轨
    "2034": "检测中",  # 2轨
    "1017": "人工复检",  # 1轨
    "1018": "人工复检",  # 2轨
    "1019": "人工复检",  # 1轨
    "1020": "人工复检",  # 2轨
    "2039": "等待",  # 1轨
    "2040": "等待",  # 2轨
    "2037": "要板",  # 1轨
    "2041": "要板",  # 2轨
    "2038": "有板",  # 1轨
    "2042": "有板",  # 2轨
    "10000": "急停",
    "10002": "安全门开",
    "2004": "校验失败",  # 1轨
    "2005": "校验失败",  # 2轨
    "2008": "上传失败",  # 1轨
    "2009": "上传失败",  # 2轨
    "5000": "磁盘已满",
    "6005": "直通告警",
    "6006": "直通告警",
    "6007": "直通告警",
    "6008": "直通告警",
    "4000": "Mark报错",  # 1轨
    "4001": "Mark报错",  # 2轨
    "2030": "板卡NG",  # 1轨
    "2032": "板卡NG",  # 2轨
    "8102": "相机Error",
    "2000": "主软件启动",
    "2001": "离线",
    "1004": "扫码失败",  # 1轨
    "1005": "扫码失败"  # 2轨
}


class Engine(ErrorMapEngine):
    version = {
        "version": "release v1.0.0.16",
        "customer": ["韶关金悦通", "shaoguanjinyuetong"],
        "device": "AIS630,AIS430,AIS431",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-04-23 10:14  ATAOI_2019-38284:标准版+json数据-整板部分，加上程序制作员（pcb_program_developer）
date: 2025-05-07 17:03  ATAOI_2019-39293: 发送设备状态
date: 2025-05-07 22:11  ATAOI_2019-39293: 分轨道统计数据  getStatus根据轨道正在运行的板式来响应数据
date: 2025-05-08 10:25  ATAOI_2019-39293: 增加清零功能，并且优化部分代码
date: 2025-05-09 10:26  ATAOI_2019-39293: 轨道号强转成int类型
date: 2025-05-09 14:56  推送的设备状态, data需要序列化
date: 2025-05-10 12:15  设备状态优化
date: 2025-05-12 18:16  新增五个设备状态，并且优化部分 websocket 链接
date: 2025-05-14 19:42  新增设备状态，以及增加按钮类时间推送
date: 2025-05-15 11:44  换线状态根据轨道区分，并且不过滤重复发送
date: 2025-05-15 15:52  bugfix: 轨道2数据多了一层数组
date: 2025-05-15 17:47  退出主软件时，推送离线状态
date: 2025-07-09 15:21  兼容AIS431设备状态
date: 2025-07-10 15:56  根据检测软件提供的最新状态码来修改兼容
""", }

    other_combo = {
        "is_upload_mes": {
            "ui_name": "上传json数据到Mes服务器",
            "item": [
                "是",
                "否"
            ],
            "value": "否"
        },
        "is_upload_ftp": {
            "ui_name": "上传数据到ftp服务器",
            "item": [
                "是",
                "否"
            ],
            "value": "否"
        },
        "ftp_path_rule": {
            "ui_name": "FTP路径规则",
            "item": [
                "FTP根路径",
                "FTP根路径/设备名称",
                "FTP根路径/日期(yyyymmdd)",
                "FTP根路径/设备名称/日期(yyyymmdd)",
                'FTP根路径/日期(yyyymmdd)/设备名称',
            ],
            "value": "FTP根路径/设备名称/日期(yyyymmdd)"
        },
        "ftp_encoding": {
            "ui_name": "FTP编码",
            "item": [
                "utf-8",
                "gbk"
            ],
            "value": "utf-8"
        },
        "auto_start_server": {
            "ui_name": "自动启动服务器",
            "item": [
                "Yes",
                "No"
            ],
            "value": "Yes"
        },
    }

    other_form = {
        "mes_api_url": {
            "ui_name": "Mes接口URL",
            "value": ""
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP密码",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP根路径",
            "value": "/MES/SMT"
        },
    }

    combo = {
        "is_save_local": {
            "ui_name": "保存数据到本地",
            "item": [
                "是",
                "否",
            ],
            "value": "是"
        },
        # "is_save_comp_image": {
        #     "ui_name": "保存NG器件图到本地",
        #     "item": [
        #         "是",
        #         "否",
        #     ],
        #     "value": "否"
        # },
        "file_type": {
            "ui_name": "文件类型",
            "item": [
                "csv",
                "txt",
                "json",
                "xml",
            ],
            "value": "csv"
        },
        "save_type": {
            "ui_name": "文件生成方式",
            "item": [
                "整板生成",
                "拼板生成",
            ],
            "value": "整板生成"
        },
        "panel_filename": {
            "ui_name": "整板文件名格式",
            "item": [
                "时间_整板条码",
                "整板条码_时间",
                "整板条码",
                "时间",
                "---",
            ],
            "value": "时间_整板条码"
        },
        "board_filename": {
            "ui_name": "拼板文件名格式",
            "item": [
                '时间_拼板条码',
                '拼板条码_时间',
                '拼板条码',
                '时间_拼板条码_拼板序号',
                '时间_拼板序号',
                '拼板序号_时间',
                "---",
            ],
            "value": "时间_拼板序号"
        },
        "save_path_type": {
            "ui_name": "保存数据路径格式",
            "item": [
                '数据路径',
                '数据路径/日期(yyyymmdd)',
                '数据路径/设备名称',
                '数据路径/设备名称/日期(yyyymmdd)',
                '数据路径/日期(yyyymmdd)/设备名称',
            ],
            "value": "数据路径/设备名称/日期(yyyymmdd)"
        },
        # "save_image_path_fmt": {
        #     "ui_name": "保存NG器件图路径格式",
        #     "item": [
        #         '图片路径/条码_拼板号_位号_NG描述.png',
        #         '图片路径/日期(yyyymmdd)/条码_拼板号_位号_NG描述.png',
        #         '图片路径/设备名称/条码_拼板号_位号_NG描述.png',
        #         '图片路径/设备名称/日期(yyyymmdd)/条码_拼板号_位号_NG描述.png',
        #         '图片路径/日期(yyyymmdd)/设备名称/条码_拼板号_位号_NG描述.png',
        #     ],
        #     "value": "图片路径/日期(yyyymmdd)/设备名称/条码_拼板号_位号_NG描述.png"
        # },
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "全部"
        },
        "newline_type": {
            "ui_name": "换行符格式",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        },
    }

    path = {
        "save_path": {
            "ui_name": "数据路径",
            "value": ""
        },
        # "save_image_path": {
        #     "ui_name": "图片路径",
        #     "value": ""
        # }
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "AOI001"
        },
        # "web_socket_host": {
        #     "ui_name": "服务器IP",
        #     "value": "0.0.0.0"
        # },
        "web_socket_port": {
            "ui_name": "服务器端口",
            "value": "9393"
        },
        "device_id_1": {
            "ui_name": "设备ID(1轨)",
            "value": "AOI001"
        },
        "device_id_2": {
            "ui_name": "设备ID(2轨)",
            "value": "AOI002"
        },
    }

    button = {
        "start_server": {
            "ui_name": "✅ 启动服务器",
        },
        "stop_server": {
            "ui_name": "🛑 关闭服务器",
        },
    }

    def __init__(self):
        self.server_thread = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        device_id_1 = other_vo.get_value_by_cons_key("device_id_1")
        device_id_2 = other_vo.get_value_by_cons_key("device_id_2")

        xutil.CacheUtil.append_or_update_new_data({
            "device_id_1": device_id_1,
            "device_id_2": device_id_2,
        })

        web_socket_port = other_vo.get_value_by_cons_key("web_socket_port")
        auto_start_server = other_vo.get_value_by_cons_key("auto_start_server")
        web_socket_host = "0.0.0.0"

        self.server_thread = WebSocketServerThread(web_socket_host, web_socket_port)
        self.server_thread.daemon = True  # 设置为守护线程

        stop_btn_obj = getattr(main_window, f"btn_stop_server")
        stop_btn_obj.setEnabled(False)
        start_btn_obj = getattr(main_window, f"btn_start_server")
        start_btn_obj.setEnabled(True)

        if auto_start_server == "Yes":
            self.server_thread.start()
            start_ret = queue1.get(timeout=3)
            if start_ret:
                main_window.log_info(f"websocket服务启动成功！运行端口：{web_socket_port}")
                start_btn_obj.setEnabled(False)
                stop_btn_obj.setEnabled(True)
            else:
                main_window.log_info(f"websocket服务启动失败！", False)

    def _cache_data_and_upload_data_to_websocket(self, data_vo: DataVo):
        pcb_entity = data_vo.pcb_entity
        track_index = pcb_entity.get_track_index()

        review_path = data_vo.get_review_path()

        if not circle_list.is_exist_item(review_path):
            # 1. 统计数据，并且记录最后一个板式的宽度

            if track_index == 1:
                device_id = data_vo.get_value_by_cons_key("device_id_1")
            else:
                device_id = data_vo.get_value_by_cons_key("device_id_2")

            time_now = int(time.time() * 1000)

            cache_data = xutil.CacheUtil.get_cache_data()
            project_name = pcb_entity.project_name

            xglobal.global_data[f"track{track_index}"] = project_name  # 缓存一下，当前轨道运行的板式
            # track_width = jyt_module.get_track_width_by_file(project_name)

            insp_data = get_insp_data_by_track_index(track_index)
            pass_count = insp_data.get("passCount", 0)
            repass_count = insp_data.get("repassCount", 0)
            ng_count = insp_data.get("ngCount", 0)

            total_count = pass_count + repass_count + ng_count
            cache_data[f"device_status_time_{track_index}"] = time_now

            xutil.CacheUtil.append_or_update_new_data(cache_data)
            self.log.info(f"数据缓存完毕！")

            # 2. 上传数据到websocket客户端
            upload_param = {
                "type": "partialStatusUpdate",
                "data": xutil.OtherUtil.obj_to_json({
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "ProductionFileName": project_name,
                            "CurrentProductionQuantity": str(total_count),
                            "DirectPassCount": str(pass_count),
                            "NGCount": str(repass_count),
                            "FalsePositiveCount": str(ng_count),
                        }
                    }
                })
            }

            server_status = xglobal.global_data.get("jrt_server_is_start")
            self.log.info(f"client number: {len(jrt_clients)}")
            self.log.info(f"server status: {server_status}")

            if server_status and len(jrt_clients) > 0:
                jyt_data_list.append(upload_param)

            circle_list.add_item(review_path)

    def _repair_send_mes(self, data_vo: DataVo):
        """
        标准版的发送mes
        """
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        is_upload_mes = data_vo.get_value_by_cons_key("is_upload_mes")
        mes_api_url = data_vo.get_value_by_cons_key("mes_api_url")
        is_save_local = data_vo.get_value_by_cons_key("is_save_local")
        file_type = data_vo.get_value_by_cons_key("file_type")
        save_type = data_vo.get_value_by_cons_key("save_type")
        panel_filename = data_vo.get_value_by_cons_key("panel_filename")
        board_filename = data_vo.get_value_by_cons_key("board_filename")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path_type = data_vo.get_value_by_cons_key("save_path_type")
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")
        newline_type = data_vo.get_value_by_cons_key("newline_type")

        is_upload_ftp = data_vo.get_value_by_cons_key("is_upload_ftp")
        ftp_path_rule = data_vo.get_value_by_cons_key("ftp_path_rule")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        ftp_encoding = data_vo.get_value_by_cons_key("ftp_encoding")

        if save_type == "整板生成" and panel_filename == "---":
            return self.x_response("false", "请选择整板文件名格式！")

        if save_type == "拼板生成" and board_filename == "---":
            return self.x_response("false", "请选择拼板文件名格式！")

        if file_type != "json" and is_upload_mes == "是":
            return self.x_response("false", "目前仅支持上传json格式到Mes服务器，请将文件类型切换成json！")

        self.log.info(f"文件生成格式：{file_type}")
        pcb_program_developer = jyt_module.get_project_create_user(pcb_entity.project_name)

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        time_date = time_str[:8]
        if save_path_type == "数据路径":
            dst_path = f"{save_path}"
        elif save_path_type == "数据路径/日期(yyyymmdd)":
            dst_path = f"{save_path}/{time_date}"
        elif save_path_type == "数据路径/设备名称":
            dst_path = f"{save_path}/{device_name}"
        elif save_path_type == "数据路径/设备名称/日期(yyyymmdd)":
            dst_path = f"{save_path}/{device_name}/{time_date}"
        elif save_path_type == "数据路径/日期(yyyymmdd)/设备名称":
            dst_path = f"{save_path}/{time_date}/{device_name}"
        else:
            return self.x_response("false", f"不支持的数据路径格式：{save_path_type}")

        if is_save_local == "是":
            xutil.FileUtil.ensure_dir_exist(dst_path)

        if is_upload_ftp == "是":
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port, encoding=ftp_encoding)
            ftp_client.login()

            if ftp_path_rule == "FTP根路径/设备名称":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{device_name}")
            elif ftp_path_rule == "FTP根路径/日期(yyyymmdd)":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{time_date}")
            elif ftp_path_rule == "FTP根路径/设备名称/日期(yyyymmdd)":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{device_name}/{time_date}")
            elif ftp_path_rule == "FTP根路径/日期(yyyymmdd)/设备名称":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{time_date}/{device_name}")
            else:
                ftp_client.cd_or_mkdir(ftp_path)

        else:
            ftp_client = None

        if save_type == "整板生成":
            board_data = []
            pcb_board_user_ng_number = 0
            pcb_board_robot_ng_number = 0
            pcb_comp_user_ng_number = 0
            pcb_comp_robot_ng_number = 0
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                barcode = board_entity.barcode
                board_no = board_entity.board_no

                if board_entity.is_repair_ng():
                    pcb_board_user_ng_number += 1

                if board_entity.is_robot_ng():
                    pcb_board_robot_ng_number += 1

                pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
                pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

                comp_data = []
                for comp_entity in board_entity.yield_comp_entity():
                    print(comp_entity)

                    if comp_data_output == "全部" or \
                            (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                        comp_data.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

                board_data.append({
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_data": comp_data,
                })
            pcb_data = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
                "pcb_board_user_ng_number": pcb_board_user_ng_number,
                "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
                "pcb_comp_number": pcb_entity.comp_count,
                "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
                "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
                "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
                "board_data": board_data
            }

            if panel_filename == "时间_整板条码":
                dst_filename = f"{time_str}_{pcb_sn}"
            elif panel_filename == "整板条码_时间":
                dst_filename = f"{pcb_sn}_{time_str}"
            elif panel_filename == "整板条码":
                dst_filename = f"{pcb_sn}"
            elif panel_filename == "时间":
                dst_filename = f"{time_str}"
            else:
                return self.x_response("false", f"不支持的整板文件名格式：{panel_filename}")

            if file_type == "csv":
                dst_filename = f"{dst_filename}.csv"
                dst_filepath = f"{dst_path}/{dst_filename}"

                comp_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    for _comp_data in _board_data.get("comp_data", {}):
                        _comp_data.update(_board_data)
                        comp_data_str += csv_comp_panel_template.format(**_comp_data)

                pcb_data["CompData"] = comp_data_str
                pcb_content = csv_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                if is_save_local == "是":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

                if is_upload_ftp == "是":
                    ftp_client.upload_content(dst_filename, pcb_content)

            elif file_type == "txt":
                dst_filename = f"{dst_filename}.txt"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += txt_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += txt_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = txt_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                if is_save_local == "是":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

                if is_upload_ftp == "是":
                    ftp_client.upload_content(dst_filename, pcb_content)

            elif file_type == "json":
                dst_filename = f"{dst_filename}.json"
                dst_filepath = f"{dst_path}/{dst_filename}"

                pcb_data["pcb_program_developer"] = pcb_program_developer
                pcb_data_str = json.dumps(pcb_data, indent=4, ensure_ascii=False)

                if newline_type == 'window':
                    pcb_data_str = pcb_data_str.replace('\n', '\r\n')

                if is_save_local == "是":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_data_str)

                if is_upload_mes == "是":
                    ret = xrequest.RequestUtil.post_json(mes_api_url, pcb_data)
                    if str(ret.get("code")) != "200":
                        return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

                if is_upload_ftp == "是":
                    ftp_client.upload_content(dst_filename, pcb_data_str)

            elif file_type == "xml":
                dst_filename = f"{dst_filename}.xml"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += xml_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += xml_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = xml_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                if is_save_local == "是":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

                if is_upload_ftp == "是":
                    ftp_client.upload_content(dst_filename, pcb_content)

            else:
                return self.x_response("false", f"不支持的文件类型：{file_type}")

        elif save_type == "拼板生成":
            for board_entity in pcb_entity.yield_board_entity():
                board_sn = board_entity.barcode
                board_no = board_entity.board_no

                comp_data_list = []
                for comp_entity in board_entity.yield_comp_entity():

                    if comp_data_output == "全部" or \
                            (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                        comp_data_list.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

                board_data_fmt = {
                    "device_name": device_name,
                    "pcb_sn": pcb_sn,
                    "pcb_track_line": pcb_entity.track_index,
                    "pcb_board_side": pcb_entity.board_side,
                    "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                    "pcb_cycle_time": pcb_entity.get_cycle_time(),
                    "pcb_project_name": pcb_entity.project_name,
                    "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                    "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                    "pcb_final_result": pcb_entity.get_final_result(),
                    "pcb_repair_user": pcb_entity.repair_user,
                    "pcb_board_number": pcb_entity.board_count,
                    "pcb_comp_number": pcb_entity.comp_count,

                    "board_sn": board_sn,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),

                    "board_comp_number": board_entity.comp_total_number,
                    "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                    "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                    "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                }

                if board_filename == "时间_拼板条码":
                    filename = f"{time_str}_{board_sn}"
                elif board_filename == "拼板条码_时间":
                    filename = f"{board_sn}_{time_str}"
                elif board_filename == "拼板条码":
                    filename = f"{board_sn}"
                elif board_filename == "时间_拼板条码_拼板序号":
                    filename = f"{time_str}_{board_sn}_{board_no}"
                elif board_filename == "时间_拼板序号":
                    filename = f"{time_str}_{board_no}"
                elif board_filename == "拼板序号_时间":
                    filename = f"{board_no}_{time_str}"
                else:
                    return self.x_response("false", f"不支持的拼板文件名格式！")

                comp_data = ""
                if file_type == "txt":
                    filename = f"{filename}.txt"
                    for item in comp_data_list:
                        comp_data += txt_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = txt_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "是":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_ftp == "是":
                        ftp_client.upload_content(filename, board_data)

                elif file_type == "xml":
                    filename = f"{filename}.xml"
                    for item in comp_data_list:
                        comp_data += xml_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = xml_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "是":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_ftp == "是":
                        ftp_client.upload_content(filename, board_data)

                elif file_type == "json":
                    filename = f"{filename}.json"
                    board_data_fmt["comp_data"] = comp_data_list

                    board_data = json.dumps(board_data_fmt, indent=4, ensure_ascii=False)
                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "是":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_mes == "是":
                        ret = xrequest.RequestUtil.post_json(mes_api_url, board_data_fmt)
                        if str(ret.get("code")) != "200":
                            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

                    if is_upload_ftp == "是":
                        ftp_client.upload_content(filename, board_data)

                elif file_type == "csv":
                    filename = f"{filename}.csv"
                    for item in comp_data_list:
                        comp_data += csv_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = csv_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "是":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_ftp == "是":
                        ftp_client.upload_content(filename, board_data)

                else:
                    return self.x_response("false", "不支持的文件类型！")

        else:
            return self.x_response("false", f"不支持的文件生成方式！")

        if ftp_client is not None:
            ftp_client.close()

        return self.x_response()

    def _send_insp_device_data(self, data_vo: DataVo):
        """
        检测完发送设备状态到websockets
        """
        pcb_entity = data_vo.pcb_entity
        track_index = pcb_entity.get_track_index()

        if track_index == 1:
            device_id = data_vo.get_value_by_cons_key("device_id_1")
        else:
            device_id = data_vo.get_value_by_cons_key("device_id_2")

        time_now = int(time.time() * 1000)

        cache_data = xutil.CacheUtil.get_cache_data()
        project_name = pcb_entity.project_name

        xglobal.global_data[f"track{track_index}"] = project_name  # 缓存一下，当前轨道运行的板式

        insp_data = get_insp_data_by_track_index(track_index)
        pass_count = insp_data.get("passCount", 0)
        repass_count = insp_data.get("repassCount", 0)
        ng_count = insp_data.get("ngCount", 0)

        total_count = pass_count + repass_count + ng_count
        cache_data[f"device_status_time_{track_index}"] = time_now

        run_status = "运行"
        cache_data[f"device_run_status_{track_index}"] = run_status
        cache_data[f"project_name{track_index}"] = project_name

        xutil.CacheUtil.append_or_update_new_data(cache_data)
        self.log.info(f"数据缓存完毕！")

        # 2. 上传数据到websocket客户端
        upload_param = {
            "type": "partialStatusUpdate",
            "data": xutil.OtherUtil.obj_to_json({
                device_id: {
                    "DeviceID": device_id,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "ProductionFileName": project_name,
                        "CurrentProductionQuantity": str(total_count),
                        "DirectPassCount": str(pass_count),
                        "NGCount": str(repass_count),
                        "FalsePositiveCount": str(ng_count),
                    }
                }
            })
        }

        server_status = xglobal.global_data.get("jrt_server_is_start")
        self.log.info(f"client number: {len(jrt_clients)}")
        self.log.info(f"server status: {server_status}")

        if server_status and len(jrt_clients) > 0:
            jyt_data_list.append(upload_param)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()

        if inspect_type == "inspector":
            project_name = data_vo.pcb_entity.project_name
            track_index = data_vo.pcb_entity.get_track_index()

            xutil.CacheUtil.set(f"project_name{track_index}", project_name)

            xglobal.global_data[f"track{track_index}"] = project_name  # 缓存一下，当前轨道运行的板式

            return self.x_response()
        else:
            self._cache_data_and_upload_data_to_websocket(data_vo)
            return self._repair_send_mes(data_vo)

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        if btn_key == "start_server":
            self.server_thread.start()

            start_ret = queue1.get(timeout=3)
            if start_ret:
                getattr(other_param, f"btn_start_server").setEnabled(False)
                getattr(other_param, f"btn_stop_server").setEnabled(True)
            else:
                return self.x_response("false", f"服务启动失败！")

        elif btn_key == "stop_server":
            self.server_thread.stop()
            getattr(other_param, f"btn_start_server").setEnabled(True)
            getattr(other_param, f"btn_stop_server").setEnabled(False)

        return self.x_response()

    def send_device_status_to_mes_by_ais431(
            self,
            device_id: str,
            time_now: int,
            status_code_v3: str,
            track_index: int,
            cache_data: dict,
            device_id_1: str,
            device_id_2: str
    ) -> dict:
        """
        兼容AIS431设备状态
        """
        self.log.info(f"----AIS431-----")
        if status_code_v3 not in xcons.DEVICE_STATUS_431:
            self.log.warning(f"不在支持的设备状态码列表中：{status_code_v3}")
            return self.x_response()

        run_status = error_code_map_431.get(status_code_v3, "unknown")

        is_board_in = "N"
        is_board_out = "N"
        track_has_board = "N"

        if status_code_v3 in [
            "10002", "2004", "2005", "5000", "6005", "6006", "6007", "6008", "4000", "4001", "8102", "1004"
        ]:
            alarm_message = run_status
            run_status = "故障"
        else:
            alarm_message = ""
        if status_code_v3 in ["2018", "2035"]:
            is_board_in = "Y"

        if status_code_v3 in ["2019", "2036"]:
            is_board_out = "Y"

        if status_code_v3 in ["2018", "2019", "2033", "2034", "1004", "1005", "2035", "2036"]:
            track_has_board = "Y"

        if status_code_v3 in [
            "2018", "2019", "2034", "2035", "2036", "2033", "1017", "1018",
            "1019", "1020", "2039", "2040", "2034", "2037", "2041", "2038", "2042", "2043", "2044"
        ]:
            # 根据轨道区分的设备状态
            # 需要单独记录的设备状态
            cache_data[f"device_status_time_{track_index}"] = time_now
            cache_data[f"device_run_status_{track_index}"] = run_status
            cache_data[f"device_alarm_msg_{track_index}"] = alarm_message

            data = {
                device_id: {
                    "DeviceID": device_id,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "IsBoardRequest": is_board_in,  # 要板信号
                        "IsBoardEject": is_board_out,  # 出板信号
                        "TrackHasBoard": track_has_board,  # 当前是否有板
                        "AlarmMessage": alarm_message,  # 告警信息
                    }
                }
            }

        elif status_code_v3 in [
            "4000", "1004", "4001", "1005"
        ]:
            """
            mark点错误停机时，对应轨道推送mark停机，另外轨道推送停止状态   
            扫不到条码的停机报警，对应轨道推送扫不到条码的停机报警，另外轨道推送停止状态
            """
            if track_index == 1:
                cache_data["device_status_time_1"] = time_now
                cache_data["device_run_status_1"] = run_status
                cache_data["device_alarm_msg_1"] = alarm_message

                cache_data["device_status_time_2"] = time_now
                cache_data["device_run_status_2"] = "停止"
                cache_data["device_alarm_msg_2"] = ""
                data = {
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": run_status,  # 运行状态
                            "IsBoardRequest": is_board_in,  # 要板信号
                            "IsBoardEject": is_board_out,  # 出板信号
                            "TrackHasBoard": track_has_board,  # 当前是否有板
                            "AlarmMessage": alarm_message,  # 告警信息
                        }
                    },
                }

                if jyt_module.track_is_exist(2):
                    data[device_id_2] = {
                        "DeviceID": device_id_2,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": "停止",  # 运行状态
                            "IsBoardRequest": "N",  # 要板信号
                            "IsBoardEject": "N",  # 出板信号
                            "TrackHasBoard": "N",  # 当前是否有板
                            "AlarmMessage": "",  # 告警信息
                        }
                    }
            else:
                # 轨道2触发时   轨道1肯定是存在的，所以不用再判断一下
                cache_data["device_status_time_1"] = time_now
                cache_data["device_run_status_1"] = "停止"
                cache_data["device_alarm_msg_1"] = ""

                cache_data["device_status_time_2"] = time_now
                cache_data["device_run_status_2"] = run_status
                cache_data["device_alarm_msg_2"] = alarm_message

                data = {
                    device_id_1: {
                        "DeviceID": device_id_1,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": "停止",  # 运行状态
                            "IsBoardRequest": "N",  # 要板信号
                            "IsBoardEject": "N",  # 出板信号
                            "TrackHasBoard": "N",  # 当前是否有板
                            "AlarmMessage": "",  # 告警信息
                        }
                    },
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": run_status,  # 运行状态
                            "IsBoardRequest": is_board_in,  # 要板信号
                            "IsBoardEject": is_board_out,  # 出板信号
                            "TrackHasBoard": track_has_board,  # 当前是否有板
                            "AlarmMessage": alarm_message,  # 告警信息
                        }
                    },
                }

        else:
            # 停机、开始、安全门、急停 ...推送设备状态给websockets客户端时，两个轨道都要推送
            cache_data["device_status_time_1"] = time_now
            cache_data["device_run_status_1"] = run_status
            cache_data["device_alarm_msg_1"] = alarm_message

            cache_data["device_status_time_2"] = time_now
            cache_data["device_run_status_2"] = run_status
            cache_data["device_alarm_msg_2"] = alarm_message

            data = {
                device_id_1: {
                    "DeviceID": device_id_1,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "IsBoardRequest": is_board_in,  # 要板信号
                        "IsBoardEject": is_board_out,  # 出板信号
                        "TrackHasBoard": track_has_board,  # 当前是否有板
                        "AlarmMessage": alarm_message,  # 告警信息
                    }
                },
            }

            if jyt_module.track_is_exist(2):
                data[device_id_2] = {
                    "DeviceID": device_id_2,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "IsBoardRequest": is_board_in,  # 要板信号
                        "IsBoardEject": is_board_out,  # 出板信号
                        "TrackHasBoard": track_has_board,  # 当前是否有板
                        "AlarmMessage": alarm_message,  # 告警信息
                    }
                }

        xutil.CacheUtil.append_or_update_new_data(cache_data)

        sk_param = {
            "type": "partialStatusUpdate",
            "data": xutil.OtherUtil.obj_to_json(data)
        }

        server_status = xglobal.global_data.get("jrt_server_is_start")
        self.log.info(f"client number: {len(jrt_clients)}")
        self.log.info(f"server status: {server_status}")

        if server_status and len(jrt_clients) > 0:
            jyt_data_list.append(sk_param)

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        device_type = other_vo.get_origin_param("deviceType")

        device_id_1 = other_vo.get_value_by_cons_key("device_id_1")
        device_id_2 = other_vo.get_value_by_cons_key("device_id_2")
        status_code_v3 = other_vo.get_status_code_v3()

        time_now = int(time.time() * 1000)

        cache_data = xutil.CacheUtil.get_cache_data()

        cache_data["device_id_1"] = device_id_1
        cache_data["device_id_2"] = device_id_2

        track_index = other_vo.get_track_index()

        self.log.info(f"device track index: {track_index}")

        # 过滤重复的设备状态
        last_status_code = xglobal.global_data.get("last_status_code_v3", "")
        last_track_index = xglobal.global_data.get("last_track_index", "")
        if last_status_code == status_code_v3 and status_code_v3 not in ["1005", "2043", "2044"] and \
                last_track_index == track_index:
            # 1005/2034 换线不用过滤
            self.log.warning(f"过滤重复的设备状态！")
            return self.x_response()
        else:
            xglobal.global_data["last_status_code_v3"] = status_code_v3
            xglobal.global_data["last_track_index"] = track_index

        if track_index == 1:
            device_id = device_id_1
        else:
            device_id = device_id_2

        if (status_code_v3 not in error_code_map and status_code_v3 not in error_code_map_431) or status_code_v3 in [
            "4003", "1021", "3004",  # 40x
            "2030", "2032", "2000", "2008", "2009"  # 431
        ]:
            self.log.warning(f"该设备状态暂不处理！{status_code_v3}")
            return self.x_response()

        if device_type == "AIS431":
            # 兼容AIS431机型
            return self.send_device_status_to_mes_by_ais431(
                device_id,
                time_now,
                status_code_v3,
                track_index,
                cache_data,
                device_id_1,
                device_id_2
            )

        run_status = error_code_map.get(status_code_v3, "unknown")

        is_board_in = "N"
        is_board_out = "N"
        track_has_board = "N"

        if status_code_v3 in [
            "2002", "3003", "3004", "3005", "3006", "3007", "4001", "4002", "5001", "5002"
        ]:
            alarm_message = run_status
            run_status = "故障"
        else:
            alarm_message = ""

        if status_code_v3 in ["1001"]:
            is_board_in = "Y"

        if status_code_v3 in ["1002", "3002"]:
            is_board_out = "Y"

        if status_code_v3 in ["1001", "1004", "1011", "1012", "1013", "3002", "4010"]:
            track_has_board = "Y"

        if status_code_v3 in [
            "1001", "1004", "1011", "1012", "1013", "1014", "1005"
                                                            "3001", "3002", "3010"
        ]:
            # 根据轨道区分的设备状态
            # 需要单独记录的设备状态
            cache_data[f"device_status_time_{track_index}"] = time_now
            cache_data[f"device_run_status_{track_index}"] = run_status
            cache_data[f"device_alarm_msg_{track_index}"] = alarm_message

            data = {
                device_id: {
                    "DeviceID": device_id,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "IsBoardRequest": is_board_in,  # 要板信号
                        "IsBoardEject": is_board_out,  # 出板信号
                        "TrackHasBoard": track_has_board,  # 当前是否有板
                        "AlarmMessage": alarm_message,  # 告警信息
                    }
                }
            }

        elif status_code_v3 in [
            "4002", "4010"
        ]:
            """
            mark点错误停机时，对应轨道推送mark停机，另外轨道推送停止状态   
            扫不到条码的停机报警，对应轨道推送扫不到条码的停机报警，另外轨道推送停止状态
            """
            if track_index == 1:
                cache_data["device_status_time_1"] = time_now
                cache_data["device_run_status_1"] = run_status
                cache_data["device_alarm_msg_1"] = alarm_message

                cache_data["device_status_time_2"] = time_now
                cache_data["device_run_status_2"] = "停止"
                cache_data["device_alarm_msg_2"] = ""
                data = {
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": run_status,  # 运行状态
                            "IsBoardRequest": is_board_in,  # 要板信号
                            "IsBoardEject": is_board_out,  # 出板信号
                            "TrackHasBoard": track_has_board,  # 当前是否有板
                            "AlarmMessage": alarm_message,  # 告警信息
                        }
                    },
                }

                if jyt_module.track_is_exist(2):
                    data[device_id_2] = {
                        "DeviceID": device_id_2,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": "停止",  # 运行状态
                            "IsBoardRequest": "N",  # 要板信号
                            "IsBoardEject": "N",  # 出板信号
                            "TrackHasBoard": "N",  # 当前是否有板
                            "AlarmMessage": "",  # 告警信息
                        }
                    }
            else:
                # 轨道2触发时   轨道1肯定是存在的，所以不用再判断一下
                cache_data["device_status_time_1"] = time_now
                cache_data["device_run_status_1"] = "停止"
                cache_data["device_alarm_msg_1"] = ""

                cache_data["device_status_time_2"] = time_now
                cache_data["device_run_status_2"] = run_status
                cache_data["device_alarm_msg_2"] = alarm_message

                data = {
                    device_id_1: {
                        "DeviceID": device_id_1,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": "停止",  # 运行状态
                            "IsBoardRequest": "N",  # 要板信号
                            "IsBoardEject": "N",  # 出板信号
                            "TrackHasBoard": "N",  # 当前是否有板
                            "AlarmMessage": "",  # 告警信息
                        }
                    },
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "OperationStatus": run_status,  # 运行状态
                            "IsBoardRequest": is_board_in,  # 要板信号
                            "IsBoardEject": is_board_out,  # 出板信号
                            "TrackHasBoard": track_has_board,  # 当前是否有板
                            "AlarmMessage": alarm_message,  # 告警信息
                        }
                    },
                }

        else:
            # 停机、开始、安全门、急停 ...推送设备状态给websockets客户端时，两个轨道都要推送
            cache_data["device_status_time_1"] = time_now
            cache_data["device_run_status_1"] = run_status
            cache_data["device_alarm_msg_1"] = alarm_message

            cache_data["device_status_time_2"] = time_now
            cache_data["device_run_status_2"] = run_status
            cache_data["device_alarm_msg_2"] = alarm_message

            data = {
                device_id_1: {
                    "DeviceID": device_id_1,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "IsBoardRequest": is_board_in,  # 要板信号
                        "IsBoardEject": is_board_out,  # 出板信号
                        "TrackHasBoard": track_has_board,  # 当前是否有板
                        "AlarmMessage": alarm_message,  # 告警信息
                    }
                },
            }

            if jyt_module.track_is_exist(2):
                data[device_id_2] = {
                    "DeviceID": device_id_2,
                    "LastStatusChangeTimestamp": time_now,
                    "TextRegions": {
                        "OperationStatus": run_status,  # 运行状态
                        "IsBoardRequest": is_board_in,  # 要板信号
                        "IsBoardEject": is_board_out,  # 出板信号
                        "TrackHasBoard": track_has_board,  # 当前是否有板
                        "AlarmMessage": alarm_message,  # 告警信息
                    }
                }

        xutil.CacheUtil.append_or_update_new_data(cache_data)

        sk_param = {
            "type": "partialStatusUpdate",
            "data": xutil.OtherUtil.obj_to_json(data)
        }

        server_status = xglobal.global_data.get("jrt_server_is_start")
        self.log.info(f"client number: {len(jrt_clients)}")
        self.log.info(f"server status: {server_status}")

        if server_status and len(jrt_clients) > 0:
            jyt_data_list.append(sk_param)

        return self.x_response()

    def send_btn_click_push_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        btn_click_type = other_vo.get_btn_click_type()
        track_index = other_vo.get_track_index()

        if track_index == 1:
            device_id = other_vo.get_value_by_cons_key("device_id_1")
        else:
            device_id = other_vo.get_value_by_cons_key("device_id_2")

        time_now = int(time.time() * 1000)

        if btn_click_type == "ClearPanelData":
            # 统计数据清零操作推送
            upload_param = {
                "type": "partialStatusUpdate",
                "data": xutil.OtherUtil.obj_to_json({
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "CurrentProductionQuantity": "0",
                            "DirectPassCount": "0",
                            "NGCount": "0",
                            "FalsePositiveCount": "0",
                        }
                    }
                })
            }

            server_status = xglobal.global_data.get("jrt_server_is_start")
            self.log.info(f"client number: {len(jrt_clients)}")
            self.log.info(f"server status: {server_status}")

            if server_status and len(jrt_clients) > 0:
                jyt_data_list.append(upload_param)

        elif btn_click_type == "TrackWidthSet":
            # 轨道设置宽度推送
            upload_param = {
                "type": "partialStatusUpdate",
                "data": xutil.OtherUtil.obj_to_json({
                    device_id: {
                        "DeviceID": device_id,
                        "LastStatusChangeTimestamp": time_now,
                        "TextRegions": {
                            "RailWidthMm": str(other_vo.json_data.get("width", "0.0")),
                        }
                    }
                })
            }

            server_status = xglobal.global_data.get("jrt_server_is_start")
            self.log.info(f"client number: {len(jrt_clients)}")
            self.log.info(f"server status: {server_status}")

            if server_status and len(jrt_clients) > 0:
                jyt_data_list.append(upload_param)

        return self.x_response()
