# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : maoshuodianyuan.py
# Time       ：2024/9/18 下午2:43
# Author     ：sch
# version    ：python 3.8
# Description：茂硕电源 https://jira.cvte.com/browse/ATAOI_2019-33164
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

comp_ng_template = """
        <TEST>
            <Seq.No>{ix}</Seq.No>
            <TestTime>{test_time}</TestTime>
            <WorkStation>{work_station}</WorkStation>
            <NGLocation>{ng_location}</NGLocation>
            <NGType>{ng_type}</NGType>
            <NGImageCode>{ng_image_code}</NGImageCode>
        </TEST>"""

ext_template = """<Data>
    <MD>
        <SN>{barcode}</SN>
        <Result>{result}</Result>
        <Detected>{comp_total_cnt}</Detected>  
        <DirectAccess>{comp_pass_cnt}</DirectAccess>  
        <PoorQuality>{comp_ng_cnt}</PoorQuality>  
        <Misjudgment>{comp_misjudge_cnt}</Misjudgment> 
        <DPMOPoorQuality>{dpmo_ng}</DPMOPoorQuality>  
        <DPMOMisjudgment >{dpmo_misjudge}</DPMOMisjudgment>{comp_data_str}
    </MD>
</Data>"""


def parse_res(ret_text):
    """
    解析响应参数
    """
    root = xutil.XmlUtil.get_xml_root_by_str(ret_text)
    return_data = xutil.XmlUtil.get_xml_root_by_str(root.text)
    status = return_data.find("RtnString").text
    msg = return_data.find("ErrMsg").text
    return status, msg


class Engine(ErrorMapEngine):
    version = {
        "title": "maoshuodianyuan release v1.0.0.7",
        "device": "AIS40X",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-18 17:28  条码校验，上传数据
date: 2024-09-24 10:22  修改上传参数
date: 2024-09-27 12:27  修改上传参数: ExtParameter
date: 2024-09-29 16:52  NGImageCode传空
date: 2024-10-10 10:18  pass时，ExtParameter传 <Data></Data>
date: 2025-06-23 17:40  ExtParameter中<MD>增加一些参数以及增加PASS拼板数据；增加是否上传NG图片、上传数据范围、是否上传PASS拼板数据开关
date: 2025-07-17 16:37  ATAOI_2019-33164:增加一个上传图片范围选择选项：初判、复判、初判+复判
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/WebService/BasalWebService.asmx/WS_BcValid"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/WebService/BasalWebService.asmx/WS_UpdUnitRecord"
        },
    }

    other_combo = {
        "upload_ng_pic": {
            "ui_name": "是否上传NG图片",
            "item": ["否", "是"],
            "value": "否"
        },
        "upload_data_range": {
            "ui_name": "上传数据范围",
            "item": ["复判", "初判+复判", "初判"],
            "value": "复判"
        },
        "upload_image_range": {
            "ui_name": "上传图片范围",
            "item": ["复判", "初判+复判", "初判"],
            "value": "复判"
        },
        "upload_pass_board": {
            "ui_name": "是否上传PASS拼板数据",
            "item": ["是", "否"],
            "value": "是"
        },
    }

    form = {
        "station_name": {
            "ui_name": "工位号",
            "value": ""
        },
        "rsc_name": {
            "ui_name": "资源名称",
            "value": ""
        },
        "username": {
            "ui_name": "用户账户",
            "value": ""
        },
        "work_station": {
            "ui_name": "工序",
            "value": ""
        },
    }

    combo = {
        "is_multi_plate": {
            "ui_name": "是否多联板",
            "item": ["No", "Yes"],
            "value": "No"
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station_name = other_vo.get_value_by_cons_key("station_name")
        rsc_name = other_vo.get_value_by_cons_key("rsc_name")
        is_multi_plate = other_vo.get_value_by_cons_key("is_multi_plate")
        username = other_vo.get_value_by_cons_key("username")

        sn_list = other_vo.list_sn()

        err_msg_list = []
        for ix, sn in enumerate(sn_list):
            param = {
                "Barcode": sn,
                "UserName": username,
                "StationName": station_name,
                "RscName": rsc_name,
                "IsMultiPlate": is_multi_plate == "Yes"
            }

            ret_text = xrequest.RequestUtil.post_form(api_url_check, param, to_json=False)
            status, msg = parse_res(ret_text)
            if status != "1":
                err_msg_list.append(f"No:{ix} SN:{sn} Error:{msg}")

        if err_msg_list:
            err_str = f"\n".join(err_msg_list)
            return self.x_response("false", f"Mes接口响应异常，{err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        station_name = data_vo.get_value_by_cons_key("station_name")
        rsc_name = data_vo.get_value_by_cons_key("rsc_name")
        is_multi_plate = data_vo.get_value_by_cons_key("is_multi_plate")
        username = data_vo.get_value_by_cons_key("username")
        work_station = data_vo.get_value_by_cons_key("work_station")
        upload_ng_pic = data_vo.get_value_by_cons_key("upload_ng_pic")
        upload_data_range = data_vo.get_value_by_cons_key("upload_data_range")
        upload_image_range = data_vo.get_value_by_cons_key("upload_image_range")
        upload_pass_board = data_vo.get_value_by_cons_key("upload_pass_board")

        inspect_type = data_vo.get_inspect_type()

        if upload_data_range == '复判' and inspect_type == xcons.INSPECTOR:
            self.log.info('设置为只上传复判后数据，此次发送为初判数据，直接返回不上传')
            return self.x_response()
        elif upload_data_range == '初判' and inspect_type == xcons.REPAIR:
            self.log.info('设置为只上传初判数据，此次发送为复判数据，直接返回不上传')
            return self.x_response()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        defect_code_list = []
        err_msg_list = []
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no
            barcode = board_entity.barcode
            defect_code = ""
            if inspect_type == xcons.INSPECTOR:
                board_result_bool = board_entity.robot_result
                board_result_str = board_entity.get_robot_result("PASS", "NG")
                ng_cnt = board_entity.comp_robot_ng_number
                misjudge_cnt = 0
            else:
                board_result_bool = board_entity.repair_result
                board_result_str = board_entity.get_repair_result("PASS", "NG")
                ng_cnt = board_entity.comp_repair_ng_number
                misjudge_cnt = board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number

            comp_ng_data = ""
            ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                comp_base64_data = ""
                comp_src_img = comp_entity.image_path
                if inspect_type == xcons.INSPECTOR:
                    is_comp_ng = comp_entity.is_robot_ng()
                    comp_ng_code = comp_entity.robot_ng_code
                    comp_ng_str = comp_entity.robot_ng_str
                    if upload_ng_pic == '是' and upload_image_range == '初判' and comp_src_img and is_comp_ng:
                        comp_base64_data = xutil.OtherUtil.file_to_base64_content(comp_src_img)
                else:
                    is_comp_ng = comp_entity.is_repair_ng()
                    comp_ng_code = comp_entity.repair_ng_code
                    comp_ng_str = comp_entity.repair_ng_str
                    if upload_ng_pic == '是' and upload_image_range == '复判' and comp_src_img and is_comp_ng:
                        comp_base64_data = xutil.OtherUtil.file_to_base64_content(comp_src_img)

                if upload_ng_pic == '是' and upload_image_range == '初判+复判' and comp_src_img and is_comp_ng:
                    comp_base64_data = xutil.OtherUtil.file_to_base64_content(comp_src_img)

                if is_comp_ng:
                    comp_tag = comp_entity.designator
                    if not defect_code:
                        defect_code = comp_ng_code
                        defect_code_list.append(f"{board_no}:{comp_tag}:{comp_ng_code}")

                    ix += 1
                    comp_ng_data += comp_ng_template.format(**{
                        "ix": ix,
                        "test_time": test_time,
                        "work_station": work_station,
                        "ng_location": comp_tag,
                        "ng_type": comp_ng_str,
                        "ng_image_code": comp_base64_data,
                    })

            if board_result_bool and upload_pass_board == '否':
                ext_param = "<Data></Data>"
            else:
                total_cnt = board_entity.comp_total_number
                pass_cnt = total_cnt - ng_cnt
                dpmo_ng = round(ng_cnt / total_cnt * 1000000, 2)
                dpmo_misjudge = round(misjudge_cnt / total_cnt * 1000000, 2)
                ext_param = ext_template.format(**{
                    "barcode": barcode,
                    "result": board_result_str,
                    "comp_total_cnt": total_cnt,
                    "comp_pass_cnt": pass_cnt,
                    "comp_ng_cnt": ng_cnt,
                    "comp_misjudge_cnt": misjudge_cnt,
                    "dpmo_ng": dpmo_ng,
                    "dpmo_misjudge": dpmo_misjudge,
                    "comp_data_str": comp_ng_data
                })

            if upload_ng_pic == '否':
                # 上传图片数据量比较大，这里就不打印了
                self.log.info(f'发送的ExtParameter数据：{ext_param}')

            param = {
                "Barcode": barcode,
                "StationName": station_name,
                "RscName": rsc_name,
                "IsMultiPlate": is_multi_plate == "Yes",
                "JudgeRsl": board_entity.repair_result,
                "DefectCode": defect_code,
                "ExtParameter": ext_param,
                "UserName": username,
            }
            try:
                ret_text = xrequest.RequestUtil.post_form(api_url_data, param, to_json=False)
                status, msg = parse_res(ret_text)
                if status != "1":
                    err_msg_list.append(f"No:{board_no} SN:{barcode} Error:{msg}")
            except Exception as e:
                err_msg_list.append(f"本地网络错误，拼板【No:{board_no} SN:{barcode}】发送失败， Error:{e}")

        if err_msg_list:
            err_str = f"\n".join(err_msg_list)
            return self.x_response("false", f"Mes接口响应异常，{err_str}")

        return self.x_response()
