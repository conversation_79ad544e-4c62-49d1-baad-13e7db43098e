# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/27 下午14:11
# Author     ：chencb
# version    ：python 3.8
# Description：长沙兆兴博拓 https://jira.cvte.com/browse/ATAOI_2019-37855
"""
from typing import Any
from common import xrequest, xutil, xcons
from common.xutil import log, x_response
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "zhaoxingbotuo release v1.0.0.1",
        "device": "AIS203",
        "feature": ["发送数据", "条码校验", "拷贝图片"],
        "author": "chenchongbing",
        "release": """
date: 2025-03-28 10:30  jira:37855 发送数据、条码校验、拷贝图片
""",
    }

    path = {
        "save_path": {
            "ui_name": "整板图保存路径",
            "value": ""
        },
    }

    form = {
        "work_station": {
            "ui_name": "工站",
            "value": "",
        },
        "line": {
            "ui_name": "线体",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "soft_version": {
            "ui_name": "软件版本",
            "value": "",
        },
        "ip_addr": {
            "ui_name": "IP地址",
            "value": "",
        },
        "user_id": {
            "ui_name": "用户ID",
            "value": "",
        },
        "sn_url": {
            "ui_name": "条码检查地址",
            "value": "http://**************:9528/api/EdcExt/InStationCheckStatus",
        },
        "mes_url": {
            "ui_name": "上传结果地址",
            "value": "http://**************:9528/api/EdcExt/OutStationUploadResult2",
        },
    }

    def __init__(self):
        self.fixture_id = ''

    def _copy_pcb_image(self, save_path, pcb_entity):
        if not save_path:
            self.log.info(f"整板图保存路径未设置，保存失败")
            return

        project = pcb_entity.project_name
        pcb_sn = pcb_entity.pcb_barcode
        inspect_date = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        inspect_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        full_path = f"{save_path}/{project}/{inspect_date}"

        xutil.FileUtil.ensure_dir_exist(full_path)

        pcb_image_list = pcb_entity.pcb_image
        count = len(pcb_image_list)
        for index, pcb_image in enumerate(pcb_image_list):
            if count > 1:
                pcb_filename = f"{pcb_sn}_{inspect_time}_{index}.jpg"
            else:
                pcb_filename = f"{pcb_sn}_{inspect_time}.jpg"
            dst_pcb_image = f"{full_path}/{pcb_filename}"
            xutil.FileUtil.copy_file(pcb_image, dst_pcb_image)
            self.log.info(f"整板图已保存到：{dst_pcb_image}")

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        work_station = other_vo.get_value_by_cons_key("work_station")
        line = other_vo.get_value_by_cons_key("line")
        device_name = other_vo.get_value_by_cons_key("device_name")
        soft_version = other_vo.get_value_by_cons_key("soft_version")
        ip_addr = other_vo.get_value_by_cons_key("ip_addr")
        user_id = other_vo.get_value_by_cons_key("user_id")
        sn_url = other_vo.get_value_by_cons_key("sn_url", not_null=True)

        # 治具条码
        barcode_map = other_vo.get_barcode_map()
        fixture_id = barcode_map.get('-2', '')
        # 拼板标识 Y 或 N
        sn_list = other_vo.list_sn()
        mp_flag = 'Y' if len(sn_list) > 1 else 'N'
        for sn in sn_list:
            check_param = {
                "SerialNumber": sn,
                "StationName": work_station,
                "ProductionLine": line,
                "MachineName": device_name,
                "FixtureId": fixture_id,
                "AppVersion": soft_version,
                "IpAddress": ip_addr,
                "UserId": user_id,
                "MpFlag": mp_flag,
                "Language": "CN"
            }

            '''返回结果格式：{
                "Status": 2, # 状态 1-Success,2-Error,3-Repair,4-NotFound
                "IsPassStation": false,
                "Message": "条码:TEST24370004 没有绑定记录,请使用单板模式测试!",
                "Data": null
            }
            '''
            ret = xrequest.RequestUtil.post_json(sn_url, check_param)
            if ret.get('Status') != 1:
                return self.x_response("false", f"条码：{sn}，错误信息：{ret.get('Message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        work_station = data_vo.get_value_by_cons_key("work_station")
        line = data_vo.get_value_by_cons_key("line")
        device_name = data_vo.get_value_by_cons_key("device_name")
        soft_version = data_vo.get_value_by_cons_key("soft_version")
        ip_addr = data_vo.get_value_by_cons_key("ip_addr")
        user_id = data_vo.get_value_by_cons_key("user_id")
        mes_url = data_vo.get_value_by_cons_key("mes_url", not_null=True)
        save_path = data_vo.get_value_by_cons_key("save_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        self._copy_pcb_image(save_path, pcb_entity)

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)

            defect_code = []
            defect_location = []
            for comp_entity in board_entity.yield_comp_entity():
                if not comp_entity.is_repair_ng():
                    continue

                defect_code.append(comp_entity.repair_ng_code)
                defect_location.append(comp_entity.designator)

            board_data = {
                "SerialNumber": board_entity.barcode,
                "Result": board_entity.get_final_result('PASS', 'PASS', 'FAIL', 'FAIL'),
                "DefectCode": ','.join(defect_code),
                "DefectLocation": ','.join(defect_location),
                "ParameterList": "",
                "Remark": ""
            }
            board_data_list.append(board_data)

        mp_flag = 'Y' if len(pcb_entity.board_data_json) > 1 else 'N'
        upload_data = {
            "StationName": work_station,
            "ProductionLine": line,
            "MachineName": device_name,
            "FixtureId": self.fixture_id,
            "AppVersion": soft_version,
            "IpAddress": ip_addr,
            "UserId": user_id,
            "MpFlag": mp_flag,
            "Language": "CN",
            "SnResults": board_data_list
        }

        ret = xrequest.RequestUtil.post_json(mes_url, upload_data)
        if ret.get('Status') != 1:
            return self.x_response("false", f"结果上传失败，错误信息：{ret.get('Message')}")

        return self.x_response()
