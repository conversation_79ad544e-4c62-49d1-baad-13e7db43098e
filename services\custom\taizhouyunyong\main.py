# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/7 下午2:15
# Author     ：sch
# version    ：python 3.8
# Description：泰州云涌
"""
import json
from typing import Any

import requests

from common import xcons, xrequest, xutil
from common.xutil import log
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine, ErrorMapEngine


def post_json(
        url,
        body_data: dict,
        headers: dict = None,
        params: dict = None,
        to_json=True,
        log_number: int = 300000,
        timeout=5,
        auth=None,
        files: dict = None,
) -> Any:
    """
    post 接口，并默认返回json数据
    请求头：content-type: application/json
    :param url: 请求API
    :param headers: 请求头
    :param params: 请求参数
    :param body_data: 请求体参数
    :param to_json: 是否需要将返回参数转成 `python dict` 类型
    :param log_number: 打印多少参数
    :param auth: auth
    :param timeout: timeout
    :param files
    :return:
    """
    # cls.log_common_param(url, headers, params, body_data, log_number)

    # body_data_str = ""
    # if type(body_data) is dict:
    #     body_data_str = json.dumps(body_data, ensure_ascii=False)
    #
    # log.info(f"-->请求URL：{url}  【Body Json】参数：\n{body_data_str[:log_number]}")
    if headers:
        log.info(f"-->请求头：\n{headers}")

    if params:
        log.info(f"-->【Param】参数：\n{params}")

    if auth:
        res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, auth=auth,
                            files=files)
    else:
        res = requests.post(url, json=body_data, headers=headers, params=params, timeout=timeout, files=files)

    log.info(f"post_json 响应参数 code: {res} msg: {res.text}")

    if not res:
        # log.warning(f"post_json code: {res}  error: {res.text}")
        raise Exception("接口调用出错2003，请检查接口服务是否正常！")

    if to_json:
        return res.json()
    else:
        return res.text


class Engine(ErrorMapEngine):
    version = {
        "title": "taizhouyunyong release v1.0.0.2",
        "device": "203,501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-07 14:16  上传数据到mes
date: 2023-12-12 17:00  Result, 复判NG时，多个不良用英文分号分隔
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://*************:9013/api/Equipment/LCAOITransfer",
        },
        "logon_id": {
            "ui_name": "操作员ID",
            "value": "",
        },
        "operator": {
            "ui_name": "操作员名称",
            "value": "",
        },
        "station": {
            "ui_name": "站点",
            "value": "",
        },
        "device_ip": {
            "ui_name": "设备IP地址",
            "value": "",
        },
        "api_username": {
            "ui_name": "接口认证用户",
            "value": "OPC",
        },
        "api_password": {
            "ui_name": "接口认证密码",
            "value": "MESOPC",
        },
    }

    combo = {
        "board_side": {
            "ui_name": "AB面",
            "item": ["A", "B"],
            "value": "A",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        logon_id = data_vo.get_value_by_cons_key("logon_id")
        operator = data_vo.get_value_by_cons_key("operator")
        station = data_vo.get_value_by_cons_key("station")
        device_ip = data_vo.get_value_by_cons_key("device_ip")
        board_side = data_vo.get_value_by_cons_key("board_side")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        t_src_image = pcb_entity.get_pcb_t_image()

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            result = "PASS"

            error_result_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    error_result_list.append(comp_entity.repair_ng_str)

            if error_result_list:
                result = ";".join(error_result_list)

            board_param = {
                "BoardName": pcb_entity.project_name,
                "Barcode": barcode,
                "Result": result,
                "StencilHeight": "",
                "BoardWidth": "",
                "BoardHeight": "",
                "InspectionDateTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                # "BoardImage": "0x0221836712983712412843",
                "LogonID": logon_id,
                "Operator": operator,
                "Shift": "",
                "Line": "",
                "ProductLot": "",
                "Station": station,
                "Status": "",
                "RailID": "",
                "Side": board_side,
                "CycleTime": str(pcb_entity.get_cycle_time()),
                "IP": device_ip
            }
            log.info(f"-->请求URL：{api_url}  【Body Json】参数：\n{json.dumps(board_param, ensure_ascii=False)}")
            log.info(f"图片已转成base64编码附加到参数里，因参数过大，并没有打印！图片地址：{t_src_image}")
            board_param["BoardImage"] = xutil.ImageUtil.file_to_base64_content(t_src_image)

            ret = post_json(api_url, board_param)
            if not ret.get("success"):
                error_msg = ret.get("msg")

        if error_msg:
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{error_msg}")

        return self.x_response()
