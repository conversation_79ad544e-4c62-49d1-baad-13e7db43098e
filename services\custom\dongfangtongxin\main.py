# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/29 上午9:30
# Author     ：sch
# version    ：python 3.8
# Description：东方通信
"""

from typing import Any

from common import xcons, xutil, xenum
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "dongfangtongxin release v1.0.0.1",
        "device": "303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-29 10:45  init
""", }

    form = {
        "line": {
            "ui_name": "线体",
            "value": "",
        },
    }
    combo = {
        "board_side": {
            "ui_name": "面别",
            "item": ["T", "B", "T+B"],
            "value": "",
        },
    }
    path = {
        "save_path": {
            "ui_name": "txt文档保存路径",
            "value": "",
        }
    }

    def __init__(self):
        self.common_config['sendmes_setting3'] = xenum.SendMesSetting3.Send
        self.common_config['sendmes_setting2'] = xenum.SendMesSetting2.SaveInspectNG

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        board_side = data_vo.get_value_by_cons_key("board_side")
        save_path = data_vo.get_value_by_cons_key("save_path")
        line = data_vo.get_value_by_cons_key("line")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        project_name = pcb_entity.project_name
        repair_user = pcb_entity.repair_user

        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_FILE)
        cycle_time = pcb_entity.get_cycle_time()

        pcb_sn = pcb_entity.pcb_barcode

        all_data_list = [
            "cModel,BoardSN,TopBtm,CompSN,fdate,idStation,CompStatus,CompModifierStatus,TotalPad,Modifier,ModifierDate,CycleTime"]
        comp_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            test_result = board_entity.get_final_result("P", "P", "F", "F")
            repair_result = board_entity.get_final_result("P", "P", "F", "F")
            t_number = board_entity.comp_total_number

            all_data_list.append(
                f"{project_name},{barcode},{board_side},{board_no},{time_file},{line},{test_result},{repair_result},{t_number},{repair_user},{review_time},{cycle_time}")

            report_xml = pcb_entity.get_pcb_t_report_xml()

            x_y_data = xutil.XmlUtil.dict_x_y_pos(report_xml)

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                comp_part = comp_entity.part

                xy_info = x_y_data.get(comp_entity.comp_id, {})
                angle = xy_info.get('angle', '')

                robot_result = comp_entity.get_final_result("P", "F", "F")
                repair_result = comp_entity.get_final_result("P", "P", "F")

                robot_ng_code = comp_entity.robot_ng_code
                repair_ng_code = comp_entity.repair_ng_code

                comp_data_list.append(
                    f"{project_name},{barcode},{board_side},{board_no},{comp_tag},{line},{angle},{comp_part},{robot_result},{repair_result},{robot_ng_code},{repair_ng_code},{repair_user},{review_time},{time_file}")

        all_data_list.append(
            '-------------------------------------------------------------------------------------------')
        all_data_list.append(
            'cModel,BoardSN,TopBtm,imulti,CompName,idStation,iComAngle,szComType,CompStatus,CompModifierStatus,errtype,CompErrtype,Modifier,CompModifierDate,fdate')
        all_data_list.extend(comp_data_list)

        txt_content = '\n'.join(all_data_list)
        xutil.FileUtil.write_content_to_file(f"{save_path}/{pcb_sn}_{time_file}.txt", txt_content)

        return self.x_response()
