<dsWIPTD>
    <WIPTDHeader>
        <IntSerial>{barcode}</IntSerial>
        <Process>{process}</Process>
        <OperatorName>{operator}</OperatorName>
        <Tester>{device_name}</Tester>
        <ProgramName>{program_name}</ProgramName>
        <ProgramVersion>v1.0.0.1</ProgramVersion>
        <IPSNo>{ips_no}</IPSNo>
        <IPSVersion>{ips_version}</IPSVersion>
        <Remark>{remark}</Remark>
    </WIPTDHeader>
    <WIPTDItem>
        <Item>{ix}</Item>
        <TestStep>{test_step}</TestStep>
        <TestName>{test_name}</TestName>
        <OutputName />
        <InputCondition />
        <OutputLoading />
        <LowerLimit>{lower_limit}</LowerLimit>
        <Result>{result}</Result>
        <UpperLimit>{upper_limit}</UpperLimit>
        <Unit />
        <Status>{status}</Status>
        <IPSReference />
        <TestID>{ix}</TestID>
    </WIPTDItem>
</dsWIPTD>
