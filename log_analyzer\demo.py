#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志分析工具演示脚本
"""

import os
import tempfile
from datetime import datetime

def create_demo_log():
    """创建演示日志文件"""
    demo_logs = [
        "2024-01-01 10:00:00 - [line:1] - INFO: 系统启动成功",
        "2024-01-01 10:01:00 - [line:2] - INFO: 连接数据库成功", 
        "2024-01-01 10:02:00 - [line:3] - WARNING: 内存使用率较高: 85%",
        "2024-01-01 10:03:00 - [line:4] - ERROR: 连接MES服务器失败, error255",
        "2024-01-01 10:04:00 - [line:5] - INFO: 重试连接MES服务器",
        "2024-01-01 10:05:00 - [line:6] - ERROR: 工单不存在, error18",
        "2024-01-01 10:06:00 - [line:7] - CRITICAL: 数据库连接丢失",
        "2024-01-01 10:07:00 - [line:8] - INFO: 数据库重连成功",
        "2024-01-01 10:08:00 - [line:9] - ERROR: AOI检测发现漏件, error1",
        "2024-01-01 10:09:00 - [line:10] - WARNING: 设备温度过高: 75°C",
        "<span style='color:red'>2024-01-01 10:10:00 网络连接超时</span>",
        "<span style='color:green'>2024-01-01 10:11:00 网络连接恢复</span>",
        "2024-01-01 10:12:00 ---Error--- 文件读取失败: permission denied",
        "2024-01-01 10:13:00 ---Info--- 文件读取重试成功",
        "2024-01-01 10:14:00 - [line:15] - ERROR: 产品已下线, error244",
    ]
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False, encoding='utf-8')
    for log_line in demo_logs:
        temp_file.write(log_line + '\n')
    temp_file.close()
    
    return temp_file.name

def demo_basic_parsing():
    """演示基本解析功能"""
    print("=== 日志解析演示 ===")
    
    # 创建演示日志
    log_file = create_demo_log()
    print(f"创建演示日志文件: {log_file}")
    
    try:
        # 简单的日志解析演示
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"日志文件包含 {len(lines)} 行")
        
        # 统计不同级别的日志
        levels = {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0}
        error_codes = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 简单的级别识别
            for level in levels:
                if level in line:
                    levels[level] += 1
                    break
            
            # 简单的错误码识别
            if 'error' in line.lower():
                import re
                match = re.search(r'error(\d+)', line, re.IGNORECASE)
                if match:
                    code = match.group(1)
                    error_codes[code] = error_codes.get(code, 0) + 1
        
        print("\n日志级别统计:")
        for level, count in levels.items():
            if count > 0:
                print(f"  {level}: {count}")
        
        print("\n错误码统计:")
        for code, count in error_codes.items():
            print(f"  错误码{code}: {count}次")
        
        # 显示一些日志内容
        print("\n日志内容示例:")
        for i, line in enumerate(lines[:5]):
            print(f"  {i+1}: {line.strip()}")
        
        return log_file
        
    except Exception as e:
        print(f"解析过程中出现错误: {e}")
        return None

def demo_search_functionality(log_file):
    """演示搜索功能"""
    print("\n=== 搜索功能演示 ===")
    
    if not log_file or not os.path.exists(log_file):
        print("日志文件不存在，跳过搜索演示")
        return
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 搜索包含"error"的行
        print("搜索包含'error'的日志:")
        error_lines = []
        for i, line in enumerate(lines):
            if 'error' in line.lower():
                error_lines.append((i+1, line.strip()))
        
        for line_num, content in error_lines:
            print(f"  行{line_num}: {content}")
        
        # 搜索特定错误码
        print("\n搜索错误码255:")
        for i, line in enumerate(lines):
            if 'error255' in line.lower():
                print(f"  行{i+1}: {line.strip()}")
        
        # 搜索WARNING级别
        print("\n搜索WARNING级别日志:")
        for i, line in enumerate(lines):
            if 'WARNING' in line:
                print(f"  行{i+1}: {line.strip()}")
                
    except Exception as e:
        print(f"搜索过程中出现错误: {e}")

def demo_error_analysis(log_file):
    """演示错误分析功能"""
    print("\n=== 错误分析演示 ===")
    
    if not log_file or not os.path.exists(log_file):
        print("日志文件不存在，跳过错误分析演示")
        return
    
    try:
        # 内置的错误码映射
        error_map = {
            "1": {"description": "漏件", "category": "检测缺陷", "severity": "ERROR"},
            "18": {"description": "工单不存在", "category": "数据错误", "severity": "ERROR"},
            "244": {"description": "产品已下线", "category": "状态错误", "severity": "WARNING"},
            "255": {"description": "服务端未定义异常", "category": "系统错误", "severity": "CRITICAL"},
        }
        
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("错误分析结果:")
        
        # 分析错误码
        import re
        found_errors = {}
        
        for line in lines:
            match = re.search(r'error(\d+)', line, re.IGNORECASE)
            if match:
                code = match.group(1)
                if code in error_map:
                    error_info = error_map[code]
                    if code not in found_errors:
                        found_errors[code] = {
                            'count': 0,
                            'description': error_info['description'],
                            'category': error_info['category'],
                            'severity': error_info['severity']
                        }
                    found_errors[code]['count'] += 1
        
        if found_errors:
            print("\n发现的错误:")
            for code, info in found_errors.items():
                print(f"  错误码{code}: {info['description']}")
                print(f"    分类: {info['category']}")
                print(f"    严重程度: {info['severity']}")
                print(f"    出现次数: {info['count']}")
                print()
        else:
            print("  未发现已知错误码")
        
        # 分析错误模式
        print("错误模式分析:")
        error_patterns = [
            ("连接.*失败", "连接错误"),
            ("文件.*失败", "文件操作错误"),
            ("数据库.*", "数据库相关"),
            ("温度.*高", "温度异常"),
            ("内存.*", "内存相关"),
        ]
        
        for pattern, description in error_patterns:
            count = 0
            for line in lines:
                if re.search(pattern, line, re.IGNORECASE):
                    count += 1
            if count > 0:
                print(f"  {description}: {count}次")
                
    except Exception as e:
        print(f"错误分析过程中出现错误: {e}")

def demo_export_functionality(log_file):
    """演示导出功能"""
    print("\n=== 导出功能演示 ===")
    
    if not log_file or not os.path.exists(log_file):
        print("日志文件不存在，跳过导出演示")
        return
    
    try:
        # 演示CSV导出
        csv_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
        csv_file.write("时间,级别,消息\n")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        import re
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 简单解析时间和级别
            time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            level_match = re.search(r'(INFO|WARNING|ERROR|CRITICAL)', line)
            
            time_str = time_match.group(1) if time_match else "未知时间"
            level_str = level_match.group(1) if level_match else "未知级别"
            
            # 清理消息内容
            message = re.sub(r'<[^>]+>', '', line)  # 移除HTML标签
            message = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.*?-', '', message)  # 移除时间戳部分
            message = message.strip()
            
            csv_file.write(f'"{time_str}","{level_str}","{message}"\n')
        
        csv_file.close()
        
        print(f"CSV导出完成: {csv_file.name}")
        
        # 显示导出文件的前几行
        with open(csv_file.name, 'r', encoding='utf-8-sig') as f:
            csv_lines = f.readlines()
        
        print("导出文件内容预览:")
        for i, line in enumerate(csv_lines[:5]):
            print(f"  {line.strip()}")
        
        # 清理临时文件
        os.unlink(csv_file.name)
        
    except Exception as e:
        print(f"导出过程中出现错误: {e}")

def main():
    """主演示函数"""
    print("日志快速定位分析工具演示")
    print("=" * 40)
    print()
    
    try:
        # 基本解析演示
        log_file = demo_basic_parsing()
        
        if log_file:
            # 搜索功能演示
            demo_search_functionality(log_file)
            
            # 错误分析演示
            demo_error_analysis(log_file)
            
            # 导出功能演示
            demo_export_functionality(log_file)
            
            # 清理临时文件
            os.unlink(log_file)
        
        print("\n" + "=" * 40)
        print("演示完成！")
        print()
        print("工具特性总结:")
        print("✓ 支持多种日志格式解析")
        print("✓ 提供强大的搜索功能")
        print("✓ 智能错误码识别和分析")
        print("✓ 支持多种格式导出")
        print("✓ 提供直观的GUI界面")
        print()
        print("使用方法:")
        print("  GUI模式: python main.py")
        print("  命令行模式: python main.py --cli")
        print("  查看帮助: python main.py --help")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
