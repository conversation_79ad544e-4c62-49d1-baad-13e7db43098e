# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/06/29 晚上21:00
# Author     ：chencb
# version    ：python 3.8
# Description：武汉小米 https://jira.cvte.com/browse/ATAOI_2019-39971
"""
import base64
import hashlib
import json
import os
import threading
from datetime import datetime
from pathlib import Path
from typing import Any, Union
import requests
from common import xutil, xcons
from common.xconfig import home_dir
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo
from opcua import Client, ua
from opcua.common import ua_utils
from opcua.ua import status_codes

# 上传失败文件信息记录
FAIL_CACHE_DIR = f"{home_dir}/.aoi/fail_cache"
FAIL_FILE_RECORD = f'{FAIL_CACHE_DIR}/fail_files.json'


class Engine(ErrorMapEngine):
    version = {
        "customer": ["武汉小米", "wuhanxiaomi"],
        "version": "release v1.0.0.1",
        "device": "AIS203",
        "feature": ["OPCUA数据上传", "小米X5协议", "获取条码", "条码校验", "发送数据", '上传图片和板式zip文件'],
        "author": "chenchongbing",
        "release": """
date: 2025-07-04 18:00  jira:39971 OPCUA数据上传;小米X5协议；获取条码；条码校验；发送数据；上传图片和板式zip文件
""", }

    form = {
        "api_url_opcua": {
            "ui_name": "OPCUA服务地址",
            "value": "",
        },
        "api_url_mes": {
            "ui_name": "MES接口地址",
            "value": "",
        },
        "app_id_mes": {
            "ui_name": "AppID(MES)",
            "value": "",
        },
        "app_key_mes": {
            "ui_name": "AppKey(MES)",
            "value": "",
        },
        "api_url_upload": {
            "ui_name": "文件上传接口地址",
            "value": "",
        },
        "app_id_upload": {
            "ui_name": "AppID(文件上传)",
            "value": "",
        },
        "app_key_upload": {
            "ui_name": "AppKey(文件上传)",
            "value": "",
        },

        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "work_unit_code": {
            "ui_name": "工位ID",
            "value": "",
        },
        "factory": {
            "ui_name": "工厂编码",
            "value": "",
        },
        "product_mode": {
            "ui_name": "生产阶段",
            "value": "",
        },
        "project_name": {
            "ui_name": "项目号",
            "value": "",
        },
        "line_no": {
            "ui_name": "线体",
            "value": "",
        },
        "work_station_no": {
            "ui_name": "工站",
            "value": "",
        },
    }

    def __init__(self):
        self.vehicle_sn = ''
        self.main_window = None

    def call_custom_function(self, func_name: str, other_vo: OtherVo):
        if func_name == 'OPCUANodesValueReadWrite':
            api_url_opcua = other_vo.get_value_by_cons_key("api_url_opcua", not_null=True)
            func_args = other_vo.get_func_args()
            if func_args:
                """参数格式说明：
                "funcArgs": {
                    "transferType": "write",
                    "data": [ 
                    {
                    "nodeId": "ns=2;s=CustomData.SensorData.Stop", 
                    "attributeId": 13,
                    "uaType": 1, 
                    "value": true
                    },]
                """
                transfer_type = func_args.get('transferType')
                data_list = func_args.get('data')
                ret = self.opcua_nodes_value_read_write(api_url_opcua, transfer_type, data_list)
                return ret
            else:
                return self.x_response('false', '未获取到funcArgs值，请检查接口数据传递是否正确！！')
        elif func_name == 'X5UploadFiles':
            func_args = other_vo.get_func_args()
            if func_args:
                """参数格式说明：
                "funcArgs": [ 
                {
                "filePath": "/home/<USER>/Downloads/111.zip", 
                "md5Hash": "4E3A9D8B7F6C5E2A1B3C4D5E6F7A8B9C", 
                "removeAfterSuccess": true 
                },]
                """
                file_list = []
                for file in func_args:
                    file_path = file.get('filePath')
                    file_name = os.path.basename(file_path)
                    file_uuid = xutil.OtherUtil.get_origin_uuid4_str()
                    file_list.append({
                        "file_path": file_path,
                        "dst_file_name": file_name,
                        "uuid": file_uuid,
                        "md5_hash": '',  # 暂时不用，怕服务器校验后上传失败
                        "tag": "",
                        "remove_after_success": file.get('removeAfterSuccess')
                    })

                upload_files = {
                    "file_type": "ZIP",
                    "sn": "",
                    "file_list": file_list
                }

                # 上传文件需异步上传
                timer = threading.Timer(0.2, self._x5_upload_files, args=[other_vo, upload_files])
                timer.start()
                return self.x_response()
            else:
                return self.x_response('false', '未获取到funcArgs值，请检查接口数据传递是否正确！！')

    def opcua_nodes_value_read_write(self, server_url, transfer_type: str, data_list: list):
        client = Client(server_url)
        nodes_write_err_msg = []
        nodes_read_fail = False
        nodes_read_result = []
        try:
            client.connect()
            self.log.info(f"成功连接到OPCUA服务器：{server_url}")
            nodes = []
            if transfer_type == 'write':
                values_to_write = []
                for data in data_list:
                    node_id = data.get('nodeId')
                    node = client.get_node(node_id)
                    nodes.append(node)
                    value = data.get('value')
                    values_to_write.append(value)

                # 批量写入数据（重写client.set_values，主要是为了获取原始的results）
                node_ids = [node.nodeid for node in nodes]
                dvs = [ua_utils.value_to_datavalue(val) for val in values_to_write]
                results = client.uaclient.set_attributes(node_ids, dvs, ua.AttributeIds.Value)
                self.log.info(f"OPCUA数据写入完成，进行结果解析")
                for i, result in enumerate(results):
                    if result.is_good():
                        self.log.info(f"节点 {node_ids[i]} 写入成功")
                    else:
                        status_code = result.value
                        err_desc = status_codes.get_name_and_doc(status_code)
                        err_msg = f'节点 {node_ids[i]} 写入失败：{err_desc}'
                        nodes_write_err_msg.append(err_msg)
                        self.log.info(err_msg)

            elif transfer_type == 'read':
                # 获取节点对象列表
                for data in data_list:
                    node_id = data.get('nodeId')
                    node = client.get_node(node_id)
                    nodes.append(node)

                # 批量读取节点值
                results = client.uaclient.get_attributes(nodes, ua.AttributeIds.Value)
                for i, result in enumerate(results):
                    node_id = nodes[i].nodeid.to_string()
                    status_code = result.StatusCode
                    value = None
                    err_desc = ''
                    if status_code.is_good():
                        success = True
                        if result.Value is not None and result.Value.Value is not None:
                            value = result.Value.Value
                            data_type = result.Value.VariantType
                            self.log.info(f"节点 {node_id} 读取成功， 值: {value}，数据类型: {data_type}")
                        else:
                            self.log.info(f"节点 {node_id} 读取成功，但值为空")
                    else:
                        nodes_read_fail = True
                        success = False
                        status_code_value = status_code.value
                        err_desc = status_codes.get_name_and_doc(status_code_value)
                        err_msg = f'节点 {node_id} 读取失败：{err_desc}'
                        self.log.info(err_msg)

                    nodes_read_result.append({
                        'nodeId': node_id,
                        'value': value,
                        'success': success,
                        'errorStr': err_desc,
                    })

        except Exception as e:
            if transfer_type == 'write':
                nodes_write_err_msg.append(f"opcua节点写入失败，error: {e}")
            else:
                nodes_read_fail = True
                for data in data_list:
                    nodes_read_result.append({
                        'nodeId': data.get('nodeId'),
                        'value': None,
                        'success': False,
                        'errorStr': f'本地网络异常:{e}',
                    })
        finally:
            # 如果连接失败，这里执行disconnect会报错，所以需要捕获下
            try:
                client.disconnect()
            except:
                pass
            self.log.info("opcua客户端已断开连接")

        if transfer_type == 'read':
            nodes_read_result_str = json.dumps(nodes_read_result, ensure_ascii=False, indent=4)
            status = 'false' if nodes_read_fail else 'true'
            return self.x_response(status, nodes_read_result_str)
        else:
            if nodes_write_err_msg:
                return self.x_response('false', ';'.join(nodes_write_err_msg))
            else:
                return self.x_response()

    def _get_mac_address(self):
        mac_addr = xutil.OtherUtil.get_mac_address()
        if mac_addr:
            # 转为客户要求的格式：04-D4-C4-4B-A7-C7
            return mac_addr.replace(':', '-').upper()

    def _get_file_create_time(self, file_path):
        formatted_time = ''
        try:
            # 获取文件创建时间（ctime）
            create_time = os.path.getctime(file_path)
            # 将时间戳转换为 datetime 对象
            dt_object = datetime.fromtimestamp(create_time)
            # 格式化为 "YYYY-MM-DD HH:MM:SS"
            formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
        except FileNotFoundError:
            self.log.info(f"获取文件创建时间错误：文件 {file_path} 不存在")
        except Exception as e:
            self.log.info(f"获取文件创建时间错误：{e}")
        finally:
            return formatted_time

    @xutil.time_cost
    def _send_x5_request(self, api_url, app_id, app_key, body: dict, upload_file=None):
        # 生成紧凑型json，减少数据量
        body_str = json.dumps(body, separators=(',', ':'), ensure_ascii=False)
        sign_str = f"{app_id}{body_str}{app_key}"
        sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

        if upload_file:
            # 使用上传文件定义的格式
            request_data = {
                "header":
                    {
                        "appid": app_id,
                        "method": "UploadMqtt",
                        "sign": sign,
                        "url": api_url
                    },
                "body": body_str
            }
        else:
            request_data = {
                "header": {
                    "appid": app_id,
                    "sign": sign
                },
                "body": body_str
            }

        # 将数据转换为字符串并进行 Base64 安全编码，生成紧凑型json，减少数据量
        data_str = json.dumps(request_data, separators=(',', ':'), ensure_ascii=False)
        # 进行 URL 安全的 Base64 编码（自动替换 +/ 为 -_，并去掉 = 填充）
        data_base64 = base64.urlsafe_b64encode(data_str.encode('utf-8')).decode('utf-8')
        # 请求参数:data={加密后的数据}
        form_data = {
            "data": data_base64,
        }

        if upload_file:
            file = open(upload_file, 'rb')
            files = {"file": file}
            self.log.info(f"---上传文件：{upload_file}")
        else:
            files = None

        self.log.info(f"---请求URL：{api_url}，请求body:\n{body}")
        # 使用data=参数后，底层网络库自动会对form_data的value进行url编码，并Content-Type:application/x-www-form-urlencoded
        response = requests.post(api_url, files=files, data=form_data, timeout=6)
        return response

    def _parse_response(self, resp):
        """
        解析三种可能的返回场景，发送成功时返回(True，返回的json)，发送失败时返回（False,错误信息）
        """
        try:
            resp_json = resp.json()
            self.log.info(f"---接口返回信息：\n{resp_json}")

            # 文档里定义了两种场景的异常场景，为了减少问题都做兼容吧
            ret_code = resp_json.get('code')
            # 外层直接读到code，则为异常场景
            if ret_code:
                return False, f"服务器报错：code={ret_code}, err_msg={resp_json.get('msg')}"
            else:
                header = resp_json.get('header', {})
                if header.get('code') != 200:
                    return False, f"服务器报错:{header.get('desc')}"
                else:
                    return True, resp_json
        except Exception as e:
            self.log.info(f"---接口返回信息：\n{resp.text}")
            return False, f"服务器报错：{resp.text}"

    def _append_fail_files(self, fail_files: dict):
        self.log.info(f'缓存上传失败文件信息到本地：{fail_files}')

        xutil.FileUtil.ensure_dir_exist(FAIL_CACHE_DIR)
        fail_file_list = []
        if os.path.exists(FAIL_FILE_RECORD):
            # 读取原有fail_files，并把新的数据追加到原有的记录里重新保存
            fail_file_list = xutil.FileUtil.load_json_file(FAIL_FILE_RECORD)
        fail_file_list.append(fail_files)
        xutil.FileUtil.dump_json_to_file(FAIL_FILE_RECORD, fail_file_list)

    def _upload_fail_files(self, data_vo: DataVo):
        # 没有失败数据
        if not os.path.exists(FAIL_FILE_RECORD):
            return

        self.log.info('服务恢复正常，上传失败的文件重新进行上传！')
        fail_file_list = xutil.FileUtil.load_json_file2(FAIL_FILE_RECORD)
        # 数据加载出来后，直接删除文件，如果重传失败会重新保存
        xutil.FileUtil.remove_file(FAIL_FILE_RECORD)
        for fail_file in fail_file_list:
            self._x5_upload_files(data_vo, fail_file)

    def _x5_upload_files(self, data_vo: Union[OtherVo, DataVo], upload_files: dict):
        """upload_files格式如下：
            {
            "file_type": "IMAGE" # IMAGE、TEXT、ZIP
            "sn": "整板条码" # 如果是图片需要带上整板条码
            "file_list":[{
            "file_path": "/home/<USER>/Downloads/111.zip", # 文件绝对路径
            "dst_file_name":"xx_xx_xx.jpg" # 上传到文件服务器的目标文件名
            "uuid": "xxxx-xxx-x-xxx" # 上传文件时需要用到的uuid
            "md5_hash": "4E3A9D8B7F6C5E2A1B3C4D5E6F7A8B9C", # zip时从接口里获取，图片时暂时没生成为空
            "tag": "" #如果是图片，整板图(whole)，器件图（part），非图片填写空
            "remove_after_success": true # 上传成功后,是否需要删除对应文件，默认都为false
            }
        """
        api_url_upload = data_vo.get_value_by_cons_key("api_url_upload", not_null=True)
        app_id = data_vo.get_value_by_cons_key("app_id_upload", not_null=True)
        app_key = data_vo.get_value_by_cons_key("app_key_upload", not_null=True)
        factory = data_vo.get_value_by_cons_key("factory")
        device_code = data_vo.get_value_by_cons_key("device_code")
        project_name = data_vo.get_value_by_cons_key("project_name")
        product_mode = data_vo.get_value_by_cons_key("product_mode")
        line_no = data_vo.get_value_by_cons_key("line_no")
        work_station_no = data_vo.get_value_by_cons_key("work_station_no")

        upload_url = f'{api_url_upload}/x5/file/upload/mqtt'
        file_type = upload_files.get('file_type', '')
        pcb_sn = upload_files.get('sn', '')
        file_list = upload_files.get('file_list', [])

        err_msg_list = []
        is_upload_fail_files = False
        try:
            for file in file_list:
                file_path = file.get('file_path', '')
                # 如果文件不存在则忽略
                if not os.path.exists(file_path):
                    continue

                file_name = file.get('dst_file_name', '')
                file_md5 = file.get('md5_hash', '')
                file_tag = file.get('tag', '')
                file_uuid = file.get('uuid', '')
                remove_after_success = file.get('remove_after_success', False)
                file_create_time = self._get_file_create_time(file_path)

                pass_station_id = file_uuid  # TODO: 确认这个值填写
                reference_info = json.dumps([{"pass_station_id": pass_station_id}], separators=(',', ':'),
                                            ensure_ascii=False)
                mqtt_payload = {
                    "factory": factory,
                    "project_name": project_name,
                    "product_mode": product_mode,
                    "tag": file_tag,
                    "line_no": line_no,
                    "work_station_no": work_station_no,
                    "equipment_no": device_code,
                    "file_id": file_uuid,
                    "sn": pcb_sn,
                    "file_name": file_name,
                    "opt_time": file_create_time,
                    "file_type": file_type,
                    "pass_station_id": pass_station_id,
                    "reference_info": reference_info
                }
                mqtt_payload_str = json.dumps(mqtt_payload, separators=(',', ':'), ensure_ascii=False)
                body = {
                    "bucket": f"{factory}/{device_code}/{file_type}",  # todo:代确认需要拼接:工厂编码/装备编码/文件类型
                    "informMqtt": True,
                    "md5": file_md5,
                    "mqttPayload": mqtt_payload_str,
                    "name": file_name,
                    "uploadCloud": True,
                    "uuid": file_uuid
                }

                resp = self._send_x5_request(upload_url, app_id, app_key, body, file_path)
                success, ret = self._parse_response(resp)
                if success:
                    if remove_after_success:
                        os.remove(file_path)
                else:
                    # 服务返回上传失败一般是参数填写不正确，如果缓存信息下次上传可能还是会出错，所以就不缓存这个失败的场景
                    err_msg_list.append(ret)
                # 网络恢复，若有失败缓存数据可以进行下重传
                is_upload_fail_files = True
        except Exception as e:
            # 只缓存本地网络异常时场景
            self._append_fail_files(upload_files)
            err_msg_list.append(f'本地网络异常，上传文件失败，error:{e}')

        if is_upload_fail_files:
            # 网络恢复正常，若有失败信息则重新进行下异步上传
            timer = threading.Timer(0.5, self._upload_fail_files, args=[data_vo])
            timer.start()

        if err_msg_list:
            self.log.info('\n'.join(err_msg_list))
            # 界面输出只显示一个就可以了，避免如果都是同一错误时，信息过多
            err_msg = f"文件上传失败，{err_msg_list[0]}"
            self.main_window.log_info(err_msg, False, pop_prompt=False)
            return self.x_response('false', '\n'.join(err_msg_list))
        else:
            return self.x_response()

    def _generate_upload_image_files(self, data_vo: DataVo, comp_pic_list: list):
        """
        comp_pic_list里的数据项为：
        {
            "designator": comp_entity.designator,
            "image_path": comp_entity.image_path
        }
        return:(fileData,upload_files)
        fileData: mes上传数据时需要组装的fileData字段
        upload_files：需要后续进行上传的图片列表
        """
        project_name = data_vo.get_value_by_cons_key("project_name")
        work_station = data_vo.get_value_by_cons_key("work_station_no")
        factory = data_vo.get_value_by_cons_key("factory")
        device_code = data_vo.get_value_by_cons_key("device_code")
        pcb_entity = data_vo.pcb_entity

        # 把不一样的信息汇总到这里，后续统一生成上传上传文件列表信息
        image_list = []
        # 添加整板图
        pcb_image_list = pcb_entity.pcb_image
        for pcb_image in pcb_image_list:
            file_suffix = Path(pcb_image).suffix  # 结果：'.jpg'（带点号）
            image_list.append({
                "file_path": pcb_image,
                "designator": "-",
                "raw_pic": "A",  # A:原图,B:结果图
                "tag": "whole",  # whole整板图，part:器件图
                "suffix": file_suffix,
            })

        for comp_image in comp_pic_list:
            image_path = comp_image.get('image_path')
            file_suffix = Path(image_path).suffix
            designator = comp_image.get('designator')
            image_list.append({
                "file_path": image_path,
                "designator": designator if designator else '-',
                "raw_pic": "B",  # A:原图,B:结果图
                "tag": "part",  # whole整板图，part:器件图
                "suffix": file_suffix,
            })

        upload_pic_list = []
        # 给mes数据上传fileData字段使用
        file_data = []
        file_name_template = """{project_name}_{work_station}_{designator}_{test_time}_{result}_{raw_pic}-{index}-{total}{suffix}"""
        # 20230605-195310110,精确到毫秒
        test_time = pcb_entity.get_start_time().strftime("%Y%m%d-%H%M%S%f")[:-3]
        bucket = f"{factory}/{device_code}/IMAGE"
        image_cnt = len(image_list)
        index = 0
        for image in image_list:
            index += 1
            file_info = {
                "project_name": project_name if project_name else '-',
                "work_station": work_station if work_station else '-',
                "designator": image.get('designator'),
                "test_time": test_time if test_time else '-',
                "result": pcb_entity.get_robot_result('Pass', 'Fail'),
                "raw_pic": image.get('raw_pic'),
                "index": index,
                "total": image_cnt,
                "suffix": image.get('suffix'),
            }
            file_name = file_name_template.format(**file_info)
            file_uuid = xutil.OtherUtil.get_origin_uuid4_str()
            upload_pic_list.append({
                "file_path": image.get('file_path'),
                "dst_file_name": file_name,
                "uuid": file_uuid,
                "md5_hash": '',
                "tag": image.get('tag'),
                "remove_after_success": False
            })

            file_data.append({
                "fileName": file_name,
                "fileId": file_uuid,
                "bucket": bucket,
            })

        # 给最终文件上传使用的
        upload_files = {
            "file_type": "IMAGE",
            "sn": pcb_entity.pcb_barcode,
            "file_list": upload_pic_list
        }

        return file_data, upload_files

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        app_id_mes = other_vo.get_value_by_cons_key("app_id_mes", not_null=True)
        app_key_mes = other_vo.get_value_by_cons_key("app_key_mes", not_null=True)
        device_code = other_vo.get_value_by_cons_key("device_code")
        work_unit_code = other_vo.get_value_by_cons_key("work_unit_code")
        api_url_mes = other_vo.get_value_by_cons_key("api_url_mes", not_null=True)
        self.vehicle_sn = other_vo.get_barcode_map().get('-2', '')

        pcb_sn = other_vo.get_pcb_sn()
        if not pcb_sn:
            return self.x_response('false', '请求条码为空，请检查！')

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT2)
        mac = self._get_mac_address()
        param = {
            "deviceCode": device_code,
            "workUnitCode": work_unit_code,
            "clientMac": mac,
            "clientTime": time_now,
            "unitSn": pcb_sn,
        }

        try:
            get_sn_url = f"{api_url_mes}/ac-mid-mes/smt/x5/LaserGetSnData"
            resp = self._send_x5_request(get_sn_url, app_id_mes, app_key_mes, param)
            success, ret = self._parse_response(resp)
            if success:
                unit_sn_list = ret.get('body', {}).get('unitSnList', [])
                # 按no字段从小到大排序（转为整数比较）
                sorted_list = sorted(unit_sn_list, key=lambda x: int(x["no"]))
                # 提取sn字段到新列表
                sn_list = [item["sn"] for item in sorted_list]
                return self.x_response('true', ','.join(sn_list))
            else:
                return self.x_response('false', ret)
        except Exception as e:
            return self.x_response('false', f'本地网络异常，error:{e}')

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        app_id_mes = other_vo.get_value_by_cons_key("app_id_mes", not_null=True)
        app_key_mes = other_vo.get_value_by_cons_key("app_key_mes", not_null=True)
        device_code = other_vo.get_value_by_cons_key("device_code")
        work_unit_code = other_vo.get_value_by_cons_key("work_unit_code")
        api_url_mes = other_vo.get_value_by_cons_key("api_url_mes", not_null=True)
        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT2)
        track = str(other_vo.get_track_index())
        mac = self._get_mac_address()

        try:
            for sn in other_vo.list_sn():
                param = {
                    "workUnitCode": work_unit_code,
                    "clientMac": mac,
                    "deviceCode": device_code,
                    "clientTime": time_now,
                    "sn": sn,
                    "track": track,
                    "processDataList": [],  # 传空
                }
                in_url = f"{api_url_mes}/ac-mid-mes/smt/x5/in"
                resp = self._send_x5_request(in_url, app_id_mes, app_key_mes, param)
                success, ret = self._parse_response(resp)
                if not success:
                    return self.x_response('false', ret)
        except Exception as e:
            return self.x_response('false', f'本地网络异常，error:{e}')

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        app_id_mes = data_vo.get_value_by_cons_key("app_id_mes", not_null=True)
        app_key_mes = data_vo.get_value_by_cons_key("app_key_mes", not_null=True)
        device_code = data_vo.get_value_by_cons_key("device_code")
        work_unit_code = data_vo.get_value_by_cons_key("work_unit_code")
        api_url_mes = data_vo.get_value_by_cons_key("api_url_mes", not_null=True)
        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT2)

        inspect_type = data_vo.get_inspect_type()
        if inspect_type != xcons.INSPECTOR:
            err_msg = '当前定义只发送机器检测后数据，人工复判数据暂不发送，若要发送需提需求重新实现！！'
            return self.x_response('false', err_msg)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        cycle_time = str(pcb_entity.get_cycle_time())

        inspection_item_data = []
        inspection_point_data = []
        ng_comp_pic_list = []
        for board_entity in pcb_entity.yield_board_entity():
            comp_data_list = []
            ng_comp_index = 0
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_result:
                    continue

                ng_comp_pic_list.append({
                    "designator": comp_entity.designator,
                    "image_path": comp_entity.image_path
                })

                for alg_entity in comp_entity.yield_alg_entity():
                    if alg_entity.result == comp_entity.robot_ng_code:
                        comp_data_list.append({
                            "testItem": alg_entity.test_name,
                            "functionName": alg_entity.test_name,
                            "errorCode": alg_entity.result,
                            "hiLimit": alg_entity.max_threshold,
                            "lowLimit": alg_entity.min_threshold,
                            "status": comp_entity.get_final_result("PASS", "FAIL", "FAIL"),
                            "testTtime": cycle_time,
                            "value": alg_entity.test_val,
                            "description": ""
                        })

                ng_comp_index += 1
                inspection_point_data.append({
                    "no": str(ng_comp_index),
                    "result": comp_entity.get_final_result("PASS", "FAIL", "FAIL"),
                    "defectCode": comp_entity.robot_ng_code,
                    "defectDesc": comp_entity.robot_ng_str,
                    "location": comp_entity.designator,
                    "panelNo": board_entity.board_no
                })

            inspection_item_data.append({
                "childsn": board_entity.barcode,
                "childUnitState": board_entity.get_robot_result('PASS', 'FAIL'),
                "dataItem": comp_data_list
            })

        vehicle_datas = []
        if self.vehicle_sn:
            vehicle_datas.append({
                "vehicleSn": self.vehicle_sn,
                "vehicleType": "",
                "slot": ""
            })

        # 只在初判阶段上传图片数据即可，复判时就不用再上传数据了
        file_data = []
        if inspect_type == xcons.INSPECTOR:
            file_data, upload_image_files = self._generate_upload_image_files(data_vo, ng_comp_pic_list)
            # 异步上传文件
            timer = threading.Timer(1, self._x5_upload_files, args=[data_vo, upload_image_files])
            timer.start()

        pcb_data = {
            "deviceCode": device_code,
            "workUnitCode": work_unit_code,
            "clientMac": self._get_mac_address(),
            "clientTime": time_now,
            "sn": pcb_entity.pcb_barcode,
            "state": pcb_entity.get_robot_result('PASS', 'FAIL'),
            "track": str(pcb_entity.track_index),
            "unitData": {
                "inspectionItemData": inspection_item_data,
                "inspectionPointData": inspection_point_data,
                "part": [],
                "processData": [],
                "keyParamData": [],
                "fileData": file_data,
                "consumableData": [],
                "vehicleDatas": vehicle_datas,
            }
        }

        try:
            out_url = f"{api_url_mes}/ac-mid-mes/smt/x5/out"
            resp = self._send_x5_request(out_url, app_id_mes, app_key_mes, pcb_data)
            success, ret = self._parse_response(resp)
            x_resp = self.x_response() if success else self.x_response('false', ret)
            return x_resp
        except Exception as e:
            return self.x_response('false', f'本地网络异常，error:{e}')
