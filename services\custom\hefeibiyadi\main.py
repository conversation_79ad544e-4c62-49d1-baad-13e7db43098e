# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/2/18 上午9:55
# Author     ：sch
# version    ：python 3.8
# Description：合肥比亚迪    https://jira.cvte.com/browse/ATAOI_2019-37205    (ps: 和松下万宝类似)
"""
import os
import shutil
from typing import Any

from PIL import Image

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import LimitedDict
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

carry_type_map = {
    "无载体": 0,
    "载体与产品绑定": 1,
    "通过载体码返回产品标识": 2,
    "载体与产品解绑": 3,
    "返回当前工单的产品型号": 4,
    "载盘作业批入站": 5,
}

cmd_code_map = {
    "产品标识": "1",
    "载体条码": "2",
    "作业批次(载体条码)": "3",
}

error_map = {
    "1": "可生产",
    "16": "生产线中设备不存在",
    "17": "工单对应工艺下无该设备",
    "18": "工单不存在",
    "19": "不符合时间锁",
    "20": "不符合物料锁",
    "21": "产品不存在",
    "22": "非混产模式",
    "240": "无型号",
    "241": "不符合工序锁",
    "242": "未处于返修模式下出现不合格",
    "243": "互锁表达式异常",
    "244": "产品已下线",
    "245": "产品已报废",
    "246": "无生产计划",
    "247": "条码校验失败",
    "248": "物料缺料",
    "249": "烧录次数达到 3 次",
    "255": "服务端未定义异常而不能生产",
}

error_map2 = {
    "1": "成功",
    "2": "未知异常",
    "3": "产品标识为空",
    "4": "产品信息不完整",
    "5": "该工序无质检单模板",
    "6": "生产线标识错误",
    "7": "工艺路线中找不到该设备",
    "77": "物料不齐套",
    "255": "服务端未定义异常而不能生产",
}

error_map3 = {
    "1": "成功",
    "17": "失败",
    "3": "产品标识不能为空",
    "4": "找不到产品信息",
    "5": "工艺路线中设备不存在",
    "6": "该工序无质检单模板",
    "7": "载盘条码不能为空",
    "8": "找不到载盘信息或作业批",
    "9": "作业批无成功的入站记录",
    "10": "作业批不允许重复出站",
    "11": "不符合作业批出站质量锁",
    "255": "服务端未定义异常而不能生产"
}

status_code_map = {
    "2001": 0,  # 急停
    "2002": 1,  # 安全门报警
    "3003": 2,  # 条码校验过站失败
    "3004": 3,  # 上传检测信息失败
    "3005": 4,  # 磁盘已满
    "3006": 5,  # 掉板
    "3007": 6,  # EAP告警
    "4001": 7,  # 直通率告警
    "4002": 8,  # Mark点错误
    "4003": 9,  # 板卡NG
    "5001": 10,  # 风扇停转
    "5002": 11,  # 相机连接失败
}

limit_data = LimitedDict(500)

cache_path = f"{os.getcwd()}/cache_data"


class Engine(ErrorMapEngine):
    version = {
        "title": "hefeibiyadi release v1.0.0.7",
        "device": "AIS303B-1",
        "feature": ["条码校验", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2025-02-19 09:21  条码校验，上传数据，设备状态
date: 2025-02-28 15:49  修改上传逻辑
date: 2025-02-28 17:31  productId传缓存的productId
date: 2025-03-01 10:48  jira->37205: 需求变更
date: 2025-03-02 18:30  OK与NG都需要上传数据，只有OK的板子才过站
""", }

    other_form = {
        "api_url": {
            "ui_name": "服务器地址",
            "value": "http://127.0.0.1:8081",
        },

        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    combo = {
        "line_code": {
            "ui_name": "产线通讯标识",
            "item": [str(i) for i in range(256)],
            "value": "1"
        },
        "station_code": {
            "ui_name": "工位通讯标识",
            "item": [str(i) for i in range(256)],
            "value": "1"
        },
        "carry_type": {
            "ui_name": "载体方式",
            "item": list(carry_type_map.keys()),
            "value": "无载体"
        },
        "re_online_type": {
            "ui_name": "返修上线方式",
            "item": [str(i) for i in range(256)],
            "value": "1"
        },
        "cmd_code": {
            "ui_name": "出站标识",
            "item": list(cmd_code_map.keys()),
            "value": "1"
        },
        "board_number_ui": {
            "ui_name": "拼板数量",
            "item": [str(i) for i in range(1, 256)],
            "value": "1"
        },
    }

    other_combo = {
        "ftp_upload_img": {
            "ui_name": "是否上传图片到FTP",
            "item": ["Yes", "No"],
            "value": "No",
        }
    }

    form = {
        "user": {
            "ui_name": "员工编号",
            "value": "",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        # 初始化
        self.log.info(f"cache path: {cache_path}")
        xutil.FileUtil.ensure_dir_exist(cache_path)

        file_list = os.listdir(cache_path)
        self.log.info(f"缓存的压缩文件数量：{len(file_list)}")

        for filename in file_list:
            file_path = os.path.join(cache_path, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)  # 如果是文件，则删除它
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # 如果是文件夹，则递归删除整个文件夹

            self.log.info(f"文件：{file_path} 已删除")

        if len(file_list) != 0:
            self.log.info(f"缓存文件已删除完毕！")

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        line_code = other_vo.get_value_by_cons_key("line_code", to_int=True)
        station_code = other_vo.get_value_by_cons_key("station_code", to_int=True)
        # carry_code = other_vo.get_value_by_cons_key("carry_code")
        carry_type = other_vo.get_value_by_cons_key("carry_type")
        re_online_type = other_vo.get_value_by_cons_key("re_online_type", to_int=True)
        board_number_ui = other_vo.get_value_by_cons_key("board_number_ui", to_int=True)

        check_url = f"{api_url}/api/v2/CheckIn"

        sn_list = other_vo.list_sn()

        ret_res = self.x_response()

        only_one_sn = ""

        for sn in sn_list:
            if not only_one_sn and sn:
                only_one_sn = sn

        if "-" in only_one_sn:
            only_one_sn = only_one_sn.split("-")[0]

        self.log.info(f"check_sn: {only_one_sn}")
        self.log.info(f"board number: {board_number_ui}")

        for ix in range(board_number_ui):
            ix += 1

            carrier_code = f"{only_one_sn}-{ix}"
            check_param = {
                "LineIntCode": line_code,
                "StationIntCode": station_code,
                "BarCode": "",
                "CarrierCode": carrier_code,
                "CarrierType": carry_type_map[carry_type],
                "ReOnlineType": re_online_type,
                "ReOnlineInfo": ""
            }
            ret = xrequest.RequestUtil.post_json(check_url, check_param)
            handle_result = str(ret.get("HandleResult"))

            if handle_result != "1":
                err_str = error_map.get(handle_result)
                ret_res = self.x_response("false", f"mes接口异常，No:{ix} SN:{carrier_code}，error：{err_str}")

            product_id = ret.get("ProductId")
            self.log.info(f"mes check sn: {carrier_code} product_id: {product_id}")
            limit_data.add_item(carrier_code, product_id)

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        line_code = data_vo.get_value_by_cons_key("line_code")
        station_code = data_vo.get_value_by_cons_key("station_code")
        cmd_code = data_vo.get_value_by_cons_key("cmd_code")
        user = data_vo.get_value_by_cons_key("user")

        ftp_upload_img = data_vo.get_value_by_cons_key("ftp_upload_img")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        only_one_sn = ""

        for sn in pcb_entity.all_barcode:
            if not only_one_sn and sn:
                only_one_sn = sn

        self.log.info(f"SN: {only_one_sn}")

        cycle_time = int(pcb_entity.get_cycle_time())
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        time_date = time_file[:8]

        if ftp_upload_img == "Yes":
            ftp_client = FTPClient(
                ftp_host, ftp_user, ftp_password, ftp_port
            )

            ftp_client.login()
            ftp_path = f"{ftp_path}/{time_date}"
            ftp_client.cd_or_mkdir(ftp_path)
            board_box_map = pcb_entity.get_board_box_position()
        else:
            ftp_client = None
            board_box_map = {}

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            board_ftp_dst_path = ""

            if ftp_client:
                t_board_box = board_box_map.get(board_no)

                if t_board_box:
                    self.log.info(f"开始保存拼板[{board_no}]的拼板图片...")
                    pcb_img_list = pcb_entity.list_all_pcb_image_v2()
                    if pcb_img_list:
                        t_src_img = pcb_img_list[0]

                        image = Image.open(t_src_img)
                        cropped_image = image.crop(t_board_box)

                        src_board_img = f"{cache_path}/T_{barcode}_{board_no}_{time_file}.jpg"
                        cropped_image.save(src_board_img)

                        # 上传到ftp
                        board_ftp_dst_path = f"{only_one_sn}_{board_no}_{time_file}.jpg"
                        ftp_client.upload_file(src_board_img, board_ftp_dst_path)

                        os.remove(src_board_img)

                else:
                    self.log.warning(f"找到拼板坐标，无法切割图片！")

            carrier_code = f"{only_one_sn}-{board_no}"
            product_id_from_cache = limit_data.get_value(carrier_code, "")
            self.log.info(f"获取到的 product id: {product_id_from_cache}")

            #if board_entity.is_repair_ng():
            if board_ftp_dst_path:
                full_path = f"{ftp_path}/{board_ftp_dst_path}"
            else:
                full_path = ""

            ptiv_item_list = [
                # 工序是否合格
                {
                    "PTLVFlag": 1,
                    "Result": board_entity.get_repair_result(1, 2),
                    "DataType": "3",
                    "DataLength": "1",
                    "Value": board_entity.get_repair_result("1", "2"),
                },
                # 节拍时间(s)
                {
                    "PTLVFlag": 2,
                    "Result": 1,
                    "DataType": "3",
                    "DataLength": "1",
                    "Value": str(cycle_time),
                },
                # 员工编号
                {
                    "PTLVFlag": 3,
                    "Result": 1,
                    "DataType": "3",
                    "DataLength": "1",
                    "Value": user,
                },
                # ftp文件路径
                {
                    "PTLVFlag": 4,
                    "Result": 1,
                    "DataType": "3",
                    "DataLength": "1",
                    "Value": full_path,
                },
            ]

            start_ix = 4
            for comp_entity in board_entity.yield_comp_entity():
                start_ix += 1

                if comp_entity.is_repair_ng():
                    ptiv_item_list.append({
                        "PTLVFlag": start_ix,
                        "Result": 1,
                        "DataType": "3",
                        "DataLength": "1",
                        "Value": comp_entity.repair_ng_code,
                    })

            ng_data_param = {
                "LineIntCode": line_code,
                "StationIntCode": station_code,
                "DataItem": len(ptiv_item_list),
                "ProductId": product_id_from_cache,
                "CarrierCode": "",
                "Position": 0,
                "PTLVItems": ptiv_item_list,
            }

            # 1. 上传NG数据
            self.log.info(f"正在上传NG数据...")
            ng_data_url = f"{api_url}/api/v2/ParameterUpload"
            ret = xrequest.RequestUtil.post_json(ng_data_url, ng_data_param)
            handle_result = str(ret.get("HandleResult"))

            if handle_result != "1":
                err_str = error_map2.get(handle_result)
                err_msg_list.append(f"mes接口异常，上传数据失败，No:{board_no} SN:{barcode}，error：{err_str}")

            if not board_entity.is_repair_ng():
                # 2. 上传过站数据
                self.log.info(f"正在上传过站数据...")
                check_out_url = f"{api_url}/api/v2/CheckOut"
                ret = xrequest.RequestUtil.post_json(check_out_url, {
                    "LineIntCode": line_code,
                    "StationIntCode": station_code,
                    "CmdCode": cmd_code,
                    "ProductId": product_id_from_cache,
                })

                handle_result = str(ret.get("HandleResult"))
                if handle_result != "1":
                    err_str = error_map3.get(handle_result)
                    err_msg_list.append(f"mes接口异常，上传数据失败，No:{board_no} SN:{barcode}，error：{err_str}")

        if ftp_client:
            ftp_client.close()

        if err_msg_list:
            ret_res = self.x_response("false", "\n".join(err_msg_list)[:500])

        return ret_res

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        line_code = other_vo.get_value_by_cons_key("line_code", to_int=True)
        station_code = other_vo.get_value_by_cons_key("station_code", to_int=True)

        state = 0

        status_code_v3 = other_vo.get_status_code_v3()
        if status_code_v3 in ["1001", "1002", "1004"]:
            state = 1
        elif status_code_v3 in ["3001", "3002"]:
            state = 2
        elif status_code_v3 in ["1003", "2001", "2002", "3003", "5001", "5002"]:
            state = 3
        elif status_code_v3 in ["3004", "3005", "3006", "3007", "4001", "4002", "4003"]:
            state = 4
        else:
            self.log.warning(f"未知设备状态：{status_code_v3}")

        if state:
            self.log.info(f"正在上传设备状态[{state}]....")
            status_url = f"{api_url}/api/v2/DeviceStatus"
            status_param = {
                "LineIntCode": line_code,
                "StationIntCode": station_code,
                "Status": state,
            }

            xrequest.RequestUtil.post_json(status_url, status_param)

        if status_code_v3 in status_code_map:
            # 报警发生
            # 1. 上传报警数据
            last_alarm_id = global_data.get("alarm_id")
            alarm_id = status_code_map.get(status_code_v3, 12)

            if global_data.get("alarm_code", 0) != 1:
                self.log.info(f"正在上传报警状态[{alarm_id}]....")

                alarm_code = 1

                alarm_url = f"{api_url}/api/v2/AlarmReport"
                alarm_param = {
                    "LineIntCode": line_code,
                    "StationIntCode": station_code,
                    "AlarmCode": alarm_code,
                    "AlarmId": alarm_id,
                }

                xrequest.RequestUtil.post_json(alarm_url, alarm_param)
                global_data["alarm_code"] = alarm_code
                global_data["alarm_id"] = alarm_id
            else:
                self.log.warning(f"上一次的报警[{last_alarm_id}]还未处理，本次报警[{alarm_id}]不处理！")

        elif status_code_v3 in [
            "1001", "1002", "1004", "0001"
        ]:
            # 报警解除
            last_alarm_id = global_data.get("alarm_id")
            if global_data.get("alarm_code", 0) == 1:
                self.log.info(f"正在解除报警状态[{last_alarm_id}]....")

                alarm_code = 2

                alarm_url = f"{api_url}/api/v2/AlarmReport"
                alarm_param = {
                    "LineIntCode": line_code,
                    "StationIntCode": station_code,
                    "AlarmCode": alarm_code,
                    "AlarmId": last_alarm_id,
                }

                xrequest.RequestUtil.post_json(alarm_url, alarm_param)
                global_data["alarm_code"] = alarm_code
            else:
                self.log.warning(f"没有报警需要处理！")

        return self.x_response()
