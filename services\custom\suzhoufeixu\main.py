# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/9 上午9:56
# Author     ：sch
# version    ：python 3.8
# Description：苏州飞旭   ATAOI_2019-32203
"""
import os
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "suzhoufeixu release v1.0.0.3",
        "device": "AIS503",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-09 18:00  获取条码，上传数据
date: 2025-02-14 15:05  json数据不需要格式化
date: 2025-03-14 11:51  文件后缀从txt改为json
""", }

    path = {
        "get_sn_path": {
            "ui_name": "获取条码路径",
            "value": "",
        },
        "json_save_path": {
            "ui_name": "文档保存路径",
            "value": "",
        },

    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        get_sn_path = other_vo.get_value_by_cons_key("get_sn_path", not_null=True)

        pcb_sn = other_vo.get_pcb_sn()

        sn_filepath = f"{get_sn_path}/{pcb_sn}.txt"

        if not os.path.exists(sn_filepath):
            return self.x_response("false", f"找不到条码文件：{sn_filepath}")

        sn_content = xutil.FileUtil.read_file(sn_filepath)
        if "\r\n" in sn_content:
            sn_content = sn_content.replace("\r\n", "\n")

        self.log.info(f"sn content: {sn_content}")

        sn_row_list = sn_content.split("\n")

        if len(sn_row_list) <= 0:
            return self.x_response("false", f"未获取到条码，请检查文件内容：{sn_filepath}")

        new_sn_list = [i for i in sn_row_list if i]

        return self.x_response("true", ",".join(new_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key("device_name")
        json_save_path = data_vo.get_value_by_cons_key("json_save_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_list.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data_fmt = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "comp_data": comp_data_list
            }

            if not barcode:
                sn_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
            else:
                sn_file = barcode

            filepath = f"{json_save_path}/{sn_file}.json"
            xutil.FileUtil.dump_json_to_file(filepath, board_data_fmt, fmt=False)

        return self.x_response()
