# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : schedule_service.py
# Time       ：2023/2/2 下午6:07
# Author     ：sch
# version    ：python 3.8
# Description：
"""

from PyQt5.QtCore import QThread
from apscheduler.schedulers.blocking import BlockingScheduler

from common.xutil import log
from services.route import engine
from templates.main_window import Ui_MesMainWindow


class ScheduleThread(QThread):
    def __init__(self, main_window: Ui_MesMainWindow):
        super(ScheduleThread, self).__init__()
        self.main_window = main_window

    def run(self) -> None:
        """
        定时任务
        :return:
        """
        app_setting = self.main_window.config_data["app_setting"]  # noqa
        interval_cron = app_setting.get('custom_interval_cron')
        interval_cron2 = app_setting.get('custom_interval_cron2')
        interval_cron3 = app_setting.get('custom_interval_cron3')

        if app_setting.get("is_cron_clear") or interval_cron or interval_cron2 or interval_cron3:
            scheduler = BlockingScheduler(timezone="Asia/Shanghai")

            if app_setting.get("is_cron_clear"):
                if app_setting.get("fixed_time_clear"):
                    # 固定时间清除
                    if app_setting.get("fixed_status_1"):
                        fixed_time1 = app_setting.get("fixed_time_1").split(":")
                        hour1 = fixed_time1[0]
                        minute1 = fixed_time1[1]
                        scheduler.add_job(self.main_window.reset_data_on_click, "cron", hour=hour1, minute=minute1)  # noqa
                        log.info(f"固定任务添加成功：固定时间[{hour1}:{minute1}]清零一次")
                    if app_setting.get("fixed_status_2"):
                        fixed_time_2 = app_setting.get("fixed_time_2").split(":")
                        hour2 = fixed_time_2[0]
                        minute2 = fixed_time_2[1]
                        scheduler.add_job(self.main_window.reset_data_on_click, "cron", hour=hour2, minute=minute2)  # noqa
                        log.info(f"固定任务添加成功：固定时间[{hour2}:{minute2}]清零一次")

                else:
                    # 间隔时间清除
                    h = app_setting.get("interval_time")
                    scheduler.add_job(self.main_window.reset_data_on_click, "interval", seconds=60 * 60 * h)  # noqa
                    log.info(f"间隔任务添加成功：每隔{h}小时清零一次")

            if interval_cron:
                interval_time = app_setting.get('custom_interval_time')

                scheduler.add_job(self.main_window.custom_cron_function, "interval", seconds=interval_time)  # noqa

                log.info(f"定时任务初始化")
                log.info(f"定制的定时任务已开启, 每隔{interval_time}秒({int(interval_time/60)}分钟)触发一次！")
            else:
                pass

            if interval_cron2:
                interval_time = app_setting.get('custom_interval_time2')

                scheduler.add_job(self.main_window.custom_cron_function2, "interval", seconds=interval_time)  # noqa

                log.info(f"定时任务2初始化")
                log.info(f"定制的定时任务2已开启, 每隔{interval_time}秒({int(interval_time / 60)}分钟)触发一次！")
            else:
                pass

            if interval_cron3:
                fixed_time = app_setting.get('custom_fixed_time')
                scheduler.add_job(self.main_window.custom_cron_function3, 'cron', **fixed_time) # noqa
                log.info(f"定时任务3初始化")
                log.info(f"定制的定时任务3已开启, 每固定时间点{fixed_time}触发一次！")

            log.info("定时任务启动完成")
            scheduler.start()
