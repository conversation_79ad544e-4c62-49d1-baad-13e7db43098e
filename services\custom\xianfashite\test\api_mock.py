# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : server.py
# Time       ：2025/2/7 下午4:33
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import socket

from common import xutil


def start_server(host='127.0.0.1', port=65432):
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    server_socket.bind((host, port))
    server_socket.listen()
    print(f"服务器已启动，监听 {host}:{port}")

    while True:
        conn, addr = server_socket.accept()
        print(f"连接来自 {addr}")

        data = conn.recv(4096)  # 每次接收4096个字节
        data = data.decode('utf-8')
        print(f"总接收到的数据：{data}")

        root = xutil.XmlUtil.get_xml_root_by_str(data)
        cmd = root.find('request').find('command').text

        if cmd == 'PARTTYPELIST':
            ret_str = """<acccomm>
    <response>
        <result>0</result>
        <message>success</message>
        <message2>
            <parttype>1;motor</parttype>
            <parttype>2;vivo</parttype>
            <parttype>3;xiaomi</parttype>
        </message2>
        <otherinfo></otherinfo>
    </response>
</acccomm>"""

        elif cmd == "PARTTYPEDATA":
            ret_str = """<acccomm>
    <response>
        <result>1</result>
        <message>切换型号失败</message>
        <message2>
            <parttypedata>
                <nint></nint>
                <nreal></nreal>
                <nstring></nstring>
            </parttypedata>
        </message2>
        <otherinfo></otherinfo>
    </response>
</acccomm>"""
        elif cmd == "LOAD":
            ret_str = """<acccomm>
    <response>
        <result>0</result>
        <message>Motor;401</message>
        <message2>
            <returnvalue>
                <param>CYCLE_ID,401</param>
                <param>STATUSBITS,1</param>
                <param>ISREWORK,True</param>
                <param>UNITPARTTYPEID,1</param>
                <param>PANELSTATE,</param>
            </returnvalue>
        </message2>
        <otherinfo></otherinfo>
    </response>
</acccomm>"""
        elif cmd == "UNLOAD":
            ret_str = """<acccomm>
    <response>
        <result>0</result>
        <message></message>
        <message2>
            <returnvalue>
                <param>CYCLE_ID,401</param>
            </returnvalue>
        </message2>
        <otherinfo></otherinfo>
    </response>
</acccomm>"""

        else:
            ret_str = b'ok'

        conn.sendall(ret_str.encode('utf-8'))

        conn.close()


if __name__ == "__main__":
    start_server(port=8085)
