# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/4 下午7:18
# Author     ：sch
# version    ：python 3.8
# Description：阳光电动力-EP30单独的线体    jira: ATAOI_2019-32856
"""
from typing import Any

from common import xcons, xrequest, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "yangguangdiandongli_ep30 release v1.0.0.2",
        "device": "303",
        "feature": ["条码校验", "上传数据", "从mes获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-04 19:18  init
date: 2024-12-09 11:01  从mes获取条码+上传数据参数修改
""",
    }

    form = {
        "operator_account": {
            "ui_name": "操作员",
            "value": "OP130-1"
        },
        "station_name": {
            "ui_name": "工站名",
            "value": "AOI"
        },
    }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/api/Home/CheckSerialNumberState"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/api/Home/UploadStateAndMeasurementData"
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "http://127.0.0.1:8081/api/Home/GetSerialNumberByCarrierCode"
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "Token": xutil.DateUtil.get_datetime_now(),
            "CarrierCode": pcb_sn,  # 托盘号
        }
        ret = xrequest.RequestUtil.post_json(api_url_get_sn, param)

        if ret.get("ReturnCode"):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('ErrorMessage')}")

        serial_number = ret.get('Data', "")  # 序列号

        return self.x_response("true", serial_number)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        """
        条码校验时的状态
        if statusCode == 0:
            状态1
        elif statusCode < -100 or statusCode > 0:
            状态3
        else:
            状态2

        1. 状态1       正常状态，进板检测                      mes接口返回：0
        2. 状态2       状态异常，停机报警                      mes接口返回：-100~0
        3. 状态3       直接流板，不需要检测也不需要有记录         mes接口返回：<-100     >0
        :param other_vo:
        :param other_param:
        :return:
        """
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station_name = other_vo.get_value_by_cons_key("station_name")

        sn_list = other_vo.list_sn()
        ret_res = self.x_response()

        for sn in sn_list:
            check_param = {
                "stationNumber": station_name,
                "serialNumber": sn,
                "layer": 1,
            }
            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

            return_code = ret.get("ReturnCode", -1)
            self.log.info(f"return code: {return_code}")

            if str(return_code) != "0":
                ret_res = self.x_response("false", f"mes接口异常: {ret.get('ErrorMessage')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        operator_account = data_vo.get_value_by_cons_key("operator_account")
        station_name = data_vo.get_value_by_cons_key("station_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_list = []
            ix = 0

            for comp_entity in board_entity.yield_comp_entity():
                ix += 1
                repair_ng_code = comp_entity.repair_ng_code

                if comp_entity.is_repair_ng():
                    comp_list.append({
                        "MeasureName": comp_entity.designator,
                        "MeasureValue": "",
                        "TestStepSequence": ix,
                        "FailureCode": repair_ng_code if comp_entity.is_repair_ng() else "",
                        "State": 1 if comp_entity.is_repair_ng() else 0,
                    })

            board_state = board_entity.get_repair_result(0, 1)

            if board_state == 0:
                comp_list = None

            data_param = {
                "stationNumber": station_name,
                "serialNumber": barcode,
                "layer": 1,
                "state": board_state,
                "measurementDataArray": comp_list,
                "operatorAccount": operator_account,
                "cycleTime": pcb_entity.get_cycle_time(),
                "productionDateTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "Token": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE),
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if str(ret.get("ReturnCode")) != "0":
                ret_res = self.x_response("false", f"mes接口异常，error：{ret.get('ErrorMessage')}")

        return ret_res
