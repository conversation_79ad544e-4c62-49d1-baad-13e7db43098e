# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/13 下午2:32
# Author     ：sch
# version    ：python 3.8
# Description：致丰微
"""
import os
from typing import Any

from common import xrequest, xcons, xutil
from common.xutil import x_response, log
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

global_data = {}


def x_refresh_token(api_url, username, password, card_id, company_code, retries_number):
    """
    刷新token
    :param api_url:
    :param username:
    :param password:
    :param card_id:
    :param company_code:
    :param retries_number:
    :return:
    """
    if username and password:
        card_id = ""
        log.warning(f"登录接口，如果LoginName和Password不为空时，CardID传空值!")

    ret = xrequest.RequestUtil.post_json_with_retries(api_url, {
        "LoginName": username,
        "Password": password,
        "CardID": card_id,
        "CompanyCode": company_code
    }, max_retries=retries_number)

    if not ret.get("success"):
        return x_response("false", f"mes接口异常，登录失败，error：{ret.get('msg')}")

    token = ret.get("response", {}).get("Token")
    if not token:
        return x_response("false", "未获取到token！")

    global_data["token"] = token
    log.info("获取token成功！")


class Engine(ErrorMapEngine):
    version = {
        "title": "zhifengwei release v1.0.0.5",
        "device": "AIS50X",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-13 17:32  获取条码，上传数据  
date: 2024-08-16 10:25  需求变更
date: 2024-08-17 15:31  增加获取条码模式
date: 2024-09-03 14:07  1小时刷新一次token
date: 2024-11-14 10:25  扫到什么条码就返回什么条码
""", }

    other_form = {
        "api_url_token": {
            "ui_name": "接口URL(用户登录)",
            "value": "https://www5.trio-prc.com:64443/api/Login",
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "https://www5.trio-prc.com:64443/AOI/GetUUTLabelNo",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "https://www5.trio-prc.com:64443/AOI/UploadAOITestData",
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/WS12LINEC(J1140GJ)"
        },
    }

    other_combo = {
        "api_retry_number": {
            "ui_name": "接口重试次数",
            "item": ["1", "2", "3", "5", "8", "10"],
            "value": "1",
        },
        "get_sn_mode": {
            "ui_name": "获取条码模式",
            "item": ["Normal", "Custom"],
            "value": "Normal",
        },
    }

    form = {
        "login_name": {
            "ui_name": "MES 账号",
            "value": "",
        },
        "password": {
            "ui_name": "账号密码",
            "value": "",
        },
        "card_id": {
            "ui_name": "ID卡号",
            "value": "",
        },
        "company_code": {
            "ui_name": "公司代码",
            "value": "1000",
        },
        "computer_name": {
            "ui_name": "操作电脑名称",
            "value": "PC10002",
        },
        # "order_id": {
        #     "ui_name": "工单",
        #     "value": "PC10002",
        # },

    }

    button = {
        "login_user_btn": {
            "ui_name": "登录Mes",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60)  # 6小时刷新一下token

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        api_retry_number = other_vo.get_value_by_cons_key("api_retry_number", to_int=True)

        api_url_token = other_vo.get_value_by_cons_key("api_url_token")
        login_name = other_vo.get_value_by_cons_key("login_name")
        password = other_vo.get_value_by_cons_key("password")
        card_id = other_vo.get_value_by_cons_key("card_id")
        company_code = other_vo.get_value_by_cons_key("company_code")
        get_sn_mode = other_vo.get_value_by_cons_key("get_sn_mode")

        pcb_sn = other_vo.get_pcb_sn()

        get_param = {
            "ClampNO": pcb_sn
        }

        token = global_data.get("token")

        if not token:
            if x_res := x_refresh_token(api_url_token, login_name, password, card_id, company_code, api_retry_number):
                return x_res

        token = global_data.get("token")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        if get_sn_mode == "Normal":
            ret = xrequest.RequestUtil.post_json_with_retries(api_url_get_sn, get_param,
                                                              headers=headers, max_retries=api_retry_number)

            if not ret.get("success"):
                return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('msg')}")

            barcode_str = ret.get('response', {}).get("UUTLabelNo", "")
            barcode_str_ret = barcode_str.replace(";", ",")

        else:
            barcode_map = other_vo.get_barcode_map()
            if "-1" in barcode_map:
                del barcode_map["-1"]

            if "-2" in barcode_map:
                del barcode_map["-2"]

            scan_barcode_list = list(barcode_map.values())
            scan_barcode_list_filter = [sn for sn in scan_barcode_list if sn]
            log.info(f"扫到的拼板条码：{scan_barcode_list_filter}")

            if not scan_barcode_list_filter:
                log.info(f"未扫到拼板条码，将从MES获取拼板条码...")
                ret = xrequest.RequestUtil.post_json_with_retries(api_url_get_sn, get_param,
                                                                  headers=headers, max_retries=api_retry_number)

                if not ret.get("success"):
                    return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('msg')}")

                barcode_str = ret.get('response', {}).get("UUTLabelNo", "")
                barcode_str_ret = barcode_str.replace(";", ",")
            else:
                log.info(f"扫到了拼板条码，直接将扫到的拼板条码返回！")
                barcode_str_ret = ",".join(scan_barcode_list)

        return self.x_response("true", barcode_str_ret)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        api_retry_number = data_vo.get_value_by_cons_key("api_retry_number", to_int=True)
        api_url_token = data_vo.get_value_by_cons_key("api_url_token")
        login_name = data_vo.get_value_by_cons_key("login_name")
        password = data_vo.get_value_by_cons_key("password")
        card_id = data_vo.get_value_by_cons_key("card_id")
        company_code = data_vo.get_value_by_cons_key("company_code")
        computer_name = data_vo.get_value_by_cons_key("computer_name")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        # order_id = data_dao.get_value_by_cons_key("order_id")

        token = global_data.get("token")

        if not token:
            if x_res := x_refresh_token(api_url_token, login_name, password, card_id, company_code, api_retry_number):
                return x_res

        token = global_data.get("token")

        headers = {
            "Authorization": f"Bearer {token}"
        }

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        pcb_sn = pcb_entity.pcb_barcode

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        the_first_barcode = ""

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = time_file[:8]

        order_id_only = ""

        ret_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            order_id = barcode[:8]

            if not order_id_only and order_id:
                order_id_only = order_id

            if not the_first_barcode and barcode:
                the_first_barcode = barcode

            log.info(f"工单号: {order_id}")

            time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

            comp_ftp_full_path = f"{ftp_path}/{date_file}/{order_id}/{pcb_sn}_{time_file}/{barcode}"
            ftp_client.cd_or_mkdir(comp_ftp_full_path)

            ok_count = 0
            for comp_entity in board_entity.yield_comp_entity():
                comp_src_img = comp_entity.image_path
                comp_side = comp_entity.board_side
                comp_tag = comp_entity.designator
                robot_ng_str = comp_entity.robot_ng_str
                repair_ng_str = comp_entity.repair_ng_str

                if comp_src_img:
                    comp_dst_img = f"{comp_side}_{comp_tag}_{robot_ng_str}_{repair_ng_str}.png"
                    ftp_client.upload_file(comp_src_img, comp_dst_img, is_log=False)

                    ok_count += 1

            log.info(f"拼板：{board_no}，本次成功上传器件图数量：{ok_count}")

            board_data = {
                "CardId": card_id,
                "UUTLabelNo": barcode,
                "TestTime": test_time,
                "TestResult": board_entity.get_repair_result("true", "false"),
                "UploadTime": time_now,
                "ClampNO": pcb_sn,
                "ComputerName": computer_name,
            }
            ret = xrequest.RequestUtil.post_json_with_retries(api_url_data, board_data,
                                                              headers=headers, max_retries=api_retry_number)

            if not ret.get("success"):
                ret_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('msg')}")

        all_image_list_origin = pcb_entity.list_all_pcb_image_origin_img()

        ftp_full_path = f"{ftp_path}/{date_file}/{order_id_only}/{pcb_sn}_{time_file}"
        ftp_client.cd_or_mkdir(ftp_full_path)

        if not all_image_list_origin:
            upload_img_list = pcb_entity.list_all_pcb_image()
            is_origin_img = False
        else:
            upload_img_list = all_image_list_origin
            is_origin_img = True

        self.log.info(f"开始上传整板图，上传数量：{len(upload_img_list)}")

        for pcb_src_filename in upload_img_list:
            if "/T_" in pcb_src_filename:
                board_side = "T"
            else:
                board_side = "B"

            dst_filename = f"{board_side}_{pcb_sn}_{time_file}_{the_first_barcode}.jpg"
            ftp_client.upload_file(pcb_src_filename, dst_filename)

            if is_origin_img:
                os.remove(pcb_src_filename)
                self.log.info(f"高清原大图已上传至ftp服务器，本地的已删除")

        ftp_client.close()

        if ret_msg_list:
            err_msg = "\n".join(ret_msg_list)
            return self.x_response("false", f"mes接口异常，上传测试数据失败，\n{err_msg}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()

        api_url_token = btn_vo.get_value_by_cons_key("api_url_token")
        login_name = btn_vo.get_value_by_cons_key("login_name")
        password = btn_vo.get_value_by_cons_key("password")
        card_id = btn_vo.get_value_by_cons_key("card_id")
        company_code = btn_vo.get_value_by_cons_key("company_code")
        api_retry_number = btn_vo.get_value_by_cons_key("api_retry_number", to_int=True)

        if btn_key == "login_user_btn":
            if x_res := x_refresh_token(api_url_token, login_name, password, card_id, company_code, api_retry_number):
                return x_res

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_token = other_vo.get_value_by_cons_key("api_url_token")
        login_name = other_vo.get_value_by_cons_key("login_name")
        password = other_vo.get_value_by_cons_key("password")
        card_id = other_vo.get_value_by_cons_key("card_id")
        company_code = other_vo.get_value_by_cons_key("company_code")
        api_retry_number = other_vo.get_value_by_cons_key("api_retry_number", to_int=True)

        self.log.info(f"定时刷新token中...")

        x_refresh_token(api_url_token, login_name, password, card_id, company_code, api_retry_number)
