# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/2/5 上午11:38
# Author     ：sch
# version    ：python 3.8
# Description：松下万宝美   https://jira.cvte.com/browse/ATAOI_2019-36528
"""

from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "songxiawanbaomei release v1.0.0.3",
        "device": "AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-02-05 15:19  jira->36528: 上传数据+登录
date: 2025-03-25 12:01  bugfix: 修改枚举值
date: 2025-03-28 16:24  bugfix:修改Method值
""", }

    form = {
        "username": {
            "ui_name": "用户名",
            "value": "admin",
        },
        "password": {
            "ui_name": "密码",
            "value": "admin",
        },
        "work_order_no": {
            "ui_name": "MES工单号",
            "value": "OrderNo001",
        },
        "inv_org_id": {
            "ui_name": "InvOrgId",
            "value": "1",
        },
        "device_id": {
            "ui_name": "设备编码",
            "value": "AOI001",
        },
        "prefix_path": {
            "ui_name": "拼接前缀路径",
            "value": "http://127.0.0.1/MESData",
        },
    }

    other_form = {
        "api_url_login": {
            "ui_name": "登录接口",
            "value": "http://127.0.0.1:8081/wbm/login",
        },
        "api_url_data": {
            "ui_name": "数据接口",
            "value": "http://127.0.0.1:8081/wbm/data",
        },
    }

    path = {
        "save_path_img_ng": {
            "ui_name": "NG图片保存路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_login = data_vo.get_value_by_cons_key("api_url_login")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        username = data_vo.get_value_by_cons_key("username")
        password = data_vo.get_value_by_cons_key("password")
        save_path_img_ng = data_vo.get_value_by_cons_key("save_path_img_ng", not_null=True)

        work_order_no = data_vo.get_value_by_cons_key("work_order_no")
        inv_org_id = data_vo.get_value_by_cons_key("inv_org_id")
        device_id = data_vo.get_value_by_cons_key("device_id")
        prefix_path = data_vo.get_value_by_cons_key("prefix_path")

        try:
            inv_org_id = int(inv_org_id)
        except Exception as err:
            self.log.warning(f"inv_org_id转换失败，{err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # 0. 登录
        login_data = {
            "ApiType": "AuthenticationController",
            "Parameters": [
                {
                    "Value": username
                },
                {
                    "Value": password
                }
            ],
            "Method": "Login",
            "Context": {}
        }

        ret = xrequest.RequestUtil.post_json(api_url_login, login_data)

        if not ret.get("Success"):
            return self.x_response("false", f"登录失败，{ret.get('Message')}")

        ticket = ret.get("Context").get("Ticket")
        if not ticket:
            return self.x_response("false", "登录失败，ticket为空")

        file_date = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        project_name = pcb_entity.project_name

        project_count = xutil.CacheUtil.get(f"project_count_{project_name}", 0)  # 某个程序跑了几块板
        project_count += 1
        xutil.CacheUtil.set(f"project_count_{project_name}", project_count)

        self.log.info(f"该程序{project_name}已跑了{project_count}块板")

        # Qty传当前程序的复判不良板卡数，持续累加，根据工单统计，不同工单分开累加
        order_count = xutil.CacheUtil.get(f"order_count_{work_order_no}", 0)

        for board_entity in pcb_entity.yield_board_entity():
            if board_entity.is_repair_ng():
                order_count += 1

        xutil.CacheUtil.set(f"order_count_{work_order_no}", order_count)
        self.log.info(f"该工单{work_order_no} Qty: {order_count}")

        err_msg_list = []

        board_side = pcb_entity.get_board_side()

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            comp_dst_path = f"{save_path_img_ng}/{file_date}/{project_name}-{project_count}-{barcode}"
            xutil.FileUtil.ensure_dir_exist(comp_dst_path)

            comp_ix = 0

            comp_ng_list = []
            comp_img_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    comp_img_name = ""

                    if comp_entity.is_repair_ng():
                        comp_ix += 1

                        comp_src_img = comp_entity.image_path

                        if comp_src_img:

                            try:
                                comp_img_name = f"{comp_ix}_{comp_entity.designator}.png"
                                comp_dst_filepath = f"{comp_dst_path}/{comp_img_name}"
                                xutil.FileUtil.copy_file(comp_src_img, comp_dst_filepath)

                                concat_path = f"{prefix_path}/{file_date}/{project_name}-{project_count}-{barcode}/{comp_img_name}"

                                comp_img_ng_list.append({
                                    "ImageUrl": concat_path,
                                    "ImageIndex": str(comp_ix),
                                    "TotalImage": str(board_entity.comp_robot_ng_number),
                                    "UploadResult": True
                                })
                            except Exception as e:
                                err_msg_list.append(f"拷贝NG图片失败，error: {e}")
                    else:
                        self.log.warning(f"元件{comp_entity.designator}为误报器件，未上传NG图片！")

                    comp_ng_list.append({
                        "BothSideCode": board_side,
                        "CreateDate": start_time,
                        "ReviseEndDate": review_time,
                        "TestResult": comp_entity.get_final_result(True, False, False),
                        "ReviseResult": comp_entity.get_final_result(True, True, False),
                        "Barcode": barcode,
                        "PartsName": comp_entity.designator,
                        "FaultCode": comp_entity.robot_ng_code,
                        "RevisedFaultCode": comp_entity.repair_ng_code,
                        "ImageUrl": comp_img_name
                    })

            # 1. 上传数据到mes
            data_param = {
                "ApiType": "ScadaApiController",
                "Parameters": [
                    {
                        "Value": {
                            "ProgramName": pcb_entity.project_name,
                            "Qty": str(order_count),
                            "DeviceId": device_id,
                            "Barcode": barcode,
                            "TestResult": board_entity.get_robot_result(True, False),
                            "ReviseResult": board_entity.get_repair_result(True, False),
                            "TimeStamp": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                            "WorkOrderNo": work_order_no,
                            "PartDetectionInfos": comp_ng_list,
                            "ImageInfos": comp_img_ng_list
                        }
                    }
                ],
                "Method": "AOIWipMove",
                "Context": {
                    "Ticket": ticket,
                    "InvOrgId": inv_org_id
                }
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if not ret.get("Success"):
                err_msg_list.append(f"上传MES失败，{ret.get('Message')}")

        if err_msg_list:
            err_msg_str = '\n'.join(err_msg_list)[:200]
            return self.x_response("false", f"上传MES失败，{err_msg_str}")

        return self.x_response()
