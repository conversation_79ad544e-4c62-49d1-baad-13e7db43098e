"""
# File       : main.py
# Time       ：2025/05/20 10:23
# Author     ："wxc"
# version    ：python 3.8
# Description：陕西高盛达
"""
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

board_template = """{project_name}
{board_barcode}
{device_name}
{no}
{repair_user}
{work_id}
{test_time}
{review_time}
{final_result}
{side}
{pcb_comp_count}
{board_comp_ng_count}
{comp_data}##"""
comp_template = """{designator};{part};{robot_result};{repair_result};{result_code}
"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["陕西高盛达", "shangxigaoshengda"],
        "version": "release v1.0.0.1",
        "device": "AIS430-D",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-20 10:29 ATAOI_2019-39613：本地生成文件
"""
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },

    }
    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": ""
        },

    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT)
        # 整板总点数
        pcb_comp_count = 0
        for board_entity in pcb_entity.yield_board_entity():
            pcb_comp_count += board_entity.comp_total_number

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_barcode = board_entity.barcode
            no = board_entity.board_no
            comp_data = ""
            for comp in board_entity.yield_comp_entity():
                result_code = "0" if comp.repair_result else "1"
                comp_data += comp_template.format(
                    designator=comp.designator + "_" + no,
                    part=comp.part,
                    robot_result=comp.robot_ng_str,
                    repair_result="OK" if comp.repair_result else comp.repair_ng_str,
                    result_code=result_code
                )
            file_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
            file_name = f"（{pcb_entity.get_final_result('PASS', 'PASS', 'FAIL')}）{board_barcode}_{device_name}_{file_time}_{no}"
            file_path = f"{save_path}/{file_name}.txt"
            board_data = {
                "project_name": pcb_entity.project_name,
                "board_barcode": board_barcode,
                "device_name": device_name,
                "no": no,
                "repair_user": pcb_entity.repair_user,
                "work_id": 0,
                "test_time": test_time,
                "review_time": review_time,
                "final_result": pcb_entity.get_final_result('PASS', 'PASS', 'FAIL'),
                "side": pcb_entity.board_side,
                "pcb_comp_count": pcb_comp_count,  # 程序总点数
                "board_comp_ng_count": board_entity.comp_repair_ng_number,  # 当前小板不良点数
                "comp_data": comp_data
            }
            xutil.FileUtil.write_content_to_file_pro(file_path, board_template.format(**board_data))

        return self.x_response()
