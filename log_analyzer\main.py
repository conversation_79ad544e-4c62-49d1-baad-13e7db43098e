#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志快速定位分析工具
主程序入口
"""

import sys
import os
import argparse
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("警告: PyQt5未安装，将使用命令行模式")

try:
    from .log_parser import LogParser
    from .log_searcher import LogSearcher, SearchCriteria
    from .error_analyzer import ErrorAnalyzer
    from .log_exporter import LogExporter
    from .config_manager import ConfigManager
    if GUI_AVAILABLE:
        from .main_window import LogAnalyzerMainWindow
except ImportError:
    from log_parser import LogParser
    from log_searcher import LogSearcher, SearchCriteria
    from error_analyzer import <PERSON>rrorAnalyzer
    from log_exporter import LogExporter
    from config_manager import ConfigManager
    if GUI_AVAILABLE:
        from main_window import LogAnalyzerMainWindow


class CommandLineInterface:
    """命令行界面"""
    
    def __init__(self):
        self.parser = LogParser()
        self.searcher = LogSearcher()
        self.analyzer = ErrorAnalyzer()
        self.exporter = LogExporter()
        self.config_manager = ConfigManager()
    
    def run(self, args):
        """运行命令行模式"""
        if args.command == 'analyze':
            self.analyze_logs(args)
        elif args.command == 'search':
            self.search_logs(args)
        elif args.command == 'export':
            self.export_logs(args)
        elif args.command == 'config':
            self.show_config(args)
    
    def analyze_logs(self, args):
        """分析日志"""
        print(f"正在分析日志: {args.path}")
        
        if os.path.isdir(args.path):
            log_files = self.parser.scan_log_directory(args.path)
        else:
            log_files = [args.path]
        
        if not log_files:
            print("未找到日志文件")
            return
        
        print(f"找到 {len(log_files)} 个日志文件")
        
        all_entries = []
        for file_path in log_files:
            print(f"解析: {file_path}")
            entries = self.parser.parse_log_file(file_path)
            all_entries.extend(entries)
        
        print(f"共解析 {len(all_entries)} 条日志")
        
        # 错误分析
        if args.error_analysis:
            print("\n=== 错误分析 ===")
            error_summary = self.analyzer.get_error_summary(all_entries)
            
            print(f"总错误数: {error_summary['total_errors']}")
            print("\n严重程度分布:")
            for severity, count in error_summary['severity_distribution'].items():
                print(f"  {severity}: {count}")
            
            print("\n错误分类:")
            for category, count in error_summary['error_categories'].items():
                print(f"  {category}: {count}")
            
            print("\n最频繁错误 (Top 5):")
            for i, error in enumerate(error_summary['top_errors'][:5], 1):
                print(f"  {i}. [{error['code']}] {error['description']} - {error['count']}次")
        
        # 统计信息
        if args.statistics:
            print("\n=== 统计信息 ===")
            self.searcher.load_entries(all_entries)
            stats = self.searcher.get_statistics()
            
            print(f"总条目数: {stats['total']}")
            print("\n日志级别分布:")
            for level, count in stats['levels'].items():
                print(f"  {level}: {count}")
            
            print("\n文件分布 (Top 5):")
            for file_name, count in list(stats['files'].items())[:5]:
                print(f"  {file_name}: {count}")
    
    def search_logs(self, args):
        """搜索日志"""
        print(f"正在搜索日志: {args.path}")
        print(f"搜索关键词: {args.keyword}")
        
        if os.path.isdir(args.path):
            log_files = self.parser.scan_log_directory(args.path)
        else:
            log_files = [args.path]
        
        all_entries = []
        for file_path in log_files:
            entries = self.parser.parse_log_file(file_path)
            all_entries.extend(entries)
        
        self.searcher.load_entries(all_entries)
        
        # 构建搜索条件
        criteria = SearchCriteria()
        criteria.keywords = [args.keyword]
        
        if args.level:
            criteria.levels = [args.level.upper()]
        
        if args.error_code:
            criteria.error_codes = [args.error_code]
        
        criteria.case_sensitive = args.case_sensitive
        
        # 执行搜索
        results = self.searcher.search(criteria)
        
        print(f"\n找到 {len(results)} 条匹配的日志:")
        print("-" * 80)
        
        for entry in results[:args.limit]:
            time_str = entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else "未知时间"
            file_name = os.path.basename(entry.file_path)
            print(f"[{time_str}] [{entry.level}] {file_name}:{entry.line_number}")
            print(f"  {entry.message}")
            if entry.error_code:
                print(f"  错误码: {entry.error_code}")
            print()
    
    def export_logs(self, args):
        """导出日志"""
        print(f"正在导出日志: {args.path}")
        
        if os.path.isdir(args.path):
            log_files = self.parser.scan_log_directory(args.path)
        else:
            log_files = [args.path]
        
        all_entries = []
        for file_path in log_files:
            entries = self.parser.parse_log_file(file_path)
            all_entries.extend(entries)
        
        # 过滤条件
        if args.level:
            all_entries = [e for e in all_entries if e.level == args.level.upper()]
        
        if args.keyword:
            all_entries = [e for e in all_entries if args.keyword.lower() in e.message.lower()]
        
        # 导出
        success = self.exporter.export_filtered_logs(all_entries, args.output, args.format)
        
        if success:
            print(f"成功导出 {len(all_entries)} 条日志到: {args.output}")
        else:
            print("导出失败")
    
    def show_config(self, args):
        """显示配置"""
        summary = self.config_manager.get_config_summary()
        
        print("=== 配置信息 ===")
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        if args.reset:
            self.config_manager.reset_to_defaults()
            print("\n配置已重置为默认值")


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="日志快速定位分析工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动GUI界面
  python main.py
  
  # 分析日志文件
  python main.py analyze /path/to/logs --error-analysis --statistics
  
  # 搜索日志
  python main.py search /path/to/logs --keyword "error" --level ERROR
  
  # 导出日志
  python main.py export /path/to/logs --output results.csv --format csv --level ERROR
  
  # 显示配置
  python main.py config
        """
    )
    
    parser.add_argument('--gui', action='store_true', help='启动GUI界面（默认）')
    parser.add_argument('--cli', action='store_true', help='使用命令行界面')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 分析命令
    analyze_parser = subparsers.add_parser('analyze', help='分析日志文件')
    analyze_parser.add_argument('path', help='日志文件或目录路径')
    analyze_parser.add_argument('--error-analysis', action='store_true', help='执行错误分析')
    analyze_parser.add_argument('--statistics', action='store_true', help='显示统计信息')
    
    # 搜索命令
    search_parser = subparsers.add_parser('search', help='搜索日志')
    search_parser.add_argument('path', help='日志文件或目录路径')
    search_parser.add_argument('--keyword', required=True, help='搜索关键词')
    search_parser.add_argument('--level', help='日志级别过滤')
    search_parser.add_argument('--error-code', help='错误码过滤')
    search_parser.add_argument('--case-sensitive', action='store_true', help='区分大小写')
    search_parser.add_argument('--limit', type=int, default=50, help='结果数量限制')
    
    # 导出命令
    export_parser = subparsers.add_parser('export', help='导出日志')
    export_parser.add_argument('path', help='日志文件或目录路径')
    export_parser.add_argument('--output', required=True, help='输出文件路径')
    export_parser.add_argument('--format', default='csv', 
                              choices=['csv', 'excel', 'json', 'xml', 'html', 'txt'],
                              help='导出格式')
    export_parser.add_argument('--level', help='日志级别过滤')
    export_parser.add_argument('--keyword', help='关键词过滤')
    
    # 配置命令
    config_parser = subparsers.add_parser('config', help='配置管理')
    config_parser.add_argument('--reset', action='store_true', help='重置为默认配置')
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 如果没有指定命令且GUI可用，启动GUI
    if not args.command and GUI_AVAILABLE and not args.cli:
        app = QApplication(sys.argv)
        app.setApplicationName("日志快速定位分析工具")
        app.setApplicationVersion("1.0")
        
        try:
            window = LogAnalyzerMainWindow()
            window.show()
            sys.exit(app.exec_())
        except Exception as e:
            QMessageBox.critical(None, "错误", f"启动GUI失败: {str(e)}")
            sys.exit(1)
    
    # 命令行模式
    elif args.command or args.cli or not GUI_AVAILABLE:
        if not args.command:
            print("错误: 命令行模式需要指定命令")
            parser.print_help()
            sys.exit(1)
        
        cli = CommandLineInterface()
        try:
            cli.run(args)
        except KeyboardInterrupt:
            print("\n操作被用户中断")
            sys.exit(1)
        except Exception as e:
            print(f"错误: {str(e)}")
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == '__main__':
    main()
