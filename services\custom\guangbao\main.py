# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/18 上午9:18
# Author     ：sch
# version    ：python 3.8
# Description：光宝、东莞光宝
"""
from typing import Any

from common import xutil, xrequest
from common.xglobal import global_data
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "guangbao release v1.0.0.1",
        "device": "AIS401",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-18 15:15  条码校验，上传数据
""",
    }

    other_form = {
    }

    form = {
        "window_ip": {
            "ui_name": "windowIp(中转电脑)",
            "value": ""
        },
        "username": {
            "ui_name": "工号",
            "value": ""
        },
    }

    button = {
        "login_user": {
            "ui_name": "校验工号"
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        sn_list = other_vo.list_sn()

        x_res = self.x_response()

        for sn in sn_list:
            pocket_param = {
                "type": 2,
                "request_param": f"{sn};",
                "request_id": xutil.OtherUtil.get_uuid4_str(),
                "send_time": xutil.DateUtil.get_datetime_now(),
            }

            if sn != "badBoard":
                ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param)
                ret_str = ret.get('string')
                if "ok" not in ret_str.lower():
                    x_res = self.x_response("false", f"接口响应异常，条码校验，cmd2调用失败，error：{ret_str}")

        return x_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        window_ip = data_vo.get_value_by_cons_key("window_ip")
        username = data_vo.get_value_by_cons_key("username")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        x_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            # board_no = board_entity.board_no

            just_one_ng_code = ""

            user_result = board_entity.get_repair_result("OK", "NG")

            ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():

                    if not just_one_ng_code:
                        just_one_ng_code = f"{comp_entity.repair_ng_code};"

                    ng_list.append(f"{comp_entity.designator}:{comp_entity.repair_ng_code}")

            param3_str = f"{username};{barcode};{user_result};{just_one_ng_code}"
            pocket_param3 = {
                "type": 3,
                "request_param": param3_str,
                "request_id": xutil.OtherUtil.get_uuid4_str(),
                "send_time": xutil.DateUtil.get_datetime_now(),
            }

            ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param3)
            ret_str = ret.get('string')
            if "ok" not in ret_str.lower():
                x_res = self.x_response("false", f"接口响应异常，上传过站失败，cmd3调用失败，error：{ret_str}")

            if ng_list:
                req_param6 = f"{username};{barcode};{';'.join(ng_list)};"
                pocket_param6 = {
                    "type": 6,
                    "request_param": req_param6,
                    "request_id": xutil.OtherUtil.get_uuid4_str(),
                    "send_time": xutil.DateUtil.get_datetime_now(),
                }

                ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param6)
                ret_str = ret.get('string')
                if "ok" not in ret_str.lower():
                    x_res = self.x_response("false", f"接口响应异常，上传测试值失败，cmd3调用失败，error：{ret_str}")

            else:
                self.log.warning(f"没有NG测试值需要上传！")

        return x_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        username = btn_vo.get_value_by_cons_key("username")
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "login_user":
            pocket_data = {
                "type": 1,
                "request_param": f"{username};",
                "request_id": xutil.OtherUtil.get_uuid4_str(),
                "send_time": xutil.DateUtil.get_datetime_now(),
            }
            ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_data)
            msg = ret.get("string")
            if 'ok' not in msg.lower():
                return self.x_response()("false", f"接口响应异常，校验工号失败，cmd1调用失败，error：{msg}")

            global_data["is_login"] = True

        return self.x_response()
