# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/16 上午10:58
# Author     ：sch
# version    ：python 3.8
# Description：东莞立鼎
"""
import json
import traceback
from typing import Any

from common import xutil, xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

txt_template = """{project_name}
{barcode}
{device_code}
SHIP
{operator_id}
{work_order}
{test_date}
{test_time}
{test_result}
{board_side}
{comp_number}
{comp_repair_ng_number}{comp_data_str}

"""


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanliding release v1.0.0.5",
        "device": "AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-16 16:44  上传数据到MES
date: 2024-07-18 09:15  修改参数
date: 2024-07-23 18:09  兼容返回参数
""", }

    other_form = {
        "api_url_data_ld": {
            "ui_name": "接口URL",
            "value": "",
        },
        "line_no": {
            "ui_name": "线体名称",
            "value": "",
        },
        "device_code": {
            "ui_name": "机台编号(TXT)",
            "value": "",
        },
        "terminal_id1": {
            "ui_name": "站点(1轨)",
            "value": "",
        },
        "terminal_id2": {
            "ui_name": "站点(2轨)",
            "value": "",
        },
    }

    form = {
        "work_order": {
            "ui_name": "工单号",
            "value": "",
        },
        "operator_id": {
            "ui_name": "操作员工号",
            "value": "",
        },
    }

    combo = {
        "machine_type": {
            "ui_name": "设备类型",
            "item": ["AOI", "SPI"],
            "value": "AOI",
        },
        "is_check_x": {
            "ui_name": "是否检测X板",
            "item": ["1", "0"],
            "value": "1",
        },
        "is_bind_mic": {
            "ui_name": "是否绑定MIC零件码",
            "item": ["1", "0"],
            "value": "1",
        },
        "is_retest": {
            "ui_name": "是否卡复测",
            "item": ["1", "0"],
            "value": "1",
        },
        "is_bind_fixture_sn": {
            "ui_name": "是否载具绑定",
            "item": ["1", "0"],
            "value": "1",
        },
        "board_type_ui": {
            "ui_name": "大小板过站",
            "item": ["1", "0"],
            "value": "0",
        },
        "is_upload_mic_len": {
            "ui_name": "上传MIC码长度",
            "item": ["1", "0"],
            "value": "1",
        },
        "check_type_ui": {
            "ui_name": "check in/out",
            "item": ["1", "0"],
            "value": "1",
        },
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        },
        "bak_path": {
            "ui_name": "备份路径",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data_ld = data_vo.get_value_by_cons_key("api_url_data_ld")
        line_no = data_vo.get_value_by_cons_key("line_no")
        work_order = data_vo.get_value_by_cons_key("work_order")
        terminal_id1 = data_vo.get_value_by_cons_key("terminal_id1")
        terminal_id2 = data_vo.get_value_by_cons_key("terminal_id2")
        machine_type = data_vo.get_value_by_cons_key("machine_type")
        device_code = data_vo.get_value_by_cons_key("device_code")
        operator_id = data_vo.get_value_by_cons_key("operator_id")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        bak_path = data_vo.get_value_by_cons_key("bak_path", not_null=True)

        is_check_x = data_vo.get_value_by_cons_key("is_check_x")
        is_bind_mic = data_vo.get_value_by_cons_key("is_bind_mic")
        is_retest = data_vo.get_value_by_cons_key("is_retest")
        is_bind_fixture_sn = data_vo.get_value_by_cons_key("is_bind_fixture_sn")
        board_type_ui = data_vo.get_value_by_cons_key("board_type_ui")
        is_upload_mic_len = data_vo.get_value_by_cons_key("is_upload_mic_len")
        check_type_ui = data_vo.get_value_by_cons_key("check_type_ui")

        origin_review_path = other_data.get("review_path", "")
        if ";" in origin_review_path:
            mes_path1, mes_path2 = origin_review_path.split(";")
            dict_comp_barcode1 = xutil.XmlUtil.dict_comp_barcode(f"{mes_path1}/report.xml")
            dict_comp_barcode2 = xutil.XmlUtil.dict_comp_barcode(f"{mes_path2}/report.xml")
        else:
            dict_comp_barcode1 = xutil.XmlUtil.dict_comp_barcode(f"{origin_review_path}/report.xml")
            dict_comp_barcode2 = {}

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if pcb_entity.track_index == 1:
            terminal_id = terminal_id1
        else:
            terminal_id = terminal_id2

        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT1)

        command_list = [
            is_check_x,
            is_bind_mic,
            is_retest,
            is_bind_fixture_sn,
            board_type_ui,
            is_upload_mic_len,
            check_type_ui
        ]

        board_data = []

        txt_content = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            try:
                material_code1 = dict_comp_barcode1.get(board_no, [])
                material_code2 = dict_comp_barcode2.get(board_no, [])

                material_code_list = []

                if material_code1:
                    material_code_list += material_code1

                if material_code2:
                    material_code_list += material_code2

                if not material_code_list:
                    self.log.warning(f"找不到器件条码！")
                    material_code = ""
                else:
                    material_code = ",".join(material_code_list)

            except Exception as err:
                self.log.warning(f"没有器件条码，err: {err}")
                self.log.warning(traceback.format_exc())
                material_code = ""

            comp_data_list = []

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_data_list.append({
                        "RefranceID": comp_entity.designator,
                        "ErrorCode": comp_entity.repair_ng_code
                    })

                    comp_data_str += f"\n{comp_entity.designator};{comp_entity.part};{comp_entity.repair_ng_str};0;0;0;"
            board_item = {
                "Blocknumber": board_no.zfill(2),
                "BoardBarCode": barcode,
                "SKIP": board_entity.get_final_result("N", "N", "N", "Y"),
                "ComponmentID": material_code,
                "TestResult": board_entity.get_final_result("PASS", "RPASS", "REPAIR"),
            }

            if comp_data_list:
                board_item["Errordetail"] = comp_data_list

            board_data.append(board_item)

            txt_content += txt_template.format(**{
                "project_name": pcb_entity.project_name,
                "barcode": barcode,
                "device_code": device_code,
                "operator_id": operator_id,
                "work_order": work_order,
                "test_date": pcb_entity.get_start_time().strftime(xcons.FMT_DATE),
                "test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME),
                "test_result": board_entity.get_final_result("0;0;1;0;", "0;0;1;0;", "0;0;0;1;", "1;0;0;1;"),
                "board_side": pcb_entity.board_side,
                "comp_number": board_entity.comp_total_number,
                "comp_repair_ng_number": board_entity.comp_repair_ng_number,
                "comp_data_str": comp_data_str
            })

        pcb_param = {
            "CMD": ",".join(command_list),
            "WorkOrder": work_order,
            "LaneNO": str(pcb_entity.track_index),
            "MachineType": machine_type,
            "TerminalID": terminal_id,
            "OperatorID": operator_id,
            "ProgramName": pcb_entity.project_name,
            "ToolingSN": pcb_entity.pcb_barcode,
            "CreateTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT1),
            "ReviseEndTime": review_time,
            "TestResult": pcb_entity.get_robot_result("PASS", "FAIL"),
            "ReviseResult": pcb_entity.get_final_result("PASS", "REPASS", "REPAIR"),
            "Panels": [{
                "PanelBarCode": pcb_entity.pcb_barcode,
                "TestResult": pcb_entity.get_robot_result("PASS", "FAIL"),
                "ReviseResult": pcb_entity.get_final_result("PASS", "REPASS", "REPAIR"),
                "Boards": board_data
            }]
        }

        ret_res = self.x_response()

        param_str = json.dumps(pcb_param, ensure_ascii=False, separators=(",", ":"))
        ret_str = xrequest.RequestUtil.post_form(api_url_data_ld, {"Parames": param_str}, to_json=False)
        ret = xutil.XmlUtil.get_xml_root_by_str(ret_str).text

        ret = json.loads(ret)

        ret0 = ret[0]

        if str(ret0.get("Result")) != "OK":
            ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret0.get('Message')}")

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        save_filepath = f"{save_path}/{line_no}_{time_file}.txt"
        bak_filepath = f"{bak_path}/{line_no}_{time_file}.txt"

        try:
            xutil.FileUtil.write_content_to_file(save_filepath, txt_content)
        except Exception as err:
            ret_res = self.x_response("false", f"TXT文件生成失败，已保存到备份路径！err: {err}")
            xutil.FileUtil.write_content_to_file(bak_filepath, txt_content)

        return ret_res


if __name__ == '__main__':
    ret_str1 = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="MesWebService">[{"Result":"NG","Message":"B-2DAOI-A
5-3F-S08-2TerminalID 不存在!"}]</string>
"""

    root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    print(root1.text)