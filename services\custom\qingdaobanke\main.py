"""
# File       : main.py
# Time       ：2025/05/09 17:51
# Author     ："wxc"
# version    ：python 3.8
# Description：青岛斑科
"""
import json
import os
from datetime import datetime
from typing import Any

from common import xrequest, xcons, xutil
from common.xutil import log
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo

circle_list = xutil.CircularList(1000)

def upload_pcb_image(pcb_image, pcb_sn, lbwp_id, upload_image_api):
    """
    上传PCB图片到指定API接口
    """
    # 获取文件修改时间和文件名
    file_date = datetime.fromtimestamp(os.path.getmtime(pcb_image)).strftime(xcons.FMT_TIME_DEFAULT)
    file_name = os.path.basename(pcb_image)

    # 构建图片数据
    image_data = {
        "lb_id": pcb_sn,
        "lbwp_id": lbwp_id,
        "file_name": file_name,
        "file_date": file_date
    }

    # 记录请求信息（不打印base64大文件）
    log.info(f"请求url：{upload_image_api} 请求参数data：{json.dumps(image_data, indent=4)}")
    log.info(f"图片已转成base64编码附加到[data:file_data]参数里，因参数过大，并没有打印！图片地址：{pcb_image}")

    # 添加base64编码的图片数据
    image_data["file_data"] = xutil.ImageUtil.file_to_base64_content(pcb_image)

    # 构建请求参数
    image_param = {
        "docType": "UPLODE_PHOTO",
        "updateType": "UPDATE",
        "data": [image_data]
    }

    # 发送请求
    ret = xrequest.RequestUtil.post_json(upload_image_api, image_param, log_number=0)
    return ret


class Engine(ErrorMapEngine):
    version = {
        "customer": ["青岛斑科", "qingdaobanke"],
        "version": "release v1.0.0.13",
        "device": "AIS203 AIS303",
        "feature": ["获取条码", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-09 18:06  ATAOI_2019-39360：获取条码,上传数据,上传图片
date: 2025-05-12 10:13  修改上传图片base64日志打印,数据上传接口请求参数修改
date: 2025-05-20 09:13  bugfix：wp_code参数存在空格，mes无法识别，去除空格
date: 2025-05-23 10:46  bugfix：docType参数值存在空格，去除空格（复制请求示例时导致的空格）
date: 2025-05-23 17:05  先传过站信息取到lbwp_id再上传图片
date: 2025-05-23 19:05  bugfix：ndc_components参数存在空格
date: 2025-05-23 20:11  上传图片时lb_code改为拼版条码
date: 2025-05-27 09:21  修改上传数据逻辑：器件明细不判断是否不良，都上传
date: 2025-05-27 15:04  修改上传数据逻辑：器件明细testdt只上传不良的
date: 2025-06-04 14:14  修改上传数据逻辑：按整板上传，testdt里面增加comp_data
date: 2025-06-12 09:51  修改上传数据逻辑：机判NG时才传器件数据，comp_data外层增加mct_result，md_result
date: 2025-06-14 10:19  bar_code与lb_code保持一致，均为拼版条码
date: 2025-07-15 11:36  bugfix:延后复判导致全局key都用都一个pcb_sn
"""
    }

    form = {
        "dev_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "wp_code": {
            "ui_name": "工序编码",
            "value": "",
        },
        "models_name": {
            "ui_name": "机种名称",
            "value": "",
        },
        "test_batch": {
            "ui_name": "测试批次",
            "value": "",
        },
        "user_code": {
            "ui_name": "操作员工号",
            "value": "",
        },
        "pguser_codessss": {
            "ui_name": "编程员工号",
            "value": "",
        },
    }
    other_form = {
        "get_sn_api": {
            "ui_name": "下发SN接口",
            "value": "http://ip:port/ims-integrate/api/updateImsData",
        },
        "upload_api": {
            "ui_name": "数据上传接口",
            "value": "http://ip:port/ims-integrate/api/updateImsData",
        },
        "upload_image_api": {
            "ui_name": "照片上传接口",
            "value": "http://ip:port/ims-integrate/api/updateImsData",
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        wp_code = other_vo.get_value_by_cons_key("wp_code")
        get_sn_api = other_vo.get_value_by_cons_key("get_sn_api")

        pcb_sn = other_vo.get_pcb_sn()
        param = {
            "docType": "UPLODE_LBISSUE",
            "updateType": "UPDATE",
            "data": [
                {
                    "lb_code": pcb_sn,
                    "wp_code": wp_code
                }
            ]
        }
        ret = xrequest.RequestUtil.post_json(get_sn_api, param)
        if str(ret.get("resultCode")) != "0000":
            return self.x_response("false", f"mes接口异常, message={ret.get('resultMsg')}")
        ret_result = ret.get('resultData')[0]
        lb_code = ret_result.get('lb_code', [])
        lb_ids = [item["lb_id"] for item in lb_code]

        circle_list.add_item(pcb_sn)

        result = ",".join(lb_ids)
        return self.x_response('true', result)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        upload_api = data_vo.get_value_by_cons_key("upload_api")
        upload_image_api = data_vo.get_value_by_cons_key("upload_image_api")
        dev_code = data_vo.get_value_by_cons_key("dev_code")
        wp_code = data_vo.get_value_by_cons_key("wp_code")
        models_name = data_vo.get_value_by_cons_key("models_name")
        test_batch = data_vo.get_value_by_cons_key("test_batch")
        user_code = data_vo.get_value_by_cons_key("user_code")
        pguser_codessss = data_vo.get_value_by_cons_key("pguser_codessss")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)
        pcb_cycle_time = pcb_entity.get_cycle_time()
        # 取获取条码阶段的扫到条码
        pcb_sn = pcb_entity.pcb_barcode
        if circle_list.is_exist_item(pcb_sn):
            self.log.info(f"获取到的扫到条码为：{pcb_sn}")
        else:
            for sn_flag in pcb_entity.yield_board_entity():
                if circle_list.is_exist_item(sn_flag.barcode):
                    pcb_sn = sn_flag.barcode
                    self.log.info(f"获取到的扫到条码为：{pcb_sn}")
                    break

        board_side = pcb_entity.board_side

        # 不上传检测数据
        if data_vo.get_inspect_type() == "inspector":
            self.log.warning(f"检测数据不上传，只上传复判后数据")
            return self.x_response()

        testdt_list = []
        comp_total_number = 0
        comp_robot_ng_number = 0
        comp_repair_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            no = board_entity.board_no
            comp_total_number += board_entity.comp_total_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_repair_ng_number += board_entity.comp_repair_ng_number
            comp_data = []
            robot_ng_code = "0"

            for comp_entity in board_entity.yield_comp_entity():
                if not comp_entity.robot_result:
                    comp_data.append({
                        "mtrl_code": comp_entity.part,  # 物料编码
                        "angle": comp_entity.geometry.angle,  # 角度
                        "md_result": comp_entity.robot_ng_code,  # 机器检测结果代码
                        "mct_result": "FAIL" if comp_entity.is_repair_ng() else "PASS",  # 人工确认检测结果
                        "ng_item_code": comp_entity.repair_ng_code  # 不良代码（测试结果为不良时上传）
                    })
                    # 取最后一个不良元件的机器人检测代码
                    robot_ng_code = comp_entity.robot_ng_code
            testdt_list.append({
                "packaging": board_entity.comp_total_number,  # 总器件数
                "bar_code": barcode,  # 主板条码（拼板条码）
                "lb_code": barcode,  # 标签编码（SN）
                "sub_board": no,  # 子板板号
                "cl_template": "0",  # 元件库模板（默认传0）
                "md_result": robot_ng_code,  # 机器检测结果代码
                "mct_result": board_entity.get_final_result("PASS", "PASS", "FAIL"),  # 人工确认检测结果
                "comp_data": comp_data
            })
        pcb_data = {
            "dev_code": dev_code,  # 设备编码（前端配置项）
            "lb_code": pcb_sn,  # 前面获取条码时扫到的条码
            "wp_code": wp_code,  # 工序编码（前端配置项）
            "os_board": board_side,  # 板面（前端配置项）
            "models_name": models_name,  # 机种名称（前端配置项）
            "test_time": test_time,  # 测试时间
            "begin_time": test_time,  # 检测开始时间
            "end_time": end_time,  # 检测结束时间
            "test_duration": pcb_cycle_time,  # 测试耗时（秒）
            "test_result": pcb_entity.get_robot_result("PASS", "FAIL"),  # 设备测试结果(PASS/FAIL)
            "manually_result": pcb_entity.get_repair_result("PASS", "FAIL"),  # 人工判定结果(PASS/FAIL)
            "result": pcb_entity.get_final_result("PASS", "PASS", "FAIL"),  # 是否整体合格(PASS/FAIL)
            "test_batch": test_batch,  # 测试批次（前端配置项）
            "tn_components": comp_total_number,  # 总元件数
            "mdd_components": comp_robot_ng_number,  # 机器检测不良元件数
            "ndc_components": comp_repair_ng_number,  # 人工确认不良元件数
            "user_code": user_code,  # 操作员工号（前端配置项）
            "pguser_codessss": pguser_codessss,  # 编程员工号（前端配置项）
            "testdt": testdt_list
        }
        pcb_post_data = {
            "docType": "UPLODE_LBPASS",
            "updateType": "UPDATE",
            "data": [pcb_data]
        }
        ret = xrequest.RequestUtil.post_json(upload_api, pcb_post_data)
        if str(ret.get("resultCode")) != "0000":
            return self.x_response("false", f"mes数据上传接口异常, message={ret.get('resultMsg')}")

        lbwp_id = ret.get("resultData", [])[0].get("lbwp_id")
        # 获取整版图
        pcb_images = pcb_entity.list_all_pcb_image_v2()
        image_list = pcb_images if isinstance(pcb_images, list) else [pcb_images]
        for pcb_image in image_list:
            if pcb_image and os.path.exists(pcb_image):
                # 上传整版图
                ret = upload_pcb_image(pcb_image, pcb_sn, lbwp_id, upload_image_api)
                if str(ret.get("resultCode")) != "0000":
                    return self.x_response("false", f"mes接口异常, message={ret.get('resultMsg')}")
            else:
                self.log.warning("没有获取到整版图数据")

        # 上传不良器件图
        for board_tmp in pcb_entity.yield_board_entity():
            for comp_entity in board_tmp.yield_comp_entity():
                if comp_entity.image_path and not comp_entity.robot_result:
                    comp_src_img = comp_entity.image_path
                    if comp_src_img and os.path.exists(comp_src_img):
                        ret = upload_pcb_image(comp_src_img, pcb_sn, lbwp_id, upload_image_api)
                        if str(ret.get("resultCode")) != "0000":
                            return self.x_response("false", f"mes上传图片接口异常, message={ret.get('resultMsg')}")
        return self.x_response()
