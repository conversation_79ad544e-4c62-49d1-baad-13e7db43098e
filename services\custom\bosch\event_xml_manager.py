# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : event_xml_manager.py
# Time       ：2025/02/11 下午15:41
# Author     ：chencb
# version    ：python 3.8
# Description：bosch mes接口请求XML数据模版以及参数
"""
import copy
from enum import Enum
from xml.etree.ElementTree import Element
from xml.etree import ElementTree
from common import xcons
from common.xcons import REPAIR
from entity.MesEntity import PcbEntity
from vo.mes_vo import OtherVo
from common.xutil import log, XmlUtil, DateUtil


# 所有MES事件枚举
class MesEvent(Enum):
    # 9.1.1 Change in set-up state
    PLC_OPERATION_MODE_CHANGED = "plcOperationModeChanged",  # 和客户沟通我们不做支持
    # 9.1.2 System error
    PLC_ERROR = "plcError",
    # 9.1.5 The present system status ->"part missing"
    PLC_PARTS_MISSING_STARTED = "plcPartsMissingStarted",
    PLC_PARTS_MISSING = "plcPartsMissing",
    # 9.1.6 The present system status ->"jam"
    PLC_JAM_STARTED = "plcJamStarted",
    PLC_JAM = "plcJam",
    # 9.1.3和9.1.4事件名一样，发送mes时是通过eventSwitch识别事件，把eventSwitch值添加到尾部作为新的事件名
    # 9.1.3 The present set-up status , eventSwitch=10
    DATA_UPLOAD_REQUIRED_10 = "dataUploadRequired_10",
    # 9.1.4 current requipment identification data，eventSwitch=105
    DATA_UPLOAD_REQUIRED_105 = "dataUploadRequired_105",
    # 9.2.1 Set-up request from the system
    PLC_CHANGE_OVER_STARTED = "plcChangeOverStarted",
    # 9.2.2 Set-up request change over parameters
    DATA_DOWNLOAD_REQUIRED = "dataDownloadRequired",
    # 9.2.3 Set-up acknowledgement
    PLC_CHANGE_OVER = "plcChangeOver",
    # 9.3.1 Processing release
    PART_RECEIVED = "partReceived",
    # 9.3.2 Start of processing AOI_THT
    PART_PROCESSING_STARTED = "partProcessingStarted",
    # 9.3.3 End of AOI_THT processing
    PART_PROCESSED = "partProcessed",
    # 9.3.4 Processing aborted
    PART_PROCESSING_ABORTED = "partProcessingAborted"


def event_response(code: str = "0", msg: str = ""):
    """
    统一定义一个mes事件响应数据的格式，供和上层接口间数据传递
    return:
    {
    # 0，成功，非0，异常
    “code”:'0';
    # 0时为空，非0时显示异常信息，包括result和trace属性里的信息
    'msg':''
    }
    """
    return {'code': code, 'msg': msg}


class EventXmlManager:
    # 初始化event_id计数器
    event_id_counter = 1

    # 每个事件需要上层单独赋值的参数统一在这里
    event_params = {
        MesEvent.PLC_ERROR: {
            'errorNo': '',
            'errorText': '',
            'errorType': '',
            'errorState': '',
        },
        MesEvent.DATA_UPLOAD_REQUIRED_10: {
            'eventSwitch': '10',
            'changeFlag': '',
            'jam': '',
            'partMissing': '',
            'errorNo': '',
            'errorText': '',
        },
        MesEvent.DATA_UPLOAD_REQUIRED_105: {
            'eventSwitch': '105',
            # requipment identification data
            'machineDescription': '',
            'machineSupplier': '',
            'machineType': '',
            'machineProductionYear': '',
            'serialNumber': '',
            'machineSystemSoftware': '',
            'softwareServicePack': '',
            'plant': '',
            'machineDokuNo': '',
            'machineSoftwareVersion': '',
            'interfaceSoftwareVersionNo': '',
            'xmlVersion': '',
            'machineTime': '',
            'machineNumberOfProductionArea': '',
        },
        MesEvent.DATA_DOWNLOAD_REQUIRED: {
            'eventSwitch': '1',
        },
        MesEvent.PART_RECEIVED: {
            'identifier': '',
        },
        MesEvent.PART_PROCESSING_STARTED: {
            'identifier': '',
        },
        MesEvent.PART_PROCESSED: {
            'eventSwitch': '',
            'identifier': '',
            'result': '-1',
            'nioBits': "0",
            # 发送此事件时需要带两个参数Prog1.Name和WorkPart.FailCount，如果使用这个带点的key作为模版参数，template.format会报错
            # 做了如下替换，所以在模版替换参数需要改名为Prog1_Name和WorkPart_FailCount
            # <item name="Prog1.Name" value="{{Prog1_Name}}" dataType="8"/>
            # <item name="WorkPart.FailCount" value="{{WorkPart_FailCount}}" dataType="3"/>
            'Prog1_Name': '',
            'WorkPart_FailCount': '',
            'array_results': '',
            'array_AOI': '',
            'array_matData': '',
            'array_comp': '',
            'array_statisticData': '',
        },
        MesEvent.PART_PROCESSING_ABORTED: {
            'identifier': '',
        },
    }

    # 事件是否等待响应列表
    events_is_wait_response = {
        MesEvent.PLC_OPERATION_MODE_CHANGED: False,
        MesEvent.PLC_ERROR: False,
        MesEvent.DATA_UPLOAD_REQUIRED_10: False,
        MesEvent.DATA_UPLOAD_REQUIRED_105: False,
        MesEvent.PLC_PARTS_MISSING_STARTED: False,
        MesEvent.PLC_PARTS_MISSING: False,
        MesEvent.PLC_JAM_STARTED: False,
        MesEvent.PLC_JAM: False,
        MesEvent.PLC_CHANGE_OVER: False,
        MesEvent.PART_PROCESSING_STARTED: False,
        MesEvent.PART_PROCESSING_ABORTED: False,
        MesEvent.PLC_CHANGE_OVER_STARTED: True,
        MesEvent.DATA_DOWNLOAD_REQUIRED: True,
        MesEvent.PART_RECEIVED: True,
        MesEvent.PART_PROCESSED: True,
    }

    # xml公共header和location统一抽取出来维护
    header_attrs = 'eventId="{eventId}" version="{version}" contentType="3" timeStamp="{timeStamp}"'
    location_attrs = 'lineNo="{lineNo}" statNo="{statNo}" statIdx="{statIdx}" fuNo="{fuNo}" workPos="{workPos}" toolPos="{toolPos}" ' \
                     'processNo="{processNo}" processName="{processName}" application="IPC"'

    '''
    以下模版中，由于使用了f'',变量替换更改为使用{{}}，解释如下：
    如果想要在f的字符串中包含花括号本身，需对花括号进行转义，两个连续的花括号 {{ 或 }} 会被解释为单个花括号字符。
    另外标签与标签之间不要有空格，以及标签之间进行换行，因为发送数据前会剔除换行
    '''
    plc_error_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcError" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<plcError typeNo="{{typeNo}}" typeVar="{{typeVar}}" errorNo="{{errorNo}}" errorText="{{errorText}}" errorType="{{errorType}}" 
errorState="{{errorState}}" operationMode="{{operationMode}}" modeOn="{{modeOn}}" chainNo="999" />
</event>
<body />
</root>'''
    data_upload_required_10_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="dataUploadRequired" eventSwitch="{{eventSwitch}}" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<dataUploadRequired />
</event>
<body>
<items>
<item name="modeOn" value="{{modeOn}}" dataType="11" />
<item name="operationMode" value="{{operationMode}}" dataType="2" />
<item name="typeNo" value="{{typeNo}}" dataType="8" />
<item name="typeVar" value="{{typeVar}}" dataType="8" />
<item name="batch" value="{{batch}}" dataType="8" />
<item name="changeFlag" value="{{changeFlag}}" dataType="11" />
<item name="jam" value="{{jam}}" dataType="11" />
<item name="partMissing" value="{{partMissing}}" dataType="11" />
<item name="errorNo" value="{{errorNo}}" dataType="2" />
<item name="errorText" value="{{errorText}}" dataType="8" />
</items>
</body>
</root>'''
    data_upload_required_105_template = f'''<root>
<header eventName="dataUploadRequired" eventSwitch="{{eventSwitch}}" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<dataUploadRequired />
</event>
<body>
<items>
<item name="machineDescription" value="{{machineDescription}}" dataType="8" />
<item name="machineSupplier" value="{{machineSupplier}}" dataType="8" />
<item name="machineType" value="{{machineType}}" dataType="8" />
<item name="machineProductionYear" value="{{machineProductionYear}}" dataType="3" />
<item name="serialNumber" value="{{serialNumber}}" dataType="8" />
<item name="machineSystemSoftware" value="{{machineSystemSoftware}}" dataType="8" />
<item name="softwareServicePack" value="{{softwareServicePack}}" dataType="8" />
<item name="plant" value="{{plant}}" dataType="8" />
<item name="machineDokuNo" value="{{machineDokuNo}}" dataType="8" />
<item name="machineSoftwareVersion" value="{{machineSoftwareVersion}}" dataType="8" />
<item name="interfaceSoftwareVersionNo" value="{{interfaceSoftwareVersionNo}}" dataType="8" />
<item name="xmlVersion" value="{{xmlVersion}}" dataType="8" />
<item name="machineTime" value="{{machineTime}}" dataType="8" />
<item name="machineNumberOfProductionArea" value="{{machineNumberOfProductionArea}}" dataType="3" />
</items>
</body>
</root>'''
    plc_parts_missing_started_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcPartsMissingStarted" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<plcPartsMissingStarted missingParts="1" typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body/>
</root>'''
    plc_parts_missing_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcPartsMissing" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<plcPartsMissing missingParts="1" typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body/>
</root>'''
    plc_jam_started_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcJamStarted" eventSwitch="-1" {header_attrs} >
<location {location_attrs} />
</header>
<event>
<plcJamStarted typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body/>
</root>'''
    plc_jam_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcJam" eventSwitch="-1" {header_attrs} >
<location {location_attrs} />
</header>
<event>
<plcJam typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body/>
</root>'''
    plc_change_over_started_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcChangeOverStarted" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<plcChangeOverStarted typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body />
</root>'''
    data_download_required_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="dataDownloadRequired" eventSwitch="{{eventSwitch}}" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<dataDownloadRequired />
</event>
<body>
<items>
<item name="TypeNo" value="{{typeNo}}" dataType="8" />
<item name="TypeVar" value="{{typeVar}}" dataType="8" />
</items>
</body>
</root>'''
    plc_change_over_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="plcChangeOver" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<plcChangeOver typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body />
</root>'''
    part_received_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="partReceived" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<partReceived identifier="{{identifier}}" typeNo="{{typeNo}}" typeVar="{{typeVar}}" />
</event>
<body />
</root>'''
    part_processing_started_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="partProcessingStarted" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<partProcessingStarted identifier="{{identifier}}" />
</event>
<body />
</root>'''
    array_results_template = '''<array name="results">
<structDef>
<item name="pos" dataType="3" />
<item name="result" dataType="3" />
<item name="nioBits" dataType="3" />
<item name="identifier" dataType="8" />
</structDef>
<values>
{}
</values>
</array>'''
    array_results_item_template = '<item pos="{pos}" result="{result}" nioBits="0" identifier="{identifier}" />'
    array_aoi_template = '''<array name="AOI">
<structDef>
<item name="posNo" dataType="8" />
<item name="pos" dataType="3" />
<item name="package" dataType="8" />
<item name="typeNo" dataType="8" />
<item name="windowNo" dataType="3" />
<item name="pinNo" dataType="8" />
<item name="checkType" dataType="8" />
<item name="checkValue" dataType="3" />
<item name="checkFeature" dataType="3" />
<item name="checkAllFeature" dataType="8" />
<item name="errorCode" dataType="3" />
<item name="ack" dataType="3" />
<item name="scrapAck" dataType="3" />
<item name="systemName" dataType="8" />
<item name="errorImage" dataType="8" />
<item name="errorText" dataType="8" />
<item name="imageNo" dataType="8" />
<item name="errorXPosImage" dataType="3" />
<item name="errorYPosImage" dataType="3" />
<item name="errorXSize" dataType="3" />
<item name="errorYSize" dataType="3" />
<item name="uXPosImage" dataType="3" />
<item name="uYPosImage" dataType="3" />
</structDef>
<values>
{}
</values>
</array>'''
    array_aoi_item_template = '<item posNo="{posNo}" pos="{pos}" package="{package}" typeNo="{typeNo}" windowNo="{windowNo}" ' \
                              'pinNo="{pinNo}" checkType="{checkType}" checkValue="{checkValue}" checkFeature="{checkFeature}" ' \
                              'checkAllFeature="{checkAllFeature}" errorCode="{errorCode}" ack="{ack}" scrapAck="{scrapAck}" ' \
                              'systemName="{systemName}" errorImage="{errorImage}" errorText="{errorText}" imageNo="{imageNo}" ' \
                              'errorXPosImage="{errorXPosImage}" errorYPosImage="{errorYPosImage}" errorXSize="{errorXSize}" ' \
                              'errorYSize="{errorYSize}" uXPosImage="{uXPosImage}" uYPosImage="{uYPosImage}" />'
    array_mat_data_template = '''<array name="matData">
<structDef>
<item name="idx" dataType="3" />
<item name="identifier" dataType="8" />
</structDef>
<values>
{}
</values>
</array>'''
    array_mat_data_item_template = '<item idx="{idx}" identifier="{identifier}" />'
    array_comp_template = '''<array name="comp">
<structDef>
<item name="pos" dataType="3" />
<item name="ref" dataType="3" />
<item name="refDes" dataType="8" />
</structDef>
<values>
{}
</values>
</array>'''
    array_comp_item_template = '<item pos="{pos}" ref="{ref}" refDes="{refDes}" />'
    array_statistic_data_template = '''<array name="statisticData">
<structDef>
<item name="pos" dataType="3" />
<item name="countComponent" dataType="3" />
<item name="countTermination" dataType="3" />
<item name="errorCountComponent" dataType="3" />
<item name="passCountComponent" dataType="3" />
<item name="errorCountTermination" dataType="3" />
<item name="passCountTermination" dataType="3" />
</structDef>
<values>
{}
</values>
</array>'''
    array_group_template = f'''<structArrays>
{{array_results}}
{{array_AOI}}
{{array_matData}}
{{array_comp}}
{{array_statisticData}}
</structArrays>'''
    array_one_part_template = f'''{{array_results}}
{{array_AOI}}
{{array_matData}}
{{array_comp}}
{{array_statisticData}}'''
    array_statistic_data_item_template = '<item pos="{pos}" countComponent="{countComponent}" ' \
                                         'countTermination="{countTermination}" errorCountComponent="{errorCountComponent}" ' \
                                         'passCountComponent="{passCountComponent}" errorCountTermination="{errorCountTermination}" ' \
                                         'passCountTermination="{passCountTermination}" />'
    part_processed_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="partProcessed" eventSwitch="{{eventSwitch}}" {header_attrs} >
<location {location_attrs} />
</header>
<event>
<partProcessed identifier="{{identifier}}" />
</event>
<body>
<structs>
<resHead result="{{result}}" typeNo="{{typeNo}}" typeVar="{{typeVar}}" workingCode="{{workingCode}}" 
nioBits="0" machineID="{{machineID}}" batch="{{batch}}" />
</structs>
<items>
<item name="Prog1.Name" value="{{Prog1_Name}}" dataType="8"/>
<item name="WorkPart.FailCount" value="{{WorkPart_FailCount}}" dataType="3"/>
</items>
{{arrays}}
</body>
</root>'''
    part_processing_aborted_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<root>
<header eventName="partProcessingAborted" eventSwitch="-1" {header_attrs}>
<location {location_attrs} />
</header>
<event>
<partProcessingAborted identifier="{{identifier}}" />
</event>
<body>
<structs>
<resHead result="3" typeNo="{{typeNo}}" typeVar="{{typeVar}}" workingCode="{{workingCode}}" 
nioBits="0" machineID="{{machineID}}" batch="{{batch}}" />
</structs>
</body>
</root>'''

    def __init__(self, inspect_type):
        self.inspect_type = inspect_type
        # 缓存整板条码
        self.pcb_sn_cache = ''
        self.part_received_return_code = ''
        # 请求中公共的参数统一在这里
        self.public_params = {
            # header部分
            'eventId': '1',
            'version': '2.3',  # 对应xmlVersion的拷贝
            'timeStamp': '',
            # location部分
            'lineNo': '',
            'statNo': '',
            'statIdx': '',
            'fuNo': '',
            'workPos': '',
            'toolPos': '',
            'processNo': '',
            'processName': '',
            'machineID': '',
            # 由于我们不支持plcOperationModeChanged事件，下面下个参数会直接hardcode默认值
            'operationMode': '1',  # 1(automatic)
            'modeOn': 'true',  # true(activated)

            # ----------- 以下是mes系统返回的数据  --------------
            # partReceived事件返回workPart里的值
            'workPart': {
                'typeNo': '1111111111',  # typeNo和typeVar在mes没有给到之前使用文档给的默认值
                'typeVar': '0000',  # 文档是0001,和客户电话沟通时说改为0000
                'identifier': '',
                'partForStation': '',
                'changeOver': '',
                'passThrough': '',
                'nextProcessNo': '',
                'workingCode': '',
                'batch': '',
            },
            # partReceived事件返回workItems里的值，每个item为：{'pos':'1','id':'12345678','partForStation':'true'}
            'workItems': [],
            # dataDownloadRequired、plcChangeOverStarted事件返回items里的值
            'WorkPart.PartsPerCarrier': '',
            'Prog1.Name': '',
            # partReceived事件返回items里的值
            'ProcessState': '',
            'LocationState': '',
        }

    def set_pcb_sn(self, sn):
        self.pcb_sn_cache = sn

    def get_pcb_sn(self):
        return self.pcb_sn_cache

    def _generate_event_id(self):
        # 计数器超出范围时从头开始计数，当前默认最大支持到5位数
        if self.event_id_counter > 99999:
            self.event_id_counter = 1
        event_id = self.event_id_counter
        self.event_id_counter += 1
        return event_id

    def update_public_params(self, other_vo: OtherVo):
        """
        更新配置参数值，从other_vo中获取需要更新的值，每次发送事件请求前都更新一次
        """
        # 维修站单独参数
        if self.inspect_type == REPAIR:
            work_pos = other_vo.get_value_by_cons_key("workPos_vi")
            process_name = other_vo.get_value_by_cons_key("processName_vi")
        else:
            work_pos = other_vo.get_value_by_cons_key("workPos")
            process_name = other_vo.get_value_by_cons_key("processName")

        self.public_params.update({
            'version': other_vo.get_value_by_cons_key("xmlVersion"),
            'lineNo': other_vo.get_value_by_cons_key("lineNo"),
            'statNo': other_vo.get_value_by_cons_key("statNo"),
            'statIdx': other_vo.get_value_by_cons_key("statIdx"),
            'fuNo': other_vo.get_value_by_cons_key("fuNo"),
            'workPos': work_pos,
            'toolPos': other_vo.get_value_by_cons_key("toolPos"),
            'processNo': other_vo.get_value_by_cons_key("processNo"),
            'processName': process_name,
            'machineID': other_vo.get_value_by_cons_key("machineID"),
        })

    def clear_public_params(self):
        # 清空上个板mes返回的相关数据
        # 先清空外层值
        clear_param = {
            'workItems': [],
            'WorkPart.PartsPerCarrier': '',
            'ProcessState': '',
            'LocationState': '',
        }
        self.public_params.update(clear_param)

        # 再清空workPart,其中typeNo和typeVar重新发送PART_RECEIVED要用不能清空
        work_part = self.public_params.get('workPart', {})
        part_for_station = work_part.get('partForStation')
        clear_param = {
            'identifier': '',
            'partForStation': '',
            'changeOver': '',
            'passThrough': '',
            'nextProcessNo': '',
            'workingCode': '',
            'batch': '',
        }
        # 如果上一块板返回了partForStation=false，则清空typeNo
        # 确保下一块检测板能正常走MES获取板式名，不至于如果typeNo刚好和那块板一样时，导致板式名获取错误
        if part_for_station == 'false':
            clear_param['typeNo'] = '1111111111'
            clear_param['typeVar'] = '0000'
        work_part.update(clear_param)

        self.pcb_sn_cache = ''
        self.part_received_return_code = ''

    def get_public_param(self, key: str):
        return self.public_params.get(key, {})

    def _format_template(self, template: str, event_param: dict):
        # 嵌套字典无法正常替换参数，替换参数前先把workPart里的值先扁平化出来
        work_part = self.public_params['workPart']
        flat_param = copy.copy(self.public_params)
        flat_param.pop('workPart')
        flat_param.update(work_part)
        # 如果公共参数和event_param都有相同的key值（比如identifier）会优先使用event_param的值
        flat_param.update(event_param)
        result = template.format(**flat_param)
        return result

    def is_wait_response(self, event: MesEvent):
        return self.events_is_wait_response.get(event, False)

    def get_array_xml(self, name: str, item_list: list):
        """
        根据对应的key，生成对应的array xml数据给外层组装请求数据
        """
        if name == 'results':
            item_template = self.array_results_item_template
            array_template = self.array_results_template
        elif name == 'AOI':
            item_template = self.array_aoi_item_template
            array_template = self.array_aoi_template
        elif name == 'statisticData':
            item_template = self.array_statistic_data_item_template
            array_template = self.array_statistic_data_template
        elif name == 'matData':
            item_template = self.array_mat_data_item_template
            array_template = self.array_mat_data_template
        elif name == 'comp':
            item_template = self.array_comp_item_template
            array_template = self.array_comp_template
        else:
            return ''

        item_strings = []
        for item in item_list:
            item_str = item_template.format(**item)
            item_strings.append(item_str)
        items = "\n".join(item_strings)
        array_xml = array_template.format(items)
        return array_xml

    def get_arrays_xml(self, name: str, arrays: dict):
        if name == 'group':
            arrays_template = self.array_group_template
        elif name == 'part':
            arrays_template = self.array_one_part_template
        else:
            return ''

        arrays_xml = arrays_template.format(**arrays)
        return arrays_xml

    def get_event_xml(self, event: MesEvent, event_data: dict = None):
        """
        根据事件类型返回对应要发送的xml数据，如果event_data有值，需要对对应参数进行替换
        """
        template = ''
        if event == MesEvent.PLC_ERROR:
            template = self.plc_error_template
        elif event == MesEvent.DATA_UPLOAD_REQUIRED_10:
            template = self.data_upload_required_10_template
        elif event == MesEvent.DATA_UPLOAD_REQUIRED_105:
            template = self.data_upload_required_105_template
        elif event == MesEvent.PLC_PARTS_MISSING_STARTED:
            template = self.plc_parts_missing_started_template
        elif event == MesEvent.PLC_PARTS_MISSING:
            template = self.plc_parts_missing_template
        elif event == MesEvent.PLC_JAM_STARTED:
            template = self.plc_jam_started_template
        elif event == MesEvent.PLC_JAM:
            template = self.plc_jam_template
        elif event == MesEvent.PLC_CHANGE_OVER:
            template = self.plc_change_over_template
        elif event == MesEvent.PLC_CHANGE_OVER_STARTED:
            template = self.plc_change_over_started_template
        elif event == MesEvent.DATA_DOWNLOAD_REQUIRED:
            template = self.data_download_required_template
        elif event == MesEvent.PART_RECEIVED:
            template = self.part_received_template
        elif event == MesEvent.PART_PROCESSING_STARTED:
            template = self.part_processing_started_template
        elif event == MesEvent.PART_PROCESSED:
            template = self.part_processed_template
        elif event == MesEvent.PART_PROCESSING_ABORTED:
            template = self.part_processing_aborted_template
        else:
            log.info(f"event type:{event} not support")

        if template:
            event_param = self.event_params.get(event, {})
            # 有这个事件独有的参数时，会先更新替换新的参数：
            if event_data:
                event_param.update(event_data)

            # 每次发送事件前生成哥唯一eventId和当前时间
            self.public_params['eventId'] = self._generate_event_id()
            time_stamp = DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT8)
            self.public_params['timeStamp'] = time_stamp
            event_xml = self._format_template(template, event_param)
            # bosch的mes的xml要求必须是紧凑，标签与标签间无空格和换行
            event_xml = event_xml.replace('\n', '')
        else:
            event_xml = ''
        return event_xml

    def _parse_err_msg(self, root: Element):
        """
        所有响应的xml里returnCode为非0时定义的错误信息都是一样，统一在这个函数里处理错误信息的组装
        """
        header_element = root.find('header')
        event_name = header_element.get('eventName')
        result_element = root.find('event/result')
        error_msg = f'{event_name} '
        if result_element is not None:
            result_str = ElementTree.tostring(result_element, encoding='unicode')
            error_msg += f'result info:{result_str}'

        trace_element = root.find('event/trace/trace')
        if trace_element is not None:
            trace_str = ElementTree.tostring(trace_element, encoding='unicode')
            error_msg += f'trace info:{trace_str}'

        return error_msg

    def _parse_setup_request_resp(self, xml_data: str):
        """
        解析plcChangeOverStarted和dataDownloadRequired事件mes系统返回的响应数据
        return: event_response
        #####################################################
        1.报文主要内容：
         <root>
          <event>
            <result returnCode="0">
            </result>
            <trace />
            或者：
          <result returnCode="-2"> MES detected a warning </result>
          <trace>
            <trace level="warning" code="12333" text="Warning level reached" source="RMS" />
          </trace>
          </event>
          <body>
            <items>
              <item name="WorkPart.PartsPerCarrier" value="10" dataType="3" />
              <item name="Prog1.Name" value="0285020781" dataType="8" />
            </items>
          </body>
        </root>

        2.参数说明
        returnCode 0:成功，非0：失败，需要返回AOI前端展示
        WorkPart.PartsPerCarrier、Prog1.Name，保存到public_params
        """
        root = XmlUtil.get_xml_root_by_str(xml_data)
        result_element = root.find('event/result')
        return_code = result_element.get('returnCode')
        if return_code == '0':  # 成功
            msg = ''
            # 保存items中返回的参数
            items_element = root.find('.//items')
            if items_element is not None:
                item_list = items_element.findall('item')
                for item in item_list:
                    item_data = {item.get('name'): item.get('value')}
                    self.public_params.update(item_data)
        else:  # 其它非0异常数据
            msg = self._parse_err_msg(root)

        return event_response(code=return_code, msg=msg)

    def _parse_part_received_resp(self, xml_data: str):
        """
        解析partReceived事件mes系统返回的响应数据
        return: event_response
        #############################################
        报文主要内容：
            <root>
              <event>
                <result returnCode="0"></result>
                <trace />
              </event>
              <body>
                <structs>
                  <workPart partForStation="true" changeOver="false" workingCode="0" typeNo="0285021028" typeVar="0000"
                    identifier="xxx" passThrough="true" nextProcessNo="925" />
                </structs>
                <items>
                    <item name="ProcessState" value="0" dataType="2" />
                    <item name="LocationState" value="2" dataType="2" />
                </items>
                <structArrays>
                  <array name="workItems">
                    <structDef>
                      <item name="pos" dataType="3" />
                      <item name="id" dataType="8" />
                      <item name="partForStation" dataType="11" />
                    </structDef>
                    <values>
                      <item pos="1" id="68077000068810012501070285021028" partForStation="true" />
                      ...
                    </values>
                  </array>
                </structArrays>
              </body>
            </root>
        参数说明：
        workPart：返回的后续请求需要使用的参数，保存到public_params
        ProcessState和LocationState：用于标记a process or a location is lock
        workItems：返回的多板条码，trace_type配置为Group发送时才有，如果是one part是没有这个列表的
        """
        root = XmlUtil.get_xml_root_by_str(xml_data)
        result_element = root.find('event/result')
        return_code = result_element.get('returnCode')

        if return_code in ['0', '-1', '-2']:  # 这几个code返回的格式差不多，只是值上的一些差异，统一处理
            # 保存workPart中返回的参数
            work_part_element = root.find('.//workPart')
            work_part_false_msg = ''
            if work_part_element is not None:
                self.public_params.update({
                    'workPart': work_part_element.attrib
                })
                # 如果partForStation为false，把信息传递给外层统一发给主软件停机显示
                if work_part_element.attrib.get('partForStation') == 'false':
                    work_part_false_msg = ElementTree.tostring(work_part_element, encoding='unicode')

            # 保存items中返回的参数（ProcessState和LocationState）
            items_element = root.find('.//items')
            if items_element is not None:
                item_list = items_element.findall('item')
                for item in item_list:
                    self.public_params.update({item.get('name'): item.get('value')})

            # 保存workItems中返回的信息，Group时才有，one part是返回空
            work_items_array_element = root.find('.//array[@name="workItems"]')
            if work_items_array_element is not None:
                item_list = work_items_array_element.findall('values/item')
                work_items = []
                for item in item_list:
                    # 直接缓存到公共参数中使用
                    work_items.append(item.attrib)

                # 把子板的信息缓存在公共参数中，后续发送mes数据时需要使用
                self.public_params.update({'workItems': work_items})

            msg = ''
            if return_code == '0':
                # 存在partForStation=false场景，msg添加partForStation=false的信息
                if work_part_false_msg:
                    msg = work_part_false_msg
            else:  # -1 和 -2 时把workPart和items信息也呈现出来
                msg = self._parse_err_msg(root)
                # 把workPart和items信息也呈现出来
                if work_part_element is not None:
                    work_part_str = ElementTree.tostring(work_part_element, encoding='unicode')
                    msg += '\n' + work_part_str
                if items_element is not None:
                    items_str = ElementTree.tostring(items_element, encoding='unicode')
                    msg += '\n' + items_str
        else:  # 其它异常数据,比如>0
            msg = self._parse_err_msg(root)

        self.part_received_return_code = return_code
        return event_response(code=return_code, msg=msg)

    def _parse_part_processed_resp(self, xml_data: str):
        """
        解析partProcessed事件mes系统返回的响应数据
        return: event_response
        #####################################################
        1.报文主要内容：
        <root>
          <event>
            <result returnCode="0">
            </result>
            <trace />
          </event>
          <body />
        </root>
        """
        root = XmlUtil.get_xml_root_by_str(xml_data)
        result_element = root.find('event/result')
        return_code = result_element.get('returnCode')
        if return_code == '0':  # 成功
            msg = ''
        else:  # 其它非0异常数据
            msg = self._parse_err_msg(root)

        return event_response(code=return_code, msg=msg)

    def parse_response_xml(self, event: MesEvent, xml_data: str):
        """
        根据事件类型解析mes系统返回的响应数据
        """
        # 这两个事件都是Set-up request中的，返回xml内容格式一样
        if event in [MesEvent.PLC_CHANGE_OVER_STARTED, MesEvent.DATA_DOWNLOAD_REQUIRED]:
            ret = self._parse_setup_request_resp(xml_data)
        elif event == MesEvent.PART_RECEIVED:
            ret = self._parse_part_received_resp(xml_data)
        elif event == MesEvent.PART_PROCESSED:
            ret = self._parse_part_processed_resp(xml_data)
        else:
            log.info(f'not support resp parse event：{event}')
            ret = event_response()

        return ret

    def is_allowed_send_data_to_mes(self, pcb_entity: PcbEntity):
        """
        当mes返回错误、partForStation=false，拼板数量或者板式名不一致等，不允许再发送数据给mes，否则会出现板报废等情况发生
        """
        if self.part_received_return_code != '0':
            # MES返回报错信息或者未请求过partRecived，板有异常，不再发送数据
            err_msg = 'partRecived请求MES返回过此板异常（returnCode!=0），不再发送结果数据给MES！'
            return False, err_msg

        # 板已过站也不再发送数据
        work_part = self.get_public_param('workPart')
        part_for_station = work_part.get('partForStation')

        # 如果发送的数据的拼板数量和MES返回的拼板数量不一致，说明数据异常了，不能发送给MES
        work_items = self.get_public_param('workItems')
        mes_ret_board_cnt = len(work_items)

        # 如果发送的板式和MES返回的板式不一致，也不发送数据
        program_name = self.get_public_param('Prog1.Name') + '.001'

        if part_for_station == 'false':
            err_msg = f'partRecived请求MES返回过此板已过站(partForStation=false)，不再发送结果数据给MES！'
            return False, err_msg
        elif mes_ret_board_cnt != pcb_entity.board_count:
            err_msg = f'发送数据的拼板数量【{pcb_entity.board_count}】和MES返回的拼板数量【{mes_ret_board_cnt}】不一致，数据异常不再发送结果数据给MES！'
            return False, err_msg
        elif program_name != pcb_entity.project_name:
            err_msg = f'发送数据的板式名【{pcb_entity.project_name}】和MES返回的板式名【{program_name}】不一致，数据异常不再发送结果数据给MES！'
            return False, err_msg
        else:
            return True, ''
