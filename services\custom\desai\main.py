# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/10/12 上午10:09
# Author     ：sch
# version    ：python 3.8
# Description：德赛
"""
import hashlib
import time
from typing import Any

from common import xutil, xrequest, xcons
from vo.mes_vo import DataVo, ButtonVo, ComboVo, OtherVo
from engine.MesEngine import BaseEngine

global_data = {}


def x_get_sign_and_time(app_key: str, app_secret: str):
    """
    获取本次上传的sign和时间戳
    return: (app_key, sign, timestamp)
    """
    now = str(int(time.time()))

    content = f"{app_key}{app_secret}{now}"
    sign_ret = hashlib.md5(content.encode(encoding="utf8")).hexdigest().lower()

    return sign_ret, now


class Engine(BaseEngine):
    version = {
        "title": "desai release v1.0.0.1",
        "device": "430",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-10-12 11:07  init
""", }

    combo = {
        "workstation": {
            "ui_name": "车间",
            "item": [],
            "value": ""
        },
        "line": {
            "ui_name": "线体编码",
            "item": [],
            "value": ""
        },
        "order": {
            "ui_name": "工作中心",
            "item": [],
            "value": ""
        },
    }

    form = {

    }

    other_form = {
        "app_key": {
            "ui_name": "appkey",
            "value": "cecd9cf711d44007823c579fbfbe9fbb"
        },
        "app_secret": {
            "ui_name": "appsecret",
            "value": "cnNEQcSb"
        },
        "lan_wei_api_url": {
            "ui_name": "蓝微Mes接口URL",
            "value": "http://127.0.0.1:8081"
        },
    }

    button = {
        "refresh_workstation": {
            "ui_name": "获取工作中心",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        sn_list = other_vo.list_sn()
        lan_wei_api_url = other_vo.get_value_by_cons_key("lan_wei_api_url")
        app_key = other_vo.get_value_by_cons_key("app_key")
        app_secret = other_vo.get_value_by_cons_key("app_secret")

        workstation = other_vo.get_value_by_cons_key("workstation")
        line = other_vo.get_value_by_cons_key("line")
        order = other_vo.get_value_by_cons_key("order")

        room_map = xutil.CacheUtil.get("room_map")
        work_id = room_map.get(workstation, {}).get("line_map", {}).get(line, {}).get(
            "work_map", {}).get(order, {}).get("work_id", "")
        print(f"work id: {work_id}")

        pc_list = []
        for ix, sn in enumerate(sn_list):
            ix += 1
            pc_list.append({
                "seq": str(ix),
                "barcode": sn
            })

        check_url = f"{lan_wei_api_url}/mc/http/interface.ms?model=DeviceApi&method=CheckSN"
        sign, timestamp = x_get_sign_and_time(app_key, app_secret)

        check_param = {
            "appkey": app_key,
            "timestamp": timestamp,
            "sign": sign,
            "workStationCode": work_id,
            "verifyType": "PANEL",
            "pcList": pc_list
        }

        ret = xrequest.RequestUtil.post_json(check_url, check_param)

        if ret.get("result") != "OK":
            return self.x_response("false", f"接口响应异常，条码校验失败，error：{ret.get('message')}")

        # 缓存事务码
        event_code = ret.get("eventCode")
        self.log.info(f"缓存事务码：{event_code}")
        global_data["event_code"] = event_code

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_no = board_entity.board_no
            barcode = board_entity.barcode

            comp_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_list.append({
                    "seq": board_no,
                    "component": board_no,
                })

            board_list.append({
                "seq": board_no,
                "barcode": barcode,
                "status": board_entity.get_final_result("OK", "PASS", "NG"),
                "dataList": comp_list
            })

        pcb_data = {
            "seq": "1",
            "barcode": pcb_entity.pcb_barcode,
            "version": "",
            "programName": project_name,
            "testTime": start_time,
            "status": "",
            "badMark": "",
        }

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        # workstation = btn_dao.get_value_by_cons_key("workstation")
        lan_wei_api_url = btn_vo.get_value_by_cons_key("lan_wei_api_url")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "refresh_workstation":
            get_workstation_url = f"{lan_wei_api_url}/mc/http/interface.ms?model=DeviceApi&method=GetWorkstation"

            line = btn_vo.get_value_by_cons_key("line")
            app_key = btn_vo.get_value_by_cons_key("app_key")
            app_secret = btn_vo.get_value_by_cons_key("app_secret")

            sign, timestamp = x_get_sign_and_time(app_key, app_secret)

            param1 = {
                "appkey": app_key,
                "timestamp": timestamp,
                "sign": sign,
                "lineCode": line
            }

            ret1 = xrequest.RequestUtil.post_json(get_workstation_url, param1)
            if ret1.get("result") != "OK":
                return self.x_response("false", f"接口响应异常，获取工作中心失败，error：{ret1.get('message')}")

            room_list = ret1.get("roomList")

            if not room_list:
                return self.x_response("false", f"未获取到工作中心！")

            room_map = {}
            for item in room_list:

                line_map = {}
                for line_item in item.get("lineList", []):

                    work_map = {}
                    for work_item in line_item.get("workList", []):
                        work_id = work_item.get("workStationCode")
                        work_name = work_item.get("workStationName")
                        work_map[work_name] = {
                            "work_id": work_id
                        }

                    line_code = line_item.get("lineCode")
                    line_name = line_item.get("lineName")
                    line_map[line_name] = {
                        "line_code": line_code,
                        "work_map": work_map,
                    }

                room_code = item.get("roomCode")
                room_name = item.get("roomName")
                room_map[room_name] = {
                    "room_code": room_code,
                    "line_map": line_map
                }

            combo_workstation = getattr(other_param, "combo_workstation")
            combo_line = getattr(other_param, "combo_line")
            combo_order = getattr(other_param, "combo_order")

            combo_workstation.clear()
            combo_line.clear()
            combo_order.clear()

            room_list = []
            line_list = []
            work_list = []

            the_first_1 = True
            for room_name, line_obj in room_map.items():
                combo_workstation.addItem(room_name)
                room_list.append(room_name)

                if the_first_1:
                    the_first_1 = False
                    line_map = line_obj.get("line_map", {})

                    the_first_2 = True
                    for line_name, work_obj in line_map.items():
                        combo_line.addItem(line_name)
                        line_list.append(line_name)

                        if the_first_2:
                            the_first_2 = False
                            work_map = work_obj.get("work_map", {})

                            for work_name, _ in work_map.items():
                                combo_order.addItem(work_name)
                                work_list.append(work_name)

            other_param.config_data["combo"]["workstation"]["item"] = room_list
            other_param.config_data["combo"]["line"]["item"] = line_list
            other_param.config_data["combo"]["order"]["item"] = work_list
            other_param.save_config_data_to_file()

            xutil.CacheUtil.set("room_map", room_map)

        return self.x_response()

    def combo_index_changed(self, combo_vo: ComboVo, other_param: Any):
        combo_key = combo_vo.get_combo_key()
        combo_value = combo_vo.get_combo_value()

        if combo_key == "workstation":
            # 线体编号和工单需要跟着变化
            room_map = xutil.CacheUtil.get("room_map", {})
            if not room_map:
                return

            line_obj = room_map.get(combo_value)

            line_map = line_obj.get("line_map", {})

            combo_line = getattr(other_param, "combo_line")
            combo_order = getattr(other_param, "combo_order")

            combo_line.clear()
            combo_order.clear()

            line_list = []
            work_list = []

            the_first_2 = True
            for line_name, work_obj in line_map.items():
                combo_line.addItem(line_name)
                line_list.append(line_name)

                if the_first_2:
                    the_first_2 = False
                    work_map = work_obj.get("work_map", {})

                    for work_name, _ in work_map.items():
                        combo_order.addItem(work_name)
                        work_list.append(work_name)

            other_param.config_data["combo"]["line"]["item"] = line_list
            other_param.config_data["combo"]["order"]["item"] = work_list
            other_param.save_config_data_to_file()

            xutil.CacheUtil.set("room_map", room_map)

        if combo_key == "line":
            work_name = combo_vo.get_value_by_cons_key("workstation")

            room_map = xutil.CacheUtil.get("room_map", {})
            if not room_map:
                return

            work_obj = room_map.get(work_name, {}).get("line_map", {}).get(combo_value, {})
            if not work_obj:
                return

            work_map = work_obj.get("work_map", {})

            combo_order = getattr(other_param, "combo_order")
            combo_order.clear()

            work_list = []

            for work_name, _ in work_map.items():
                combo_order.addItem(work_name)
                work_list.append(work_name)

            other_param.config_data["combo"]["order"]["item"] = work_list
            other_param.save_config_data_to_file()

            xutil.CacheUtil.set("room_map", room_map)

    def init_main_window(self, main_window, other_vo: OtherVo):
        combo_line = getattr(main_window, "combo_line")
        combo_line.setEditable(True)


if __name__ == '__main__':
    k1 = "cecd9cf711d44007823c579fbfbe9fbb"
    s1 = "cnNEQcSb"
    time1 = "1589011714"
    r1 = xutil.OtherUtil.get_md5_sign(f"{k1}{s1}{time1}")
    print(r1)
