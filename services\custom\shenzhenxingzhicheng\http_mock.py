# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : http_mock.py
# Time       ：2024/11/14 下午4:32
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import uvicorn
from fastapi import FastAPI

app = FastAPI()


@app.post("/get_sn")
def api1():
    return {
        "code": 0,
        "data": [
            {"SERIALNO": "111", "XONE": 1},
            {"SERIALNO": "222", "XONE": 2},
            {"SERIALNO": "333", "XONE": 3},
            {"SERIALNO": "444", "XONE": 4},
        ],
        "message": "ok"
    }


@app.post("/check")
def api2():
    return {
        "code": 0,
        "message": "ok"
    }


@app.post("/data")
def api3():
    return {
        "code": 0,
        "message": "ok"
    }


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8081)
