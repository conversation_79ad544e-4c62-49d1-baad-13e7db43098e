# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/28 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：爱科通
"""
import os
from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "aiketong release v1.0.0.8",
        "device": "40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
 date: 2023-08-28 09:28  init
 date: 2023-08-28 16:28  comp_user_result传中文不良描述
 date: 2023-09-12 09:59  修复获取不到复判人的问题
 date: 2023-11-30 11:16  坏板的数据不解析
 date: 2024-08-15 18:00  优化：兼容所有拼板都是坏板时的情况
 date: 2024-10-18 10:04  增加日志打印用于澄清问题
 """, }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://*************:30000/InfraredCounting/GetFirstArticleNoticeInfo"
        },
        "order_id": {
            "ui_name": "批次号",
            "value": ""
        },
        "device_ip": {
            "ui_name": "当前设备IP",
            "value": "************"
        }
    }

    path = {
        "pcb_img_path": {
            "ui_name": "整板图路径",
            "value": "",
        },
        "json_data_path": {
            "ui_name": "数据文件路径",
            "value": "",
        },
    }

    button = {
        "get_order_id": {
            "ui_name": "获取批次号"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        order_id = data_vo.get_value_by_cons_key("order_id")
        pcb_img_path = data_vo.get_value_by_cons_key("pcb_img_path")
        json_data_path = data_vo.get_value_by_cons_key("json_data_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        track_index = pcb_entity.track_index

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        board_data_list = []
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                repair_ng_str = comp_entity.repair_ng_str
                repair_ng_code = comp_entity.repair_ng_code
                repair_ng_str = xcons.AIS_40X_ERROR_MAP.get(repair_ng_code, {}).get("standard", repair_ng_str)

                comp_data_list.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": repair_ng_code,
                    "comp_user_result": repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data_list.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data_list,
            })

        pcb_data = {
            "pcb_board_map_path": f"{time_file}.jpg",
            "batchnumber": order_id,
            "Readjudicator": pcb_entity.repair_user,
            "pcb_sn": pcb_sn,
            "pcb_track_line": track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data_list
        }

        img_dst_filepath = f"{pcb_img_path}/{time_file}.jpg"
        pcb_src_image = pcb_entity.get_pcb_t_image()

        xutil.FileUtil.copy_file(pcb_src_image, img_dst_filepath)

        json_dst_filepath = f"{json_data_path}/{time_file}.json"
        xutil.FileUtil.dump_json_to_file(json_dst_filepath, pcb_data)

        return self.x_response()

    def init_main_window(self, main_window, other_vo: OtherVo):
        tmp_edit = getattr(main_window, f"form_order_id")
        tmp_edit.setEnabled(False)

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url = btn_vo.get_value_by_cons_key("api_url")
        device_ip = btn_vo.get_value_by_cons_key("device_ip")

        tmp_edit = getattr(other_param, f"form_order_id")
        tmp_edit.setEnabled(True)

        if btn_vo.get_btn_key() == "get_order_id":
            ret = xrequest.RequestUtil.get(api_url, {"GroupingIP": device_ip})
            if str(ret.get("result")) != "1":
                return self.x_response("false", f"mes接口异常，获取批次失败，error：{ret}")

            order_id = ret.get("OrderNumber")

            tmp_edit.setText(order_id)

        return self.x_response()

    def save_btn_on_window(self, main_window):
        tmp_edit = getattr(main_window, f"form_order_id")
        tmp_edit.setEnabled(False)
