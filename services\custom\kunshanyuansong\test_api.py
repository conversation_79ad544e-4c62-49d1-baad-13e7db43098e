# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2024/9/26 下午2:19
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from xml.etree import ElementTree

# SOAP 响应字符串
from common import xutil

soap_response = '''<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <CheckBarCodeResponse xmlns="http://tempuri.org/">
      <CheckBarCodeResult>OK;OK</CheckBarCodeResult>
    </CheckBarCodeResponse>
  </soap:Body>
</soap:Envelope>'''

# # 解析 XML
# root = ElementTree.fromstring(soap_response)
#
# # 定义命名空间
# namespaces = {
#     'soap': 'http://schemas.xmlsoap.org/soap/envelope/',
#     'ns': 'http://tempuri.org/'
# }
#
# # 查找 CheckBarCodeResult 元素
# check_barcode_result = root.find('.//ns:CheckBarCodeResult', namespaces)
#
# # 获取 CheckBarCodeResult 的文本内容
# if check_barcode_result is not None:
#     result_value = check_barcode_result.text
#     print(f'CheckBarCodeResult: {result_value}')
# else:
#     print('CheckBarCodeResult not found')

ret_str = """<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><CheckBarCodeResponse xmlns="http://tempuri.org/"><CheckBarCodeResult>P;</CheckBarCodeResult></CheckBarCodeResponse></soap:Body></soap:Envelope>"""

root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
print(root[0][0][0].text)

