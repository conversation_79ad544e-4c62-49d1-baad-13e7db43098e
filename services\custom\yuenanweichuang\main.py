# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/11/29 下午4:48
# Author     ：sch
# version    ：python 3.8
# Description：越南纬创
"""

from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

txt_template = """{board_sn}
{project_name}
{result}
{review_time}
{inspect_time}
0
{comp_number}
{comp_ng_number}
Error flag,Recipe name,Paste ID,CAD link Gerber,Error code,Multi Number,Defect sum{comp_ng_str}
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "yuenanweichuang release v1.0.0.3",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-11-29 17:37  保存文档到本地
date: 2024-11-30 00:06  bugfix: 修复存不到指定文件夹问题+数据多输出了一列
date: 2024-12-23 17:25  修改成传检测NG的不良描述
""", }

    path = {
        "save_path_3dx": {
            "ui_name": "文档保存路径",
            "ui_name_en": "FileSavePath",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_3dx = data_dao.get_value_by_cons_key("save_path_3dx", not_null=True)

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name

        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_FILE)

        if review_time == "19700101000000":
            review_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_str = ""

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator
                    comp_result = comp_entity.get_final_result("GOOD", "GOOD", "FAIL")
                    robot_ng_str = comp_entity.robot_ng_str

                    comp_ng_str += f"\n{comp_result};{project_name};{comp_tag};{comp_tag};{robot_ng_str};{board_no};0"

            txt_content = txt_template.format(**{
                "board_sn": barcode,
                "project_name": project_name,
                "result": board_entity.get_repair_result("PASS", "FAIL"),
                "review_time": review_time,
                "inspect_time": start_time,
                "comp_number": board_entity.comp_total_number,
                "comp_ng_number": board_entity.comp_repair_ng_number,
                "comp_ng_str": comp_ng_str
            })

            if not barcode:
                barcode = start_time

            filepath = f"{save_path_3dx}/{barcode}.3dx"
            xutil.FileUtil.write_content_to_file_pro(filepath, txt_content)

        return self.x_response()
