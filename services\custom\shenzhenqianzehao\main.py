"""
# File       : main.py
# Time       ：2025/05/26 09:44
# Author     ："wxc"
# version    ：python 3.8
# Description：深圳谦泽豪
"""
import os
from typing import Any

from common import xrequest, xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["深圳谦泽豪", "she<PERSON><PERSON>qianzehao"],
        "version": "release v1.0.0.2",
        "device": "AIS203 AIS303",
        "feature": ["条码校验", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-26 09:44  ATAOI_2019-39725：条码校验，上传数据
date: 2025-07-08 16:28  ATAOI_2019-39725：新增上传ng器件图片（挂载mes服务器目录），接口增加test_file_list
"""
    }
    other_form = {
        "check_sn_url": {
            "ui_name": "条码校验接口",
            "value": "",
        },
        "upload_api": {
            "ui_name": "上传数据接口",
            "value": "",
        }
    }
    form = {
        "wost_code": {
            "ui_name": "工序代码",
            "value": "",
        },
        "emp_no": {
            "ui_name": "员工号",
            "value": "",
        },
        "wo": {
            "ui_name": "工单",
            "value": "",
        },
        "layt_code": {
            "ui_name": "工位代码",
            "value": "",
        },
        "machine_code": {
            "ui_name": "机台代码",
            "value": "",
        },
        "mes_path": {
            "ui_name": "服务器挂载路径",
            "value": "",
        }
    }
    path = {
        "save_ng_path": {
            "ui_name": "NG器件图片保存路径",
            "value": "",
        },
    }
    combo = {
        "board_side": {
            "ui_name": "板面",
            "item": ["T面", "B面"],
            "value": "T面",
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        check_sn_url = other_vo.get_value_by_cons_key("check_sn_url")
        wost_code = other_vo.get_value_by_cons_key("wost_code")
        emp_no = other_vo.get_value_by_cons_key("emp_no")
        wo = other_vo.get_value_by_cons_key("wo")
        layt_code = other_vo.get_value_by_cons_key("layt_code")

        sn = other_vo.list_sn()[0]

        check_param = {
            "Pcs_SN": sn,
            "Wost_Code": wost_code,
            "Layt_Code": layt_code,
            "Emp_NO": emp_no,
            "WO": wo,
            "Flag": "2",
            "Is_All_Flag": "1",
            "Is_Lot_Flag": "1",
            "M_Client_Id": 1,
            "M_Org_Id": 1,
            "Fixt_SN": "",
        }
        ret = xrequest.RequestUtil.post_json(check_sn_url, check_param)
        if str(ret.get("ReturnStatusType")) != "1":
            return self.x_response("false", f"条码校验失败，error：{ret.get('ErrorMessage')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        upload_api = data_vo.get_value_by_cons_key("upload_api")
        wost_code = data_vo.get_value_by_cons_key("wost_code")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        wo = data_vo.get_value_by_cons_key("wo")
        layt_code = data_vo.get_value_by_cons_key("layt_code")
        machine_code = data_vo.get_value_by_cons_key("machine_code")
        board_side = data_vo.get_value_by_cons_key("board_side")
        save_ng_path = data_vo.get_value_by_cons_key("save_ng_path", not_null=True)
        mes_path = data_vo.get_value_by_cons_key("mes_path", not_null=True)
        is_mount = xutil.FileUtil.ismount_pro(save_ng_path)

        # 检查挂载路径是否挂载上
        if not is_mount:
            return self.x_response("false", f"NG器件图片保存路径未挂载，请检查挂载路径：{save_ng_path}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        fixt_info = {
            "Fixt_SN": pcb_sn,
            "Fixt_Time": ""
        }
        sn_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            defect_list = []
            test_file_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if not comp_entity.repair_result:
                    defect_list.append({
                        "Defect_Code": comp_entity.repair_ng_code,
                        "Defect_Location": comp_entity.designator,
                        "Defect_Desc": comp_entity.repair_ng_str,
                        "Defect_Point": 1,
                        "Original_Defect_Code": comp_entity.robot_ng_code
                    })
                    image_path = comp_entity.image_path
                    if image_path and xutil.FileUtil.file_is_exists(image_path):
                        filename = image_path.split('/')[-1]
                        xutil.FileUtil.copy_file(image_path, f"{save_ng_path}/{filename}")
                        test_file_list.append({
                            "wipf_file_name": filename,
                            "wipf_file_path": mes_path,
                            "wipf_remark": "NG器件图片"
                        })

            sn_list.append({
                "NO": board_entity.board_no,
                "SN": board_entity.barcode,
                "Point_Qty": board_entity.comp_total_number,
                "NG_Point_Qty ": board_entity.comp_robot_ng_number,
                "Remark_Location": "",
                "Remark_Surface": board_side,
                "Result": board_entity.get_final_result("PASS", "REPASS", "FAIL"),
                "SN_Beg_Time": start_time,
                "SN_End_Time": end_time,
                "New_SN": "",
                "Cust_SN": "",
                "Param_List": [],
                "Chek_Keypart_List ": [],
                "Band_Keypart_List": [],
                "Test_Steps_list": [],
                "Defect_List": defect_list,
                "Test_File_List": test_file_list,
            })
        pcb_data = {
            "Wost_Code": wost_code,
            "Layt_Code": layt_code,
            "Machine_Code": machine_code,
            "Emp_NO": emp_no,
            "WO": wo,
            "Action_Code": "",
            "M_Client_Id": 1,
            "M_Org_Id": 1,
            "Fixt_InfoList": fixt_info,
            "SNList": sn_list,
        }
        ret = xrequest.RequestUtil.post_json(upload_api, pcb_data)
        if str(ret.get("ReturnStatusType")) != "1":
            return self.x_response("false", f"mes接口异常，error：{ret.get('ErrorMessage')}")
        return self.x_response()
