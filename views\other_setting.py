# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : other_setting.py
# Time       ：2022/12/29 下午4:55
# Author     ：sch
# version    ：python 3.8
# Description：设置-其他设置
"""
import traceback

from PyQt5.QtWidgets import *

from common import xutil
from common.xutil import log
from services.route import engine
from templates.other_setting import Ui_OtherSettingDialog


class OtherSettingViews(QDialog, Ui_OtherSettingDialog):
    def __init__(self, main_window):
        super(OtherSettingViews, self).__init__()
        self.setupUi(self)

        self.main_window = main_window

        try:
            engine.hock_other_setting(self)
        except Exception as err:
            log.warning(f"其他参数设置失败：{err}")
            log.error(traceback.format_exc())


    def save_app_setting_on_btn(self):
        """
        保存其他设置
        :return:
        """
        app_setting = self.main_window.config_data["app_setting"]

        app_setting["filter_repeat_send"] = self.setting_filter_repeat_send.isChecked()
        merge_send_setting = self.setting_merge_send.isChecked()
        app_setting["merge_send"] = merge_send_setting
        app_setting["send_error_info"] = self.setting_send_error_info.isChecked()
        app_setting["send_info_to_repair"] = self.setting_send_info_to_repair.isChecked()
        app_setting["repair_ip"] = self.edit_repair_ip.text()

        lang_list = ["ZH", "EN"]
        app_setting["setting_lang"] = lang_list[self.setting_lang.currentIndex()]

        # 合并发送需要修改MES.ini中的 SendTogether --> true/false
        send_info_to_repair = app_setting.get("send_info_to_repair")
        repair_ip = app_setting.get("repair_ip")
        xutil.OtherUtil.update_config_by_key("SendTogether", "true" if merge_send_setting else "false")
        xutil.OtherUtil.update_config_by_key("WorkStationIp", repair_ip)
        xutil.OtherUtil.update_config_by_key("SendResultToWorkStation", "true" if send_info_to_repair else "false")

        self.main_window.save_config_data_to_file()
        # self.main_window.log_info("保存其他设置成功！")

    def show_window(self):
        app_setting = self.main_window.config_data["app_setting"]
        self.setting_filter_repeat_send.setChecked(app_setting["filter_repeat_send"])
        self.setting_merge_send.setChecked(app_setting["merge_send"])
        self.setting_send_error_info.setChecked(app_setting["send_error_info"])
        self.setting_send_info_to_repair.setChecked(app_setting["send_info_to_repair"])
        self.edit_repair_ip.setText(app_setting["repair_ip"])

        self.setting_lang.setCurrentText(app_setting.get("setting_lang", "ZH"))

        self.setting_http_server_run.setChecked(app_setting["http_server_run"])
        ret = self.exec_()

        if ret == 1:
            # ok button
            self.save_app_setting_on_btn()
