# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/15 上午11:00
# Author     ：sch
# version    ：python 3.8
# Description：信奥迅
"""
import json
from typing import Any
from uuid import uuid4

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


def x_get_sign_headers(app_id: str = "xxxx") -> dict:
    """
    获取签名头
    :param app_id:
    :return:
    """

    time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
    guid = str(uuid4())
    sign = xutil.OtherUtil.get_md5_sign(f"{app_id}{time_now}{guid}")

    return {
        "app_id": app_id,
        "time": time_now,
        "guid": guid,
        "sign": sign.upper()
    }


class Engine(ErrorMapEngine):
    version = {
        "title": "xinaoxun release v1.0.0.7",
        "device": "401",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-15 17:34  获取条码，上传数据
date: 2024-01-19 11:15  接口请求增加签名校验
date: 2024-01-19 12:11  兼容获取子工单号的返回值
date: 2024-01-29 09:41  界面优化+工单列表显示order_code
""", }

    other_form = {
        "api_app_id": {
            "ui_name": "应用ID(用于签名)",
            "value": "",
        },
        "api_test_connect": {
            "ui_name": "测试服务器接口URL",
            "value": "",
        },
        "api_get_order_list": {
            "ui_name": "获取工单接口URL",
            "value": "",
        },
        "api_get_sn_list": {
            "ui_name": "获取条码接口URL",
            "value": "",
        },
        "api_upload_data": {
            "ui_name": "上传数据接口URL",
            "value": "",
        },
    }

    form = {
        "process_id_1": {
            "ui_name": "*工序编码(1轨)",
            "value": "",
        },
        "process_id_2": {
            "ui_name": "*工序编码(2轨)",
            "value": "",
        },
        "p_org_code": {
            "ui_name": "_*工厂编码",
            "value": "",
        },
        "p_testequipemnt_code": {
            "ui_name": "*测试设备编码",
            "value": "",
        },
        "p_testequipemnt_name": {
            "ui_name": "*测试设备名称",
            "value": "",
        },
        "p_user_code": {
            "ui_name": "*操作用户编码",
            "value": "",
        },
        "model_name": {
            "ui_name": "*设备模式",
            "value": "",
        },
        "side_1": {
            "ui_name": "*产品面别(1轨)",
            "value": "",
        },
        "side_2": {
            "ui_name": "*产品面别(2轨)",
            "value": "",
        },
        "machine_name": {
            "ui_name": "*机器名称",
            "value": "",
        },
        "page": {
            "ui_name": "_*当前第几页",
            "value": "1",
        },
        "count": {
            "ui_name": "_*总条数",
            "value": "20",
        },
        "limit": {
            "ui_name": "_*每页显示条数",
            "value": "20",
        },
        "p_productionorder": {
            "ui_name": "_生产订单号",
            "value": "",
        },
        "p_workorder": {
            "ui_name": "_生产工单号",
            "value": "",
        },
        "p_schedule": {
            "ui_name": "_排产子工单号",
            "value": "",
        },
        "p_product": {
            "ui_name": "_产品编码、名称、规格",
            "value": "",
        },
        "p_line": {
            "ui_name": "_产线编码",
            "value": "",
        },
        "p_customer": {
            "ui_name": "_客户编码、名称",
            "value": "",
        },
        "p_process": {
            "ui_name": "_工艺编码、名称",
            "value": "",
        },
        "p_create_date_start": {
            "ui_name": "_开始创建日期",
            "value": "",
        },
        "p_create_date_end": {
            "ui_name": "_结束创建日期",
            "value": "",
        },
        "p_remark": {
            "ui_name": "_备注信息",
            "value": "",
        },
    }

    combo = {
        "p_page": {
            "ui_name": "_*是否开启分页",
            "item": ['true', 'false'],
            "value": "true",
        },
        "order_list_1": {
            "ui_name": "排产子工单ID(1轨)",
            "item": [],
            "value": "",
        },
        "order_list_2": {
            "ui_name": "排产子工单ID(2轨)",
            "item": [],
            "value": "",
        },
    }

    button = {
        "test_connect": {
            "ui_name": "测试服务器",
        },
        "get_order_btn": {
            "ui_name": "获取排产子工单"
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_get_sn_list = other_vo.get_value_by_cons_key("api_get_sn_list")
        api_app_id = other_vo.get_value_by_cons_key("api_app_id")

        headers = x_get_sign_headers(api_app_id)

        track_index = other_vo.get_track_index()
        pcb_sn = other_vo.get_pcb_sn()

        if track_index == 1:
            order_id = other_vo.get_value_by_cons_key("order_list_1")
        else:
            order_id = other_vo.get_value_by_cons_key("order_list_2")

        ret = xrequest.RequestUtil.get(api_get_sn_list, {"p_schedule_id": order_id, "p_code": pcb_sn},
                                       headers=headers)
        if ret.get('code') != 'success':
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('msg')}")

        data_list = ret.get('data', [])
        ret_sn = []
        for item in data_list:
            sn = item.get('sn_code', '')
            if sn:
                ret_sn.append(sn)

        return self.x_response('true', ','.join(ret_sn))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.track_index

        if track_index == 1:
            order_code = data_vo.get_value_by_cons_key("order_list_1")
            process_id = data_vo.get_value_by_cons_key("process_id_1")
            board_side = data_vo.get_value_by_cons_key("side_1")
        else:
            order_code = data_vo.get_value_by_cons_key("order_list_2")
            process_id = data_vo.get_value_by_cons_key("process_id_2")
            board_side = data_vo.get_value_by_cons_key("side_2")

        order_id_map = xutil.CacheUtil.get('order_id_map', {})
        order_id = order_id_map.get(order_code)

        p_testequipemnt_code = data_vo.get_value_by_cons_key("p_testequipemnt_code")
        p_testequipemnt_name = data_vo.get_value_by_cons_key("p_testequipemnt_name")
        p_user_code = data_vo.get_value_by_cons_key("p_user_code")
        model_name = data_vo.get_value_by_cons_key("model_name")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        api_upload_data = data_vo.get_value_by_cons_key("api_upload_data")

        api_app_id = data_vo.get_value_by_cons_key("api_app_id")

        headers = x_get_sign_headers(api_app_id)

        judge_results = []

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        ng_image_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_data_list.append({
                        "element_no": comp_entity.designator,
                        "element_name": comp_entity.part,
                        "defect_code": comp_entity.robot_ng_code,
                        "confirm_defect_code": comp_entity.repair_ng_code,
                    })

                if comp_entity.is_repair_ng():
                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        ng_image_list.append(comp_src_img)

            judge_results.append({
                "sn_code": barcode,
                "sn_panelid": board_no,
                "model_name": model_name,
                "program_name": pcb_entity.project_name,
                "side": board_side,
                "lane": pcb_entity.track_index,
                "machine_name": machine_name,
                "operator_user": p_user_code,
                "inspection_date": start_time,
                "bengin_date": start_time,
                "end_date": end_time,
                "report_result": board_entity.get_robot_result("PASS", "FAIL"),
                "confirm_result": board_entity.get_repair_result("PASS", "FAIL"),
                "total_component": board_entity.comp_total_number,
                "fail_component": board_entity.comp_robot_ng_number,
                "confirm_fail_component": board_entity.comp_repair_ng_number,
                "aoi_defects": comp_data_list,
            })

        param2 = {
            "p_schedule_id": order_id,
            "p_process_arg_code": process_id,
            "p_testequipemnt_code": p_testequipemnt_code,
            "p_testequipemnt_name": p_testequipemnt_name,
            "p_user_code": p_user_code,
            "p_judge_results": json.dumps(judge_results, ensure_ascii=False),
        }

        files = {}
        for ix, img in enumerate(ng_image_list):
            file_key = f'file{ix}'
            self.log.info(f"上传的文件 key:{file_key} ：{img}")
            tmp_obj = open(img, 'rb')
            files[file_key] = tmp_obj

        ret = xrequest.RequestUtil.post_form(api_upload_data, param2, files=files, headers=headers)

        for i, v in files.items():
            v.close()

        if ret.get('code') != 'success':
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        api_test_connect = btn_vo.get_value_by_cons_key("api_test_connect")
        api_app_id = btn_vo.get_value_by_cons_key("api_app_id")

        headers = x_get_sign_headers(api_app_id)

        if btn_key == 'test_connect':

            ret = xrequest.RequestUtil.get(api_test_connect, {}, headers=headers)
            if ret.get('code') != 'success':
                return self.x_response("false", f"mes接口异常，连接mes失败，error：{ret.get('msg')}")

            return self.x_response()

        elif btn_key == 'get_order_btn':
            api_get_order_list = btn_vo.get_value_by_cons_key("api_get_order_list")
            p_org_code = btn_vo.get_value_by_cons_key("p_org_code")
            p_page = btn_vo.get_value_by_cons_key("p_page")
            page = btn_vo.get_value_by_cons_key("page")
            count = btn_vo.get_value_by_cons_key("count")
            limit = btn_vo.get_value_by_cons_key("limit")
            p_productionorder = btn_vo.get_value_by_cons_key("p_productionorder")
            p_workorder = btn_vo.get_value_by_cons_key("p_workorder")
            p_schedule = btn_vo.get_value_by_cons_key("p_schedule")
            p_product = btn_vo.get_value_by_cons_key("p_product")
            p_line = btn_vo.get_value_by_cons_key("p_line")
            p_customer = btn_vo.get_value_by_cons_key("p_customer")
            p_process = btn_vo.get_value_by_cons_key("p_process")
            p_create_date_start = btn_vo.get_value_by_cons_key("p_create_date_start")
            p_create_date_end = btn_vo.get_value_by_cons_key("p_create_date_end")
            p_remark = btn_vo.get_value_by_cons_key("p_remark")

            if not p_org_code:
                return self.x_response("false", f"请先输入工厂编码再获取排产子工单")

            param1 = {
                "p_org_code": p_org_code,
                "p_page": p_page,
                "page": page,
                "count": count,
                "limit": limit,
                "p_productionorder": p_productionorder,
                "p_workorder": p_workorder,
                "p_schedule": p_schedule,
                "p_product": p_product,
                "p_line": p_line,
                "p_customer": p_customer,
                "p_process": p_process,
                "p_create_date_start": p_create_date_start,
                "p_create_date_end": p_create_date_end,
                "p_remark": p_remark,
            }

            ret = xrequest.RequestUtil.get(api_get_order_list, param1, headers=headers)
            if ret.get('code') != 'success':
                return self.x_response("false", f"mes接口异常，获取排产子工单失败，error：{ret.get('msg')}")

            order_list = ret.get('data', {}).get('data', [])

            # order_ids = []
            order_id_map = {}
            for i in order_list:
                order_id = i.get('id', '')
                order_code = i.get('order_code', '')
                if order_id and order_id not in order_id_map:
                    # order_id_map.append(order_id)
                    order_id_map[order_code] = order_id

            if not order_id_map:
                return self.x_response("false", f"未获取到工单列表！")

            xutil.CacheUtil.set('order_id_map', order_id_map)

            order_ids = list(order_id_map.keys())

            order_item1 = getattr(other_param, 'combo_order_list_1')
            order_item2 = getattr(other_param, 'combo_order_list_2')

            order_item1.clear()
            order_item2.clear()
            for i in order_ids:
                order_item1.addItem(i)
                order_item2.addItem(i)

            other_param.config_data['combo']['order_list_1']['item'] = order_ids
            other_param.config_data['combo']['order_list_1']['value'] = order_ids[0]
            other_param.config_data['combo']['order_list_2']['item'] = order_ids
            other_param.config_data['combo']['order_list_2']['value'] = order_ids[0]
            other_param.save_config_data_to_file()

        return self.x_response()


if __name__ == '__main__':
    ret = x_get_sign_headers()
    print(ret)
