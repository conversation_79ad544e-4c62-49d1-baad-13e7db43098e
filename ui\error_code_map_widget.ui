<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ErrorCodeWidget</class>
 <widget class="QWidget" name="ErrorCodeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>910</width>
    <height>523</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>自定义Mes不良代码</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>1</number>
   </property>
   <property name="leftMargin">
    <number>1</number>
   </property>
   <property name="topMargin">
    <number>1</number>
   </property>
   <property name="rightMargin">
    <number>1</number>
   </property>
   <property name="bottomMargin">
    <number>1</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>机型:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="combo_device_mode">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <item>
         <property name="text">
          <string>AIS203/AIS303/AIS40X/AIS50x</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>AIS301</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>AIS201</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>AIS63X</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>906</width>
        <height>473</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="spacing">
        <number>2</number>
       </property>
       <property name="leftMargin">
        <number>2</number>
       </property>
       <property name="topMargin">
        <number>2</number>
       </property>
       <property name="rightMargin">
        <number>2</number>
       </property>
       <property name="bottomMargin">
        <number>2</number>
       </property>
       <item>
        <widget class="QTableWidget" name="error_table_widget">
         <column>
          <property name="text">
           <string>机型</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>默认Code</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>默认描述</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>自定义Code</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>自定义描述</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>整体上传Mes</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>单个上传Mes</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>配置项3</string>
          </property>
         </column>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_2">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="layoutDirection">
          <enum>Qt::RightToLeft</enum>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="spacing">
           <number>6</number>
          </property>
          <property name="leftMargin">
           <number>9</number>
          </property>
          <property name="topMargin">
           <number>9</number>
          </property>
          <property name="rightMargin">
           <number>9</number>
          </property>
          <property name="bottomMargin">
           <number>9</number>
          </property>
          <item>
           <widget class="QPushButton" name="btn_save">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="layoutDirection">
             <enum>Qt::RightToLeft</enum>
            </property>
            <property name="text">
             <string>保存</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_import_config">
            <property name="text">
             <string>导入配置文件</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_output_config">
            <property name="text">
             <string>导出配置文件</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
