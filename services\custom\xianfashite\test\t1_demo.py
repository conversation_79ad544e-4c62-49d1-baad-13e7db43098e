# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2025/2/8 15:54
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

ret_xml = """<acccomm>
    <response>
        <result>0</result>
        <message>Motor;401</message>
        <message2>
            <returnvalue>
                <param>CYCLE_ID,401</param>
                <param>STATUSBITS,1</param>
                <param>ISREWORK,True</param>
                <param>UNITPARTTYPEID,1</param>
                <param>PANELSTATE,</param>
            </returnvalue>
        </message2>
        <otherinfo></otherinfo>
    </response>
</acccomm>"""


if __name__ == '__main__':
    root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
    # cmd = root.find('request').find('command').text
    print(root)
    # print(cmd)

    # part_list = root.find('response').find('message2')
    #
    # ret_part_list = []
    # for part in part_list:
    #     # print(part.text)
    #     ret_part_list.append(part.text)
    #
    # print(ret_part_list)

    ret_val = root.find('response').find('message2')[0]

    cycle_id = 0
    for item in ret_val:
        item_val = item.text
        if "CYCLE_ID" in item_val:
            cycle_id = item_val.split(',')[1]

    print(cycle_id)


