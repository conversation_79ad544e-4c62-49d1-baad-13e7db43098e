# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/11/28 下午5:14
# Author     ：sch
# version    ：python 3.8
# Description： 测试minio
"""

# from minio import Minio
# from minio.error import S3Error
#
#
# def upload_data_to_minio_server(
#         minio_server: str,
#         access_key: str,
#         secret_key: str,
#         src_filepath: str,
#         dst_bucket_name: str,
#         dst_filename: str,
# ):
#     try:
#         # 创建MinIO客户端对象
#         client = Minio(
#             minio_server,
#             access_key=access_key,
#             secret_key=secret_key,
#             secure=False
#         )
#
#         ret = client.fput_object(dst_bucket_name, dst_filename, src_filepath)
#         print(f"上传成功: {ret=}")
#
#     except S3Error as err:
#         print("Error occurred: ", err)
#     except Exception as err:
#         print(f"上传失败，其他异常：error:{err}")
#
#
# if __name__ == '__main__':
#     src_file = '/home/<USER>/Desktop/tmp20230504.txt'
#
#     upload_data_to_minio_server(
#         "127.0.0.1:9000",
#         "kh5LrFNdETJhdGFIiBbn",
#         "zclayJCC3NXYruZr9P3W1KolFDGVXfkjvyidTVDr",
#         src_file,
#         "image",
#         "test1.txt"
#     )
import asyncio
import base64
import json

import httpx

from common import xrequest


def get_org_str(v: str) -> str:
    return base64.b64decode(v).decode('utf-8')


async def http_mes(api_url, json_param: dict, headers: dict):
    print(f"请求地址：{api_url} Body Json 请求参数：{json.dumps(json_param, ensure_ascii=False)} Headers: {headers}")
    async with httpx.AsyncClient(verify=False) as client:
        res = await client.post(api_url, json=json_param, headers=headers)

    print(f"接口响应：{res}  msg:{res.text}")

    if res:
        return res.json()


if __name__ == '__main__':
    api_url = ""
    access_token = ""
    workstation = ""
    pcb_sn = ""

    headers = {
        "Authorization": f"Bearer {access_token}",
    }

    # 1. 先获取基本信息
    get_info_url = f"{api_url}/system/config/oss/getossconninfo"
    get_info_ret = asyncio.run(http_mes(get_info_url, {}, headers))

    minio_server = ""
    minio_access_key = ""
    minio_secret_key = ""
    minio_bucket_name = ""
    src_image_path = ""
    filename = ""

    # 2. 上传minio服务器
    xrequest.upload_data_to_minio_server(
        minio_server,
        minio_access_key,
        minio_secret_key,
        minio_bucket_name,
        src_image_path,
        filename,
    )

    # 3. 调用接口 /product/sn/savefilerec 上传数据
    save_file_rec = f"{api_url}/product/sn/savefilerec"
    save_file_param = {
        "WorkStation": workstation,
        "Sn": pcb_sn,
        "FileName": filename
    }
    save_file_ret = asyncio.run(http_mes(save_file_rec, save_file_param, headers))
