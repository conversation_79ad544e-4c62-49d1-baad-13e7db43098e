# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/06/17 17:20
# Author     ：chencb
# version    ：python 3.8
# Description：东莞钧捷 https://jira.cvte.com/browse/ATAOI_2019-40259
"""
import json
from typing import Any
from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "customer": ["东莞钧捷", "dongguanjunjie"],
        "version": "release v1.0.0.5",
        "device": "AIS401, AIS430",
        "feature": ["登录获取token", "从Mes获取条码", "条码校验", "上传数据", "图片保存"],
        "author": "chenchongbing",
        "release": """
date: 2025-06-18 17:24  jira:40259 增加登录获取token、从Mes获取条码、条码校验、上传数据功能
date: 2025-07-07 17:15  remark字段改为json列表;picDir改为传空；器件数据改为检测NG；增加图片保存路径并按检测时间创建子目录；
date: 2025-07-08 17:30  整板图路径从remark字段移到picDir
date: 2025-07-16 18:06  remark字段改为列表序列化为字符串
date: 2025-07-18 17:10  remark字段增加复判检测结果字段result
""", }

    form = {
        "login_user": {
            "ui_name": "登录工号",
            "value": ""
        },
        "login_pwd": {
            "ui_name": "登录密码",
            "value": ""
        },
        "company_no": {
            "ui_name": "公司编号",
            "value": ""
        },
        "machine_no": {
            "ui_name": "机器编号",
            "value": ""
        },
        "line_no": {
            "ui_name": "线别编号",
            "value": ""
        },
        "station_no": {
            "ui_name": "站点编号",
            "value": ""
        },
        "prod_no": {
            "ui_name": "工单号",
            "value": ""
        },
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
    }

    password_style = ["login_pwd"]

    combo = {
        "retest": {
            "ui_name": "允许重复测试",
            "value": "允许",
            "item": ["允许", "不允许"]
        },
    }

    path = {
        "save_path_pic": {
            "ui_name": "图片保存路径",
            "value": "",
        }
    }

    def __init__(self):
        self.token = ''

    def _get_token(self, vo):
        self.log.info('先登录获取token')
        user = vo.get_value_by_cons_key("login_user")
        password = vo.get_value_by_cons_key("login_pwd")
        api_url = vo.get_value_by_cons_key("api_url")

        err_msg = ''
        if not api_url:
            err_msg = '接口地址未填写，获取token失败！请正确填写登录工号、登录密码和接口地址！'
        elif not user or not password:
            err_msg = '账号密码未填写，获取token失败！请正确填写登录工号、登录密码！'

        if not err_msg:
            param = {
                "userNo": user,
                "password": password
            }
            try:
                login_url = f"{api_url}/swiftmom/api/clientLogin"
                ret = xrequest.RequestUtil.post_json(login_url, param)
                if str(ret.get('msgId')) == '0':
                    self.token = ret.get('token')
                else:
                    err_msg = f"获取token失败，失败原因：{ret.get('msgStr')}"
            except Exception as e:
                err_msg = f'本地网络异常，获取token失败！错误信息：{e}'

        if err_msg:
            self.token = ''
            return self.x_response('false', err_msg)
        else:
            return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        if not self.token:
            # token为空需要提前获取下
            ret = self._get_token(other_vo)
            if not ret.get('result'):
                return ret

        api_url = other_vo.get_value_by_cons_key("api_url")
        prod_no = other_vo.get_value_by_cons_key("prod_no")
        company_no = other_vo.get_value_by_cons_key("company_no")

        get_sn_url = f"{api_url}/swiftmom/api/getRelationPcbSeq"
        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "pcbSeq": pcb_sn,
            "prodNo": prod_no,
            "orgId": company_no,
            "type": "1"
        }
        header = {'token': self.token}
        try:
            ret = xrequest.RequestUtil.post_json(get_sn_url, param, headers=header)
            if str(ret.get('msgId')) == '0':
                data = ret.get("data")
                ret_sn = [item.get("pcbSeq") for item in data]
                sn_str = ",".join(ret_sn)
                return self.x_response("true", sn_str)
            else:
                return self.x_response("false", f"mes接口返回错误, 获取条码失败, 错误信息: {ret.get('msgStr')}")
        except Exception as e:
            return self.x_response("false", f'本地网络异常，获取条码失败！错误信息：{e}')

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        if not self.token:
            # token为空需要提前获取下
            ret = self._get_token(other_vo)
            if not ret.get('result'):
                return ret

        api_url = other_vo.get_value_by_cons_key("api_url")
        prod_no = other_vo.get_value_by_cons_key("prod_no")
        station_no = other_vo.get_value_by_cons_key("station_no")
        retest = other_vo.get_value_by_cons_key("retest")

        sn_list = other_vo.list_sn()
        check_url = f"{api_url}/swiftmom/api/checkRoute"

        try:
            header = {'token': self.token}
            for sn in sn_list:
                param = {
                    "pcbSeq": sn,
                    "prodNo": prod_no,
                    "stationNo": station_no,
                    "retest": 0 if retest != "允许" else 1
                }
                ret = xrequest.RequestUtil.post_json(check_url, param, headers=header)
                if str(ret.get("msgId")) != '0':
                    return self.x_response("false", f"mes接口返回错误,条码【{sn}】校验失败, 错误信息: {ret.get('msgStr')}")

            return self.x_response()
        except Exception as e:
            return self.x_response("false", f'本地网络异常，条码校验失败！错误信息：{e}')

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()
        if inspect_type == xcons.INSPECTOR:
            return self.x_response('false', '只发送复判后结果，若要发送机器初判结果请提变更需求！')

        if not self.token:
            # token为空需要提前获取下
            ret = self._get_token(data_vo)
            if not ret.get('result'):
                return ret

        api_url = data_vo.get_value_by_cons_key("api_url", not_null=True)
        line_no = data_vo.get_value_by_cons_key("line_no")
        station_no = data_vo.get_value_by_cons_key("station_no")
        prod_no = data_vo.get_value_by_cons_key("prod_no")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        company_no = data_vo.get_value_by_cons_key("company_no")
        save_path_pic = data_vo.get_value_by_cons_key("save_path_pic")
        if not save_path_pic:
            return self.x_response("false", "请先选择图片保存路径！")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        start_datetime = pcb_entity.get_start_time()
        start_time = start_datetime.strftime(xcons.FMT_TIME_DEFAULT)
        time_file = start_datetime.strftime(xcons.FMT_TIME_FILE)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)
        header = {'token': self.token}
        send_data_url = f"{api_url}/swiftmom/api/createAOIData"

        # 图片拷贝路径
        test_time_path = start_datetime.strftime("%Y/%m/%d/%H/%M/%S")
        image_dest_path = f'{save_path_pic}/{test_time_path}'
        xutil.FileUtil.ensure_dir_exist(image_dest_path)

        # 拷贝整板图，整板图文件名：程序名_整板条码_检测时间.jpg
        src_pcb_image = pcb_entity.pcb_image[0]
        suffix = xutil.FileUtil.get_file_suffix(src_pcb_image)
        dst_pcb_image = f'{image_dest_path}/{pcb_entity.project_name}_{pcb_entity.pcb_barcode}_{time_file}{suffix}'
        xutil.FileUtil.copy_file(src_pcb_image, dst_pcb_image)

        try:
            err_msg_list = []

            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                board_sn = board_entity.barcode
                board_no = board_entity.board_no
                if not board_sn:
                    board_sn = f"{time_file}_{board_no}"
                now = xutil.DateUtil.get_datetime_now("%Y-%m-%d %H:%M:%S")

                remark = []
                for comp_entity in board_entity.yield_comp_entity():
                    # 需要记录机器检测所有不良信息数据
                    if comp_entity.is_robot_ng():
                        program = pcb_entity.project_name
                        part = comp_entity.part
                        designator = comp_entity.designator
                        ng_code = comp_entity.robot_ng_code
                        # 获取中文不良描述
                        ng_desc = xcons.AIS_40X_ERROR_MAP.get(ng_code, {}).get('standard')
                        if not ng_desc:
                            ng_desc = comp_entity.robot_ng_str

                        # 拷贝不良器件图，命名：程序名_拼板条码_拼板号_料号_位号_不良描述.jpg
                        src_ng_image = comp_entity.image_path
                        suffix = xutil.FileUtil.get_file_suffix(src_ng_image)
                        dst_ng_image_name = f'{program}_{board_sn}_{board_no}_{part}_{designator}_{ng_desc}{suffix}'
                        dst_ng_image = f'{image_dest_path}/{dst_ng_image_name}'
                        xutil.FileUtil.copy_file(src_ng_image, dst_ng_image)

                        min_val = test_val = max_val = ''
                        for alg in comp_entity.yield_alg_entity():
                            if alg.result == ng_code:
                                min_val = alg.min_threshold
                                test_val = alg.test_val
                                max_val = alg.max_threshold

                        remark.append({
                            "part": part,
                            "designator": designator,
                            "result": comp_entity.get_final_result("PASS", "PASS", "FAIL"),
                            "ngDesc": ng_desc,
                            "minThreshold": min_val,
                            "testVal": test_val,
                            "maxThreshold": max_val,
                            "programName": program,
                            "ngImagePath": dst_ng_image,
                        })

                remark_str = json.dumps(remark, separators=(',', ':'), ensure_ascii=False)
                param = {
                    "pcbSeq": board_sn,
                    "createdDateTime": now,
                    "prodNo": prod_no,
                    "result": board_entity.get_repair_result("PASS", "FAIL"),
                    "oriResult": board_entity.get_robot_result("PASS", "FAIL"),
                    "machineNo": machine_no,
                    "threadNo": line_no,
                    "beginTime": start_time,
                    "endTime": end_time,
                    "board": pcb_entity.board_side,
                    "remark": remark_str,
                    "companyNo": company_no,
                    "mainPcbSeq": pcb_sn,
                    "partSn": board_no,
                    "trackName": str(pcb_entity.track_index),
                    "badPointQty": str(board_entity.comp_repair_ng_number),
                    "pointQty": str(board_entity.comp_total_number),
                    "siteNo": station_no,
                    "programName ": pcb_entity.project_name,
                    "picDir ": dst_pcb_image  # 只存储整板图
                }

                res = xrequest.RequestUtil.post_json(send_data_url, param, headers=header)
                if res.get("result") != 0:
                    err_msg_list.append(f"拼板【{board_sn}】错误信息: {res.get('message')}")

            # 复判数据发送完成表示流程结束,token置空，下次流程重新获取
            self.token = ''
            if err_msg_list:
                return self.x_response("false", f"mes接口返回错误，上传数据失败：{','.join(err_msg_list)}")
            return self.x_response()
        except Exception as e:
            return self.x_response("false", f'本地网络异常，上传数据失败！错误信息：{e}')
