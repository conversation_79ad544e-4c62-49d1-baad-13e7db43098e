# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/19 上午9:20
# Author     ：sch
# version    ：python 3.8
# Description：基于深技改动，改为视源工厂通用版本
"""
import base64
from typing import Any

from common import xutil, xrequest, xcons
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import ErrorMapEngine

global_data = {}


class Engine(ErrorMapEngine):
    version = {
        "customer": ["视源工厂", "shiyuan_cvte"],
        "version": "release v1.0.0.2",
        "device": "AIS203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-19 11:35  获取Token，条码校验，上传数据
date: 2024-06-25 17:07  上传数据2，token头改为`cmes_info`
date: 2025-07-16 10:39  增加是否上传NG数据的配置项
""", }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(获取Token)",
            "value": "http://szdz-cmessys.gz.cvte.cn/api/Account/CreateToken",
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://szdz-cmesprod.gz.cvte.cn/api/services/test/TestScan/CheckLotSnCode",
        },
        "api_url_data1": {
            "ui_name": "接口URL(上传数据1)",
            "value": "http://szdz-cmesprod.gz.cvte.cn/api/services/test/TestScan/SaveProduceTestByAutoTest",
        },
        "api_url_data2": {
            "ui_name": "接口URL(上传数据2)",
            "value": "http://smes-open-api.gz.cvte.cn/api/services/testapi/TestLog/InsertProcessItem",
        },
        "factory_code": {
            "ui_name": "工厂短码",
            "value": "szdz",
        },
        "station_code": {
            "ui_name": "工步编号/测试工位",
            "value": "",
        },
        "work_center": {
            "ui_name": "产线编号(WorkCenter)",
            "value": "",
        },
        "line_id": {
            "ui_name": "产线编号(LineId)",
            "value": "",
        },
        "line_name": {
            "ui_name": "产线名称",
            "value": "",
        },
        "tenant_id": {
            "ui_name": "工厂/租户ID",
            "value": "1213",
        },
        "tenant_name": {
            "ui_name": "工厂/租户名称",
            "value": "",
        },
    }

    form = {
        "email": {
            "ui_name": "账号",
            "value": "<EMAIL>",
        },
        "password": {
            "ui_name": "密码",
            "value": "1234@Qwer",
        },

        "test_no": {
            "ui_name": "测试架编号",
            "value": "",
        },
        "worker_no": {
            "ui_name": "测试员编号",
            "value": "",
        },
        "key_info": {
            "ui_name": "TV测试KEY信息",
            "value": "",
        },
        "test_version": {
            "ui_name": "TV测试版本",
            "value": "",
        },
        "m_lot_no": {
            "ui_name": "批次号",
            "value": "",
        },
        "item_name_ui": {
            "ui_name": "项目名称",
            "value": "",
        },
        "product_model": {
            "ui_name": "机型",
            "value": "",
        },
    }

    button = {
        "get_token_btn": {
            "ui_name": "获取Token",
            "value": "",
        }
    }

    other_combo = {
        "is_upload_ng_data": {
            "ui_name": "是否上传NG数据",
            "item": ["Yes", "No"],
            "value": "No",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 6)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        factory_code = other_vo.get_value_by_cons_key("factory_code")
        station_code = other_vo.get_value_by_cons_key("station_code")

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"未获取到Token, 请先获取Token!")

        header1 = {
            "Content-Type": "application/json",
            f"cmes_{factory_code}_info": token
        }

        ret_res = self.x_response()

        error_msg_list = []
        for ix, sn in enumerate(other_vo.list_sn()):
            body1 = {
                "lotSnCode": sn,
                "stationCode": station_code,
            }

            ret1 = xrequest.RequestUtil.post_json(api_url_check, body1, headers=header1)

            result = ret1.get("result", {})

            code = result.get("code")
            msg = result.get("msg")

            if str(code) != "200":
                error_msg_list.append(f"条码:({ix + 1},{sn}) error:{msg}")

        if error_msg_list:
            error_msg = "\n".join(error_msg_list)
            return self.x_response("false", f"mes接口异常，条码校验失败, 错误信息:{error_msg}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        factory_code = data_vo.get_value_by_cons_key("factory_code")
        station_code = data_vo.get_value_by_cons_key("station_code")
        work_center = data_vo.get_value_by_cons_key("work_center")
        test_no = data_vo.get_value_by_cons_key("test_no")
        worker_no = data_vo.get_value_by_cons_key("worker_no")
        key_info = data_vo.get_value_by_cons_key("key_info")
        test_version = data_vo.get_value_by_cons_key("test_version")
        m_lot_no = data_vo.get_value_by_cons_key("m_lot_no")
        item_name_ui = data_vo.get_value_by_cons_key("item_name_ui")
        product_model = data_vo.get_value_by_cons_key("product_model")
        line_id = data_vo.get_value_by_cons_key("line_id")
        line_name = data_vo.get_value_by_cons_key("line_name")
        tenant_id = data_vo.get_value_by_cons_key("tenant_id")
        tenant_name = data_vo.get_value_by_cons_key("tenant_name")
        is_upload_ng_data = data_vo.get_value_by_cons_key("is_upload_ng_data")

        api_url_data1 = data_vo.get_value_by_cons_key("api_url_data1")
        api_url_data2 = data_vo.get_value_by_cons_key("api_url_data2")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if pcb_entity.order_id:
            m_lot_no = pcb_entity.order_id

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        token = global_data.get("token")
        if not token:
            return self.x_response("false", f"未获取到Token, 请先获取Token!")

        header1 = {
            "Content-Type": "application/json",
            f"cmes_{factory_code}_info": token
        }

        header2 = {
            "Content-Type": "application/json",
            f"cmes_info": token
        }

        error_msg_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list1 = []
            comp_data_list2 = []

            only_ng_str = ""

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                repair_ng_str = comp_entity.repair_ng_str
                if comp_entity.is_repair_ng():

                    # 1.
                    comp_data_list1.append(f"{comp_tag},{repair_ng_str}")

                    if not only_ng_str:
                        only_ng_str = repair_ng_str

                    # 2.
                    alg_data_list = []
                    for alg_entity in comp_entity.yield_alg_entity():
                        alg_data_list.append({
                            "Name": alg_entity.test_name,
                            "Value": alg_entity.test_val,
                            "StandardValue": f"{alg_entity.min_threshold}~{alg_entity.max_threshold}"
                        })

                    comp_data_list2.append({
                        "InstructionName": repair_ng_str,
                        "InstructionDesc": comp_tag,
                        "InstructionResult": False,
                        "Data": alg_data_list,
                    })

            param = {
                "lotSnCode": barcode,
                "isPass": board_entity.repair_result,
                "workCenter": work_center,
                "stationCode": station_code,
                "testFrameidNo": test_no,
                "testTime": start_time,
                "workerNo": worker_no,
                "keyInfo": key_info,
                "autoTestVersion": test_version,
                "testStartDate": start_time,
                "testEndDate": end_time,
                "testItem": ";".join(comp_data_list1),
            }

            ret = xrequest.RequestUtil.post_json(api_url_data1, param, headers=header1)
            result = ret.get("result", {})
            code = result.get("code")
            msg = result.get("msg")
            if str(code) != "200":
                error_msg_list.append(f"条码:({board_no},{barcode}) error:{msg}")

            if is_upload_ng_data == "Yes":
                if board_entity.is_repair_ng():
                    param2 = {
                        "LotSnCode": barcode,
                        "MoLotNo": m_lot_no,
                        "ItemName": item_name_ui,
                        "ItemResult": board_entity.repair_result,
                        "StationCode": station_code,

                        "FailureReason": only_ng_str,
                        "ItemStartTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT8),
                        "Itemduration": str(pcb_entity.get_cycle_time()) + "s",
                        "ProductModel": product_model,
                        "LineId": line_id,
                        "LineName": line_name,
                        "TenantId": tenant_id,
                        "TenantName": tenant_name,
                        "InstructionList": comp_data_list2
                    }
                    ret2 = xrequest.RequestUtil.post_json(api_url_data2,  param2, headers=header2)

                    result = ret2.get("result", {})
                    code = result.get("code")
                    msg = result.get("msg")
                    if str(code) != "200":
                        error_msg_list.append(f"条码:({board_no},{barcode}) error:{msg}")
                else:
                    self.log.warning(f"板卡PASS，无需上传NG数据！")

        if error_msg_list:
            error_msg = "\n".join(error_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败, 报错信息:{error_msg}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
        email = btn_vo.get_value_by_cons_key("email")
        password = btn_vo.get_value_by_cons_key("password")

        if btn_vo.get_btn_key() == "get_token_btn":
            if "@" not in email:
                pwd_encrypt = base64.b64encode(password.encode('utf8')).decode("utf8")
            else:
                pwd_encrypt = xutil.OtherUtil.get_md5_sign(password).lower()

            login_param = {"Email": email, "Pwd": pwd_encrypt}

            ret = xrequest.RequestUtil.get(api_url_login, login_param, headers=login_param)

            if not ret.get("success"):
                msg = ret.get("error", {}).get("message")
                return self.x_response("false", f"登录失败, error: {msg}")

            global_data["token"] = ret.get("result")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        email = other_vo.get_value_by_cons_key("email")
        password = other_vo.get_value_by_cons_key("password")

        if "@" not in email:
            pwd_encrypt = base64.b64encode(password.encode('utf8')).decode("utf8")
        else:
            pwd_encrypt = xutil.OtherUtil.get_md5_sign(password).lower()

        login_param = {"Email": email, "Pwd": pwd_encrypt}

        ret = xrequest.RequestUtil.get(api_url_login, login_param, headers=login_param)

        if not ret.get("success"):
            return

        global_data["token"] = ret.get("result")
