# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/7/8 下午3:26
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from typing import Any

from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "version": "release v1.0.0.1",
        "customer": ["标准版2", "standard-sn"],
        "device": "20x,30x,40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-07-08 15:31  临时版本：自动生成条码
""", }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:

        barcode_map = other_vo.get_barcode_map()

        barcode_num = len(barcode_map) - 2

        pcb_sn = other_vo.get_pcb_sn()

        try:
            pcb_sn_int = int(pcb_sn)
        except Exception as err:
            return self.x_response("false", f"PCB SN 格式错误，{pcb_sn}, 条码需为纯数字才可以自动生成！err:{err}")

        ret_sn_list = []
        for ix in range(barcode_num):
            barcode = str(pcb_sn_int + ix)
            ret_sn_list.append(barcode)

        return self.x_response("true", ",".join(ret_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        return self.x_response()
