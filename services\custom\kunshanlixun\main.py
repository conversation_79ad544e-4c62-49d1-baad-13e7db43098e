# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/11/1 上午11:10
# Author     ：sch
# version    ：python 3.8
# Description：昆山立讯
"""
import json
from typing import Any

from common import xrequest, xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

get_sn_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <getsn xmlns="MesWebService">
      <Parames>{param_str}</Parames>
    </getsn>
  </soap:Body>
</soap:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <AOIPostDataFianl xmlns="MesWebService">
      <Parames>{param_str}</Parames>
    </AOIPostDataFianl>
  </soap:Body>
</soap:Envelope>"""


def write_request_log(log_save_path: str, write_content):
    """
    保存接口请求日志
    :return:
    """
    date_file = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE0)
    time_file = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
    filepath = f"{log_save_path}/{date_file}.log"

    with open(filepath, "a") as f:
        f.write(f"{time_file} -   {write_content}\n")


# bad_info_limit_dict = LimitedDict(100)


def x_get_sn(
        cmd,
        pcb_sn,
        work_order,
        terminal_id,
        machine_type,
        track_index,
        operator_id,
        project_name,
        api_url_get_sn,
        log_save_path,
):
    """
    从mes获取条码
    :param cmd:
    :param pcb_sn:
    :param work_order:
    :param terminal_id:
    :param machine_type:
    :param track_index:
    :param operator_id:
    :param project_name:
    :param api_url_get_sn:
    :param log_save_path:
    :return:
    """
    if cmd == "1":
        tool_sn = ""
        panel_sn = pcb_sn
    else:
        tool_sn = pcb_sn
        panel_sn = ""

    get_sn_param = json.dumps({
        "WorkOrder": work_order,
        "TerminalID": terminal_id,
        "MachineType": machine_type,
        "LaneNO": str(track_index),
        "OperatorID": operator_id,
        "CMD": cmd,
        "ProgramName": project_name,
        "ToolingSN": tool_sn,
        "PanelBarCode": panel_sn
    }, ensure_ascii=False)

    get_sn_str = get_sn_template.format(param_str=get_sn_param)

    req_log1 = f"RequestUrl: {api_url_get_sn}"
    req_log2 = f"RequestParam: {get_sn_param}"
    write_request_log(log_save_path, req_log1)
    write_request_log(log_save_path, req_log2)

    ret_str_get_sn = xrequest.RequestUtil.post_soap(
        api_url_get_sn,
        get_sn_str,
        soap_action="MesWebService/getsn"
    )
    root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str_get_sn)
    text_str = root1[0][0][0].text

    req_log3 = f"Response: {text_str}"
    write_request_log(log_save_path, req_log3)

    ret0 = json.loads(text_str)

    return ret0

    # if ret0.get('Result') != 'OK':
    #     return x_response("false", f"mes接口异常，error：{ret0.get('MESSAGE')}")
    #
    # boards = ret0.get('Boards', [])
    #
    # if not boards:
    #     return x_response("false", f"mes接口异常，未从mes获取到坏板信息！")
    #
    # ret_sn = [str(int(i.get('Blocknumber'))) for i in boards if i.get('SKIP') == 'Y']
    #
    # return


link_txt_template = """{pcb_sn}
{board_sn_str}
{fixture_sn}
{gangban_sn}
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "kunshanlixun release v1.0.0.24",
        "device": "401,501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-11-01 11:11  init
date: 2024-11-06 15:59  从mes获取条码，上传数据 
date: 2024-11-07 15:52  传机器报的不良代码
date: 2024-11-12 14:49  增加坏板绑定功能
date: 2024-11-12 17:19  bugfix:增加坏板绑定功能
date: 2024-12-02 17:02  检测完发送mes时输出link文档
date: 2024-12-11 12:24  修改Link的发送时机，并且加上发送选项
date: 2024-12-19 11:52  Link过站时不需要从Mes获取条码/坏板+新增保存高清整板图
date: 2024-12-20 11:15  优先上传主软件保存的高清原图
date: 2024-12-21 09:49  如果是高清原图，本地不保存，直接移动走
""",
    }

    combo = {
        "cmd": {
            "ui_name": "CMD",
            "item": ["0", "1"],
            "value": "1",
        },
        "machine_type": {
            "ui_name": "设备类型",
            "item": ["AOI", "SPI"],
            "value": "AOI",
        },
        "barcode_type": {
            "ui_name": "条码类型",
            "item": ["虚拟条码", "真实条码"],
            "value": "真实条码",
        },
        "send_type": {
            "ui_name": "过站类型",
            "item": ["PSA", "Link", "PSA+Link"],
            "value": "PSA",
        },
    }

    form = {
        "work_order": {
            "ui_name": "工单",
            "value": "",
        },
        "terminal_id": {
            "ui_name": "站点",
            "value": "",
        },
        "operator_id": {
            "ui_name": "员工",
            "value": "",
        },
    }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "http://127.0.0.1:8081/spiaoi/Luxshare_AOISPIService.asmx/getsn",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/spiaoi/Luxshare_AOISPIService.asmx/AOIPostDataFianl",
        },
        "api_url_tool_get_panel_sn": {
            "ui_name": "接口URL(治具获取大板码)",
            "value": "http://127.0.0.1:8081/api/bobcat/dopost/plugins/panel/ToolGetPanelNo",
        },
    }

    path = {
        "log_save_path": {
            "ui_name": "接口上传日志",
            "value": "",
        },
        "save_path_link_txt": {
            "ui_name": "link文档保存路径",
            "value": "",
        },
        "save_path_img_t": {
            "ui_name": "T面整板图保存路径",
            "value": "",
        },
        "save_path_img_b": {
            "ui_name": "B面整板图保存路径",
            "value": "",
        },
    }

    def get_bad_board_info(self, other_vo: OtherVo, other_param: Any):
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        cmd = other_vo.get_value_by_cons_key("cmd")
        machine_type = other_vo.get_value_by_cons_key("machine_type")
        work_order = other_vo.get_value_by_cons_key("work_order")
        terminal_id = other_vo.get_value_by_cons_key("terminal_id")
        operator_id = other_vo.get_value_by_cons_key("operator_id")
        log_save_path = other_vo.get_value_by_cons_key("log_save_path", not_null=True)

        pcb_sn = other_vo.list_sn()[0]

        track_index = other_vo.get_track_index()

        if cmd == "1":
            tool_sn = ""
            panel_sn = pcb_sn
        else:
            tool_sn = pcb_sn
            panel_sn = ""

        get_sn_param = json.dumps({
            "WorkOrder": work_order,
            "TerminalID": terminal_id,
            "MachineType": machine_type,
            "LaneNO": str(track_index),
            "OperatorID": operator_id,
            "CMD": cmd,
            "ProgramName": other_vo.get_project_name(),
            "ToolingSN": tool_sn,
            "PanelBarCode": panel_sn
        }, ensure_ascii=False)

        get_sn_str = get_sn_template.format(param_str=get_sn_param)

        req_log1 = f"RequestUrl: {api_url_get_sn}"
        req_log2 = f"RequestParam: {get_sn_param}"
        write_request_log(log_save_path, req_log1)
        write_request_log(log_save_path, req_log2)

        ret_str_get_sn = xrequest.RequestUtil.post_soap(
            api_url_get_sn,
            get_sn_str,
            soap_action="MesWebService/getsn"
        )
        root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str_get_sn)
        text_str = root1[0][0][0].text

        req_log3 = f"Response: {text_str}"
        write_request_log(log_save_path, req_log3)

        ret0 = json.loads(text_str)

        if ret0.get('Result') != 'OK':
            return self.x_response("false", f"mes接口异常，error：{ret0.get('MESSAGE')}")

        boards = ret0.get('Boards', [])

        if not boards:
            return self.x_response("false", f"mes接口异常，未从mes获取到坏板信息！")

        ret_sn = [str(int(i.get('Blocknumber'))) for i in boards if i.get('SKIP') == 'Y']

        return self.x_response("true", ",".join(ret_sn))

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        cmd = other_vo.get_value_by_cons_key("cmd")
        machine_type = other_vo.get_value_by_cons_key("machine_type")
        work_order = other_vo.get_value_by_cons_key("work_order")
        terminal_id = other_vo.get_value_by_cons_key("terminal_id")
        operator_id = other_vo.get_value_by_cons_key("operator_id")
        log_save_path = other_vo.get_value_by_cons_key("log_save_path", not_null=True)

        track_index = other_vo.get_track_index()

        pcb_sn = other_vo.get_pcb_sn()

        barcode_map = other_vo.get_barcode_map()

        if cmd == "1":
            tool_sn = ""
            panel_sn = pcb_sn
        else:
            tool_sn = pcb_sn
            panel_sn = ""

        get_sn_param = json.dumps({
            "WorkOrder": work_order,
            "TerminalID": terminal_id,
            "MachineType": machine_type,
            "LaneNO": str(track_index),
            "OperatorID": operator_id,
            "CMD": cmd,
            "ProgramName": other_vo.get_project_name(),
            "ToolingSN": tool_sn,
            "PanelBarCode": panel_sn
        }, ensure_ascii=False)

        get_sn_str = get_sn_template.format(param_str=get_sn_param)

        req_log1 = f"RequestUrl: {api_url_get_sn}"
        req_log2 = f"RequestParam: {get_sn_param}"
        write_request_log(log_save_path, req_log1)
        write_request_log(log_save_path, req_log2)

        ret_str_get_sn = xrequest.RequestUtil.post_soap(api_url_get_sn, get_sn_str,
                                                        soap_action="MesWebService/getsn")

        root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str_get_sn)
        text_str = root1[0][0][0].text

        req_log3 = f"Response: {text_str}"
        write_request_log(log_save_path, req_log3)

        ret0 = json.loads(text_str)

        if ret0.get('Result') != 'OK':
            return self.x_response("false", f"mes接口异常，error：{ret0.get('MESSAGE')}")

        boards = ret0.get('Boards', [])

        if not boards:
            return self.x_response("false", f"mes接口异常，未从mes获取到条码！")

        ret_sn = [i.get('BoardBarCode') for i in boards if i.get('SKIP') == 'N']

        i = 0
        for sn in ret_sn:
            i += 1
            barcode_map[str(i)] = sn

        return self.x_response("true", json.dumps(barcode_map))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        machine_type = data_vo.get_value_by_cons_key("machine_type")
        work_order = data_vo.get_value_by_cons_key("work_order")
        terminal_id = data_vo.get_value_by_cons_key("terminal_id")
        operator_id = data_vo.get_value_by_cons_key("operator_id")
        log_save_path = data_vo.get_value_by_cons_key("log_save_path", not_null=True)
        save_path_link_txt = data_vo.get_value_by_cons_key("save_path_link_txt", not_null=True)

        api_url_tool_get_panel_sn = data_vo.get_value_by_cons_key("api_url_tool_get_panel_sn")

        cmd = data_vo.get_value_by_cons_key("cmd")
        api_url_get_sn = data_vo.get_value_by_cons_key("api_url_get_sn")
        barcode_type = data_vo.get_value_by_cons_key("barcode_type")
        send_type = data_vo.get_value_by_cons_key("send_type")

        save_path_img_t = data_vo.get_value_by_cons_key("save_path_img_t", not_null=True)
        save_path_img_b = data_vo.get_value_by_cons_key("save_path_img_b", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_barcode = pcb_entity.pcb_barcode
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        upload_img_list = pcb_entity.list_all_pcb_image_origin_img()  # 高清原图

        if not upload_img_list:
            # 如果没有高清原图，尝试去获取缩略图
            upload_img_list = pcb_entity.list_all_pcb_image_v2()

            is_origin_img = False
        else:
            is_origin_img = True

        self.log.info(f"开始上传整板图，上传数量：{len(upload_img_list)}")

        for pcb_src_filename in upload_img_list:
            if "/T_" in pcb_src_filename:
                save_path_img = save_path_img_t
            else:
                save_path_img = save_path_img_b

            dst_filepath = f"{save_path_img}/{pcb_barcode}_{time_file}.jpg"

            if is_origin_img:
                xutil.FileUtil.move_file(pcb_src_filename, dst_filepath)
            else:
                xutil.FileUtil.copy_file(pcb_src_filename, dst_filepath)

        inspect_type = data_vo.get_inspect_type()

        track_index = pcb_entity.track_index
        project_name = pcb_entity.project_name

        pcb_sn = pcb_entity.pcb_barcode

        boards = []

        if inspect_type == "repair":

            if send_type != "Link":
                ret0 = x_get_sn(
                    cmd,
                    pcb_sn,
                    work_order,
                    terminal_id,
                    machine_type,
                    track_index,
                    operator_id,
                    project_name,
                    api_url_get_sn,
                    log_save_path,
                )

                if ret0.get('Result') != 'OK':
                    return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret0.get('MESSAGE')}")

                boards = ret0.get('Boards', [])

                if not boards:
                    return self.x_response("false", f"mes接口异常，未从mes获取到条码！")

        # ret_sn = [i.get('BoardBarCode') for i in boards if i.get('SKIP') == 'N']

        board_bad_flag_map = {}  # 坏板标识
        for b in boards:
            board_bad_flag_map[str(int(b.get('Blocknumber')))] = b.get('SKIP')

        self.log.info(f"board_bad_flag_map: {len(board_bad_flag_map)}")

        board_data_list = []

        t_report_xml = pcb_entity.get_pcb_t_report_xml()
        b_report_xml = pcb_entity.get_pcb_b_report_xml()

        comp_barcode_map = xutil.XmlUtil.dict_comp_barcode(t_report_xml)

        if b_report_xml:
            b_comp_barcode_map = xutil.XmlUtil.dict_comp_barcode(b_report_xml)
            # 如果是双面时，更新器件条码map
            for k, v in b_comp_barcode_map.items():
                if k in comp_barcode_map:
                    comp_barcode_map[k].extend(v)

        self.log.info(f"comp barcode map: {comp_barcode_map}")

        fixture_barcode = pcb_entity.fixture_barcode
        gangban_sn = ""
        pcb_sn = pcb_entity.pcb_barcode

        comp_sn_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no
            barcode = board_entity.barcode

            if board_no == "999":
                self.log.info(f"钢板码，不需要发送mes！")

                gangban_sn = barcode
                continue

            comp_barcode_list = comp_barcode_map.get(board_no, [])
            comp_barcode_list.sort(key=lambda x: x["comp_tag"])

            # new_sn_list = [item.get("comp_barcode") for item in comp_barcode_list]
            new_sn_list = []
            for item in comp_barcode_list:
                comp_barcode = item.get("comp_barcode")
                if not comp_barcode:
                    comp_barcode = "NOREAD"

                new_sn_list.append(comp_barcode)

            link_sn = ",".join(new_sn_list)

            # if not link_sn:
            #     link_sn = "NOREAD"

            comp_sn_list.append(link_sn)

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_data_list.append({
                        "RefranceID": comp_entity.designator,
                        "ErrorCode": comp_entity.robot_ng_code,
                    })

            board_data = {
                "Blocknumber": board_no.zfill(3),
                "BoardBarCode": pcb_sn,
                # "SKIP": board_entity.get_final_result("N", "N", "N", "Y"),
                "SKIP": board_bad_flag_map.get(board_no, "N"),
                "ComponmentID": "",
                "TestResult": board_entity.get_final_result("PASS", "RPASS", "REPAIR"),
            }

            if comp_data_list:
                board_data["Errordetail"] = comp_data_list

            board_data_list.append(board_data)

        ret_res = self.x_response()

        if inspect_type == "repair":
            panel_param = {
                "CMD": "1,1,1,1",
                "WorkOrder": work_order,
                "LaneNO": str(pcb_entity.track_index),
                "MachineType": machine_type,
                "TerminalID": terminal_id,
                "OperatorID": operator_id,
                "ProgramName": pcb_entity.project_name,
                "ToolingSN": "",
                "CreateTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1),
                "ReviseEndTime": pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT1),
                "TestResult": pcb_entity.get_final_result("PASS", "RPASS", "REPAIR"),
                "Panels": [
                    {
                        "PanelBarCode": pcb_sn,
                        "TestResult": pcb_entity.get_final_result("PASS", "RPASS", "REPAIR"),
                        "ReviseResult": pcb_entity.get_final_result("PASS", "RPASS", "REPAIR"),
                        "Boards": board_data_list,
                    }
                ]
            }

            data_param = json.dumps(
                panel_param, ensure_ascii=False
            )

            if send_type in ["PSA", "PSA+Link"]:
                self.log.info(f"AOI send data ....")
                data_str = data_template.format(param_str=data_param)

                req_log1 = f"RequestUrl: {api_url_data}"
                req_log2 = f"RequestParam: {data_param}"
                write_request_log(log_save_path, req_log1)
                write_request_log(log_save_path, req_log2)

                data_ret = xrequest.RequestUtil.post_soap(api_url_data, data_str,
                                                          soap_action="MesWebService/AOIPostDataFianl")

                root1 = xutil.XmlUtil.get_xml_root_by_str(data_ret)
                text_str = root1[0][0][0].text

                req_log3 = f"Response: {text_str}"
                write_request_log(log_save_path, req_log3)

                ret = json.loads(text_str)

                ret0 = ret[0]

                if ret0.get('Result') != 'OK':
                    return self.x_response("false", f"mes接口异常，error：{ret0.get('MESSAGE')}")

            if send_type in ["Link", "PSA+Link"]:
                self.log.info(f"生成link文档中....")

                if barcode_type == "虚拟条码":
                    # 需要拿虚拟码去获取整板码
                    get_panel_sn_param = {
                        "tool_sn": pcb_sn
                    }
                    ret = xrequest.RequestUtil.post_json(api_url_tool_get_panel_sn, get_panel_sn_param)
                    if str(ret.get("code")) != "0":
                        ret_res = self.x_response("false", f"mes接口异常，获取整板码失败，error：{ret.get('msg')}")

                    panel_sn = ret.get('data', {}).get('panel_no', "")
                    pcb_sn = panel_sn
                    self.log.info(f"虚拟码，已经置换成从mes获取到的PanelSN！")

                link_content = link_txt_template.format(**{
                    "pcb_sn": pcb_sn,
                    "board_sn_str": ";".join(comp_sn_list),
                    "fixture_sn": fixture_barcode,
                    "gangban_sn": gangban_sn,
                })
                start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
                link_filename = f"{save_path_link_txt}/{pcb_sn}_{start_time}.txt"
                xutil.FileUtil.write_content_to_file_pro(link_filename, link_content)

        else:
            self.log.warning(f"inspector send mes, ignore!")

        return ret_res


if __name__ == '__main__':
    #     ret_str1 = """<?xml version="1.0" encoding="utf-8" ?>
    # <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    #                xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    #     <soap:Body>
    #         <getsnResponse xmlns="MesWebService">
    #             <getsnResult>
    #                 {
    # 	"PanelBarCode": "AP303STF1119D00003",
    # 	"Result": "OK",
    # 	"Boards": [{
    # 		"Blocknumber": "001",
    # 		"BoardBarCode": "AP303STF1119D00003001",
    # 		"SKIP": "N"
    # 	}, {
    # 		"Blocknumber": "002",
    # 		"BoardBarCode": "AP303STF1119D00003002",
    # 		"SKIP": "N"
    # 	}],
    # 	"MESSAGE": ""
    # }
    #             </getsnResult>
    #         </getsnResponse>
    #     </soap:Body>
    # </soap:Envelope>"""
    #
    #     root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    #     text_str = root1[0][0][0].text
    #
    #     ret_json = json.loads(text_str)
    #     print(ret_json)
    comp_barcode_map = xutil.XmlUtil.dict_comp_barcode(f"/home/<USER>/Downloads/昆山立讯/20241220/mes-link-12-20/20241220-1/T_20241220115814615_1_NG/report.xml")
    print(comp_barcode_map)
