# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/7 上午10:48
# Author     ：sch
# version    ：python 3.8
# Description：厦门宏发
"""
import os
from typing import Any

from common import xrequest, xcons, xutil
from common.xutil import x_response, time_cost
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

global_data = {}


def match_project_name(part_name: str, board_side: str) -> list:
    """
    模糊匹配程序名
    :param part_name: 料号
    :param board_side: 板面
    :return:
    """
    project_path = f"/home/<USER>/aoi/program/projects"

    file_list_dir = os.listdir(project_path)

    filter_project_file = []

    for file in file_list_dir:
        filepath = f"{project_path}/{file}"

        if not os.path.isdir(filepath):
            continue

        if part_name not in file:
            continue

        if board_side not in file:
            continue

        filter_project_file.append(file)

    return filter_project_file


def x_refresh_token(api_url, username, password):
    """
    刷新token
    :param api_url:
    :param username:
    :param password:
    :return:
    """
    ret = xrequest.RequestUtil.get(api_url, {"name": username, "password": password})
    access_token = ret.get("access_token")
    if not access_token:
        return x_response("false", f"mes接口异常，获取token失败，error：{ret.get('errmsg')}")

    global_data["access_token"] = access_token
    return None


class Engine(ErrorMapEngine):
    version = {
        "title": "xiamenhongfa release v1.0.0.6",
        "device": "40x",
        "feature": ["条码校验", "上传数据", "切换板式"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-16 14:31  条码校验，上传数据，切换板式
date: 2024-08-16 18:46  bugfix
date: 2024-08-20 14:15  兼容MES接口返回ErrCode 1 
date: 2024-09-25 14:59  模糊匹配需要切换的板式
date: 2024-09-29 18:42  bugfix:切换板式超时
date: 2024-11-25 11:12  通过ftp上传NG图片
""", }

    other_form = {
        "api_url_get_token": {
            "ui_name": "接口URL(获取Token)",
            "value": "http://***********:1002/Authentication/Login"
        },
        "api_url_get_project": {
            "ui_name": "接口URL(自动调程序)",
            "value": "http://***********:1002/Equipment/AutoCallProgram"
        },
        "api_url_check": {
            "ui_name": "接口URL(进板校验)",
            "value": "http://***********:1002/Equipment/BarcodeScan"
        },
        "api_url_data": {
            "ui_name": "接口URL(数据上传)",
            "value": "http://***********:1002/Equipment/UploadAOIData"
        },

        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    form = {
        "username": {
            "ui_name": "用户名",
            "value": "",
        },
        "password": {
            "ui_name": "用户密码",
            "value": "",
        },
        "device_id": {
            "ui_name": "设备ID",
            "value": "AOI01",
        },
        "operator": {
            "ui_name": "操作员",
            "value": "1902789",
        },
        "order_id": {
            "ui_name": "工单",
            "value": "",
        },
    }

    combo = {
        "check_board_side": {
            "ui_name": "切换程序的板面",
            "item": ["TOP", "BOT", "SID"],
            "value": "TOP",
        }
    }

    button = {
        "get_token_btn": {
            "ui_name": "获取Token"
        },
        "check_project_btn": {
            "ui_name": "根据工单切换板式"
        },

    }

    other_combo = {
        "check_project_timeout": {
            "ui_name": "切换板式超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "20", "30", "40", "50", "60", "80", "100", "120", "300", "600"],
            "value": "20",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 6)  # 定时刷新token

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")

        api_url_get_token = other_vo.get_value_by_cons_key("api_url_get_token")
        username = other_vo.get_value_by_cons_key("username")
        password = other_vo.get_value_by_cons_key("password")

        project_name = other_vo.get_project_name()

        access_token = global_data.get("access_token")

        if not access_token:
            x_refresh_token(api_url_get_token, username, password)

        access_token = global_data.get("access_token")

        headers = {
            "Authorization": f"Bearer {access_token}"
        }

        sn_list = other_vo.list_sn()

        ret_msg_list = []
        ix = 0
        for sn in sn_list:

            ix += 1
            check_param = {
                "Barcode": sn,
                "ProductProgram": project_name
            }

            ret = xrequest.RequestUtil.post_form(api_url_check, {
                "UserParameter": xutil.OtherUtil.obj_to_json(check_param)
            }, headers=headers)
            if ret.get("ErrCode") != "0000":
                ret_msg_list.append(f"No:{ix} SN:{sn} Error:{ret.get('ErrMsg')}")

        if ret_msg_list:
            err_msg = "\n".join(ret_msg_list)
            return self.x_response("false", f"mes接口异常，条码校验失败，{err_msg}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        api_url_get_token = data_vo.get_value_by_cons_key("api_url_get_token")
        username = data_vo.get_value_by_cons_key("username")
        password = data_vo.get_value_by_cons_key("password")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        access_token = global_data.get("access_token")

        if not access_token:
            x_refresh_token(api_url_get_token, username, password)

        access_token = global_data.get("access_token")

        headers = {
            "Authorization": f"Bearer {access_token}"
        }

        review_path = pcb_entity.get_pcb_pcb_t_review_path()

        ng_path = f"{review_path}/images/ng"

        board_pass_count = 0
        board_repass_count = 0
        board_ng_count = 0

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()
        file_date = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        ftp_client.cd_or_mkdir(f"{ftp_path}/{file_date}")

        for board_entity in pcb_entity.yield_board_entity():
            final_result = board_entity.get_final_result()

            if final_result == "PASS":
                board_pass_count += 1
            elif final_result == "REPASS":
                board_repass_count += 1
            else:
                board_ng_count += 1

        ret_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    comp_data_list.append({
                        "CompDesignator": comp_entity.designator,
                        "CompPart": comp_entity.part,
                        "CompRobotCode": comp_entity.robot_ng_code,
                        "CompRobotResult": comp_entity.robot_ng_str,
                        "CompUserResult": comp_entity.repair_ng_str
                    })

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        comp_dst_img = f"{barcode}_{comp_entity.designator}.png"
                        ftp_client.upload_file(comp_src_img, comp_dst_img)

            board_param = {
                "Barcode": barcode,
                "TestResult": board_entity.get_robot_result(),
                "NGPicture": ng_path,
                "WholeBoardPic": pcb_entity.get_pcb_t_image(),
                "AlarmInfo": "",
                "CTTime": str(pcb_entity.get_cycle_time()),
                "IssueLocation": "",
                "IssueName": "",
                "CheckTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "ReJugeResult": board_entity.get_repair_result(),
                "RepairUser": pcb_entity.repair_user,
                "BoardNumber": pcb_entity.board_count,
                "BoardCheckNGNo": board_repass_count + board_ng_count,
                "BoardReJugeNGNo": board_ng_count,
                "BoardFalseAlaemNo": board_repass_count,
                "BoardSN": barcode,
                "BoardNO": board_no,
                "BoardCompList": comp_data_list
            }

            ret = xrequest.RequestUtil.post_form(api_url_data, {
                "UserParameter": xutil.OtherUtil.obj_to_json(board_param)
            }, headers=headers)
            if ret.get("ErrCode") != "0000":
                ret_msg_list.append(f"No:{board_no} SN:{barcode} Error:{ret.get('ErrMsg')}")

        ftp_client.close()

        if ret_msg_list:
            err_msg = "\n".join(ret_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，{err_msg}")

        return self.x_response()

    @time_cost
    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()

        api_url_get_token = btn_vo.get_value_by_cons_key("api_url_get_token")
        username = btn_vo.get_value_by_cons_key("username")
        password = btn_vo.get_value_by_cons_key("password")
        check_project_timeout = btn_vo.get_value_by_cons_key("check_project_timeout", to_int=True)

        check_board_side = btn_vo.get_value_by_cons_key("check_board_side")

        if btn_key == "get_token_btn":
            if x_res := x_refresh_token(api_url_get_token, username, password):
                return x_res

        elif btn_key == "check_project_btn":
            self.log.info(f"------------手动切换程序-------------")
            api_url_get_token = btn_vo.get_value_by_cons_key("api_url_get_token")
            username = btn_vo.get_value_by_cons_key("username")
            password = btn_vo.get_value_by_cons_key("password")

            api_url_get_project = btn_vo.get_value_by_cons_key("api_url_get_project")
            device_id = btn_vo.get_value_by_cons_key("device_id")
            order_id = btn_vo.get_value_by_cons_key("order_id")
            operator = btn_vo.get_value_by_cons_key("operator")

            access_token = global_data.get("access_token")

            if not access_token:
                x_refresh_token(api_url_get_token, username, password)

            access_token = global_data.get("access_token")

            headers = {
                "Authorization": f"Bearer {access_token}"
            }

            check_project_param = {
                "MoName": order_id,
                "DeviceId": device_id,
                "Operator": operator
            }

            ret = xrequest.RequestUtil.post_form(api_url_get_project, {
                "UserParameter": xutil.OtherUtil.obj_to_json(check_project_param)
            }, headers=headers)
            if ret.get("ErrCode") != "0000":
                return self.x_response("false", f"mes接口异常，切换板式失败，error：{ret.get('ErrMsg')}")

            product_program = ret.get("ProductProgram")
            self.log.info(f"将要切换的料号：{product_program} 板面：{check_board_side}  ---> 完整板式名还需做模糊匹配...")

            check_project_list = match_project_name(product_program, check_board_side)
            if len(check_project_list) == 1:
                project_name = check_project_list[0]
                self.log.info(f"找到本地板式[{project_name}]，正在切换中...")

                ret = xrequest.SocketUtil.api_check_project(project_name, timeout=check_project_timeout)
                if ret.get("ErrorCodeV2") != "0":
                    return self.x_response("false", f"切换板式失败，error：{ret.get('ErrorMessageV2')}")

            elif len(check_project_list) == 0:
                return self.x_response("false", f"本机无法找到该料号[{product_program}] {check_board_side}板面的程序，无法进行切换！")
            else:
                return self.x_response("false", f"该料号找到了多个程序，无法进行切换！ ---> {check_project_list}")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_get_token = other_vo.get_value_by_cons_key("api_url_get_token")
        username = other_vo.get_value_by_cons_key("username")
        password = other_vo.get_value_by_cons_key("password")

        x_refresh_token(api_url_get_token, username, password)
