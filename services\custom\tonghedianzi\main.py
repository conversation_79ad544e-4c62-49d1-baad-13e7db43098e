# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/5 上午10:51
# Author     ：sch
# version    ：python 3.8
# Description：通合电子
"""
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import x_response
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


def x_request(api_url, command_string) -> dict:
    """
    统一请求方式
    """
    api_url = f"{api_url}/ATECommandCode"
    ret = xrequest.RequestUtil.post_form(api_url, {"commandString": command_string}, to_json=False)
    root = xutil.XmlUtil.get_xml_root_by_str(ret)
    ret_str = root.text

    if not ret_str.startswith("OK"):
        return x_response("false", f"接口响应异常, error: {ret_str}")
    else:
        return dict()


class Engine(ErrorMapEngine):
    version = {
        "title": "tonghedianzi release v1.0.0.1",
        "device": "401,430,501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-05 10:57  init
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/webservice/mesateapi.asmx/ATECommandCode",
        },
        "username": {
            "ui_name": "工号",
            "value": "",
        },
        "process_name": {
            "ui_name": "工序名称",
            "value": "",
        },
        "source_name": {
            "ui_name": "资源名称",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        username = other_vo.get_value_by_cons_key("username")
        process_name = other_vo.get_value_by_cons_key("process_name")
        source_name = other_vo.get_value_by_cons_key("source_name")
        device_code = other_vo.get_value_by_cons_key("device_code")

        error_res = {}
        for ix, sn in enumerate(other_vo.list_sn()):
            test_data = ["01", username, sn, process_name, source_name, device_code, ""]

            check_param_str = f'{";".join(test_data)};'
            res1 = x_request(api_url, check_param_str)
            if res1:
                error_res = res1

        if error_res:
            return error_res

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        username = data_vo.get_value_by_cons_key("username")
        process_name = data_vo.get_value_by_cons_key("process_name")
        source_name = data_vo.get_value_by_cons_key("source_name")
        device_code = data_vo.get_value_by_cons_key("device_code")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        error_res = dict()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_ng_list4 = []
            comp_ng_list5 = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    comp_ng_str = comp_entity.repair_ng_str

                    comp_ng_list4.append(f"{comp_tag}:{comp_ng_str}")

                    comp_ng_list5.append("NG")
                    comp_ng_list5.append(comp_ng_str)
                    comp_ng_list5.append(comp_tag)

            param4_list = ["04", username, barcode, process_name, source_name, device_code, "",
                           test_time, ','.join(comp_ng_list4)]

            param4_str = ";".join(param4_list)
            self.log.info(f"开始调用04接口....")
            res1 = x_request(api_url, param4_str)
            if res1:
                error_res = res1

            param5_list = ["05", username, barcode, process_name, source_name, device_code, ""]
            if comp_ng_list5:
                base_data = param5_list + comp_ng_list5
                param5_str = ";".join(base_data) + ";"
            else:
                param5_list.append("OK;")
                param5_str = ";".join(param5_list)

            self.log.info(f"开始调用05接口....")
            res2 = x_request(api_url, param5_str)
            if res2:
                error_res = res2

        if error_res:
            return error_res

        return self.x_response()
