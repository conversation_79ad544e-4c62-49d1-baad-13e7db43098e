#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
开发人员专用的高级日志分析界面
提供强大的搜索、过滤和分析功能
"""

import sys
import os
import re
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QTabWidget, QTextEdit, 
                            QLineEdit, QPushButton, QLabel, QComboBox, QCheckBox,
                            QTableWidget, QTableWidgetItem, QSplitter, QGroupBox,
                            QDateTimeEdit, QSpinBox, QProgressBar, QMessageBox,
                            QFileDialog, QListWidget, QTreeWidget, QTreeWidgetItem,
                            QFrame, QScrollArea, QTextBrowser, QPlainTextEdit,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDateTime, QTimer, QRegExp
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap, QPalette, QSyntaxHighlighter, QTextCharFormat

try:
    from .log_parser import LogParser, LogEntry
    from .log_searcher import LogSearcher, SearchCriteria
    from .error_analyzer import ErrorAnalyzer
    from .business_analyzer import BusinessAnalyzer
    from .smart_diagnosis import SmartDiagnosis
    from .config_manager import ConfigManager
except ImportError:
    from log_parser import LogParser, LogEntry
    from log_searcher import LogSearcher, SearchCriteria
    from error_analyzer import ErrorAnalyzer
    from business_analyzer import BusinessAnalyzer
    from smart_diagnosis import SmartDiagnosis
    from config_manager import ConfigManager


class LogHighlighter(QSyntaxHighlighter):
    """日志语法高亮器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.highlighting_rules = []
        
        # 时间戳格式
        timestamp_format = QTextCharFormat()
        timestamp_format.setForeground(QColor(100, 100, 100))
        self.highlighting_rules.append((QRegExp(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'), timestamp_format))
        
        # 日志级别
        error_format = QTextCharFormat()
        error_format.setForeground(QColor(220, 50, 47))
        error_format.setFontWeight(QFont.Bold)
        self.highlighting_rules.append((QRegExp(r'\bERROR\b|\bCRITICAL\b'), error_format))
        
        warning_format = QTextCharFormat()
        warning_format.setForeground(QColor(255, 165, 0))
        warning_format.setFontWeight(QFont.Bold)
        self.highlighting_rules.append((QRegExp(r'\bWARNING\b|\bWARN\b'), warning_format))
        
        info_format = QTextCharFormat()
        info_format.setForeground(QColor(0, 100, 200))
        self.highlighting_rules.append((QRegExp(r'\bINFO\b'), info_format))
        
        # 错误码
        error_code_format = QTextCharFormat()
        error_code_format.setForeground(QColor(255, 0, 255))
        error_code_format.setFontWeight(QFont.Bold)
        self.highlighting_rules.append((QRegExp(r'error\d+'), error_code_format))
        
        # IP地址
        ip_format = QTextCharFormat()
        ip_format.setForeground(QColor(0, 150, 0))
        self.highlighting_rules.append((QRegExp(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'), ip_format))
        
        # 文件路径
        path_format = QTextCharFormat()
        path_format.setForeground(QColor(150, 75, 0))
        self.highlighting_rules.append((QRegExp(r'[/\\][^\s]+'), path_format))
    
    def highlightBlock(self, text):
        for pattern, format in self.highlighting_rules:
            expression = QRegExp(pattern)
            index = expression.indexIn(text)
            while index >= 0:
                length = expression.matchedLength()
                self.setFormat(index, length, format)
                index = expression.indexIn(text, index + length)


class AdvancedSearchThread(QThread):
    """高级搜索线程"""
    progress_updated = pyqtSignal(int)
    search_completed = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, searcher, criteria):
        super().__init__()
        self.searcher = searcher
        self.criteria = criteria
        
    def run(self):
        try:
            results = self.searcher.search(self.criteria)
            self.search_completed.emit(results)
        except Exception as e:
            self.error_occurred.emit(str(e))


class DeveloperMainWindow(QMainWindow):
    """开发人员专用主窗口"""
    
    def __init__(self):
        super().__init__()
        self.entries = []
        self.searcher = LogSearcher()
        self.error_analyzer = ErrorAnalyzer()
        self.business_analyzer = BusinessAnalyzer()
        self.smart_diagnosis = SmartDiagnosis()
        self.config_manager = ConfigManager()
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("日志分析工具 - 开发人员版")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QLineEdit, QTextEdit, QPlainTextEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
            }
            QTableWidget {
                background-color: #3c3c3c;
                color: #ffffff;
                gridline-color: #555555;
            }
            QHeaderView::section {
                background-color: #555555;
                color: #ffffff;
                padding: 4px;
                border: 1px solid #666666;
            }
            QComboBox {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧搜索面板
        search_panel = self.create_search_panel()
        splitter.addWidget(search_panel)
        
        # 右侧内容面板
        content_panel = self.create_content_panel()
        splitter.addWidget(content_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 1200])
        
        # 状态栏
        self.statusBar().showMessage("就绪 - 开发人员模式")
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QGroupBox("文件操作")
        layout = QHBoxLayout(toolbar)
        
        # 文件选择
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("选择日志文件或目录...")
        layout.addWidget(QLabel("日志路径:"))
        layout.addWidget(self.file_path_edit)
        
        self.browse_btn = QPushButton("浏览")
        layout.addWidget(self.browse_btn)
        
        self.load_btn = QPushButton("加载日志")
        layout.addWidget(self.load_btn)
        
        self.reload_btn = QPushButton("重新加载")
        layout.addWidget(self.reload_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return toolbar
        
    def create_search_panel(self):
        """创建搜索面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 快速搜索
        quick_search = QGroupBox("快速搜索")
        quick_layout = QVBoxLayout(quick_search)
        
        self.quick_search_edit = QLineEdit()
        self.quick_search_edit.setPlaceholderText("输入关键词...")
        quick_layout.addWidget(self.quick_search_edit)
        
        quick_buttons = QHBoxLayout()
        self.quick_search_btn = QPushButton("搜索")
        self.clear_search_btn = QPushButton("清除")
        quick_buttons.addWidget(self.quick_search_btn)
        quick_buttons.addWidget(self.clear_search_btn)
        quick_layout.addLayout(quick_buttons)
        
        layout.addWidget(quick_search)
        
        # 高级搜索
        advanced_search = QGroupBox("高级搜索")
        advanced_layout = QGridLayout(advanced_search)
        
        # 关键词搜索
        advanced_layout.addWidget(QLabel("关键词:"), 0, 0)
        self.keywords_edit = QLineEdit()
        self.keywords_edit.setPlaceholderText("用空格分隔多个关键词")
        advanced_layout.addWidget(self.keywords_edit, 0, 1)
        
        # 排除关键词
        advanced_layout.addWidget(QLabel("排除:"), 1, 0)
        self.exclude_edit = QLineEdit()
        self.exclude_edit.setPlaceholderText("排除的关键词")
        advanced_layout.addWidget(self.exclude_edit, 1, 1)
        
        # 正则表达式
        advanced_layout.addWidget(QLabel("正则:"), 2, 0)
        self.regex_edit = QLineEdit()
        self.regex_edit.setPlaceholderText("正则表达式模式")
        advanced_layout.addWidget(self.regex_edit, 2, 1)
        
        # 日志级别
        advanced_layout.addWidget(QLabel("级别:"), 3, 0)
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        advanced_layout.addWidget(self.level_combo, 3, 1)
        
        # 错误码
        advanced_layout.addWidget(QLabel("错误码:"), 4, 0)
        self.error_code_edit = QLineEdit()
        self.error_code_edit.setPlaceholderText("如: 255, 18, 244")
        advanced_layout.addWidget(self.error_code_edit, 4, 1)
        
        # 文件过滤
        advanced_layout.addWidget(QLabel("文件:"), 5, 0)
        self.file_filter_edit = QLineEdit()
        self.file_filter_edit.setPlaceholderText("文件名模式")
        advanced_layout.addWidget(self.file_filter_edit, 5, 1)
        
        # 时间范围
        advanced_layout.addWidget(QLabel("开始时间:"), 6, 0)
        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDateTime(QDateTime.currentDateTime().addDays(-1))
        advanced_layout.addWidget(self.start_time_edit, 6, 1)
        
        advanced_layout.addWidget(QLabel("结束时间:"), 7, 0)
        self.end_time_edit = QDateTimeEdit()
        self.end_time_edit.setDateTime(QDateTime.currentDateTime())
        advanced_layout.addWidget(self.end_time_edit, 7, 1)
        
        # 选项
        self.case_sensitive_cb = QCheckBox("区分大小写")
        advanced_layout.addWidget(self.case_sensitive_cb, 8, 0, 1, 2)
        
        # 搜索按钮
        self.advanced_search_btn = QPushButton("高级搜索")
        advanced_layout.addWidget(self.advanced_search_btn, 9, 0, 1, 2)
        
        layout.addWidget(advanced_search)
        
        # 预设搜索
        preset_search = QGroupBox("预设搜索")
        preset_layout = QVBoxLayout(preset_search)
        
        self.preset_buttons = [
            ("最近错误", self.search_recent_errors),
            ("网络问题", self.search_network_issues),
            ("MES通信", self.search_mes_communication),
            ("条码问题", self.search_barcode_issues),
            ("设备故障", self.search_device_issues),
            ("性能问题", self.search_performance_issues)
        ]
        
        for text, handler in self.preset_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(handler)
            preset_layout.addWidget(btn)
        
        layout.addWidget(preset_search)
        
        # 搜索历史
        history_group = QGroupBox("搜索历史")
        history_layout = QVBoxLayout(history_group)
        
        self.search_history = QListWidget()
        self.search_history.setMaximumHeight(150)
        history_layout.addWidget(self.search_history)
        
        layout.addWidget(history_group)
        
        return widget
        
    def create_content_panel(self):
        """创建内容面板"""
        tab_widget = QTabWidget()
        
        # 搜索结果标签页
        results_tab = self.create_results_tab()
        tab_widget.addTab(results_tab, "搜索结果")
        
        # 日志详情标签页
        detail_tab = self.create_detail_tab()
        tab_widget.addTab(detail_tab, "日志详情")
        
        # 统计分析标签页
        stats_tab = self.create_stats_tab()
        tab_widget.addTab(stats_tab, "统计分析")
        
        # 智能诊断标签页
        diagnosis_tab = self.create_diagnosis_tab()
        tab_widget.addTab(diagnosis_tab, "智能诊断")
        
        return tab_widget
        
    def create_results_tab(self):
        """创建搜索结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 结果信息
        info_layout = QHBoxLayout()
        self.results_info = QLabel("搜索结果: 0 条")
        info_layout.addWidget(self.results_info)
        info_layout.addStretch()
        
        self.export_results_btn = QPushButton("导出结果")
        info_layout.addWidget(self.export_results_btn)
        
        layout.addLayout(info_layout)
        
        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            "时间", "级别", "文件", "行号", "错误码", "消息", "原始内容"
        ])
        
        # 设置表格属性
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.results_table)
        
        return widget
        
    def create_detail_tab(self):
        """创建日志详情标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 详情信息
        info_layout = QHBoxLayout()
        self.detail_info = QLabel("选择一条日志查看详情")
        info_layout.addWidget(self.detail_info)
        info_layout.addStretch()
        
        self.copy_detail_btn = QPushButton("复制详情")
        info_layout.addWidget(self.copy_detail_btn)
        
        layout.addLayout(info_layout)
        
        # 详情文本
        self.detail_text = QPlainTextEdit()
        self.detail_text.setFont(QFont("Consolas", 10))
        
        # 添加语法高亮
        self.highlighter = LogHighlighter(self.detail_text.document())
        
        layout.addWidget(self.detail_text)
        
        return widget
        
    def create_stats_tab(self):
        """创建统计分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        # 级别统计
        level_stats = QGroupBox("日志级别统计")
        level_layout = QVBoxLayout(level_stats)
        self.level_stats_text = QTextEdit()
        self.level_stats_text.setMaximumHeight(150)
        level_layout.addWidget(self.level_stats_text)
        stats_layout.addWidget(level_stats)
        
        # 文件统计
        file_stats = QGroupBox("文件分布统计")
        file_layout = QVBoxLayout(file_stats)
        self.file_stats_text = QTextEdit()
        self.file_stats_text.setMaximumHeight(150)
        file_layout.addWidget(self.file_stats_text)
        stats_layout.addWidget(file_stats)
        
        # 错误码统计
        error_stats = QGroupBox("错误码统计")
        error_layout = QVBoxLayout(error_stats)
        self.error_stats_text = QTextEdit()
        self.error_stats_text.setMaximumHeight(150)
        error_layout.addWidget(self.error_stats_text)
        stats_layout.addWidget(error_stats)
        
        layout.addLayout(stats_layout)
        
        # 时间分布图表（文本形式）
        time_stats = QGroupBox("时间分布统计")
        time_layout = QVBoxLayout(time_stats)
        self.time_stats_text = QTextEdit()
        time_layout.addWidget(self.time_stats_text)
        layout.addWidget(time_stats)
        
        return widget
        
    def create_diagnosis_tab(self):
        """创建智能诊断标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 诊断控制
        control_layout = QHBoxLayout()
        self.diagnose_btn = QPushButton("开始诊断")
        control_layout.addWidget(self.diagnose_btn)
        
        self.export_diagnosis_btn = QPushButton("导出诊断报告")
        control_layout.addWidget(self.export_diagnosis_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 诊断结果
        self.diagnosis_text = QTextBrowser()
        self.diagnosis_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.diagnosis_text)
        
        return widget
        
    def setup_connections(self):
        """设置信号连接"""
        self.browse_btn.clicked.connect(self.browse_files)
        self.load_btn.clicked.connect(self.load_logs)
        self.reload_btn.clicked.connect(self.reload_logs)
        
        self.quick_search_btn.clicked.connect(self.quick_search)
        self.clear_search_btn.clicked.connect(self.clear_search)
        self.advanced_search_btn.clicked.connect(self.advanced_search)
        
        self.export_results_btn.clicked.connect(self.export_results)
        self.copy_detail_btn.clicked.connect(self.copy_detail)
        self.diagnose_btn.clicked.connect(self.run_diagnosis)
        self.export_diagnosis_btn.clicked.connect(self.export_diagnosis)
        
        self.results_table.itemSelectionChanged.connect(self.show_log_detail)
        self.search_history.itemClicked.connect(self.load_search_from_history)
        
        # 回车键搜索
        self.quick_search_edit.returnPressed.connect(self.quick_search)
        
    def browse_files(self):
        """浏览文件"""
        file_dialog = QFileDialog()
        path = file_dialog.getExistingDirectory(self, "选择日志目录")
        
        if path:
            self.file_path_edit.setText(path)
            
    def load_logs(self):
        """加载日志"""
        path = self.file_path_edit.text().strip()
        if not path:
            QMessageBox.warning(self, "警告", "请选择日志文件或目录")
            return
            
        if os.path.isdir(path):
            parser = LogParser()
            log_files = parser.scan_log_directory(path)
        elif os.path.isfile(path):
            log_files = [path]
        else:
            QMessageBox.warning(self, "警告", "指定的路径不存在")
            return
            
        if not log_files:
            QMessageBox.warning(self, "警告", "未找到日志文件")
            return
            
        # 加载日志
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.load_btn.setEnabled(False)
        
        try:
            parser = LogParser()
            all_entries = []
            
            for i, file_path in enumerate(log_files):
                self.progress_bar.setValue(int((i / len(log_files)) * 100))
                QApplication.processEvents()
                
                entries = parser.parse_log_file(file_path)
                all_entries.extend(entries)
            
            self.entries = all_entries
            self.searcher.load_entries(all_entries)
            
            self.progress_bar.setVisible(False)
            self.load_btn.setEnabled(True)
            
            # 显示所有日志
            self.display_results(all_entries[-1000:])  # 显示最近1000条
            self.update_statistics()
            
            self.statusBar().showMessage(f"加载完成，共 {len(all_entries)} 条日志")
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.load_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"加载日志时出错: {str(e)}")
            
    def reload_logs(self):
        """重新加载日志"""
        if self.file_path_edit.text().strip():
            self.load_logs()
        else:
            QMessageBox.information(self, "提示", "请先选择日志路径")
            
    def quick_search(self):
        """快速搜索"""
        query = self.quick_search_edit.text().strip()
        if not query or not self.entries:
            return
            
        # 添加到搜索历史
        self.add_to_search_history(f"快速搜索: {query}")
        
        results = self.searcher.quick_search(query, limit=5000)
        self.display_results(results)
        
    def clear_search(self):
        """清除搜索"""
        self.quick_search_edit.clear()
        self.keywords_edit.clear()
        self.exclude_edit.clear()
        self.regex_edit.clear()
        self.error_code_edit.clear()
        self.file_filter_edit.clear()
        
        if self.entries:
            self.display_results(self.entries[-1000:])
            
    def advanced_search(self):
        """高级搜索"""
        if not self.entries:
            QMessageBox.warning(self, "警告", "请先加载日志数据")
            return
            
        criteria = SearchCriteria()
        
        # 构建搜索条件
        keywords = self.keywords_edit.text().strip()
        if keywords:
            criteria.keywords = keywords.split()
            
        exclude = self.exclude_edit.text().strip()
        if exclude:
            criteria.exclude_keywords = exclude.split()
            
        regex = self.regex_edit.text().strip()
        if regex:
            criteria.regex_pattern = regex
            
        level = self.level_combo.currentText()
        if level != "全部":
            criteria.levels = [level]
            
        error_code = self.error_code_edit.text().strip()
        if error_code:
            criteria.error_codes = [code.strip() for code in error_code.split(',')]
            
        criteria.start_time = self.start_time_edit.dateTime().toPyDateTime()
        criteria.end_time = self.end_time_edit.dateTime().toPyDateTime()
        criteria.case_sensitive = self.case_sensitive_cb.isChecked()
        
        # 添加到搜索历史
        search_desc = f"高级搜索: {keywords or regex or error_code or level}"
        self.add_to_search_history(search_desc)
        
        # 执行搜索
        results = self.searcher.search(criteria)
        self.display_results(results)
        
    def search_recent_errors(self):
        """搜索最近错误"""
        if not self.entries:
            return
        results = self.searcher.get_recent_errors(hours=24, limit=500)
        self.display_results(results)
        self.add_to_search_history("预设搜索: 最近错误")
        
    def search_network_issues(self):
        """搜索网络问题"""
        if not self.entries:
            return
        criteria = SearchCriteria(keywords=['timeout', 'connection', 'network', '网络'])
        results = self.searcher.search(criteria)
        self.display_results(results)
        self.add_to_search_history("预设搜索: 网络问题")
        
    def search_mes_communication(self):
        """搜索MES通信问题"""
        if not self.entries:
            return
        criteria = SearchCriteria(keywords=['mes', 'post_json', 'http'])
        results = self.searcher.search(criteria)
        self.display_results(results)
        self.add_to_search_history("预设搜索: MES通信")
        
    def search_barcode_issues(self):
        """搜索条码问题"""
        if not self.entries:
            return
        criteria = SearchCriteria(keywords=['条码', 'barcode', 'sn', '二维码'])
        results = self.searcher.search(criteria)
        self.display_results(results)
        self.add_to_search_history("预设搜索: 条码问题")
        
    def search_device_issues(self):
        """搜索设备问题"""
        if not self.entries:
            return
        criteria = SearchCriteria(keywords=['设备', 'device', '相机', '光源', '温度'])
        results = self.searcher.search(criteria)
        self.display_results(results)
        self.add_to_search_history("预设搜索: 设备故障")
        
    def search_performance_issues(self):
        """搜索性能问题"""
        if not self.entries:
            return
        criteria = SearchCriteria(keywords=['timeout', '超时', 'slow', '慢', 'memory', '内存'])
        results = self.searcher.search(criteria)
        self.display_results(results)
        self.add_to_search_history("预设搜索: 性能问题")
        
    def display_results(self, results):
        """显示搜索结果"""
        self.results_table.setRowCount(len(results))
        self.results_info.setText(f"搜索结果: {len(results)} 条")
        
        for row, entry in enumerate(results):
            # 时间
            time_str = entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else ""
            self.results_table.setItem(row, 0, QTableWidgetItem(time_str))
            
            # 级别
            level_item = QTableWidgetItem(entry.level)
            if entry.level == 'ERROR':
                level_item.setBackground(QColor(139, 69, 19))
            elif entry.level == 'CRITICAL':
                level_item.setBackground(QColor(178, 34, 34))
            elif entry.level == 'WARNING':
                level_item.setBackground(QColor(255, 140, 0))
            self.results_table.setItem(row, 1, level_item)
            
            # 文件
            file_name = os.path.basename(entry.file_path)
            self.results_table.setItem(row, 2, QTableWidgetItem(file_name))
            
            # 行号
            self.results_table.setItem(row, 3, QTableWidgetItem(str(entry.line_number)))
            
            # 错误码
            self.results_table.setItem(row, 4, QTableWidgetItem(entry.error_code or ""))
            
            # 消息
            message = entry.message[:200] + "..." if len(entry.message) > 200 else entry.message
            self.results_table.setItem(row, 5, QTableWidgetItem(message))
            
            # 原始内容
            raw_content = entry.raw_line[:100] + "..." if len(entry.raw_line) > 100 else entry.raw_line
            self.results_table.setItem(row, 6, QTableWidgetItem(raw_content))
        
        self.results_table.resizeColumnsToContents()
        
    def show_log_detail(self):
        """显示日志详情"""
        current_row = self.results_table.currentRow()
        if current_row >= 0:
            # 获取当前显示的结果
            time_item = self.results_table.item(current_row, 0)
            level_item = self.results_table.item(current_row, 1)
            file_item = self.results_table.item(current_row, 2)
            line_item = self.results_table.item(current_row, 3)
            error_item = self.results_table.item(current_row, 4)
            message_item = self.results_table.item(current_row, 5)
            raw_item = self.results_table.item(current_row, 6)
            
            detail_text = f"时间: {time_item.text() if time_item else '未知'}\n"
            detail_text += f"级别: {level_item.text() if level_item else '未知'}\n"
            detail_text += f"文件: {file_item.text() if file_item else '未知'}\n"
            detail_text += f"行号: {line_item.text() if line_item else '未知'}\n"
            detail_text += f"错误码: {error_item.text() if error_item else '无'}\n"
            detail_text += f"\n消息内容:\n{message_item.text() if message_item else ''}\n"
            detail_text += f"\n原始内容:\n{raw_item.text() if raw_item else ''}"
            
            self.detail_text.setPlainText(detail_text)
            self.detail_info.setText(f"第 {current_row + 1} 条日志详情")
            
    def update_statistics(self):
        """更新统计信息"""
        if not self.entries:
            return
            
        stats = self.searcher.get_statistics()
        
        # 级别统计
        level_text = ""
        for level, count in stats['levels'].items():
            level_text += f"{level}: {count}\n"
        self.level_stats_text.setText(level_text)
        
        # 文件统计
        file_text = ""
        for file_name, count in list(stats['files'].items())[:10]:
            file_text += f"{file_name}: {count}\n"
        self.file_stats_text.setText(file_text)
        
        # 错误码统计
        error_text = ""
        for code, count in list(stats['error_codes'].items())[:10]:
            error_text += f"错误码{code}: {count}\n"
        self.error_stats_text.setText(error_text)
        
        # 时间分布
        time_text = "小时分布:\n"
        for hour, count in stats['hourly_distribution'].items():
            time_text += f"{hour}:00 - {count}条\n"
        self.time_stats_text.setText(time_text)
        
    def add_to_search_history(self, search_desc):
        """添加到搜索历史"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        history_item = f"[{timestamp}] {search_desc}"
        self.search_history.insertItem(0, history_item)
        
        # 限制历史记录数量
        if self.search_history.count() > 20:
            self.search_history.takeItem(20)
            
    def load_search_from_history(self, item):
        """从历史记录加载搜索"""
        # 这里可以实现从历史记录恢复搜索条件的逻辑
        pass
        
    def run_diagnosis(self):
        """运行智能诊断"""
        if not self.entries:
            QMessageBox.warning(self, "警告", "请先加载日志数据")
            return
            
        self.diagnose_btn.setEnabled(False)
        self.diagnose_btn.setText("诊断中...")
        
        try:
            report = self.smart_diagnosis.generate_diagnosis_report(self.entries)
            self.diagnosis_text.setText(report)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"诊断过程中出错: {str(e)}")
        finally:
            self.diagnose_btn.setEnabled(True)
            self.diagnose_btn.setText("开始诊断")
            
    def export_results(self):
        """导出搜索结果"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有可导出的结果")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出搜索结果", f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv", 
            "CSV Files (*.csv)")
            
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    # 写入表头
                    headers = ["时间", "级别", "文件", "行号", "错误码", "消息", "原始内容"]
                    f.write(','.join(f'"{h}"' for h in headers) + '\n')
                    
                    # 写入数据
                    for row in range(self.results_table.rowCount()):
                        row_data = []
                        for col in range(self.results_table.columnCount()):
                            item = self.results_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        f.write(','.join(f'"{data}"' for data in row_data) + '\n')
                        
                QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
                
    def copy_detail(self):
        """复制详情"""
        text = self.detail_text.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            self.statusBar().showMessage("详情已复制到剪贴板", 2000)
            
    def export_diagnosis(self):
        """导出诊断报告"""
        text = self.diagnosis_text.toPlainText()
        if not text:
            QMessageBox.warning(self, "警告", "请先运行诊断")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出诊断报告", f"diagnosis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 
            "Text Files (*.txt)")
            
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                QMessageBox.information(self, "成功", f"诊断报告已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("日志分析工具 - 开发人员版")
    
    window = DeveloperMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
