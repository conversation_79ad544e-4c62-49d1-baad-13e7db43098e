# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : http_mock.py
# Author     ：sch
# version    ：python 3.8
# Description：
"""

import uvicorn
from fastapi import FastAPI, Response

app = FastAPI()


@app.post("/ws/webservice-MacRunStatus")
def api1():
    ret1 = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
       <soap:Body>
          <ns2:MACRUNSTATUSResponse xmlns:ns2="http://service.webservice.services.intmes.com/">
             <return>
                <ERRCODE>NG</ERRCODE>
                <MACHINEID>?</MACHINEID>
                <STATUS>Y</STATUS>
                <UPLOADTIME>?</UPLOADTIME>
             </return>
          </ns2:MACRUNSTATUSResponse>
       </soap:Body>
    </soap:Envelope>"""

    return Response(media_type="application/xml", content=ret1)


@app.post("/MesWebService.asmx")
def api2():

    ret2 = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <soap:Body>
      <AOITESTinsertResponse xmlns="http://MesWebService.org/">
         <AOITESTinsertResult><![CDATA[<return><MACHINESN>string</MACHINESN><PROGRAMNAME>string</PROGRAMNAME><BARCODE>string</BARCODE><PANELBARCODE>1</PANELBARCODE><TIMESTMP>20210812155159</TIMESTMP><SATUS>OK</SATUS><MSG>插入成功</MSG></return>]]></AOITESTinsertResult>
      </AOITESTinsertResponse>
   </soap:Body>
</soap:Envelope>"""
    return Response(media_type="application/xml", content=ret2)


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8081)
