"""
# File       : main.py
# Time       ：2025/06/25 10:07
# Author     ："wxc"
# version    ：python 3.8
# Description：烟台力高新能源
"""
from typing import Any

from common import xcons, xrequest, xenum
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["烟台力高新能源", "yantailigaoxinnengyuan"],
        "version": "release v1.0.0.1",
        "device": "AIS303,AIS50X",
        "feature": ["条码校验", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-06-25 10:09 ATAOI_2019-40456：条码校验、上传数据
"""
    }
    form = {
        "station_id": {
            "ui_name": "工位",
            "value": "",
        },
    }
    other_form = {
        "upload_api_url": {
            "ui_name": "接口URL",
            "value": "",
        },
        "check_sn_url": {
            "ui_name": "过站接口URL",
            "value": "",
        }
    }
    combo = {
        "is_check_process": {
            "ui_name": "是否检查工序",
            "item": ["是", "否"],
            "value": "否"
        },
    }

    def __init__(self):
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        station_id = other_vo.get_value_by_cons_key("station_id")
        check_sn_url = other_vo.get_value_by_cons_key("check_sn_url")

        sn_list = other_vo.list_sn()
        for sn in sn_list:
            check_data = {
                "PROCEDURE": "MES.P_GET_CHECK_INFO",
                "FUNCTION_CODE": "CHECKSTATION",
                "INALLDATA": f"STATION={station_id};SN={sn};"
            }
            ret = xrequest.RequestUtil.post_json(check_sn_url, check_data)
            if not ret.get("success"):
                return self.x_response("false", f"mes接口异常, error:{ret.get('message')}")
        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("upload_api_url")
        station_id = data_vo.get_value_by_cons_key("station_id")
        is_check_process = data_vo.get_value_by_cons_key("is_check_process")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        proram_name = pcb_entity.project_name
        test_start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        test_end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        is_auto = "0"
        if is_check_process == "是":
            is_auto = "1"
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            sn = board_entity.barcode
            test_result = board_entity.get_repair_result("Pass", "NG")

            test_items = []
            defect_items = []
            for comp_entity in board_entity.yield_comp_entity():
                defect_items.append({
                    "sn": sn,
                    "defectcode": comp_entity.repair_ng_code,
                    "defectlocation": comp_entity.designator
                })
                for alg in comp_entity.yield_alg_entity():
                    test_items.append({
                        "itemType": "",
                        "itemName": alg.test_name,
                        "actualValue": alg.test_val,
                        "testResult": "Pass" if alg.result == "0" else "NG",
                        "threshold": f"阈值[{alg.min_threshold}]:[{alg.max_threshold}]"
                    })
            board_data = {
                "sn": sn,
                "station": station_id,
                "testResult": test_result,
                "testStartTime": test_start_time,
                "testEndTime": test_end_time,
                "programName": proram_name,
                "hardVersion": "",
                "isAuto": is_auto,
                "testItems": test_items,
                "defectItems": defect_items
            }
            ret = xrequest.RequestUtil.post_json(api_url, board_data)
            if not ret.get("success"):
                return self.x_response("false", f"mes接口异常, error:{ret.get('message', '')}")
            return self.x_response()
