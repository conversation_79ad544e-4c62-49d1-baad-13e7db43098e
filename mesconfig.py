# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2022/12/26 下午5:20
# Author     ：sch
# version    ：python 3.8
# Description： 主窗口
"""
import os
import sys

from PyQt5.QtCore import QTranslator
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction, QMessageBox

from common import xutil
from views.main_window import MainWindow


class TrayApp:
    def __init__(self):
        self.app = QApplication(sys.argv)

        pwd = os.getcwd()
        icon_path = f"{pwd}/static/mes.png"

        file_config = xutil.FileUtil.load_config_file()
        set_lang = file_config.get("app_setting", {}).get("setting_lang", "ZH")
        if set_lang == "EN":
            trans = QTranslator()
            trans.load('./static/mes_translate_v2')
            self.app.installTranslator(trans)

        self.app.setQuitOnLastWindowClosed(False)

        icon = QIcon(icon_path)  # 可替换为本地 QIcon("xxx.png")
        self.tray_icon = QSystemTrayIcon(icon)
        self.tray_icon.setToolTip("托盘应用演示")

        self.window = MainWindow(self.tray_icon)

        try:
            icon = QIcon(icon_path)  # 使用资源文件中的路径
            self.window.setWindowIcon(icon)
        except Exception as e:
            print(f"Error loading icon: {e}")

        # 创建右键菜单
        self.menu = QMenu()

        self.show_action = QAction("显示窗口")
        self.hide_action = QAction("隐藏窗口")
        # self.message_action = QAction("弹出通知")
        self.about_action = QAction("关于")
        self.quit_action = QAction("退出")

        self.show_action.triggered.connect(self.window.showNormal)
        self.hide_action.triggered.connect(self.window.hide)
        # self.message_action.triggered.connect(self.show_message)
        self.about_action.triggered.connect(self.show_about)
        self.quit_action.triggered.connect(self.exit_app)

        self.menu.addAction(self.show_action)
        self.menu.addAction(self.hide_action)
        self.menu.addSeparator()
        # self.menu.addAction(self.message_action)
        # self.menu.addAction(self.about_action)
        self.menu.addSeparator()
        self.menu.addAction(self.quit_action)

        self.tray_icon.setContextMenu(self.menu)
        self.tray_icon.activated.connect(self.on_tray_activated)
        self.tray_icon.show()

    def show_message(self):
        self.tray_icon.showMessage(
            "通知",
            "这是系统托盘弹出的消息通知。",
            QSystemTrayIcon.Information,
            3000
        )

    def show_about(self):
        QMessageBox.information(
            None,
            "关于",
            "Python版本Mes配置器"
        )

    def on_tray_activated(self, reason):
        if reason == QSystemTrayIcon.DoubleClick:
            self.window.showNormal()
            self.window.activateWindow()

    def exit_app(self):
        self.window.close_event_by_tray_app()
        # self.tray_icon.hide()
        # QApplication.quit()

    def run(self):
        self.window.show()
        sys.exit(self.app.exec_())


if __name__ == '__main__':
    # app = QApplication(sys.argv)
    #
    # file_config = xutil.FileUtil.load_config_file()
    # set_lang = file_config.get("app_setting", {}).get("setting_lang", "ZH")
    # if set_lang == "EN":
    #     trans = QTranslator()
    #     trans.load('./static/mes_translate_v2')
    #     app.installTranslator(trans)
    #
    # ex = MainWindow()
    #
    # try:
    #     pwd = os.getcwd()
    #     icon_path = f"{pwd}/static/mes.png"
    #
    #     icon = QIcon(icon_path)  # 使用资源文件中的路径
    #     ex.setWindowIcon(icon)
    # except Exception as e:
    #     print(f"Error loading icon: {e}")
    #
    # sys.exit(app.exec_())
    TrayApp().run()
