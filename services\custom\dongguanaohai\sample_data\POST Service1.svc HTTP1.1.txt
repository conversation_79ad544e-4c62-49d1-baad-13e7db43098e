服务器：http://**************:9999/Service1.svc


## BarcodeRelation  ---------------绑定接口
POST /Service1.svc HTTP/1.1
Content-Type: text/xml; charset=utf-8
SOAPAction: "http://tempuri.org/IService1/BarcodeRelationOneToMany"
Host: **************:9999
Content-Length: 841
Expect: 100-continue
Accept-Encoding: gzip, deflate
Connection: Keep-Alive

### 请求参数
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><BarcodeRelationOneToMany xmlns="http://tempuri.org/"><configdtstr>[{"name":"UserNo","value":"1305"},{"name":"FactoryNo","value":"3"},{"name":"FilePath","value":"C:\\Users\\<USER>\\Desktop"},{"name":"MAC","value":"CC-96-E5-48-5F-A2"},{"name":"IPAddress","value":"**************"},{"name":"lineId","value":"29"},{"name":"workStationId","value":"9"},{"name":"MachineID","value":"42"},{"name":"IsDebug","value":"false"},{"name":"ServerIP","value":"**************"},{"name":"ServerDatabaseName","value":"AOHAIMES"},{"name":"WCFUrl","value":"http://**************:9999/Service1.svc"},{"name":"rulebindinginfoId","value":"0"}]</configdtstr><serialNumber>A001</serialNumber><relationBarcodeNos>B1,B2</relationBarcodeNos></BarcodeRelationOneToMany></s:Body></s:Envelope>


### 响应参数
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <BarcodeRelationOneToManyResponse xmlns="http://tempuri.org/">
            <BarcodeRelationOneToManyResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>false</a:string>
                <a:string>条码:A001不存在注册信息,请确认!</a:string>
            </BarcodeRelationOneToManyResult>
        </BarcodeRelationOneToManyResponse>
    </s:Body>
</s:Envelope>



## CheckAndwriteResult  ---------------上传数据接口
POST /Service1.svc HTTP/1.1
Content-Type: text/xml; charset=utf-8
SOAPAction: "http://tempuri.org/IService1/CheckAndwriteResult"
Host: **************:9999
Content-Length: 859
Expect: 100-continue
Accept-Encoding: gzip, deflate

### 请求参数
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><CheckAndwriteResult xmlns="http://tempuri.org/"><configdtstr>[{"name":"UserNo","value":"1305"},{"name":"FactoryNo","value":"3"},{"name":"FilePath","value":"C:\\Users\\<USER>\\Desktop"},{"name":"MAC","value":"CC-96-E5-48-5F-A2"},{"name":"IPAddress","value":"**************"},{"name":"lineId","value":"29"},{"name":"workStationId","value":"9"},{"name":"MachineID","value":"42"},{"name":"IsDebug","value":"false"},{"name":"ServerIP","value":"**************"},{"name":"ServerDatabaseName","value":"AOHAIMES"},{"name":"WCFUrl","value":"http://**************:9999/Service1.svc"},{"name":"rulebindinginfoId","value":"0"}]</configdtstr><serialNumber>A001</serialNumber><Result>true</Result><allResultValues>{"comp01": "PASS"}</allResultValues></CheckAndwriteResult></s:Body></s:Envelope>


### 响应参数
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CheckAndwriteResultResponse xmlns="http://tempuri.org/">
            <CheckAndwriteResultResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>false</a:string>
                <a:string>验证失败:当前站假如是首站需要配置工单流程绑定!传入参数:A001;{"comp01": "PASS"}线体ID:29</a:string>
            </CheckAndwriteResultResult>
        </CheckAndwriteResultResponse>
    </s:Body>
</s:Envelope>
