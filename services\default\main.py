# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test.py
# Time       ：2023/4/7 上午9:04
# Author     ：sch
# version    ：python 3.8
# Description：默认版本
"""
import json
import time
from typing import Any

from common.xutil import x_response
from vo.mes_vo import DataVo, OtherVo, ComboVo
from engine.MesEngine import BaseEngine

AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "2", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "3", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "5", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "6", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "7", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "8", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "9", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "10", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "11", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "12", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "13", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "14", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "15", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "17", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "23", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}

AIS_30X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "不合格", "custom_code": "1", "custom_str": "NG"},
    "2": {"standard": "多锡", "custom_code": "2", "custom_str": "ExcessSolder"},
    "3": {"standard": "连锡", "custom_code": "3", "custom_str": "Bridge"},
    "4": {"standard": "少锡", "custom_code": "4", "custom_str": "InsufficientSolder"},
    "5": {"standard": "孔洞", "custom_code": "5", "custom_str": "PinHole"},
    "6": {"standard": "未出脚", "custom_code": "6", "custom_str": "NoPin"},
    "7": {"standard": "异常出脚", "custom_code": "7", "custom_str": "ExceptionPin"},
    "8": {"standard": "缺件", "custom_code": "8", "custom_str": "MissingPart"},
    "9": {"standard": "偏位", "custom_code": "9", "custom_str": "ShiftPart"},
    "10": {"standard": "露铜", "custom_code": "10", "custom_str": "ExposeCopper"},
    "11": {"standard": "错件", "custom_code": "11", "custom_str": "WrongPart"},
    "12": {"standard": "极性错误", "custom_code": "12", "custom_str": "ReversePart"},
    "13": {"standard": "条码识别错误", "custom_code": "13", "custom_str": "BarcodeRecognition"},
    "14": {"standard": "数据错误", "custom_code": "14", "custom_str": "CountError"},
    "15": {"standard": "定位错误", "custom_code": "15", "custom_str": "Locate"},
    "16": {"standard": "流程错误", "custom_code": "16", "custom_str": "ProcessError"},
    "17": {"standard": "锡珠", "custom_code": "17", "custom_str": "SolderBall"},
    "18": {"standard": "拼版特征不匹配", "custom_code": "18", "custom_str": "FeatureMismatch"},
}

AIS_20X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
    "-1": {"standard": "未检查", "custom_code": "-1", "custom_str": "NoCheck"},
    "4": {"standard": "反件", "custom_code": "4", "custom_str": "ReversePart"},
    "5": {"standard": "错件", "custom_code": "5", "custom_str": "WrongPart"},
    "20": {"standard": "引脚未插", "custom_code": "20", "custom_str": "Pin Not Found"},
    "21": {"standard": "不是引脚", "custom_code": "21", "custom_str": "NoPin"},
    "101": {"standard": "多件", "custom_code": "101", "custom_str": "多件"},
    "102": {"standard": "浮高", "custom_code": "102", "custom_str": "Part Lift"},
    "103": {"standard": "歪斜", "custom_code": "103", "custom_str": "Part Tilt"},
    "104": {"standard": "条码错误", "custom_code": "104", "custom_str": "Barcode Error"},
    "105": {"standard": "内部错误", "custom_code": "105", "custom_str": "Internal Error"},
    "80": {"standard": "多涂", "custom_code": "80", "custom_str": "MoreCoating"},
    "81": {"standard": "少涂", "custom_code": "81", "custom_str": "LessCoating"},
    "82": {"standard": "气泡", "custom_code": "82", "custom_str": "BubbleCoating"},
}


class DefaultEngine(BaseEngine):
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        return x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        return x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        return x_response()

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        username, password = other_dao.get_login_info()

        if password != "admin":
            return x_response("false", f"密码错误")

        return x_response()

    def combo_index_changed(self, combo_vo: ComboVo, other_param: Any):
        return x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, other_param: Any):
        time.sleep(0.1)
        new_items = ["Python", "Java", "Golang", "Golang111"]

        return x_response("true", json.dumps({
            "new_items": new_items
        }))
