# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/1/11 下午2:52
# Author     ：sch
# version    ：python 3.8
# Description：岳阳东颂
"""

from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "yueyangdongsong release v1.0.0.2",
        "device": "401",
        "feature": ["获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-01-11 15:27  获取条码，上传数据
date: 2024-01-26 16:41  优化获取工单列表  
""", }

    form = {
        'tenant_id': {
            'ui_name': 'tenantId',
            'value': '102',
        },
        'p_line': {
            'ui_name': 'p_line',
            'value': '',
        },
        'device_id': {
            'ui_name': '设备编号',
            'value': '',
        },
        'qty': {
            'ui_name': '拼板数',
            'value': '',
        },
    }

    combo = {
        'mo_list': {
            'ui_name': '工单列表',
            "item": [],
            'value': '',
        }
    }

    other_form = {
        'save_data_url': {
            'ui_name': '上传数据接口URL',
            'value': '',
        },
        'get_sn_url': {
            'ui_name': '获取条码接口URL',
            'value': '',
        },
        'get_mo_list_url': {
            'ui_name': '刷新工单接口URL',
            'value': '',
        },
    }

    button = {
        'refresh_mo_list': {
            'ui_name': "刷新工单列表"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_id = data_vo.get_value_by_cons_key('device_id')
        save_data_url = data_vo.get_value_by_cons_key('save_data_url')
        mo = data_vo.get_value_by_cons_key('mo_list')

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        start_date = test_time[:10]
        start_time = test_time[11:]
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)[11:]

        exception_list = []
        comp_total_number = 0

        sn_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if barcode:
                sn_list.append(barcode)

            comp_total_number += board_entity.comp_total_number

            comp_exception_list = []

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_exception_list.append({
                        "point": comp_entity.designator,
                        "defect": comp_entity.repair_ng_str
                    })

            exception_list.append({
                "pcb_sn": barcode,
                "exceptions": comp_exception_list
            })

        mo_job_map = xutil.CacheUtil.get('mo_job_map', {})

        if not mo_job_map:
            return self.x_response("false", f"请先获取工单列表")

        job_id = mo_job_map.get(mo, '')

        # 2. 上传数据
        param = {
            "machine_no": device_id,
            "jobid": job_id,
            "date": start_date,
            "time_start": start_time,
            "time_end": end_time,
            "qty": pcb_entity.board_count,
            "sn_list": sn_list,
            "point_per": comp_total_number,
            "exceptions": exception_list
        }

        ret = xrequest.RequestUtil.post_json(save_data_url, param)
        if ret.get("code") != 200:
            return self.x_response("false", f"接口响应异常，上传数据失败，error: {ret.get('message')}")

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        get_sn_url = other_vo.get_value_by_cons_key('get_sn_url')
        p_line = other_vo.get_value_by_cons_key('p_line')
        qty = other_vo.get_value_by_cons_key('qty')
        tenant_id = other_vo.get_value_by_cons_key('tenant_id')

        barcode_list = other_vo.get_origin_param('barcodeList')

        if barcode_list:
            qty = len(barcode_list) - 2
            self.log.info(f"拼板数根据barcodeList自动获取！")

        pcb_sn = other_vo.get_pcb_sn()

        get_param = {
            "pcbaCode": pcb_sn,
            "pline": p_line,
            "qty": qty,
            "tenantid": tenant_id,
        }
        ret = xrequest.RequestUtil.get(get_sn_url, get_param)

        if not ret.get('success'):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('message')}")

        sn_list = ret.get('result', [])

        return self.x_response("true", ",".join(sn_list))

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        get_mo_list_url = btn_vo.get_value_by_cons_key('get_mo_list_url')
        p_line = btn_vo.get_value_by_cons_key('p_line')
        tenant_id = btn_vo.get_value_by_cons_key('tenant_id')

        btn_key = btn_vo.get_btn_key()
        if btn_key == 'refresh_mo_list':
            ret = xrequest.RequestUtil.get(get_mo_list_url, {'pline': p_line, 'tenantid': tenant_id})

            if not ret.get('success'):
                return self.x_response("false", f"mes接口异常，刷新工单失败，error：{ret.get('message')}")

            mo_job_id_list = ret.get('result')

            mo_job_map = {}
            mo_list = []
            for i in mo_job_id_list:
                mo = i.get('mo')
                if not mo:
                    self.log.warning(f"工单号为空！")
                    continue
                mo_job_map[mo] = i.get('thisid')
                mo_list.append(mo)

            if not mo_list:
                return self.x_response("false", f"未获取到工单列表！")

            xutil.CacheUtil.set("mo_job_map", mo_job_map)

            mo_list_item = getattr(other_param, 'combo_mo_list')

            mo_list_item.clear()
            for i in mo_list:
                mo_list_item.addItem(i)

            other_param.config_data['combo']['mo_list']['item'] = mo_list
            other_param.config_data['combo']['mo_list']['value'] = mo_list[0]
            other_param.save_config_data_to_file()

        return self.x_response()
