# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/11 上午10:04
# Author     ：sch
# version    ：python 3.8
# Description：越南亚旭
"""
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "yuenanyaxu release v1.0.0.5",
        "device": "430",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-11 15:02  从mes获取条码, 条码校验, 上传数据
date: 2023-12-19 14:52  修复从mes获取条码的接口返回
date: 2023-12-19 18:13  优化校验失败不发送mes功能
date: 2023-12-20 16:47  按拼板上传整板结果
date: 2024-02-04 14:59  增加上传误报数据，接口：`cfUploadJudgmentResult`
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://***********:801/WebService_VN.asmx",
        },

        "station": {
            "ui_name": "站点",
            "value": "",
        },
        "aoi_sn": {
            "ui_name": "AOI编号",
            "value": "",
        },
        "machine_name": {
            "ui_name": "机器名",
            "value": "",
        },
        "operator": {
            "ui_name": "测试operator",
            "value": "",
        },
    }

    combo = {
        "factory": {
            "ui_name": "厂别",
            "item": ["VN1", "VN2"],
            "value": "VN1",
        },
        "repair_in": {
            "ui_name": "RepairIn",
            "item": ["Y", "N"],
            "value": "Y",
        },
    }

    def __init__(self):
        self.ERROR_MAP["AIS203/AIS303/AIS40X/AIS50x"] = {
            "1": {"standard": "漏件", "custom_code": "AOI1", "custom_str": "MissingPart"},
            "2": {"standard": "错件", "custom_code": "AOI2", "custom_str": "WrongPart"},
            "3": {"standard": "反件", "custom_code": "AOI3", "custom_str": "ReversePart"},
            "4": {"standard": "立碑", "custom_code": "AOI4", "custom_str": "Tomstone"},
            "5": {"standard": "偏移", "custom_code": "AOI5", "custom_str": "ShiftPart"},
            "6": {"standard": "翻转", "custom_code": "AOI6", "custom_str": "UpsideDown"},
            "7": {"standard": "浮高", "custom_code": "AOI7", "custom_str": "LiftedPackage"},
            "8": {"standard": "损件", "custom_code": "AOI8", "custom_str": "Broken"},
            "9": {"standard": "露铜", "custom_code": "AOI9", "custom_str": "ExposeCopper"},
            "10": {"standard": "少锡", "custom_code": "AOI10", "custom_str": "InsufficientSolder"},
            "11": {"standard": "多锡", "custom_code": "AOI11", "custom_str": "ExcessSolder"},
            "12": {"standard": "未出脚", "custom_code": "AOI12", "custom_str": "NoPin"},
            "13": {"standard": "孔洞", "custom_code": "AOI13", "custom_str": "PinHole"},
            "14": {"standard": "连锡", "custom_code": "AOI14", "custom_str": "Bridge"},
            "15": {"standard": "锡珠", "custom_code": "AOI15", "custom_str": "SolderBall"},
            "16": {"standard": "翘脚", "custom_code": "AOI16", "custom_str": "LiftedLead"},
            "17": {"standard": "弯脚", "custom_code": "AOI17", "custom_str": "ShiftedLead"},
            "18": {"standard": "异物", "custom_code": "AOI18", "custom_str": "ForeignMaterial"},
            "19": {"standard": "条码识别", "custom_code": "AOI19", "custom_str": "BarcodeRecognition"},
            "20": {"standard": "Marker搜索", "custom_code": "AOI20", "custom_str": "MarkerSearch"},
            "21": {"standard": "多件", "custom_code": "AOI21", "custom_str": "ForeignPart"},
            "22": {"standard": "溢胶", "custom_code": "AOI22", "custom_str": "Overflow"},
            "23": {"standard": "虚焊", "custom_code": "AOI23", "custom_str": "IncompleteWeld"},
            "24": {"standard": "脏污", "custom_code": "AOI24", "custom_str": "Dirty"},
            "25": {"standard": "坏板", "custom_code": "AOI25", "custom_str": "BadPanel"},
            "26": {"standard": "定位", "custom_code": "AOI26", "custom_str": "Locate"},
            "27": {"standard": "数目错误", "custom_code": "AOI27", "custom_str": "CountError"},
            "28": {"standard": "少涂/多涂", "custom_code": "AOI28", "custom_str": "LessMoreCoating"},
            "29": {"standard": "少涂", "custom_code": "AOI29", "custom_str": "LessCoating"},
            "30": {"standard": "多涂", "custom_code": "AOI30", "custom_str": "MoreCoating"},
            "31": {"standard": "气泡", "custom_code": "AOI31", "custom_str": "Bubble"},
            "32": {"standard": "划痕", "custom_code": "AOI32", "custom_str": "Scratch"},
            "33": {"standard": "距离", "custom_code": "AOI33", "custom_str": "Distance"},
            "34": {"standard": "锡膏检测", "custom_code": "AOI34", "custom_str": "SPIDetect"},
            "35": {"standard": "共线性", "custom_code": "AOI35", "custom_str": "Collinearity"},
            "36": {"standard": "CPU插针检测", "custom_code": "AOI36", "custom_str": "CPUPinDetect"}
        }

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        factory = other_vo.get_value_by_cons_key("factory")
        api_url = other_vo.get_value_by_cons_key("api_url")

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "LinkSN": pcb_sn,
            "pchFactory": factory
        }
        ret = xrequest.RequestUtil.get(f"{api_url}/cfGetSNByLabellerSN", param)
        ret_data = ret.get('result', [{}])[0]
        ret_code = ret_data.get("RET_CODE")
        ret_list_sn = ret_data.get('RET_VALUE')
        if str(ret_code) != "1":
            return self.x_response("false", f"Mes接口异常，获取条码失败，error：{ret_list_sn}")

        # ret_list = ret_value.split(",")

        # r = f"[{ret_value}]"
        # ret_list_sn = json.loads(r)
        ret_sn = []
        for item in ret_list_sn:
            ret_sn.append(item.get("SN"))

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        factory = other_vo.get_value_by_cons_key("factory")
        api_url = other_vo.get_value_by_cons_key("api_url")
        station = other_vo.get_value_by_cons_key("station")
        aoi_sn = other_vo.get_value_by_cons_key("aoi_sn")

        for sn in other_vo.list_sn():
            check_param = {
                "BarCode": sn,
                "ItemName": station,
                "AOI_NO": aoi_sn,
                "pchFactory": factory,
            }
            ret = xrequest.RequestUtil.get(f"{api_url}/cfCheckValidBarCode", check_param)
            ret_data = ret.get('result', [{}])[0]
            ret_code = ret_data.get("RET_CODE")
            ret_value = ret_data.get('RET_VALUE')
            if str(ret_code) != "1":
                return self.x_response("false", f"Mes接口异常，条码校验失败，error：{ret_value}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        factory = data_vo.get_value_by_cons_key("factory")
        api_url = data_vo.get_value_by_cons_key("api_url")
        station = data_vo.get_value_by_cons_key("station")
        # aoi_sn = data_dao.get_value_by_cons_key("aoi_sn")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        repair_in = data_vo.get_value_by_cons_key("repair_in")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        return_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            self.log.info(board_entity)
            # repair_result = board_entity.get_repair_result("T", "F")
            repair_result = pcb_entity.get_repair_result("T", "F")

            comp_ng_list = []
            comp_ng_list1 = []
            repass_data = []

            for comp_entity in board_entity.yield_comp_entity():

                # if comp_entity.is_robot_ng():
                comp_tag = comp_entity.designator
                comp_part = comp_entity.part
                comp_code = comp_entity.repair_ng_code
                robot_ng_code = comp_entity.robot_ng_code
                robot_ng_str = comp_entity.robot_ng_str

                comp_r = comp_entity.get_final_result("T", "T", "F")
                comp_ng_list.append(comp_r)

                if comp_entity.is_repair_ng():
                    comp_ng_list1.append(f"<{board_no}:{comp_tag}:{comp_part}:{comp_code}>")

                if comp_entity.get_final_result() == "REPASS":
                    repass_data.append(f"<{board_no}:{comp_tag}:{robot_ng_str}:{robot_ng_code}>")

            comp_ng_str = "".join(comp_ng_list)
            comp_ng_str1 = "".join(comp_ng_list1)

            test_data_str = f";;;{barcode};;;{repair_result};;;{operator};{comp_ng_str};0;0;0;{comp_ng_str1};;"

            data_param = {
                "MachineType": machine_name,
                "Dlen": str(len(test_data_str)),
                "ItemName": station,
                "REPAIRIN": repair_in,
                "pchFactory": factory,
                "TestData": test_data_str,
            }

            ret = xrequest.RequestUtil.get(f"{api_url}/cfUploadTestResult", data_param)
            ret_data = ret.get('result', [{}])[0]
            ret_code = ret_data.get("RET_CODE")
            ret_value = ret_data.get('RET_VALUE')
            if str(ret_code) != "1":
                return_res = self.x_response("false", f"Mes接口异常，上传数据失败，error：{ret_value}")

            if board_entity.get_final_result() == "REPASS":
                self.log.info(f"上传误报数据中...")
                repass_param = {
                    "Barcode": barcode,
                    "TestData": "".join(repass_data),
                    "OperatorID": operator,
                    "ComputerName": machine_name,
                    "pchFactory": factory,
                }

                ret2 = xrequest.RequestUtil.get(f"{api_url}/cfUploadJudgmentResult", repass_param)
                ret_data2 = ret2.get('result', [{}])[0]
                ret_code2 = ret_data2.get("RET_CODE")
                ret_value2 = ret_data2.get('RET_VALUE')
                if str(ret_code2) != "1":
                    return_res = self.x_response("false", f"Mes接口异常，上传数据失败，error：{ret_value2}")

        return return_res
