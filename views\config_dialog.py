# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : config_dialog.py
# Time       ：2023/10/20 下午5:09
# Author     ：sch
# version    ：python 3.8
# Description：设置-个性化配置项
"""
from PyQt5.QtWidgets import *

from common import xenum
from templates.config_dialog import Ui_ConfigDialog


class CommonConfigViews(QDialog, Ui_ConfigDialog):
    def __init__(self, main_window):
        super(CommonConfigViews, self).__init__()
        self.setupUi(self)

        self.main_window = main_window

        check_barcode_setting1 = self.read_common_config_by_key(
            "check_barcode_setting1",
            xenum.CheckSetting1.CheckAll
        )
        check_barcode_setting2 = self.read_common_config_by_key(
            "check_barcode_setting2",
            xenum.CheckSetting2.CheckFailNotSendData
        )
        sendmes_setting1 = self.read_common_config_by_key(
            "sendmes_setting1",
            xenum.SendMesSetting1.SaveAll
        )
        sendmes_setting2 = self.read_common_config_by_key(
            "sendmes_setting2",
            xenum.SendMesSetting2.SaveAll
        )
        sendmes_setting3 = self.read_common_config_by_key(
            "sendmes_setting3",
            xenum.SendMesSetting3.NotSend
        )

        self.check_barcode_setting1.setCurrentText(check_barcode_setting1)
        self.check_barcode_setting2.setCurrentText(check_barcode_setting2)
        self.sendmes_setting1.setCurrentText(sendmes_setting1)
        self.sendmes_setting2.setCurrentText(sendmes_setting2)
        self.sendmes_setting3.setCurrentText(sendmes_setting3)

    def read_common_config_by_key(self, config_key, default_value):
        """
        读取通用配置项参数
        :param config_key:
        :param default_value:
        :return:
        """
        return self.main_window.config_data.get("common_config", {}).get(config_key, default_value)

    def exec_dialog(self):
        """
        展示窗口
        :return:
        """
        ret = self.exec_()

        if ret == 1:
            # 保存设置
            check_barcode_setting1_text = self.check_barcode_setting1.currentText()
            check_barcode_setting2_text = self.check_barcode_setting2.currentText()
            sendmes_setting1_text = self.sendmes_setting1.currentText()
            sendmes_setting2_text = self.sendmes_setting2.currentText()
            sendmes_setting3_text = self.sendmes_setting3.currentText()

            self.main_window.config_data["common_config"]["check_barcode_setting1"] = check_barcode_setting1_text
            self.main_window.config_data["common_config"]["check_barcode_setting2"] = check_barcode_setting2_text
            self.main_window.config_data["common_config"]["sendmes_setting1"] = sendmes_setting1_text
            self.main_window.config_data["common_config"]["sendmes_setting2"] = sendmes_setting2_text
            self.main_window.config_data["common_config"]["sendmes_setting3"] = sendmes_setting3_text

            self.main_window.save_config_data_to_file()
