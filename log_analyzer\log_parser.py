#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志解析模块
支持多种日志格式的解析和处理
"""

import os
import re
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path


@dataclass
class LogEntry:
    """日志条目数据类"""
    timestamp: Optional[datetime]
    level: str
    message: str
    line_number: int
    file_path: str
    raw_line: str
    module: Optional[str] = None
    function: Optional[str] = None
    error_code: Optional[str] = None


class LogParser:
    """日志解析器"""
    
    def __init__(self):
        # 支持的日志格式正则表达式
        self.log_patterns = {
            # 标准格式: 2024-01-01 12:00:00 - [line:123] - INFO: message
            'standard': re.compile(
                r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
                r'.*?\[line:(?P<line>\d+)\].*?'
                r'(?P<level>DEBUG|INFO|WARNING|ERROR|CRITICAL):\s*'
                r'(?P<message>.*)'
            ),
            # 简单格式: 2024-01-01 12:00:00 ---Info--- message
            'simple': re.compile(
                r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
                r'.*?---(?P<level>\w+)---\s*'
                r'(?P<message>.*)'
            ),
            # HTML格式: <span style='color:red'>2024-01-01 12:00:00 message</span>
            'html': re.compile(
                r"<span.*?color:(?P<color>\w+).*?>"
                r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\s*'
                r'(?P<message>.*?)</span>'
            ),
            # 通用格式: 任何包含时间戳的行
            'generic': re.compile(
                r'(?P<timestamp>\d{4}[-/]\d{2}[-/]\d{2}[\s\T]\d{2}:\d{2}:\d{2})'
                r'.*?(?P<message>.*)'
            )
        }
        
        # 错误码模式
        self.error_code_pattern = re.compile(r'error\s*[:\s]*(\d+|[A-Z]+\d+)', re.IGNORECASE)
        
        # 日志级别映射
        self.level_mapping = {
            'info': 'INFO',
            'debug': 'DEBUG', 
            'warning': 'WARNING',
            'warn': 'WARNING',
            'error': 'ERROR',
            'err': 'ERROR',
            'critical': 'CRITICAL',
            'fatal': 'CRITICAL',
            'red': 'ERROR',
            'green': 'INFO',
            'yellow': 'WARNING'
        }

    def parse_log_file(self, file_path: str, encoding: str = 'utf-8') -> List[LogEntry]:
        """解析日志文件"""
        entries = []
        
        if not os.path.exists(file_path):
            return entries
            
        try:
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    entry = self._parse_log_line(line, line_num, file_path)
                    if entry:
                        entries.append(entry)
                        
        except Exception as e:
            print(f"解析日志文件失败 {file_path}: {e}")
            
        return entries

    def _parse_log_line(self, line: str, line_number: int, file_path: str) -> Optional[LogEntry]:
        """解析单行日志"""
        # 尝试各种格式
        for pattern_name, pattern in self.log_patterns.items():
            match = pattern.search(line)
            if match:
                return self._create_log_entry(match, pattern_name, line, line_number, file_path)
        
        # 如果没有匹配到任何格式，创建一个基本条目
        return LogEntry(
            timestamp=None,
            level='UNKNOWN',
            message=line,
            line_number=line_number,
            file_path=file_path,
            raw_line=line
        )

    def _create_log_entry(self, match: re.Match, pattern_name: str, 
                         line: str, line_number: int, file_path: str) -> LogEntry:
        """创建日志条目"""
        groups = match.groupdict()
        
        # 解析时间戳
        timestamp = None
        if 'timestamp' in groups and groups['timestamp']:
            timestamp = self._parse_timestamp(groups['timestamp'])
        
        # 解析日志级别
        level = 'INFO'  # 默认级别
        if 'level' in groups and groups['level']:
            level = self.level_mapping.get(groups['level'].lower(), groups['level'].upper())
        elif 'color' in groups and groups['color']:
            level = self.level_mapping.get(groups['color'].lower(), 'INFO')
        
        # 提取消息
        message = groups.get('message', line).strip()
        
        # 提取错误码
        error_code = None
        error_match = self.error_code_pattern.search(message)
        if error_match:
            error_code = error_match.group(1)
        
        return LogEntry(
            timestamp=timestamp,
            level=level,
            message=message,
            line_number=line_number,
            file_path=file_path,
            raw_line=line,
            error_code=error_code
        )

    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳"""
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y/%m/%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%d %H:%M:%S +08:00'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str.split(' +')[0], fmt)
            except ValueError:
                continue
        
        return None

    def scan_log_directory(self, directory: str, extensions: List[str] = None) -> List[str]:
        """扫描日志目录，返回日志文件列表"""
        if extensions is None:
            extensions = ['.log', '.txt']
        
        log_files = []
        directory_path = Path(directory)
        
        if not directory_path.exists():
            return log_files
        
        for ext in extensions:
            log_files.extend(directory_path.rglob(f'*{ext}'))
        
        return [str(f) for f in sorted(log_files)]

    def get_log_summary(self, entries: List[LogEntry]) -> Dict:
        """获取日志摘要统计"""
        if not entries:
            return {}
        
        summary = {
            'total_entries': len(entries),
            'level_counts': {},
            'error_codes': {},
            'time_range': {},
            'files': set()
        }
        
        timestamps = []
        
        for entry in entries:
            # 统计日志级别
            level = entry.level
            summary['level_counts'][level] = summary['level_counts'].get(level, 0) + 1
            
            # 统计错误码
            if entry.error_code:
                summary['error_codes'][entry.error_code] = summary['error_codes'].get(entry.error_code, 0) + 1
            
            # 收集时间戳
            if entry.timestamp:
                timestamps.append(entry.timestamp)
            
            # 收集文件
            summary['files'].add(entry.file_path)
        
        # 时间范围
        if timestamps:
            summary['time_range'] = {
                'start': min(timestamps),
                'end': max(timestamps)
            }
        
        summary['files'] = list(summary['files'])
        
        return summary
