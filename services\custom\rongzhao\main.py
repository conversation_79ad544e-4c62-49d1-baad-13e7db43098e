# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/29 下午3:03
# Author     ：sch
# version    ：python 3.8
# Description：融兆
"""
import json
from collections import defaultdict
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "rongzhao release v1.0.0.5",
        "device": "203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-29 16:31  条码校验, 上传数据
date: 2024-01-02 17:51  参数调整
date: 2024-01-02 18:46  参数调整
date: 2024-10-30 17:54  根据不良代码统计位号
""", }
    form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://*************:8081/EQInterface/StartTest",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://*************:8081/EQInterface/EndTestWithData",
        },
        "station": {
            "ui_name": "站点号",
            "value": "",
        },
        "operator": {
            "ui_name": "测试人员编号",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station = other_vo.get_value_by_cons_key("station")
        operator = other_vo.get_value_by_cons_key("operator")

        error_msg = ""
        for sn in other_vo.list_sn():
            check_param = {
                "STATION": station,
                "PROSN": sn,
                "OPERATER": operator,
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, {}, params=check_param)
            if not ret.get('success'):
                error_msg = f"mes接口异常，条码校验失败，error：{ret.get('MsgContent')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        station = data_vo.get_value_by_cons_key("station")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            comp_data_list2 = []

            err_data_map = defaultdict(list)

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    repair_ng_code = comp_entity.repair_ng_code
                    repair_ng_str = comp_entity.repair_ng_str

                    alg_val = ''
                    for alg_entity in comp_entity.yield_alg_entity():
                        if alg_entity.result != '0':
                            alg_val = alg_entity.test_val

                    err_data_map[repair_ng_code].append(f"{comp_tag}_{repair_ng_str}")

                    comp_data_list2.append({
                        "TESTCODE": repair_ng_code,
                        "TKEY1": comp_tag,
                        "TVALUE1": alg_val
                    })

            comp_data_list1 = []
            for k, v in err_data_map.items():
                comp_data_list1.append({
                    "DEFCODE": k,
                    "DEDESC": ",".join(v),
                })

            data_param = {
                "STATION": station,
                "PROSN": barcode,
                "OPERATER": operator,
                "JUDAGECODE": board_entity.get_repair_result("$OK$", "$NOK$"),
                "ERRDATA": json.dumps(comp_data_list1, ensure_ascii=False, separators=((",", ":"))),
                "TESTDATA": json.dumps(comp_data_list2, ensure_ascii=False, separators=((",", ":"))),
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, {}, params=data_param)
            if not ret.get('success'):
                error_msg = f"mes接口异常，条码校验失败，error：{ret.get('MsgContent')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
