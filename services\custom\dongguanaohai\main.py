# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/11 下午4:17
# Author     ：sch
# version    ：python 3.8
# Description：东莞奥海： https://jira.cvte.com/browse/ATAOI_2019-37458
"""
import os.path
from typing import Any
from xml.etree import ElementTree

from common import xutil, xrequest, xcons
from engine.MesEngine import ErrorMapEngine
from services.custom.dongguanaohai import ah_module
from vo.mes_vo import DataVo, OtherVo


def parse_soap_xml(xml_string: str) -> dict:
    """
    解析 SOAP XML 字符串，并返回结果。
    Args:
        xml_string: SOAP XML 字符串。
    Returns:
        一个字典，包含解析后的结果，包括 header message 和 body success 状态。
        如果解析失败，返回 None。
    """
    try:
        root = ElementTree.fromstring(xml_string)
        # 命名空间
        ns = {
            's': 'http://schemas.xmlsoap.org/soap/envelope/',
            'h': 'http://tempuri.org/',
            '': 'http://tempuri.org/'  # 默认命名空间
        }
        # 获取 Header 中的 Message
        header_message_element = root.find('.//s:Header/h:Message', namespaces=ns)
        header_message = header_message_element.text if header_message_element is not None else None
        # 获取 Body 中的 IsSuccessed
        is_successed_element = root.find('.//s:Body/ResultMessage/IsSuccessed', namespaces=ns)
        is_successed = is_successed_element.text.lower() == 'true' if is_successed_element is not None else None
        return {
            'header_message': header_message,
            'is_successed': is_successed
        }
    except ElementTree.ParseError as e:
        print(f"XML解析错误: {e}")
        return {}
    except Exception as e:
        print(f"解析过程中发生错误: {e}")
        return {}


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanaohai release v1.0.0.8",
        "device": "AIS203",
        "feature": ["获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-04-11 16:19  上传测试数据
date: 2025-04-15 10:55  增加配置项：是否绑定条码
date: 2025-04-18 14:20  增加绑定类型：器件条码/拼板条码
date: 2025-04-21 15:36  更换绑定条码的接口为：BarcodeRelationOneToMany
date: 2025-04-24 14:50  器件条码增加按位号排序
date: 2025-06-27 16:57  ATAOI_2019-37458: 需求变更
date: 2025-06-30 17:52  ATAOI_2019-37458: bugfix: 条码校验时，接口异常
date: 2025-07-11 11:34  取不到板边条码时，用第一拼版条码
""", }

    other_form = {
        "server_ip": {
            "ui_name": "ServerIP",
            "value": "**************",
        },
        "server_database_name": {
            "ui_name": "ServerDatabaseName",
            "value": "AOHAIMES",
        },
        "file_path": {
            "ui_name": "FilePath",
            "value": r"C:\Users\<USER>\Desktop",
        },
        "mac": {
            "ui_name": "MAC",
            "value": "CC-96-E5-48-5F-A2",
        },
        "ip_address": {
            "ui_name": "IPAddress",
            "value": "**************"
        },
        "wcf_url": {
            "ui_name": "WCFUrl",
            "value": "http://**************:9999/Service1.svc",
        },
        "rule_binding_info_id": {
            "ui_name": "rulebindinginfoId",
            "value": "0",
        }
    }

    other_combo = {
        "is_debug": {
            "ui_name": "IsDebug",
            "item": ["true", "false"],
            "value": "false",
        },
        "check_work_station": {
            "ui_name": "checkWorkStation",
            "item": ["true", "false"],
            "value": "true",
        },
    }

    form = {
        "user_no": {
            "ui_name": "UserNo",
            "value": "1305",
        },
        "factory_no": {
            "ui_name": "FactoryNo",
            "value": "3",
        },
        "line_id": {
            "ui_name": "lineId",
            "value": "29",
        },
        "work_station_id": {
            "ui_name": "workStationId",
            "value": "9",
        },
        "machine_id": {
            "ui_name": "MachineID",
            "value": "42",
        },
    }

    combo = {
        "is_bind_sn": {
            "ui_name": "是否绑定条码",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "bind_type": {
            "ui_name": "绑定类型",
            "item": ["器件条码", "拼板条码"],
            "value": "拼板条码",
        },
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        server_ip = other_vo.get_value_by_cons_key("server_ip")
        server_database_name = other_vo.get_value_by_cons_key("server_database_name")
        file_path = other_vo.get_value_by_cons_key("file_path")
        mac = other_vo.get_value_by_cons_key("mac")
        ip_address = other_vo.get_value_by_cons_key("ip_address")
        wcf_url = other_vo.get_value_by_cons_key("wcf_url")
        rule_binding_info_id = other_vo.get_value_by_cons_key("rule_binding_info_id")
        is_debug = other_vo.get_value_by_cons_key("is_debug")
        user_no = other_vo.get_value_by_cons_key("user_no")
        factory_no = other_vo.get_value_by_cons_key("factory_no")
        line_id = other_vo.get_value_by_cons_key("line_id")
        work_station_id = other_vo.get_value_by_cons_key("work_station_id")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        config_list = [
            {
                "name": "UserNo",
                "value": user_no
            },
            {
                "name": "FactoryNo",
                "value": factory_no
            },
            {
                "name": "FilePath",
                "value": file_path
            },
            {
                "name": "MAC",
                "value": mac
            },
            {
                "name": "IPAddress",
                "value": ip_address
            },
            {
                "name": "lineId",
                "value": line_id
            },
            {
                "name": "workStationId",
                "value": work_station_id
            },
            {
                "name": "MachineID",
                "value": machine_id
            },
            {
                "name": "IsDebug",
                "value": is_debug
            },
            {
                "name": "ServerIP",
                "value": server_ip
            },
            {
                "name": "ServerDatabaseName",
                "value": server_database_name
            },
            {
                "name": "WCFUrl",
                "value": wcf_url
            },
            {
                "name": "rulebindinginfoId",
                "value": rule_binding_info_id
            }
        ]

        pcb_sn = other_vo.get_pcb_sn()

        check_work_station = other_vo.get_value_by_cons_key("check_work_station")

        get_sn_str = ah_module.get_sn_template.format(**{
            "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
            "serialNumber": pcb_sn,
            "checkWorkStation": check_work_station
        })

        ret_str1 = xrequest.RequestUtil.post_soap(
            wcf_url,
            get_sn_str,
            soap_action="http://tempuri.org/IService1/GetMultiboardList"
        )

        root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)

        get_result = root1[0][0][0]

        r1 = get_result[0].text
        r2 = get_result[1].text

        if r1 != "true":
            return self.x_response("false", f"mes接口异常，error：{r2}")

        return self.x_response("true", r2)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        server_ip = other_vo.get_value_by_cons_key("server_ip")
        server_database_name = other_vo.get_value_by_cons_key("server_database_name")
        file_path = other_vo.get_value_by_cons_key("file_path")
        mac = other_vo.get_value_by_cons_key("mac")
        ip_address = other_vo.get_value_by_cons_key("ip_address")
        wcf_url = other_vo.get_value_by_cons_key("wcf_url")
        rule_binding_info_id = other_vo.get_value_by_cons_key("rule_binding_info_id")
        is_debug = other_vo.get_value_by_cons_key("is_debug")
        user_no = other_vo.get_value_by_cons_key("user_no")
        factory_no = other_vo.get_value_by_cons_key("factory_no")
        line_id = other_vo.get_value_by_cons_key("line_id")
        work_station_id = other_vo.get_value_by_cons_key("work_station_id")
        machine_id = other_vo.get_value_by_cons_key("machine_id")

        config_list = [
            {
                "name": "UserNo",
                "value": user_no
            },
            {
                "name": "FactoryNo",
                "value": factory_no
            },
            {
                "name": "FilePath",
                "value": file_path
            },
            {
                "name": "MAC",
                "value": mac
            },
            {
                "name": "IPAddress",
                "value": ip_address
            },
            {
                "name": "lineId",
                "value": line_id
            },
            {
                "name": "workStationId",
                "value": work_station_id
            },
            {
                "name": "MachineID",
                "value": machine_id
            },
            {
                "name": "IsDebug",
                "value": is_debug
            },
            {
                "name": "ServerIP",
                "value": server_ip
            },
            {
                "name": "ServerDatabaseName",
                "value": server_database_name
            },
            {
                "name": "WCFUrl",
                "value": wcf_url
            },
            {
                "name": "rulebindinginfoId",
                "value": rule_binding_info_id
            }
        ]

        sn_list = other_vo.list_sn()

        err_msg_list = []

        for sn in sn_list:
            check_route_str = ah_module.check_route_template.format(**{
                "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
                "serialNumber": sn,
            })
            ret_str2 = xrequest.RequestUtil.post_soap(
                wcf_url,
                check_route_str,
                soap_action="http://tempuri.org/IService1/CheckRouteForSerialNumber"
            )

            root2 = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
            get_result = root2[0][0][0]

            r1 = get_result[0].text
            r2 = get_result[1].text

            if r1 != "true":
                err_msg_list.append(f"sn：{sn}，error：{r2}")

        if err_msg_list:
            err_msg = "mes接口异常" + "\n".join(err_msg_list)
            return self.x_response("false", err_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        绑定类型选择器件条码，使用PCB条码作为SN，去调一次过站接口即可
        绑定类型选择拼板条码时，使用拼板条码作为SN，去调用过站接口
        """
        server_ip = data_vo.get_value_by_cons_key("server_ip")
        server_database_name = data_vo.get_value_by_cons_key("server_database_name")
        file_path = data_vo.get_value_by_cons_key("file_path")
        mac = data_vo.get_value_by_cons_key("mac")
        ip_address = data_vo.get_value_by_cons_key("ip_address")
        wcf_url = data_vo.get_value_by_cons_key("wcf_url")
        rule_binding_info_id = data_vo.get_value_by_cons_key("rule_binding_info_id")
        is_debug = data_vo.get_value_by_cons_key("is_debug")
        user_no = data_vo.get_value_by_cons_key("user_no")
        factory_no = data_vo.get_value_by_cons_key("factory_no")
        line_id = data_vo.get_value_by_cons_key("line_id")
        work_station_id = data_vo.get_value_by_cons_key("work_station_id")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        is_bind_sn = data_vo.get_value_by_cons_key("is_bind_sn")
        bind_type = data_vo.get_value_by_cons_key("bind_type")
        save_path = data_vo.get_value_by_cons_key("save_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.get_unique_sn()

        pcb_src_img = pcb_entity.get_pcb_t_image()

        ret_res = self.x_response()

        if os.path.exists(pcb_src_img):
            unique_sn = pcb_entity.get_unique_sn()
            time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

            if not unique_sn:
                unique_sn = time_file

            full_save_path = f"{save_path}/{time_file[:8]}"
            xutil.FileUtil.ensure_dir_exist(full_save_path)

            pcb_dst_img = os.path.join(full_save_path, f"{unique_sn}.jpg")
            xutil.FileUtil.copy_file(pcb_src_img, pcb_dst_img)
        else:
            self.log.warning(f"找不到需要上传的整板图！{pcb_src_img}")

        config_list = [
            {
                "name": "UserNo",
                "value": user_no
            },
            {
                "name": "FactoryNo",
                "value": factory_no
            },
            {
                "name": "FilePath",
                "value": file_path
            },
            {
                "name": "MAC",
                "value": mac
            },
            {
                "name": "IPAddress",
                "value": ip_address
            },
            {
                "name": "lineId",
                "value": line_id
            },
            {
                "name": "workStationId",
                "value": work_station_id
            },
            {
                "name": "MachineID",
                "value": machine_id
            },
            {
                "name": "IsDebug",
                "value": is_debug
            },
            {
                "name": "ServerIP",
                "value": server_ip
            },
            {
                "name": "ServerDatabaseName",
                "value": server_database_name
            },
            {
                "name": "WCFUrl",
                "value": wcf_url
            },
            {
                "name": "rulebindinginfoId",
                "value": rule_binding_info_id
            }
        ]
        self.log.info(f"bind type: {bind_type}")

        bind_sn_list = []

        if bind_type == "器件条码":
            comp_barcode_map = pcb_entity.get_all_comp_barcode()
            self.log.info(f"获取到的器件条码：{comp_barcode_map}")

            all_comp_list = []

            for comp_list in comp_barcode_map.values():
                for item in comp_list:
                    all_comp_list.append(item)

            all_comp_list.sort(key=lambda x: x.get("comp_tag"))

            for item in all_comp_list:
                comp_barcode = item.get("comp_barcode")
                bind_sn_list.append(comp_barcode)

        else:
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                barcode = board_entity.barcode
                bind_sn_list.append(barcode)

        bind_param_str = ah_module.barcode_relation_one_to_many_template.format(**{
            "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
            "board_sn_str": ",".join(bind_sn_list),
            "serialNumber": pcb_sn,
        })

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT1)

        if is_bind_sn == "Yes":
            # 1. 上传条码绑定关系
            ret = xrequest.RequestUtil.post_soap(
                wcf_url,
                bind_param_str,
                soap_action="http://tempuri.org/IService1/BarcodeRelationOneToMany"
            )

            r1, r2 = ah_module.parse_ret(ret)
            if r1 != "true":
                ret_res = self.x_response("false", f"mes接口异常，上传条码绑定关系失败，error：{r2}")

        comp_ng_map_pcb = {}
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_ng_map = {}
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                if comp_entity.is_robot_ng():
                    comp_ng_map[comp_tag] = comp_entity.get_final_result("PASS", "PASS", "FAIL")
                    comp_ng_map_pcb[comp_tag] = comp_entity.get_final_result("PASS", "PASS", "FAIL")

                if comp_entity.is_repair_ng():

                    test_item_list = [
                        f"Start,{start_time},End,{end_time}"
                    ]

                    for alg_entity in comp_entity.yield_alg_entity():
                        test_name = alg_entity.test_name
                        alg_max = alg_entity.max_threshold
                        alg_min = alg_entity.min_threshold
                        alg_val = alg_entity.test_val
                        alg_result = "PASS" if alg_entity.result == "0" else "FAIL"

                        test_item_list.append(
                            f"{test_name},{alg_max},{alg_min},,{alg_val},{alg_result}"
                        )
                    test_item_str = ";".join(test_item_list) + ";"

                    comp_src_img = comp_entity.image_path

                    if os.path.exists(comp_src_img):
                        comp_base_str = xutil.OtherUtil.file_to_base64_content(comp_src_img)
                        self.log.info(f"由于图片数据过多，所以此处不打印图片base64数据！{comp_src_img}")
                    else:
                        self.log.warning(f"找不到需要上传的器件图！{comp_tag}")
                        comp_base_str = ""

                    check_result_str = ah_module.check_and_write_result_template.format(**{
                        "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
                        "serialNumber": barcode,
                        "Result": comp_entity.get_final_result("true", "true", "false"),
                        "allResultValues": test_item_str,
                        "badness": comp_entity.repair_ng_str,
                        "badnessSite": comp_tag,
                        "fileContent": comp_base_str
                    })

                    check_result_str_log = ah_module.check_and_write_result_template.format(**{
                        "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
                        "serialNumber": barcode,
                        "Result": comp_entity.get_final_result("true", "true", "false"),
                        "allResultValues": test_item_str,
                        "badness": comp_entity.repair_ng_str,
                        "badnessSite": comp_tag,
                        "fileContent": ""
                    })

                    self.log.info(f"请求URL：{wcf_url} 请求参数：\n{check_result_str_log}")

                    ret_str3 = xrequest.RequestUtil.post_soap(
                        wcf_url,
                        check_result_str,
                        soap_action="http://tempuri.org/IService1/CheckAndwriteResultByAOI",
                        log_number=0
                    )

                    # root3 = xutil.XmlUtil.get_xml_root_by_str(ret_str3)
                    # item_header = root3[0][0].text
                    # item_body = root3[1][0][0].text
                    ret_data = parse_soap_xml(ret_str3)

                    if not ret_data.get('is_successed'):
                        ret_res = self.x_response(
                            "false",
                            f"mes接口异常，上传器件图片失败，error：{ret_data.get('header_message')}"
                        )

            if bind_type == "拼板条码":
                # 2. 上传过站数据
                ret2 = xrequest.RequestUtil.post_soap(
                    wcf_url,
                    ah_module.check_and_write_template.format(**{
                        "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
                        "serialNumber": barcode,
                        "Result": board_entity.get_repair_result("true", "false"),
                        "allResultValues": xutil.OtherUtil.obj_to_json(comp_ng_map)
                    }),
                    soap_action="http://tempuri.org/IService1/CheckAndwriteResult"
                )
                r1_data, r2_data = ah_module.parse_ret(ret2)
                if r1_data != "true":
                    ret_res = self.x_response("false", f"mes接口异常，上传过站数据失败，error：{r2_data}")

        if bind_type == "器件条码":
            # 2. 上传过站数据
            ret2 = xrequest.RequestUtil.post_soap(
                wcf_url,
                ah_module.check_and_write_template.format(**{
                    "configdtstr": xutil.OtherUtil.obj_to_json(config_list),
                    "serialNumber": pcb_sn,
                    "Result": pcb_entity.get_repair_result("true", "false"),
                    "allResultValues": xutil.OtherUtil.obj_to_json(comp_ng_map_pcb)
                }),
                soap_action="http://tempuri.org/IService1/CheckAndwriteResult"
            )
            r1_data, r2_data = ah_module.parse_ret(ret2)
            if r1_data != "true":
                ret_res = self.x_response("false", f"mes接口异常，上传过站数据失败，error：{r2_data}")

        return ret_res
