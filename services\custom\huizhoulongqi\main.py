# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/7 下午5:07
# Author     ：sch
# version    ：python 3.8
# Description：惠州龙旗
"""
from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import BaseEngine

global_data = {}


class Engine(BaseEngine):
    version = {
        "title": "huizhoulongqi release v1.0.0.3",
        "device": "40x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-07 17:16  init
date: 2023-06-08 10:21  登录，条码校验，上传数据
date: 2023-07-19 17:52  按整板上传
""", }

    form = {
        "window_ip": {
            "ui_name": "window中转(dll)地址",
            "value": "127.0.0.1"
        },
        "user_code": {
            "ui_name": "用户代码",
            "value": ""
        },
        "password": {
            "ui_name": "用户密码",
            "value": ""
        },
        "res_code": {
            "ui_name": "资源代码",
            "value": ""
        },
        "jig_code": {
            "ui_name": "治具编号",
            "value": ""
        },
        "operator": {
            "ui_name": "操作人员",
            "value": ""
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登录"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        window_ip = data_vo.get_value_by_cons_key("window_ip")
        res_code = data_vo.get_value_by_cons_key("res_code")
        operator = data_vo.get_value_by_cons_key("operator")

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        robot_ng_code_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not barcode:
                barcode = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    # 检测NG
                    robot_ng_code = comp_entity.robot_ng_code
                    if robot_ng_code not in robot_ng_code_list:
                        robot_ng_code_list.append(robot_ng_code)

        window_param = {
            "type": 2,
            "request_param": {
                "iSN": pcb_sn,
                "iResCode": res_code,
                "iOperator": operator,
                "iResult": pcb_entity.get_robot_result("OK", "NG"),
                "iErrCode": ",".join(robot_ng_code_list)
            }
        }

        ret = xrequest.SocketUtil.x_socket_send_data(window_ip, window_param)
        ret_str = ret.get('string')
        if ret_str != "true":
            return self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret_str}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        if btn_key == "login_btn":
            window_ip = btn_vo.get_value_by_cons_key("window_ip")
            user_code = btn_vo.get_value_by_cons_key("user_code")
            password = btn_vo.get_value_by_cons_key("password")
            res_code = btn_vo.get_value_by_cons_key("res_code")
            jig_code = btn_vo.get_value_by_cons_key("jig_code")

            window_param = {
                "type": 10,
                "request_param": {
                    "iUserCode": user_code,
                    "iResCode": res_code,
                    "iPassWord": password,
                    "iJigCode": jig_code
                }
            }

            ret = xrequest.SocketUtil.x_socket_send_data(window_ip, window_param)
            ret_str = ret.get('string')
            if ret_str != 'true':
                return self.x_response("false", f"mes接口异常，登录失败，error：{ret_str}")

            global_data["is_login"] = True

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        res_code = other_vo.get_value_by_cons_key("res_code")

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        for sn in other_vo.list_sn():
            window_param = {
                "type": 1,
                "request_window": {
                    "iSN": sn,
                    "iResCode": res_code
                }
            }
            ret = xrequest.SocketUtil.x_socket_send_data(window_ip, window_param)
            ret_str = ret.get('string')
            if ret_str != 'true':
                return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret_str}")

        return self.x_response()
