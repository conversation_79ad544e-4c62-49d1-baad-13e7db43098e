# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/18 上午9:26
# Author     ：sch
# version    ：python 3.8
# Description：星驱动科技
"""
from typing import Any

from common import xrequest, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "xingqudong release v1.0.0.2",
        "device": "40x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-18 10:20  init
date: 2023-05-18 15:08  条码校验、上传数据
date: 2023-05-26 15:11  接口参数bugfix
""", }

    form = {
        "api_host": {
            "ui_name": "接口地址",
            "value": "http://127.00..1:8081"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")

        save_url = f"{api_host}/sis/restful/printedCircuitBoard/saveProductTrackHistory"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        product_list = []

        error_code = []
        error_tag = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            if not barcode:
                barcode = xutil.DateUtil.get_datetime_now()

            for comp_entity in board_entity.yield_comp_entity():

                if not comp_entity.repair_result:
                    error_code.append(comp_entity.repair_ng_code)
                    error_tag.append(comp_entity.designator)

            product_list.append({
                "statusCode": board_entity.get_repair_result("1", "2"),
                "productNum": barcode
            })

        save_param = {
            "siteCode": "Z117",
            "operationCode": "OP1010",
            "time": str(pcb_entity.get_cycle_time()),
            "productNumStatusCode": product_list,
            "errorCode": ",".join(error_code),
            "locationResult": ",".join(error_tag),
        }

        ret = xrequest.RequestUtil.post_json(save_url, save_param)
        if ret.get('code') != 'success':
            return self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret.get('message')}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")

        list_sn = other_vo.list_sn()

        check_param = {
            "siteCode": "Z117",
            "operationCode": "OP1010",
            "productNum": list_sn[0]
        }

        check_url = f"{api_host}/sis/restful/printedCircuitBoard/inboundVerify"

        ret = xrequest.RequestUtil.get(check_url, check_param)
        if ret.get('code') != 'success':
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('message')}")

        return self.x_response()
