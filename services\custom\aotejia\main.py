# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/1 上午9:50
# Author     ：sch
# version    ：python 3.8
# Description：奥特佳
"""
import binascii
import socket
import struct
import time
from typing import Any

from common import xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine

req_type_map = {
    "无载体": 0,
    "载体与产品绑定": 1,
    "通过载体码返回产品标识": 2,
    "载体与产品解绑": 3,
}


def bytes_to_hex(b: bytes) -> str:
    """
    字节转16进制
    :return:
    """
    return b.hex()


def cons_shi_to_hex(string_shi: int) -> bytes:
    """
    十进制转十六进制
    :return:
    """
    hex_str = hex(string_shi)[2:]

    return binascii.a2b_hex(hex_str.zfill(2))


def cons_shi_to_hex_str(string_shi: int) -> str:
    """
    十进制转十六进制
    :return:
    """
    hex_str = hex(string_shi)[2:]

    return hex_str.zfill(2).upper()


def send_data_to_socket_server(socket_ip: str, socket_port: int, param: bytes, timeout=5) -> bytes:
    """
    发送数据到socket服务端
    """

    req_list = []
    for i in param:
        req_list.append(cons_shi_to_hex_str(i))

    log.info(f"请求地址: {socket_ip}:{socket_port} 请求参数:{'-'.join(req_list)}  len:{len(param)}")

    sk = socket.socket()
    sk.settimeout(timeout)
    sk.connect((socket_ip, socket_port))
    sk.send(param)
    ret_str = sk.recv(1024)
    # ret_str = ret_str.decode('utf-8')
    log.info(f"接口响应字节: {ret_str}")

    log.logger.info('------------接口响应解析--------')
    for ix, i in enumerate(ret_str):
        log.logger.info(f'位数：{ix} 十进制：{i} 十六进制：{cons_shi_to_hex_str(i)}')
    log.logger.info('------------接口响应解析--------')

    sk.close()
    return ret_str


def len_add_data(d_len: int, d_data: str) -> bytes:
    f = struct.pack(f'{d_len}s', d_data.encode('utf8'))
    return cons_shi_to_hex(d_len) + cons_shi_to_hex(len(d_data)) + f


def get_data_form_bytes(bytes_data: bytes) -> str:
    d_len = bytes_data[1]
    bytes_d = bytes_data[2:2 + d_len]
    return bytes_d.decode('utf-8')


def r_fill_zero(s: str, number: int):
    """
    给字符串补零  [右边补零]
    :param s:
    :param number:
    :return:
    """
    origin_l = len(s)
    need_fill_i = number - origin_l

    if origin_l > number:
        return s[:number]
    else:
        ret_str = s + chr(0) * need_fill_i
        return ret_str


csv_board_template = """程序名,{pcb_project_name}
测试时间,{pcb_test_time}
操作员,{pcb_repair_user}
大板条码,{pcb_sn}
整板结果,{pcb_final_result}
拼板条码,{board_sn}
拼板序号,{board_no}
拼板结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件复判NG数量,{board_comp_user_ng_number}

CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult,CompImage{CompData}
"""

csv_comp_template = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""


sn_product_map = {}

global_data = {}


class Engine(BaseEngine):
    version = {
        "title": "aotejia release v1.0.0.12",
        "device": "303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-01 09:51  init
date: 2023-12-08 11:04  xxx
date: 2023-12-11 10:26  修改功能码
date: 2023-12-12 16:08  修改数据结构
date: 2023-12-13 09:19  查询上线码接口补齐184位
date: 2023-12-13 14:52  上传数据时产品标识取上报上线接口对应返回的产品标识
date: 2024-07-01 09:49  优化:ftp增加重传机制
date: 2024-07-01 14:25  bugfix
""", }

    form = {
        "tcp_host": {
            "ui_name": "TCP Host",
            "value": "",
        },
        "tcp_port": {
            "ui_name": "TCP Port",
            "value": "",
        },
        # "tcp_flag": {
        #     "ui_name": "通讯标识",
        #     "value": "",
        # },
        "line_id": {
            "ui_name": "产线ID(十进制)",
            "value": "",
        },
        "device_id": {
            "ui_name": "设备ID(十进制)",
            "value": "",
        },
        "username": {
            "ui_name": "员工编号",
            "value": "",
        },
    }

    combo = {
        "req_type": {
            "ui_name": "载体方式",
            "item": ["无载体", "载体与产品绑定", "通过载体码返回产品标识", "载体与产品解绑"],
            "value": "无载体",
        },
        "req_symbol": {
            "ui_name": "上传数据的请求信号",
            "item": ["true", "false"],
            "value": "true",
        },
        "ftp_retries": {
            "ui_name": "ftp重传次数",
            "item": ["1", "2", "3", "5", "8", "10"],
            "value": "3",
        }
    }

    other_form = {
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "value": "/MES/SMT"
        },
        "concat_path": {
            "ui_name": "FTP拼接路径前缀",
            "value": "D:\AOI"
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        tcp_host = other_vo.get_value_by_cons_key("tcp_host")
        tcp_port = other_vo.get_value_by_cons_key("tcp_port")
        req_type = other_vo.get_value_by_cons_key("req_type")

        line_id = other_vo.get_value_by_cons_key("line_id")
        device_id = other_vo.get_value_by_cons_key("device_id")

        try:
            line_id = int(line_id)
        except Exception as err:
            return self.x_response("false", f"产线ID必须为数字！error:{err}")

        try:
            device_id = int(device_id)
        except Exception as err:
            return self.x_response("false", f"设备ID必须为数字！error:{err}")

        error_map = {
            1: "可生产",
            16: "生产线中设备不存在",
            17: "工单对应工艺下无该设备",
            18: "工单不存在",
            19: "不符合时间锁",
            20: "不符合物料锁",
            21: "产品不存在",
            22: "非混产模式",
            23: "不符合人员技能锁",
            240: "无型号",
            241: "不符合工序锁",
            242: "未处于返修模式下出现不合格",
            243: "互锁表达式异常",
            244: "产品已下线",
            245: "产品已报废",
            246: "无生产计划",
            247: "条码校验失败",
            248: "物料缺料",
            249: "烧录次数达到3次",
            255: "服务端未定义异常而不能生产",
        }

        # try:
        #     tcp_flag = int(tcp_flag)
        # except Exception as err:
        #     return self.x_response("false", f"通信标识必须为数字！error：{err}")
        try:
            tcp_port = int(tcp_port)
        except Exception as err:
            return self.x_response("false", f"TCP Port必须为数字！error：{err}")

        # if not line_id:
        #     line_id = chr(0)
        # else:
        #     line_id = line_id[:1]
        #
        # if not device_id:
        #     device_id = chr(0)
        # else:
        #     device_id = device_id[:1]

        self.log.info(f"{line_id=} {device_id=}")

        cache_sn_list = global_data.get("cache_sn_list", [])

        error_msg = ""
        for sn in other_vo.list_sn():
            param = {
                "foo1": struct.pack('>h', 184),
                "foo2": binascii.a2b_hex("0100"),
                "foo3": struct.pack('>h', 0),  # 通讯标识
                "foo4": cons_shi_to_hex(line_id),
                "foo5": cons_shi_to_hex(device_id),
                "foo6": struct.pack('>h', 1),  # 请求信号
                "foo7": len_add_data(128, sn),
                "foo8": len_add_data(38, ''),
                "foo9": struct.pack('>h', req_type_map.get(req_type, 0)),
                "foo10": binascii.a2b_hex('0000')
            }

            param_str = b''
            for i in param.values():
                param_str += i

            ret_bytes = send_data_to_socket_server(tcp_host, tcp_port, param_str)
            ret_code = ret_bytes[2:4]
            self.log.info(f"ret code: {ret_code}")
            code_int = struct.unpack('>h', ret_code)[0]

            product_flag = get_data_form_bytes(ret_bytes[38:38+40])  # 获取到的产品标识
            self.log.info(f"获取到的产品标识是：{product_flag}")
            sn_product_map[sn] = product_flag
            cache_sn_list.append(sn)

            if code_int != 1:
                error_msg = f"mes接口异常，上报上线码失败，errorCode：{code_int} errorMsg:{error_map.get(code_int, '未知异常码！')}"

        cache_sn_list = cache_sn_list[-500:]
        global_data['cache_sn_list'] = cache_sn_list

        tmp_list = list(sn_product_map.keys())
        for k in tmp_list:
            if k not in cache_sn_list:
                del sn_product_map[k]

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        tcp_host = data_vo.get_value_by_cons_key("tcp_host")
        tcp_port = data_vo.get_value_by_cons_key("tcp_port", to_int=True)
        req_symbol = data_vo.get_value_by_cons_key("req_symbol")
        line_id = data_vo.get_value_by_cons_key("line_id")
        device_id = data_vo.get_value_by_cons_key("device_id")
        username = data_vo.get_value_by_cons_key("username")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        concat_path = data_vo.get_value_by_cons_key("concat_path")
        ftp_retries = data_vo.get_value_by_cons_key("ftp_retries", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        req_symbol_int = 1 if req_symbol == 'true' else 0
        cycle_time = int(pcb_entity.get_cycle_time())

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        try:
            line_id = int(line_id)
        except Exception as err:
            return self.x_response("false", f"产线ID必须为数字！error:{err}")

        try:
            device_id = int(device_id)
        except Exception as err:
            return self.x_response("false", f"设备ID必须为数字！error:{err}")

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP Port必须为数字，error：{err}")

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()
        ftp_client.cd_or_mkdir(ftp_path)
        self.log.info(f"ftp路径已切换至：{ftp_path}")

        error_map = {
            0: "停用",
            1: "合格",
            2: "不合格"
        }

        error_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            product_flag = sn_product_map.get(barcode, '')

            self.log.info(f"从缓存中获取到的产品标识是：{product_flag}")

            board_result = board_entity.get_repair_result(1, 2)

            comp_str = ""
            repair_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_str += csv_comp_template.format(**{
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

                if comp_entity.is_repair_ng():
                    repair_ng_list.append(comp_entity.repair_ng_code)

            repair_ng_code = ",".join(repair_ng_list)

            if repair_ng_code:
                param5 = "0100"
            else:
                param5 = "0000"

            csv_content = csv_board_template.format(**{
                "pcb_project_name": pcb_entity.project_name,
                "pcb_test_time": pcb_entity.get_cycle_time(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_sn": pcb_entity.pcb_barcode,
                "pcb_final_result": pcb_entity.get_final_result(),
                "board_sn": product_flag,
                "board_no": board_entity.board_no,
                "board_final_result": board_entity.get_final_result(),
                "board_comp_number": board_entity.comp_total_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "CompData": comp_str
            })

            ftp_file = f"{product_flag}_{time_file}.csv"

            ftp_filepath = f"{ftp_path}/{ftp_file}"

            def upload_content_with_retries(max_retries):
                """
                尝试多次上传数据到ftp
                :return:
                """
                log.info(f"ftp尝试重传次数：{max_retries}")
                retry_delay = 0.2
                for retry in range(max_retries + 1):
                    try:
                        # 使用try-except块包装发送数据的操作
                        ftp_client.upload_content(ftp_file, csv_content)
                        log.info(f"ftp数据发送成功（第{retry + 1}次尝试）")
                        # self.log.info(f"文件已上传至：{ftp_filepath}")

                        # break  # 如果发送成功，则退出循环
                        return
                    except Exception as err1:
                        if retry < max_retries:
                            # 如果还有重试机会，则等待一段时间后再试
                            time.sleep(retry_delay)
                            log.warning(f"错误信息：{err1}, 开始第{retry + 1}次重发ftp....")
                        else:
                            # 如果达到最大重试次数，则记录最终错误并退出
                            error_msg_list.append(f"ftp上传达到最大重试次数（{max_retries}次），最终错误信息：{err1}")
                            return

            time.sleep(0.04)
            upload_content_with_retries(ftp_retries)

            a = '\\'
            full_dst_path = f"{concat_path}{ftp_filepath.replace('/', a)}"
            self.log.info(f"上传的ftp路径：{full_dst_path}")

            data_param = {
                "foo1": struct.pack('>h', 402),
                "foo2": binascii.a2b_hex("0300"),
                "foo3": struct.pack('>h', 0),  # 通讯标识
                "foo4": cons_shi_to_hex(line_id),  # 产线ID
                "foo5": cons_shi_to_hex(device_id),
                "foo6": struct.pack('>h', req_symbol_int),  # 请求信号
                "foo7": len_add_data(38, product_flag),  # 产品标识
                "foo8": struct.pack('>h', 0),  # 检测项数量
                "foo9": binascii.a2b_hex("0100"),  # 检测项1.启用
                "foo10": struct.pack('>h', board_result),
                "foo11": binascii.a2b_hex("0100"),  # 检测项2.启用
                "foo12": struct.pack('>h', cycle_time),
                "foo13": binascii.a2b_hex("0100"),  # 检测项3.启用
                "foo14": len_add_data(30, username),  # 员工编码
                "foo15": binascii.a2b_hex("0100"),  # 检测项4.启用
                "foo16": len_add_data(150, full_dst_path),  # ftp路径
                "foo17": binascii.a2b_hex(param5),  # 检测项5.启用
                "foo18": len_add_data(30, repair_ng_code),  # 不合格原因
                "foo19": binascii.a2b_hex("0000"),  # 检测项6，启用
                "foo20": len_add_data(30, ''),
                "foo21": binascii.a2b_hex("0000"),
                "foo22": len_add_data(30, ''),
                "foo23": binascii.a2b_hex("0000"),
                "foo24": len_add_data(30, ''),
                "foo25": binascii.a2b_hex("0000"),
                "foo26": struct.pack('f', 0.0),
                "foo27": binascii.a2b_hex("0000"),  # 测试结果2
                "foo28": struct.pack('f', 0.0),
                "foo29": binascii.a2b_hex("0000"),
                "foo30": struct.pack('f', 0.0),
            }

            param_str = b''
            for i in data_param.values():
                param_str += i

            ret_bytes = send_data_to_socket_server(tcp_host, tcp_port, param_str)
            ret_code = ret_bytes[2:4]

            self.log.info(f"ret code: {ret_code}")
            code_int = struct.unpack('>h', ret_code)[0]

            if code_int != 1:
                error_msg_list.append(f"mes接口异常，上报质量数据失败，errorCode：{code_int} errorMsg:{error_map.get(code_int, '未知异常码！')}")
                # error_msg = f"mes接口异常，上报质量数据失败，errorCode：{code_int} errorMsg:{error_map.get(code_int, '未知异常码！')}"

        ftp_client.close()

        if error_msg_list:
            error_msg = "\n".join(error_msg_list)
            return self.x_response("false", error_msg)

        return self.x_response()


if __name__ == '__main__':
    # ret = cons_shi_to_hex(0)
    # print(ret, type(ret), len(ret))

    # ret = struct.pack('128s', "sn11".encode('utf8'))
    # ret = cons_shi_to_hex(128) + cons_shi_to_hex(4) + ret
    # print(ret, type(ret))
    # print(len(ret))

    # b = len_add_data(38, "sn01")
    # print(b, type(b), len(b))
    #
    # for ix, i in enumerate(b):
    #     print(f"位数：{ix}, {i}")
    #
    #
    # def get_data_form_bytes(bytes_data: bytes) -> str:
    #     d_len = bytes_data[1]
    #     bytes_d = bytes_data[2:2 + d_len]
    #     return bytes_d.decode('utf-8')
    #
    #
    # cc = get_data_form_bytes(b)

    pass
