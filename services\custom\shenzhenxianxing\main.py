# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/31 上午9:28
# Author     ：sch
# version    ：python 3.8
# Description：深圳先行
"""
import traceback
from typing import Any

from common import xcons, xrequest
from common.xcache import cache_upload_failed_data, re_upload_cache_data
from engine.MesEngine import ErrorMapEngine
from entity.MesEntity import PcbEntity
from services.custom.shenzhenxianxing.sql_service import DatabaseManager
from vo.mes_vo import DataVo, ButtonVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["深圳先行", "shenzhenxianxing"],
        "version": "release v1.0.0.3",
        "device": "AIS401,AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-31 17:21  增加上传失败数据功能
date: 2025-05-12 16:32  jira:ATAOI_2019-34647,读取客户接口返回改为判断status
date: 2025-07-08 14:49  jira:ATAOI_2019-34647,新增数据上传（写入远程sqlserver）
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
    }
    other_form = {
        "db_name": {
            "ui_name": "数据库实例",
            "value": "",
        },
        "db_user": {
            "ui_name": "数据库用户名",
            "value": "",
        },
        "db_password": {
            "ui_name": "数据库密码",
            "value": "",
        },
        "db_host": {
            "ui_name": "数据库地址",
            "value": "",
        },
        "db_port": {
            "ui_name": "数据库端口",
            "value": "1433",
        },
    }

    combo = {
        "is_auto_upload": {
            "ui_name": "自动重传(重启mes配置器生效)",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "upload_interval": {
            "ui_name": "重传频率(分钟)",
            "item": list(map(str, range(5, 300, 5))),
            "value": "120",
        }
    }

    path = {
        "bak_path": {
            "ui_name": "上传失败数据备份路径",
            "value": "",
        }
    }

    button = {
        "upload_cache_data": {
            "ui_name": "上传失败数据"
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        is_auto_upload = other_vo.get_value_by_cons_key("is_auto_upload")
        upload_interval = other_vo.get_value_by_cons_key("upload_interval", to_int=True)

        main_window.set_cron_setting(is_auto_upload == "Yes", upload_interval * 60)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        bak_path = data_vo.get_value_by_cons_key("bak_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        db_name = data_vo.get_value_by_cons_key("db_name")
        db_user = data_vo.get_value_by_cons_key("db_user")
        db_password = data_vo.get_value_by_cons_key("db_password")
        db_host = data_vo.get_value_by_cons_key("db_host")
        db_port = data_vo.get_value_by_cons_key("db_port", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        data_date = pcb_entity.get_start_time().strftime(xcons.FMT_DATE)
        board_side = 1 if pcb_entity.board_side == "T" else 0

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0
        # 直通拼版数
        pcb_board_pass_number = 0
        pcb_comp_total = 0

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            pcb_comp_total += board_entity.comp_total_number

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1
            else:
                pcb_board_pass_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
            })

        pcb_param = {
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        try:
            ret = xrequest.RequestUtil.post_json(api_url, pcb_param)

            if str(ret.get("status")) != "成功":
                cache_upload_failed_data(bak_path, pcb_param)
                return self.x_response("false", f"mes接口异常，error：{ret.get('message')}，数据已缓存到本地！")

        except Exception as err:
            cache_upload_failed_data(bak_path, pcb_param)
            return self.x_response("false", f"数据上传失败，已缓存到本地！error:{err}")
        try:
            # 为不影响原来功能，放在最后
            self.log.info(f"开始更新数据库统计数据，日期：{data_date}, 设备：{device_name}, 板面：{board_side}")
            insert_db_data = {
                "data_date": data_date,
                "machine": device_name,
                "board_side": board_side,
                "board_total": pcb_entity.board_count,
                "board_pass": pcb_board_pass_number,
                "board_ng": pcb_board_user_ng_number,
                "board_false": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
                "comp_total": pcb_comp_total,
                "comp_pass": pcb_comp_total - pcb_comp_robot_ng_number,  # 直通
                "comp_ng": pcb_comp_user_ng_number,
                "comp_false": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量

            }
            sql_service = DatabaseManager(
                db_name=db_name,
                db_username=db_user,
                db_password=db_password,
                db_host=db_host,
                db_port=db_port
            )
            sql_service.create_daily_production_stats_table("daily_production_stats")
            query_list = sql_service.query_daily_production_stats("daily_production_stats", data_date,
                                                                  device_name,
                                                                  board_side)
            # 如果不存在，则插入数据
            if len(query_list) == 1:
                # 如果存在，则更新数据
                update_data = {
                    "board_total": query_list[0].get("board_total") + insert_db_data.get("board_total"),
                    "board_pass": query_list[0].get("board_pass") + insert_db_data.get("board_pass"),
                    "board_ng": query_list[0].get("board_ng") + insert_db_data.get("board_ng"),
                    "board_false": query_list[0].get("board_false") + insert_db_data.get("board_false"),
                    "comp_total": query_list[0].get("comp_total") + insert_db_data.get("comp_total"),
                    "comp_pass": query_list[0].get("comp_pass") + insert_db_data.get("comp_pass"),
                    "comp_ng": query_list[0].get("comp_ng") + insert_db_data.get("comp_ng"),
                    "comp_false": query_list[0].get("comp_false") + insert_db_data.get("comp_false"),
                    "data_date": data_date,
                    "machine": device_name,
                    "board_side": board_side
                }
                sql_service.update_daily_production_stats("daily_production_stats", update_data)
            elif len(query_list) == 0:
                sql_service.insert_daily_production_stats("daily_production_stats", insert_db_data)
            else:
                return self.x_response("false", "查询到多条数据，请检查数据库！")
        except Exception as e:
            self.log.error(traceback.format_exc())
            return self.x_response("false", f"数据上传成功，但更新数据库统计数据失败！error: {e}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url = btn_vo.get_value_by_cons_key("api_url")
        bak_path = btn_vo.get_value_by_cons_key("bak_path", not_null=True)

        btn_key = btn_vo.get_btn_key()

        if btn_key == "upload_cache_data":

            total_count, ok_count, error_count = re_upload_cache_data(bak_path, api_url)

            if error_count != 0:
                return self.x_response("false", f"上传总数：{total_count} 成功数量：{ok_count} 失败数量：{error_count}")

            if total_count == 0:
                return self.x_response("false", f"没有需要上传的缓存数据！")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        bak_path = other_vo.get_value_by_cons_key("bak_path")
        api_url = other_vo.get_value_by_cons_key("api_url")

        re_upload_cache_data(bak_path, api_url)
