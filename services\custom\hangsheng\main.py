# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/12 上午10:15
# Author     ：sch
# version    ：python 3.8
# Description：航盛
"""
import random
from typing import Any

from common import xutil
from engine.MesEngine import BaseEngine
from vo.mes_vo import DataVo

csv_template = """AOI Test Report
SerialNumber:,{sn}
Model:,{project_name}
Result:,{result}
TestDate:,{test_date}
TestStation:,{test_station}
User:,{repair_user}
SumTime:,{time_cost}s
BadInformation:,{bad_information}

BoardNO,Number,TestItem,LowerLimit,Standard,UpperLimit,TestValue,Units,TestResult,ErrorDesc
{comp_data}
"""

comp_template = "{board_no},{no},{tag},{alg_low},,{alg_upper},{test_val},,{test_result},{error_desc}"

AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "2", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "3", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "5", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "6", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "7", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "8", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "9", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "10", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "11", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "12", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "13", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "14", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "15", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "17", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "23", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}


def generate_serial_number():
    """
    生成随机四位流水号
    :return:
    """
    return ''.join(random.choice('0123456789') for i in range(4))


class Engine(BaseEngine):
    version = {
        "customer": ["深圳航盛", "hangsheng"],
        "version": "release v1.0.0.7",
        "device": "40x",
        "feature": ["生成本地文档"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-12 11:05  init
date: 2023-08-07 11:21  输出复判后的不良器件信息
date: 2023-08-09 10:45  修复Result的输出值
date: 2023-08-28 17:15  增加输出：ErrorDesc（机器检测不良描述）
date: 2023-11-16 17:11  分拼板生成数据  
date: 2025-05-14 11:18  jira:ATAOI_2019-39482,csv列表表头新增拼板号
date: 2025-05-22 16:23  jira:ATAOI_2019-39482,恢复整版生成数据
""",
    }

    form = {
        "station": {
            "ui_name": "工站",
            "value": "AOI"
        },
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        station = data_vo.get_value_by_cons_key("station")
        save_path = data_vo.get_value_by_cons_key("save_path")

        if not save_path:
            return self.x_response("false", "请先选择保存路径！")

        pcb_entity = data_vo.pcb_entity

        self.log.info(pcb_entity)
        pcb_sn = pcb_entity.pcb_barcode

        project_name = pcb_entity.project_name
        start_time = pcb_entity.get_start_time()
        cycle_time = pcb_entity.get_cycle_time()

        comp_ix = 0
        comp_rows = []

        comp_ng_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                comp_ix += 1

                alg_low = ""  # 下限
                alg_upper = ""  # 上限
                alg_val = ""  # 测试值

                for alg_obj in comp_entity.alg_data:
                    alg_upper = alg_obj.get("max_threshold")
                    alg_low = alg_obj.get("mix_threshold")
                    alg_val = alg_obj.get("test_val")
                    break

                for alg_obj in comp_entity.alg_data:
                    alg_result = alg_obj.get("result")
                    if alg_result != "0":
                        alg_upper = alg_obj.get("max_threshold")
                        alg_low = alg_obj.get("mix_threshold")
                        alg_val = alg_obj.get("test_val")
                        break

                if comp_entity.is_robot_ng():
                    error_desc = comp_entity.robot_ng_str
                else:
                    error_desc = ""

                comp_rows.append(comp_template.format(**{
                    "board_no": board_entity.board_no,
                    "no": comp_ix,
                    "tag": comp_entity.designator,
                    "alg_low": alg_low,
                    "alg_upper": alg_upper,
                    "test_val": alg_val,
                    "test_result": comp_entity.get_final_result("PASS", "REPASS", "Failed"),
                    "error_desc": error_desc
                }))

                if comp_entity.is_repair_ng():
                    ng_code = comp_entity.repair_ng_code
                    repair_ng_str = comp_entity.repair_ng_str
                    ng_tag = comp_entity.designator

                    ng_str = AIS_40X_ERROR_MAP.get(ng_code, {}).get('standard', repair_ng_str)
                    comp_ng_list.append(f"{ng_str}_{ng_tag}")

        csv_content = csv_template.format(**{
            "sn": pcb_sn,
            "project_name": project_name,
            # "result": pcb_entity.get_final_result("PASS", "REPASS", "Failed"),
            "result": pcb_entity.get_final_result("PASS", "REPASS", "Failed"),
            "test_date": str(start_time),
            "test_station": station,
            "repair_user": pcb_entity.repair_user,
            "time_cost": cycle_time,
            "bad_information": ";".join(comp_ng_list),
            "comp_data": "\n".join(comp_rows),
        })

        time_now = xutil.DateUtil.get_datetime_now()
        csv_file = f"{save_path}/{pcb_sn}_{time_now}_{generate_serial_number()}.csv"
        xutil.FileUtil.write_content_to_file(csv_file, csv_content)

        return self.x_response()

# if __name__ == '__main__':
#     import random
#
#
#     def generate_serial_number():
#         return ''.join(random.choice('0123456789') for i in range(4))
#
#
#     print(generate_serial_number())
