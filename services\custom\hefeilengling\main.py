# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/17 上午10:52
# Author     ：sch
# version    ：python 3.8
# Description：合肥崚凌
"""

from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "hefeilengling release v1.0.0.3",
        "device": "203,303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-17 15:16  init
date: 2024-07-10 17:14  参数改成全大写
date: 2025-01-13 09:38  RESULT参数改为OK和NG
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "条码查询接口",
            "value": "http://XXXXX/sfcs/vehicle_query/"
        },
        "api_url_data": {
            "ui_name": "过账接口",
            "value": "http://XXXXX/sfcs/connected_board_posting/"
        },
    }

    combo = {
        "bind_type": {
            "ui_name": "绑定类型",
            "item": ["生产条码", "FPCSN", "客户SN"],
            "value": "生产条码"
        }
    }

    form = {
        "part_no": {
            "ui_name": "机种",
            "value": " "
        },
        "station": {
            "ui_name": "站位",
            "value": " "
        },
        "line_no": {
            "ui_name": "线体",
            "value": " "
        },
        "machine": {
            "ui_name": "机台编号",
            "value": " "
        },
        "operator": {
            "ui_name": "作业员账号",
            "value": " "
        },
        "work_no": {
            "ui_name": "工单",
            "value": " "
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        bind_type = other_vo.get_value_by_cons_key("bind_type")

        bind_type_map = {
            "生产条码": 1,
            "FPCSN": 2,
            "客户SN": 3,
        }

        type_code = bind_type_map.get(bind_type)

        for sn in other_vo.list_sn():
            ret = xrequest.RequestUtil.post_json(api_url_check, {
                "LOADERBOARD": sn,
                "TYPE": type_code,
            })

            if ret.get("Result") != "OK":
                return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('Message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        part_no = data_vo.get_value_by_cons_key("part_no")
        station = data_vo.get_value_by_cons_key("station")
        line_no = data_vo.get_value_by_cons_key("line_no")
        machine = data_vo.get_value_by_cons_key("machine")
        operator = data_vo.get_value_by_cons_key("operator")
        work_no = data_vo.get_value_by_cons_key("work_no")
        bind_type = data_vo.get_value_by_cons_key("bind_type")

        bind_type_map = {
            "生产条码": 1,
            "FPCSN": 2,
            "客户SN": 3,
        }

        type_code = bind_type_map.get(bind_type)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_code = []
            for comp_entity in board_entity.yield_comp_entity():
                ng_code = comp_entity.repair_ng_code

                if comp_entity.is_repair_ng():
                    if ng_code not in comp_ng_code:
                        comp_ng_code.append(ng_code)

            board_data.append({
                "SN": barcode,
                "POSITION": int(board_no),
                "RESULT": board_entity.get_repair_result("OK", "NG"),
                "NGCODE": ",".join(comp_ng_code)
            })

        req_param = {
            "PARTNO": part_no,
            "LOADERBOARD": pcb_entity.pcb_barcode,
            "STATION": station,
            "LINENO": line_no,
            "MACHINE": machine,
            "OP": operator,
            "STARTTIME": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "ENDTIME": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
            "UPLOADPATH": "",
            "WORKNO": work_no,
            "TYPE": type_code,
            "BOARDLIST": board_data,
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, req_param)
        if ret.get("Result") != "OK":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('Message')}")

        return self.x_response()
