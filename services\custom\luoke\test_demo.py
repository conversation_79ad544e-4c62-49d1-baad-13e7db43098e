# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/2/22 上午10:18
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xsql

if __name__ == '__main__':
    # insert_board_template = """INSERT INTO boardData (board_sn, board_robot_result, board_user_result, board_final_result, pcb_comp_number, pcb_comp_robot_ng_number, pcb_comp_user_ng_number, pcb_comp_repass_number)
    # VALUES {data};"""
    #
    # d = ('B0001', 'PASS', 'PASS', 'PASS', 100, 0, 0, 0)
    # d2 = ('B0002', 'NG', 'PASS', 'PASS', 100, 0, 0, 0)
    #
    # insert_sql = insert_board_template.format(data=',\n'.join([str(d), str(d2)]))
    # print(insert_sql)

    insert_sql = """INSERT INTO boardData (board_sn, board_robot_result, board_user_result, board_final_result, pcb_comp_number, pcb_comp_robot_ng_number, pcb_comp_user_ng_number, pcb_comp_repass_number)  
OUTPUT INSERTED.id
VALUES ('D3VZ15KBT4021000PO2050', 'NG', 'NG', 'REPASS', 147, 84, 0, 84);"""

    conn = xsql.get_mssql_cursor('127.0.0.1', 'sa', 'Xx123456.', 'new_test', 1433)
    ret = xsql.insert_row_into_table_and_return_primary_id(conn, insert_sql)
    print(f"ret:{ret}")


