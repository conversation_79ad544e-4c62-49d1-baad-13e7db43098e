# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/2 上午9:17
# Author     ：sch
# version    ：python 3.8
# Description：江苏诺博/诺博汽车
"""

from typing import Any

from common import xrequest, xenum
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "jiangsunuobo release v1.0.0.4",
        "device": "AIS401",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-02 11:02  条码校验、上传数据
date: 2024-08-09 10:29  修改上传参数
date: 2024-09-20 09:50  整板pass时，IsReviewed传false
date: 2024-09-20 09:50  单板pass时，单板的IsReviewed传false
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(进站校验)",
            "value": "http://***********/apriso/HttpServices/api/extensions/1/DataCollection/MoveInVerify",
        },
        "api_url_data": {
            "ui_name": "接口URL(过站接口)",
            "value": "http://***********/apriso/HttpServices/api/extensions/1/DataCollection/MoveOut",
        },
    }

    form = {
        "account": {
            "ui_name": "登陆账号",
            "value": "",
        },
        "resource_name": {
            "ui_name": "设备代码",
            "value": "",
        },
    }

    def __init__(self):
        self.common_config["check_barcode_setting1"] = xenum.CheckSetting1.CheckFirst
        self.common_config["sendmes_setting2"] = xenum.SendMesSetting2.SaveInspectNG

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        account = other_vo.get_value_by_cons_key("account")
        resource_name = other_vo.get_value_by_cons_key("resource_name")

        project_name = other_vo.get_project_name()

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", f"未扫到条码！")

        ret_res = self.x_response()
        for sn in sn_list:
            check_param = {
                "Account": account,
                "Data": {
                    "CarrierNO": None,
                    "MESSN": sn,
                    "RecipeName": project_name
                },
                "ResourceName": resource_name
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

            if str(ret.get("Result")) != "200":
                ret_res = self.x_response("false", f"mes接口异常，条码进站校验失败，error：{ret.get('Message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        account = data_vo.get_value_by_cons_key("account")
        resource_name = data_vo.get_value_by_cons_key("resource_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        review_type = other_data.get("inspect_type")

        if review_type == "inspector":
            is_reviewed = False
        else:
            is_reviewed = True

        comp_data_list = []
        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if review_type == "inspector":
                is_reviewed1 = False
            else:
                is_reviewed1 = True

            if board_entity.get_final_result() == "PASS":
                is_reviewed1 = False

            for comp_entity in board_entity.yield_comp_entity():
                for alg_entity in comp_entity.yield_alg_entity():
                    comp_data_list.append({
                        "Characteristic": alg_entity.test_name,
                        "DataValue": alg_entity.test_val,
                        "DefectCode": comp_entity.repair_ng_code,
                        "Segment1": comp_entity.robot_ng_code,
                        "LowerSpecificationLimit": float(alg_entity.min_threshold),
                        "SN": barcode,
                        "TargetValue": 0.0,
                        "UOM": None,
                        "UpperSpecificationLimit": float(alg_entity.max_threshold),
                        "WipContentClassId": comp_entity.get_final_result(1, 1, 2),
                    })

            board_data_list.append({
                "Index": int(board_no),
                "Result": board_entity.get_repair_result(1, 2),
                "IsReviewed": is_reviewed1,
                "SN": barcode,
                "ICCID": "",
                "IMEI": ""
            })

        pcb_final_result = pcb_entity.get_final_result()
        if pcb_final_result == "PASS":
            is_reviewed = False

        data_param = {
            "Account": account,
            "Data": {
                "IsReviewed": is_reviewed,
                "InspectionResult": pcb_entity.get_repair_result(1, 2),
                "MESSN": pcb_sn,
                "RecipeName": pcb_entity.project_name,
                "OnWipCharacteristicModels": comp_data_list,
                "SubMESSNs": board_data_list,
                "OnWipToolModels": [],
            },
            "ResourceName": resource_name,
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

        if str(ret.get("Result")) != "200":
            return self.x_response("false", f"mes接口异常，条码过站失败，error：{ret.get('Message')}")

        return self.x_response()
