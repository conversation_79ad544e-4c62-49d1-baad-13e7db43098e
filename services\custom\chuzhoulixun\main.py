# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/25 下午4:54
# Author     ：sch
# version    ：python 3.8
# Description：滁州立讯
"""
import json
import os
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


get_sn_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetBoards xmlns="MesWebService">
      <Parames>{pcb_sn}</Parames>
    </GetBoards>
  </soap:Body>
</soap:Envelope>"""


class Engine(BaseEngine):
    version = {
        "title": "chuzhoulixun release v1.0.0.9",
        "device": "50x",
        "feature": ["条码校验", "上传数据", "从mes获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-25 16:55  init
date: 2023-07-04 09:49  参数放到body参数里面
date: 2023-07-04 15:38  解析条码校验返回的参数
date: 2023-07-04 17:28  条码校验失败时，要把校验失败的条码弹窗提示
date: 2023-07-26 15:30  增加从mes获取条码功能
date: 2024-01-25 16:25  增加保存整板图功能，以及重新解析获取条码的返回值
date: 2024-01-29 16:22  优先取光源2的整板大图
date: 2024-04-03 17:05  增加整板图光源配置项
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://192.168.16.30/Bobcat/sfc_response.aspx"
        },
        "get_sn_url": {
            "ui_name": "获取条码接口URL",
            "value": "http://192.168.16.30/SLEnergyGetSN.asmx"
        },
        "station": {
            "ui_name": "站点名称",
            "value": "ITCZ_A04-2FT-02_5_LED-CAL"
        },
        "product": {
            "ui_name": "专案名称",
            "value": "C99"
        },
        "test_station_name": {
            "ui_name": "测试站名称",
            "value": "IQC-CAP"
        },
    }

    combo = {
        "pcb_src_flag_t": {
            "ui_name": "整板图光源(T面)",
            "item": ["0", "1", "2", "3", "4", "5"],
            "value": "0",
        },
        "pcb_src_flag_b": {
            "ui_name": "整板图光源(B面)",
            "item": ["0", "1", "2", "3", "4", "5"],
            "value": "0",
        },
    }

    path = {
        "save_path_img": {
            "ui_name": "整板图路径",
            "value": "",
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        station = other_vo.get_value_by_cons_key("station")

        for sn in other_vo.list_sn():
            check_param = {
                "p": "unit_process_check",
                "c": "QUERY_RECORD",
                "tsid": station,
                "sn": sn,
            }
            ret = xrequest.RequestUtil.post_form(api_url, check_param, to_json=False)
            # if len(ret) > 2:
            #     return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret}")

            if "UNIT OUT OF PROCESS" in ret or (not ret.endswith("OK")):
                return self.x_response("false", f"mes接口异常，条码校验失败，SN:{sn}，error：{ret}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        station = data_vo.get_value_by_cons_key("station")
        product = data_vo.get_value_by_cons_key("product")
        test_station_name = data_vo.get_value_by_cons_key("test_station_name")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img")
        pcb_src_flag_t = data_vo.get_value_by_cons_key("pcb_src_flag_t")
        pcb_src_flag_b = data_vo.get_value_by_cons_key("pcb_src_flag_b")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_tag_list = []
            comp_ng_str_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag_list.append(comp_entity.designator)
                    comp_ng_str_list.append(comp_entity.repair_ng_str)

            save_param = {
                "c": "ADD_RECORD",
                "result": board_entity.get_repair_result("PASS", "FAIL"),
                "product": product,
                "test_station_name": test_station_name,
                "station_id": station,
                "audit_mode": "0",
                "start_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "stop_time": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
                "sn": barcode,
                "list_of_failing_tests": ",".join(comp_tag_list),
                "failure_message": ",".join(comp_ng_str_list),
            }

            ret = xrequest.RequestUtil.post_form(api_url, save_param, to_json=False)
            ret_lower = ret.lower()
            if ret_lower not in ["0 sfc ok", "0 sfc_ok", "0 arc ok", "0 arc_ok"]:
                # return self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret_lower}")
                error_msg = f"mes接口异常，上传测试数据失败，error：{ret_lower}"

        board_side = pcb_entity.get_board_side()
        result = pcb_entity.get_repair_result("PASS", "FAIL")

        dst_path = f"{save_path_img}/{time_file[:8]}/{result}"
        xutil.FileUtil.ensure_dir_exist(dst_path)

        if board_side == 'T+B':
            # 合并发送
            # t_src_img0 = pcb_entity.get_pcb_t_image()
            # b_src_img0 = pcb_entity.get_pcb_b_image()

            t_review_path = pcb_entity.get_pcb_pcb_t_review_path()
            b_review_path = pcb_entity.get_pcb_pcb_b_review_path()

            t_src_img = f"{t_review_path}/thumbnail/{pcb_src_flag_t}/thumb.jpg"
            b_src_img = f"{b_review_path}/thumbnail/{pcb_src_flag_b}/thumb.jpg"

            # t_src_img = t_src_img.replace("/0/", f"/{pcb_src_flag_t}/")
            # b_src_img = b_src_img.replace("/0/", f"/{pcb_src_flag_b}/")

            # if os.path.exists(t_src_img2):
            #     t_src_img = t_src_img2
            # else:
            #     t_src_img = t_src_img0
            #
            # if os.path.exists(b_src_img2):
            #     b_src_img = b_src_img2
            # else:
            #     b_src_img = b_src_img0

            if os.path.exists(t_src_img):
                xutil.FileUtil.copy_file(t_src_img, f"{dst_path}/T_{pcb_sn}_{time_file}_{result}.jpg")
            else:
                self.log.warning(f'未找到大图：{t_src_img}')

            if os.path.exists(b_src_img):
                xutil.FileUtil.copy_file(b_src_img, f"{dst_path}/B_{pcb_sn}_{time_file}_{result}.jpg")
            else:
                self.log.warning(f"未找到大图：{b_src_img}")

        else:
            # 分开发送
            # t_src_img = pcb_entity.get_pcb_t_image()

            # if os.path.exists(t_src_img2):
            #     t_src_img = t_src_img2
            # else:
            #     t_src_img = t_src_img0
            if pcb_entity.board_side == "B":
                pcb_flag = pcb_src_flag_b
            else:
                pcb_flag = pcb_src_flag_t

            t_review_path = pcb_entity.get_pcb_pcb_t_review_path()
            t_src_img = f"{t_review_path}/thumbnail/{pcb_flag}/thumb.jpg"

            if os.path.exists(t_src_img):
                xutil.FileUtil.copy_file(t_src_img, f"{dst_path}/{board_side}_{pcb_sn}_{time_file}_{result}.jpg")
            else:
                self.log.warning(f"未找到大图：{t_src_img}")

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        pcb_sn = other_vo.get_pcb_sn()
        get_sn_url = other_vo.get_value_by_cons_key("get_sn_url")

        get_sn_param = get_sn_template.format(**{
            "pcb_sn": pcb_sn,
        })

        ret_str = xrequest.RequestUtil.post_soap(get_sn_url, get_sn_param, soap_action="MesWebService/GetBoards")
        root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

        result = root[0][0][0].text

        result_json = json.loads(result)
        result_str = result_json.get("Result")
        if result_str != "OK":
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{result_json.get('message')}")

        barcode_list = result_json.get('Barcode', [])
        sn_list = []
        for b in barcode_list:
            sn_list.append(b.get('BoardBarcode'))

        return self.x_response("true", ",".join(sn_list))
