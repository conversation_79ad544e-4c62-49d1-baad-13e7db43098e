# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/03/05 09:28
# Author     ：chencb
# version    ：python 3.8
# Description：惠州华创 https://jira.cvte.com/browse/ATAOI_2019-37615
"""
import json
import os
from typing import Any
from urllib.parse import urljoin

from common import xutil, xrequest, xcons
from common.xconfig import home_dir
from common.xcons import FMT_TIME_DEFAULT
from entity.MesEntity import PcbEntity
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import ErrorMapEngine

# 上传失败缓存目录以及缓存信息文件
FAIL_CACHE_DIR = f"{home_dir}/.aoi/mes_fail_cache"
SPI_DATA_FILE = f'{FAIL_CACHE_DIR}/spi_data.json'
AOI_DATA_FILE = f'{FAIL_CACHE_DIR}/aoi_data.json'
# 接口路径
AOI_API_PATH = '/api/ShopFloor/ReflowSolderingInspect'
SPI_API_PATH = '/api/ShopFloor/SolderPrintingInspect'


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhouhuachuang release v1.0.0.2",
        "device": "AIS43X, AIS63X",
        "feature": ["SPI数据发送", "AOI数据发送", "一键重传"],
        "author": "chenchongbing",
        "release": """
date: 2025-03-05 20:30  jira:37615 SPI数据发送、AOI数据发送、一键重传
date: 2025-03-11 19:30  jira:37615 增加Operator字段、error信息从原来独立字段改为Errors数组
""", }

    combo = {
        "api_type": {
            "ui_name": "接口类型",
            "item": [
                "AOI",
                "SPI",
            ],
            "value": "AOI"
        },
    }

    form = {
        "api_url": {
            "ui_name": "接口地址",
            "value": ""
        },
        "station_id": {
            "ui_name": "工站ID",
            "value": ""
        },
        "device_id": {
            "ui_name": "设备ID",
            "value": ""
        },
    }

    button = {
        "upload_fail_data": {
            "ui_name": "一键重传"
        },
    }

    def __init__(self):
        self.other_vo = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        # 后续所有配置参数获取都统一从这获取
        self.other_vo = other_vo

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        response = self.x_response()
        if btn_key == "upload_fail_data":
            response = self._upload_fail_data()
        return response

    def _upload_fail_data(self):
        """
        点击一键重传按钮时触发上传，上传失败重新缓存
        """
        api_type = self.other_vo.get_value_by_cons_key("api_type")
        api_url = self.other_vo.get_value_by_cons_key("api_url", not_null=True)
        if api_type == 'AOI':
            fail_data_file = AOI_DATA_FILE
            post_url = urljoin(api_url, AOI_API_PATH)
        else:
            fail_data_file = SPI_DATA_FILE
            post_url = urljoin(api_url, SPI_API_PATH)

        # 如果没有缓存文件则无失败文件，返回提示信息
        if not os.path.exists(fail_data_file):
            return self.x_response("false", f"没有需要重新上传的失败数据")

        fail_data = xutil.FileUtil.load_json_file(fail_data_file)
        response = self.x_response()
        err_msg_list = []
        new_fail_data_list = []
        for send_data in fail_data:
            response = self._send_data(post_url, send_data)
            if not response.get('result'):
                err_msg = response.get('string')
                if '本地网络异常' in err_msg:
                    # 本地网络异常才认为重传失败，需缓存失败数据
                    err_msg_list.append(err_msg)
                    new_fail_data_list.append(send_data)
                else:
                    response = self.x_response()

        # 先把旧缓存文件移除，如果重传后还有失败的，重新保存
        os.remove(fail_data_file)
        if new_fail_data_list:
            self._save_fail_data_to_file(fail_data_file, new_fail_data_list)

        # 把所有错误信息组装起来发送
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"{err_str}")
        return response

    def _save_fail_data_to_file(self, save_file: str, fail_data: list):
        """
        保存失败数据到文件
        """
        # 没有记录过则直接写入
        if not os.path.exists(save_file):
            # 先创建失败缓存目录：
            xutil.FileUtil.ensure_dir_exist(FAIL_CACHE_DIR)
            data_str = json.dumps(fail_data, indent=4, ensure_ascii=False)
            xutil.FileUtil.write_content_to_file(save_file, data_str)
            return

        # 读取原有fail_data，并把新的数据追加到原有的记录里重新保存
        cached_fail_data = xutil.FileUtil.load_json_file2(save_file)
        for data in fail_data:
            exists = any(data == d for d in cached_fail_data)
            # 已经缓存的数据就不重复缓存
            if not exists:
                cached_fail_data.append(data)

        data_str = json.dumps(cached_fail_data, indent=4, ensure_ascii=False)
        xutil.FileUtil.write_content_to_file(save_file, data_str)

    def _send_data(self, api_url: str, send_data: dict):
        """
        发送最终数据，MES返回响应格式如下：
        {
            "attachObject": {},
            "success": true,
            "message": "",
            "code": "" ，
            "timeElasped":0,
            "endpoint":"",
            "dateTime":"2025-02-24 17:30"
        }
        """
        response = self.x_response()
        try:
            ret = xrequest.RequestUtil.post_json(api_url, send_data)
            if not ret.get("success"):
                code = ret.get('code')
                message = ret.get('message')
                response = self.x_response("false", f"【服务器返回失败，code：{code}, message：{message}】")
        except Exception as e:
            response = self.x_response("false", f"【本地网络异常：{e}】")
        return response

    def _send_aoi_data(self, pcb_entity: PcbEntity):
        """
        aoi设备数据生成和发送
        """
        station_id = self.other_vo.get_value_by_cons_key("station_id")
        device_id = self.other_vo.get_value_by_cons_key("device_id")
        api_url = self.other_vo.get_value_by_cons_key("api_url", not_null=True)

        start_time = str(pcb_entity.get_start_time().strftime(FMT_TIME_DEFAULT))
        end_time = str(pcb_entity.get_end_time().strftime(FMT_TIME_DEFAULT))

        err_msg_list = []
        fail_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            board_sn = board_entity.barcode
            if not board_sn:
                # 条码为空时传时间戳
                board_sn = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE)

            final_result = True if board_entity.final_result in ['PASS', 'REPASS'] else False

            ng_comp_cnt = 0
            error_comp_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    error_comp_list.append({
                        "Type": comp_entity.robot_ng_str,
                        "Image": xutil.ImageUtil.file_to_base64_content(comp_entity.image_path)
                    })
                    ng_comp_cnt += 1
                    # 最多只缓存3份数据
                    if ng_comp_cnt >= 3:
                        break

            board_data = {
                "StationId": station_id,
                "Machine": device_id,
                "PSN": board_sn,
                "InspectResult": board_entity.robot_result,
                "ReviewResult": board_entity.repair_result,
                "LastResult": final_result,
                "StartTime": start_time,
                "EndTime": end_time,
                "Operator": pcb_entity.repair_user,
                "Errors": error_comp_list
            }

            post_url = urljoin(api_url, AOI_API_PATH)
            response = self._send_data(post_url, board_data)
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)
                if '本地网络异常' in err_msg:
                    # 本地网络异常才需缓存失败数据
                    fail_data_list.append(board_data)

        # 把缓存的失败数据信息保存到本地
        if fail_data_list:
            self._save_fail_data_to_file(AOI_DATA_FILE, fail_data_list)
        # 把所有错误信息组装起来发送给AOI
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"{err_str}")
        else:
            response = self.x_response()
        return response

    def _send_spi_data(self, pcb_entity: PcbEntity):
        """
        spi设备数据生成和发送
        """
        station_id = self.other_vo.get_value_by_cons_key("station_id")
        device_id = self.other_vo.get_value_by_cons_key("device_id")
        api_url = self.other_vo.get_value_by_cons_key("api_url", not_null=True)

        start_time = str(pcb_entity.get_start_time().strftime(FMT_TIME_DEFAULT))
        end_time = str(pcb_entity.get_end_time().strftime(FMT_TIME_DEFAULT))

        err_msg_list = []
        fail_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            board_sn = board_entity.barcode
            if not board_sn:
                # 条码为空时传时间戳
                board_sn = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE)

            area_total = 0
            area_count = 0
            height_total = 0
            height_count = 0
            for comp_entity in board_entity.yield_comp_entity():
                for alg_entity in comp_entity.yield_alg_entity():
                    test_name = alg_entity.test_name
                    test_val = float(alg_entity.test_val)
                    if test_name == "Area":
                        area_total += test_val
                        area_count += 1
                    elif test_name == "Height":
                        height_total += test_val
                        height_count += 1

            area_average = round(area_total / area_count, 2) if area_count > 0 else 0.0
            height_average = round(height_total / height_count, 2) if height_count > 0 else 0.0
            board_data = {
                "StationId": station_id,
                "Machine": device_id,
                "PSN": board_sn,
                "SolderArea": area_average,
                "SolderHeight": height_average,
                "StartTime": start_time,
                "EndTime": end_time,
            }

            post_url = urljoin(api_url, SPI_API_PATH)
            response = self._send_data(post_url, board_data)
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)
                if '本地网络异常' in err_msg:
                    # 本地网络异常才需缓存失败数据
                    fail_data_list.append(board_data)

        # 把缓存的失败数据信息保存到本地
        if fail_data_list:
            self._save_fail_data_to_file(SPI_DATA_FILE, fail_data_list)

        # 把所有错误信息组装起来发送给AOI
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"{err_str}")
        else:
            response = self.x_response()
        return response

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        api_type = data_vo.get_value_by_cons_key("api_type")

        if api_type == "AOI":
            response = self._send_aoi_data(pcb_entity)
        else:
            response = self._send_spi_data(pcb_entity)

        return response
