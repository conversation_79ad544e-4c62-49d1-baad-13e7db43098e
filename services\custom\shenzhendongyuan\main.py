"""
# File       : main.py
# Time       ：2025/06/05 16:29
# Author     ："wxc"
# version    ：python 3.8
# Description：深圳东原
"""
from contextlib import contextmanager
from datetime import datetime
from typing import Any
import pymssql
from common import xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo


@contextmanager
def db_conn_and_create_table(db_host, db_username, db_password, db_name, db_port=1433, table_name="MES5_SMT_OUT_SCAN"):
    """
    上下文管理器，用于连接数据库并创建表，如果表不存在的话。
    """
    create_sql = f"""
        CREATE TABLE dbo.{table_name} (
         -- 主键字段（唯一标识）
         SN1 VARCHAR(30) NOT NULL,
         SN2 VARCHAR(30),
         SN3 VARCHAR(30),
         SN4 VARCHAR(30),
         SN5 VARCHAR(30),
         SN6 VARCHAR(30),
         MODEL VARCHAR(30) NOT NULL,  
         -- 其他业务字段
         LINE VARCHAR(30),
         ORDEE VARCHAR(30),
         TIMER DATETIME,
        
         -- 主键约束（复合主键）
         PRIMARY KEY (SN1, MODEL)
        );
    """
    check_sql = f"""
    IF OBJECT_ID('{table_name}', 'U') IS NULL
    BEGIN
        {create_sql}
    END
    """
    conn = None
    cursor = None
    try:
        conn = pymssql.connect(
            server=db_host,
            user=db_username,
            password=db_password,
            database=db_name,
            port=db_port
        )
        cursor = conn.cursor()
        cursor.execute(check_sql)
        conn.commit()
        yield conn, cursor
    except pymssql.Error as e:
        if conn:
            conn.rollback()
        raise Exception(f"数据库错误: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


class Engine(ErrorMapEngine):
    version = {
        "customer": ["深圳东原", "shenzhendongyuan"],
        "version": "release v1.0.0.2",
        "device": "AIS401",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-06-05 16:30 ATAOI_2019-39969 写入远程SQL SERVER数据库
date: 2025-06-16 16:30 修改客户名称
"""
    }
    other_form = {
        "db_host": {
            "ui_name": "数据库地址",
            "value": "",
        },
        "db_username": {
            "ui_name": "数据库用户名",
            "value": "",
        },
        "db_password": {
            "ui_name": "数据库密码",
            "value": "",
        },
        "db_name": {
            "ui_name": "数据库实例",
            "value": "",
        },
        "db_port": {
            "ui_name": "数据库端口",
            "value": "1433",
        }
    }
    form = {
        "work_id": {
            "ui_name": "工单号",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        db_host = data_vo.get_value_by_cons_key("db_host")
        db_username = data_vo.get_value_by_cons_key("db_username")
        db_password = data_vo.get_value_by_cons_key("db_password")
        db_name = data_vo.get_value_by_cons_key("db_name")
        db_port = data_vo.get_value_by_cons_key("db_port")
        work_id = data_vo.get_value_by_cons_key("work_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        upload_time = datetime.now().strftime(xcons.FMT_TIME_DEFAULT5)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            no = board_entity.board_no
            if not barcode:
                barcode = f"{test_time}_{no}"

            with db_conn_and_create_table(
                    db_host=db_host,
                    db_username=db_username,
                    db_password=db_password,
                    db_name=db_name,
                    db_port=db_port) as (conn, cursor):
                cursor.execute(
                    "INSERT INTO dbo.MES5_SMT_OUT_SCAN (SN1, SN4, MODEL, ORDEE, TIMER) VALUES (%s, %s, %s, %s, %s)",
                    (barcode, board_entity.get_final_result("PASS", "PASS", "FAIL"),
                     pcb_entity.project_name, work_id, upload_time))
                conn.commit()
        return self.x_response()
