# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/15 下午3:18
# Author     ：sch
# version    ：python 3.8
# Description：珠海迈科
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, ButtonVo, OtherVo, ComboVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "zhuhaimaike release v1.0.0.6",
        "device": "203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-15 18:37  条码校验，上传数据，获取车间，获取线体
date: 2023-12-16 11:29  修改上传参数
date: 2023-12-18 10:14  修改上传参数:pcTestItems
date: 2024-01-05 14:32  增加获取机型接口
date: 2024-02-20 15:51  优化车间、线体、机型获取方式
""", }

    combo = {
        "workshop": {
            "ui_name": "车间",
            "item": [],
            "value": "",
        },
        "line": {
            "ui_name": "线体",
            "item": [],
            "value": "",
        },
        "device_type": {
            "ui_name": "机型",
            "item": [],
            "value": "",
        },
    }

    form = {

        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
        "username": {
            "ui_name": "工号",
            "value": "",
        },
        "station": {
            "ui_name": "工站",
            "value": "",
        },
        "barcode_type": {
            "ui_name": "条码类型",
            "value": "",
        },
        "other_param": {
            "ui_name": "扩展参数",
            "value": "",
        },
    }

    other_form = {
        "get_workshop_api": {
            "ui_name": "获取车间接口URL",
            "value": "",
        },
        "get_line_api": {
            "ui_name": "获取线体接口URL",
            "value": "",
        },
        "check_barcode_api": {
            "ui_name": "条码校验接口URL",
            "value": "",
        },
        "upload_data_api": {
            "ui_name": "上传数据接口URL",
            "value": "",
        },
        "get_device_type_api": {
            "ui_name": "获取机型接口URL",
            "value": "",
        },
    }

    # button = {
    #     "get_workshop": {
    #         "ui_name": "获取车间",
    #     },
    #     "get_line": {
    #         "ui_name": "获取线体",
    #     },
    #     "get_device": {
    #         "ui_name": "获取机型",
    #     },
    # }

    def __init__(self):
        error_map = self.ERROR_MAP
        for i, k in error_map.items():
            for d, j in k.items():
                self.ERROR_MAP[i][d]["custom_str"] = j.get("standard")

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_barcode_api = other_vo.get_value_by_cons_key("check_barcode_api")
        order_id = other_vo.get_value_by_cons_key("order_id")
        device_type = other_vo.get_value_by_cons_key("device_type")
        username = other_vo.get_value_by_cons_key("username")
        station = other_vo.get_value_by_cons_key("station")
        barcode_type = other_vo.get_value_by_cons_key("barcode_type")
        other_param1 = other_vo.get_value_by_cons_key("other_param")
        workshop = other_vo.get_value_by_cons_key("workshop")
        line = other_vo.get_value_by_cons_key("line")

        cache_data = xutil.CacheUtil.get_cache_data()
        work_shop_map = cache_data.get("work_shop_map", {})
        line_map = cache_data.get("line_map", {})

        new_workshop = work_shop_map.get(workshop, '')
        new_line = line_map.get(line, '')

        error_msg = ""
        for sn in other_vo.list_sn():
            check_param = {
                "pcPo": order_id,
                "pcModel": device_type,
                "pcUser": f"{username},{new_workshop},{new_line}",
                "pcStation": station,
                "pcBarcodeType": barcode_type,
                "pcBarcode": sn,
                "pcExtra": other_param1,
            }

            ret = xrequest.RequestUtil.post_json(check_barcode_api, check_param)
            if str(ret.get('Result')) != '1':
                error_msg = f"mes接口异常，条码校验失败，error：{ret.get('ErrMsg')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        upload_data_api = data_vo.get_value_by_cons_key("upload_data_api")
        order_id = data_vo.get_value_by_cons_key("order_id")
        device_type = data_vo.get_value_by_cons_key("device_type")
        username = data_vo.get_value_by_cons_key("username")
        station = data_vo.get_value_by_cons_key("station")
        barcode_type = data_vo.get_value_by_cons_key("barcode_type")
        other_param1 = data_vo.get_value_by_cons_key("other_param")
        workshop = data_vo.get_value_by_cons_key("workshop")
        line = data_vo.get_value_by_cons_key("line")

        cache_data = xutil.CacheUtil.get_cache_data()
        work_shop_map = cache_data.get("work_shop_map", {})
        line_map = cache_data.get("line_map", {})

        new_workshop = work_shop_map.get(workshop, '')
        new_line = line_map.get(line, '')
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        pcb_sn = pcb_entity.pcb_barcode
        pcb_result = pcb_entity.get_repair_result("PASS", "NG")

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            ng_list = []
            ng_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    ng_list.append(comp_entity.repair_ng_str)

                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator
                    comp_part = comp_entity.part
                    comp_package = comp_entity.package
                    comp_type = comp_entity.type

                    robot_ng_code = comp_entity.robot_ng_code
                    robot_ng_str = comp_entity.robot_ng_str
                    repair_ng_code = comp_entity.repair_ng_code
                    repair_ng_str = comp_entity.repair_ng_str

                    ng_data_list.append({
                        "name": f"{comp_tag},{comp_part},{comp_package},{comp_type}",
                        "value": f"{robot_ng_code}.{robot_ng_str}.{repair_ng_code}.{repair_ng_str}",
                        "param": "",
                        "result": 2
                    })

            board_result = board_entity.get_repair_result("PASS", "NG")

            board_param = {
                "pcPo": order_id,
                "pcModel": device_type,
                "pcUser": f"{username},{new_workshop},{new_line}",
                "pcStation": station,
                "pcBarcodeType": barcode_type,
                "pcBarcode": barcode,
                "pcExtra": other_param1,
                "pcTestResult": board_result,
                "pcBeginTime": start_time,
                "iTestTime": str(int(pcb_entity.get_cycle_time())),
                "pcNgItem": ",".join(ng_list),
                "pcLogFileName": f"{new_workshop}_{pcb_sn}_{barcode}_{board_result}_{pcb_result}.txt",
                "pcTestItems": json.dumps(ng_data_list, ensure_ascii=False),
                "pcBarcodeDict": None,
            }

            ret = xrequest.RequestUtil.post_json(upload_data_api, board_param)
            if str(ret.get('Result')) != '1':
                error_msg = f"mes接口异常，上传数据失败，error：{ret.get('ErrMsg')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        get_workshop_api = btn_vo.get_value_by_cons_key("get_workshop_api")
        get_line_api = btn_vo.get_value_by_cons_key("get_line_api")
        workshop = btn_vo.get_value_by_cons_key("workshop")
        order_id = btn_vo.get_value_by_cons_key("order_id")
        get_device_type_api = btn_vo.get_value_by_cons_key("get_device_type_api")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "get_workshop":
            ret = xrequest.RequestUtil.post_json(get_workshop_api, {})
            if str(ret.get('Result')) != '1':
                return self.x_response("false", f"mes接口异常，获取车间失败，error：{ret.get('ErrMsg')}")

            work_shop_map = ret.get('ResultContent', {})

            new_work_shop_map = {}
            for k, v, in work_shop_map.items():
                new_work_shop_map[v] = k

            xutil.CacheUtil.set("work_shop_map", new_work_shop_map)

            workshop_list = list(new_work_shop_map.keys())

            if not workshop_list:
                return self.x_response("false", f"未获取到车间！")

            workshop_item = getattr(other_param, 'combo_workshop')

            workshop_item.clear()
            for i in workshop_list:
                workshop_item.addItem(i)

            other_param.config_data['combo']['workshop']['item'] = workshop_list
            other_param.config_data['combo']['workshop']['value'] = workshop_list[0]
            other_param.save_config_data_to_file()

        elif btn_key == "get_line":
            if not workshop:
                return self.x_response("false", f"请先获取车间，再获取线体！")

            workshop_map = xutil.CacheUtil.get('work_shop_map')

            ret = xrequest.RequestUtil.post_json(get_line_api, {'workshop': workshop_map.get(workshop)})
            if str(ret.get('Result')) != '1':
                return self.x_response("false", f"mes接口异常，获取车间失败，error：{ret.get('ErrMsg')}")

            line_map = ret.get('ResultContent', {})

            new_line_map = {}
            for k, v, in line_map.items():
                new_line_map[v] = k

            xutil.CacheUtil.set("line_map", new_line_map)

            line_list = list(new_line_map.keys())

            if not line_list:
                return self.x_response("false", f"未获取到线体！")

            line_item = getattr(other_param, 'combo_line')
            line_item.clear()
            for i in line_list:
                line_item.addItem(i)

            other_param.config_data['combo']['line']['item'] = line_list
            other_param.config_data['combo']['line']['value'] = line_list[0]
            other_param.save_config_data_to_file()

        elif btn_key == 'get_device':
            if not order_id:
                return self.x_response("false", f"请先输入工单号，再获取机型！")

            ret = xrequest.RequestUtil.post_json(get_device_type_api,
                                                 {'po': order_id})

            if str(ret.get('Result')) != '1':
                return self.x_response("false", f"mes接口异常，获取机型失败，error：{ret.get('ErrMsg')}")

            device_type_list = ret.get('ResultContent', [])

            if not device_type_list:
                return self.x_response("false", f"未获取到机型！")

            device_type_item = getattr(other_param, 'combo_device_type')
            device_type_item.clear()
            for i in device_type_list:
                device_type_item.addItem(i)

            other_param.config_data['combo']['device_type']['item'] = device_type_list
            other_param.config_data['combo']['device_type']['value'] = device_type_list[0]
            other_param.save_config_data_to_file()

        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, other_param: Any):
        get_workshop_api = combo_vo.get_value_by_cons_key("get_workshop_api")
        # order_id = combo_dao.get_value_by_cons_key("order_id")
        get_device_type_api = combo_vo.get_value_by_cons_key("get_device_type_api")

        combo_key = combo_vo.get_combo_key()

        if combo_key == 'workshop':
            ret = xrequest.RequestUtil.post_json(get_workshop_api, {})
            if str(ret.get('Result')) != '1':
                return self.x_response("false", f"mes接口异常，获取车间失败，error：{ret.get('ErrMsg')}")

            work_shop_map = ret.get('ResultContent', {})

            new_work_shop_map = {}
            for k, v, in work_shop_map.items():
                new_work_shop_map[v] = k

            xutil.CacheUtil.set("work_shop_map", new_work_shop_map)

            ret_list = list(new_work_shop_map.keys())

            if not ret_list:
                return self.x_response("false", f"未获取到车间！")

            ret_data = {
                "new_items": ret_list
            }

            return self.x_response("true", json.dumps(ret_data))

        elif combo_key == 'device_type':
            order_id_item = getattr(other_param, f'form_order_id')
            order_id = order_id_item.text()

            if not order_id:
                return self.x_response("false", f"请先输入工单号，再获取机型！")

            ret = xrequest.RequestUtil.post_json(get_device_type_api,
                                                 {'po': order_id})

            if str(ret.get('Result')) != '1':
                return self.x_response("false", f"mes接口异常，获取机型失败，error：{ret.get('ErrMsg')}")

            device_type_list = ret.get('ResultContent', [])

            if not device_type_list:
                return self.x_response("false", f"未获取到机型！")

            device_type_item = getattr(other_param, 'combo_device_type')
            device_type_item.clear()
            for i in device_type_list:
                device_type_item.addItem(i)

            other_param.config_data['combo']['device_type']['item'] = device_type_list
            other_param.config_data['combo']['device_type']['value'] = device_type_list[0]
            other_param.save_config_data_to_file()

    def combo_index_changed(self, combo_vo: ComboVo, other_param: Any):
        combo_key = combo_vo.get_combo_key()

        get_line_api = combo_vo.get_value_by_cons_key("get_line_api")
        workshop = combo_vo.get_value_by_cons_key("workshop")

        if combo_key == "workshop":
            workshop_map = xutil.CacheUtil.get('work_shop_map')

            ret = xrequest.RequestUtil.post_json(get_line_api, {'workshop': workshop_map.get(workshop)})
            if str(ret.get('Result')) != '1':
                return self.x_response("false", f"mes接口异常，获取车间失败，error：{ret.get('ErrMsg')}")

            line_map = ret.get('ResultContent', {})

            new_line_map = {}
            for k, v, in line_map.items():
                new_line_map[v] = k

            xutil.CacheUtil.set("line_map", new_line_map)

            line_list = list(new_line_map.keys())

            if not line_list:
                return self.x_response("false", f"未获取到线体！")

            line_item = getattr(other_param, 'combo_line')
            line_item.clear()
            for i in line_list:
                line_item.addItem(i)

            other_param.config_data['combo']['line']['item'] = line_list
            other_param.config_data['combo']['line']['value'] = line_list[0]
            other_param.save_config_data_to_file()
