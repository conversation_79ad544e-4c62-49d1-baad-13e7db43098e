# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/5 上午11:30
# Author     ：sch
# version    ：python 3.8
# Description：东莞同欣
"""
import os
from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "dongguantongxin release v1.0.0.1",
        "device": "410B-D",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-05 11:30  init
""", }

    form = {
        "device_sn": {
            "ui_name": "设备序列号",
            "value": ""
        },
    }

    path = {
        "csv_data_path": {
            "ui_name": "文件保存路径",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        csv_data_path = data_vo.get_value_by_cons_key("csv_data_path")
        device_sn = data_vo.get_value_by_cons_key("device_sn")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name

        time_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        time_date = time_now[:8]

        ng_list = []
        ng_count = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            ng_count += board_entity.comp_robot_ng_number

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    comp_code = comp_entity.repair_ng_code

                    ng_list.append(f"{comp_tag}_{comp_code}")

        # 1.
        cache_data = xutil.CacheUtil.get_cache_data()

        ix = cache_data.get("index", 0)
        last_date = cache_data.get("currentDate", time_date)

        if last_date != time_date:
            ix = 0

        ix += 1
        cache_data["index"] = ix
        cache_data["currentDate"] = time_date
        xutil.CacheUtil.save_cache_data(cache_data)

        pcb_result = pcb_entity.get_repair_result("Y", "N")

        pcb_row = f"\n{ix},{project_name},{start_time},{ng_count},{pcb_result},{';'.join(ng_list)}"

        csv_filename = f"{csv_data_path}/{device_sn}_{project_name}_{time_date}.csv"

        if not os.path.exists(csv_filename):
            csv_content = "序号,产品名称,检测时间,需要人为判断器件数,检测是否通过,不通过的器件编号"
        else:
            csv_content = xutil.FileUtil.read_file(csv_filename)

        csv_content += pcb_row

        xutil.FileUtil.write_content_to_file(csv_filename, csv_content)

        return self.x_response()
