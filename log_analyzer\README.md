# 日志快速定位分析工具

一个专为MES系统和AOI检测设备设计的日志分析工具，支持日志模糊搜索、常见错误展示和问题快速定位。

## 功能特性

### 🔍 强大的搜索功能
- **模糊搜索**: 支持关键词模糊匹配
- **高级搜索**: 支持日志级别、时间范围、错误码等多维度过滤
- **正则表达式**: 支持复杂的模式匹配
- **快速搜索**: 针对常见错误提供快捷搜索

### 📊 智能错误分析
- **错误码识别**: 基于项目中的错误码映射自动识别
- **错误分类**: 按设备错误、网络错误、数据错误等分类统计
- **错误趋势**: 分析错误发生的时间趋势
- **错误模式**: 自动提取常见错误模式

### 📈 丰富的统计信息
- **日志级别分布**: 统计各级别日志数量
- **文件分布**: 显示各文件的日志数量
- **时间分布**: 按小时/天统计日志分布
- **错误热点**: 识别错误高发时段和文件

### 💾 多格式导出
- **CSV格式**: 适合Excel打开和数据分析
- **Excel格式**: 包含格式化和统计图表
- **JSON格式**: 适合程序处理
- **HTML报告**: 可视化的错误分析报告
- **XML格式**: 结构化数据导出

### ⚙️ 灵活的配置管理
- **路径管理**: 保存常用日志路径
- **搜索偏好**: 自定义搜索默认设置
- **错误码自定义**: 添加项目特定的错误码
- **界面个性化**: 字体、主题等界面设置

## 安装说明

### 环境要求
- Python 3.7+
- PyQt5 (GUI界面)
- openpyxl (Excel导出)

### 安装步骤

1. **克隆或下载项目**
```bash
# 将log_analyzer文件夹复制到您的项目目录
```

2. **安装依赖**
```bash
cd log_analyzer
pip install -r requirements.txt
```

3. **运行程序**
```bash
# GUI模式（推荐）
python main.py

# 命令行模式
python main.py --cli
```

## 使用指南

### GUI界面使用

#### 1. 加载日志
- 点击"浏览"按钮选择日志文件或目录
- 点击"加载日志"开始解析
- 支持的文件格式：.log, .txt

#### 2. 快速搜索
- 在搜索框输入关键词
- 点击"搜索"或按回车键
- 结果会在右侧表格中显示

#### 3. 高级搜索
- 选择日志级别过滤
- 设置时间范围
- 输入错误码
- 设置排除关键词
- 选择是否区分大小写

#### 4. 快捷功能
- **最近24小时错误**: 快速查看最近的错误日志
- **严重错误**: 显示所有CRITICAL和ERROR级别的日志
- **错误模式分析**: 分析常见错误模式

#### 5. 导出结果
- 在搜索结果页面点击"导出结果"
- 选择导出格式和保存位置
- 支持CSV、Excel、JSON等多种格式

### 命令行使用

#### 分析日志
```bash
# 分析指定目录的所有日志
python main.py analyze /path/to/logs --error-analysis --statistics

# 分析单个日志文件
python main.py analyze /path/to/app.log --error-analysis
```

#### 搜索日志
```bash
# 搜索包含"error"的日志
python main.py search /path/to/logs --keyword "error"

# 搜索特定级别的日志
python main.py search /path/to/logs --keyword "timeout" --level ERROR

# 搜索特定错误码
python main.py search /path/to/logs --keyword "failed" --error-code "255"
```

#### 导出日志
```bash
# 导出所有错误日志到CSV
python main.py export /path/to/logs --output errors.csv --level ERROR

# 导出包含关键词的日志到Excel
python main.py export /path/to/logs --output results.xlsx --format excel --keyword "timeout"
```

## 支持的日志格式

工具支持多种日志格式的自动识别：

### 1. 标准格式
```
2024-01-01 12:00:00 - [line:123] - INFO: 这是一条信息日志
2024-01-01 12:01:00 - [line:124] - ERROR: 这是一条错误日志
```

### 2. 简单格式
```
2024-01-01 12:00:00 ---Info--- 系统启动成功
2024-01-01 12:01:00 ---Error--- 连接数据库失败
```

### 3. HTML格式
```html
<span style='color:green'>2024-01-01 12:00:00 操作成功</span>
<span style='color:red'>2024-01-01 12:01:00 操作失败</span>
```

### 4. 自定义格式
工具会尝试从任何包含时间戳的日志行中提取信息。

## 错误码映射

工具内置了以下错误码映射：

### MES系统错误码
- `1`: 可生产/成功
- `16`: 生产线中设备不存在
- `17`: 工单对应工艺下无该设备
- `18`: 工单不存在
- `255`: 服务端未定义异常
- 更多...

### AOI检测错误码
- `0`: OK
- `1`: 漏件
- `2`: 错件
- `3`: 反件
- `4`: 立碑
- `5`: 偏移
- 更多...

### 设备状态码
- `01`: 进板
- `02`: 开始检测
- `12`: 紧急故障
- `20`: 数据超时
- `21`: 板卡NG
- 更多...

## 配置文件

配置文件保存在用户目录下的 `.log_analyzer` 文件夹中：

```
~/.log_analyzer/
├── config.json              # 主配置文件
└── custom_error_codes.json  # 自定义错误码
```

### 自定义错误码

您可以通过界面或直接编辑配置文件添加项目特定的错误码：

```json
{
  "9001": {
    "description": "自定义错误描述",
    "category": "自定义分类",
    "severity": "ERROR"
  }
}
```

## 常见问题

### Q: 如何处理大型日志文件？
A: 工具支持流式处理大型文件，但建议：
- 使用时间范围过滤减少数据量
- 优先搜索最近的日志
- 考虑分批处理超大文件

### Q: 支持哪些字符编码？
A: 工具默认使用UTF-8编码，同时支持：
- GBK/GB2312（中文）
- ASCII
- 其他常见编码（自动检测）

### Q: 如何添加新的日志格式？
A: 可以修改 `log_parser.py` 中的正则表达式模式，或联系开发者添加支持。

### Q: 导出的Excel文件打开乱码怎么办？
A: 工具使用UTF-8编码导出，如果Excel显示乱码，请：
1. 使用"数据" -> "从文本"导入
2. 选择UTF-8编码
3. 或使用WPS Office等支持UTF-8的软件

## 技术架构

```
log_analyzer/
├── main.py              # 主程序入口
├── log_parser.py        # 日志解析模块
├── log_searcher.py      # 日志搜索模块
├── error_analyzer.py    # 错误分析模块
├── log_exporter.py      # 日志导出模块
├── config_manager.py    # 配置管理模块
├── main_window.py       # GUI主界面
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 开发计划

- [ ] 支持实时日志监控
- [ ] 添加图表可视化
- [ ] 支持日志告警规则
- [ ] 集成邮件通知
- [ ] 支持多语言界面
- [ ] 添加插件系统

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本工具专为MES系统和AOI检测设备的日志分析而设计，包含了相关的错误码映射和分析规则。如需适配其他系统，可能需要调整错误码映射和分析规则。
