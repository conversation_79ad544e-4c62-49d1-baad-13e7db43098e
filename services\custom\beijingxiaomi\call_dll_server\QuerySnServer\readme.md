### 环境准备：
1. 运行过QuerySn项目
   - QuerySn项目目录和QuerySnServer项目在同级，直接运行[环境准备.bat]，会把需要的QuerySn的文件copy到QuerySnServer项目下。
2. 未运行过QuerySn项目
   - 直接从坚果云（https://drive.cvte.com/p/DYPJvbkQvcoGGJiZMyAA） 找一个[QuerySnServer_xxx.zip]下载即可
   - 解压后把QuerySn文件夹拷贝到QuerySnServer项目下即可

### 打包：
1. 直接运行[打包.bat],会在项目目录下生成QuerySnServer_时间.zip
2. 可以解压出来看看服务是否可以正常运行
   - 运行批处理文件startServer.bat启动服务
   - 运行批处理文件stopServer.bat可关闭服务

### 其它
 - 可以从这里（https://drive.cvte.com/p/DS_fGAcQvcoGGNCcMyAA） 查看QuerySnServer详细的使用说明