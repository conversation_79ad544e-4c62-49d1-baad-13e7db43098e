# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_s3.py
# Time       ：2024/11/29 下午10:59
# Author     ：sch
# version    ：python 3.8
# Description：测试 上传图片
"""

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError


def upload_to_s3_and_make_public(file_name, bucket, object_name=None, aws_access_key_id=None,
                                 aws_secret_access_key=None, region_name='us-east-1'):
    """
    上传文件到指定的 S3 Bucket 并设置为公共读取权限.

    :param file_name: 要上传的文件路径
    :param bucket: S3 Bucket 的名称
    :param object_name: 在 S3 中保存文件的对象名称 (默认为文件名)
    :param aws_access_key_id: AWS Access Key ID (可选)
    :param aws_secret_access_key: AWS Secret Access Key (可选)
    :param region_name: AWS Region (默认是 'us-east-1')
    :return: 如果上传成功并设置为公共，返回 True 和永久下载链接；否则返回 False 和 None
    """
    try:
        # 如果没有指定 object_name，则使用 file_name 作为对象名
        if object_name is None:
            object_name = file_name

        # 创建 S3 客户端
        s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
        )

        # 上传文件
        s3_client.upload_file(file_name, bucket, object_name)

        # 设置对象ACL为公共读取
        s3_client.put_object_acl(Bucket=bucket, Key=object_name, ACL='public-read')

        # 构建永久下载链接
        download_url = f"https://{bucket}.s3.{region_name}.amazonaws.com/{object_name}"

        print(f"File {file_name} uploaded to {bucket}/{object_name} and made public.")
        return True, download_url

    except FileNotFoundError:
        print(f"The file {file_name} was not found.")
        return False, None
    except (NoCredentialsError, PartialCredentialsError) as e:
        print("AWS credentials are not available or incomplete.")
        raise e
    except Exception as e:
        print(f"An error occurred while uploading the file or setting it public: {e}")
        return False, None


def get_s3_download_link(bucket_name, object_name, expiration=3600, aws_access_key_id=None, aws_secret_access_key=None,
                         region_name='us-east-1'):
    """
    生成S3对象的预签名下载链接。

    :param bucket_name: 包含对象的S3存储桶名称。
    :param object_name: S3中的对象名称。
    :param expiration: 链接的有效期（以秒为单位），默认为3600秒（1小时）。
    :param aws_access_key_id: AWS Access Key ID (可选)
    :param aws_secret_access_key: AWS Secret Access Key (可选)
    :param region_name: AWS Region (默认是 'us-east-1')
    :return: 返回预签名URL字符串。
    """
    try:
        # 创建S3客户端
        s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
        )

        # 生成预签名URL
        response = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': object_name},
            ExpiresIn=expiration
        )

        return response
    except (NoCredentialsError, PartialCredentialsError) as e:
        print("AWS credentials are not available or incomplete.")
        raise e
    except Exception as e:
        print(f"An error occurred while generating the presigned URL: {e}")
        raise e


# 使用函数示例
download_link = get_s3_download_link('my-bucket-name', 'myfile-in-s3.txt')
print(f"Download link: {download_link}")


def upload_to_s3(
        file_name,
        bucket,
        object_name=None,
        aws_access_key_id=None,
        aws_secret_access_key=None,
        region_name='us-east-1'
):
    """
    上传文件到指定的 S3 Bucket.

    :param file_name: 要上传的文件路径
    :param bucket: S3 Bucket 的名称
    :param object_name: 在 S3 中保存文件的对象名称 (默认为文件名)
    :param aws_access_key_id: AWS Access Key ID
    :param aws_secret_access_key: AWS Secret Access Key
    :param region_name: AWS Region (默认是 'us-east-1')
    :return: 如果上传成功，返回 True；否则返回 False
    """
    print(f"上传图片中，{bucket=} {object_name=}")

    try:
        # 如果没有指定 object_name，则使用 file_name 作为对象名
        if object_name is None:
            object_name = file_name

        # 创建 S3 客户端
        s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
        )

        try:
            # 上传文件
            s3_client.upload_file(file_name, bucket, object_name)
            print(f"File {file_name} uploaded to {bucket}/{object_name}")
            return True
        except FileNotFoundError:
            print(f"The file {file_name} was not found.")
            return False
        except NoCredentialsError:
            print("Credentials not available.")
            return False
    except Exception as err:
        print(f"上传图片失败,error: {err}")
        return False


if __name__ == '__main__':
    aws_access_key_id_ = ""
    aws_secret_access_key_ = ""
    src_img = "./COMP1163_1163.png"
    bucket_name_ = ""
    upload_to_s3(src_img, bucket_name_, "COMP1163_1163.png", aws_access_key_id_, aws_secret_access_key_)
