# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test.py
# Time       ：2023/4/7 上午9:04
# Author     ：sch
# version    ：python 3.8
# Description：镭晨测试版本
"""
import time
from typing import Any

from common import xenum
from common.xdecorator import send_data_to_mes_custom2, send_data_to_mes_custom3
from common.xutil import log, x_response
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo, ButtonVo


class Engine(ErrorMapEngine):
    version = {
        "title": "leichen release v1.1.1.5",
        "device": "20x,30x,40x",
        "feature": ["条码校验", "从mes获取条码", "发送数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-07 12:38  init
date: 2023-04-07 12:38  条码校验、发送数据功能添加
date: 2023-04-13 10:06  增加模拟发送功能
date: 2023-04-13 14:07  增加获取坏板信息
date: 2025-01-21 10:20  增加xml解析异常报错信息的打印
date: 2025-04-22 15:40  打印431算法数据
date: 2025-05-07 15:07  标准化，去除打印431算法数据
date: 2025-05-27 19:32  增加[从mes获取程序名]功能
date: 2025-06-25 16:54  增加日志打印
""",
    }

    combo = {
        "check_return": {
            "item": [
                "true", "false"
            ],
            "value": "true",
            "ui_name": "条码校验功能返回结果",
            "ui_name_en": "CheckBarcodeResult"
        },
        "send_data_return": {
            "item": [
                "true", "false"
            ],
            "value": "true",
            "ui_name": "发送mes功能返回结果",
            "ui_name_en": "SendMesResult"
        },
        "sleep_time": {
            "item": [
                "0", "1", "3", "5", "10", "20", "30", "50", "100", "200"
            ],
            "value": "0",
            "ui_name": "延迟等待时间(s)",
            "ui_name_en": "WaitTime"
        },
    }

    form = {
        "return_sn": {
            "ui_name": "从mes获取到的条码(多个用,号分隔)",
            "value": "sn001,sn002,sn003",
            "ui_name_en": "GetBarcodeResult"
        },
        "return_bad_info": {
            "ui_name": "从mes获取坏板信息(多个用,号分隔)",
            "value": "2",
            "ui_name_en": "GetBadInfoResult"
        },
        "return_project_name": {
            "ui_name": "从mes获取到的板式名",
            "value": "333.001",
            "ui_name_en": "GetProjectNameBySn"
        },
    }

    button = {
        "test1": {
            "ui_name": "测试1",
            "ui_name_en": "test1",
        }
    }

    other_path = {
        "save_path": {
            "ui_name": "保存路径",
            "ui_name_en": "Save Path",
            "value": ""
        }
    }

    def __init__(self):
        # self.set_lang_to_en()
        self.common_config["sendmes_setting3"] = xenum.SendMesSetting3.Send

    # @send_data_to_mes_custom3
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        self.log.info(data_vo.pcb_entity)

        for board_entity in data_vo.pcb_entity.yield_board_entity():
            log.info(board_entity)

            for comp_entity in board_entity.yield_comp_entity():
                log.info(comp_entity)

        send_data_ret = data_vo.get_value_by_cons_key("send_data_return")
        sleep_time = data_vo.get_value_by_cons_key("sleep_time", to_int=True)
        time.sleep(sleep_time)

        if send_data_ret == "false":
            return x_response("false", "发送mes数据失败")

        return x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_return = other_vo.get_value_by_cons_key("check_return")
        sleep_time = other_vo.get_value_by_cons_key("sleep_time")

        for i in range(int(sleep_time)):
            time.sleep(1)

        if check_return == "false":
            return x_response("false", "条码校验失败")
        return x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_status_str = other_vo.get_device_status_str()
        return x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        # pcb_sn = other_dao.get_pcb_sn()

        # other_param(f"使用设备扫到的第一个条码 {pcb_sn} 去获取拼板条码...")
        return_sn = other_vo.get_value_by_cons_key("return_sn")

        sleep_time = other_vo.get_value_by_cons_key("sleep_time")
        for i in range(int(sleep_time)):
            time.sleep(1)

        return x_response("true", return_sn)

    def send_idle_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        return x_response()

    def get_bad_board_info(self, other_vo: OtherVo, other_param: Any):
        return_bad_info = other_vo.get_value_by_cons_key("return_bad_info")

        log.info(f"将要返回的坏板信息：{return_bad_info}")

        return x_response("true", return_bad_info)

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):

        print(other_param.get_lang())

        return self.x_response()

    def get_project_name_by_sn(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        return_project_name = other_vo.get_value_by_cons_key("return_project_name")

        list_sn = other_vo.list_sn()

        if not list_sn:
            return self.x_response("false", "未扫到条码！")

        self.log.info(f"sn list: {list_sn}")

        return self.x_response("true", return_project_name)
