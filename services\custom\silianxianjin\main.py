# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/6 上午10:03
# Author     ：sch
# version    ：python 3.8
# Description：四联先进
"""
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

check_template = """<data>
    <order_code>{order_code}</order_code>
    <work_line>{work_line}</work_line>
    <work_station>{work_station}</work_station>
    <work_shift>{work_shift}</work_shift>
    <seq_code>{seq_code}</seq_code>
    <machine_code>{machine_code}</machine_code>
    <operator_code>{operator_code}</operator_code>
    <barcode>{barcode}</barcode>
</data>"""

data_template = """<data>
    <seq_code>{seq_code}</seq_code>
    <barcode>{barcode}</barcode>
    <result>{result}</result>
    <mc_result>{mc_result}</mc_result>
    <order_code>{order_code}</order_code>
    <work_line>{work_line}</work_line>
    <work_station>{work_station}</work_station>
    <work_shift>{work_shift}</work_shift>
    <machine_code>{machine_code}</machine_code>
    <operator_code>{operator_code}</operator_code>
    <ng_remark>{ng_remark}</ng_remark>
    <test_data></test_data>
    <test_items>{test_item_data}
    </test_items>
</data>"""

item_template = """
        <item id="{id}" name="{name}" lsl="{lsl}" std="{usl}" usl="{usl}" um="" val="{val}"
              result="{result}" mc_result="{mc_result}" remark="{remark}" time="{test_time}"/>"""


class Engine(BaseEngine):
    version = {
        "title": "silianxianjin release v1.0.0.14",
        "device": "63x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-06 10:04  init
date: 2023-07-06 15:07  条码校验，上传数据
date: 2023-07-27 10:57  result传PASS/FAIL
date: 2023-07-31 15:44  工序代码分为T/B面配置
date: 2023-08-02 16:46  兼容SPI的算法Result
date: 2023-08-05 10:13  程序名后缀匹配TOP/BOT,增加输出复判后的器件结果
date: 2023-08-09 15:21  上传参数修改
date: 2023-08-25 12:57  上传参数修改
date: 2023-08-25 18:11  id传序号
date: 2023-08-25 19:47  name传测试项名称
date: 2023-09-13 18:24  上传参数修改，见需求文档
date: 2023-09-14 18:04  SPI检测不良描述区分出细分项
date: 2024-08-06 14:25  未扫到条码，弹窗报警 
date: 2024-08-29 14:58  兼容AOI设备
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://192.168.0.240:1690/MesService.asmx"
        },
        "seq_code_t": {
            "ui_name": "工序代码(T面)",
            "value": "S010",
        },
        "seq_code_b": {
            "ui_name": "工序代码(B面)",
            "value": "S010",
        },
        "work_line": {
            "ui_name": "线别代码",
            "value": "WL001",
        },
        "work_station": {
            "ui_name": "工位代码",
            "value": "WS001",
        },
        "work_shift": {
            "ui_name": "班别",
            "value": "白班",
        },
        "order_code": {
            "ui_name": "工单号",
            "value": "378121",
        },
        "machine_code": {
            "ui_name": "机台号",
            "value": "M001",
        },
        "operator_code": {
            "ui_name": "操作员工号",
            "value": "1001",
        },
    }

    combo = {
        "device_type": {
            "ui_name": "设备类型",
            "item": ["SPI", "AOI"],
            "value": "SPI"
        }
    }

    @staticmethod
    def parse_ret_str(ret_str):
        root_ret = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        result = root_ret.find("result").text if root_ret.find("result") is not None else ""
        error = root_ret.find("error").text if root_ret.find("error") is not None else ""
        return result, error

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        order_code = other_vo.get_value_by_cons_key("order_code")
        work_line = other_vo.get_value_by_cons_key("work_line")
        work_station = other_vo.get_value_by_cons_key("work_station")
        work_shift = other_vo.get_value_by_cons_key("work_shift")
        machine_code = other_vo.get_value_by_cons_key("machine_code")
        operator_code = other_vo.get_value_by_cons_key("operator_code")

        if not other_vo.list_sn():
            return self.x_response("false", f"未扫到条码！")

        project_name = other_vo.get_project_name()
        self.log.info(f"程序名：{project_name}")

        project_name = project_name.upper()

        if project_name.endswith("BOT"):
            seq_code = other_vo.get_value_by_cons_key("seq_code_b")
        elif project_name.endswith("TOP"):
            seq_code = other_vo.get_value_by_cons_key("seq_code_t")
        else:
            seq_code = other_vo.get_value_by_cons_key("seq_code_t")
            self.log.warning(f"警告，未找到程序后缀T/B，将用【工序代码(T面)】上传！")

        for sn in other_vo.list_sn():
            check_param = check_template.format(**{
                "barcode": sn,
                "order_code": order_code,
                "work_line": work_line,
                "work_station": work_station,
                "work_shift": work_shift,
                "seq_code": seq_code,
                "machine_code": machine_code,
                "operator_code": operator_code,
            })

            check_url = f"{api_url}/CheckBarcode"
            # headers = {
            #     "Content-Type": "application/xml"
            # }
            ret_text = xrequest.RequestUtil.post_xml(check_url, check_param)

            result, error = self.parse_ret_str(ret_text)
            if result != "OK":
                return self.x_response("false", f"mes接口异常，条码校验失败，error：{error}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        operator_code = data_vo.get_value_by_cons_key("operator_code")
        order_code = data_vo.get_value_by_cons_key("order_code")
        work_line = data_vo.get_value_by_cons_key("work_line")
        work_station = data_vo.get_value_by_cons_key("work_station")
        work_shift = data_vo.get_value_by_cons_key("work_shift")
        machine_code = data_vo.get_value_by_cons_key("machine_code")
        device_type = data_vo.get_value_by_cons_key("device_type")

        if device_type == "SPI":
            true_flag = "1"
        else:
            true_flag = "0"

        self.log.info(f"true_flag: {true_flag}")

        data_url = f"{api_url}/CreateWork"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        project_name = pcb_entity.project_name.upper()

        if project_name.endswith("BOT"):
            seq_code = data_vo.get_value_by_cons_key("seq_code_b")
        elif project_name.endswith("TOP"):
            seq_code = data_vo.get_value_by_cons_key("seq_code_t")
        else:
            seq_code = data_vo.get_value_by_cons_key("seq_code_t")
            self.log.warning(f"警告，未找到程序后缀TOP/BOT，将用【工序代码(T面)】上传！")

        if device_type == "SPI":
            comp_pad_data, _ = pcb_entity.get_pad_test_data()
        else:
            comp_pad_data = {}

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            comp_ix = 0
            test_item_data = ""

            ng_remark_list = []
            for comp_entity in board_entity.yield_comp_entity():
                robot_ng_code = comp_entity.robot_ng_code

                robot_ng_str = comp_entity.robot_ng_str

                if robot_ng_code == "34" and comp_pad_data:
                    comp_uuid = comp_entity.comp_id.replace("{", "").replace("}", "")

                    pad_list = comp_pad_data.get(comp_uuid, [])

                    if pad_list:
                        pad1 = pad_list[0]

                        pad_result = pad1.get("result")
                        robot_ng_str = xcons.AIS630_ERROR_MAP.get(pad_result, {}).get("standard", robot_ng_str)

                # ng_str = comp_entity.repair_ng_str

                # tmp_name = "" if comp_entity.repair_result else comp_entity.robot_ng_str
                comp_tag = comp_entity.designator

                remark = ""
                if not comp_entity.robot_result:
                    remark += f"J:{robot_ng_str}"

                if not comp_entity.repair_result:
                    remark += f"/R:{comp_entity.repair_ng_str}"

                ng_remark_list.append(remark)

                for alg in comp_entity.alg_data:
                    robot_result = "OK" if alg.get("result", "") == true_flag else "NG"
                    repair_result = "OK" if comp_entity.repair_result else "NG"

                    # result = f"{robot_result}/{repair_result}"

                    comp_ix += 1
                    test_item_data += item_template.format(**{
                        # "id": comp_entity.designator,
                        "id": comp_ix,
                        # "name": tmp_name,
                        # "name": alg.get("test_name", ""),
                        "name": f'{comp_tag}_{alg.get("test_name", "")}',
                        "lsl": alg.get("max_threshold", ""),
                        "usl": alg.get("mix_threshold", ""),
                        "val": alg.get("test_val", ""),
                        # "result": result,
                        "result": repair_result,
                        "mc_result": robot_result,
                        "test_time": test_time,
                        "remark": remark,
                    })

            data_param = data_template.format(**{
                "seq_code": seq_code,
                "barcode": barcode,
                "result": board_entity.get_repair_result("PASS", "FAIL"),
                "mc_result": board_entity.get_robot_result("PASS", "FAIL"),
                "order_code": order_code,
                "work_line": work_line,
                "work_station": work_station,
                "work_shift": work_shift,
                "machine_code": machine_code,
                "operator_code": operator_code,
                "ng_remark": ",".join(ng_remark_list),
                "test_item_data": test_item_data,
            })

            ret_str = xrequest.RequestUtil.post_xml(data_url, data_param)
            result, error = self.parse_ret_str(ret_str)
            if result != "OK":
                return self.x_response("false", f"mes接口异常，上传数据失败error：{error}")

        return self.x_response()
