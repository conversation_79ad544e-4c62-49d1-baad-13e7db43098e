# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/12 上午10:43
# Author     ：sch
# version    ：python 3.8
# Description：惠州盛微
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

row_template = """
{board_no},{start_time},{cycle_time},{board_result},{barcode},{project_name},{project_path},{comp_number},{height},{area},{volume},{x_offset},{y_offset}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoushengwei release v1.0.0.2",
        "device": "630",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-12 10:44  生成csv数据
date: 2024-03-12 16:08  编码改为gbk
""", }

    path = {
        "save_path_csv": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_csv = data_vo.get_value_by_cons_key("save_path_csv", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_content = f"PCB编号,测试时间,测试用时,测试结果,二维码,PCB名称,程序路径,检测框总数,平均高度,平均面积,平均体积,平均X偏移,平均Y偏移"
        t1 = pcb_entity.get_start_time()
        start_time = t1.strftime(xcons.FMT_TIME_DEFAULT1)
        time_file = t1.strftime(xcons.FMT_TIME_FILE)
        cycle_time = pcb_entity.get_cycle_time()

        project_name = pcb_entity.project_name

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            height = ""
            area = ""
            volume = ""
            x_offset = ""
            y_offset = ""

            for comp_entity in board_entity.yield_comp_entity():

                if not all([
                    height, area, volume, x_offset, y_offset
                ]):
                    for alg_entity in comp_entity.yield_alg_entity():
                        alg_test_name = alg_entity.test_name
                        alg_test_val = alg_entity.test_val

                        if alg_test_name == 'Area':
                            area = alg_test_val
                        elif alg_test_name == 'Volume':
                            volume = alg_test_val
                        elif alg_test_name == 'Height':
                            height = alg_test_val
                        elif alg_test_name == 'XOffset':
                            x_offset = alg_test_val
                        elif alg_test_name == 'YOffset':
                            y_offset = alg_test_val

                if comp_entity.is_robot_ng():
                    for alg_entity in comp_entity.yield_alg_entity():
                        alg_test_name = alg_entity.test_name
                        alg_test_val = alg_entity.test_val

                        if alg_test_name == 'Area':
                            area = alg_test_val
                        elif alg_test_name == 'Volume':
                            volume = alg_test_val
                        elif alg_test_name == 'Height':
                            height = alg_test_val
                        elif alg_test_name == 'XOffset':
                            x_offset = alg_test_val
                        elif alg_test_name == 'YOffset':
                            y_offset = alg_test_val

                    break

            pcb_content += row_template.format(**{
                "board_no": board_entity.board_no,
                "start_time": start_time,
                "cycle_time": cycle_time,
                "board_result": board_entity.get_repair_result("PASS", "NG"),
                "barcode": barcode,
                "project_name": project_name,
                "project_path": f"/home/<USER>/aoi/program/projects/{project_name}",
                "comp_number": board_entity.comp_total_number,
                "height": height,
                "area": area,
                "volume": volume,
                "x_offset": x_offset,
                "y_offset": y_offset,
            })

        filepath = f"{save_path_csv}/{time_file}_{pcb_sn}.csv"
        xutil.FileUtil.write_content_to_file(filepath, pcb_content, encoding='gbk')

        return self.x_response()
