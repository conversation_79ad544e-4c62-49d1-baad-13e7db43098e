# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : tcp_server.py
# Time       ：2024/8/17 上午9:32
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import socket
import struct

ret_str = """<?xml version="1.0" encoding="utf-8"?><SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <SOAP-ENV:Body>
    <ns2:dataCollectForSfcExResponse xmlns:ns2="http://machine.ws.atlmes.com/">
      <return>
        <code>0</code>
        <message>ok</message>
      </return>
    </ns2:dataCollectForSfcExResponse>
  </SOAP-ENV:Body>
</SOAP-ENV:Envelope>"""


sk = socket.socket()
sk.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

sk.bind(("0.0.0.0", 9099))
sk.listen()

PACKETSTART = b'PACKET_START'
PACKETEND = b'PACKET_END'
PACKETTYPE = b'02'

part = 50000

# 包数据结构：  包头[12]+长度[8]+包类型[2]+数据[N]+包尾[10]
print("runing....")

while True:
    conn, addr = sk.accept()
    print(f"recv from: {addr}")

    packet_start = conn.recv(1024)
    print("data len", len(packet_start))
    print(packet_start)

    ret_data = {"result": True, "string": ret_str}

    print("-------------开始向客户端发消息")
    # with open("./ret_data.txt", "r") as f:
    #     ret_data = json.load(f)
    ret_data = json.dumps(ret_data, ensure_ascii=False).encode('utf8')

    ret_len = struct.pack("!q", len(ret_data))
    print(ret_data)

    ret_full_param = PACKETSTART + ret_len + PACKETTYPE + ret_data + PACKETEND

    conn.send(ret_full_param)
    print("发送完毕")

    conn.close()