# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/19 下午4:01
# Author     ：sch
# version    ：python 3.8
# Description：锐捷网络
"""
import time
from typing import Any

from common import xrequest, xutil, xenum
from common.xutil import filter_v3_status_code
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "ruijiewangluo release v1.0.0.6",
        "device": "501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-19 16:01  init
date: 2024-03-22 09:18  条码校验，上传数据
date: 2024-03-26 15:40  出站接口 Function参数改为MoveOut
date: 2024-03-26 15:40  上传关键数据接口 Function参数改为MoveOut
date: 2024-03-27 16:09  上传关键数据接口 Function参数改为Parameter
date: 2024-05-16 16:45  出站接口参数调整
""", }

    form = {
        "operator": {
            "ui_name": "操作员ID",
            "value": "",
        },
        "process_id": {
            "ui_name": "工序",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单",
            "value": "",
        },
        "product_id": {
            "ui_name": "产品名称",
            "value": "",
        },
    }

    other_form = {
        "api_url1": {
            "ui_name": "接口URL(设备进站接口)",
            "value": "",
        },
        "api_url2": {
            "ui_name": "接口URL(设备出站接口)",
            "value": "",
        },
        "api_url3": {
            "ui_name": "接口URL(关键参数上传接口)",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备号",
            "value": "",
        },
        "line_code": {
            "ui_name": "线体号",
            "value": "",
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "value": "/MES/SMT"
        },
    }

    other_combo = {
        "is_upload_ftp": {
            "ui_name": "上传图片到FTP服务器",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
    }

    def __init__(self):
        self.app_setting['http_server_run'] = True
        self.common_config['check_barcode_setting1'] = xenum.CheckSetting1.CheckFirst

        xutil.CacheUtil.append_or_update_new_data({
            'device_status_current_state': '设备停止',
            'device_status_current_str': '',
            'X_time_device_run': int(time.time()),
            'X_time_stop_count': 0,
            'X_time_stop': None,
            'X_time_except_count': 0,
            'X_time_except': None,
        })

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url1 = other_vo.get_value_by_cons_key("api_url1")
        device_code = other_vo.get_value_by_cons_key("device_code")
        line_code = other_vo.get_value_by_cons_key("line_code")
        operator = other_vo.get_value_by_cons_key("operator")
        process_id = other_vo.get_value_by_cons_key("process_id")
        order_id = other_vo.get_value_by_cons_key("order_id")
        product_id = other_vo.get_value_by_cons_key("product_id")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "Container": sn,
                "Line": line_code,
                "Resource": device_code,
                "Function": "MoveIn",
                "Operator": operator,
                "MfgOrder": order_id,
                "Product": product_id,
                "Spec": process_id
            }

            self.log.info(f"开始调用设备进站接口....")
            ret1 = xrequest.RequestUtil.post_json(api_url1, check_param)
            if ret1.get('Result') != 'OK':
                error_msg = ret1.get('ErrorException')

                if not error_msg:
                    error_msg = ret1.get('errorException')

                ret_res = self.x_response("false", f"mes接口异常，设备进站失败，error：{error_msg}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url2 = data_vo.get_value_by_cons_key("api_url2")
        api_url3 = data_vo.get_value_by_cons_key("api_url3")
        device_code = data_vo.get_value_by_cons_key("device_code")
        line_code = data_vo.get_value_by_cons_key("line_code")
        operator = data_vo.get_value_by_cons_key("operator")
        process_id = data_vo.get_value_by_cons_key("process_id")
        order_id = data_vo.get_value_by_cons_key("order_id")
        product_id = data_vo.get_value_by_cons_key("product_id")

        is_upload_ftp = data_vo.get_value_by_cons_key("is_upload_ftp")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = int(data_vo.get_value_by_cons_key("ftp_port"))
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_data = []

        if is_upload_ftp == "Yes":
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()
            ftp_client.cd_or_mkdir(ftp_path)
        else:
            ftp_client = None
            self.log.warning(f"已配置不上传图片到FTP服务器！")

        ret_res = self.x_response()
        checkout_board = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data = []

            data_list = {
                "Straight": board_entity.get_robot_result("OK", "NG"),
                "Manual": board_entity.get_repair_result("OK", "NG"),
                "NGPIC": "",
                "SN": barcode,
            }

            comp_ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                comp_src_image = comp_entity.image_path
                comp_tag = comp_entity.designator
                robot_ng_str = comp_entity.robot_ng_str
                repair_ng_str = comp_entity.repair_ng_str

                if comp_src_image:
                    # 上传图片
                    if ftp_client is not None:
                        ftp_dst_img = f"{board_no}_{barcode}_{comp_tag}_{robot_ng_str}.png"
                        ftp_client.upload_file(comp_src_image, ftp_dst_img)

                        comp_full_path = f"{ftp_host}{ftp_path}/{ftp_dst_img}"
                    else:
                        comp_full_path = comp_src_image
                else:
                    comp_full_path = ""

                # if comp_entity.is_repair_ng() and comp_full_path:
                #     comp_img_list.append(comp_full_path)

                if comp_entity.is_repair_ng():
                    comp_ix += 1
                    data_list[f"StraightNG_{comp_ix}"] = f"{comp_tag};{robot_ng_str};{comp_full_path}"
                    data_list[f"ManualNG_{comp_ix}"] = f"{comp_tag};{repair_ng_str};{comp_full_path}"

                comp_data.append({
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_full_path,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result(),
                "board_user_result": board_entity.get_repair_result(),
                "comp_data": comp_data
            })

            checkout_board.append({
                "ListType": board_no,
                "DetailList": data_list
            })

        if ftp_client is not None:
            ftp_client.close()

        data_param = {
            "Container": pcb_sn,
            "Line": line_code,
            "Resource": device_code,
            "Function": "Parameter",
            "Operator": operator,
            "Result": pcb_entity.get_repair_result(),
            "MfgOrder": order_id,
            "Product": product_id,
            "Spec": process_id,
            "board_data": board_data
        }

        checkout_param = {
            "Container": pcb_sn,
            "Line": line_code,
            "Resource": device_code,
            "Function": "MoveOut",
            "Operator": operator,
            "Result": pcb_entity.get_repair_result(),
            "MfgOrder": order_id,
            "Product": product_id,
            "Spec": process_id,
            "DataList": checkout_board
        }

        self.log.info(f"开始上传关键参数(详细数据)到mes....")
        ret3 = xrequest.RequestUtil.post_json(api_url3, data_param)

        if ret3.get('Result') != 'OK':
            error_msg = ret3.get('ErrorException')

            if not error_msg:
                error_msg = ret3.get('errorException')

            ret_res = self.x_response("false", f"mes接口异常，关键参数上传失败，error：{error_msg}")

        self.log.info(f"开始调用设备出站接口....")
        ret2 = xrequest.RequestUtil.post_json(api_url2, checkout_param)

        if ret2.get('Result') != 'OK':

            error_msg = ret2.get('ErrorException')

            if not error_msg:
                error_msg = ret2.get('errorException')

            ret_res = self.x_response("false", f"mes接口异常，设备出站失败，error：{error_msg}")

        return ret_res

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        status_code = other_vo.get_status_code()

        if status_code in ['999-01', '999-04']:
            self.log.warning(f"进出板状态不处理！")
            return self.x_response()

        self.log.info(f'status code: {status_code}')

        current_time_now = int(time.time())
        cache_data = xutil.CacheUtil.get_cache_data()

        if status_code == "01":
            current_status = '设备运行'
            error_desc = ''

            # ---------计算稼动率
            time_except = cache_data.get("X_time_except", None)  # 异常时间点
            time_stop = cache_data.get("X_time_stop", None)  # 停止检查时间点
            if time_except:
                # 如果有上一次异常时间，则算一下<故障总时长>，算完之后将异常时间清空
                time_except_delta = int(current_time_now - time_except)
                self.log.info(f"本次异常占用时长：{time_except_delta}s")

                xutil.CacheUtil.append_or_update_new_data({
                    'X_time_except_count': int(cache_data.get('X_time_except_count', 0) + time_except_delta),
                    'X_time_except': None  # 时间点用过了之后需要删掉
                })

            if time_stop:
                # 如果有上一次停止，则算一下<停止总时长>，算完之后将异常时间清空
                time_stop_delta = int(current_time_now - time_stop)
                self.log.info(f"本次暂停时长：{time_stop_delta}s")

                xutil.CacheUtil.append_or_update_new_data({
                    'X_time_stop_count': int(cache_data.get('X_time_stop_count', 0) + time_stop_delta),
                    'X_time_stop': None  # 时间点用过了之后需要删掉
                })
            # ---------计算稼动率

        elif status_code == '02':
            current_status = '设备停止'
            error_desc = ''
            if not cache_data.get('X_time_stop'):
                xutil.CacheUtil.set('X_time_stop', current_time_now)

        else:
            current_status = '设备故障'
            error_desc = other_vo.get_status_desc()

            if not cache_data.get('X_time_except'):
                xutil.CacheUtil.set('X_time_except', current_time_now)

        xutil.CacheUtil.append_or_update_new_data({
            'device_status_current_state': current_status,
            'device_status_current_str': error_desc
        })

        return self.x_response()

    def send_idle_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        xutil.CacheUtil.set('device_status_current_state', "设备空闲")

        return self.x_response()
