# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/28 上午10:16
# Author     ：sch
# version    ：python 3.8
# Description：福建星云
"""

from typing import Any

from common import xcons, xutil, xrequest
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

csv_pcb_panel_template = """程序名,{pcb_project_name}
测试时间,{pcb_test_time}
复判操作员,{pcb_repair_user}
整板条码,{pcb_sn}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}
整板图片路径,{pcb_image}

BoardNo,Barcode,BoardFinalResult,CompDesignator,CompPart,CompPackage,CompType,CompRobotCode,CompRobotResult,CompUserCode,CompUserResult,CompImage{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "fujianxingyun release v1.0.0.4",
        "device": "203P、303-l",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-28 14:39  done
date: 2023-08-26 11:35  设备状态上传
date: 2023-11-21 14:46  csv文档输出所有器件数据
date: 2023-12-08 11:41  新增自定义不良代码功能
""", }

    path = {
        "csv_data_path": {
            "ui_name": "数据文本路径",
            "value": "",
        },
        "pcb_img_path": {
            "ui_name": "整板图路径",
            "value": "",
        },
        "comp_img_path": {
            "ui_name": "器件图路径",
            "value": "",
        },
    }

    form = {
        "device_api_url": {
            "ui_name": "设备状态接口URL",
            "value": ""
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": ""
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        csv_data_path = data_vo.get_value_by_cons_key("csv_data_path")
        pcb_img_path = data_vo.get_value_by_cons_key("pcb_img_path")
        comp_img_path = data_vo.get_value_by_cons_key("comp_img_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        pcb_sn = pcb_entity.pcb_barcode

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        comp_data_str = ""

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = pcb_sn

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            for comp_entity in board_entity.yield_comp_entity():
                comp_src_file = comp_entity.image_path
                comp_tag = comp_entity.designator
                robot_ng_code = comp_entity.robot_ng_code

                if comp_src_file:
                    comp_dst_file = f"{comp_img_path}/{barcode}_{comp_tag}_{robot_ng_code}"
                    suffix = xutil.FileUtil.copy_file(comp_src_file, comp_dst_file, is_auto_add_suffix=True)
                    dst_filename = f"{comp_dst_file}{suffix}"
                else:
                    dst_filename = ""

                # if comp_entity.is_robot_ng():
                comp_data_str += csv_comp_panel_template.format(**{
                    "board_no": board_no,
                    "board_sn": barcode,
                    "board_final_result": board_entity.get_final_result(),
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": dst_filename,
                })

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        pcb_src_image = pcb_entity.get_unknown_t_pcb_image()
        pcb_dst_image = f"{pcb_img_path}/{pcb_sn}_{time_file}.jpg"
        xutil.FileUtil.copy_file(pcb_src_image, pcb_dst_image)

        csv_content = csv_pcb_panel_template.format(**{
            "pcb_image": pcb_dst_image,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "CompData": comp_data_str
        })
        csv_dst_filepath = f"{csv_data_path}/{pcb_sn}_{time_file}.csv"
        xutil.FileUtil.write_content_to_file(csv_dst_filepath, csv_content)

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_code = other_vo.get_value_by_cons_key("device_code")
        device_name = other_vo.get_value_by_cons_key("device_name")
        device_api_url = other_vo.get_value_by_cons_key("device_api_url")

        device_status_str = other_vo.get_device_status_str()
        device_status_code = other_vo.get_old_device_status_code()

        if device_status_code == "02":
            remove1 = "1"
        else:
            remove1 = "0"

        req_param = {
            "devInfo": [
                {
                    "machineCode": device_code,
                    "machineName": device_name,
                    "tCode": device_status_code,
                    "desc": device_status_str,
                    "createTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                    "remove": remove1,
                }
            ]
        }

        ret = xrequest.RequestUtil.post_json(device_api_url, req_param)
        result = ret.get("result", {})

        if str(result.get("code")) != "0":
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{result.get('msg')}")

        return self.x_response()
