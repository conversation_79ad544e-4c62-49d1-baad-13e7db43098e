# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/4/8 上午9:10
# Author     ：sch
# version    ：python 3.8
# Description：深圳崧盛
"""
import json
import os
import traceback
from datetime import datetime
from typing import Any
from common import xrequest, xcons, xutil
from common.xconfig import home_dir
from common.xutil import log, x_response, filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import BaseEngine

AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "AOI-001", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "AOI-002", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "AOI-003", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "AOI-004", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "AOI-005", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "AOI-006", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "AOI-007", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "AOI-008", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "AOI-009", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "AOI-010", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "AOI-011", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "0029", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "0030", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "AOI-027", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "AOI-012", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "AOI-013", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "AOI-014", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "AOI-015", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "0036", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "0037", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "AOI-016", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "AOI-017", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "AOI-018", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "AOI-019", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "AOI-020", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "0043", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "AOI-021", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "AOI-022", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "AOI-023", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "AOI-024", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "AOI-025", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}

# 上传失败缓存目录以及缓存信息文件
FAIL_CACHE_DIR = f"{home_dir}/.aoi/mes_fail_cache"
FAIL_MES_DATA_FILE = f'{FAIL_CACHE_DIR}/mes_data.json'


def post_device_status_api(status_url, dev_id, state, msg=""):
    """
    发送设备状态给Mes
    :param status_url:
    :param dev_id:
    :param state:
    :param msg:
    :return:
    """
    status_param = {
        "DEV_ID": dev_id,
        "STATUS": state,
        "MSG": msg
    }
    ret = xrequest.RequestUtil.post_json(status_url, status_param)
    if ret.get("ERR_CODE") != "OK":
        return x_response("false", f"mes接口响应异常，上传设备状态失败，error：{ret.get('ERR_MSG')}")
    else:
        return None


class Engine(BaseEngine):
    version = {
        "title": "shenzhensongsheng release v1.1.3.12",
        "device": "40x,50x",
        "feature": ["条码校验", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-08 09:12  init
date: 2023-04-08 11:15  条码校验，发送数据到Mes，发送设备状态
date: 2023-04-14 14:19  接口地址由客户填写全URL,FILE_NAME传复判文件man.xml地址
date: 2023-04-18 17:17  修改不良代码
date: 2023-06-15 11:49  设备未扫到条码，则报警提示
date: 2023-06-15 17:12  未扫到一个条码，就需要报警提示
date: 2023-07-27 16:29  当某个拼板扫不到条码时停机报警
date: 2023-08-04 17:12  增加传输误报的器件数据
date: 2023-08-24 10:44  增加请求参数
date: 2023-08-31 15:42  兼容安全门状态
date: 2023-09-08 11:53  上传完所有拼板数据再弹窗提示
date: 2023-10-16 16:10  需求变更，见需求文档【深圳崧盛MES需求确认单20230922.docx】
date: 2023-10-19 18:10  保存机器NG器件图
date: 2023-10-26 15:40  修改图片保存路径
date: 2023-10-31 09:20  拷贝的器件图增加后缀
date: 2024-04-18 14:13  网络失败造成的发送失败也不能影响其他板卡发送 
date: 2025-03-21 15:30  jira:23997 新增一键重传功能
date: 2025-07-15 10:19  bugfix：mes_data.json内容为空导致报错（应是open期间异常中断导致）
""",
    }

    form = {
        "api_url_check": {
            "ui_name": "条码过站URL",
            "value": "http://127.0.0.1:9996/IMS/GET_CHECK_LB"
        },
        "api_url_upload": {
            "ui_name": "过站上传URL",
            "value": "http://127.0.0.1:9996/IMS/RECEIVE_LB"
        },
        "api_url_device_status": {
            "ui_name": "设备状态",
            "value": "http://127.0.0.1:9996/IMS/UploadStatus"
        },
        "wp_id": {
            "ui_name": "工序编码",
            "value": "AOI"
        },
        "dev_id": {
            "ui_name": "设备编码",
            "value": "AOI01"
        },
        "mo_id": {
            "ui_name": "工单编码",
            "value": ""
        },

    }

    button = {
        "modify_mo": {
            "ui_name": "修改工单"
        },
        "upload_fail_data": {
            "ui_name": "一键重传"
        },
    }

    path = {
        "save_path": {
            "ui_name": "NG器件图路径",
            "value": "",
        }
    }

    combo = {
        "data_type": {
            "ui_name": "上传数据类型",
            "item": ["SMT", "DIP"],
            "value": "SMT"
        },
    }

    def __init__(self):
        self.other_vo = None

    def _upload_fail_data(self):
        """
        点击一键重传按钮时触发上传，上传失败重新缓存
        缓存数据结构：{
            "DIP":{
                "save_time1":{"NG":{data},"PASS":{data}},
                "save_time2":{"NG":{data},"PASS":{data}},
            },
            "SMT":{
                "save_time1":{data1},
                "save_time2":{data2}
            }
        }
        """
        # 如果没有缓存文件直接返回
        if not os.path.exists(FAIL_MES_DATA_FILE):
            return self.x_response()

        self.log.info(f'开始上传失败缓存文件：{FAIL_MES_DATA_FILE}')

        api_url = self.other_vo.get_value_by_cons_key("api_url_upload", not_null=True)

        all_fail_data = xutil.FileUtil.load_json_file(FAIL_MES_DATA_FILE)
        new_all_fail_data = {'DIP': {}, 'SMT': {}}
        dip_fail_data = all_fail_data.get('DIP', {})
        err_msg_dip = ''
        for save_time_str, fail_data in dip_fail_data.items():
            # 超过3天丢弃
            cur_time = datetime.now()
            save_time = datetime.strptime(save_time_str, xcons.FMT_TIME_FILE1)
            time_diff = cur_time - save_time
            if time_diff.days > 3:
                self.log.info(f'DIP数据缓存超过三天丢弃')
                continue

            new_fail_data = {}
            is_upload_fail = False
            try:
                # 按原发送顺序，先发NG，再发PASS
                ng_data = fail_data.get('NG')
                pass_data = fail_data.get('PASS')
                # 先缓存，确保网络抛出异常时能进行缓存
                new_fail_data['NG'] = ng_data
                new_fail_data['PASS'] = pass_data

                if ng_data:
                    ret = xrequest.RequestUtil.post_json(api_url, ng_data)
                    if ret.get("ERR_CODE") != "OK":
                        if not err_msg_dip:
                            err_msg_dip = 'mes接口响应异常，DIP上传数据失败，error：'
                        err_msg_dip += f"NG -- {ret.get('ERR_MSG')};"
                        is_upload_fail = True
                    else:
                        new_fail_data.pop('NG')

                if pass_data:
                    ret = xrequest.RequestUtil.post_json(api_url, pass_data)
                    if ret.get("ERR_CODE") != "OK":
                        if not err_msg_dip:
                            err_msg_dip = 'mes接口响应异常，DIP上传数据失败，error：'
                        err_msg_dip += f"PASS -- {ret.get('ERR_MSG')};"
                        is_upload_fail = True
                    else:
                        new_fail_data.pop('PASS')

            except Exception as e:
                err_msg_dip = f"本地网络异常，上传失败，error：{e}"
                is_upload_fail = True
            finally:
                if is_upload_fail:
                    cur_time_str = cur_time.strftime(xcons.FMT_TIME_FILE1)
                    new_all_fail_data['DIP'].update({cur_time_str: new_fail_data})

        smt_fail_data = all_fail_data.get('SMT', [])
        err_msg_smt = ''
        for save_time_str, fail_data in smt_fail_data.items():
            # 超过3天丢弃
            cur_time = datetime.now()
            save_time = datetime.strptime(save_time_str, xcons.FMT_TIME_FILE1)
            time_diff = cur_time - save_time
            if time_diff.days > 3:
                self.log.info(f'SMT数据缓存超过三天丢弃')
                continue

            new_fail_data = fail_data
            is_upload_fail = False
            try:
                ret = xrequest.RequestUtil.post_json(api_url, fail_data)
                if ret.get("ERR_CODE") != "OK":
                    if not err_msg_smt:
                        err_msg_smt = 'mes接口响应异常，SMT上传数据失败，error：'
                    err_msg_smt += f"{ret.get('ERR_MSG')};"
                    is_upload_fail = True
                else:
                    new_fail_data = {}
            except Exception as e:
                err_msg_dip = f"本地网络异常，上传失败，error：{e}"
                is_upload_fail = True
            finally:
                if is_upload_fail:
                    cur_time_str = cur_time.strftime(xcons.FMT_TIME_FILE1)
                    new_all_fail_data['SMT'].update({cur_time_str: new_fail_data})

        # 先把旧缓存文件移除，如果重传后还有失败的，重新保存
        os.remove(FAIL_MES_DATA_FILE)
        if err_msg_dip or err_msg_smt:
            self._save_fail_data_to_file('RE_UPLOAD', new_all_fail_data)
            # 把所有错误信息组装起来发送
            err_str = ''
            if err_msg_dip:
                err_str = err_msg_dip
            if err_msg_smt:
                err_str += "\n" + err_msg_smt
            return self.x_response("false", f"{err_str}")
        else:
            return self.x_response()

    def _save_fail_data_to_file(self, save_type: str, fail_data: dict):
        """
        保存失败数据到文件
        缓存数据结构：{
            "DIP":{
                "save_time1":{"NG":{data},"PASS":{data}},
                "save_time2":{"NG":{data},"PASS":{data}},
            },
            "SMT":{
                "save_time1":{data1},
                "save_time2":{data2}
            }
        }
        """
        # 先创建失败缓存目录
        xutil.FileUtil.ensure_dir_exist(FAIL_CACHE_DIR)

        if save_type == 'RE_UPLOAD':
            # 上传失败的数据已经是完整的，直接保存即可
            data_str = json.dumps(fail_data, indent=4, ensure_ascii=False)
            xutil.FileUtil.write_content_to_file_atomic(FAIL_MES_DATA_FILE, data_str)
            return

        cached_fail_data = {'DIP': {}, 'SMT': {}}
        if os.path.exists(FAIL_MES_DATA_FILE):
            # 读取原有fail_data，并把新的数据追加到原有的记录里重新保存
            cached_fail_data = xutil.FileUtil.load_json_file(FAIL_MES_DATA_FILE)

        save_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE1)
        if save_type == 'DIP':
            cached_fail_data['DIP'].update({save_time: fail_data})
        elif save_type == 'SMT':
            cached_fail_data['SMT'].update({save_time: fail_data})
        else:
            return

        data_str = json.dumps(cached_fail_data, indent=4, ensure_ascii=False)
        xutil.FileUtil.write_content_to_file_atomic(FAIL_MES_DATA_FILE, data_str)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_url = data_vo.get_value_by_cons_key("api_url_upload")
        wp_id = data_vo.get_value_by_cons_key("wp_id")
        dev_id = data_vo.get_value_by_cons_key("dev_id")
        mo_id = data_vo.get_value_by_cons_key("mo_id")
        save_path = data_vo.get_value_by_cons_key("save_path")
        data_type = data_vo.get_value_by_cons_key("data_type")

        review_path = data_vo.pcb_entity.review_path

        log.info(data_vo.pcb_entity)

        if not save_path:
            return self.x_response("false", f"请先选择NG器件图路径！")

        start_date = data_vo.pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        ret_res = self.x_response()

        is_upload_fail_data = True
        for board_entity in data_vo.pcb_entity.yield_board_entity():
            log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            full_path = f"{save_path}/{barcode}_{start_date[:8]}"
            xutil.FileUtil.ensure_dir_exist(full_path)

            comp_ng_list = []
            comp_pass_list = []

            # 上传失败时记录数据
            cache_send_data = {}
            is_send_fail = False
            cache_type = ''

            try:
                for comp_entity in board_entity.yield_comp_entity():
                    # repair_code = comp_entity.repair_ng_code
                    robot_ng_code = comp_entity.robot_ng_code
                    comp_tag = comp_entity.designator

                    if robot_ng_code != "0":
                        error_code = AIS_40X_ERROR_MAP.get(robot_ng_code, {}).get("custom_code")
                        comp_ng_list.append({
                            "ERR_CODE": error_code,
                            "POINT": comp_tag,
                            "IS_MISJUDGE": "Y" if comp_entity.get_final_result() == "REPASS" else "N"
                        })

                        comp_pass_list.append({
                            "ERR_CODE": error_code,
                            "POINT": comp_entity.designator,
                            "IS_MISJUDGE": "Y"  # 是误报
                        })

                        # if comp_entity.is_repair_ng():
                        # 保存NG图片
                        comp_src_img = comp_entity.image_path
                        comp_dst_img = f"{full_path}/{barcode}_{comp_tag}_{xutil.OtherUtil.get_origin_uuid4_str()}"

                        if comp_src_img:
                            xutil.FileUtil.copy_file(comp_src_img, comp_dst_img, is_auto_add_suffix=True)

                if barcode:
                    data_param = {
                        "WP_ID": wp_id,
                        "MO": mo_id,
                        "DEV_ID": dev_id,
                        "LB_ID": barcode,
                        "TEST_RESULT": "OK" if board_entity.repair_result else "NG",
                        "FILE_NAME": ",".join([f"{i}/mes/{board_no}/man.xml" for i in review_path]),
                        "BAD_ITEM": comp_ng_list
                    }

                    self.log.info(f"发送数据类型：{data_type}")

                    if data_type == "DIP":
                        if board_entity.is_repair_ng():
                            # 检测ng，人工复判ng

                            # 数据提前生成，用于发生本地网络异常发生Exception时确保已经缓存数据
                            pass_param = {
                                "WP_ID": wp_id,
                                "MO": mo_id,
                                "DEV_ID": dev_id,
                                "LB_ID": barcode,
                                "TEST_RESULT": "OK",
                                "FILE_NAME": ",".join([f"{i}/mes/{board_no}/man.xml" for i in review_path]),
                                "BAD_ITEM": comp_pass_list
                            }
                            cache_send_data['NG'] = data_param
                            cache_send_data['PASS'] = pass_param

                            # 需要先发送ng的结果数据，再发送pass的结构数据
                            self.log.info(f"检测ng，人工复判ng，需要先发送ng的结果数据，再发送pass的结构数据")
                            self.log.info(f"1. 发送ng的数据...")
                            ret = xrequest.RequestUtil.post_json(data_url, data_param)
                            if ret.get("ERR_CODE") != "OK":
                                # return x_response("false", f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}")
                                error_msg = f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}"
                                ret_res = self.x_response("false", error_msg)
                                is_send_fail = True
                            else:
                                cache_send_data.pop('NG')

                            self.log.info(f"2. 发送pass的数据...")
                            ret = xrequest.RequestUtil.post_json(data_url, pass_param)
                            if ret.get("ERR_CODE") != "OK":
                                # return x_response("false", f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}")
                                error_msg = f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}"
                                ret_res = self.x_response("false", error_msg)
                                is_send_fail = True
                            else:
                                cache_send_data.pop('PASS')

                        else:
                            cache_send_data['PASS'] = data_param
                            # 最终结果是pass，只发送一次pass的数据
                            ret = xrequest.RequestUtil.post_json(data_url, data_param)
                            if ret.get("ERR_CODE") != "OK":
                                # return x_response("false", f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}")
                                error_msg = f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}"
                                is_send_fail = True
                            else:
                                cache_send_data.pop('PASS')

                    else:
                        cache_send_data = data_param
                        ret = xrequest.RequestUtil.post_json(data_url, data_param)
                        if ret.get("ERR_CODE") != "OK":
                            # return x_response("false", f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}")
                            error_msg = f"mes接口响应异常，上传数据失败，error：{ret.get('ERR_MSG')}"
                            ret_res = self.x_response("false", error_msg)
                            is_send_fail = True
                        else:
                            cache_send_data = {}
                else:
                    self.log.warning(f"没有条码，数据不上传到Mes！")
            except Exception as err_:
                # self.log.warning(f"")
                log.warning(traceback.format_exc())
                err_msg = f"其他异常，上传失败，error：{err_}"
                ret_res = self.x_response("false", err_msg)
                log.warning(f"不能影响下一块板卡发送！")
                is_send_fail = True
            finally:
                if is_send_fail:
                    self._save_fail_data_to_file(data_type, cache_send_data)
                    is_upload_fail_data = False

        if is_upload_fail_data:
            self._upload_fail_data()

        return ret_res

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        sn_list = other_vo.list_sn()
        check_url = other_vo.get_value_by_cons_key("api_url_check")
        wp_id = other_vo.get_value_by_cons_key("wp_id")
        dev_id = other_vo.get_value_by_cons_key("dev_id")
        mo_id = other_vo.get_value_by_cons_key("mo_id")

        barcode_list = other_vo.json_data.get('barcodeList', {})

        if barcode_list:
            for k, v in barcode_list.items():
                if k not in ['-2', '-1'] and (not v):
                    return x_response("false", f"拼板{k}未扫到条码！")

        if not sn_list or "" in sn_list:
            return x_response("false", f"设备未扫到条码！")

        for sn in sn_list:
            check_param = {
                "WP_ID": wp_id,
                "MO": mo_id,
                "DEV_ID": dev_id,
                "LB_ID": sn
            }
            # check_url = f"{api_url}/IMS/GET_CHECK_LB"
            ret = xrequest.RequestUtil.post_json(check_url, check_param)
            if ret.get("ERR_CODE") != "OK":
                return x_response("false", f"mes接口响应异常，条码校验失败，error：{ret.get('ERR_MSG')}")

        return x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_status = other_vo.get_device_status_str()
        log.info(f"device status: {device_status}")

        device_code = xcons.DEVICE_STATUS.get(device_status)
        if "safedoor" in device_status:
            device_code = "10"
            device_status = "安全门"

        if "安全门" in device_status:
            device_code = "10"
            device_status = "安全门"

        if device_code in ["01", "02", "03", "04"]:
            state = "1"
            msg = ""
        elif device_code in ["10", "12", "99"]:
            state = "2"
            msg = device_status
        else:
            log.warning(f"此状态不发送给Mes")
            return x_response()

        api_url = other_vo.get_value_by_cons_key("api_url_device_status")
        dev_id = other_vo.get_value_by_cons_key("dev_id")

        res = post_device_status_api(api_url, dev_id, state, msg)
        if res:
            return res
        return x_response()

    def send_idle_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url_device_status")
        dev_id = other_vo.get_value_by_cons_key("dev_id")

        res = post_device_status_api(api_url, dev_id, "3")
        if res:
            return res

        return x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        resp = self.x_response()
        if btn_key == "modify_mo":
            mo_form = getattr(other_param, "form_mo_id")
            mo_form.setEnabled(True)
        elif btn_key == "upload_fail_data":
            resp = self._upload_fail_data()

        return resp

    def init_main_window(self, main_window, other_vo: OtherVo):
        mo_form = getattr(main_window, "form_mo_id")
        mo_form.setEnabled(False)

        # 后续所有配置参数获取都统一从这获取
        self.other_vo = other_vo

    def save_btn_on_window(self, main_window):
        mo_form = getattr(main_window, "form_mo_id")
        mo_form.setEnabled(False)

        return x_response()
