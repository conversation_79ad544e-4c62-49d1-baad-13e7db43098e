# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t1.py
# Time       ：2024/12/2 下午3:40
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import boto3
from botocore.client import Config

endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
aws_access_key_id_ = "enp6eA=="
aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"

# 创建一个会话（如果你已经在环境变量中设置了凭证，这一步可以省略）
session = boto3.Session(
    aws_access_key_id=aws_access_key_id_,  # 替换为你的访问密钥
    aws_secret_access_key=aws_secret_access_key_  # 替换为你的秘密密钥
)

# 创建 S3 客户端，并指定自定义终端节点和其他配置选项
s3_client = session.client(
    's3',
    endpoint_url=endpoint_url_,  # 确保URL包含协议部分 (http:// 或 https://)
    config=Config(
        s3={'addressing_style': 'path'},  # 使用路径样式 URL
        signature_version='s3v4'  # 如果适用，可以指定签名版本
    ),
    region_name=''  # 如果适用，可以指定区域名称
)

ret = s3_client.get_object_acl(Bucket="my-new-bucket", Key="COMP1039_1039_2.png")
print("ret", ret)
