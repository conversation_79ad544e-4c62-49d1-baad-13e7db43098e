# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/21 上午10:20
# Author     ：sch
# version    ：python 3.8
# Description：北京小米     ATAOI_2023-1450
"""
import base64
import hashlib
import json
import os
import shutil
from typing import Any

import requests
from PIL import Image

from common import xutil, xcons, xrequest
from common.xutil import log
from engine.MesEngine import ErrorMapEngine
from services.custom.beijingxiaomi.xmodule import PersistentLimitedDict, clean_cache_folder, clean_old_folders_by_date
from vo.mes_vo import DataVo, OtherVo, ButtonVo


def generate_sign(app_id, app_key, json_body):
    """生成签名"""

    sign_str = f"{app_id}{json_body}{app_key}"

    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    return sign


cache_path = f"{os.getcwd()}/cache_data"
xutil.FileUtil.ensure_dir_exist(cache_path)


# def compress_to_memory(input_path: str, quality: int = 85) -> Optional[io.BufferedReader]:
#     """压缩图片到内存（返回一个文件操作符）
#
#     Args:
#         input_path: 输入图片路径
#         quality: 压缩质量(1-100)
#
#     Returns:
#         Optional[bytes]: 压缩后的二进制数据，失败返回None
#     """
#     try:
#         with Image.open(input_path) as img:
#             buffer = io.BytesIO()
#             img.save(buffer, format="JPEG", quality=quality, optimize=True)
#             file_size = buffer.tell()
#
#             buffer.seek(0)  # 关键：重置指针
#
#             if file_size >= 1073741824:  # 1 GB
#                 log.info(f"File size: {file_size / 1073741824:.2f} GB")
#             elif file_size >= 1048576:  # 1 MB
#                 log.info(f"File size: {file_size / 1048576:.2f} MB")
#             elif file_size >= 1024:  # 1 KB
#                 log.info(f"File size: {file_size / 1024:.2f} KB")
#             else:
#                 log.info(f"File size: {file_size} bytes")
#
#             return io.BufferedReader(buffer)
#     except Exception as e:
#         log.warning(f"压缩失败: {e}")
#         return None


def resize_compress(
        input_path: str,
        output_path: str,
        scale: float = 0.5,
        resample=Image.LANCZOS
):
    """按比例缩小图片尺寸，支持指定重采样方法"""
    if scale == 1.0:
        xutil.FileUtil.copy_file(input_path, output_path)
        print("图片未缩放")
        return

    img = Image.open(input_path)
    width, height = img.size
    img = img.resize(
        (int(width * scale), int(height * scale)),
        resample=resample  # 添加重采样参数
    )
    img.save(output_path)


def compress_folder_to_sibling_dir(folder_path) -> str:
    """
    使用 shutil 将指定文件夹压缩到同级目录下，生成同名的 .zip 文件

    参数:
        folder_path (str): 要压缩的文件夹的完整路径（例如：'/home/<USER>/Dev/mesconfig_python/cache_data/20250326181415'）
    """
    # 确保文件夹存在
    if not os.path.isdir(folder_path):
        raise NotADirectoryError(f"文件夹不存在: {folder_path}")

    # 获取文件夹所在目录和文件夹名（不带路径）
    parent_dir = os.path.dirname(folder_path)
    folder_name = os.path.basename(folder_path)

    # 创建zip文件的路径（同级目录下，同名.zip）
    zip_path = os.path.join(parent_dir, f"{folder_name}.zip")

    # 使用 shutil 创建压缩文件
    shutil.make_archive(
        base_name=os.path.join(parent_dir, folder_name),  # 不带扩展名的基本路径
        format='zip',  # 压缩格式
        root_dir=parent_dir,  # 要压缩内容的根目录
        base_dir=folder_name  # 相对于root_dir的要压缩的目录
    )

    print(f"文件夹已压缩到: {zip_path}")

    shutil.rmtree(folder_path)
    return zip_path


def sync_upload_file(
        upload_src_file: str,
        dst_filename: str,
        bucket_name: str,
        limit_dict_file,
        app_id: str,
        app_key: str,
        api_url_file: str,
):
    """
    同步上传图片
    """
    file_base_name = os.path.basename(upload_src_file)
    xutil.FileUtil.log_file_size(upload_src_file)

    # 上传成功之后删除文件
    file_uuid = xutil.OtherUtil.get_origin_uuid4_str().upper()
    file_param = {
        "bucket": bucket_name,
        "name": dst_filename,
        "uuid": file_uuid,
    }

    limit_dict_file.add_item(file_base_name, file_param)

    ret_json = xiaomi_request(
        app_id,
        app_key,
        api_url_file,
        file_param,
        "Upload",
        filepath=upload_src_file,
    )

    ret = ret_json.get("header", {})
    if str(ret.get("code")) != "200":
        err_msg = ret.get("desc")
        if not err_msg:
            err_msg = ret.get("msg")

        return f"上传文件失败，error：{err_msg}"
    else:
        # 上传成功之后删除文件
        os.remove(upload_src_file)
        log.info(f"上传文件成功！")

        return {
            "fileName": dst_filename,
            "fileId": file_uuid,
            "bucket": bucket_name
        }


@xutil.time_cost
def xiaomi_request(
        app_id: str,
        app_key: str,
        url: str,
        json_body: dict,
        method: str,
        filepath=None,
) -> dict:
    """
    发送小米请求
    """
    log.info(f"appId: {app_id}")
    log.info(f"appKey: {app_key}")
    json_body_str = json.dumps(json_body, indent=2, ensure_ascii=False)
    log.info(f"-----请求URL：{url}   请求参数:\n{json_body_str}")

    sign_str = f"{app_id}{json_body_str}{app_key}"

    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

    data = {
        "header": {
            "appid": app_id,
            "sign": sign,
            "method": method
        },
        "body": json_body_str
    }

    # 将数据转换为字符串并进行 Base64 编码
    data_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    data_base64 = base64.b64encode(data_str.encode('utf-8')).decode('utf-8')

    # 构建表单数据
    form_data = {
        "data": data_base64,
    }

    log.info(form_data)

    if filepath:
        file = open(filepath, 'rb')

        files = {
            "file": file
        }
    else:
        files = None

    # 发送 POST 请求
    response = requests.post(url, data=form_data, files=files, timeout=5)

    # 打印响应
    log.info(f"------接口返回参数：\n{response.text}")

    return response.json()


class Engine(ErrorMapEngine):
    version = {
        "title": "beijingxiaomi release v1.0.0.16",
        "device": "AIS431",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-03-22 11:57  条码校验，上传数据
date: 2025-03-24 17:02  修改接口参数
date: 2025-03-24 17:26  加上接口超时时间
date: 2025-03-25 14:26  优化请求模块
date: 2025-03-26 13:18  增加配置项：是否上传图片+压缩图片质量
date: 2025-03-26 17:36  改成按拼板上传，以及只校验拼板条码
date: 2025-03-27 13:12  增加压缩上传图片功能
date: 2025-03-27 18:04  需求变更
date: 2025-04-22 15:44  ATAOI_2023-1623->增加算法数据上传：3D高度及颜色宽度算法
date: 2025-04-24 17:13  ATAOI_2023-1450->增加保存整板图在本地+从线控获取条码
date: 2025-05-15 10:46  兼容431主软件可能输出多个RGB图像，修改特征判断
date: 2025-05-17 10:17  bugfix: 兼容431主软件可能输出多个RGB图像，修改特征判断
date: 2025-06-11 14:30  ATAOI_2023-1450：1. 假设陪板未过前道工序，正常板也可以过站  2. state参数增加REPASS来识别误报
date: 2025-07-02 11:34  ATAOI_2023-1450: 只要是未过前道工序的报警，都当做是正常的条码，不拦截
""", }

    form = {
        "machine_id": {
            "ui_name": "装备id",
            "value": "",
        },
        "station_id": {
            "ui_name": "工位id",
            "value": "",
        },
        "client_mac": {
            "ui_name": "客户端本机MAC地址",
            "value": "XX-XX-XX-XX-XX-XX",
        },
        "user_id": {
            "ui_name": "用户id",
            "value": "",
        },
        "factory_id": {
            "ui_name": "工厂id",
            "value": "1",
        },
    }

    other_form = {
        "api_url_in": {
            "ui_name": "过途程接口IN",
            "value": "http://127.0.0.1:8081/UnitConfirmDataSetIn",
        },
        "in_app_id": {
            "ui_name": "AppID(过途程接口IN)",
            "value": "CMBE",
        },
        "in_app_key": {
            "ui_name": "AppKey(过途程接口IN)",
            "value": "23CE906FA1261286AC1DBA3266B97962",
        },
        "api_url_out": {
            "ui_name": "过途程接口OUT",
            "value": "http://127.0.0.1:8081/UnitConfirmDataSetOut",
        },
        "out_app_id": {
            "ui_name": "AppID(过途程接口OUT)",
            "value": "CMBE",
        },
        "out_app_key": {
            "ui_name": "AppKey(过途程接口OUT)",
            "value": "23CE906FA1261286AC1DBA3266B97962",
        },
        "api_url_file": {
            "ui_name": "文件服务接口(二进制流)",
            "value": "http://127.0.0.1:8081/x5/file/upload/mqtt",
        },
        "bucket_name": {
            "ui_name": "文件所属包(bucket名称)",
            "value": "MES/AOI",
        },
        "app_id": {
            "ui_name": "AppID(文件服务)",
            "value": "Auto-Soft",
        },
        "app_key": {
            "ui_name": "AppKey(文件服务)",
            "value": "5984710e-bb38-4806-b94d-7a9a727e3880",
        },
        "window_ip": {
            "ui_name": "window中转地址(获取条码)",
            "value": "*************",
        },
    }

    other_combo = {
        "is_upload_img": {
            "ui_name": "是否上传图片",
            "item": ["是", "否"],
            "value": "是",
        },
        # "img_quality": {
        #     "ui_name": "图片质量",
        #     "item": [str(i) for i in range(1, 101)],
        #     "value": "100",
        # }
        "resize_scale": {
            "ui_name": "图片压缩比例",
            "item": [
                "0.1",
                "0.2",
                "0.3",
                "0.4",
                "0.5",
                "0.6",
                "0.7",
                "0.8",
                "0.9",
                "1.0",
            ],
            "value": "1.0",
        }
    }

    path = {
        "pcb_img_save_path": {
            "ui_name": "整板图存储路径",
            "value": f"",
        }
    }

    button = {
        "test_connect": {
            "ui_name": "测试window连接",
        }
    }

    def __init__(self):
        clean_cache_folder(cache_path)

    def init_main_window(self, main_window, other_vo: OtherVo):
        pcb_img_save_path = other_vo.get_value_by_cons_key("pcb_img_save_path")
        clean_old_folders_by_date(pcb_img_save_path)

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        window_ip = other_vo.get_value_by_cons_key("window_ip")

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "plate_sn": pcb_sn
        }

        get_sn_url = f"http://{window_ip}:9050/sn"  # noqa
        ret = xrequest.RequestUtil.post_json(get_sn_url, param)
        if str(ret.get("code")) != "0":
            return self.x_response("false", f"mes接口异常，获取SN失败，error：{ret.get('message')}")

        ret_data = ret.get("data", {})
        board_sn = ret_data.get("board_sn4", "")
        if not board_sn:
            return self.x_response("false", f"未获取到子板条码！")

        board_sn = board_sn.replace(".", ",")

        return self.x_response("true", board_sn)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        定制业务逻辑
        1. 陪板未过前道工序时，另外一块正常的拼板可以继续过站   接口desc参数会报错 ---> 8D506V000064 应该投入 AEFCT 工艺
        2. 必须要有条码校验正常的拼板，才可以继续过站
        3. 假设所有的拼板都未过前道工序，那么都不可以过站
        4. 其他条码校验失败，还是需要弹窗报警

        ## 20250702 需求变更
        1. 只要是未过前道工序的报警，都当做是正常的条码，不拦截
        """
        api_url_in = other_vo.get_value_by_cons_key("api_url_in")
        machine_id = other_vo.get_value_by_cons_key("machine_id")
        station_id = other_vo.get_value_by_cons_key("station_id")
        client_mac = other_vo.get_value_by_cons_key("client_mac")
        user_id = other_vo.get_value_by_cons_key("user_id")
        factory_id = other_vo.get_value_by_cons_key("factory_id")
        in_app_id = other_vo.get_value_by_cons_key("in_app_id")
        in_app_key = other_vo.get_value_by_cons_key("in_app_key")

        try:
            factory_id = int(factory_id)
        except ValueError:
            self.log.warning(f"工厂ID无法转换为整数，factory_id={factory_id}")

        # sn_list = other_vo.list_sn()
        barcode_map = other_vo.get_barcode_map()

        if "-2" in barcode_map:
            del barcode_map["-2"]

        if "-1" in barcode_map:
            del barcode_map["-1"]

        sn_list = list(barcode_map.values())  # 拼板条码列表

        log.info(f"校验的条码列表：{sn_list}")

        # is_has_ok_flag = False
        # is_has_err_type1 = False  # 未过前道工序的报警
        # is_has_err_type_other = False  # 其他类型的报警

        ret_msg_list = []
        for sn in sn_list:
            param = {
                "machineId": machine_id,
                "stationId": station_id,
                "clientMac": client_mac,
                "clientTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                "unitSn": sn,
                "userId": user_id,
                "factoryId": factory_id,
            }

            ret_json = xiaomi_request(
                in_app_id,
                in_app_key,
                api_url_in,
                param,
                "UnitConfirmDataSetIn"
            )
            ret = ret_json.get("header", {})
            ret_code = str(ret.get("code"))
            ret_desc = ret.get('desc')

            if str(ret_code) != "200" and "应该投入" not in ret_desc:
                ret_msg_list.append(f"mes接口异常，SN:{sn} error：{ret_desc}")

            # if str(ret.get("code")) != "200":
            #     ret_desc = ret.get('desc')
            #     ret_msg_list.append(f"mes接口异常，error：{ret_desc}")
            #
            #     if "应该投入" in ret_desc:
            #         is_has_err_type1 = True
            #     else:
            #         is_has_err_type_other = True
            #
            # else:
            #     is_has_ok_flag = True

        # 如果是陪板报，未过前道工序，可以让正常板继续过站
        # if is_has_ok_flag and is_has_err_type1 and not is_has_err_type_other:
        #     return self.x_response("true", "陪板未过前道工序，正常板可以继续过站！")

        if ret_msg_list:
            return self.x_response("false", "\n".join(ret_msg_list)[:500])

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_out = data_vo.get_value_by_cons_key("api_url_out")
        api_url_file = data_vo.get_value_by_cons_key("api_url_file")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        client_mac = data_vo.get_value_by_cons_key("client_mac")
        user_id = data_vo.get_value_by_cons_key("user_id")
        factory_id = data_vo.get_value_by_cons_key("factory_id")

        bucket_name = data_vo.get_value_by_cons_key("bucket_name")
        app_id = data_vo.get_value_by_cons_key("app_id")
        app_key = data_vo.get_value_by_cons_key("app_key")

        out_app_id = data_vo.get_value_by_cons_key("out_app_id")
        out_app_key = data_vo.get_value_by_cons_key("out_app_key")
        pcb_img_save_path = data_vo.get_value_by_cons_key("pcb_img_save_path", not_null=True)

        is_upload_img = data_vo.get_value_by_cons_key("is_upload_img") == "是"
        resize_scale = data_vo.get_value_by_cons_key("resize_scale")
        log.info(f"{is_upload_img=}, {resize_scale=}")

        try:
            factory_id = int(factory_id)
        except ValueError:
            self.log.warning(f"工厂ID无法转换为整数，factory_id={factory_id}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        cycle_time = pcb_entity.get_cycle_time()
        pcb_sn = pcb_entity.pcb_barcode

        comp_data_list_point = []  # 检测点位

        ret_msg_list = []

        board_barcode_list = []
        for board_entity in pcb_entity.yield_board_entity():
            board_barcode_list.append(board_entity.barcode)

        if not all(board_barcode_list):
            return self.x_response("false", f"部分拼板没有扫到条码，本次不上传MES！")

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        pcb_431_src_img = pcb_entity.get_image_path_431()

        limit_dict_file = PersistentLimitedDict(200, storage_path=f"{cache_path}/limit_dict_file.json")

        board_sn_list = []
        for board_entity in pcb_entity.yield_board_entity():
            board_sn_list.append(board_entity.barcode)

        board_sn_str = "_".join(board_sn_list)
        log.info(f"board sn str: {board_sn_str}")

        pcb_src_file_param = {}

        if is_upload_img:
            if pcb_431_src_img:
                log.info(f"上传的整板图源路径为：{pcb_431_src_img}")

                # 压缩后的文件路径
                resize_img_dst_path = f"{cache_path}/{test_time}.jpg"
                resize_compress(
                    pcb_431_src_img,
                    resize_img_dst_path,
                    scale=float(resize_scale)
                )

                full_pcb_img_save_path = f"{pcb_img_save_path}/{test_time[:8]}"
                xutil.FileUtil.ensure_dir_exist(full_pcb_img_save_path)
                pcb_dst_img_path = f"{full_pcb_img_save_path}/{board_sn_str}.jpg"

                # 再把图片在本地留存一份
                xutil.FileUtil.copy_file(
                    resize_img_dst_path,
                    pcb_dst_img_path
                )

                # 直接上传resize之后的图片
                ret = sync_upload_file(
                    resize_img_dst_path,
                    f"{board_sn_str}.jpg",
                    bucket_name,
                    limit_dict_file,
                    app_id,
                    app_key,
                    api_url_file,
                )

                if type(ret) is dict:
                    pcb_src_file_param = ret

                else:
                    ret_msg_list.append(ret)

        alg_data_map = pcb_entity.get_height_measure_and_color_width_data_431()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list_test_item = []  # 检测项
            full_path = f"{cache_path}/{test_time}_{board_no}"

            file_count = 0
            for comp_entity in board_entity.yield_comp_entity():
                comp_uuid = comp_entity.comp_id
                alg_data_list = alg_data_map.get(comp_uuid, [])
                comp_tag = comp_entity.designator
                comp_result = comp_entity.get_final_result("PASS", "PASS", "FAIL")

                for alg_item in alg_data_list:
                    alg_n = alg_item.get("alg_name")
                    alg_val = alg_item.get("alg_value")
                    alg_info_str = alg_val.get("displayAlgInfo", '{}')
                    alg_info = json.loads(alg_info_str)

                    if alg_n == "colorwidth":
                        comp_data_list_test_item.append({
                            "testItem": f"{barcode}_{comp_tag}",
                            "functionName": alg_n,
                            "value": alg_info.get("averageWidth", "0"),  # 颜色算法的平均宽度
                            "hiLimit": alg_info.get("maxWidth", "0"),  # 颜色算法的最大宽度
                            "lowLimit": alg_info.get("minWidth", "0"),  # 颜色算法的最小宽度
                            "testTime": cycle_time,
                            "errorCode": comp_entity.repair_ng_code,
                            "status": comp_result,
                            "description": ""
                        })

                    if alg_n == "heightMeasure":
                        comp_data_list_test_item.append({
                            "testItem": f"{barcode}_{comp_tag}",
                            "functionName": alg_n,
                            "value": alg_info.get("height", "0"),  # 颜色算法的平均宽度
                            "hiLimit": alg_info.get("maxHeightThreshold", "0"),  # 颜色算法的最大宽度
                            "lowLimit": alg_info.get("minHeightThreshold", "0"),  # 颜色算法的最小宽度
                            "testTime": cycle_time,
                            "errorCode": comp_entity.repair_ng_code,
                            "status": comp_result,
                            "description": ""
                        })

                if comp_entity.is_repair_ng():

                    ng_item_list = []
                    for alg_entity in comp_entity.yield_alg_entity():
                        if alg_entity.result != "0":
                            alg_name = alg_entity.test_name

                            if alg_name not in ng_item_list:
                                ng_item_list.append(alg_name)

                    comp_data_list_point.append({
                        "no": ",".join(ng_item_list),
                        "result": comp_result,
                        "defectCode": comp_entity.repair_ng_code,
                        "defectDesc": comp_entity.repair_ng_str,
                        "location": comp_tag,
                        "panelNo": board_no
                    })

                    # 拷贝图片
                    xutil.FileUtil.ensure_dir_exist(full_path)
                    comp_src_img = comp_entity.image_path
                    repair_ng_str = comp_entity.repair_ng_str
                    dst_filename = f"{pcb_sn}_{barcode}_{comp_tag}_{repair_ng_str}.png"
                    fst_filepath = f"{full_path}/{dst_filename}"

                    if comp_src_img:
                        resize_compress(comp_src_img, fst_filepath, scale=float(resize_scale))
                        file_count += 1

            zip_file_param = {}
            if file_count > 0 and is_upload_img:
                log.info(f"压缩文件：{file_count}")
                zip_file = compress_folder_to_sibling_dir(full_path)

                ret2 = sync_upload_file(
                    zip_file,
                    f"{board_sn_str}.zip",
                    bucket_name,
                    limit_dict_file,
                    app_id,
                    app_key,
                    api_url_file,
                )

                if type(ret2) is dict:
                    zip_file_param = ret2
                else:
                    ret_msg_list.append(ret2)

            board_data_list = [{
                "childUnitSn": barcode,
                "childUnitState": board_entity.get_repair_result("PASS", "FAIL"),
                "toolVersion": "v3.0.0.15",
                "dataItem": comp_data_list_test_item
            }]

            file_data_list = []
            if pcb_src_file_param:
                file_data_list.append(pcb_src_file_param)

            if zip_file_param:
                file_data_list.append(zip_file_param)

            data_param = {
                "machineId": machine_id,
                "stationId": station_id,
                "clientMac": client_mac,
                "clientTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                "unitSn": barcode,
                "uuidInspection": xutil.OtherUtil.get_origin_uuid4_str().upper(),
                "state": pcb_entity.get_final_result("PASS", "REPASS", "FAIL"),
                "userId": user_id,
                "factoryId": factory_id,
                "unitData": {
                    "inspectionItemData": board_data_list,
                    "inspectionPointData": comp_data_list_point,
                    "fileData": file_data_list
                }
            }

            ret_json = xiaomi_request(
                out_app_id,
                out_app_key,
                api_url_out,
                data_param,
                "UnitConfirmDataSetOut"
            )

            ret = ret_json.get("header", {})
            if str(ret.get("code")) != "200":
                ret_msg_list.append(f"mes接口异常，error：{ret.get('desc')}")

        if ret_msg_list:
            err_msg = "\n".join(ret_msg_list)
            return self.x_response("false", f"上传过站数据失败，error：{err_msg}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        if btn_vo.get_btn_key() == "test_connect":
            is_connect = xrequest.SocketUtil.check_window_port(window_ip, 9050)

            if not is_connect:
                return self.x_response("false", f"连接window中转程序失败！请检查网络！")

        return self.x_response()

# if __name__ == '__main__':
#     app_id = "MIUAT"
#     app_key = "6B3D57E2-F062-471F-932B-76C8A79E0D66"
#
#     xiaomi_request(app_id, app_key,
#                    "http://im.pre.mi.com/mes/x5/pass/station/v2",
#                    {
#                        "machineId": "A030007000102",
#                        "stationId": "G05-AFCCBFP-11",
#                        "clientMac": "00155DD01E93",
#                        "clientTime": "2024-08-03 09:12:32.176",
#                        "unitSn": "50303/N3X900237",
#                        "userId": "AKPKTSQAS46NKE4NHY",
#                        "factoryId": 1
#                    },
#                    "UnitConfirmDataSetIn"
#                    )
