# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : draw2.py
# Time       ：2025/3/10 下午3:45
# Author     ：sch
# version    ：python 3.8
# Description：
"""

from PIL import Image, ImageDraw

def draw_rectangles_on_image(image_path, rectangle_coordinates_list, outline_color="red", outline_width=3):
    """
    Opens an image and draws multiple rectangles on it based on a list of coordinates.

    Args:
        image_path (str): Path to the image file.
        rectangle_coordinates_list (list): A list of tuples or lists, where each element contains
                                         (top_left_x, top_left_y, width, height) for a rectangle.
        outline_color (str, tuple): The color of the rectangle's outline. Defaults to "red".
        outline_width (int): The width of the rectangle's outline in pixels. Defaults to 3.

    Returns:
        None. Saves the modified image to the same path, overwriting the original.
    """
    try:
        img = Image.open(image_path)
    except FileNotFoundError:
        print(f"Error: Image not found at {image_path}")
        return
    except Exception as e:
        print(f"Error: Could not open image: {e}")
        return

    draw = ImageDraw.Draw(img)

    for rect_coords in rectangle_coordinates_list:
        top_left_x = rect_coords[0]
        top_left_y = rect_coords[1]
        width = rect_coords[2]
        height = rect_coords[3]

        # Calculate the bottom-right corner coordinates
        bottom_right_x = top_left_x + width
        bottom_right_y = top_left_y + height

        # Draw the rectangle
        draw.rectangle((top_left_x, top_left_y, bottom_right_x, bottom_right_y), outline=outline_color, width=outline_width)

    try:
        # img.save(image_path)  # Overwrite the original image
        img.show()
        print(f"Rectangles drawn and saved to {image_path}")
    except Exception as e:
        print(f"Error: Could not save image: {e}")


# Example Usage
image_path = "/home/<USER>/Downloads/431/20250109/T_20250109092133_1/images/thumbnail/1084485632_Bgr_RGB.jpg" # Replace with the actual path to your image

# Example list of rectangle coordinates: [(x1, y1, width1, height1), (x2, y2, width2, height2), ...]
rectangle_coordinates_list = [
    (1549, 1287, 301, 322),  # Rectangle 1
    (100, 100, 50, 50),    # Rectangle 2
    (200, 200, 100, 75)   # Rectangle 3
]

# outline_color = "red"
outline_color = "yellow"
outline_width = 5

draw_rectangles_on_image(image_path, rectangle_coordinates_list, outline_color, outline_width)



# # Example Usage
#
# image_path = "/home/<USER>/Downloads/431/20250109/T_20250109092133_1/images/thumbnail/1084485632_Bgr_RGB.jpg"
# rectangle_coordinates = (1549, 1287, 301, 322)  # (x1, y1, x2, y2)
#
# top_left_x = rectangle_coordinates[0]
# top_left_y = rectangle_coordinates[1]
# width = rectangle_coordinates[2]
# height = rectangle_coordinates[3]
# outline_color = "blue"
# outline_width = 5
#
# draw_rectangle_on_image(image_path, top_left_x, top_left_y, width, height, outline_color, outline_width)
