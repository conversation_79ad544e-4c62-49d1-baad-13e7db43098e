# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/27 上午11:09
# Author     ：sch
# version    ：python 3.8
# Description：太仓众华
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

csv_template = """线别,机器类型,程序版本,生产订单,工单号,作业员,机种名,板号,开始时间,结束时间,工具条码,条码,报错数量,板子机测结果,人员判定板子结果,,不良位置,元件类型,机测元件结果,人员判定元件结果,不良代码
Line,MachineType,SoftwareVersion,ProductionOrder,WorkOrder,Operator,ProductName,BoardID,StartTime,EndTime,ToolBarcode,Barcode,ErrorCount,BoardFirstResult,BoardRePassResult,SkipBoard,BitName,PartName,ElementFirstResult,ElementRePassResult,ErrorCode{comp_data_str}
"""

comp_row_template = """
{Line},{MachineType},{SoftwareVersion},{ProductionOrder},{WorkOrder},{Operator},{ProductName},{BoardID},{StartTime},{EndTime},{ToolBarcode},{Barcode},{ErrorCount},{BoardFirstResult},{BoardRePassResult},{SkipBoard},{BitName},{PartName},{ElementFirstResult},{ElementRePassResult},{ErrorCode}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "taicangzhonghua release v1.0.0.2",
        "device": "AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-27 15:25  生成csv文档
date: 2024-10-16 16:17  修改文件名规则
""", }

    form = {
        "line": {
            "ui_name": "线别",
            "value": "",
        },
        "machine_type": {
            "ui_name": "机器类型",
            "value": "",
        },
        "software_version": {
            "ui_name": "程序版本",
            "value": "",
        },
        "production_order": {
            "ui_name": "生产订单",
            "value": "",
        },
        "work_order": {
            "ui_name": "工单号",
            "value": "",
        },
        "operator": {
            "ui_name": "作业员",
            "value": "",
        },
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)

        line = data_vo.get_value_by_cons_key("line")
        machine_type = data_vo.get_value_by_cons_key("machine_type")
        software_version = data_vo.get_value_by_cons_key("software_version")
        production_order = data_vo.get_value_by_cons_key("production_order")
        work_order = data_vo.get_value_by_cons_key("work_order")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        project_name = pcb_entity.project_name

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_data_str += comp_row_template.format(**{
                    "Line": line,
                    "MachineType": machine_type,
                    "SoftwareVersion": software_version,
                    "ProductionOrder": production_order,
                    "WorkOrder": work_order,
                    "Operator": operator,
                    "ProductName": project_name,
                    "BoardID": board_no,
                    "StartTime": start_time,
                    "EndTime": end_time,
                    "ToolBarcode": pcb_sn,
                    "Barcode": barcode,
                    "ErrorCount": "",
                    "BoardFirstResult": comp_entity.get_final_result("OK", "NG", "NG"),
                    "BoardRePassResult": comp_entity.get_final_result("OK", "OK", "NG"),
                    "SkipBoard": "",
                    "BitName": comp_entity.designator,
                    "PartName": comp_entity.part,
                    "ElementFirstResult": comp_entity.robot_ng_str,
                    "ElementRePassResult": comp_entity.repair_ng_str,
                    "ErrorCode": comp_entity.repair_ng_code,
                })

            csv_content = csv_template.format(comp_data_str=comp_data_str)
            board_result = board_entity.get_repair_result("pass", "fail")

            filepath = f"{save_path}/{barcode}_{time_file}_{board_result}_{board_no}.csv"
            xutil.FileUtil.write_content_to_file(filepath, csv_content)

        return self.x_response()
