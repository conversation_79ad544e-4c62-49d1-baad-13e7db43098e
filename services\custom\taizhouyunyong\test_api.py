# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/12/7 下午2:25
# Author     ：sch
# version    ：python 3.8
# Description： 读取图片的二进制文件
"""

if __name__ == '__main__':
    t_src = f"/home/<USER>/Pictures/Trojan.jpg"
    with open(t_src, 'rb') as f:
        b = f.read()
        # print(b)
        print(type(b))
        c = str(b)
        print(c)
        print(type(c))

