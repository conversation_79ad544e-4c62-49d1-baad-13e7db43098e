# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/2/8 上午10:30
# Author     ：sch
# version    ：python 3.8
# Description：西安法士特    供应商/代理商：德智  https://jira.cvte.com/browse/ATAOI_2019-37144
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, ComboVo, ButtonVo, OtherVo
from engine.MesEngine import ErrorMapEngine

get_part_type_list_template = """<acccomm>
    <request>
        <program>{program}</program>
        <product>{product}</product>
        <station>{station}</station>
        <command>PARTTYPELIST</command>
        <params></params>
    </request>
</acccomm>"""

check_part_template = """<acccomm>
    <request>
        <program>{program}</program>
        <product>{product}</product>
        <station>{station}</station>
        <command>PARTTYPEDATA</command>
        <params>
            <param name="PARTTYPEID">{part_id}</param>
        </params>
    </request>
</acccomm>"""

check_sn_template = """<acccomm>
    <request>
        <program>{program}</program>
        <product>{product}</product>
        <station>{station}</station>
        <command>LOAD</command>
        <params>
            <param name="PARTTYPEID">{part_id}</param>
            <param name="SN1">{sn}</param>
        </params>
    </request>
</acccomm>"""

data_template = """<acccomm>
    <request>
        <program>{program}</program>
        <product>{product}</product>
        <station>{station}</station>
        <command>UNLOAD</command>
        <params>
            <param name="PARTTYPEID">{part_id}</param>
            <param name="SN1">{sn}</param>
            <param name="STATUSBITS">{is_success}</param>
            <param name="FAILUREBITS">{is_failed}</param>
            <param name="N_REAL1"></param>
        </params>
    </request>
</acccomm>"""

txt_template = """####
{foo1}
{foo2}
{foo3}
{foo4}
{foo5}
{foo6}
{foo7}
{foo8}
{foo9}
{foo10}{foo11}
{foo12}
"""

row_data_template = """
{foo1}|{foo2}|{foo3}|{foo4}|{foo5}"""

limit_data_map = xutil.LimitedDict(200)


class Engine(ErrorMapEngine):
    version = {
        "title": "xianfashite release v1.0.0.4",
        "device": "AIS303B",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-02-10 16:52  jira->37144: 条码校验，上传数据，获取型号，切换型号
date: 2025-02-12 14:49  修改获取型号的请求参数
date: 2025-02-12 17:16  修改获取型号的请求参数 1
date: 2025-02-16 10:02  bugfix: 无法接收服务端的返回值
""",
    }

    form = {
        "program": {
            "ui_name": "program",
            "value": "PRG_NAME",
        },
        "product": {
            "ui_name": "product",
            "value": "PARTNO",
        },
        "station": {
            "ui_name": "station",
            "value": "30",
        },
    }

    other_form = {
        "api_host": {
            "ui_name": "服务器IP",
            "value": "127.0.0.1",
        },
        "api_port": {
            "ui_name": "服务器端口",
            "value": "8085",
        }
    }

    combo = {
        "part_list": {
            "ui_name": "型号列表",
            "item": [],
            "value": "",
        }
    }

    button = {
        "check_part": {
            "ui_name": "切换型号",
        }
    }

    path = {
        "save_path_txt": {
            "ui_name": "txt保存路径",
            "value": "",
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        api_port = other_vo.get_value_by_cons_key("api_port", to_int=True)

        program = other_vo.get_value_by_cons_key("program")
        product = other_vo.get_value_by_cons_key("product")
        station = other_vo.get_value_by_cons_key("station")
        part_list_select = other_vo.get_value_by_cons_key("part_list")

        part_map = xutil.CacheUtil.get("part_map")
        if part_list_select not in part_map:
            return self.x_response("false", "请选择型号")

        part_id = part_map.get(part_list_select)

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            check_sn_param = check_sn_template.format(
                program=program,
                product=product,
                station=station,
                part_id=part_id,
                sn=sn
            )
            ret_xml = xrequest.SocketUtil.send_data_to_socket_server_v2(
                api_host,
                api_port,
                check_sn_param
            )
            root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
            result = root.find('response').find('result').text
            message = root.find('response').find('message').text
            ret_val = root.find('response').find('message2')[0]

            cycle_id = 0
            for item in ret_val:
                item_val = item.text
                if "CYCLE_ID" in item_val:
                    cycle_id = item_val.split(',')[1]

            self.log.info(f"----> sn:{sn}, cycle_id:{cycle_id} 已缓存！")
            limit_data_map.add_item(f"cycle_id_{sn}", cycle_id)

            if result != "0":
                return self.x_response("false", f"mes接口异常，sn:{sn} 校验失败，{message}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        api_port = data_vo.get_value_by_cons_key("api_port", to_int=True)

        program = data_vo.get_value_by_cons_key("program")
        product = data_vo.get_value_by_cons_key("product")
        station = data_vo.get_value_by_cons_key("station")
        part_list_select = data_vo.get_value_by_cons_key("part_list")
        save_path_txt = data_vo.get_value_by_cons_key(
            "save_path_txt",
            not_null=True
        )

        part_map = xutil.CacheUtil.get("part_map")
        if part_list_select not in part_map:
            return self.x_response("false", "请选择型号")

        part_id = part_map.get(part_list_select)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        error_msg_list = []

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            cycle_id = limit_data_map.get_value(barcode, '')

            comp_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():

                for alg_entity in comp_entity.yield_alg_entity():
                    comp_data_str += row_data_template.format(
                        foo1=f"{comp_entity.designator}_{alg_entity.test_name}",
                        foo2=alg_entity.test_val,
                        foo3=alg_entity.min_threshold,
                        foo4=alg_entity.max_threshold,
                        foo5="P" if alg_entity.result == "0" else "N",
                    )

            # 1. 保存txt文档
            txt_content = txt_template.format(
                foo1=product,
                foo2=part_list_select,
                foo3=f"TC{cycle_id}",
                foo4=f"v3.0.0.1",
                foo5=barcode,
                foo6=pcb_entity.project_name,
                foo7=pcb_entity.get_start_time().strftime(xcons.FMT_DATE1),
                foo8=pcb_entity.get_start_time().strftime(xcons.FMT_TIME),
                foo9=f"ST{station}",
                foo10="TN",
                foo11=comp_data_str,
                foo12=board_entity.get_repair_result("PASS", "NG")
            )

            txt_filepath = f"{save_path_txt}/{time_file}_{barcode}.txt"
            xutil.FileUtil.write_content_to_file_pro(txt_filepath, txt_content)

            # 2. 调用UNLOAD接口上传数据
            is_success = board_entity.get_repair_result(1, 0)
            is_failed = board_entity.get_repair_result(0, 1)

            data_param = data_template.format(
                program=program,
                product=product,
                station=station,
                part_id=part_id,
                sn=barcode,
                is_success=is_success,
                is_failed=is_failed
            )

            ret_xml = xrequest.SocketUtil.send_data_to_socket_server_v2(
                api_host,
                api_port,
                data_param
            )
            root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
            result = root.find('response').find('result').text
            message = root.find('response').find('message').text

            if result != "0":
                error_msg_list.append(
                    f"mes接口异常，sn:{barcode} 上传失败，{message}"
                )

        if error_msg_list:
            return self.x_response(
                "false",
                '\n'.join(error_msg_list)
            )

        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, main_window: Any):
        api_host = combo_vo.get_value_by_cons_key("api_host")
        api_port = combo_vo.get_value_by_cons_key("api_port", to_int=True)
        program = combo_vo.get_value_by_cons_key("program")
        product = combo_vo.get_value_by_cons_key("product")
        station = combo_vo.get_value_by_cons_key("station")

        combo_key = combo_vo.get_combo_key()

        if combo_key == "part_list":

            get_part_list_param = get_part_type_list_template.format(
                program=program,
                product=product,
                station=station,
            )

            ret_xml = xrequest.SocketUtil.send_data_to_socket_server_v2(
                api_host,
                api_port,
                get_part_list_param
            )

            root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
            result = root.find('response').find('result')
            message = root.find('response').find('message')
            part_list = root.find('response').find('message2')

            if result.text != "0":
                return self.x_response(
                    "false",
                    f"mes接口异常，获取型号列表失败：{message.text}"
                )

            ret_part_list = []
            for part in part_list:
                ret_part_list.append(part.text.split(';'))

            part_map = {item[1]: item[0] for item in ret_part_list}

            xutil.CacheUtil.set("part_map", part_map)

            return self.x_response("true", json.dumps({
                "new_items": list(part_map.keys())
            }))

    def custom_button_clicked(self, button_vo: ButtonVo, main_window: Any):
        api_host = button_vo.get_value_by_cons_key("api_host")
        api_port = button_vo.get_value_by_cons_key("api_port", to_int=True)
        program = button_vo.get_value_by_cons_key("program")
        product = button_vo.get_value_by_cons_key("product")
        station = button_vo.get_value_by_cons_key("station")

        btn_key = button_vo.get_btn_key()
        if btn_key == "check_part":
            part_list_select = button_vo.get_value_by_cons_key("part_list")
            part_map = xutil.CacheUtil.get("part_map")
            if part_list_select not in part_map:
                return self.x_response("false", "请选择型号")

            part_id = part_map.get(part_list_select)

            check_part_param = check_part_template.format(
                program=program,
                product=product,
                station=station,
                part_id=part_id
            )

            ret_xml = xrequest.SocketUtil.send_data_to_socket_server_v2(
                api_host,
                api_port,
                check_part_param
            )

            root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
            result = root.find('response').find('result')

            if result.text != "0":
                return self.x_response("false", f"mes接口异常，切换型号失败")

        return self.x_response()
