# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/2/1 下午2:34
# Author     ：sch
# version    ：python 3.8
# Description： 深圳爱科思达
"""
import json
from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.mlsoap.org/soap/envelope/">
    <soap:Body>
        <LeiChenAOILogUpload xmlns="http://tempuri.org/">
            <Parameter>{pcb_data}</Parameter>
        </LeiChenAOILogUpload>
    </soap:Body>
</soap:Envelope>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "aikesida release v1.0.0.4",
        "device": "AIS401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-02-01 14:34  init
date: 2024-02-01 16:55  修改接口请求方式
""", }

    form = {
        "mes_api_url": {
            "ui_name": "Mes接口URL",
            "value": "http://192.168.0.235:6060/MesService/MESService.asmx/LeiChenAOILogUpload"
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        mes_api_url = data_vo.get_value_by_cons_key("mes_api_url")
        device_name = data_vo.get_value_by_cons_key("device_name")

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        pcb_data = {
            "device_name": device_name,
            "pcb_sn": pcb_entity.pcb_barcode,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        # data_str = data_template.format(pcb_data=json.dumps(pcb_data, ensure_ascii=False))

        # ret_str = xrequest.RequestUtil.post_soap(mes_api_url, data_str, soap_action="http://tempuri.org/LeiChenAOILogUpload")
        ret_json = xrequest.RequestUtil.post_form(mes_api_url, body_data={"Parameter": json.dumps(pcb_data, ensure_ascii=False)})

        if str(ret_json.get('Result')) != "0":
            return self.x_response("false", f"mes接口异常，上传数据失败，error: {ret_json.get('Message')}")

        return self.x_response()
