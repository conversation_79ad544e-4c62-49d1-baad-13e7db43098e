<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OtherSettingDialog</class>
 <widget class="QDialog" name="OtherSettingDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>398</width>
    <height>328</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>其他设置</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QCheckBox" name="setting_filter_repeat_send">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>去除重复数据(发送mes)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="setting_merge_send">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>合并上下板面数据</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="setting_send_error_info">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>异常弹窗提示</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="setting_send_info_to_repair">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>发送结果给维修工站</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="setting_http_server_run">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>启动http服务</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame_2">
       <property name="frameShape">
        <enum>QFrame::NoFrame</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <property name="topMargin">
         <number>1</number>
        </property>
        <property name="bottomMargin">
         <number>1</number>
        </property>
        <item>
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>语言(修改后重启生效)</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="setting_lang">
          <item>
           <property name="text">
            <string>ZH</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>EN</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frame">
       <property name="frameShape">
        <enum>QFrame::NoFrame</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>维修工站IP</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="edit_repair_ip"/>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>OtherSettingDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>OtherSettingDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
