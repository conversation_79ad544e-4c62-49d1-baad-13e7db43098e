# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/28 下午2:15
# Author     ：sch
# version    ：python 3.8
# Description：惠为--->格式和森瑞达差不多
"""
from typing import Any

from common import xcons, xutil, xenum
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

row_template = """{foo1},{foo2},{foo3},{foo4},{foo5},{foo6},{foo7},{foo8},{foo9},{foo10},{foo11},{foo12}
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "huiwei release v1.0.0.3",
        "device": "401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-28 14:16  生成本地txt文档  
date: 2024-11-14 10:58  拼板条码未识别到时，使用时间格式来代替，枚举值更改：OK-->pass  
date: 2024-11-14 16:49  修改换行符为window换行符
""", }

    path = {
        "save_path1": {
            "ui_name": "保存路径(1轨)",
            "value": "",
        },
        "save_path2": {
            "ui_name": "保存路径(2轨)",
            "value": "",
        },
    }

    def __init__(self):
        self.common_config["sendmes_setting3"] = xenum.SendMesSetting3.Send

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        project_name = pcb_entity.pcb
        date_file1 = pcb_entity.get_start_time().strftime(xcons.FMT_DATE1)
        time_file1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME)
        time_file2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        board_count = pcb_entity.board_count

        txt_content_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode
                self.log.info(f"主条码为空，已设置为第一拼板条码:{pcb_sn}")

            if not barcode and pcb_sn:
                barcode = pcb_sn
                self.log.info(f"拼板条码为空，已设置为主条码: {barcode}")

            if not barcode:
                barcode = time_file2
                self.log.info(f"拼板条码/主条码为空，已设置为时间格式: {barcode}")

            board_result1 = board_entity.get_final_result("True", "Fault", "Fault", "True")

            if board_entity.robot_result:
                # 机器测试OK
                txt_content_list.append(row_template.format(**{
                    "foo1": project_name,
                    "foo2": f"{barcode}_{board_no}",
                    "foo3": date_file1,
                    "foo4": time_file1,
                    "foo5": board_count,
                    "foo6": "no",
                    "foo7": "no",
                    "foo8": board_no,
                    "foo9": "pass",
                    "foo10": board_result1,
                    "foo11": "pass",
                    "foo12": "pass"
                }))

            elif board_entity.get_final_result() == "BadBoard":
                txt_content_list.append(row_template.format(**{
                    "foo1": project_name,
                    "foo2": f"{barcode}_{board_no}",
                    "foo3": date_file1,
                    "foo4": time_file1,
                    "foo5": board_count,
                    "foo6": "no",
                    "foo7": "no",
                    "foo8": board_no,
                    "foo9": "skip",
                    "foo10": board_result1,
                    "foo11": "skip",
                    "foo12": "skip"
                }))

            else:
                # 需要复判
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_robot_ng():
                        repair_ng_str = comp_entity.repair_ng_str

                        if repair_ng_str == "OK":
                            repair_ng_str = "pass"

                        txt_content_list.append(row_template.format(**{
                            "foo1": project_name,
                            "foo2": f"{barcode}_{board_no}",
                            "foo3": date_file1,
                            "foo4": time_file1,
                            "foo5": board_count,
                            "foo6": comp_entity.designator,
                            "foo7": comp_entity.part,
                            "foo8": board_no,
                            "foo9": comp_entity.robot_ng_str,
                            "foo10": board_result1,
                            "foo11": comp_entity.get_final_result("pass", "pass", "fail"),
                            "foo12": repair_ng_str
                        }))

        if not pcb_sn:
            pcb_sn = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
            self.log.info(f"主条码为空，已更换为时间戳：{pcb_sn}")

        track_index = pcb_entity.track_index
        if track_index == 1:
            save_path = data_vo.get_value_by_cons_key("save_path1")
        else:
            save_path = data_vo.get_value_by_cons_key("save_path2")

        pcb_result = pcb_entity.get_final_result("G", "P", "F")

        txt_content = "\n".join(txt_content_list)
        filepath = f"{save_path}/{pcb_sn}_{pcb_result}_Lane{track_index}.txt"
        xutil.FileUtil.write_content_to_file_pro(filepath, txt_content, window_line=True)

        return self.x_response()
