#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置管理模块
管理应用程序的配置信息
"""

import json
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict


@dataclass
class SearchPreferences:
    """搜索偏好设置"""
    default_log_level: str = "全部"
    case_sensitive: bool = False
    max_results: int = 1000
    auto_refresh: bool = True
    highlight_keywords: bool = True
    recent_searches: List[str] = None
    
    def __post_init__(self):
        if self.recent_searches is None:
            self.recent_searches = []


@dataclass
class UIPreferences:
    """界面偏好设置"""
    window_width: int = 1400
    window_height: int = 900
    window_x: int = 100
    window_y: int = 100
    font_family: str = "Arial"
    font_size: int = 10
    theme: str = "default"
    show_line_numbers: bool = True
    word_wrap: bool = True


@dataclass
class LogPaths:
    """日志路径配置"""
    default_paths: List[str] = None
    recent_paths: List[str] = None
    auto_scan_subdirs: bool = True
    file_extensions: List[str] = None
    exclude_patterns: List[str] = None
    
    def __post_init__(self):
        if self.default_paths is None:
            self.default_paths = []
        if self.recent_paths is None:
            self.recent_paths = []
        if self.file_extensions is None:
            self.file_extensions = ['.log', '.txt']
        if self.exclude_patterns is None:
            self.exclude_patterns = ['*.tmp', '*.bak']


@dataclass
class ExportSettings:
    """导出设置"""
    default_format: str = "csv"
    include_headers: bool = True
    max_export_rows: int = 10000
    default_export_path: str = ""
    auto_open_after_export: bool = False


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = None):
        if config_dir is None:
            config_dir = os.path.join(os.path.expanduser('~'), '.log_analyzer')
        
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / 'config.json'
        self.error_codes_file = self.config_dir / 'custom_error_codes.json'
        
        # 默认配置
        self.search_prefs = SearchPreferences()
        self.ui_prefs = UIPreferences()
        self.log_paths = LogPaths()
        self.export_settings = ExportSettings()
        self.custom_error_codes = {}
        
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 加载各个配置部分
                if 'search_preferences' in config_data:
                    self.search_prefs = SearchPreferences(**config_data['search_preferences'])
                
                if 'ui_preferences' in config_data:
                    self.ui_prefs = UIPreferences(**config_data['ui_preferences'])
                
                if 'log_paths' in config_data:
                    self.log_paths = LogPaths(**config_data['log_paths'])
                
                if 'export_settings' in config_data:
                    self.export_settings = ExportSettings(**config_data['export_settings'])
            
            # 加载自定义错误码
            if self.error_codes_file.exists():
                with open(self.error_codes_file, 'r', encoding='utf-8') as f:
                    self.custom_error_codes = json.load(f)
                    
        except Exception as e:
            print(f"加载配置失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            config_data = {
                'search_preferences': asdict(self.search_prefs),
                'ui_preferences': asdict(self.ui_prefs),
                'log_paths': asdict(self.log_paths),
                'export_settings': asdict(self.export_settings)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            # 保存自定义错误码
            with open(self.error_codes_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_error_codes, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def add_recent_search(self, search_term: str):
        """添加最近搜索"""
        if search_term and search_term not in self.search_prefs.recent_searches:
            self.search_prefs.recent_searches.insert(0, search_term)
            # 限制最近搜索数量
            self.search_prefs.recent_searches = self.search_prefs.recent_searches[:20]
            self.save_config()
    
    def add_recent_path(self, path: str):
        """添加最近路径"""
        if path and path not in self.log_paths.recent_paths:
            self.log_paths.recent_paths.insert(0, path)
            # 限制最近路径数量
            self.log_paths.recent_paths = self.log_paths.recent_paths[:10]
            self.save_config()
    
    def get_recent_searches(self) -> List[str]:
        """获取最近搜索"""
        return self.search_prefs.recent_searches
    
    def get_recent_paths(self) -> List[str]:
        """获取最近路径"""
        return self.log_paths.recent_paths
    
    def update_window_geometry(self, x: int, y: int, width: int, height: int):
        """更新窗口几何信息"""
        self.ui_prefs.window_x = x
        self.ui_prefs.window_y = y
        self.ui_prefs.window_width = width
        self.ui_prefs.window_height = height
        self.save_config()
    
    def get_window_geometry(self) -> tuple:
        """获取窗口几何信息"""
        return (self.ui_prefs.window_x, self.ui_prefs.window_y, 
                self.ui_prefs.window_width, self.ui_prefs.window_height)
    
    def add_custom_error_code(self, code: str, description: str, 
                             category: str = "自定义", severity: str = "ERROR"):
        """添加自定义错误码"""
        self.custom_error_codes[code] = {
            "description": description,
            "category": category,
            "severity": severity
        }
        self.save_config()
    
    def remove_custom_error_code(self, code: str):
        """删除自定义错误码"""
        if code in self.custom_error_codes:
            del self.custom_error_codes[code]
            self.save_config()
    
    def get_custom_error_codes(self) -> Dict:
        """获取自定义错误码"""
        return self.custom_error_codes
    
    def get_all_log_paths(self) -> List[str]:
        """获取所有日志路径（默认+最近）"""
        all_paths = []
        all_paths.extend(self.log_paths.default_paths)
        all_paths.extend(self.log_paths.recent_paths)
        return list(set(all_paths))  # 去重
    
    def add_default_log_path(self, path: str):
        """添加默认日志路径"""
        if path and path not in self.log_paths.default_paths:
            self.log_paths.default_paths.append(path)
            self.save_config()
    
    def remove_default_log_path(self, path: str):
        """删除默认日志路径"""
        if path in self.log_paths.default_paths:
            self.log_paths.default_paths.remove(path)
            self.save_config()
    
    def get_file_extensions(self) -> List[str]:
        """获取文件扩展名列表"""
        return self.log_paths.file_extensions
    
    def set_file_extensions(self, extensions: List[str]):
        """设置文件扩展名列表"""
        self.log_paths.file_extensions = extensions
        self.save_config()
    
    def get_exclude_patterns(self) -> List[str]:
        """获取排除模式列表"""
        return self.log_paths.exclude_patterns
    
    def set_exclude_patterns(self, patterns: List[str]):
        """设置排除模式列表"""
        self.log_paths.exclude_patterns = patterns
        self.save_config()
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到文件"""
        try:
            config_data = {
                'search_preferences': asdict(self.search_prefs),
                'ui_preferences': asdict(self.ui_prefs),
                'log_paths': asdict(self.log_paths),
                'export_settings': asdict(self.export_settings),
                'custom_error_codes': self.custom_error_codes
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 导入各个配置部分
            if 'search_preferences' in config_data:
                self.search_prefs = SearchPreferences(**config_data['search_preferences'])
            
            if 'ui_preferences' in config_data:
                self.ui_prefs = UIPreferences(**config_data['ui_preferences'])
            
            if 'log_paths' in config_data:
                self.log_paths = LogPaths(**config_data['log_paths'])
            
            if 'export_settings' in config_data:
                self.export_settings = ExportSettings(**config_data['export_settings'])
            
            if 'custom_error_codes' in config_data:
                self.custom_error_codes = config_data['custom_error_codes']
            
            self.save_config()
            return True
            
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.search_prefs = SearchPreferences()
        self.ui_prefs = UIPreferences()
        self.log_paths = LogPaths()
        self.export_settings = ExportSettings()
        self.custom_error_codes = {}
        self.save_config()
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要"""
        return {
            'config_dir': str(self.config_dir),
            'recent_searches_count': len(self.search_prefs.recent_searches),
            'recent_paths_count': len(self.log_paths.recent_paths),
            'default_paths_count': len(self.log_paths.default_paths),
            'custom_error_codes_count': len(self.custom_error_codes),
            'file_extensions': self.log_paths.file_extensions,
            'theme': self.ui_prefs.theme,
            'default_export_format': self.export_settings.default_format
        }


class ConfigDialog:
    """配置对话框（简化版，实际使用时需要用PyQt5实现）"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
    
    def show_search_preferences(self):
        """显示搜索偏好设置"""
        prefs = self.config_manager.search_prefs
        print("搜索偏好设置:")
        print(f"  默认日志级别: {prefs.default_log_level}")
        print(f"  区分大小写: {prefs.case_sensitive}")
        print(f"  最大结果数: {prefs.max_results}")
        print(f"  自动刷新: {prefs.auto_refresh}")
        print(f"  高亮关键词: {prefs.highlight_keywords}")
    
    def show_ui_preferences(self):
        """显示界面偏好设置"""
        prefs = self.config_manager.ui_prefs
        print("界面偏好设置:")
        print(f"  窗口大小: {prefs.window_width}x{prefs.window_height}")
        print(f"  字体: {prefs.font_family}, {prefs.font_size}pt")
        print(f"  主题: {prefs.theme}")
        print(f"  显示行号: {prefs.show_line_numbers}")
        print(f"  自动换行: {prefs.word_wrap}")
    
    def show_log_paths(self):
        """显示日志路径配置"""
        paths = self.config_manager.log_paths
        print("日志路径配置:")
        print(f"  默认路径: {paths.default_paths}")
        print(f"  最近路径: {paths.recent_paths}")
        print(f"  自动扫描子目录: {paths.auto_scan_subdirs}")
        print(f"  文件扩展名: {paths.file_extensions}")
        print(f"  排除模式: {paths.exclude_patterns}")
    
    def show_export_settings(self):
        """显示导出设置"""
        settings = self.config_manager.export_settings
        print("导出设置:")
        print(f"  默认格式: {settings.default_format}")
        print(f"  包含表头: {settings.include_headers}")
        print(f"  最大导出行数: {settings.max_export_rows}")
        print(f"  默认导出路径: {settings.default_export_path}")
        print(f"  导出后自动打开: {settings.auto_open_after_export}")
    
    def show_custom_error_codes(self):
        """显示自定义错误码"""
        codes = self.config_manager.get_custom_error_codes()
        print("自定义错误码:")
        for code, info in codes.items():
            print(f"  {code}: {info['description']} ({info['category']}, {info['severity']})")


# 使用示例
if __name__ == "__main__":
    # 创建配置管理器
    config_mgr = ConfigManager()
    
    # 添加一些测试数据
    config_mgr.add_recent_search("error")
    config_mgr.add_recent_path("/var/log")
    config_mgr.add_custom_error_code("9001", "自定义错误", "测试", "WARNING")
    
    # 显示配置摘要
    summary = config_mgr.get_config_summary()
    print("配置摘要:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 创建配置对话框
    dialog = ConfigDialog(config_mgr)
    dialog.show_search_preferences()
    dialog.show_custom_error_codes()
