<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en" sourcelanguage="zh_CN">
<context>
    <name>ConfigDialog</name>
    <message>
        <location filename="ui/config_dialog.ui" line="14"/>
        <source>个性化配置项</source>
        <translation>Personalized Configuration Items</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="57"/>
        <source>条码校验</source>
        <translation>Barcode Verification</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="39"/>
        <source>校验全部条码</source>
        <translation>Verify All Barcodes</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="44"/>
        <source>仅校验第一个条码</source>
        <translation>Only Verify the First Barcode</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="49"/>
        <source>不校验第一个条码</source>
        <translation>Do Not Verify the First Barcode</translation>
    </message>
    <message>
        <location filename="config_dialog.py" line="93"/>
        <source>仅校验板边条码</source>
        <translation>Only Verify The Pcb Barcode</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="65"/>
        <source>校验失败发送数据到Mes</source>
        <translation>Send Data to MES on Verification Failure</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="70"/>
        <source>校验失败不发送数据到Mes</source>
        <translation>Do Not Send Data to MES on Verification Failure</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="154"/>
        <source>发送Mes</source>
        <translation>Send MES</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="86"/>
        <source>保存全部器件图</source>
        <translation>Save Images of All Components</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="91"/>
        <source>保存检测NG器件图</source>
        <translation>Save Images of Detected NG Components</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="96"/>
        <source>保存复判NG器件图</source>
        <translation>Save Images of Rejudged NG Components</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="101"/>
        <source>不保存器件图</source>
        <translation>Do Not Save Component Images</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="117"/>
        <source>保存全部器件列表</source>
        <translation>Save List of All Components</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="122"/>
        <source>仅保存检测NG器件列表</source>
        <translation>Save List of Detected NG Components Only</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="127"/>
        <source>仅保存复判NG器件列表</source>
        <translation>Save List of Rejudged NG Components Only</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="132"/>
        <source>不保存器件列表</source>
        <translation>Do Not Save Component Lists</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="141"/>
        <source>不发送坏板</source>
        <translation>Do Not Send Defective Boards</translation>
    </message>
    <message>
        <location filename="ui/config_dialog.ui" line="146"/>
        <source>发送坏板</source>
        <translation>Send Defective Boards</translation>
    </message>
</context>
<context>
    <name>CronSettingDialog</name>
    <message>
        <location filename="ui/cron_setting.ui" line="14"/>
        <source>定时设置</source>
        <translation>Timing Settings</translation>
    </message>
    <message>
        <location filename="ui/cron_setting.ui" line="29"/>
        <source>开启定时清除功能</source>
        <translation>Enable Scheduled Clearing</translation>
    </message>
    <message>
        <location filename="ui/cron_setting.ui" line="45"/>
        <source>固定时间清除</source>
        <translation>Fixed Time Clearing</translation>
    </message>
    <message>
        <location filename="ui/cron_setting.ui" line="122"/>
        <source>HH:mm</source>
        <translation>HH:mm</translation>
    </message>
    <message>
        <location filename="ui/cron_setting.ui" line="146"/>
        <source>间隔时间清除</source>
        <translation>Interval Clearing</translation>
    </message>
    <message>
        <location filename="ui/cron_setting.ui" line="193"/>
        <source>小时</source>
        <translation>Hours</translation>
    </message>
    <message>
        <location filename="ui/cron_setting.ui" line="206"/>
        <source>修改定时设置后重启本软件生效！！！</source>
        <translation>Restart the software to apply the changes in timing settings!!!</translation>
    </message>
</context>
<context>
    <name>ErrorCodeWidget</name>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="14"/>
        <source>自定义Mes不良代码</source>
        <translation>Custom MES Fault Codes</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="50"/>
        <source>机型:</source>
        <translation>Model:</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="64"/>
        <source>AIS203/AIS303/AIS40X/AIS50x</source>
        <translation>AIS203/AIS303/AIS40X/AIS50x</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="69"/>
        <source>AIS301</source>
        <translation>AIS301</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="74"/>
        <source>AIS201</source>
        <translation>AIS201</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="79"/>
        <source>AIS63X</source>
        <translation>AIS63X</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="121"/>
        <source>机型</source>
        <translation>Model</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="126"/>
        <source>默认Code</source>
        <translation>Default Code</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="131"/>
        <source>默认描述</source>
        <translation>Default Description</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="136"/>
        <source>自定义Code</source>
        <translation>Custom Code</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="141"/>
        <source>自定义描述</source>
        <translation>Custom Description</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="146"/>
        <source>整体上传Mes</source>
        <translation>Upload to MES (All)</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="151"/>
        <source>单个上传Mes</source>
        <translation>Upload to MES (Single)</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="156"/>
        <source>配置项3</source>
        <translation>Configuration Item 3</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="206"/>
        <source>保存</source>
        <translation>Save</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="213"/>
        <source>导入配置文件</source>
        <translation>Import Configuration File</translation>
    </message>
    <message>
        <location filename="ui/error_code_map_widget.ui" line="220"/>
        <source>导出配置文件</source>
        <translation>Export Configuration File</translation>
    </message>
</context>
<context>
    <name>Form</name>
    <message>
        <location filename="ui/fake_send.ui" line="14"/>
        <source>模拟主软件触发</source>
        <translation>Simulate Main Software Trigger</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="38"/>
        <source>从Mes获取条码</source>
        <translation>Get Barcode from MES</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="418"/>
        <source>条码:</source>
        <translation>Barcode:</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="76"/>
        <source>pcb_barcode001</source>
        <translation>pcb_barcode001</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="479"/>
        <source>模拟触发</source>
        <translation>Simulate Trigger</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="117"/>
        <source>发送设备状态</source>
        <translation>Send Equipment Status</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="151"/>
        <source>设备状态:</source>
        <translation>Equipment Status:</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="159"/>
        <source>进板</source>
        <translation>Board In</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="164"/>
        <source>开始检测</source>
        <translation>Start Detection</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="169"/>
        <source>停止检查</source>
        <translation>Stop Inspection</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="174"/>
        <source>出板</source>
        <translation>Board Out</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="179"/>
        <source>安全门</source>
        <translation>Safety Door</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="184"/>
        <source>调试</source>
        <translation>Debug</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="189"/>
        <source>数据超时</source>
        <translation>Data Timeout</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="194"/>
        <source>板卡 NG</source>
        <translation>Board NG</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="199"/>
        <source>紧急故障</source>
        <translation>Emergency Fault</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="204"/>
        <source>流程错误</source>
        <translation>Process Error</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="209"/>
        <source>直通率告警</source>
        <translation>Pass Rate Alert</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="214"/>
        <source>Marker 错误</source>
        <translation>Marker Error</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="219"/>
        <source>其他错误</source>
        <translation>Other Error</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="261"/>
        <source>发送数据 (数据包路径也可直接复制过来)</source>
        <translation>Send Data (Data packet path can also be directly copied)</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="268"/>
        <source>检测完发送</source>
        <translation>Send after Detection</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="275"/>
        <source>复判完发送</source>
        <translation>Send after Re-inspection</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="306"/>
        <source>数据包路径:</source>
        <translation>Data Packet Path:</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="349"/>
        <source>选择文件夹</source>
        <translation>Select Folder</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="390"/>
        <source>条码校验</source>
        <translation>Barcode Verification</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="425"/>
        <source>sn001,sn002,sn003</source>
        <translation>sn001,sn002,sn003</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="466"/>
        <source>发送空闲状态</source>
        <translation>Send Idle Status</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="507"/>
        <source>日志打印</source>
        <translation>Log Printing</translation>
    </message>
    <message>
        <location filename="ui/fake_send.ui" line="533"/>
        <source>清除日志</source>
        <translation>Clear Log</translation>
    </message>
</context>
<context>
    <name>LoginWidget</name>
    <message>
        <location filename="ui/login_widget.ui" line="84"/>
        <source>登录</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="ui/login_widget.ui" line="29"/>
        <source>账号</source>
        <translation>Username</translation>
    </message>
    <message>
        <location filename="ui/login_widget.ui" line="42"/>
        <source>密码</source>
        <translation>Password</translation>
    </message>
    <message>
        <location filename="ui/login_widget.ui" line="55"/>
        <source>记住密码</source>
        <translation>Remember Password</translation>
    </message>
    <message>
        <location filename="ui/login_widget.ui" line="71"/>
        <source>下次启动自动登录 (勾选此设置必须记住密码)</source>
        <translation>Auto-login on next startup (must remember password if this setting is checked)</translation>
    </message>
</context>
<context>
    <name>MesMainWindow</name>
    <message>
        <location filename="ui/main_window.ui" line="20"/>
        <source>Mes System</source>
        <translation>MES System</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="45"/>
        <source>发送统计</source>
        <translation>Send Statistics</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="97"/>
        <source>发送总次数</source>
        <translation>Total Send Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="552"/>
        <source>0</source>
        <translation>0</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="114"/>
        <source>发送失败次数</source>
        <translation>Send Failure Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="134"/>
        <source>设备状态总次数</source>
        <translation>Total Equipment Status Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="151"/>
        <source>设备状态失败次数</source>
        <translation>Equipment Status Failure Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="198"/>
        <source>校验总次数</source>
        <translation>Total Verification Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="224"/>
        <source>校验失败次数</source>
        <translation>Verification Failure Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="253"/>
        <source>获取条码总次数</source>
        <translation>Total Barcode Acquisition Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="279"/>
        <source>获取条码失败次数</source>
        <translation>Barcode Acquisition Failure Count</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="332"/>
        <source>计数清零</source>
        <translation>Reset Counts</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="339"/>
        <source>定时清除</source>
        <translation>Scheduled Clearing</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="421"/>
        <source>工号：</source>
        <translation>UserId:</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="435"/>
        <source>姓名：</source>
        <translation>UserName:</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="458"/>
        <source>当前作业人员</source>
        <translation>Current Worker</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="501"/>
        <source>总产量:</source>
        <translation>Total Production:</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="545"/>
        <source>日产量:</source>
        <translation>Daily Production:</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="577"/>
        <source>参数配置</source>
        <translation>Parameter Configuration</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="654"/>
        <source>管理项目名称</source>
        <translation>ItemName</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="659"/>
        <source>管理项目值</source>
        <translation>ItemValue</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="674"/>
        <source>ParamCode</source>
        <translation>ParamCode</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="679"/>
        <source>参数名称</source>
        <translation>ParamName</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="684"/>
        <source>参数值</source>
        <translation>ParamValue</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="788"/>
        <source>保存</source>
        <translation>Save</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="843"/>
        <source>简要日志</source>
        <translation>Brief Log</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="893"/>
        <source>详细日志</source>
        <translation>Detailed Log</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="922"/>
        <source>清除日志</source>
        <translation>Clear Log</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="948"/>
        <source>设置</source>
        <translation>Settings</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="955"/>
        <source>工具</source>
        <translation>Tools</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="963"/>
        <source>帮助</source>
        <translation>Help</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="969"/>
        <source>系统</source>
        <translation>System</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="981"/>
        <source>其他设置</source>
        <translation>Other Settings</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="986"/>
        <source>版本</source>
        <translation>Version</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="991"/>
        <source>关于</source>
        <translation>About</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="996"/>
        <source>设置开机自启</source>
        <translation>Set Auto-Start</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="1001"/>
        <source>模拟主软件触发</source>
        <translation>Simulate Trigger of Main Software</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="1006"/>
        <source>创建桌面快捷方式</source>
        <translation>Create Desktop Shortcut</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="1011"/>
        <source>登录</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui" line="1016"/>
        <source>其他参数设置</source>
        <translation>Other Parameter Settings</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui"/>
        <source>个性化配置项</source>
        <translation>Personalized Settings</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui"/>
        <source>自定义Mes不良代码</source>
        <translation>Custom Bad Mes Code</translation>
    </message>
    <message>
        <location filename="ui/main_window.ui"/>
        <source>一键发送日志</source>
        <translation>Send Logs with One Click</translation>
    </message>
</context>
<context>
    <name>OtherSettingDialog</name>
    <message>
        <location filename="ui/other_setting.ui" line="14"/>
        <source>其他设置</source>
        <translation>Other Settings</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="28"/>
        <source>去除重复数据(发送mes)</source>
        <translation>Remove Duplicate Data (Send to MES)</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="41"/>
        <source>合并上下板面数据</source>
        <translation>Merge Top and Bottom Board Data</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="54"/>
        <source>异常弹窗提示</source>
        <translation>Exception Popup Notification</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="67"/>
        <source>发送结果给维修工站</source>
        <translation>Send Results to Repair Station</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui"/>
        <source>启动http服务</source>
        <translation>Start HTTP Service</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="89"/>
        <source>语言(修改后重启生效)</source>
        <translation>Language (Restart Required after Modification)</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="103"/>
        <source>ZH</source>
        <translation>ZH</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="108"/>
        <source>EN</source>
        <translation>EN</translation>
    </message>
    <message>
        <location filename="ui/other_setting.ui" line="128"/>
        <source>维修工站IP</source>
        <translation>Repair Station IP</translation>
    </message>
</context>
<context>
    <name>ParamDialog</name>
    <message>
        <location filename="ui/other_param_setting.ui" line="14"/>
        <source>其他参数设置</source>
        <translation>Other Parameter Settings</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="65"/>
        <source>Log路径</source>
        <translation>Log Path</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="79"/>
        <source>进出类型</source>
        <translation>Check Mode</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="87"/>
        <source>进出站</source>
        <translation>WOCHECK</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="92"/>
        <source>进出机</source>
        <translation>WOMachine</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="100"/>
        <source>强制出机/站</source>
        <translation>Force CheckOut</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="108"/>
        <source>不允许</source>
        <translation>Not Allowed</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="113"/>
        <source>允许</source>
        <translation>Allowed</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="121"/>
        <source>自动切换板式</source>
        <translation>Auto Switch Project</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="129"/>
        <source>否</source>
        <translation>No</translation>
    </message>
    <message>
        <location filename="ui/other_param_setting.ui" line="134"/>
        <source>是</source>
        <translation>Yes</translation>
    </message>
</context>
</TS>
