# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/29 下午3:35
# Author     ：sch
# version    ：python 3.8
# Description：益阳维胜
"""
import os
from datetime import datetime
from typing import Any

from common import xcons, xutil
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

aoi_template = """{project_name}
{barcode}
{line_name}
{board_no}
{user_id}
{order_id}
{test_time}
{cycle_time}
{review_time}
{result}
{board_side}
{comp_number}
{comp_ng_number}{comp_data_str}
"""

aoi_comp_template = """
{comp_tag};{comp_part};{comp_robot_ng_str};{comp_repair_ng_str};{comp_image}"""

spi_template = """Machine,StationID,OperatorID,Model,Barcode,Inspect Time,RepairTime,BoardStatus,Board,Compname,PinNum,DefectError,RepairResult,ClientCode,DefectImagePath,ComponentImagePath,VolumeResult,Volume Upperlimit,Volume lowerlimit,Volume(%),Volume,AreaResult,Area Upperlimit,Area lowerlimit,Area(%),Area,HeightResult,Height Upperlimit,Height lowerlimit,Height(%),Height,X_Result,Shift X limit(%),Shift X(%),Shift X,Y_Result,Shift Y limit(%),Shift Y(%),Shift Y{comp_data_str}"""

# spi_comp_template = """{machine},{station_id},{operator},{model},{barcode},{inspect_time},{repair_time},{board_status},{board},{compname},{pin_num},{defect_error},{repair_result},{client_code},{defect_image_path},{comp_image},{volume_result},{volume_upper},{volume_lower},{volume_rate},{volume_val},{area_result},{area_upper},{area_lower},{area_rate},{area_val},{height_result},{height_upper},{height_lower},{height_rate},{height_val},{x_result},{shift_x_limit_rate},{shift_x_rate},{shift_x_val},{y_result},{shift_y_limit_rate},{shift_y_rate},{shiuft_y_val}"""

spi_comp_template = """
{Machine},{StationID},{OperatorID},{Model},{Barcode},{Inspect Time},{RepairTime},{BoardStatus},{Board},{Compname},{PinNum},{DefectError},{RepairResult},{ClientCode},{DefectImagePath},{ComponentImagePath},{VolumeResult},{Volume Upperlimit},{Volume lowerlimit},{Volume(%)},{Volume},{AreaResult},{Area Upperlimit},{Area lowerlimit},{Area(%)},{Area},{HeightResult},{Height Upperlimit},{Height lowerlimit},{Height(%)},{Height},{X_Result},{Shift X limit(%)},{Shift X(%)},{Shift X},{Y_Result},{Shift Y limit(%)},{Shift Y(%)},{Shift Y}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "yiyangweisheng release v1.0.0.4",
        "device": "AIS430,AIS630",
        "feature": ["上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-30 15:36  上传数据，设备状态
date: 2024-05-27 10:05  保存整板图和器件图
date: 2024-06-11 10:17  换行符改成window换行符
date: 2024-06-18 10:08  偏移值改为百分比
""", }

    form = {
        "machine": {
            "ui_name": "工序",
            "value": "SPI",
        },
        "station_id": {
            "ui_name": "机台序列号",
            "value": "Lin1",
        },
        "user_id": {
            "ui_name": "作业员工号",
            "value": "TRI",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
        "line_id": {
            "ui_name": "线别名称",
            "value": "7700QE",
        },
        "device_name": {
            "ui_name": "设备名",
            "value": "Lin1",
        },
    }

    combo = {
        "data_type": {
            "ui_name": "文件格式",
            "item": ["AOI", "SPI"],
            "value": "AOI",
        }
    }

    path = {
        "save_path1": {
            "ui_name": "保存路径(检测数据)",
            "value": "",
        },
        "save_path2": {
            "ui_name": "保存路径(图片输出)",
            "value": "",
        },
        "save_path3": {
            "ui_name": "保存路径(设备状态)",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        line_id = data_vo.get_value_by_cons_key("line_id")
        order_id = data_vo.get_value_by_cons_key("order_id")
        user_id = data_vo.get_value_by_cons_key("user_id")
        data_type = data_vo.get_value_by_cons_key("data_type")
        save_path1 = data_vo.get_value_by_cons_key("save_path1")
        save_path2 = data_vo.get_value_by_cons_key("save_path2")
        device_name = data_vo.get_value_by_cons_key("device_name")

        machine = data_vo.get_value_by_cons_key("machine")
        station_id = data_vo.get_value_by_cons_key("station_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        time2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT10)
        project_name = pcb_entity.project_name

        base_path = f"{save_path2}/{device_name}/{time2}/{project_name}/{time_file[:8]}"

        if data_type == "AOI":
            board_data_list = []
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)

                barcode = board_entity.barcode
                board_no = board_entity.board_no

                if not pcb_sn and barcode:
                    pcb_sn = barcode

                comp_data_str = ""
                for comp_entity in board_entity.yield_comp_entity():
                    comp_tag = comp_entity.designator

                    p1 = "OK图"
                    if comp_entity.is_robot_ng():
                        comp_data_str += aoi_comp_template.format(**{
                            "comp_tag": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_robot_ng_str": comp_entity.robot_ng_str,
                            "comp_repair_ng_str": comp_entity.get_final_result("0", "0", "1"),
                            "comp_image": comp_entity.image_path
                        })

                        p1 = "NG图"

                    img_path = f"{base_path}/{p1}"
                    xutil.FileUtil.ensure_dir_exist(img_path)

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        comp_dst_img = f"{img_path}/{time_file}_{barcode}_{board_no}_{comp_tag}.png"
                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

                board_data = aoi_template.format(**{
                    "project_name": project_name,
                    "barcode": barcode if barcode else time_file,
                    "line_name": line_id,
                    "board_no": board_no,
                    "user_id": user_id,
                    "order_id": order_id,
                    "test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1),
                    "cycle_time": pcb_entity.get_cycle_time(),
                    "review_time": pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT1),
                    "result": board_entity.get_final_result("PASS", "RPASS", "FAIL"),
                    "board_side": pcb_entity.get_board_side(),
                    "comp_number": board_entity.comp_total_number,
                    "comp_ng_number": board_entity.comp_repair_ng_number,
                    "comp_data_str": comp_data_str
                })
                board_data_list.append(board_data)

            pcb_data = "\n".join(board_data_list)
            filepath = f"{save_path1}/{pcb_sn}_{time_file}.txt"
            xutil.FileUtil.write_content_to_file(filepath, pcb_data, window_line=True)
        elif data_type == "SPI":

            pcb_img_list = pcb_entity.list_all_pcb_image()
            if pcb_img_list:
                src_img0 = pcb_img_list[0]
            else:
                src_img0 = 0

            comp_data_str = ""

            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)

                barcode = board_entity.barcode
                board_no = board_entity.board_no

                if not pcb_sn and barcode:
                    pcb_sn = barcode

                for comp_entity in board_entity.yield_comp_entity():
                    comp_tag = comp_entity.designator

                    p1 = "OK图"

                    if comp_entity.is_robot_ng():
                        p1 = "NG图"

                    img_path = f"{base_path}/{p1}"
                    xutil.FileUtil.ensure_dir_exist(img_path)

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        comp_dst_img = f"{img_path}/{time_file}_{barcode}_{board_no}_{comp_tag}.png"
                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

                    comp_tag = comp_entity.designator
                    comp_img = comp_entity.image_path

                    volume_result = ""
                    volume_upper = ""
                    volume_lower = ""
                    volume_val = ""

                    area_result = ""
                    area_upper = ""
                    area_lower = ""
                    area_val = ""

                    height_result = ""
                    height_upper = ""
                    height_lower = ""
                    height_val = ""

                    x_offset_result = ""
                    x_offset_upper = ""
                    x_offset_lower = ""
                    x_offset_val = ""

                    y_offset_result = ""
                    y_offset_upper = ""
                    y_offset_lower = ""
                    y_offset_val = ""

                    for alg_entity in comp_entity.yield_alg_entity():
                        alg_result = "PASS" if alg_entity.result == "1" else "NG"

                        alg_name = alg_entity.test_name
                        alg_min = alg_entity.min_threshold
                        alg_max = alg_entity.max_threshold
                        alg_val = alg_entity.test_val

                        if alg_name == "Volume":
                            volume_result = alg_result
                            volume_upper = alg_max
                            volume_lower = alg_min
                            volume_val = alg_val
                        elif alg_name == "Area":
                            area_result = alg_result
                            area_upper = alg_max
                            area_lower = alg_min
                            area_val = alg_val
                        elif alg_name == "Height":
                            height_result = alg_result
                            height_upper = alg_max
                            height_lower = alg_min
                            height_val = alg_val
                        elif alg_name == "XOffset":
                            x_offset_result = alg_result
                            x_offset_upper = alg_max
                            x_offset_lower = alg_min
                            x_offset_val = alg_val
                        elif alg_name == "YOffset":
                            y_offset_result = alg_result
                            y_offset_upper = alg_max
                            y_offset_lower = alg_min
                            y_offset_val = alg_val

                    comp_data_str += spi_comp_template.format(**{
                        "Machine": machine,
                        "StationID": station_id,
                        "OperatorID": user_id,
                        "Model": project_name,
                        "Barcode": barcode,
                        "Inspect Time": time_file,
                        "RepairTime": pcb_entity.get_review_time().strftime(xcons.FMT_TIME_FILE),
                        "BoardStatus": board_entity.get_final_result("PASS", "RPASS", "FAIL"),
                        "Board": board_no,
                        "Compname": comp_tag,
                        "PinNum": "1",
                        "DefectError": comp_entity.robot_ng_str,
                        "RepairResult": comp_entity.repair_ng_str,
                        "ClientCode": "",
                        "DefectImagePath": src_img0,
                        "ComponentImagePath": comp_img,
                        "VolumeResult": volume_result,
                        "Volume Upperlimit": volume_upper,
                        "Volume lowerlimit": volume_lower,
                        "Volume(%)": "",
                        "Volume": volume_val,
                        "AreaResult": area_result,
                        "Area Upperlimit": area_upper,
                        "Area lowerlimit": area_lower,
                        "Area(%)": "",
                        "Area": area_val,
                        "HeightResult": height_result,
                        "Height Upperlimit": height_upper,
                        "Height lowerlimit": height_lower,
                        "Height(%)": "",
                        "Height": height_val,
                        "X_Result": x_offset_result,
                        "Shift X limit(%)": x_offset_upper,
                        "Shift X(%)": "",
                        "Shift X": x_offset_val,
                        "Y_Result": y_offset_result,
                        "Shift Y limit(%)": y_offset_upper,
                        "Shift Y(%)": "",
                        "Shift Y": y_offset_val
                    })

            spi_content = spi_template.format(comp_data_str=comp_data_str)

            filepath = f"{save_path1}/{pcb_sn}_{time_file}.txt"
            xutil.FileUtil.write_content_to_file(filepath, spi_content, window_line=True)
        else:
            return self.x_response("false", f"不支持的文件格式[{data_type}]！")

        # 拷贝大图
        pcb_src_img = pcb_entity.get_unknown_t_pcb_image()

        if pcb_src_img:
            pcb_path = f"{base_path}/大板图"
            xutil.FileUtil.ensure_dir_exist(pcb_path)
            pcb_dst_path = f"{pcb_path}/{time_file}_{pcb_sn}.jpg"
            xutil.FileUtil.copy_file(pcb_src_img, pcb_dst_path)

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        save_path3 = other_vo.get_value_by_cons_key("save_path3")

        datetime_now = datetime.now()
        time_date = datetime_now.strftime(xcons.FMT_TIME_FILE0)
        time_default = datetime_now.strftime(xcons.FMT_TIME_DEFAULT)

        filepath = f"{save_path3}/{time_date}.csv"

        if not os.path.exists(filepath):
            file_content = "StartTime,DeviceStatusCode,DeviceStatusDesc"

        else:
            file_content = xutil.FileUtil.read_file(filepath)

        old_code = other_vo.get_old_device_status_code()
        device_status_str = other_vo.get_device_status_str()
        file_content += f"\r\n{time_default},{old_code},{device_status_str}"

        xutil.FileUtil.write_content_to_file(filepath, file_content, window_line=True)

        return self.x_response()
