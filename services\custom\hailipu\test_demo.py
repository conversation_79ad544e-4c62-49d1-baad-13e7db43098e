# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2023/12/25 上午10:11
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

if __name__ == '__main__':
    #     ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    #     <soap:Body>
    #         <GET_TRAYOutput xmlns="http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI">
    #             <P_OUT>
    #                 <AOI_GET_TRAY_RESP>
    #                     <MESSAGEID>1EAE9B59-0AC2-1C20-E054-3C4A923CDF36</MESSAGEID>
    #                     <TIMESTAMP>2015-12-02T11:37:42.310+01:00</TIMESTAMP>
    #                     <INTERFACEID>IF0000</INTERFACEID>
    #                     <PROCESSINGSTATUS>
    #                         <RETURNVALUE>ACCEPTED</RETURNVALUE>
    #                         <RETURNCODE>0</RETURNCODE>
    #                         <RETURNDESCRIPTION/>
    #                     </PROCESSINGSTATUS>
    #                     <TRAY_CONTENT>
    #                         <WORKSTATION>VT-RNS-5311</WORKSTATION>
    #                         <PCB_LIST>
    #                             <AOI_GET_TRAY_PCB>
    #                                 <PCB>130B8184040136G415</PCB>
    #                                 <POSITION>1</POSITION>
    #                             </AOI_GET_TRAY_PCB>
    #                             <AOI_GET_TRAY_PCB>
    #                                 <PCB>130B8184040236G415</PCB>
    #                                 <POSITION>2</POSITION>
    #                             </AOI_GET_TRAY_PCB>
    #                             <AOI_GET_TRAY_PCB>
    #                                 <PCB>130B8184040336G415</PCB>
    #                                 <POSITION>3</POSITION>
    #                             </AOI_GET_TRAY_PCB>
    #                         </PCB_LIST>
    #                     </TRAY_CONTENT>
    #                 </AOI_GET_TRAY_RESP>
    #             </P_OUT>
    #         </GET_TRAYOutput>
    #     </soap:Body>
    # </soap:Envelope>"""
    #     root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    #     aoi_get_tray_req = root[0][0][0][0]
    #     status_item = aoi_get_tray_req.find("{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PROCESSINGSTATUS")
    #
    #     ret_code = status_item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNCODE').text
    #     ret_str = status_item.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNDESCRIPTION').text
    #     print(f"{ret_code=}")
    #     print(f"{ret_str=}")
    #
    #     pcb_list = aoi_get_tray_req.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PCB_LIST')
    #
    #     ret_sn_list = []
    #     for item in pcb_list:
    #         pcb_sn = item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PCB').text
    #         ret_sn_list.append(pcb_sn)
    #
    #     print(ret_sn_list)

    ret_str2 = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <TEST_RESULTOutput xmlns="http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI">
            <P_OUT>
                <AOI_TST_RES_RESP>
                    <MESSAGEID>1EAE9B59-0AC2-1C20-E054-3C4A923CDF36</MESSAGEID>
                    <TIMESTAMP>2016-01-29T15:10:19.828+01:00</TIMESTAMP>
                    <INTERFACEID>IF0000</INTERFACEID>
                    <PROCESSINGSTATUS>
                        <RETURNVALUE>ACCEPTED</RETURNVALUE>
                        <RETURNCODE>0</RETURNCODE>
                        <RETURNDESCRIPTION/>
                    </PROCESSINGSTATUS>
                </AOI_TST_RES_RESP>
            </P_OUT>
        </TEST_RESULTOutput>
    </soap:Body>
</soap:Envelope>"""
    root = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
    aoi_get_tray_req = root[0][0][0][0]
    print(aoi_get_tray_req.tag)

    status_item = aoi_get_tray_req.find("{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}PROCESSINGSTATUS")
    ret_code = status_item.find('{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNCODE').text
    ret_str = status_item.find('.//{http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI}RETURNDESCRIPTION').text
    print(f"{ret_code=}")
    print(f"{ret_str=}")
