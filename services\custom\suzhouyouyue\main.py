# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/26 上午10:00
# Author     ：sch
# version    ：python 3.8
# Description：苏州悠越
"""
from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "suzhouyouyue release v1.0.0.2",
        "device": "",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-26 10:02  init
date: 2024-09-09 15:09  兼容接口返回
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://**********:7000/SmartMES/CheckRoute",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://**********:7000/TestData/UploadData",
        }
    }

    form = {
        "device_sn": {
            "ui_name": "设备编码",
            "value": "",
        },
        "emp_no": {
            "ui_name": "操作人员",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        emp_no = other_vo.get_value_by_cons_key("emp_no")

        sn_m = {}

        for ix, sn in enumerate(other_vo.list_sn()):
            ix += 1
            sn_m[f"SN{ix}"] = sn

        check_param = {
            "PanelNo": "",
            "SNList": [sn_m],
            "DEVICESN": device_sn,
            "EMP_NO": emp_no
        }

        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

        if ret.get("result") != "OK":
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('msg')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT1)

        board_data_list = []
        comp_data_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            board_data_list.append({
                "SN": barcode,
                "PcbRobotResult": board_entity.get_robot_result("PASS", "NG"),
                "PcbTestResult": board_entity.get_repair_result("PASS", "NG"),
            })

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():

                    for alg_entity in comp_entity.yield_alg_entity():
                        # if alg_entity.result != "0":
                        comp_data_list.append({
                            "TESTSTEP": alg_entity.test_name,
                            "MESUREVALUE": alg_entity.test_val,
                            "RESULT": comp_entity.get_final_result("OK", "OK", "NG"),
                            "DefectCode": comp_entity.repair_ng_code
                        })

        data_param = {
            "PanelNo": pcb_sn,
            "RobotResult": pcb_entity.get_robot_result("PASS", "NG"),
            "TestResult": pcb_entity.get_repair_result("PASS", "NG"),
            "SNList": board_data_list,
            "DEVICESN": device_sn,
            "START_TIME": start_time,
            "END_TIME": end_time,
            "TestTime": pcb_entity.get_cycle_time(),
            "PARAMETERLIST": comp_data_list
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

        if ret.get("result") != "OK":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()
