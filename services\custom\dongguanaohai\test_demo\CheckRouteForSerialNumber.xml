<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CheckRouteForSerialNumber xmlns="http://tempuri.org/">
            <configdtstr>
                [{"name":"UserNo","value":"1239"},{"name":"FactoryNo","value":"3"},{"name":"FilePath","value":"E:\\?????��?��???\\AOHAIMES����?��DLL������?(??��)"},{"name":"MAC","value":"00-FF-26-F9-8A-39;4C-49-6C-FB-AB-BC"},{"name":"IPAddress","value":"*******;***********"},{"name":"lineId","value":"110"},{"name":"workStationId","value":"45"},{"name":"MachineID","value":"1684"},{"name":"IsDebug","value":"false"},{"name":"ServerIP","value":"************"},{"name":"ServerDatabaseName","value":"AOHAIMES"},{"name":"WCFUrl","value":"http://************:9999/Service1.svc"},{"name":"rulebindinginfoId","value":"0"}]
            </configdtstr>
            <serialNumber>ddd</serialNumber>
        </CheckRouteForSerialNumber>
    </s:Body>
</s:Envelope>