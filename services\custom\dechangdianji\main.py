# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/27 下午2:24
# Author     ：sch
# version    ：python 3.8
# Description：德昌电机
"""
import logging
import os
from typing import Any

from common import xutil, xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

xml_template = """<?xml version="1.0" encoding="UTF-8" ?>
<result_file>
    <filename>{filename}</filename>
    <supplier>leichen</supplier>
    <date>{date}</date>
    <station_ID>{station_id}</station_ID>
    <operator_ID>{repair_user}</operator_ID>
    <program>{project_name}</program>
    <side_variant>{board_side}</side_variant>
    <pcbs_in_panel>{board_number}</pcbs_in_panel>
    <serial>{pcb_sn}</serial>{board_sn_str}{board_result_str}
    <status>{pcb_result}</status>
    <starttime>{test_time}</starttime>
    <info>
        <window_no>{pcb_comp_number}</window_no>
        <packs_no></packs_no>
        <error_total>{pcb_robot_comp_ng_number}</error_total>
        <error_conf>{pcb_repair_comp_ng_number}</error_conf>
        <inspection_time>{cycle_time}</inspection_time>
        <panel_image>thumb.jpg</panel_image>
        <panel_image_location>{pcb_img_path}</panel_image_location>
        <ng_image_location>{comp_img_ng_path}</ng_image_location>
        <repaired>0</repaired>
    </info>
    <errors>{comp_data_ng_str}
    </errors>
    <goods>{comp_data_ok_str}
    </goods>
</result_file>"""

err_template = """
        <error_no name="{comp_ix}">
            <designator>{comp_tag}</designator>
            <pin></pin>
            <stamp_name></stamp_name>
            <package_name>{comp_tag}</package_name>
            <errortype>{comp_ng_code}</errortype>
            <error_contents>{comp_ng_str}</error_contents>
            <pcb_no>{board_no}</pcb_no>
            <feeder_no></feeder_no>
            <pos_x>{comp_x}</pos_x>
            <pos_y>{comp_y}</pos_y>
            <window></window>
            <ng_message>{comp_ng_str}</ng_message>
            <comment>{comp_ng_str}</comment>
            <ng_image>{comp_img_name}</ng_image>
        </error_no>"""

ok_template = """
        <good_no name="{comp_ix}">
            <designator>{comp_tag}</designator>
            <pin></pin>
            <stamp_name></stamp_name>
            <package_name>{comp_tag}</package_name>
            <pcb_no>{board_no}</pcb_no>
            <feeder_no></feeder_no>
            <pos_x>{comp_x}</pos_x>
            <pos_y>{comp_y}</pos_y>
            <window></window>
            <comment></comment>
        </good_no>"""


def x_archive_data(xml_path):
    """
    归档数据
    :param xml_path:
    :return:
    """
    file_data_list = os.listdir(xml_path)

    if not os.path.exists(xml_path):
        log.warning(f"未找到{xml_path}路径，无法归档数据！")
        return

    date_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_FILE0)

    count = 0
    for f in file_data_list:

        try:
            src_filepath = f"{xml_path}/{f}"

            if os.path.isfile(src_filepath):
                f_list = f.split("_")

                if len(f_list) >= 3:
                    r1, r2, r3 = f_list
                    create_date = r2[:8]
                    if create_date != date_now:
                        bak_path = f"{xml_path}/{create_date[:4]}/{create_date[4:6]}/{create_date[6:8]}"
                        xutil.FileUtil.ensure_dir_exist(bak_path)

                        bak_filepath = f"{bak_path}/{f}"
                        xutil.FileUtil.move_file(src_filepath, bak_filepath)

                        count += 1
                else:
                    log.warning(f"该文件无法归档")

        except Exception as err:
            log.warning(f"归档失败，error：{err}")

    log.info(f"本次共归档数据量：{count}")
    return count


class Engine(ErrorMapEngine):
    version = {
        "title": "dechangdianji release v1.0.0.3",
        "device": "AIS303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-27 15:31  生成本地xml文档
date: 2024-06-28 09:39  结果的枚举值改为OK/NG
date: 2024-07-18 15:48  增加归档数据的功能
""", }

    form = {
        "station_id": {
            "ui_name": "站点ID",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
    }

    combo = {
        "board_side_ui": {
            "ui_name": "板面",
            "item": ["A", "B", "A&B"],
            "value": "A",
        }
    }

    path = {
        "xml_path": {
            "ui_name": "xml保存路径",
            "value": "",
        }
    }

    button = {
        "archive_btn": {
            "ui_name": "归档xml文件"
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 20)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        xml_path = data_vo.get_value_by_cons_key("xml_path", not_null=True)
        station_id = data_vo.get_value_by_cons_key("station_id")
        board_side_ui = data_vo.get_value_by_cons_key("board_side_ui")
        device_name = data_vo.get_value_by_cons_key("device_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data1_str = ""
        board_data2_str = ""

        comp_data_ng_str = ""
        comp_data_ok_str = ""

        pcb_sn = pcb_entity.pcb_barcode

        pcb_robot_comp_ng_number = 0
        pcb_repair_comp_ng_number = 0

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            pcb_robot_comp_ng_number += board_entity.comp_robot_ng_number
            pcb_repair_comp_ng_number += board_entity.comp_repair_ng_number

            board_result = board_entity.get_repair_result("OK", "NG")

            board_data1_str += f"""
    <serial_pcb_{board_no}>{barcode}</serial_pcb_{board_no}>"""

            board_data2_str += f"""
    <status_pcb_{board_no}>{board_result}</status_pcb_{board_no}>"""

            comp_ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                comp_ix += 1
                comp_tag = comp_entity.designator

                if comp_entity.is_repair_ng():
                    src_img_path = comp_entity.image_path
                    if src_img_path:
                        comp_img_name = os.path.basename(src_img_path)
                    else:
                        comp_img_name = ""

                    comp_data_ng_str += err_template.format(**{
                        "comp_ix": comp_ix,
                        "comp_tag": comp_tag,
                        "comp_ng_code": comp_entity.repair_ng_code,
                        "comp_ng_str": comp_entity.repair_ng_str,
                        "board_no": board_no,
                        "comp_x": comp_entity.x_pos,
                        "comp_y": comp_entity.y_pos,
                        "comp_img_name": comp_img_name,
                    })
                else:
                    comp_data_ok_str += ok_template.format(**{
                        "comp_ix": comp_ix,
                        "comp_tag": comp_tag,
                        "comp_ng_code": comp_entity.repair_ng_code,
                        "comp_ng_str": comp_entity.repair_ng_str,
                        "board_no": board_no,
                        "comp_x": comp_entity.x_pos,
                        "comp_y": comp_entity.y_pos,
                    })

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        time_now = xutil.DateUtil.get_datetime_now()

        filename = f"{pcb_sn}_{time_now}_{device_name}"

        review_path = other_data.get("review_path")

        pcb_content = xml_template.format(**{
            "filename": filename,
            "date": xutil.DateUtil.get_datetime_now(),
            "station_id": station_id,
            "repair_user": pcb_entity.repair_user,
            "project_name": pcb_entity.project_name,
            "board_side": board_side_ui,
            "board_number": pcb_entity.board_count,
            "pcb_sn": pcb_sn,
            "board_sn_str": board_data1_str,
            "board_result_str": board_data2_str,
            "pcb_result": pcb_entity.get_repair_result("OK", "NG"),
            "test_time": test_time,
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_robot_comp_ng_number": pcb_robot_comp_ng_number,
            "pcb_repair_comp_ng_number": pcb_repair_comp_ng_number,
            "cycle_time": pcb_entity.get_cycle_time(),
            "pcb_img_path": f"{review_path}/thumbnail",
            "comp_img_ng_path": f"{review_path}/images/ng",
            "comp_data_ng_str": comp_data_ng_str,
            "comp_data_ok_str": comp_data_ok_str,
        })

        dst_filename = f"{xml_path}/{filename}.xml"
        xutil.FileUtil.write_content_to_file(dst_filename, pcb_content, window_line=True)

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        xml_path = other_vo.get_value_by_cons_key("xml_path", not_null=True)
        count = x_archive_data(xml_path)

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "archive_btn":
            xml_path = btn_vo.get_value_by_cons_key("xml_path", not_null=True)
            count = x_archive_data(xml_path)

            if count == 0:
                return self.x_response("false", "暂无数据需要归档")

        return self.x_response()
