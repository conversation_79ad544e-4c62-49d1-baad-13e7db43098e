# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/23 上午9:36
# Author     ：sch
# version    ：python 3.8
# Description：浙江科博达/嘉兴科博达
"""
import os
from typing import Any

from common import xcons, xutil, xsql, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

"""
3. 上传标准版json数据

1. 板式打开/切换时，调用
2. 条码校验，接口
4. 远程暂停，远程启动
5. 机台UI上的信息
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "jiaxingkeboda release v1.0.0.1",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-28 10:54  上传数据，程序更新，远程启停
""", }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        }
    }

    path = {
        "save_path_json": {
            "ui_name": "保存路径(标准json文件)",
            "value": "",
        },
        "save_path_json_1": {
            "ui_name": "保存路径(机台出产信息)",
            "value": "",
        },
        "save_path_json_2": {
            "ui_name": "保存路径(机台程序更新)",
            "value": "",
        },
        "save_path_json_3": {
            "ui_name": "扫描路径(interlock)",
            "value": "",
        },
    }

    other_form = {
    }

    other_combo = {
        "is_scan": {
            "ui_name": "开启轮询扫描interlock文件夹",
            "item": ["Yes", "No"],
            "value": "Yes"
        },
        "cron_time": {
            "ui_name": "轮询间隔(interlock)",
            "item": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "20", "30"],
            "value": "1"
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        # other_dao = OtherDao({}, main_window.config_data)

        is_scan = other_vo.get_value_by_cons_key("is_scan")
        cron_time = other_vo.get_value_by_cons_key("cron_time", to_int=True)

        main_window.set_cron_setting(True if is_scan == "Yes" else False,
                                     int(cron_time))

        # main_window.config_data["app_setting"]["custom_interval_cron"] = True if is_scan == "Yes" else False
        # main_window.config_data["app_setting"]["custom_interval_time"] = int(cron_time)  # 3s
        self.log.info("init main window done!")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path_json = data_vo.get_value_by_cons_key("save_path_json")
        save_path_json_1 = data_vo.get_value_by_cons_key("save_path_json_1")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        pcb_data = {
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        json_filepath1 = f"{save_path_json}/{time_file}_{pcb_sn}.json"
        xutil.FileUtil.dump_json_to_file(json_filepath1, pcb_data)

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        return self.x_response()

    def send_check_project_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        save_path_json_2 = other_vo.get_value_by_cons_key("save_path_json_2")

        project_name = other_vo.json_data.get('projectName')
        time_now = xutil.DateUtil.get_datetime_now()

        filepath = f"{save_path_json_2}/{time_now}_{project_name}.json"
        file_data = {
            "ProgramName": project_name,
            "StartTime": time_now,
            "EndTime": time_now
        }

        xutil.FileUtil.dump_json_to_file(filepath, file_data)

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        save_path_json_3 = other_vo.get_value_by_cons_key("save_path_json_3")

        try:
            for file in os.listdir(save_path_json_3):
                filepath = f"{save_path_json_3}/{file}"

                if not filepath.endswith("Json") and not filepath.endswith("json"):
                    continue

                self.log.info(f"正在读取文件[{filepath}]")
                json_data = xutil.FileUtil.load_json_file(filepath)
                self.log.info(f"控制信息[{json_data}]")
                if "InterLock" in json_data:
                    # 锁停
                    xrequest.send_device_start_or_stop("0")
                    os.remove(filepath)
                    self.log.info(f"已锁停，文件已删除！")
                elif "unLock" in json_data:
                    # 锁停
                    xrequest.send_device_start_or_stop("1")
                    os.remove(filepath)
                    self.log.info(f"已启动，文件已删除！")

        except Exception as err:
            self.log.info(f"抓取interlock控制信息失败！error:{err}")

    def send_board_data_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        save_path_json_1 = other_vo.get_value_by_cons_key("save_path_json_1")

        board_data = other_vo.json_data.get("boardData", [])

        for item in board_data:
            board_side = item.get("boardSide")
            project_name = item.get("projectName")

            filepath = f"{save_path_json_1}/{project_name}_{board_side}.json"
            xutil.FileUtil.dump_json_to_file(filepath, item)

        return self.x_response()


if __name__ == '__main__':
    sql_service = xsql.MySQLService("127.0.0.1", "AIS400", 3306, "root", "aoi2014")
    ret_result = sql_service.select_data_by_sql_str(
        "SELECT  b.board_id , b.test_result , b.board_result, b.pass_count , b.ng_count ,b.false_call_count  FROM  board b left JOIN project p on b.project_id = p.project_id WHERE p.pcb='333' and p.bom='999bak';")
    for row in ret_result:
        print(row)
    sql_service.close()
