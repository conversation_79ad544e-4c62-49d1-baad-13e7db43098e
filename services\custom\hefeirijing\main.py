# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/5/13 上午9:37
# Author     ：sch
# version    ：python 3.8
# Description：合肥日竞
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "hefeirijing release v1.0.0.2",
        "device": "AIS40X",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-05-13 09:38  init
date: 2024-05-15 16:39  格式修改
""", }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    combo = {
        "board_side": {
            "ui_name": "板面",
            "item": ["T", "B", "T&B"],
            "value": "T"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        board_side = data_vo.get_value_by_cons_key("board_side")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1)
        project_name = pcb_entity.project_name

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            board_result = board_entity.get_repair_result("PASS", "Fail,")

            comp_ng_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    repair_ng_str = comp_entity.repair_ng_str
                    comp_ng_list.append(f"{comp_tag}:{repair_ng_str}")

            txt_content = f"{project_name},{board_side},,{barcode},{board_result}{';'.join(comp_ng_list)},{start_time},,"
            filename = f"{save_path}/{barcode}_{time_file}_{board_no}.txt"
            xutil.FileUtil.write_content_to_file(filename, txt_content)

        return self.x_response()
