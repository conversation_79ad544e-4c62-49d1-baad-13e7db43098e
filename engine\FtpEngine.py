#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2023/4/9
# Author: sun<PERSON><PERSON><PERSON>
import io
from ftplib import FTP, error_perm
from os.path import dirname

from common.xutil import log


class FTPClient(object):
    """
    FTP client
    """

    buf_size = 1024

    def __init__(self, host, username, password, port=21, encoding="utf-8"):
        self._host = host
        self._username = username
        self._password = password
        self._port = port
        self._encoding = encoding

        self.ftp = None

    def login(self, timeout: int = 5):
        """
        登录
        :return:
        """
        self.ftp = FTP(timeout=timeout)

        self.ftp.set_debuglevel(0)
        self.ftp.encoding = self._encoding
        self.ftp.connect(self._host, self._port)
        resp = self.ftp.login(self._username, self._password)

        log.info(self.ftp.getwelcome())
        log.info("login success")
        return resp

    def close(self):
        """
        关闭连接
        :return:
        """
        if not self.ftp:
            raise Exception("ftp未登录，无法close")

        # self.ftp.close()
        self.ftp.quit()
        log.info("ftp 连接已断开")

    def upload_file(self, filepath: str, filename="", is_log=True):
        """
        上传文件到ftp服务器
        :param filepath: 将要上传的文件
        :param filename: 上传文件名
        :param is_log: 是否打印上传成功日志
        :return:
        """
        if not self.ftp:
            raise Exception("ftp未登录，无法上传文件")

        if not filename:
            if "/" in filepath:
                filename = filepath.rsplit("/", 1)[1]
            else:
                filename = filepath

        with open(filepath, "rb") as f:
            resp = self.ftp.storbinary(f"STOR {filename}", f, self.buf_size)

        if is_log:
            log.info(f"ftp上传成功, 源文件{filepath}, ftp目标文件名:{filename}")

        return resp

    def upload_content(self, filename: str, text_content: str):
        """
        上传文本内容到ftp服务器
        :param filename: 上传文件名
        :param text_content: 将要上传的内容
        :return:
        """
        if not self.ftp:
            raise Exception("ftp未登录，无法上传文件")

        file_obj = io.BytesIO(text_content.encode('utf-8'))
        resp = self.ftp.storbinary(f"STOR {filename}", file_obj, self.buf_size)

        log.info(f"ftp上传成功, ftp目标文件名:{filename}")
        return resp

    def download_file(self, filename: str, dst_path):
        """
        下载文件
        :param filename: ftp服务器上的文件名
        :param dst_path: 保存路径
        :return:
        """
        if not self.ftp:
            raise Exception("ftp未登录，无法下载文件")

        full_path = f"{dst_path}/{filename}"

        with open(full_path, "wb") as f:
            self.ftp.retrbinary(f"RETR {filename}", f.write, self.buf_size)  # 下载文件

        log.info(f"{full_path} ftp下载成功")

    def cd_or_mkdir(self, dir_path):
        """
        进入目录
        :param dir_path:
        :return:
        """
        self._cd_or_mkdir("/")

        log.info(f"ftp目录已切换至：{dir_path}")
        self._cd_or_mkdir(dir_path)

    def _cd_or_mkdir(self, dir_path, first_call=True):
        """
        进入某个目录，如果目录不存在则创建
        :param dir_path: 目录
        :param first_call: 是否是第一次调用
        :return:
        """
        try:
            self.ftp.cwd(dir_path)
        except error_perm:
            self._cd_or_mkdir(dirname(dir_path), False)
            self.ftp.mkd(dir_path)
            if first_call:
                self.ftp.cwd(dir_path)
