# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : socket_service.py
# Time       ：2023/2/2 下午2:34
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import socket
import traceback

from PyQt5.QtCore import QThread

from common import xutil, xparse, xcons
from common.xcons import DEVICE_STATUS_EN_MAP
from common.xutil import log, time_cost, CircularList, LimitedDict
from engine.DataEngine import data_engine
from entity.MesEntity import PcbEntity
from services.route import engine
from templates.main_window import Ui_MesMainWindow
from vo.mes_vo import DataVo, OtherVo

socket_global_data = {}

circular_list_robot = CircularList(500)  # 过滤机器的重复发送
circular_list_repair = CircularList(500)  # 过滤复判的重复发送
circular_list_production = CircularList(500)  # 过滤稼动数据的重复发送

send_result_dict = LimitedDict(500)


# def s_response(result: bool = True, msg: str = "ok"):
#     """
#     socket 的标准响应
#     :param result:
#     :param msg:
#     :return:
#     """
#     time_now = xutil.DateUtil.get_datetime_now("%Y.%m.%d %H:%M:%S")
#     return {'key': time_now, 'result': result, 'string': msg}


def send_response(status: bool = True, msg: str = "ok") -> str:
    """
    返回标准的响应给主软件
    :param status:
    :param msg:
    {'Description': 'ok', 'MesStatus': True, 'SocketStatus': True}
    :return:
    """
    ret = {
        'Description': msg,
        'MesStatus': status,
        'SocketStatus': status
    }

    ret = json.dumps(ret, ensure_ascii=False)

    log.info(f"<<<<<<<<<数据返回给主软件：{ret}")
    return ret


def call_response(status: bool = True, msg: str = "ok") -> str:
    """
    返回标准的响应给主软件
    ps: {'key': '2023.03.14 17:16:32', 'result': True, 'string': 'ok'}
    :param status:
    :param msg:
    :return:
    """
    time_now = xutil.DateUtil.get_datetime_now("%Y.%m.%d %H:%M:%S")

    ret = {
        "key": time_now,
        "result": status,
        "string": msg
    }

    ret = json.dumps(ret, ensure_ascii=False)
    log.info(f"<<<<<<<<<数据返回给主软件：{ret}")

    return ret


class SocketThread(QThread):
    # trigger = pyqtSignal(str)

    def __init__(self, main_window: Ui_MesMainWindow):
        super(SocketThread, self).__init__()
        self.main_window = main_window
        self.sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        # TCP释放连接后实现端口的立即复用
        self.sk.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    def get_config_data(self) -> dict:
        """
        获取主界面的配置参数

        返回值如下： （会随着软件更新而更新）
        {
          "form": {
            "user": "456",
            "api_url": "http://127.0.0.1:8081",
            "password": "456"
          },
          "app_setting": {
            "merge_send": false,
            "send_error_info": true,
            "send_info_to_repair": false,
            "repair_ip": "",
            "filter_repeat_send": true,
            "is_cron_clear": false,
            "fixed_status_1": true,
            "fixed_status_2": true,
            "fixed_time_1": "12:0",
            "fixed_time_2": "18:0",
            "fixed_time_clear": false,
            "interval_time_clear": true,
            "interval_time": 2
          }
        }
        :return:
        """
        return self.main_window.config_data  # noqa

    def get_common_config_by_key(self, config_key: str):
        """
        获取个性化配置项
        :param config_key:
        :return:
        """
        return self.main_window.config_data.get("common_config", {}).get(config_key)  # noqa

    def log_info_to_window(self, msg: str, status=True, send_to_repair=False):
        """
        打印日志到界面
        :return:
        """
        self.main_window.log_info(msg, status, send_to_repair)  # noqa

    def run(self) -> None:
        port = engine.mes_config_port
        log.info("\n")
        log.info(f"mes配置器 socket服务将启动在: 0.0.0.0:{port} ...")
        log.info(f"版本------> {engine.get_release()}")

        self.sk.bind(("0.0.0.0", port))
        self.sk.listen()
        # log.info(f"socket服务启动成功，监听端口{port}....")
        log.info(f"mes配置器 socket服务启动成功!")

        while True:
            conn, addr = self.sk.accept()
            try:
                data = conn.recv(4086)
                if data == b'connect Status.':
                    # log.info(f"connect status...")
                    continue

                if data == xcons.SOCKET_CLOSE_KEY:
                    conn.send(b"the server had closed")
                    conn.close()
                    self.sk.close()
                    log.info(f"socket服务已关闭~")
                    break

                device_data = data.decode("utf-8")

                new_version = engine.get_release()

                log.info(f"mesconfig version: {new_version}")
                log.info(f">>>>>>>>>接收到的主软件/维修站数据: {device_data}")

                if not device_data.startswith("{"):
                    log.warning(f"不是规范的mes请求，不处理！")
                    continue

                json_data = json.loads(device_data)  # 主软件传过来的数据
                # 接口文档可参考：https://doc.weixin.qq.com/doc/w3_AUYAfAa-ACAd00MkdyJQtWyUyzISn?scode=AHcAyQfeAAshDkDKdK

                # 两个接口，一个发送数据到mes，一个其他所有的接口
                if "ReviewPath" in json_data:
                    ret_data = self.send_data_to_mes(json_data)
                else:
                    ret_data = self.call_other_function(json_data)

                conn.send(ret_data.encode("utf-8"))
                # conn.close()
            except Exception as err:
                log.warning(f"socket异常，error：{err}")
                log.warning(f"error: {traceback.format_exc()}")
            finally:
                conn.close()

    @time_cost
    def send_data_to_mes(self, json_data: dict) -> str:
        """
        发送数据到mes

        InspectMesType: (inspector,主软件检测完发送)  (repair,维修站复判后发送)
        :param json_data: ps: {'ReviewPath': '/home/<USER>/aoi/run/results/333.001/20221208/T_20221208173539149_1_NG'}
        :return:
        """
        lang_is_en = self.main_window.lang_is_en()  # noqa

        try:
            total_count = self.main_window.send_total_number.text()
            self.main_window.send_total_number.setText(str(int(total_count) + 1))

            review_path = json_data.get("ReviewPath")

            if "," in review_path:
                review_path = review_path.replace(",", ";")

            if "，" in review_path:
                review_path = review_path.replace("，", ";")

            # ----------- 兼容前端传过来的数据可能出现B面在前面，T面在后面的问题：可能会引起一些bug -----
            if ";" in review_path:
                r1, r2 = review_path.split(";")
                if "B_" in r1:
                    review_path = f"{r2};{r1}"
                    json_data["ReviewPath"] = review_path
                    log.warning(f"修正数据，已将T面数据置于前面！")
            # ----------- 兼容前端传过来的数据可能出现B面在前面，T面在后面的问题：可能会引起一些bug -----

            inspect_type = json_data.get("InspectMesType", "repair")

            # send_board_no = []
            # if board_index:
            #     if ";" in board_index:
            #         board_no_list1 = board_index.split(";")
            #         # send_board_no = board_no_list1
            #         send_board_no = board_no_list1[0].split(",")
            #     else:
            #         # send_board_no.append(board_index)
            #         send_board_no = board_index.split(",")
            #
            # if send_board_no:
            #     log.info(f"分板发送的拼板ID：{send_board_no}")
            config_data = self.get_config_data()
            app_setting = config_data.get("app_setting", {})

            if json_data.get("funcName") != "UploadProductionInfo":

                if inspect_type == "inspector":
                    m1 = "检测完发送数据到mes"

                    if lang_is_en:
                        m1 = "SendDataByInspector"

                    circular_list_robot.add_item(review_path)
                    send_count = circular_list_robot.buffer.count(review_path)
                    if send_count > 1:
                        if app_setting.get("filter_repeat_send"):
                            msg = f"filter duplicate send mes,review_path: {review_path}"
                            self.log_info_to_window(msg)
                            cache_ret = send_result_dict.get_value(review_path)

                            send_res = send_response(True, msg)

                            if cache_ret:
                                send_res = send_response(
                                    cache_ret.get("result"),
                                    cache_ret.get("string")
                                )
                            return send_res
                        else:
                            log.warning(
                                f"该数据包第[{send_count}]次检测完发送MES，请评估是否合理，如果不合理，请勾选【设置-其他设置-去除重复数据！】")
                else:
                    m1 = "复判完发送数据到mes"

                    if lang_is_en:
                        m1 = "SendDataByRepair"

                    circular_list_repair.add_item(review_path)
                    send_count = circular_list_repair.buffer.count(review_path)
                    if send_count > 1:
                        if app_setting.get("filter_repeat_send"):
                            msg = f"filter duplicate send mes,review_path: {review_path}"
                            self.log_info_to_window(msg)
                            return send_response(True, msg)
                        else:
                            log.warning(
                                f"该数据包第[{send_count}]次复判完发送MES，请评估是否合理，如果不合理，请勾选【设置-其他设置-去除重复数据！】")

            else:
                m1 = "发送稼动数据到mes"

                if lang_is_en:
                    m1 = "UploadProductionInfo"

                circular_list_production.add_item(review_path)
                send_count = circular_list_production.buffer.count(review_path)
                if send_count > 1:
                    if app_setting.get("filter_repeat_send"):
                        msg = f"filter duplicate send mes,review_path: {review_path}"
                        self.log_info_to_window(msg)
                        return send_response(True, msg)
                    else:
                        log.warning(
                            f"该数据包第[{send_count}]次发送MES，请评估是否合理，如果不合理，请勾选【设置-其他设置-去除重复数据！】")

            self.log_info_to_window(f"-------------{m1}------------")

            sendmes_setting1 = self.get_common_config_by_key("sendmes_setting1")
            sendmes_setting2 = self.get_common_config_by_key("sendmes_setting2")
            sendmes_setting3 = self.get_common_config_by_key("sendmes_setting3")

            log.info(f"个性化配置项3：{sendmes_setting1}")
            log.info(f"个性化配置项4：{sendmes_setting2}")
            log.info(f"个性化配置项5：{sendmes_setting3}")

            common_config_views = self.main_window.common_config_views  # noqa
            sendmes_setting1_ix = common_config_views.sendmes_setting1.currentIndex()
            sendmes_setting2_ix = common_config_views.sendmes_setting2.currentIndex()
            sendmes_setting3_ix = common_config_views.sendmes_setting3.currentIndex()

            board_index = json_data.get("BoardIndex", "")
            if ";" in board_index:
                board_index = board_index.split(";")[0]

            board_index_list = board_index.split(',')
            board_index_list = [i for i in board_index_list if i]

            try:
                error_code_map = None
                if data_engine.exist_error_code_map():
                    select_device = self.main_window.config_data.get('app_setting',  # noqa
                                                                     {}).get('error_code_device',
                                                                             'AIS203/AIS303/AIS40X/AIS50x')

                    error_code_map = data_engine.get_error_code_map(select_device)
                else:
                    select_device = ""

                if ";" in review_path:
                    if not app_setting.get("merge_send"):
                        error_count = self.main_window.send_err_number.text()
                        self.main_window.send_err_number.setText(str(int(error_count) + 1))
                        self.log_info_to_window(f"发送失败，请勾选【设置-其他设置-合并上下板面数据】设置", False)

                        return send_response(False, f"请勾选【设置-其他设置-合并上下板面数据】设置")
                    t_path, b_path = review_path.split(";")
                    pcb_data, board_data, pcb_entity_list = xparse.merge_mes_data(
                        t_path,
                        b_path,
                        board_index_list,
                        sendmes_setting1_ix,
                        error_code_map=error_code_map,
                        select_device=select_device,
                        save_comp_type=sendmes_setting2_ix,
                        skip_bad_board=True if sendmes_setting3_ix == 0 else False,
                    )
                    log.info(f"上下板面数据合并完毕！")
                else:
                    if select_device == "AIS63X":
                        t_comp_pad_data = xutil.XmlUtil.get_pad_test_data(f"{review_path}/report.xml")
                    else:
                        t_comp_pad_data = None

                    pcb_data, board_data = xparse.parse_mes_data(
                        review_path,
                        board_index_list,
                        sendmes_setting1_ix,
                        error_code_map=error_code_map,
                        comp_pad_data=t_comp_pad_data,
                        save_comp_type=sendmes_setting2_ix,
                        skip_bad_board=True if sendmes_setting3_ix == 0 else False,
                    )

                    pcb_entity_list = [PcbEntity(pcb_data, board_data)]

            except Exception as err:
                log.error(traceback.format_exc())
                error_count = self.main_window.send_err_number.text()
                self.main_window.send_err_number.setText(str(int(error_count) + 1))

                if str(err) == "检测点没有算法":
                    self.log_info_to_window(f"data parse failed，component data err", False)
                else:
                    self.log_info_to_window(f"other exception：{err}", False)

                return send_response(False, f"data parse failed：{err}")

            # pcb_robot_result = pcb_data.get("pcb_robot_result")
            # pcb_user_result = pcb_data.get("pcb_user_result")
            #
            # if pcb_robot_result:
            #     final_result = "PASS"
            #
            # else:
            #     if pcb_user_result:
            #         final_result = "REPASS"
            #     else:
            #         final_result = "NG"
            #
            # pcb_data["final_result"] = final_result

            other_data = {
                "inspect_type": inspect_type,  # (inspector,主软件检测完发送)  (repair,维修站复判后发送)
                "review_path": review_path,
                "upstream_json_data": json_data,  # 主软件/维修站发过来的数据
                "board_index_list": board_index_list,
            }

            all_barcode = pcb_data.get("all_barcode", [])

            self.log_info_to_window(f"Board No: {pcb_data.get('board_ids')}")
            self.log_info_to_window(f"barcode number: {len(all_barcode)}")
            self.log_info_to_window(f"barcode list")
            self.log_info_to_window(">>>>>>>>>>")
            for i in all_barcode:
                self.log_info_to_window(i)
            self.log_info_to_window(">>>>>>>>>>")

            if pcb_data.get('upload_mes') is False:
                log.warning("已配置指定不良跳过，本次不上传数据到Mes！")
                return send_response(True, "ok")

            common_config_views = self.main_window.common_config_views  # noqa
            check_set_ix2 = common_config_views.check_barcode_setting2.currentIndex()

            if check_set_ix2 == 1:
                all_barcode = pcb_data.get("all_barcode", [])
                check_sn_error_list = socket_global_data.get("check_sn_error_list", [])

                for sn in all_barcode:
                    if sn in check_sn_error_list:
                        # log.warning(f"校验失败不发送数据到Mes")

                        m6 = f"SN:{sn}, 条码校验失败不发送数据到Mes！"
                        if lang_is_en:
                            m6 = "Do Not Send Data to MES on Verification Failure"

                        self.log_info_to_window(m6)

                        return send_response(True, "ok")

            pcb_data["inspect_type"] = inspect_type
            data_dao = DataVo(pcb_data, board_data, config_data, pcb_entity_list, json_data)

            if json_data.get("funcName") != "UploadProductionInfo":
                ret_data = engine.send_data_to_mes(data_dao, other_data, self.main_window)
                send_result_dict.add_item(review_path, ret_data)
            else:
                ret_data = engine.send_production_info_to_mes(data_dao, other_data, self.main_window)

            if not ret_data:
                m6 = f"未实现[发送数据到mes]相关业务功能"
                if lang_is_en:
                    m6 = f"Not implement [SendMes] function"
                self.log_info_to_window(m6)
                return call_response()

            if not ret_data.get("result"):
                # 发送数据到mes失败
                error_count = self.main_window.send_err_number.text()
                self.main_window.send_err_number.setText(str(int(error_count) + 1))
                m7 = f"发送失败，error：{ret_data.get('string')}"
                if lang_is_en:
                    m7 = f"send mes failed，error：{ret_data.get('string')}"

                self.log_info_to_window(m7, False, send_to_repair=True)

                if json_data.get("funcName") != "UploadProductionInfo":
                    return send_response(False, ret_data.get("string"))
                else:
                    return call_response(False, ret_data.get("string"))

            m8 = ret_data.get("string")

            if lang_is_en:
                m8 = "SendMes Successful"

            self.log_info_to_window(m8)

            if json_data.get("funcName") != "UploadProductionInfo":
                return send_response(True, "ok")
            else:
                return call_response(True, "ok")
        except Exception as e:
            log.warning(traceback.format_exc())
            error_msg = f"发送数据失败，其他异常，error：{e}"

            if lang_is_en:
                error_msg = f"Upload Data Failed, error: {e}"

            error_count = self.main_window.send_err_number.text()
            self.main_window.send_err_number.setText(str(int(error_count) + 1))

            self.log_info_to_window(error_msg, False, send_to_repair=True)

            if json_data.get("funcName") != "UploadProductionInfo":
                return send_response(False, error_msg)
            else:
                return call_response(False, error_msg)

    @time_cost
    def call_other_function(self, json_data: dict) -> str:
        """
        其他接口
        :param json_data:
        :return:
        """
        func_name = json_data.get("funcName")
        config_data = self.get_config_data()

        lang_is_en = self.main_window.lang_is_en()  # noqa

        other_dao = OtherVo(json_data, config_data)

        if func_name == "CheckBarcode":
            try:
                s1 = "条码校验"

                if lang_is_en:
                    s1 = "CheckBarcode"

                self.log_info_to_window(f"-------------{s1}------------")
                total_count = self.main_window.check_total_number.text()
                self.main_window.check_total_number.setText(str(int(total_count) + 1))
                all_sn_list = other_dao.list_sn()

                check_barcode_setting1 = self.get_common_config_by_key("check_barcode_setting1")

                log.info(f"个性化配置项1：{check_barcode_setting1}")

                common_config_views = self.main_window.common_config_views  # noqa

                check_set_ix1 = common_config_views.check_barcode_setting1.currentIndex()

                if check_set_ix1 == 1:
                    real_check_list = all_sn_list[:1]
                    json_data["funcArgs"] = [real_check_list]
                elif check_set_ix1 == 2:
                    real_check_list = all_sn_list[1:]
                    json_data["funcArgs"] = [real_check_list]
                elif check_set_ix1 == 3:
                    barcode_map = other_dao.get_barcode_map()
                    if not barcode_map:
                        m3 = f"条码校验失败，缺少参数[barcodeList]，主软件版本不支持！"

                        if lang_is_en:
                            m3 = "missing parameter [barcodeList], not supported by the main software version!"

                        self.log_info_to_window(m3, False)
                        return call_response(False, m3)

                    else:
                        pcb_sn = barcode_map.get("-1")  # 板边条码

                        if not pcb_sn:
                            m3 = f"条码校验失败，未扫到板边条码！"

                            if lang_is_en:
                                m3 = "Barcode not scanned"

                            self.log_info_to_window(m3, False)
                            return call_response(False, m3)

                        else:
                            real_check_list = [pcb_sn]

                else:
                    real_check_list = all_sn_list

                self.log_info_to_window(f"check barcode list: {real_check_list}")

                if not real_check_list:
                    m3 = f"条码校验失败，未扫到条码！"

                    if lang_is_en:
                        m3 = "Barcode not scanned"

                    self.log_info_to_window(m3, False)
                    return call_response(False, m3)

                ret_data = engine.check_sn(other_dao, self.main_window)

                if not ret_data:
                    s2 = f"未实现[条码校验]相关业务功能"

                    if lang_is_en:
                        s2 = f"Not implement [CheckBarcode] function"

                    self.log_info_to_window(s2)
                    return call_response()

                check_barcode_setting2 = self.get_common_config_by_key("check_barcode_setting2")
                log.info(f"个性化配置项2：{check_barcode_setting2}")

                check_sn_error_list = socket_global_data.get("check_sn_error_list", [])

                func_args = other_dao.get_func_args()
                if type(all_sn_list) is str:
                    log.warning(f"barcode had changed!")
                    all_sn_list = func_args

                if not ret_data.get("result"):
                    # 条码校验失败
                    # 需要缓存条码校验失败的数据
                    for error_sn in all_sn_list:
                        if error_sn not in check_sn_error_list:
                            check_sn_error_list.append(error_sn)
                            log.warning(f"校验失败的条码已缓存：{error_sn}")

                    socket_global_data["check_sn_error_list"] = check_sn_error_list[-200:]

                    err_count = self.main_window.check_err_number.text()
                    self.main_window.check_err_number.setText(str(int(err_count) + 1))

                    m3 = f"条码校验失败，error: {ret_data.get('string')}"
                    if lang_is_en:
                        m3 = f"CheckBarcode Failed，error: {ret_data.get('string')}"

                    self.log_info_to_window(m3, False)
                    return call_response(False, ret_data.get("string"))
                else:
                    for ok_sn in all_sn_list:
                        if ok_sn in check_sn_error_list:
                            check_sn_error_list.remove(ok_sn)
                            log.info(f"本次条码校验成功，删除缓存数据成功！sn:{ok_sn}")

                    socket_global_data["check_sn_error_list"] = check_sn_error_list[-200:]

                    m4 = "条码校验成功"
                    if lang_is_en:
                        m4 = f"CheckBarcode Successful"

                    self.log_info_to_window(m4)

                return call_response(True, ret_data.get('string'))
            except Exception as e:
                log.warning(traceback.format_exc())
                error_msg = f"条码校验失败，其他异常，error：{e}"

                if lang_is_en:
                    error_msg = f"CheckBarcode Failed，error：{e}"

                err_count = self.main_window.check_err_number.text()
                self.main_window.check_err_number.setText(str(int(err_count) + 1))
                self.log_info_to_window(error_msg, False)

                return call_response(False, error_msg)

        elif func_name == "SendDeviceStatus":
            # 此处重新打印，主要是为了看清楚设备状态中一些中文描述
            log.info(f">>>>>>>>>接收到的主软件/维修站解析后的json数据: {json_data}")
            m1 = "发送设备状态到mes"
            if lang_is_en:
                m1 = "SendDeviceStatus"
            try:
                self.log_info_to_window(f"-------------{m1}------------")
                v1_status_str = other_dao.get_device_status_str()

                if not v1_status_str:
                    v1_status_str = json_data.get("statusDescV3")

                if lang_is_en:
                    v1_status_str = DEVICE_STATUS_EN_MAP.get(v1_status_str, v1_status_str)

                self.log_info_to_window(f"DeviceStatus：{v1_status_str}")

                total_count = self.main_window.status_total_number.text()
                self.main_window.status_total_number.setText(str(int(total_count) + 1))

                # 20240704 之后，接口version变为v3
                # V3：https://www.showdoc.com.cn/DefineMes/10678187678020884    密码aoi2014

                ret_data = engine.send_device_status_to_mes(other_dao, self.main_window)

                if not ret_data:
                    m2 = f"未实现[发送设备状态到mes]相关业务功能"

                    if lang_is_en:
                        m2 = f"Not implement [{m1}] function"

                    self.log_info_to_window(m2)
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    err_count = self.main_window.status_err_number.text()
                    self.main_window.status_err_number.setText(str(int(err_count) + 1))

                    m3 = f"发送设备状态失败，error：{ret_data.get('string')}"
                    if lang_is_en:
                        m3 = f"{m1} Failed，error：{ret_data.get('string')}"

                    self.log_info_to_window(m3, False)
                    return call_response(False, ret_data.get("string"))

                m4 = "发送设备状态成功"
                if lang_is_en:
                    m4 = f"{m1} Successful"

                self.log_info_to_window(m4)
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                log.warning(traceback.format_exc())
                error_msg = f"发送设备状态失败，其他异常，error：{e}"
                if lang_is_en:
                    error_msg = f"{m1} Failed, error: {e}"

                err_count = self.main_window.status_err_number.text()
                self.main_window.status_err_number.setText(str(int(err_count) + 1))
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name in ["GetBarcodeList", "GetBarcodeListV2"]:
            try:
                m1 = "从mes获取条码"

                if lang_is_en:
                    m1 = "GetBarcodeList"

                self.log_info_to_window(f"-------------{m1}------------")

                total_count = self.main_window.get_sn_number.text()
                self.main_window.get_sn_number.setText(str(int(total_count) + 1))

                pcb_sn = other_dao.get_pcb_sn()

                m2 = f"使用设备扫到的第一个条码:{pcb_sn} 去获取拼板条码中..."
                if lang_is_en:
                    m2 = f"pcb_sn: {pcb_sn}"

                self.log_info_to_window(m2)

                ret_data = engine.get_sn_by_mes(other_dao, self.main_window)

                if not ret_data:
                    self.log_info_to_window(f"Not implement [GetBarcodeList] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 从mes获取条码失败
                    err_count = self.main_window.get_sn_err_number.text()
                    self.main_window.get_sn_err_number.setText(str(int(err_count) + 1))

                    m3 = f"获取条码失败，error：{ret_data.get('string')}"
                    if lang_is_en:
                        m3 = f"GetBarcodeList Failed，error：{ret_data.get('string')}"

                    self.log_info_to_window(m3, False)
                    return call_response(False, ret_data.get("string"))

                sn_str = ret_data.get('string')

                sn_list = sn_str.split(',')

                m4 = "获取条码成功"
                if lang_is_en:
                    m4 = "GetBarcodeList Successful"

                self.log_info_to_window(m4)

                if "{" not in sn_str:
                    m5 = f"条码数量:{len(sn_list)} 条码列表:{sn_list}"
                    if lang_is_en:
                        m5 = f"Barcode Number:{len(sn_list)} Barcode List:{sn_list}"
                else:
                    data_map = json.loads(sn_str)

                    # del data_map["-1"]
                    # del data_map["-2"]
                    m5 = f"条码数量:{len(data_map) - 2} 条码列表:{data_map}"

                self.log_info_to_window(m5)

                return call_response(True, ret_data.get('string'))
            except Exception as e:
                log.warning(traceback.format_exc())
                error_msg = f"从mes获取条码失败，其他异常，error：{e}"

                if lang_is_en:
                    error_msg = f"GetBarcodeList Failed，error：{e}"

                err_count = self.main_window.get_sn_err_number.text()
                self.main_window.get_sn_err_number.setText(str(int(err_count) + 1))
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "CycleCallMes":
            key_str = "发送空闲状态给mes"

            if lang_is_en:
                key_str = "CycleCallMes"
            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                total_count = self.main_window.status_total_number.text()
                self.main_window.status_total_number.setText(str(int(total_count) + 1))

                ret_data = engine.send_idle_status_to_mes(other_dao, self.main_window)

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")

                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    err_count = self.main_window.status_err_number.text()
                    self.main_window.status_err_number.setText(str(int(err_count) + 1))

                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                log.warning(traceback.format_exc())
                error_msg = f"{key_str} Exception，error：{e}"
                err_count = self.main_window.status_err_number.text()
                self.main_window.status_err_number.setText(str(int(err_count) + 1))
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "GetBadBoard":
            key_str = "从mes获取坏板信息"
            if lang_is_en:
                key_str = "GetBadBoard"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.get_bad_board_info(other_dao, self.main_window)

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                log.warning(traceback.format_exc())
                error_msg = f"{key_str} Exception，error：{e}"
                err_count = self.main_window.status_err_number.text()
                self.main_window.status_err_number.setText(str(int(err_count) + 1))
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "PanelData":
            try:
                self.log_info_to_window("-------------发送面板数据到mes------------")
                ret_data = engine.send_panel_data(other_dao, self.main_window)

                if not ret_data:
                    self.log_info_to_window(f"未实现[发送面板数据到mes]相关业务功能")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"发送面板数据到mes失败，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window("发送面板数据到mes")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"发送面板数据到mes失败，其他异常，error：{e}"
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "SendBoardStatus":
            key_str = "发送生产过程"

            if lang_is_en:
                key_str = "SendBoardStatus"

            try:

                self.log_info_to_window(f"-------------{key_str}------------")
                self.log_info_to_window(f"BoardStatus：{other_dao.get_production_desc()}")

                ret_data = engine.send_production_to_mes(other_dao, self.main_window)

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                log.warning(traceback.format_exc())
                error_msg = f"{key_str} Exception，error：{e}"
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "sendProjectInfo":
            key_str = "发送板式信息"

            if lang_is_en:
                key_str = "sendProjectInfo"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.send_project_info_to_mes(other_dao, self.main_window, "")

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "sendOutBoardInfo":
            key_str = "发送出板信息"

            if lang_is_en:
                key_str = "sendOutBoardInfo"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.send_out_board_info_to_mes(other_dao, self.main_window, "")

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "GetProjectNameBySn":
            key_str = "从mes获取程序"

            if lang_is_en:
                key_str = "GetProjectNameBySn"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                self.log_info_to_window(f"barcode list: {other_dao.list_sn()}")

                ret_data = engine.get_project_name_by_sn(other_dao, self.main_window, "")

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        # bosch需求场景使用，相机扫码到条码后，向MES发起条码校验确认是否要进行板式切换
        elif func_name == "CheckProjectByBarcode":
            key_str = "扫码从MES获取板信息"
            if lang_is_en:
                key_str = "CheckProjectByBarcode"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.check_project_by_barcode(other_dao, json_data)

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    self.log_info_to_window(f"{key_str} Failed，error：\n{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "CheckProject":
            key_str = "切换程序中"

            if lang_is_en:
                key_str = "CheckProject"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.send_check_project_event(other_dao, self.main_window, "")

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "SendBoardData":
            key_str = "发送界面信息中"

            if lang_is_en:
                key_str = "SendBoardData"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.send_board_data_event(other_dao, self.main_window, "")

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    # 发送设备状态失败
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        elif func_name == "BtnClickPush":
            key_str = "操作类发送"

            if lang_is_en:
                key_str = "BtnClickPush"

            try:
                self.log_info_to_window(f"-------------{key_str}------------")
                ret_data = engine.send_btn_click_push_event(other_dao, self.main_window, "")

                if not ret_data:
                    self.log_info_to_window(f"Not implement [{key_str}] function")
                    return call_response()

                if not ret_data.get("result"):
                    self.log_info_to_window(f"{key_str} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{key_str} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{key_str} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)

        else:
            try:
                self.log_info_to_window(f"--- call {func_name} ---")
                ret_data = engine.call_custom_function(func_name, other_dao)
                if not ret_data:
                    m1 = f"未定义的FuncName: {func_name}，请联系管理员！"
                    if lang_is_en:
                        m1 = f"Not implement FuncName: [{func_name}], Please contact Administrators"
                    self.log_info_to_window(m1)
                    return call_response(False, m1)

                if not ret_data.get("result"):
                    self.log_info_to_window(f"{func_name} Failed，error：{ret_data.get('string')}", False)
                    return call_response(False, ret_data.get("string"))

                self.log_info_to_window(f"{func_name} Successful")
                return call_response(True, ret_data.get('string'))

            except Exception as e:
                error_msg = f"{func_name} Exception，error：{e}"
                log.warning(traceback.format_exc())
                self.log_info_to_window(error_msg, False)
                return call_response(False, error_msg)
