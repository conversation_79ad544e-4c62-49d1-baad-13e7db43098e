# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/2 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：深圳欣旺达AOI    深圳欣旺达总部工厂四楼  （鉴锐欣供应商） 机型431
"""
import json
import os
import sqlite3
import threading
import time
import traceback
from datetime import datetime
from queue import Queue
from typing import Any

import urllib3

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import x_response, log
from engine.MesEngine import ErrorMapEngine
from services.custom.shenzhenxinwangda_jrx_4l_431.xwd_module import upload_image_to_s3, concat_ng_info_to_pcb_image, \
    concat_ng_info_to_board_image
from vo.mes_vo import DataVo, OtherVo, ButtonVo, ComboVo

# 超时时间
DEFAULT_TIMEOUT = 20

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_alg_data(review_path: str) -> dict:
    """
    获取算法数据
    """
    alg_db_path = f"{review_path}/AlgorithmInfo.db"

    if not os.path.exists(alg_db_path):
        log.warning(f"{alg_db_path}不存在！")
        return {}

    conn = sqlite3.connect(alg_db_path)
    data_map = {}

    cur = conn.cursor()

    try:
        cur.execute("SELECT * FROM compalgorithmdata;")

        # 获取所有行的结果列表
        rows = cur.fetchall()

        # 遍历结果并打印每一行
        for row in rows:
            data_map[str(row[1])] = {
                "alg_name": row[3],
                "alg_val": json.loads(row[4])
            }
    finally:
        cur.close()
        conn.close()

    log.info(f"算法数据： {len(data_map)}")

    return data_map


def refresh_token(
        api_url: str,
        user_id: str,
        password: str,
        device_id: str,
        timeout=5,
        log_save_path=None,
):
    """
    刷新token
    :param api_url:
    :param user_id:
    :param password:
    :param device_id:
    :param timeout:
    :param log_save_path:
    :return:
    """
    login_param = {
        "userId": user_id,
        "password": password,
        "deviceId": device_id
    }

    if log_save_path:
        log_str1 = f"请求URL：{api_url}  请求参数：\n{json.dumps(login_param, ensure_ascii=False)}"
        xutil.FileUtil.write_request_log(log_save_path, log_str1)

    ret = xrequest.RequestUtil.post_json(api_url, login_param, timeout=timeout)

    if log_save_path:
        log_str2 = f"接口响应：{json.dumps(ret, ensure_ascii=False)}"
        xutil.FileUtil.write_request_log(log_save_path, log_str2)

    if str(ret.get("code")) != "200":
        return x_response("false", f"MES接口响应异常，登录失败，error：{ret.get('msg')}")

    global_data["is_login"] = True


check_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTestCommon xmlns="WWW.SUNWODA.COM">
            <M_SN>{sn}</M_SN>
            <M_MACHINCENO>{machine_no}</M_MACHINCENO>
            <M_EMP>{emp}</M_EMP>
            <M_MO>{mo}</M_MO>
        </GroupTestCommon>
    </soap:Body>
</soap:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <WIPTEST xmlns="WWW.SUNWODA.COM">
            <M_SN>{M_SN}</M_SN>
            <M_RESULT>{M_RESULT}</M_RESULT>
            <M_USERNO>{M_USERNO}</M_USERNO>
            <M_MACHINENO>{M_MACHINENO}</M_MACHINENO>
            <M_ERROR>{M_ERROR}</M_ERROR>
            <M_ITEMVALUE>{M_ITEMVALUE}</M_ITEMVALUE>
        </WIPTEST>
    </soap:Body>
</soap:Envelope>"""

check_template_v2 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <BindCarrierAndPcbV2 xmlns="WWW.SUNWODA.COM">
      <CARRIER_ID>{CARRIER_ID}</CARRIER_ID>
      <PCB_SN>{PCB_SN}</PCB_SN>
      <M_MACHINCENO>{M_MACHINCENO}</M_MACHINCENO>
      <M_EMP>{M_EMP}</M_EMP>
      <M_MO>{M_MO}</M_MO>
    </BindCarrierAndPcbV2>
  </soap:Body>
</soap:Envelope>"""

bind_sn_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <PcbAndFpcBind xmlns="WWW.SUNWODA.COM">
      <pcbSn>{pcbSn}</pcbSn>
      <fpcSn>{fpcSn}</fpcSn>
      <fixsn>{fixsn}</fixsn>
      <monumber>{monumber}</monumber>
      <empno>{empno}</empno>
    </PcbAndFpcBind>
  </soap:Body>
</soap:Envelope>"""


def x_request_device_status(
        api_url_status: str,
        status_code_v3: str,
        req_cycle: int,
        device_id: str,
):
    data_list = []
    warn_list = []
    if status_code_v3 in ["1003", "1005", "3001"]:
        state = "0"  # 待机

        data_list.append({
            "extendCode": status_code_v3,
            "extendValue": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    elif status_code_v3 in ["1001", "1002", "1004"]:
        state = "1"  # 运行

        data_list.append({
            "extendCode": status_code_v3,
            "extendValue": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    elif status_code_v3 in ["2001", "2002", "3002", "3003", "3004", "3005", "3006", "3007",
                            "4001", "4002", "4003", "5001", "5002"]:
        state = "2"  # 报警

        warn_list.append({
            "warnCode": status_code_v3,
            "warnMsg": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    else:
        log.warning(f"未知的设备状态，本次不上传！")
        state = "99"

    send_count = global_data.get("send_status_count", 0)
    send_count += 1

    if state != "99":
        # 上传设备状态
        d1 = datetime.now()

        time_file = d1.strftime(xcons.FMT_TIME_FILE)
        f2 = d1.strftime(xcons.FMT_TIME_DEFAULT)
        device_param = {
            "taskId": f"DEV_{time_file}_{str(send_count).zfill(4)}",
            "reqTime": f2,
            "reqCycle ": req_cycle * 1000,
            "deviceCode": device_id,
            "statusCode": state,
            "dataList": data_list,
            "warnList": warn_list,
        }

        xrequest.RequestUtil.post_json(api_url_status, device_param)
        global_data["last_device_code_v3"] = status_code_v3


class ThreadPool:
    def __init__(self, max_workers=5):
        self.max_workers = max_workers  # 控制最大线程数
        self.task_queue = Queue()  # 任务队列
        self.workers = []  # 工作线程列表
        # self.results = {}  # 存储每个任务的执行结果
        self.lock = threading.Lock()  # 线程安全
        self.completed_event = threading.Event()
        self.active_tasks = 0
        self._init_workers()  # 初始化工作线程
        # self.result_queue = Queue()
        self.done_queue = Queue()  # 专门用来计数，用来等待所有任务完成

    def _init_workers(self):
        """初始化工作线程池"""
        for _ in range(self.max_workers):
            worker = threading.Thread(target=self._worker, daemon=True)
            worker.start()
            self.workers.append(worker)

    def _worker(self):
        """工作线程的主循环"""
        while True:
            try:
                task_id, func, args, kwargs = self.task_queue.get()
                with self.lock:
                    self.active_tasks += 1
                # try:
                # 重试失败的任务,最多重试3次
                for attempt in range(3):
                    try:
                        log.info(f"第{attempt + 1}次上传该文件！")
                        ret_url = func(*args, **kwargs)  # 找不到图片
                        # self.results[task_id] = {
                        #     'status': 'success',
                        #     'result': ret_url
                        # }

                        self.done_queue.put((task_id, ret_url))
                        break
                    except Exception as e:
                        if attempt == 2:  # 最后一次尝试失败
                            # self.results[task_id] = {
                            #     'status': 'error',
                            #     'result': ""
                            # }
                            log.warning(f"三次都上传失败，error: {e}")
                            self.done_queue.put((task_id, ""))

                        time.sleep(0.1)
                        log.warning(f"上传失败，error:{traceback.format_exc()}")
                # except Exception as e:
                #     with self.lock:
                #         self.results[task_id] = {
                #             'status': 'error',
                #             'error': str(e)
                #         }
                #     log.error(traceback.format_exc())
                # finally:
                self.task_queue.task_done()
                with self.lock:
                    self.active_tasks -= 1
                    if self.active_tasks == 0 and self.task_queue.empty():
                        self.completed_event.set()
            except Exception as e:
                log.error(f"Worker thread error: {e}")
                log.warning(traceback.format_exc())
                continue

    def add_task(self, task_id, func, *args, **kwargs):
        """添加任务到队列"""
        self.task_queue.put((task_id, func, args, kwargs))

    # def wait_completion(self, timeout=20):  # 改为默认20秒
    #     """等待所有任务完成，支持超时
    #     Args:
    #         timeout (int): 超时时间，默认20秒
    #     Returns:
    #         dict: 所有任务的执行结果
    #     """
    #     if self.completed_event.wait(timeout=timeout):
    #         return self.results
    #     else:
    #         log.warning(f"任务执行超过{timeout}秒超时限制")
    #         return {task_id: {'status': 'error', 'error': f'Upload timeout ({timeout}s)'}
    #                 for task_id in self.results.keys()}

    def clear_cache(self):
        """
        删除一些状态
        """
        self.done_queue = Queue()
        # self.results.clear()
        log.info(f"线程缓存已清除！")


class Engine(ErrorMapEngine):
    version = {
        "customer": ["深圳欣旺达jrx_4l_431", "shenzhenxinwangda_jrx_4l_431"],
        "title": "shenzhenxinwangda_jrx_4l_431 release v1.0.0.40",
        "device": "AIS431",
        "feature": ["条码校验", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-02 18:32  条码校验，上传数据，登录，获取界面信息，设备状态
date: 2024-11-29 14:52  更换条码校验，上传数据的接口
date: 2024-11-30 11:34  修改条码校验格式
date: 2024-11-30 13:10  获取B面的条码过站
date: 2024-12-01 22:51  保存日志+上传图片到AWS存储桶
date: 2024-12-02 14:55  存储桶放在界面配置
date: 2024-12-15 22:25  新增传输算法数据
date: 2024-12-18 09:17  增加软硬板码绑定
date: 2025-01-17 14:08  jira:36430,拼版发送改为整版发送
date: 2025-01-17 15:34  jira:36430,修改整版图路径为 日期/条码/整版图片
date: 2025-01-17 17:26  jira:36430,修改拼版图保存路径
date: 2025-01-18 09:24  jira:36430,修复上传图片数量,修改上传图片目录层级,整版图上传应有NG不良描述为命名
date: 2025-01-20 09:38  jira:36430,修改aws_endpoint_url服务器地址获取逻辑
date: 2025-01-20 15:54  jira:36430,修改拼版图和整版图的命名规则
date: 2025-01-20 18:10  jira:36430,拼版图生成的信息增加不良位号-不良类型-不良代码-不良描述
date: 2025-02-20 14:28  bugfix: 第一次打开Mes配置器时，报警：PermissionError: [Errno 13] 权限不够
date: 2025-03-01 11:11  bugfix: 增加堆栈打印报错信息,修复原有图片上传失败bug
date: 2025-03-03 15:08  jira:37581,整板图拼接白图，记录 拼板号：不良代码-不良描述
date: 2025-03-05 10:30  optimize: 重构白板图显示逻辑，移除重复代码，确保所有NG器件都能正确显示
date: 2025-03-05 18:00  optimize: 修改白板图宽度,修改字体大小
date: 2025-03-06 15:42  optimize: 修改字体大小,使用ubuntu自带字体FreeSans.ttf,增加间距防止字体之间重叠
date: 2025-03-07 14:52  optimize: 调整字体大小保证白板图内容清晰可见
date: 2025-03-08 10:43  optimize: 修改拼版图字体大小
date: 2025-03-14 11:18  jira:1313,整版图-拼版图-带有白板图的整版图-带有白板图的拼版图，在上传客户的AWS-S3服务器之后,做对应的删除操作,节省内存空间
date: 2025-03-19 15:17  optimize: 主软件生成的拼版图mes做删除(临时方案)
date: 2025-04-02 22:19  jira:1624,同步上传逻辑改为异步上传
date: 2025-04-07 18:35  jira:1624,优化线程上传
date: 2025-04-08 17:06  jira:1624   优化多线程上传逻辑
date: 2025-04-09 15:44  jira:1624   优化pillow图片处理
date: 2025-04-10 15:17  bugfix: 修复线程逻辑bug
date: 2025-05-08 16:24  兼容维修站图片截断问题
date: 2025-05-09 14:33  注释img.verify()方法 否则文件指针会提前释放
date: 2025-06-18 10:29  当检测到坏板时，smallPanelTestResult传BadMark
""", }

    other_combo = {
        "device_cron": {
            "ui_name": "定时上传设备状态",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "device_sequence": {
            "ui_name": "定时上传频率(s)",
            "item": ["30", "60", "180", "360", "600", "1200", "2400", "3000"],
            "value": "180",
        },
    }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(登录)",
            "value": "http://127.0.0.1:8081/common/Login",
        },
        "api_url_factory": {
            "ui_name": "接口URL(获取工厂)",
            "value": "http://127.0.0.1:8081/common/FactoryInfoDownLoadV1",
        },
        "api_url_workstation": {
            "ui_name": "接口URL(获取车间)",
            "value": "http://127.0.0.1:8081/common/WorkStationDownLoadV1",
        },
        "api_url_line": {
            "ui_name": "接口URL(获取线体)",
            "value": "http://127.0.0.1:8081/common/LineDownLoadV1",
        },
        "api_url_station": {
            "ui_name": "接口URL(获取工站)",
            "value": "http://127.0.0.1:8081/common/StationDownloadV1",
        },
        "api_url_mo": {
            "ui_name": "接口URL(获取工单)",
            "value": "http://127.0.0.1:8081/common/MoInfoDownLoad",
        },
        "api_url_check": {
            "ui_name": "接口URL(工序校验)",
            "value": "http://127.0.0.1:8081/test/MESInterface.asmx",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据/过站)",
            "value": "http://127.0.0.1:8081/commonApi/TackProduct.ashx",
        },
        # "api_url_data2": {
        #     "ui_name": "接口URL(上传数据/过站-含图片链接)",
        #     "value": "",
        # },
        "api_url_status": {
            "ui_name": "接口URL(上传设备状态)",
            "value": "http://127.0.0.1:8081/common/deviceServices",
        },
        "aws_endpoint_url": {
            "ui_name": "AWS Service Url",
            "value": "zzzxhcp.hzhcp.sunwoda.com",
        },
        "aws_access_key_id": {
            "ui_name": "AWS Access Key ID",
            "value": "enp6eGhjcA==",
        },
        "aws_secret_access_key": {
            "ui_name": "AWS Secret Access Key",
            "value": "24785287ae393901fe8c08477dbf7450",
        },
        "region_name": {
            "ui_name": "AWS RegionName",
            "value": "us-west-2",
        },
        "bucket_name": {
            "ui_name": "AWS BucketName",
            "value": "ZZZX-BL-5D1L-BL-SMT-01-YS-DEK-01",  # us-east-1
        },
    }

    form = {
        "syb": {
            "ui_name": "事业部",
            "value": "ZZZX",
        },
        "device_id": {
            "ui_name": "设备Id/设备编号",
            "value": "",
        },
        "user_id": {
            "ui_name": "工号",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登录",
        },
    }

    combo = {
        "is_upload_img": {
            "ui_name": "是否上传图片",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "is_upload_ng": {
            "ui_name": "上传NG数据/过站",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "factory_list": {
            "ui_name": "工厂",
            "item": [],
            "value": "",
        },
        "workstation_list": {
            "ui_name": "车间",
            "item": [],
            "value": "",
        },
        "line_list": {
            "ui_name": "线体",
            "item": [],
            "value": "",
        },
        "station_list": {
            "ui_name": "工站",
            "item": [],
            "value": "",
        },
        "mo_list": {
            "ui_name": "工单",
            "item": [],
            "value": "",
        },
    }

    path = {
        "log_save_path": {
            "ui_name": "日志保存路径",
            "value": "",
        }
    }

    def __init__(self):
        super().__init__()
        self.thread_pool = ThreadPool(max_workers=8)

    def init_main_window(self, main_window, other_vo: OtherVo):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        device_id = other_vo.get_value_by_cons_key("device_id")
        user_id = other_vo.get_value_by_cons_key("user_id")
        password = other_vo.get_value_by_cons_key("password")
        log_save_path = other_vo.get_value_by_cons_key("log_save_path")

        device_cron = other_vo.get_value_by_cons_key("device_cron")
        device_sequence = other_vo.get_value_by_cons_key("device_sequence", to_int=True)

        main_window.set_cron_setting(device_cron == "Yes", device_sequence)

        try:
            refresh_token(
                api_url_login,
                user_id,
                password,
                device_id,
                timeout=1,
                log_save_path=log_save_path
            )
            log.info(f"自动登录成功！")
        except Exception as err:
            err_msg = f"自动登录失败，error：{err}"
            self.log.warning(err_msg)
            xutil.FileUtil.write_request_log(log_save_path, err_msg)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        device_id = other_vo.get_value_by_cons_key("device_id")
        user_id = other_vo.get_value_by_cons_key("user_id")
        mo_list_select = other_vo.get_value_by_cons_key("mo_list")
        log_save_path = other_vo.get_value_by_cons_key("log_save_path", not_null=True)

        sn_list = other_vo.list_sn()

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        pcb_sn = sn_list[0]

        new_sn_list = sn_list[1:]

        format_sn_list = []
        ix = 0
        for item in new_sn_list:
            ix += 1
            format_sn_list.append(f"{ix}:{item}")

        check_param = check_template_v2.format(**{
            "CARRIER_ID": pcb_sn,
            "PCB_SN": "|".join(format_sn_list),
            "M_MACHINCENO": device_id,
            "M_EMP": user_id,
            "M_MO": mo_list_select,
        })

        try:
            log_str1 = f"接口URL：{api_url_check}  接口参数：\n{check_param}"
            xutil.FileUtil.write_request_log(log_save_path, log_str1)

            ret_str = xrequest.RequestUtil.post_soap(
                api_url_check,
                check_param,
                soap_action="WWW.SUNWODA.COM/BindCarrierAndPcbV2"
            )
            xutil.FileUtil.write_request_log(log_save_path, f"响应参数：{ret_str}")
            root_check = xutil.XmlUtil.get_xml_root_by_str(ret_str)

            ret_result = root_check[0][0][0].text
            if "TRUE" not in ret_result:
                return self.x_response("false", f"mes接口异常，error：{ret_result}")
        except Exception as err:
            err_msg = f"mes网络异常，error：{err}"

            xutil.FileUtil.write_request_log(log_save_path, err_msg)
            return self.x_response("false", err_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        # global panel_url, has_robot_ng, ng_designators
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        user_id = data_vo.get_value_by_cons_key("user_id")
        device_id = data_vo.get_value_by_cons_key("device_id")

        # syb = data_vo.get_value_by_cons_key("syb")
        # factory_list = data_vo.get_value_by_cons_key("factory_list")
        # workstation_list = data_vo.get_value_by_cons_key("workstation_list")
        # line_list = data_vo.get_value_by_cons_key("line_list")
        station_list = data_vo.get_value_by_cons_key("station_list")
        aws_access_key_id = data_vo.get_value_by_cons_key("aws_access_key_id")
        aws_secret_access_key = data_vo.get_value_by_cons_key("aws_secret_access_key")
        mo_list = data_vo.get_value_by_cons_key("mo_list")
        is_upload_img = data_vo.get_value_by_cons_key("is_upload_img")
        region_name = data_vo.get_value_by_cons_key("region_name")
        is_upload_ng = data_vo.get_value_by_cons_key("is_upload_ng")
        log_save_path = data_vo.get_value_by_cons_key("log_save_path", not_null=True)
        aws_endpoint_url = data_vo.get_value_by_cons_key("aws_endpoint_url")
        bucket_name_ui = data_vo.get_value_by_cons_key("bucket_name")

        cache_data = xutil.CacheUtil.get_cache_data()

        # factory_map = cache_data.get("factory_map", {})
        # workstation_map = cache_data.get("workstation_map", {})
        # line_map = cache_data.get("line_map", {})
        station_map = cache_data.get("station_map", {})

        # factory_select = factory_map.get(factory_list, "")
        # workstation_select = workstation_map.get(workstation_list, "")
        # line_select = line_map.get(line_list, "")
        station_select = station_map.get(station_list, "")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        pcb_sn = pcb_entity.pcb_barcode

        err_msg_list = []
        board_data_list = []

        date_tmp = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE1)
        date1 = date_tmp[:8]  # 年月日
        date2 = date_tmp[8:]  # 时分秒
        # bucket_name = f"{syb}-{factory_select}-{workstation_select}-{line_select}-{station_select}-{date1}"

        board_panel_url_list = []
        only_one_ng_code = ""
        ng_board_info = []

        upload_tasks = []
        board_image_info = {}

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.b_barcode
            # 拼板条码
            barcode_pin = board_entity.barcode
            board_no = board_entity.board_no
            board_image_path = board_entity.board_image_path

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng() and is_upload_ng == "No":
                log.warning(f"NG数据不上传Mes！")
                continue

            upload_data_item_v3 = []

            # 收集该拼版的不良代码和不良描述
            ng_codes = []
            ng_descriptions = []

            for comp_entity in board_entity.yield_comp_entity():
                if not only_one_ng_code:
                    only_one_ng_code = comp_entity.repair_ng_str or comp_entity.robot_ng_str
                    self.log.info(f"only_one_ng_code : {only_one_ng_code}")

                # 用于收集整版图的不良器件的不良代码
                if comp_entity.is_repair_ng() and comp_entity.repair_ng_code not in ng_codes:
                    ng_codes.append(comp_entity.repair_ng_code)
                    ng_descriptions.append(f"{comp_entity.repair_ng_code}-{comp_entity.repair_ng_str}")

                test_item_list = []
                for alg_entity in comp_entity.yield_alg_entity():
                    test_item_list.append({
                        "testCode": "",
                        "tetstItem": "",
                        "badCode": comp_entity.robot_ng_code,
                        "badName": comp_entity.robot_ng_str,
                        "result": "OK" if alg_entity.result == "0" else "NG",
                        "testValue": alg_entity.test_val,
                        "testUpper": alg_entity.max_threshold,
                        "testLower": alg_entity.min_threshold,
                        "testUnit": "",
                    })

                upload_data_item_v3.append({
                    "postionCode": comp_entity.designator,
                    "testResult": pcb_entity.get_repair_result("PASS", "NG"),
                    "testItemList": test_item_list,
                })

            if ng_codes:
                ng_board_info.append({
                    'board_no': board_no,
                    'ng_descriptions': ng_descriptions
                })

            if board_image_path and os.path.exists(board_image_path) and is_upload_img == "Yes":
                board_image_path2 = ""

                # 打开拼版图并创建新图片
                try:
                    processed_filename = concat_ng_info_to_board_image(
                        board_image_path,
                        board_entity
                    )
                except Exception as err:
                    self.log.error(f"第一次处理拼版图出错: {str(err)}")
                    self.log.error(traceback.format_exc())

                    try:
                        self.log.info(f"正在重新处理图片...")
                        board_image_path2 = os.path.splitext(board_image_path)[0] + "_mes2.jpg"
                        xutil.FileUtil.copy_file(
                            board_image_path,
                            board_image_path2
                        )
                        processed_filename = concat_ng_info_to_board_image(
                            board_image_path2,
                            board_entity
                        )

                    except Exception as err:
                        err_msg_list.append(f"第二次处理拼版{board_no}图片失败！err: {err}")
                        self.log.error(traceback.format_exc())

                        processed_filename = ""

                board_result = board_entity.get_repair_result("OK", "NG")
                if board_result == "OK":
                    dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{board_no}_{board_result}_{date1}{date2}.jpg"
                    self.log.info(f"board_result == OK 则 dst_filename : {dst_filename}")
                else:
                    dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{board_no}_{board_result}_{date1}{date2}_{only_one_ng_code}.jpg"
                    self.log.info(f"board_result == NG 则 dst_filename : {dst_filename}")

                upload_task = {
                    "endpoint_url": aws_endpoint_url,
                    "aws_access_key_id": aws_access_key_id,
                    "aws_secret_access_key": aws_secret_access_key,
                    "file_path": processed_filename,
                    "bucket_name": bucket_name_ui,
                    "object_key": dst_filename,
                    "region_name": region_name,
                }

                # 记录相关文件信息用于后续清理
                task_id = f"board_{board_no}"

                board_image_info[task_id] = {
                    'board_no': board_no,
                    'processed_filename': processed_filename,
                    'board_image_path': board_image_path,
                    'inspector_board_image': board_image_path.replace('_rect.jpg', '.jpg'),
                    "board_image_path2": board_image_path2
                }

                # 添加到线程池任务列表
                self.thread_pool.add_task(task_id, upload_image_to_s3, **upload_task)
                upload_tasks.append({
                    "task_id": task_id,
                    "task": upload_task
                })

            board_data_list.append({
                "smallPanelId": board_no,
                "smallPanelSN": barcode_pin,
                "fpcSn": barcode_pin,
                "smallPanelTestResult": board_entity.get_final_result("PASS", "PASS", "NG", "BadMark"),
                "smallPanelUrlList": [],  # 先置空，等上传完成后更新
                "extend1": "",
                "extend2": "",
                "extend3": "",
                "extend4": "",
                "postionList": upload_data_item_v3,
            })

        # 替换为新的处理逻辑
        # 只有在需要上传图片且有上传任务时才等待结果
        if is_upload_img == "Yes":
            for i in range(len(upload_tasks)):
                log.info(f"第{i + 1}次尝试去获取异步上传结果！")
                queue_ret = self.thread_pool.done_queue.get(timeout=5)
                log.info(f"queue ret: {queue_ret}")
                task_id = queue_ret[0]
                ret_url = queue_ret[1]
                # results = self.thread_pool.results
                # log.info(f"取出了几个结果：{len(results)}")

                info = board_image_info.get(task_id, {})

                if info:
                    board_no = info['board_no']

                    if ret_url:
                        # 上传成功
                        self.log.info(f"拼版{board_no}图片上传成功，URL为: {ret_url}")

                        # 更新board_data中的URL
                        for board_data in board_data_list:
                            if board_data["smallPanelId"] == board_no:
                                board_data["smallPanelUrlList"].append({"url": ret_url})
                                break

                        # 清理文件
                        xutil.FileUtil.remove_file(info['processed_filename'])
                        xutil.FileUtil.remove_file(info['board_image_path'])
                        xutil.FileUtil.remove_file(info['inspector_board_image'])

                        if info.get("board_image_path2"):
                            xutil.FileUtil.remove_file(info.get("board_image_path2"))

                        self.log.info(f"已删除相关图片文件")

            self.thread_pool.clear_cache()

        else:
            self.log.info("跳过图片上传等待，因为不需要上传图片或没有上传任务")

        if is_upload_img == "Yes":
            # 获取带标注的431整版图
            original_filename = pcb_entity.get_image_rect_431()
            pcb_result = pcb_entity.get_repair_result("OK", "NG")
            original_filename2 = ""

            if original_filename:
                if "/T_" in original_filename:
                    board_side = "TOP"
                else:
                    board_side = "BOT"

                try:
                    processed_filename = concat_ng_info_to_pcb_image(
                        original_filename,
                        ng_board_info
                    )

                except Exception as err:
                    self.log.error(f"初次处理整版图失败: {str(err)}")
                    self.log.error(traceback.format_exc())

                    try:
                        self.log.info(f"正在重新处理图片...")
                        original_filename2 = os.path.splitext(original_filename)[0] + "_mes2.jpg"
                        xutil.FileUtil.copy_file(
                            original_filename,
                            original_filename2
                        )
                        processed_filename = concat_ng_info_to_pcb_image(
                            original_filename2,
                            ng_board_info
                        )

                    except Exception as err:
                        self.log.error(f"再次处理整版图片失败: {str(err)}")
                        self.log.error(traceback.format_exc())
                        err_msg_list.append(f"处理整版图失败！err: {err}")

                        processed_filename = ""

                if pcb_result == "OK":
                    dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{pcb_result}_{date1}{date2}.jpg"
                    self.log.info(f"pcb_result == OK 则 dst_filename : {dst_filename}")
                else:
                    dst_filename = f"{date1}/{pcb_sn}/{pcb_sn}_{pcb_result}_{date1}{date2}_{only_one_ng_code}.jpg"
                    self.log.info(f"pcb_result == NG 则 dst_filename : {dst_filename}")

                try:
                    ret_url = upload_image_to_s3(
                        aws_endpoint_url,
                        aws_access_key_id,
                        aws_secret_access_key,
                        processed_filename,
                        bucket_name_ui,
                        dst_filename,
                        region_name,
                    )

                    if ret_url:
                        board_panel_url_list.append({
                            "url": ret_url
                        })

                        try:
                            xutil.FileUtil.remove_file(processed_filename)
                            xutil.FileUtil.remove_file(original_filename)
                            if original_filename2:
                                xutil.FileUtil.remove_file(original_filename2)

                            self.log.info(
                                f"成功删除带白板的整版图与原始整版图 :{processed_filename}: {original_filename}")
                        except Exception as del_err:
                            self.log.warning(
                                f"删除带白板的整版图与原始整版图失败: {processed_filename}: {original_filename}: {del_err}")

                except Exception as err:
                    err_msg_list.append(f"上传整板图失败，boardSide:{board_side}！err: {err}")

        data_param = {
            "deviceId": device_id,
            "stationId": station_select,
            "userId": user_id,
            "moNumber": mo_list,
            "pcbSN": pcb_sn,
            "carrierSN": "",
            "Result": pcb_entity.get_repair_result("PASS", "NG"),
            "bigPanelUrlList": board_panel_url_list,
            "dataList": board_data_list
        }

        log_str1 = f"接口URL：{api_url_data}  接口参数：\n{data_param}"
        xutil.FileUtil.write_request_log(log_save_path, log_str1)

        try:
            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            xutil.FileUtil.write_request_log(log_save_path, f"响应参数：{json.dumps(ret, ensure_ascii=False)}")

            if str(ret.get("code")) != "200":
                err_msg_list.append(f"mes接口异常，上传过站数据失败，error：{ret.get('msg')}")
        except Exception as err:
            err_msg = f"mes网络异常，error：{err}"
            xutil.FileUtil.write_request_log(log_save_path, err_msg)
            return self.x_response("false", err_msg)

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"MES响应异常，error：{err_str}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
        device_id = btn_vo.get_value_by_cons_key("device_id")
        user_id = btn_vo.get_value_by_cons_key("user_id")
        password = btn_vo.get_value_by_cons_key("password")
        log_save_path = btn_vo.get_value_by_cons_key("log_save_path", not_null=True)

        if btn_vo.get_btn_key() == 'login_btn':
            if x_res := refresh_token(api_url_login, user_id, password, device_id, log_save_path=log_save_path):
                return x_res

        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()

        api_url_factory = combo_vo.get_value_by_cons_key("api_url_factory")
        api_url_workstation = combo_vo.get_value_by_cons_key("api_url_workstation")
        api_url_line = combo_vo.get_value_by_cons_key("api_url_line")
        api_url_station = combo_vo.get_value_by_cons_key("api_url_station")
        api_url_mo = combo_vo.get_value_by_cons_key("api_url_mo")
        device_id = combo_vo.get_value_by_cons_key("device_id")
        user_id = combo_vo.get_value_by_cons_key("user_id")

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        if combo_key == "factory_list":
            get_factory_param = {
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_factory, get_factory_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_factory_li = ret.get("factoryList", [])

            if not ret_factory_li:
                return self.x_response("false", f"未获取到工厂列表！")

            factory_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_factory_li}

            xutil.CacheUtil.set("factory_map", factory_map)

            return self.x_response("true", json.dumps({
                "new_items": list(factory_map.keys())
            }))

        elif combo_key == "workstation_list":
            factory_select = getattr(main_window, f"combo_factory_list").currentText()
            factory_sn = xutil.CacheUtil.get("factory_map", {}).get(factory_select)

            if not factory_sn:
                return self.x_response("false", "未选中工厂，请先选中工厂再获取车间！")

            get_workstation_param = {
                "factorySN": factory_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_workstation, get_workstation_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_area_list = ret.get("areaList", [])

            if not ret_area_list:
                return self.x_response("false", f"未获取到车间列表！")

            workstation_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_area_list}

            xutil.CacheUtil.set("workstation_map", workstation_map)

            return self.x_response("true", json.dumps({
                "new_items": list(workstation_map.keys())
            }))

        elif combo_key == "line_list":
            workstation_select = getattr(main_window, f"combo_workstation_list").currentText()
            workstation_sn = xutil.CacheUtil.get("workstation_map", {}).get(workstation_select)

            if not workstation_sn:
                return self.x_response("false", "未选中车间，请先选中车间再获取线体！")

            get_workstation_param = {
                "areaSN": workstation_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_line, get_workstation_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_line_list = ret.get("lineList", [])

            if not ret_line_list:
                return self.x_response("false", f"未获取到线体列表！")

            line_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_line_list}

            xutil.CacheUtil.set("line_map", line_map)

            return self.x_response("true", json.dumps({
                "new_items": list(line_map.keys())
            }))

        elif combo_key == "station_list":
            line_select = getattr(main_window, f"combo_line_list").currentText()
            line_sn = xutil.CacheUtil.get("line_map", {}).get(line_select)

            if not line_sn:
                return self.x_response("false", "未选中线体，请先选中线体再获取工站！")

            get_station_param = {
                "lineSN": line_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_station, get_station_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_station_list = ret.get("stationList", [])

            if not ret_station_list:
                return self.x_response("false", f"未获取到工站列表！")

            station_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_station_list}

            xutil.CacheUtil.set("station_map", station_map)

            return self.x_response("true", json.dumps({
                "new_items": list(station_map.keys())
            }))

        elif combo_key == "mo_list":
            station_select = getattr(main_window, f"combo_station_list").currentText()
            station_sn = xutil.CacheUtil.get("station_map", {}).get(station_select)

            if not station_sn:
                return self.x_response("false", "未选中工站，请先选中工站再获取工单！")

            get_mo_param = {
                "stationId": station_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_mo, get_mo_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_mo_list = ret.get("moList", [])

            if not ret_mo_list:
                return self.x_response("false", f"未获取到工单列表！")

            mo_map = {i.get("moNumber"): i for i in ret_mo_list}

            xutil.CacheUtil.set("mo_map", mo_map)

            return self.x_response("true", json.dumps({
                "new_items": list(mo_map.keys())
            }))

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_status = other_vo.get_value_by_cons_key("api_url_status")
        device_sequence = other_vo.get_value_by_cons_key("device_sequence", to_int=True)
        device_id = other_vo.get_value_by_cons_key("device_id")

        if not global_data.get("is_login"):
            return self.x_response("false", f"未登录，请先登录！")

        status_code_v3 = other_vo.get_status_code_v3()
        x_request_device_status(api_url_status, status_code_v3, device_sequence, device_id)

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_status = other_vo.get_value_by_cons_key("api_url_status")
        device_sequence = other_vo.get_value_by_cons_key("device_sequence", to_int=True)
        device_id = other_vo.get_value_by_cons_key("device_id")

        status_code_v3 = global_data.get("last_device_code_v3")
        x_request_device_status(api_url_status, status_code_v3, device_sequence, device_id)


if __name__ == '__main__':
    #     ret_str1 = """<?xml version="1.0" encoding="utf-8"?>
    # <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    #                xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    #     <soap:Body>
    #         <GroupTestCommonResponse xmlns="WWW.SUNWODA.COM">
    #             <GroupTestCommonResult>string</GroupTestCommonResult>
    #         </GroupTestCommonResponse>
    #     </soap:Body>
    # </soap:Envelope>"""
    #
    #     root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    #     print(root1[0][0][0].text)
    # def fake_upload():
    #     print("start upload\n")
    #     time.sleep(0.2)
    #     print("end upload\n")
    #
    #
    # print("time cost", time.time() - t1)
    pass
