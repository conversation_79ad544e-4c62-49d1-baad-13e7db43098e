# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/14 下午2:38
# Author     ：sch
# version    ：python 3.8
# Description：安科讯
"""
import json
from typing import Any
from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "customer": ["安科讯", "ankexun"],
        "version": "release v1.0.0.17",
        "device": "AIS303-L",
        "feature": ["上传数据到MES，上传数据到AI服务器，从mes获取条码，mes上传失败停机报警"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-14 14:57  生成本地txt文档 
date: 2024-04-15 15:10  上传fov图后，需要删除本地的fov图
date: 2024-05-17 12:47  增加配置仅上传检测NG的fov图
date: 2024-08-22 15:19  增加【上传FOV信息超时时间】配置项
date: 2024-10-10 10:49  op_result接口增加接口超时时间 配置项
date: 2025-03-18 11:30  jira:37985 增加从mes接口获取条码列表功能
date: 2025-05-12 19:55  jira:37985 增加发送AOI和维修站复判数据到mes，mes上传失败停机报警开关
date: 2025-05-22 11:25  jira:39045 数据上传AI服务器，去除FOV数据上传，更改为AOI检测结果和复判结果数据
date: 2025-06-04 15:20  AI复判返回json更改为产线实际返回的格式；整板时上传sn增加-0;AI复判更改为按拼板发送
date: 2025-06-10 11:10  AI复判返回json还原为原来定义格式;失败停机也需要获取mesTransaction值；增加MES接口超时时间设置
date: 2025-06-11 10:10  AI数据发送接口中sn值改为拼板条码；增加MES获取条码接口超时时间设置选项；修复事务id板互串问题
date: 2025-06-27 11:45  jira:37985 器件数据angle/x/y填0；mes返回的message在界面里展示；配置项添加到其它参数里
date: 2025-06-27 18:23  回滚配置项添加到其它参数里，会导致维修站读取不到正确的AI配置值
""", }

    form = {
        # ---------------- AI参数 ---------------------
        "machine_id": {
            "ui_name": "【AI】线体",
            "value": ""
        },
        "station_ai": {
            "ui_name": "【AI】站点名称",
            "value": "",
        },
        # 注意：维修站会读取这个参数，key值不能改变
        "api_url_ai": {
            "ui_name": "【AI】接口URL",
            "value": ""
        },
        # ---------------- MES参数 ---------------------
        "device_id_mes": {
            "ui_name": "【MES】设备ID",
            "value": ""
        },
        "station_mes": {
            "ui_name": "【MES】站点名称",
            "value": "",
        },
        "user_id": {
            "ui_name": "【MES】UserID",
            "value": ""
        },
        "voucher": {
            "ui_name": "【MES】Voucher",
            "value": ""
        },
        "api_url_sn_mes": {
            "ui_name": "【MES】接口URL(获取条码)",
            "value": ""
        },
        "api_url_aoi_mes": {
            "ui_name": "【MES】接口URL(初判结果上传)",
            "value": ""
        },
        "api_url_review_mes": {
            "ui_name": "【MES】接口URL(复判结果上传)",
            "value": ""
        },
    }

    combo = {
        #  注意：维修站会读取这个参数，key值不能改变
        "get_mes_result": {
            "ui_name": "【AI】从AI服务器获取复判结果",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "upload_data_to_ai": {
            "ui_name": "【AI】上传数据到AI服务器",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        #  注意：维修站会读取这个参数，key值不能改变
        "api_timeout": {
            "ui_name": "【AI】获取结果超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "30", "60", "100", "300"],
            "value": "30",
        },
        "api_timeout_req": {
            "ui_name": "【AI】上传AOI数据超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "15", "20", "25", "30", "35", "60", "100", "300"],
            "value": "5",
        },
        "api_timeout_op": {
            "ui_name": "【AI】上传复判数据超时时间(s)",
            "item": ["1", "2", "3", "5", "10", "30", "60", "100", "300"],
            "value": "5",
        },
        "comp_ng_list1": {
            "ui_name": "【AI】上传检测结果范围",
            "item": ["全部", "仅检测NG"],
            "value": "仅检测NG",
        },
        "aoi_stop_warning": {
            "ui_name": "【MES】数据上传失败停机报警",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "api_timeout_sn_mes": {
            "ui_name": "【MES】获取条码超时时间(s)",
            "item": ["5", "10", "15", "20", "25", "30", "35", "60", "100", "300"],
            "value": "5",
        },
        "api_timeout_aoi_mes": {
            "ui_name": "【MES】上传AOI数据超时时间(s)",
            "item": ["5", "10", "15", "20", "25", "30", "35", "60", "100", "300"],
            "value": "5",
        },
        "api_timeout_review_mes": {
            "ui_name": "【MES】上传复判数据超时时间(s)",
            "item": ["5", "10", "15", "20", "25", "30", "35", "60", "100", "300"],
            "value": "5",
        },
    }

    def __init__(self):
        # 每个整板条码对应一个事务id
        self.mes_transaction_ids = {}
        self.main_window = None

    def _cache_mes_transaction_id(self, key, value):
        # 设定缓存上限100，一般100块板后最初的板一般都过站不会再测了
        max_cache_cnt = 100
        if len(self.mes_transaction_ids) >= max_cache_cnt:
            # 达到上限时移除最早的板
            key_list = list(self.mes_transaction_ids.keys())
            self.mes_transaction_ids.pop(key_list[0])

        # 如果key值已存在，则更新到尾部
        if key in self.mes_transaction_ids:
            self.mes_transaction_ids.pop(key)

        self.mes_transaction_ids[key] = value

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        """
        返回参数格式：
        {
        "Code": "0",
         success": "True",
        "msgType": "正常",
        "barcodeType": "工装条码",
        "barcode": {
        "1": "BT2541358944",
        "2": "BT2541358949"
        },
        "mesTransactionID": "C93333AE128F012852E52F486E44FDCF",
        "message": "工站[aoi]工装条码[118GZ439691-B-9]对应SN[BT2541358944, BT2541358949],总计[2]个!",
        "timestamp": "20250429165448336"
        }
        """
        device_id = other_vo.get_value_by_cons_key("device_id_mes")
        api_url_sn_mes = other_vo.get_value_by_cons_key("api_url_sn_mes", not_null=True)
        station = other_vo.get_value_by_cons_key("station_mes")
        user_id = other_vo.get_value_by_cons_key("user_id")
        voucher = other_vo.get_value_by_cons_key("voucher")
        api_timeout_sn_mes = other_vo.get_value_by_cons_key("api_timeout_sn_mes", to_int=True)

        pcb_sn = other_vo.get_pcb_sn()
        param = {
            "MachineID": device_id,
            "Barcode": pcb_sn,
            "station": station,
        }

        headers = {
            "UserID": user_id,
            "Voucher": voucher
        }

        ret = xrequest.RequestUtil.post_json(api_url_sn_mes, param, headers=headers, timeout=api_timeout_sn_mes)
        # 因为获取条码失败，后续还是会继续走检测流程，所以mesTransactionID值必须获取，否则后续流程会异常
        mes_transaction_id = ret.get('mesTransactionID')
        self._cache_mes_transaction_id(pcb_sn, mes_transaction_id)
        message = ret.get('message')
        if ret.get('Code') == '0':
            barcode_list = ret.get('barcode')
            ret_list = [barcode_list[str(i)] for i in range(1, len(barcode_list) + 1)]
            ret_list_str = ','.join(ret_list)
            self.main_window.log_info(f'***MES返回***：{message}')
            return self.x_response('true', ret_list_str)
        else:
            msg_type = ret.get('msgType')
            return self.x_response("false", f"入站检查失败，msgType={msg_type}，message={message}")

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", "条码检查失败，error: 未扫到条码！")

        return self.x_response()

    def _send_aoi_data_to_ai(self, data_vo: DataVo):
        self.log.info(f"------------传输AOI检测结果给AI服务器------------")
        api_url_ai = data_vo.get_value_by_cons_key("api_url_ai", not_null=True)
        station = data_vo.get_value_by_cons_key("station_ai")
        comp_ng_list1 = data_vo.get_value_by_cons_key("comp_ng_list1")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        api_timeout_req = data_vo.get_value_by_cons_key("api_timeout_req", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no
            comp_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_ng_list1 == "仅检测NG" and comp_entity.robot_result:
                    continue

                # 客户需求：小图回传a,x,y 数字统一上传为0，避免AI返回结果全部OK
                comp_list.append({
                    "board": int(board_no),
                    "part_name": comp_entity.designator,
                    "part_code": comp_entity.part,
                    # "a": comp_entity.geometry.angle,
                    # "x": comp_entity.geometry.cx,
                    # "y": comp_entity.geometry.cy,
                    "a": 0,
                    "x": 0,
                    "y": 0,
                    "w": comp_entity.geometry.width,
                    "h": comp_entity.geometry.height,
                    "image_body": str(comp_entity.image_path)  # 由于base64编码后数据很大，为了减少日志量，这里先缓存路径，等日志打印完后再转成base64
                })

            board_list.append({
                "line": machine_id,
                "station": station,
                "name": pcb_entity.project_name,
                "sn": board_entity.barcode,
                "part_list": comp_list,
            })

        err_msg_list = []
        try:
            for board_data in board_list:
                # 地址格式：http://127.0.0.1:1803/predict_data?sn=<sn>
                board_sn = board_data.get('sn')
                aoi_url = f"{api_url_ai}/predict_data"
                params = {'sn': board_sn}
                part_list = board_data['part_list']
                if part_list:
                    self.log.info("image_body因为转base64编码数据量太大，日志只输出路径，实际发送是base64编码！")
                self.log.info(f"请求url：{aoi_url}?sn={board_sn}，请求body: {json.dumps(board_data, indent=4)}")
                # 转base64编码放在日志输出后再放到参数里
                for comp in part_list:
                    image_path = comp['image_body']
                    if image_path:
                        image_body = xutil.ImageUtil.file_to_base64_content(image_path)
                        comp['image_body'] = image_body
                    else:
                        comp['image_body'] = ''
                ret = xrequest.RequestUtil.post_json(aoi_url, board_data, params=params, log_number=0,
                                                     timeout=api_timeout_req)
                if ret.get('error_code') != 0:
                    err_msg_list.append(f"AI接口返回出错：{ret.get('error_describe')}")
        except Exception as e:
            err_msg_list.append(f"本地网络出错，AOI数据未正常发送到AI服务器:{e}")

        ret_resp = self.x_response()
        if err_msg_list:
            ret_resp = self.x_response("false", "\n".join(err_msg_list))
        return ret_resp

    def _send_review_data_to_ai(self, data_vo: DataVo):
        self.log.info(f"------------传输复判结果给AI服务器------------")
        api_url_ai = data_vo.get_value_by_cons_key("api_url_ai", not_null=True)
        station = data_vo.get_value_by_cons_key("station_ai")
        machine_id = data_vo.get_value_by_cons_key("machine_id")
        api_timeout_op = data_vo.get_value_by_cons_key("api_timeout_op", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            comp_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_list.append({
                        "board": board_entity.board_no,
                        "part_name": comp_entity.designator,
                        "part_code": comp_entity.part,
                        "result": comp_entity.get_final_result("P", "P", "F"),
                    })

            board_list.append({
                "line": machine_id,
                "station": station,
                "name": pcb_entity.project_name,
                "sn": board_entity.barcode,
                "op_result": comp_list
            })

        err_msg_list = []
        try:
            for board_data in board_list:
                board_sn = board_data.get('sn')
                # 格式：http://127.0.0.1:1803/op_result?sn=C7H11820QMML73YAE882713F
                repair_url = f"{api_url_ai}/op_result"
                params = {'sn': board_sn}
                ret = xrequest.RequestUtil.post_json(repair_url, board_data, params=params, timeout=api_timeout_op)
                if ret.get('error_code') != 0:
                    err_msg_list.append(f"AI接口返回出错：{ret.get('error_describe')}")
        except Exception as e:
            err_msg_list.append(f"本地网络出错，复判数据未正常发送到AI服务器:{e}")

        ret_resp = self.x_response()
        if err_msg_list:
            ret_resp = self.x_response("false", "\n".join(err_msg_list))
        return ret_resp

    def _send_data_to_ai(self, data_vo: DataVo):
        inspect_type = data_vo.get_inspect_type()
        if inspect_type == 'inspector':
            ret_resp = self._send_aoi_data_to_ai(data_vo)
        else:
            ret_resp = self._send_review_data_to_ai(data_vo)
        return ret_resp

    def _send_aoi_data_to_mes(self, data_vo: DataVo):
        self.log.info(f"------------传输AOI检测结果给MES服务器------------")
        device_id = data_vo.get_value_by_cons_key("device_id_mes")
        station = data_vo.get_value_by_cons_key("station_mes")
        user_id = data_vo.get_value_by_cons_key("user_id")
        voucher = data_vo.get_value_by_cons_key("voucher")
        api_url_aoi_mes = data_vo.get_value_by_cons_key("api_url_aoi_mes", not_null=True)
        api_timeout_aoi_mes = data_vo.get_value_by_cons_key("api_timeout_aoi_mes", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        robot_ng_comp_count = 0
        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            robot_ng_comp_count += board_entity.comp_robot_ng_number

            comp_pass_list = []
            comp_ng_list = []
            for comp_entity in board_entity.yield_comp_entity():
                test_name = ''
                for alg in comp_entity.yield_alg_entity():
                    if comp_entity.robot_result:
                        if not test_name:
                            test_name = alg.test_name
                            break
                    else:
                        if alg.result == comp_entity.robot_ng_code:
                            test_name = alg.test_name
                            break

                comp_data = {
                    "PartName": comp_entity.type,
                    "Position": comp_entity.designator,
                    "Test_Item": test_name
                }
                if comp_entity.robot_result:
                    comp_pass_list.append(comp_data)
                else:
                    comp_data["Result"] = comp_entity.robot_ng_str
                    comp_ng_list.append(comp_data)

            defect_list = None if board_entity.robot_result else comp_ng_list
            pin_num = '1' if board_entity.board_no == '0' else board_entity.board_no
            board_list.append({
                "pinNum": pin_num,
                "pinBarcode": board_entity.barcode,
                "Qty": board_entity.comp_total_number,
                "TestQty": board_entity.comp_total_number,
                "PassQty": board_entity.comp_total_number - board_entity.comp_robot_ng_number,
                "NGQty": board_entity.comp_robot_ng_number,
                "Result": board_entity.get_robot_result("PASS", "NG"),
                "PassList": comp_pass_list,
                "DefectList": defect_list,
            })

        pcb_barcode = pcb_entity.pcb_barcode
        mes_transaction_id = self.mes_transaction_ids.get(pcb_barcode, '')
        aoi_data = {
            "MachineID": device_id,
            "mainBarcode": pcb_barcode,
            "mesTransactionID": mes_transaction_id,
            "station": station,
            "TestSecond": pcb_entity.get_cycle_time(),
            "TestStartTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1),
            "TestEndTime": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT1),
            "Operator": '',
            "ModelName": pcb_entity.project_name,
            "BoardSide": pcb_entity.board_side,
            "Qty": pcb_entity.comp_count,
            "TestQty": pcb_entity.comp_count,
            "PassQty": pcb_entity.comp_count - robot_ng_comp_count,
            "NGQty": robot_ng_comp_count,
            "Result": pcb_entity.get_robot_result("PASS", "NG"),
            "PinQty": pcb_entity.board_count,
            "pcbList": board_list,
        }

        headers = {
            "UserID": user_id,
            "Voucher": voucher
        }

        try:
            ret = xrequest.RequestUtil.post_json(api_url_aoi_mes, aoi_data, headers=headers,
                                                 timeout=api_timeout_aoi_mes)
            message = ret.get('message')
            if ret.get('Code') != '0':
                err_msg = f"msgType={ret.get('msgType')}，message={message}"
                return self.x_response("false", f"mes返回错误信息:{err_msg}")
            else:
                self.main_window.log_info(f'***MES返回***：{message}')
                return self.x_response()
        except Exception as e:
            return self.x_response("false", f"本地网络出错，数据未正常发送到MES:{e}")

    def _send_review_data_to_mes(self, data_vo: DataVo):
        self.log.info(f"------------传输复判结果给MES服务器------------")
        device_id = data_vo.get_value_by_cons_key("device_id_mes")
        station = data_vo.get_value_by_cons_key("station_mes")
        user_id = data_vo.get_value_by_cons_key("user_id")
        voucher = data_vo.get_value_by_cons_key("voucher")
        api_url_review_mes = data_vo.get_value_by_cons_key("api_url_review_mes", not_null=True)
        api_timeout_review_mes = data_vo.get_value_by_cons_key("api_timeout_review_mes", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        robot_ng_comp_count = 0
        review_ng_comp_count = 0
        board_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            robot_ng_comp_count += board_entity.comp_robot_ng_number
            review_ng_comp_count += board_entity.comp_repair_ng_number

            comp_review_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_result:
                    continue

                test_name = ''
                for alg in comp_entity.yield_alg_entity():
                    if comp_entity.repair_result:
                        if not test_name:
                            test_name = alg.test_name
                            break
                    else:
                        if alg.result == comp_entity.repair_ng_code:
                            test_name = alg.test_name
                            break

                recheck_result = 'PASS' if comp_entity.repair_result else comp_entity.repair_ng_str
                comp_review_list.append({
                    "PartName": comp_entity.type,
                    "Position": comp_entity.designator,
                    "Test_Item": test_name,
                    "PreliminaryCheckNGResult": comp_entity.robot_ng_str,
                    "RecheckResult": recheck_result
                })

            recheck_list = None if board_entity.repair_result else comp_review_list
            pin_num = '1' if board_entity.board_no == '0' else board_entity.board_no
            board_list.append({
                "pinNum": pin_num,
                "pinBarcode": board_entity.barcode,
                "PreliminaryCheckNGQty": board_entity.comp_robot_ng_number,
                "RecheckQty": board_entity.comp_robot_ng_number,
                "RecheckPassQty": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "DefectQty": board_entity.comp_repair_ng_number,
                "Result": board_entity.get_repair_result("PASS", "NG"),
                "RecheckList": recheck_list
            })

        pcb_barcode = pcb_entity.pcb_barcode
        mes_transaction_id = self.mes_transaction_ids.get(pcb_barcode, '')
        operator = pcb_entity.repair_user
        review_data = {
            "MachineID": device_id,
            "mainBarcode": pcb_barcode,
            "mesTransactionID": mes_transaction_id,
            "station": station,
            "TestSecond": pcb_entity.get_cycle_time(),
            "TestStartTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1),
            "TestEndTime": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT1),
            "Operator": operator,
            "ModelName": pcb_entity.project_name,
            "BoardSide": pcb_entity.board_side,
            "PreliminaryCheckNGQty": robot_ng_comp_count,
            "RecheckQty": robot_ng_comp_count,
            "RecheckPassQty": robot_ng_comp_count - review_ng_comp_count,
            "DefectQty": review_ng_comp_count,
            "Result": pcb_entity.get_repair_result("PASS", "NG"),
            "PinQty": pcb_entity.board_count,
            "pcbList": board_list,
        }

        headers = {
            "UserID": user_id,
            "Voucher": voucher
        }
        try:
            ret = xrequest.RequestUtil.post_json(api_url_review_mes, review_data, headers=headers,
                                                 timeout=api_timeout_review_mes)
            message = ret.get('message')
            if ret.get('Code') != '0':
                err_msg = f"msgType={ret.get('msgType')}，message={message}"
                return self.x_response("false", f"mes返回错误信息:{err_msg}")
            else:
                self.main_window.log_info(f'***MES返回***：{message}')
                return self.x_response()
        except Exception as e:
            return self.x_response("false", f"本地网络出错，数据未正常发送到MES:{e}")

    def _send_data_to_mes(self, data_vo: DataVo):
        inspect_type = data_vo.get_inspect_type()
        if inspect_type == 'inspector':
            ret_resp = self._send_aoi_data_to_mes(data_vo)
        else:
            ret_resp = self._send_review_data_to_mes(data_vo)
        return ret_resp

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        get_mes_result = data_vo.get_value_by_cons_key("get_mes_result")
        upload_data_to_ai = data_vo.get_value_by_cons_key("upload_data_to_ai")
        aoi_stop_warning = data_vo.get_value_by_cons_key("aoi_stop_warning")

        err_list = []

        # 上传结果到AI服务器
        # 【从AI服务器获取复判结果】设置项是给维修站使用，如果配置为Yes，则配置器必须上传数据到AI服务器，否则维修站会出错。
        if get_mes_result == "Yes" or upload_data_to_ai == "Yes":
            ret_resp = self._send_data_to_ai(data_vo)
            if not ret_resp.get('result'):
                err_list.append(ret_resp.get('string'))
        else:
            self.log.info(f"上传开关为关，无需上传数据到AI服务器！")

        # 上传结果到MES
        ret_resp = self._send_data_to_mes(data_vo)
        if not ret_resp.get('result'):
            err_msg = ret_resp.get('string')
            err_list.append(err_msg)
            # MES返回错误时停机报警
            if aoi_stop_warning == 'Yes':
                try:
                    self.log.info(f"MES返回错误，向主软件发送停机报警！")
                    xrequest.send_device_start_or_stop("0", '1', err_msg)
                except Exception as e:
                    self.log.info(f"网络异常，无法正常发送停机报警到主软件：{e}")

        if err_list:
            return self.x_response("false", "\n".join(err_list))
        else:
            return self.x_response()
