# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/01/22 14:23
# Author     ：chencb
# version    ：python 3.8
# Description：长沙健科 https://jira.cvte.com/browse/ATAOI_2019-36519
"""
import copy
import json
import locale
import os
import random
import subprocess
import threading
from typing import Any
from common import xcons, xutil, xrequest
from common.xconfig import home_dir
from entity.MesEntity import PcbEntity
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
import re
import time
from datetime import datetime, timedelta

# 发送到哪
SEND_TO = 'send_to'
SEND_TO_LOCAL = 'local'
SEND_TO_MES = 'mes'
SEND_TO_JSON_FTP = 'json_ftp'
SEND_TO_PIC_FTP = 'pic_ftp'
# 数据列表信息
DATA_LIST = 'data_list'
DATA_DEST_FILE_PATH = 'dest_file_path'
DATA_RAW = 'raw_data'
# 保存的是整板还是单板数据
DATA_SRC = 'data_src'
DATA_SRC_PANEL = 'panel'
DATA_SRC_BOARD = 'board'

# 上传失败缓存目录以及缓存信息文件
FAIL_CACHE_DIR = f"{home_dir}/.aoi/mes_fail_cache"
FAIL_DATA_INFO_FILE = f'{FAIL_CACHE_DIR}/fail_data_info.json'
# 设备信息上传时间记录
DEVICE_INFO_TIME_FILE = f'{FAIL_CACHE_DIR}/device_info_time.json'


# 定义一个数据结构模版，用于_send_data函数、或者失败数据缓存fail_data_cache时统一数据结构，方便各个场景间数据传递
# send_data_template = {
#     SEND_TO:'', # local、mes、json_ftp、pic_ftp
#     DATA_LIST:[],  # 要发送的数据列表，每个项都是独立要发送的数据信息，其数据结构如下：
#       {
#           DATA_DEST_FILE_PATH: '', # 要发送到目标文件路径，如果是mes则为发送失败时保存到本地路径
#           DATA_RAW: None, # 原数据，为json数据 或者 图片文件路径
#       }
# }

class Engine(ErrorMapEngine):
    version = {
        "customer": ["长沙健科", "changshajianke"],
        "version": "release v1.0.0.12",
        "device": "AIS501",
        "feature": ["条码校验", "json发送到mes", "json和不良图片发送到ftp", "json保存到本地", "一键重传", "发送设备状态"],
        "author": "chenchongbing",
        "release": """
date: 2025-02-05 10:30  jira:36519 条码校验、json发送到mes、json和不良图片发送到ftp、json保存到本地、一键重传
date: 2025-04-18 10:00  jira:36519 增加发送设备状态，包括开关机、告警、空闲（第一版，不包含缓存上次记录以及告警弹窗）
date: 2025-04-18 17:23  jira:36519 增加获取系统开关机时间、缓存上次开始结束时间和发送信息时读取缓存、增加设备故障提醒窗口和关闭窗口上传故障信息
date: 2025-04-19 10:35  BUGFIX:跨天后获取系统关机时间失败，原因是跨天后系统日志文件被轮换了，增加所有日志文件查询
date: 2025-04-24 12:10  jira:36519 变更需求：故障信息结束时间从原来弹窗改为开始检测，alarm_type改为故障描述
date: 2025-04-27 12:00  BUGFIX:没有创建说缓存目录mes_fail_cache时缓存设备状态信息会失败
date: 2025-04-29 22:36  优化关机时间获取，通过last命令行形式，包含正常关机和重启
date: 2025-05-01 09:36  优化：1>开机后先上传故障/停机，再上传开机；2>故障/停机发生时上传一次，结束后也再传一次；2>更改停机逻辑，软件启动、发送故障、停止检查5分钟后机器没有动作则视为停机
date: 2025-05-08 11:03  BUGFIX:解决关机时间因为多个空格获取失败问题；优化：故障未结束又发生故障时，上一个故障当做结束；设备空转也加入停机状态;安全门不加入故障
date: 2025-05-08 16:00  优化：去除故障发生时如果5分钟设备无动作进入停机状态
date: 2025-05-08 18:13  优化：如果故障停机导致的停止检查，不计入停机状态；空转状态改为5分钟计时
""", }

    other_combo = {
        "is_upload_mes": {
            "ui_name": "上传json数据到Mes服务器",
            "item": [
                "是",
                "否"
            ],
            "value": "是"
        },
        "is_upload_ftp": {
            "ui_name": "上传数据到ftp服务器",
            "item": [
                "是",
                "否"
            ],
            "value": "是"
        },
        "ftp_encoding": {
            "ui_name": "FTP编码",
            "item": [
                "utf-8",
                "gbk"
            ],
            "value": "gbk"
        },
    }

    other_form = {
        "json_ftp_host": {
            "ui_name": "FTP Host（JSON）",
            "value": ""
        },
        "json_ftp_port": {
            "ui_name": "FTP Port（JSON）",
            "value": "21"
        },
        "json_ftp_user": {
            "ui_name": "FTP账号（JSON）",
            "value": ""
        },
        "json_ftp_password": {
            "ui_name": "FTP密码（JSON）",
            "value": ""
        },
        "json_ftp_path": {
            "ui_name": "FTP根路径（JSON）",
            "value": ""
        },
        "pic_ftp_host": {
            "ui_name": "FTP Host（图片）",
            "value": ""
        },
        "pic_ftp_port": {
            "ui_name": "FTP Port（图片）",
            "value": "21"
        },
        "pic_ftp_user": {
            "ui_name": "FTP账号（图片）",
            "value": ""
        },
        "pic_ftp_password": {
            "ui_name": "FTP密码（图片）",
            "value": ""
        },
        "pic_ftp_path": {
            "ui_name": "FTP根路径（图片）",
            "value": ""
        },
    }

    password_style = ["json_ftp_password", 'pic_ftp_password']

    combo = {
        "is_save_local": {
            "ui_name": "保存数据到本地",
            "item": [
                "是",
                "否",
            ],
            "value": "否"
        },
        "save_type": {
            "ui_name": "文件生成方式",
            "item": [
                "整板生成",
                "拼板生成",
            ],
            "value": "整板生成"
        },
        "panel_filename": {
            "ui_name": "整板文件名格式",
            "item": [
                "时间_整板条码",
                "整板条码_时间",
                "整板条码",
                "时间",
            ],
            "value": "整板条码_时间"
        },
        "board_filename": {
            "ui_name": "拼板文件名格式",
            "item": [
                '时间_拼板条码',
                '拼板条码_时间',
                '拼板条码',
                '时间_拼板条码_拼板序号',
                '时间_拼板序号',
                '拼板序号_时间',
            ],
            "value": "拼板条码_时间"
        },
        "save_path_type": {
            "ui_name": "保存数据路径格式",
            "item": [
                '数据路径',
                '数据路径/日期(yyyymmdd)',
                '数据路径/设备名称',
                '数据路径/设备名称/日期(yyyymmdd)',
                '数据路径/日期(yyyymmdd)/设备名称',
            ],
            "value": "数据路径/设备名称/日期(yyyymmdd)"
        },
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "全部"
        },
        "newline_type": {
            "ui_name": "换行符格式",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        },
    }

    path = {
        "save_path": {
            "ui_name": "数据路径",
            "value": ""
        },
    }

    form = {
        "biill_no": {
            "ui_name": "生产工单",
            "value": ""
        },
        "device_name": {
            "ui_name": "设备编号",
            "value": ""
        },
        "sn_api_url": {
            "ui_name": "条码校验接口地址",
            "value": ""
        },
        "mes_api_url": {
            "ui_name": "MES接口地址",
            "value": ""
        },
        "status_api_url": {
            "ui_name": "设备信息上传接口地址",
            "value": ""
        },

    }

    button = {
        "upload_fail_data": {
            "ui_name": "一键重传"
        },
    }

    def __init__(self):
        # 这两个变量后续在发送数据过程中要使用，统一在这里进行定义和初始化
        self.other_vo = None
        self.fail_data_info = []
        # 设备停机检测定时器以及停机信息记录
        self.machine_stop_timer = None
        self.machine_stop_info = {}
        # 故障信息记录
        self.alarm_info = {}
        # 创建一个锁对象，用于处理多个timer执行写文件
        self.file_lock = threading.Lock()
        # 记录系统关机时间
        self.shutdown_time = ''

    def on_exit(self):
        # 关闭配置器则当做故障或者空闲结束，直接上传设备信息
        if self.machine_stop_info:
            self._send_machine_stop_info(is_end=True, is_sync=True)
        if self.alarm_info:
            self._send_alarm_info(is_end=True, is_sync=True)

    def init_main_window(self, main_window, other_vo: OtherVo):
        # 后续所有配置参数获取都统一从这获取
        self.other_vo = other_vo
        self.main_window = main_window
        # 启动后发送需要发送的设备信息
        self._send_cache_device_info()
        # 应用刚启动5分钟之后，如果还未开始检测，则视为停用
        self._machine_stop_detect()

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        sn_api_url = other_vo.get_value_by_cons_key("sn_api_url", not_null=True)
        device_name = other_vo.get_value_by_cons_key("device_name")

        for sn in other_vo.list_sn():
            param = {
                "device_name": device_name,
                "pcb_sn": sn,
            }

            ret = xrequest.RequestUtil.post_json(sn_api_url, param)
            # 返回编号,200 允许过站,400 不允许过站
            code = ret.get("code")
            if code == 400:
                # 条码校验失败停机报警
                return self.x_response("false", f"条码校验失败，error：{ret.get('message')}")
            elif code == 200:
                self.log.warning(f"整板请求一次接口做校验即可")
                return self.x_response()
            else:
                return self.x_response("false", f"返回的编号无法识别（编号：{code}，非200和400）")

        return self.x_response()

    def _append_fail_data(self, **kwargs):
        """
        把失败的数据保存到本地，同时记录到缓存信息中，等所有任务完成后，最终保存到本地FAIL_DATA_INFO_FILE文件中
        kwargs包含：
        'is_re_uploadis_re_upload':是否是一键重传，如果是重传，失败时不用再保存文件，只更新缓存信息fail_data_cache
        ‘fail_data’： 要缓存的失败数据，数据结构参看send_data_template：
            {
            SEND_TO:'', # mes、json_ftp、pic_ftp
            DATA_LIST:[],  # 要保存的数据列表，每个data的数据结构如下：
                {
                DATA_DEST_FILE_PATH: '', # 要发送的远程文件路径，如果是MES则为失败要保存的路径
                DATA_RAW: None, # 原始数据，json或者不良图片路径
                }
        """
        newline_type = self.other_vo.get_value_by_cons_key("newline_type")
        is_re_upload = kwargs.get('is_re_upload', False)
        fail_data = kwargs.get('fail_data', None)

        # 没有需要缓存的数据直接返回
        if not fail_data:
            return

        if is_re_upload:
            # 如果是一键重传时失败的，则不用转换，直接按原数据继续保存即可
            self.fail_data_info.append(fail_data)
            return

        # 先创建失败缓存目录：
        xutil.FileUtil.ensure_dir_exist(FAIL_CACHE_DIR)

        send_to = fail_data.get(SEND_TO)
        data_list = fail_data.get(DATA_LIST)
        save_data_list = []
        for data in data_list:
            dst_filepath = data.get(DATA_DEST_FILE_PATH)
            data_raw = data.get(DATA_RAW)
            save_filepath = ''
            # 非一键重传，json数据先保存到本地
            if send_to in (SEND_TO_MES, SEND_TO_JSON_FTP):
                if send_to == SEND_TO_JSON_FTP:
                    # ftp的话DATA_DEST_FILE_PATH记录的是要上传到ftp的文件路径，需先转成本地保存路径
                    filename = os.path.basename(dst_filepath)
                    # ftp的json用ftp来标记，简化后面一键重传成功后删除本地文件时不影响mes
                    save_filepath = f'{FAIL_CACHE_DIR}/ftp_{filename}'
                elif send_to == SEND_TO_MES:
                    # mes记录的已经是失败要保存的本地路径
                    save_filepath = dst_filepath

                # 如果此文件已存在，则不用再保存
                if not os.path.exists(save_filepath):
                    data_str = json.dumps(data_raw, indent=4, ensure_ascii=False)
                    if newline_type == 'window':
                        data_str = data_str.replace('\n', '\r\n')
                    xutil.FileUtil.write_content_to_file(save_filepath, data_str)

            # 生成data_list数据
            data_item = {}
            if send_to == SEND_TO_MES:
                data_item[DATA_DEST_FILE_PATH] = ''  # 一键重传时MES直接发送接口，不需要指定路径
                data_item[DATA_RAW] = save_filepath  # 一键重传时需要读取的本地文件路径
            elif send_to == SEND_TO_JSON_FTP:
                data_item[DATA_DEST_FILE_PATH] = dst_filepath  # 一键重传时上传到ftp的文件路径
                data_item[DATA_RAW] = save_filepath  # 一键重传时需要读取的本地文件路径
            elif send_to == SEND_TO_PIC_FTP:
                data_item[DATA_DEST_FILE_PATH] = dst_filepath  # 一键重传时上传到ftp的图片路径
                data_item[DATA_RAW] = data_raw  # 一键重传时需要读取的本地图片路径，图片时DATA_RAW本身就是图片路径
            save_data_list.append(data_item)

        # 创建一个SEND_TO记录添加到fail_data_info
        self.fail_data_info.append({
            SEND_TO: send_to,
            DATA_LIST: save_data_list
        })

    def _upload_fail_data(self):
        """
        点击一键重传按钮时，重传失败缓存的数据。根据fail_data_info.json记录的信息进行对应上传数据逻辑。
        """
        is_upload_mes = self.other_vo.get_value_by_cons_key("is_upload_mes")
        is_upload_ftp = self.other_vo.get_value_by_cons_key("is_upload_ftp")
        self.fail_data_info = []

        # 如果没有缓存文件则无失败文件，返回提示信息
        if not os.path.exists(FAIL_DATA_INFO_FILE):
            return self.x_response("false", f"没有需要重新上传的失败文件")

        fail_data_info = xutil.FileUtil.load_json_file(FAIL_DATA_INFO_FILE)
        response = self.x_response()
        err_msg_list = []
        for send_data in fail_data_info:
            send_to = send_data.get(SEND_TO)
            if ((send_to == SEND_TO_MES and is_upload_mes == '否') or
                    ((send_to == SEND_TO_JSON_FTP or send_to == SEND_TO_PIC_FTP) and is_upload_ftp == '否')):
                # 如果对应上传开关是关闭的，则错误缓存信息需要继续缓存
                self.fail_data_info.append(send_data)
                err_msg = '上传MES服务器开关为关闭' if send_to == SEND_TO_MES else '上传FTP服务器开关为关闭'
                err_msg_list.append(err_msg)
                continue

            response = self._send_data(send_data, True)
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 如果重传后还有失败的，则再次保存下失败信息
        if self.fail_data_info:
            self._save_fail_data_to_file(True)
        else:
            # 否则表示已上传成功，删除缓存信息文件
            os.remove(FAIL_DATA_INFO_FILE)

        # 把所有错误信息组装起来发送
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"一键重传失败：{err_str}")
        return response

    def _save_fail_data_to_file(self, is_re_upload: bool = False):
        """
        每次发送mes数据过程中有新的失败数据产生时，数据发送结束后需追加到原有的fail_data_info.json文件中
        """
        if not self.fail_data_info:
            return

        # 没有记录过fail_data_info，则直接写入
        if not os.path.exists(FAIL_DATA_INFO_FILE):
            data_str = json.dumps(self.fail_data_info, indent=4, ensure_ascii=False)
            xutil.FileUtil.write_content_to_file(FAIL_DATA_INFO_FILE, data_str)
            self.fail_data_info = []
            return

        if is_re_upload:
            # 如果是一键重传的失败，则直接保存新的fail_data_info即可，不需追加到原有fail_data_info.json中
            cached_fail_data_info = self.fail_data_info
        else:
            # 读取原有fail_data_info，并把新的数据追加到原有的记录里
            cached_fail_data_info = xutil.FileUtil.load_json_file2(FAIL_DATA_INFO_FILE)
            for new_fail_data in self.fail_data_info:
                send_to = new_fail_data.get(SEND_TO)
                new_data_list = new_fail_data.get(DATA_LIST)
                data_list = []
                # 每个SEND_TO都只缓存在一份数据，
                for cached_fail_data in cached_fail_data_info:
                    # 已有记录，则更新原有的data_list即可
                    if cached_fail_data.get(SEND_TO) == send_to:
                        data_list = cached_fail_data.get(DATA_LIST)
                        break

                if data_list:
                    # 已记录过SEND_TO，则在原有data_list里更新
                    for new_data in new_data_list:
                        exists = False
                        # 查找原有列表里是不是已有记录，若有则不用重复添加
                        for data in data_list:
                            if new_data == data:
                                exists = True
                                break

                        if not exists:
                            data_list.append(new_data)
                else:
                    # 没记录过，则全新创建一个新的SEND_TO信息添加到列表
                    cached_fail_data_info.append({
                        SEND_TO: send_to,
                        DATA_LIST: new_data_list
                    })

        # 把追加后的cached_fail_data_info保存到本地，并清空fail_data_info
        data_str = json.dumps(cached_fail_data_info, indent=4, ensure_ascii=False)
        xutil.FileUtil.write_content_to_file(FAIL_DATA_INFO_FILE, data_str)
        self.fail_data_info = []

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        response = self.x_response()
        if btn_key == "upload_fail_data":
            response = self._upload_fail_data()
        return response

    def _get_send_to_file_path(self, **kwargs):
        """
        根据可变参数生成对应需要发送的文件完整路径
        参数包含：
            send_to：local、mes、json_ftp、pic_ftp  --- 必有
            pcb_start_time: 板的检测开始时间，字符串格式FMT_TIME_FILE --- 必有
            data_src：panel、board  -- 上传ng图片(pic_ftp)时不需要
            pcb_sn：条码  -- 必有
            board_sn、board_no：单板信息  -- 单板board时才有
            comp_designator、comp_part、comp_user_code、comp_user_result、comp_image：NG图片信息  -- 上传ng图片(pic_ftp)时才有
        """
        save_path = self.other_vo.get_value_by_cons_key("save_path", not_null=True)
        save_path_type = self.other_vo.get_value_by_cons_key("save_path_type")
        panel_filename = self.other_vo.get_value_by_cons_key("panel_filename")
        board_filename = self.other_vo.get_value_by_cons_key("board_filename")
        device_name = self.other_vo.get_value_by_cons_key("device_name")
        json_ftp_path = self.other_vo.get_value_by_cons_key("json_ftp_path")
        pic_ftp_path = self.other_vo.get_value_by_cons_key("pic_ftp_path")
        time_str = kwargs.get('pcb_start_time')
        time_date = time_str[:8]

        # 生成目标目录名
        dst_path = ''
        if kwargs.get(SEND_TO) == SEND_TO_MES:
            # MES填写失败路径，用于发送失败时保存到本地时使用
            dst_path = f"{FAIL_CACHE_DIR}"
        elif kwargs.get(SEND_TO) == SEND_TO_LOCAL:
            if save_path_type == "数据路径":
                dst_path = f"{save_path}"
            elif save_path_type == "数据路径/日期(yyyymmdd)":
                dst_path = f"{save_path}/{time_date}"
            elif save_path_type == "数据路径/设备名称":
                dst_path = f"{save_path}/{device_name}"
            elif save_path_type == "数据路径/设备名称/日期(yyyymmdd)":
                dst_path = f"{save_path}/{device_name}/{time_date}"
            elif save_path_type == "数据路径/日期(yyyymmdd)/设备名称":
                dst_path = f"{save_path}/{time_date}/{device_name}"
        elif kwargs.get('send_to') == SEND_TO_JSON_FTP:
            # 存储路径：设备名称\json\日期
            dst_path = f"{json_ftp_path}/{device_name}/json/{time_date}"
        elif kwargs.get('send_to') == SEND_TO_PIC_FTP:
            # 存储路径：设备名称\image\日期
            dst_path = f"{pic_ftp_path}/{device_name}/image/{time_date}"

        # 生成目标文件名
        dst_filename = ''
        # ftp的json文件名以及MES发送失败时保存到本地文件名取值和本地保存的一样
        if kwargs.get(SEND_TO) in (SEND_TO_LOCAL, SEND_TO_JSON_FTP, SEND_TO_MES):
            # 生成整板文件名
            if kwargs.get(DATA_SRC) == DATA_SRC_PANEL:
                pcb_sn = kwargs.get('pcb_sn')
                if panel_filename == "时间_整板条码":
                    dst_filename = f"{time_str}_{pcb_sn}"
                elif panel_filename == "整板条码_时间":
                    dst_filename = f"{pcb_sn}_{time_str}"
                elif panel_filename == "整板条码":
                    dst_filename = f"{pcb_sn}"
                elif panel_filename == "时间":
                    dst_filename = f"{time_str}"
            # 生成单板文件名
            elif kwargs.get(DATA_SRC) == DATA_SRC_BOARD:
                board_sn = kwargs.get('board_sn')
                board_no = kwargs.get('board_no')
                if board_filename == "时间_拼板条码":
                    dst_filename = f"{time_str}_{board_sn}"
                elif board_filename == "拼板条码_时间":
                    dst_filename = f"{board_sn}_{time_str}"
                elif board_filename == "拼板条码":
                    dst_filename = f"{board_sn}"
                elif board_filename == "时间_拼板条码_拼板序号":
                    dst_filename = f"{time_str}_{board_sn}_{board_no}"
                elif board_filename == "时间_拼板序号":
                    dst_filename = f"{time_str}_{board_no}"
                elif board_filename == "拼板序号_时间":
                    dst_filename = f"{board_no}_{time_str}"

            dst_filename = f"{dst_filename}.json"

        elif kwargs.get(SEND_TO) == SEND_TO_PIC_FTP:
            pcb_sn = kwargs.get('pcb_sn')  # 条码
            comp_designator = kwargs.get('comp_designator')  # 位号
            comp_part = kwargs.get('comp_part')  # 料号
            comp_user_code = kwargs.get('comp_user_code')  # 器件复判不良代码
            comp_user_result = kwargs.get('comp_user_result')  # 器件复判结果
            comp_image = kwargs.get('comp_image')  # 器件图片路径
            suffix = xutil.FileUtil.get_file_suffix(comp_image)
            # 命名方式:条码-位号-料号-不良代码-结果.jpg，其中的信息如没有留空即可
            dst_filename = f"{pcb_sn}-{comp_designator}-{comp_part}-{comp_user_code}-{comp_user_result}{suffix}"

        dst_filepath = f"{dst_path}/{dst_filename}"
        return dst_filepath

    def _save_json_to_local(self, data: dict):
        """
        保存json数据到本地指定的路径
        data 数据结构：{
        DATA_DEST_FILE_PATH: '',  # 要存储的本地文件路径
        DATA_RAW: {},  # 要存储的json数据
        }
        """
        newline_type = self.other_vo.get_value_by_cons_key("newline_type")

        try:
            dst_filepath = data.get(DATA_DEST_FILE_PATH)
            # 目录不存在时先创建
            dst_dir = os.path.dirname(dst_filepath)
            xutil.FileUtil.ensure_dir_exist(dst_dir)

            data_str = json.dumps(data.get(DATA_RAW), indent=4, ensure_ascii=False)
            if newline_type == 'window':
                data_str = data_str.replace('\n', '\r\n')
            xutil.FileUtil.write_content_to_file(dst_filepath, data_str)
        except Exception as e:
            return self.x_response("false", f"本地保存json异常，异常信息：{e}")

        return self.x_response()

    def _send_json_to_mes(self, data: dict, is_re_upload: bool = False):
        """
        发送json数据到mes，发送失败时需缓存到本地供一键重传使用
        data 数据结构：{
        DATA_DEST_FILE_PATH: '',  # 这里放置MES发送失败时保存到本地的文件路径
        DATA_RAW: {},  # 要发送的json数据
        }
        is_re_upload: 是否是一键重传发送，如果是还需要执行上传成功后删除缓存文件操作
        """
        mes_api_url = self.other_vo.get_value_by_cons_key("mes_api_url")

        is_save_json = False
        response = self.x_response()
        try:
            data_raw = data.get(DATA_RAW)
            json_filepath = ''
            if is_re_upload:
                # 如果是一键重传，data_raw是json文件路径，需要先读取json内容
                json_filepath = data_raw
                data_raw = xutil.FileUtil.load_json_file(data_raw)

            ret = xrequest.RequestUtil.post_json(mes_api_url, data_raw)
            if str(ret.get("code")) == "200":
                # 上传成功后需要删除本地json缓存文件
                if is_re_upload:
                    os.remove(json_filepath)
                    self.log.info(f"ftp json数据上传成功，缓存json数据已删除！")
            else:
                response = self.x_response("false", f"mes接口发送数据出错，error：{ret.get('message')}")
                is_save_json = True
        except Exception as e:
            response = self.x_response("false", f"mes接口发送数据出错，error：{e}")
            is_save_json = True

        # 失败需保存json文件到临时目录，供下次一键重传使用,传递的参数参看send_data_template模版说明
        if is_save_json:
            data_list = [data]
            fail_data = {
                SEND_TO: SEND_TO_MES,
                DATA_LIST: data_list,
            }
            self._append_fail_data(fail_data=fail_data, is_re_upload=is_re_upload)

        return response

    def _send_json_to_ftp(self, data_list: list, is_re_upload: bool = False):
        """
        发送所有json数据到指定的ftp文件路径，发送失败时需缓存到本地供一键重传使用
        data_list:要发送的json数据列表，参看send_data_template的DATA_LIST数据结构说明：
            {
            DATA_DEST_FILE_PATH: '',  # 发送到ftp的文件路径
            DATA_RAW: {},  # 要发送的json数据
            }
        is_re_upload: 是否是一键重传发送，如果是还需要执行上传成功后删除json缓存文件操作
        """
        response = self.x_response()
        ftp_client = None
        # 拷贝一份，用于异常失败时记录需要保存的json列表
        fail_json_list = copy.deepcopy(data_list)
        try:
            newline_type = self.other_vo.get_value_by_cons_key("newline_type")
            ftp_encoding = self.other_vo.get_value_by_cons_key("ftp_encoding")
            ftp_host = self.other_vo.get_value_by_cons_key("json_ftp_host")
            ftp_port = self.other_vo.get_value_by_cons_key("json_ftp_port", to_int=True)
            ftp_user = self.other_vo.get_value_by_cons_key("json_ftp_user")
            ftp_password = self.other_vo.get_value_by_cons_key("json_ftp_password")

            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port, encoding=ftp_encoding)
            resp = ftp_client.login()
            # 230 登录成功
            if '230' in resp:
                err_file_list = []
                upload_path = ''
                for send_data in data_list:
                    dest_filepath = send_data.get(DATA_DEST_FILE_PATH)
                    filename = os.path.basename(dest_filepath)
                    # 路径都一样，cd_or_mkdir一次就可以了
                    if not upload_path:
                        upload_path = os.path.dirname(dest_filepath)
                        ftp_client.cd_or_mkdir(upload_path)

                    data_raw = send_data.get(DATA_RAW)
                    try:
                        if is_re_upload:
                            # 如果是一键重传，data_raw是json本地文件路径，直接上传文件即可
                            resp = ftp_client.upload_file(data_raw, filename, False)
                        else:
                            data_str = json.dumps(data_raw, indent=4, ensure_ascii=False)
                            if newline_type == 'window':
                                data_str = data_str.replace('\n', '\r\n')
                            resp = ftp_client.upload_content(filename, data_str)

                        # 226 上传成功
                        if '226' in resp:
                            fail_json_list.remove(send_data)
                            # 上传成功后需要删除本地json缓存文件
                            if is_re_upload:
                                os.remove(data_raw)
                                self.log.info(f"ftp json数据上传成功，缓存json数据已删除！")
                        else:
                            err_str = f'文件：{filename} ；error：{resp}'
                            err_file_list.append(err_str)
                    except Exception as e:
                        # 单个文件上传失败时，需捕获异常，记录失败信息后，继续上传其它文件
                        err_str = f'图片：{filename} ；上传error：{e}'
                        err_file_list.append(err_str)
                        continue

                if err_file_list:
                    err_str = "\n".join(err_file_list)
                    response = self.x_response("false", f"json ftp上传文件失败，失败文件列表：{err_str}")
            else:  # 登录失败
                response = self.x_response("false", f"json ftp登录失败，失败信息：{resp}")
        except Exception as e:
            response = self.x_response("false", f"json登录或者上传ftp出错，异常信息：{e}")

        # 保存失败的json文件列表,供下次一键重传使用，传递的参数参看send_data_template模版说明
        if fail_json_list:
            fail_data = {
                SEND_TO: SEND_TO_JSON_FTP,
                DATA_LIST: fail_json_list
            }
            self._append_fail_data(fail_data=fail_data, is_re_upload=is_re_upload)

        if ftp_client:
            try:
                # 如果ftp登录出现异常时，ftp还未建立，执行close会抛出异常，导致外层缓存不能正常保存，这里捕获下异常，让流程正常执行
                ftp_client.close()
            except Exception as e:
                err_msg = response.get('string') + "\n" + f"Failed to close FTP client: {e}"
                response = self.x_response("false", err_msg)

        return response

    def _send_ng_pic_to_ftp(self, data_list: list):
        """
        发送所有ng图片数据到指定的FTP路径，发送失败时会缓存失败图片信息，供一键重传使用
        data_list:要发送的NG图片列表，参看send_data_template的DATA_LIST数据结构：
            {
            DATA_DEST_FILE_PATH: '',  # 发送到ftp的图片路径
            DATA_RAW: '',  # 本地的NG图片路径
            }
        """
        response = self.x_response()
        ftp_client = None
        # 拷贝一份，用于异常失败时记录需要本地保存的图片列表
        fail_ng_pic_list = copy.deepcopy(data_list)
        try:
            ftp_encoding = self.other_vo.get_value_by_cons_key("ftp_encoding")
            ftp_host = self.other_vo.get_value_by_cons_key("pic_ftp_host")
            ftp_port = self.other_vo.get_value_by_cons_key("pic_ftp_port", to_int=True)
            ftp_user = self.other_vo.get_value_by_cons_key("pic_ftp_user")
            ftp_password = self.other_vo.get_value_by_cons_key("pic_ftp_password")

            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port, encoding=ftp_encoding)
            resp = ftp_client.login()
            if '230' in resp:  # 230 登录成功
                err_file_list = []
                upload_path = ''
                for send_data in data_list:
                    dest_filepath = send_data.get(DATA_DEST_FILE_PATH)
                    filename = os.path.basename(dest_filepath)
                    # 路径都一样，cd_or_mkdir一次就可以了
                    if not upload_path:
                        upload_path = os.path.dirname(dest_filepath)
                        ftp_client.cd_or_mkdir(upload_path)

                    src_filepath = send_data.get(DATA_RAW)
                    try:
                        resp = ftp_client.upload_file(src_filepath, filename, False)
                        if '226' in resp:  # 上传成功
                            fail_ng_pic_list.remove(send_data)
                        else:
                            err_str = f'图片：{filename} ；上传error：{resp}'
                            err_file_list.append(err_str)
                    except Exception as e:
                        # 单个文件上传失败时，需捕获异常，记录失败信息后，继续上传其它图片
                        err_str = f'图片：{filename} ；上传error：{e}'
                        err_file_list.append(err_str)
                        continue

                if err_file_list:
                    err_str = "\n".join(err_file_list)
                    response = self.x_response("false", f"不良图片ftp上传失败，失败图片列表：{err_str}")
            else:
                response = self.x_response("false", f"上传图片ftp登录失败，失败信息：{resp}")
        except Exception as e:
            response = self.x_response("false", f"上传图片ftp出错，异常信息：{e}")

        if fail_ng_pic_list:
            # 保存失败的NG图片列表,供下次一键重传使用，传递的参数参看send_data_template模版说明
            fail_data = {
                SEND_TO: SEND_TO_PIC_FTP,
                DATA_LIST: fail_ng_pic_list
            }
            self._append_fail_data(fail_data=fail_data)

        if ftp_client:
            try:
                # 如果ftp登录出现异常时，ftp还未建立，执行close会抛出异常，导致外层缓存不能正常保存，这里捕获下异常，让流程正常执行
                ftp_client.close()
            except Exception as e:
                err_msg = response.get('string') + "\n" + f"Failed to close FTP client: {e}"
                response = self.x_response("false", err_msg)

        return response

    def _send_data(self, send_data: dict, is_re_upload: bool = False):
        """
        发送最终数据，包含发送mes、本地保存、ftp json上传、ftp图片上传四种发送场景
        send_data: 统一的数据传递参数，具体数据结构参看 send_data_template
        is_re_upload: 是否是一键重传发送，如果是还需要执行上传成功后删除缓存文件操作
        """
        err_msg_list = []
        response = self.x_response()
        send_to = send_data.get(SEND_TO)
        data_list = send_data.get(DATA_LIST)

        # 保存到本地
        if send_to == SEND_TO_LOCAL:
            # 每个文件保存一次
            for data in data_list:
                response = self._save_json_to_local(data)
                if not response.get('result'):
                    err_msg = response.get('string')
                    err_msg_list.append(err_msg)

        # 上传数据到MES
        if send_to == SEND_TO_MES:
            # 每份json发送一次
            for data in data_list:
                response = self._send_json_to_mes(data, is_re_upload)
                if not response.get('result'):
                    err_msg = response.get('string')
                    err_msg_list.append(err_msg)

        # 上传数据到JSON FTP
        if send_to == SEND_TO_JSON_FTP:
            # json ftp登录成功后，所有文件统一上传
            response = self._send_json_to_ftp(data_list, is_re_upload)
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 上传数据到PIC FTP
        if send_to == SEND_TO_PIC_FTP:
            # pic ftp登录成功后，所有图片统一上传
            response = self._send_ng_pic_to_ftp(data_list)
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 把所有错误信息组装起来发送给AOI
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"数据发送或上传失败，错误信息：{err_str}")
        return response

    def _send_board_data(self, pcb_entity: PcbEntity):
        """
        单板数据生成和发送
        """
        pcb_sn = pcb_entity.pcb_barcode
        pcb_start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        comp_data_output = self.other_vo.get_value_by_cons_key("comp_data_output")
        device_name = self.other_vo.get_value_by_cons_key("device_name")
        is_save_local = self.other_vo.get_value_by_cons_key("is_save_local")
        is_upload_mes = self.other_vo.get_value_by_cons_key("is_upload_mes")
        is_upload_ftp = self.other_vo.get_value_by_cons_key("is_upload_ftp")

        ng_pic_list = []
        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            board_sn = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_data_output == "全部" or \
                        (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                        (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                    comp_data_list.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })
                    # 添加复判后不良的图片信息到列表，供后续发送不良图片时使用
                    if comp_entity.is_repair_ng():
                        ng_pic_list.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

            board_data_list.append({
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,
                "board_sn": board_sn,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "comp_data": comp_data_list,
            })

        err_msg_list = []
        response = self.x_response()
        # 上传数据到MES
        if is_upload_mes == "是":
            data_list = []
            for board_data in board_data_list:
                kwargs = {
                    SEND_TO: SEND_TO_MES,
                    DATA_SRC: DATA_SRC_BOARD,
                    'pcb_sn': pcb_sn,
                    'pcb_start_time': pcb_start_time,
                }
                # mes生成的是失败时本地保存的文件路径
                dest_file_path = self._get_send_to_file_path(**kwargs)
                # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
                data_list.append({
                    DATA_DEST_FILE_PATH: dest_file_path,  # 此文件路径指向发送MES失败时缓存文件路径
                    DATA_RAW: board_data,
                })

            response = self._send_data({
                SEND_TO: SEND_TO_MES,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 本地保存json
        if is_save_local == "是":
            data_list = []
            for board_data in board_data_list:
                kwargs = {
                    SEND_TO: SEND_TO_LOCAL,
                    DATA_SRC: DATA_SRC_BOARD,
                    'pcb_sn': pcb_sn,
                    'pcb_start_time': pcb_start_time,
                    'board_sn': board_data.get('board_sn'),
                    'board_no': board_data.get('board_no'),
                }
                # 生成本地保存文件路径
                dest_file_path = self._get_send_to_file_path(**kwargs)
                # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
                data_list.append({
                    DATA_DEST_FILE_PATH: dest_file_path,
                    DATA_RAW: board_data,
                })

            response = self._send_data({
                SEND_TO: SEND_TO_LOCAL,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 上传数据到FTP
        if is_upload_ftp == "是":
            # 上传json到FTP
            data_list = []
            for board_data in board_data_list:
                kwargs = {
                    SEND_TO: SEND_TO_JSON_FTP,
                    DATA_SRC: DATA_SRC_BOARD,
                    'pcb_sn': pcb_sn,
                    'pcb_start_time': pcb_start_time,
                    'board_sn': board_data.get('board_sn'),
                    'board_no': board_data.get('board_no'),
                }
                # 生成FTP保存文件路径
                dest_file_path = self._get_send_to_file_path(**kwargs)
                # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
                data_list.append({
                    DATA_DEST_FILE_PATH: dest_file_path,
                    DATA_RAW: board_data,
                })

            response = self._send_data({
                SEND_TO: SEND_TO_JSON_FTP,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

            # 上传不良图片到FTP
            data_list = []
            for ng_pic in ng_pic_list:
                kwargs = {
                    SEND_TO: SEND_TO_PIC_FTP,
                    'pcb_sn': pcb_sn,
                    'pcb_start_time': pcb_start_time,
                    "comp_designator": ng_pic.get('comp_designator'),
                    "comp_part": ng_pic.get('comp_part'),
                    "comp_user_code": ng_pic.get('comp_user_code'),
                    "comp_user_result": ng_pic.get('comp_user_result'),
                    "comp_image": ng_pic.get('comp_image'),
                }
                # 生成ftp保存图片路径
                dest_file_path = self._get_send_to_file_path(**kwargs)
                # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
                data_list.append({
                    DATA_DEST_FILE_PATH: dest_file_path,
                    DATA_RAW: ng_pic.get('comp_image'),  # 图片为原始的图片路径
                })

            response = self._send_data({
                SEND_TO: SEND_TO_PIC_FTP,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 把所有错误信息组装起来发送给AOI
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"单板数据发送或上传失败，错误信息：{err_str}")
        return response

    def _send_panel_data(self, pcb_entity: PcbEntity):
        """
        整板数据生成和发送
        """
        pcb_sn = pcb_entity.pcb_barcode
        pcb_start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        device_name = self.other_vo.get_value_by_cons_key("device_name")
        comp_data_output = self.other_vo.get_value_by_cons_key("comp_data_output")
        is_save_local = self.other_vo.get_value_by_cons_key("is_save_local")
        is_upload_mes = self.other_vo.get_value_by_cons_key("is_upload_mes")
        is_upload_ftp = self.other_vo.get_value_by_cons_key("is_upload_ftp")

        board_data = []
        ng_pic_list = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_data_output == "全部" or \
                        (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                        (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })
                    # 如果图片路径不为空，添加复判后不良的图片信息到列表，供后续发送不良图片时使用
                    if comp_entity.is_repair_ng() and comp_entity.image_path:
                        ng_pic_list.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        pcb_data = {
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        err_msg_list = []
        response = self.x_response()
        # 上传数据到MES
        if is_upload_mes == "是":
            kwargs = {
                SEND_TO: SEND_TO_MES,
                DATA_SRC: DATA_SRC_PANEL,
                'pcb_sn': pcb_sn,
                'pcb_start_time': pcb_start_time,
            }
            # mes生成的是失败时本地保存的文件路径
            dest_file_path = self._get_send_to_file_path(**kwargs)
            # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
            data_list = [{
                DATA_DEST_FILE_PATH: dest_file_path,  # 此文件路径指向发送MES失败时缓存文件路径
                DATA_RAW: pcb_data,
            }]
            response = self._send_data({
                SEND_TO: SEND_TO_MES,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 本地保存json
        if is_save_local == "是":
            kwargs = {
                SEND_TO: SEND_TO_LOCAL,
                DATA_SRC: DATA_SRC_PANEL,
                'pcb_sn': pcb_sn,
                'pcb_start_time': pcb_start_time,
            }
            # 生成本地保存文件路径
            dest_file_path = self._get_send_to_file_path(**kwargs)
            # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
            data_list = [{
                DATA_DEST_FILE_PATH: dest_file_path,
                DATA_RAW: pcb_data,
            }]
            response = self._send_data({
                SEND_TO: SEND_TO_LOCAL,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 上传数据到FTP
        if is_upload_ftp == "是":
            # 上传json到FTP
            kwargs = {
                SEND_TO: SEND_TO_JSON_FTP,
                DATA_SRC: DATA_SRC_PANEL,
                'pcb_sn': pcb_sn,
                'pcb_start_time': pcb_start_time,
            }
            # 生成ftp保存文件路径
            dest_file_path = self._get_send_to_file_path(**kwargs)
            # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
            data_list = [{
                DATA_DEST_FILE_PATH: dest_file_path,
                DATA_RAW: pcb_data,
            }]
            response = self._send_data({
                SEND_TO: SEND_TO_JSON_FTP,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

            # 上传不良图片到FTP
            data_list = []
            for ng_pic in ng_pic_list:
                kwargs = {
                    SEND_TO: SEND_TO_PIC_FTP,
                    DATA_SRC: DATA_SRC_PANEL,
                    'pcb_sn': pcb_sn,
                    'pcb_start_time': pcb_start_time,
                    "comp_designator": ng_pic.get('comp_designator'),
                    "comp_part": ng_pic.get('comp_part'),
                    "comp_user_code": ng_pic.get('comp_user_code'),
                    "comp_user_result": ng_pic.get('comp_user_result'),
                    "comp_image": ng_pic.get('comp_image'),
                }
                # 生成ftp保存文件路径
                dest_file_path = self._get_send_to_file_path(**kwargs)
                # 生成统一发送数据的模版参数，参数数据结构参看send_data_template说明
                data_list.append({
                    DATA_DEST_FILE_PATH: dest_file_path,
                    DATA_RAW: ng_pic.get('comp_image'),  # 图片则放置原始图片路径
                })

            response = self._send_data({
                SEND_TO: SEND_TO_PIC_FTP,
                DATA_LIST: data_list
            })
            if not response.get('result'):
                err_msg = response.get('string')
                err_msg_list.append(err_msg)

        # 把所有错误信息组装起来发送给AOI
        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            response = self.x_response("false", f"整板数据发送或上传失败，错误信息：{err_str}")
        return response

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        save_type = self.other_vo.get_value_by_cons_key("save_type")
        is_save_local = self.other_vo.get_value_by_cons_key("is_save_local")
        save_path = self.other_vo.get_value_by_cons_key("save_path")
        is_upload_mes = self.other_vo.get_value_by_cons_key("is_upload_mes")
        mes_api_url = self.other_vo.get_value_by_cons_key("mes_api_url")
        is_upload_ftp = self.other_vo.get_value_by_cons_key("is_upload_ftp")
        json_ftp_host = self.other_vo.get_value_by_cons_key("json_ftp_host")
        pic_ftp_host = self.other_vo.get_value_by_cons_key("pic_ftp_host")

        if is_save_local == "是" and not save_path:
            return self.x_response("false", "本地保存数据路径不能为空，请先选择保存路径！")
        if is_upload_mes == "是" and not mes_api_url:
            return self.x_response("false", "上传MES接口地址还未填写，请先填写！")
        if is_upload_ftp == "是" and (not json_ftp_host or not pic_ftp_host):
            return self.x_response("false", "FTP信息还未配置，请先配置！")

        if save_type == "整板生成":
            response = self._send_panel_data(pcb_entity)
        elif save_type == "拼板生成":
            response = self._send_board_data(pcb_entity)
        else:
            response = self.x_response("false", f"不支持的文件生成方式！")

        # 把缓存的失败数据信息保存到本地
        self._save_fail_data_to_file()

        return response

    def _machine_stop_detect(self):
        # 已经在停机状态，直接返回
        if self.machine_stop_info:
            self.log.info('已经处在停机状态')
            return

        # 如果已经有启动定时器，则沿用上个定时器即可
        if self.machine_stop_timer:
            self.log.info('停机状态检测定时器已启用，沿用它')
            return

        self.log.info('开始5分钟停机状态定时器检测')
        self.machine_stop_timer = threading.Timer(5*60, self._send_machine_stop_info, args=[False])
        self.machine_stop_timer.start()

    def _send_machine_stop_info(self, is_end, is_sync=False):
        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT6)
        if is_end:
            self.log.info('停机结束，准备发送停机结束信息')
            self.machine_stop_info['end_time'] = time_now
        else:
            self.log.info('5分钟停机检测时间到，进入停机状态')
            self.machine_stop_timer = None
            # 进入设备停机状态，则认为故障结束
            if self.alarm_info:
                self._send_alarm_info(is_end=True, is_sync=True)

            self.machine_stop_info = {
                'data_type': '3',
                'start_time': time_now,
                'end_time': '',
            }

        if is_sync:
            self._send_device_info(self.machine_stop_info)
        else:
            timer = threading.Timer(0.6, self._send_device_info, args=[self.machine_stop_info])
            timer.start()

        if is_end:
            self.machine_stop_info = {}

    def send_idle_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        # 设备空转也加入到停机状态
        self._machine_stop_detect()
        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        # 这个客户的主软件版本使用的是旧的设备状态接口
        status_desc = other_vo.get_status_desc()
        # 客户不需要安全门信息，直接过滤
        if '安全门已打开' in status_desc:
            return self.x_response()

        # 有新的设备状态过来，说明机器重新运转了，则表示停机结束
        if self.machine_stop_info:
            self._send_machine_stop_info(is_end=True)
        elif self.machine_stop_timer:
            # 5分钟还没到，则关闭停机状态检测定时器
            self.log.info('关闭停机状态检测定时器')
            self.machine_stop_timer.cancel()
            self.machine_stop_timer = None

        if status_desc == '开始检测':
            # 开始检测后，设备故障解除，发送设备故障信息
            if self.alarm_info:
                self._send_alarm_info(is_end=True)

        # 如果当前是故障状态，停止检查不启动停机状态检测
        if not self.alarm_info and status_desc == '停止检查':
            self._machine_stop_detect()

        # 进板、开始检测、停止检查、出板不做为报警，过滤掉不发送
        if status_desc in ['进板', '开始检测', '停止检查', '出板']:
            return self.x_response()

        # 记录并保存设备故障信息
        self._send_alarm_info(is_end=False, alarm_desc=status_desc)

        return self.x_response()

    def _send_alarm_info(self, is_end, alarm_desc='', is_sync=False):
        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT6)
        if is_end:
            self.alarm_info['end_time'] = time_now
            if is_sync:
                self._send_device_info(self.alarm_info)
            else:
                timer = threading.Timer(0.3, self._send_device_info, args=[self.alarm_info])
                timer.start()

            self.alarm_info = {}
        else:
            # 上一个故障未结束，又来一个故障时，上一个故障当做结束
            if self.alarm_info:
                self._send_alarm_info(is_end=True)

            alarm_info = {
                'data_type': '2',
                'start_time': time_now,
                'end_time': '',
                'alarm_type': alarm_desc,
                'alarm_solution': alarm_desc,
            }
            if is_sync:
                self._send_device_info(alarm_info)
            else:
                timer = threading.Timer(0.4, self._send_device_info, args=[alarm_info])
                timer.start()

            self.alarm_info = alarm_info

    def _get_boot_time(self):
        boot_time = ''
        try:
            with open('/proc/uptime', 'r') as f:
                uptime_seconds = float(f.readline().split()[0])
                boot_time = time.time() - uptime_seconds
                boot_time = time.strftime(xcons.FMT_TIME_DEFAULT6, time.localtime(boot_time))
        except Exception as e:
            self.log.info(f'获取系统开机时间失败，error:{e}')
        finally:
            return boot_time

    def _get_shutdown_time(self):
        if self.shutdown_time:
            self.log.info(f'关机时间已获取过：{self.shutdown_time}')
            return self.shutdown_time

        self.log.info('开始获取关机时间')
        shutdown_time = ''
        # 此命令用于获取关机和重启时间，只输出最新五行即可
        command = "last -x | grep -E 'runlevel' | head -n 5"
        result = subprocess.run(command, shell=True, text=True, capture_output=True)
        if result.returncode == 0:
            self.log.info("输出的runlevel信息如下：")
            self.log.info(result.stdout)
            stdout = result.stdout
            # 提取最新带有结束时间的runlevel行作为上次关机时间
            lines = stdout.splitlines()
            for line in lines:
                if 'running' in line:
                    continue

                # 格式：runlevel (to lvl 5)   5.4.0-150-generi Mon Apr 28 10:04 - 20:05 (1+10:01)
                # 提取开始时间
                start_time_match = re.search(r'(\w{3})\s+(\d{1,2})\s+(\d{2}:\d{2})', line)
                if start_time_match:
                    month, day, time = start_time_match.groups()
                    year = datetime.now().year
                    start_time_str = f"{year}-{month}-{day}T{time}"

                    original_locale = locale.getlocale()
                    # 设置为英文区域，如果非英文区域datetime.strptime会报错：time data 'Apr 18 14:18:43' does not match format '%b %d %H:%M:%S'
                    locale.setlocale(locale.LC_TIME, 'en_US.UTF-8')
                    start_time = datetime.strptime(start_time_str, "%Y-%b-%dT%H:%M")
                    # 恢复原始区域设置
                    locale.setlocale(locale.LC_TIME, original_locale)

                    # 提取时间长度，匹配两种格式：(dd+HH:MM，5+18:25) 和 (HH:MM，11:14)
                    duration_match = re.search(r'\((?:(\d+)\+)?(\d{1,2}):(\d{2})\)', line)
                    if duration_match:
                        day_str, hour_str, minute_str = duration_match.groups()
                        days = int(day_str) if day_str else 0
                        hours = int(hour_str)
                        minutes = int(minute_str)
                        # 生成 1 到 59 秒的随机数
                        random_seconds = random.randint(1, 59)
                        duration = timedelta(days=days, hours=hours, minutes=minutes, seconds=random_seconds)
                        # 计算结束时间
                        end_time = start_time + duration
                        # 格式化输出时间
                        shutdown_time = end_time.strftime("%Y-%m-%dT%H:%M:%S")
                        break
                    else:
                        self.log.info("未找到持续时间长度")
                else:
                    self.log.info("未找到开始时间信息")
        else:
            self.log.info(f"获取系统关机时间失败：{result.stderr}")

        self.shutdown_time = shutdown_time
        return shutdown_time

    def _send_power_on_info(self):
        power_on_time = self._get_boot_time()
        power_off_time = self._get_shutdown_time()
        upload_data = {
            'data_type': '1',
            'start_time': power_on_time,
            'previous_end_time': power_off_time,
        }
        # 1.8s后再启动上传，让故障和停用信息先上传
        timer = threading.Timer(1.5, self._send_device_info, args=[upload_data])
        timer.start()

    def _save_device_info_time(self, upload_data):
        self.file_lock.acquire()

        data_type = upload_data.get('data_type')
        start_time = upload_data.get('start_time', '')
        end_time = upload_data.get('end_time', '')

        time_info = {data_type: {'previous_start_time': start_time, 'previous_end_time': end_time}}
        # 没有结束时间，表示数据类型（除了开关机类型）刚发生，还未发送，先记录当前信息，不记录为上次发送
        save_cur_time = False
        if not end_time:
            # 设备故障
            if data_type == '2':
                save_cur_time = True
                time_info = {
                    data_type: {
                        'start_time': start_time,
                        'end_time': '',
                        'alarm_type': upload_data.get('alarm_type', ''),
                        'alarm_solution': upload_data.get('alarm_solution', ''),
                        'previous_start_time': '',
                        'previous_end_time': '',
                    }
                }
            # 设备空闲
            elif data_type == '3':
                save_cur_time = True
                time_info = {
                    data_type: {
                        'start_time': start_time,
                        'end_time': '',
                        'previous_start_time': '',
                        'previous_end_time': '',
                    }
                }

        try:
            # 没有记录过，直接赋值，有记录过，则追加
            if not os.path.exists(DEVICE_INFO_TIME_FILE):
                cached_time_info = time_info
            else:
                cached_time_info = xutil.FileUtil.load_json_file(DEVICE_INFO_TIME_FILE)
                if data_type not in cached_time_info:
                    cached_time_info.update(time_info)
                else:
                    pre_time = cached_time_info[data_type]
                    if save_cur_time:
                        # 由于只是保存当前时间，所以上次的时间需要继续记录
                        cur_time = time_info[data_type]
                        cur_time['previous_start_time'] = pre_time['previous_start_time']
                        cur_time['previous_end_time'] = pre_time['previous_end_time']
                        cached_time_info[data_type] = cur_time
                    else:
                        if pre_time.get('start_time'):
                            # 有记录过当前时间，则清理掉
                            cached_time_info[data_type] = time_info[data_type]
                        else:
                            if start_time:
                                cached_time_info[data_type]['previous_start_time'] = start_time
                            if end_time:
                                cached_time_info[data_type]['previous_end_time'] = end_time

            data_str = json.dumps(cached_time_info, indent=4, ensure_ascii=False)

            # 先创建失败缓存目录：
            xutil.FileUtil.ensure_dir_exist(FAIL_CACHE_DIR)
            xutil.FileUtil.write_content_to_file(DEVICE_INFO_TIME_FILE, data_str)
        except Exception as e:
            self.log.info(f"保存设备信息时间出错: {e}")
        finally:
            # 释放锁
            self.file_lock.release()

    def _send_cache_device_info(self):
        # 启动后检查告警和空闲上一次是否未发送过，如果是则启动后先发送
        if os.path.exists(DEVICE_INFO_TIME_FILE):
            device_info_time = xutil.FileUtil.load_json_file(DEVICE_INFO_TIME_FILE)
            # 故障
            time_info = device_info_time.get('2', {})
            # 有开始时间，没有结束时间表示上次未上传过
            if time_info.get('start_time') and not time_info.get('end_time'):
                upload_data = {
                    'data_type': '2',
                }
                # 把上次关机时间作为故障结束时间
                time_info['end_time'] = self._get_shutdown_time()
                upload_data.update(time_info)
                # 1.2s后再启动上传，让启动初始化其它流程先走完
                timer = threading.Timer(1, self._send_device_info, args=[upload_data])
                timer.start()
            # 空闲
            time_info = device_info_time.get('3', {})
            if time_info.get('start_time') and not time_info.get('end_time'):
                upload_data = {
                    'data_type': '3',
                }
                # 把上次关机时间作为故障结束时间
                time_info['end_time'] = self._get_shutdown_time()
                upload_data.update(time_info)
                # 1.2s后再启动上传，让启动初始化其它流程先走完
                timer = threading.Timer(1.2, self._send_device_info, args=[upload_data])
                timer.start()

        # 再发送开关机信息
        self._send_power_on_info()

    def _send_device_info(self, upload_data: dict):
        status_api_url = self.other_vo.get_value_by_cons_key("status_api_url")
        biill_no = self.other_vo.get_value_by_cons_key("biill_no")
        device_name = self.other_vo.get_value_by_cons_key("device_name")
        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT6)

        data_type_list = {'1': '设备开关机', '2': '设备故障', '3': '设备停机'}
        data_type = upload_data.get('data_type')
        data_type_desc = data_type_list[data_type]

        self.log.info(f'发送{data_type_desc}信息')

        if not status_api_url:
            self.log.info('上传设备信息地址为空，请先填写')
            self.log.info(f'缓存{data_type_desc}时间到device_info_time.json，供下次上传时使用')
            self._save_device_info_time(upload_data)
            return

        if not os.path.exists(DEVICE_INFO_TIME_FILE):
            self.log.info(f'device_info_time.json文件不存在，第一次上传')
            previous_start_time = ''
            previous_end_time = ''
            # 如果是设备开关机，上次关机时间都是实时获取的
            if data_type == '1':
                previous_end_time = upload_data.get('previous_end_time', '')
        else:
            start_time = upload_data.get('start_time', '')
            device_info_time = xutil.FileUtil.load_json_file(DEVICE_INFO_TIME_FILE)
            previous_start_time = device_info_time.get(data_type, {}).get('previous_start_time', '')
            previous_end_time = device_info_time.get(data_type, {}).get('previous_end_time', '')

            # 如果这次开始时间和上次开始时间一样，说明已经上传过，不需要重复上传
            if start_time == previous_start_time:
                self.log.info(f'{data_type_desc}信息start_time：{start_time} 已上传过')
                return

            # 如果是设备开关机，上次关机时间都是实时获取的
            if data_type == '1':
                previous_end_time = upload_data.get('previous_end_time', '')

        try:
            status_info = {
                'biill_No': biill_no,
                'equip_no': device_name,
                'data_type': int(data_type),  # 数据类型,整形(1 设备开关机、2 设备故障、3 设备停机)
                'produce_date': time_now,
                'start_time': upload_data.get('start_time', ''),
                'end_time': upload_data.get('end_time', ''),
                'alarm_type': upload_data.get('alarm_type', ''),
                'alarm_solution': upload_data.get('alarm_solution', ''),
                'comments': '',
                'previous_start_time': previous_start_time,
                'previous_end_time': previous_end_time,
            }
            ret = xrequest.RequestUtil.post_json(status_api_url, status_info)
            if ret.get("Status"):
                self.log.info(f'发送{data_type_desc}信息成功')
            else:
                self.log.info(f"发送设备信息失败，服务器返回错误：{ret.get('Message')}")
        except Exception as e:
            self.log.info(f"本地网络异常,发送设备信息失败，error：{e}")
        finally:
            self.log.info(f'缓存{data_type_desc}时间到device_info_time.json，供下次上传时使用')
            self._save_device_info_time(upload_data)
