from common import xutil

if __name__ == '__main__':
    ret_str1 = """<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
        <GetApiCollResponse xmlns="http://tempuri.org/">
          <GetApiCollResult>true</GetApiCollResult>
          <StrReturn>ok</StrReturn>
        </GetApiCollResponse>
      </soap:Body>
    </soap:Envelope>"""
    root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    # get_result = root1[0][0][0].text
    # str_return = root1[0][0][1].text
    # print(f"{get_result=}")
    # print(f"{str_return=}")
    # result = root1[0][0].find('{http://192.168.21.10/}Save_Tr_Common_DataInfoResult').text
    # message = root1[0][0].find('{http://192.168.21.10/}message').text

    # 找到GetApiCollResponse元素
    get_api_coll_response = root1.find('.//{http://tempuri.org/}GetApiCollResponse')

    # 获取GetApiCollResult标签内容
    get_api_coll_result = get_api_coll_response.find('{http://tempuri.org/}GetApiCollResult').text
    print("GetApiCollResult内容:", get_api_coll_result)

    # 获取StrReturn标签内容
    str_return = get_api_coll_response.find('{http://tempuri.org/}StrReturn').text
    print("StrReturn内容:", str_return)
