# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : http_mock.py
# Author     ：sch
# version    ：python 3.8
# Description：
"""


import uvicorn
from fastapi import FastAPI, Response

app = FastAPI()


@app.post("/WebService/BasalWebService.asmx/WS_BcValid")
def ws_bc_valid():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://mesateapi.com/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(media_type="application/xml", content=ret_str)


@app.post("/WebService/BasalWebService.asmx/WS_UpdUnitRecord")
def ws_upd_unit_record():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string xmlns="http://mesateapi.com/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(media_type="application/xml", content=ret_str)


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8081)
