# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : other_param_setting.py
# Time       ：2023/4/26 下午2:55
# Author     ：sch
# version    ：python 3.8
# Description：其他-其他参数设置
"""
import functools
import traceback

from PyQt5.QtCore import QRegExp
from PyQt5.QtGui import QIntValidator, QRegExpValidator
from PyQt5.QtWidgets import *

from common import xutil
from common.xcompoments import CustomComboOther
from common.xcons import EN_KEY
from services.route import engine
from templates.other_param_setting import Ui_ParamDialog


class OtherParamViews(QDialog, Ui_ParamDialog):
    def __init__(self, main_window):
        super(OtherParamViews, self).__init__()
        self.setupUi(self)

        self.is_load_param = False

        self.main_window = main_window

    def btn_path_select_on_click(self, pure_key: str):
        ret = QFileDialog.getExistingDirectory(self, "选择文件夹", "~")
        path_value_tmp = getattr(self, f"path_value_{pure_key}")
        path_value_tmp.setText(str(ret))

        config_data = xutil.FileUtil.load_config_file()
        path_map = config_data.get("other_path", {})

        path_item = path_map.get(pure_key, {})
        path_item["value"] = path_value_tmp.text()

        path_map[pure_key] = path_item

        self.main_window.config_data["other_path"] = path_map
        self.main_window.save_config_data_to_file()

    def param_dialog_show(self):
        """
        其他参数设置
        :return:
        """
        other_form = self.main_window.config_data.get('other_form', {})
        other_combo = self.main_window.config_data.get('other_combo', {})
        other_path = self.main_window.config_data.get('other_path', {})

        lang = self.main_window.get_lang()

        if not self.is_load_param:

            for k, v in engine.other_path.items():
                path_btn_key = f"path_btn_{k}"
                path_value_key = f"path_value_{k}"

                path_ui_name = v.get("ui_name")

                if lang == EN_KEY:
                    ui_name_en = v.get("ui_name_en")

                    if ui_name_en:
                        path_ui_name = ui_name_en

                path_btn_tmp = QPushButton(self.scrollAreaWidgetContents)

                path_btn_tmp.setText(path_ui_name)

                setattr(self, path_btn_key, path_btn_tmp)
                path_value_tmp = QLineEdit(self)
                path_value_tmp.setEnabled(False)

                path_value_value = other_path.get(k, {}).get("value", "")
                if not path_value_value:
                    path_value_value = v.get("value")

                path_value_tmp.setText(path_value_value)
                setattr(self, path_value_key, path_value_tmp)
                self.param_layout.addRow(path_btn_tmp, path_value_tmp)

                getattr(self, path_btn_key).clicked.connect(functools.partial(self.btn_path_select_on_click, k))

            # 1. 加载combo参数
            for k, v in engine.other_combo.items():
                param_key = v.get("ui_name")
                combo_item = v.get("item")
                # select_combo = v.get("value")
                select_combo = other_combo.get(k, {}).get("value")

                if lang == EN_KEY:
                    ui_name_en = v.get("ui_name_en")
                    if ui_name_en:
                        param_key = ui_name_en

                label = QLabel(self)
                label.setText(param_key)

                combo = CustomComboOther(self, combo_key=k, main_window=self.main_window)

                if select_combo and select_combo not in combo_item:
                    try:
                        select_combo = combo_item[0]
                    except Exception as err:
                        print(f"设置默认的选项失败：error：{err} ---> {traceback.format_exc()}")
                        select_combo = "unknown"

                for combo_value in combo_item:
                    combo.addItem(combo_value)

                if select_combo in combo_item:
                    combo.setCurrentIndex(combo_item.index(select_combo))

                combo_key = f"combo_{k}"
                setattr(self, combo_key, combo)

                self.param_layout.addRow(label, combo)

            # 2. 加载form参数
            for k, v in engine.other_form.items():
                label = QLabel(self)

                ui_name_form = v.get("ui_name")

                if lang == EN_KEY:
                    ui_name_en = v.get("ui_name_en")
                    if ui_name_en:
                        ui_name_form = ui_name_en

                label.setText(ui_name_form)
                edit_key = f"other_form_{k}"

                tmp_edit = QLineEdit(self)

                if v.get("is_int"):
                    tmp_edit.setValidator(QIntValidator())

                if v.get("is_port"):
                    tmp_edit.setValidator(QIntValidator(0, 65535))

                if v.get("is_ip"):
                    ip_regex = QRegExp(
                        r'^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
                    tmp_edit.setValidator(QRegExpValidator(ip_regex))

                # form_value = v.get("value", "")
                form_value = other_form.get(k, {}).get("value", "")

                tmp_edit.setText(form_value)

                if k in engine.password_style:
                    tmp_edit.setEchoMode(QLineEdit.Password)  # noqa

                setattr(self, edit_key, tmp_edit)

                self.param_layout.addRow(label, tmp_edit)

            self.is_load_param = True
        else:
            pass
            # 更新就好
            # print("gengxin jiuhao...")
            # for k, v in engine.other_form.items():
            #     edit_key = f"other_form_{k}"
            #     tmp_edit = getattr(self, edit_key)
            #     form_value = v.get("value", "")
            #     tmp_edit.setText(form_value)
            #
            # for k, v in engine.other_combo.items():
            #     combo_item = v.get("item")
            #     select_combo = v.get("value")
            #
            #     if not select_combo:
            #         log.warning(f"{v.get('ui_name')}没有选中值！")
            #
            #     combo_key = f"combo_{k}"
            #     tmp_combo = getattr(self, combo_key)
            #
            #     if select_combo in combo_item:
            #         tmp_combo.setCurrentIndex(combo_item.index(select_combo))

        ret = self.exec_()
        if ret == 1:
            # ok button
            other_form_param = engine.other_form.keys()
            other_combo_param = engine.other_combo.keys()
            other_path_param = engine.other_path.keys()

            for k in other_form_param:
                edit_key = f"other_form_{k}"

                tmp_edit = getattr(self, edit_key)
                ui_v = tmp_edit.text()  # ui上的值

                other_form[k]["value"] = ui_v

            for k in other_combo_param:
                combo_key = f"combo_{k}"
                tmp_combo = getattr(self, combo_key)

                other_combo[k]["value"] = tmp_combo.currentText()

            for k in other_path_param:
                path_key = f"path_value_{k}"
                tmp_path = getattr(self, path_key)

                other_path[k]["value"] = tmp_path.text()

            self.main_window.save_config_data_to_file()
