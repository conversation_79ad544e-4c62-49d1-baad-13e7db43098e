# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/about_me.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_AboutMeDialog(object):
    def setupUi(self, AboutMeDialog):
        AboutMeDialog.setObjectName("AboutMeDialog")
        AboutMeDialog.resize(498, 516)
        self.verticalLayout = QtWidgets.QVBoxLayout(AboutMeDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame_2 = QtWidgets.QFrame(AboutMeDialog)
        self.frame_2.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_2.setContentsMargins(1, 1, 1, 1)
        self.verticalLayout_2.setSpacing(1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label_time = QtWidgets.QLabel(self.frame_2)
        self.label_time.setObjectName("label_time")
        self.verticalLayout_2.addWidget(self.label_time)
        self.label_commit_id = QtWidgets.QLabel(self.frame_2)
        self.label_commit_id.setObjectName("label_commit_id")
        self.verticalLayout_2.addWidget(self.label_commit_id)
        self.line = QtWidgets.QFrame(self.frame_2)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.verticalLayout_2.addWidget(self.line)
        self.verticalLayout.addWidget(self.frame_2)
        self.frame = QtWidgets.QFrame(AboutMeDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_3.setContentsMargins(3, 3, 3, 3)
        self.verticalLayout_3.setSpacing(1)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.scrollArea = QtWidgets.QScrollArea(self.frame)
        self.scrollArea.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 474, 404))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.label_feature = QtWidgets.QLabel(self.scrollAreaWidgetContents)
        self.label_feature.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.label_feature.setObjectName("label_feature")
        self.verticalLayout_4.addWidget(self.label_feature)
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout_3.addWidget(self.scrollArea)
        self.verticalLayout.addWidget(self.frame)
        self.buttonBox = QtWidgets.QDialogButtonBox(AboutMeDialog)
        self.buttonBox.setOrientation(QtCore.Qt.Horizontal)
        self.buttonBox.setStandardButtons(QtWidgets.QDialogButtonBox.Ok)
        self.buttonBox.setObjectName("buttonBox")
        self.verticalLayout.addWidget(self.buttonBox)
        self.verticalLayout.setStretch(0, 1)
        self.verticalLayout.setStretch(1, 8)
        self.verticalLayout.setStretch(2, 1)

        self.retranslateUi(AboutMeDialog)
        self.buttonBox.accepted.connect(AboutMeDialog.accept) # type: ignore
        self.buttonBox.rejected.connect(AboutMeDialog.reject) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(AboutMeDialog)

    def retranslateUi(self, AboutMeDialog):
        _translate = QtCore.QCoreApplication.translate
        AboutMeDialog.setWindowTitle(_translate("AboutMeDialog", "关于"))
        self.label_time.setText(_translate("AboutMeDialog", "Built on "))
        self.label_commit_id.setText(_translate("AboutMeDialog", "From revision"))
        self.label_feature.setText(_translate("AboutMeDialog", "作者：xxx\n"
"适用机型：AIS40x\n"
"功能：上传数据\n"
"发布历史："))
