# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : jyt_websocket_server.py
# Time       ：2025/5/6 下午3:38
# Author     ：sch
# version    ：python 3.8
# Description：websocket服务端
"""
import asyncio
import json
import os.path
import time
import traceback

import websockets
from PyQt5.QtCore import QThread, pyqtSignal

from common import xutil, xglobal
from common.xutil import log
from services.custom.shaoguanjinyuetong import jyt_module
from services.custom.shaoguanjinyuetong.jyt_data import queue1, jyt_data_list

jrt_clients = set()


# shutdown_flag = False  # 标志位，用于安全关闭服务器

# jyt_global_data = {}


# def generate_status():
#     return {
#         "DeviceID": "EQ1234",
#         "LastStatusChangeTimestamp": int(time.time() * 1000),
#         "TextRegions": {
#             "OperationStatus": random.choice(["运行", "暂停", "告警"]),
#             "CurrentProductionQuantity": str(random.randint(100, 200)),
#             "AlarmMessage": random.choice(["无", "过温", "气缸错误"]),
#         }
#     }


def get_status_data() -> dict:
    """
    [客户端]-->[服务端]
    获取设备状态
    """
    cache_data = xutil.CacheUtil.get_cache_data()

    device_id_1 = cache_data.get("device_id_1")
    device_id_2 = cache_data.get("device_id_2")

    insp_data1 = jyt_module.get_insp_data_by_track_index(1)
    insp_data2 = jyt_module.get_insp_data_by_track_index(2)

    track1_project_name = ""
    track2_project_name = ""

    if insp_data1.get("projectOpened"):
        # 轨道1正在运行的板式
        track1_project_name = os.path.basename(insp_data1.get("projectName", ""))

    if not track1_project_name:
        track1_project_name = xglobal.global_data.get(f"track1", "")

    if insp_data2.get("projectOpened"):
        track2_project_name = os.path.basename(insp_data2.get("projectName", ""))

    if not track2_project_name:
        track2_project_name = xglobal.global_data.get(f"track2", "")

    log.info(f"[服务端]track1_project_name:{track1_project_name}")
    log.info(f"[服务端]track2_project_name:{track2_project_name}")

    device_status_time_1 = cache_data.get("device_status_time_1")
    device_run_status_1 = cache_data.get("device_run_status_1", "")
    device_alarm_msg_1 = cache_data.get("device_alarm_msg_1", "")

    device_status_time_2 = cache_data.get("device_status_time_2")
    device_run_status_2 = cache_data.get("device_run_status_2", "")
    device_alarm_msg_2 = cache_data.get("device_alarm_msg_2", "")

    if insp_data1.get("projectOpened"):
        track1_pass_count = insp_data1.get("passCount", 0)
        track1_repass_count = insp_data1.get("repassCount", 0)
        track1_ng_count = insp_data1.get("ngCount", 0)
    else:
        track1_pass_count = 0
        track1_repass_count = 0
        track1_ng_count = 0
        device_run_status_1 = "停止"
        device_alarm_msg_1 = ""

    if insp_data2.get("projectOpened"):
        track2_pass_count = insp_data2.get("passCount", 0)
        track2_repass_count = insp_data2.get("repassCount", 0)
        track2_ng_count = insp_data2.get("ngCount", 0)
    else:
        track2_pass_count = 0
        track2_repass_count = 0
        track2_ng_count = 0
        device_run_status_2 = "停止"
        device_alarm_msg_2 = ""

    track_width1 = insp_data1.get("trackWidth", "")
    track_width2 = insp_data2.get("trackWidth", "")

    if not track_width1:
        track_width1 = jyt_module.get_track_width_by_file(track1_project_name)

    if not track_width2:
        track_width2 = jyt_module.get_track_width_by_file(track2_project_name)

    track1_total_count = track1_pass_count + track1_repass_count + track1_ng_count
    track2_total_count = track2_pass_count + track2_repass_count + track2_ng_count

    data = {
        device_id_1: {
            "DeviceID": device_id_1,
            "LastStatusChangeTimestamp": device_status_time_1,
            "TextRegions": {
                "OperationStatus": device_run_status_1,
                "ProductionFileName": track1_project_name,
                "CurrentProductionQuantity": str(track1_total_count),
                "RailWidthMm": track_width1,
                "IsBoardRequest": "N",  # 要板信号
                "IsBoardEject": "N",  # 出板信号
                "TrackHasBoard": "N",  # 当前是否有板
                "AlarmMessage": device_alarm_msg_1,
                "DirectPassCount": str(track1_pass_count),
                "NGCount": str(track1_ng_count),
                "FalsePositiveCount": str(track1_repass_count),
            }
        },
    }

    if insp_data2:
        # 如果有二轨，则需要将二轨的状态也给客户端返回
        data[device_id_2] = {
            "DeviceID": device_id_2,
            "LastStatusChangeTimestamp": device_status_time_2,
            "TextRegions": {
                "OperationStatus": device_run_status_2,
                "ProductionFileName": track2_project_name,
                "CurrentProductionQuantity": str(track2_total_count),
                "RailWidthMm": track_width2,
                "IsBoardRequest": "N",  # 要板信号
                "IsBoardEject": "N",  # 出板信号
                "TrackHasBoard": "N",  # 当前是否有板
                "AlarmMessage": device_alarm_msg_2,
                "DirectPassCount": str(track2_pass_count),
                "NGCount": str(track2_ng_count),
                "FalsePositiveCount": str(track2_repass_count),
            }
        }

    status_param = {
        "type": "statusResponse",
        "data": xutil.OtherUtil.obj_to_json(data)
    }

    return status_param


async def safe_send(websocket, message):
    """安全发送消息，避免连接已关闭导致的异常"""
    try:
        await websocket.send(message)
    except websockets.exceptions.ConnectionClosedOK:
        log.warning(f"[服务端] 连接已关闭，无法发送消息: {message}")
    except Exception as e:
        log.error(f"[服务端] 发送消息失败: {e}")


async def notify_status_update():
    """
    消息更新推送    [服务端--->客户端]
    """
    log.info("[服务端] 状态更新推送协程已启动")  # 添加日志
    try:
        while xglobal.global_data.get("jrt_server_is_start"):
            if jrt_clients and jyt_data_list:
                try:
                    upload_data = jyt_data_list.pop(0)
                    upload_str = json.dumps(upload_data, ensure_ascii=False)
                    log.info(f"[主动推送][服务端] ---> [客户端]: {upload_str}")
                    # 使用 safe_send 避免连接关闭异常
                    await asyncio.gather(*(safe_send(ws, upload_str) for ws in jrt_clients))
                except Exception as e:
                    log.error(f"[服务端] 推送状态更新失败: {e}")
                    log.error(f"[服务端] 错误堆栈信息: {traceback.format_exc()}")
            await asyncio.sleep(0.1)
    finally:
        log.info("[服务端] 状态更新推送协程已退出")  # 添加日志


async def handle_client(websocket, path):
    """
    客户端连接处理    信息流：[客户端--->服务端]
    """
    log.info("[服务端] 客户端已连接 ---------------")
    jrt_clients.add(websocket)
    try:
        async for message in websocket:
            log.info(f"[请求][客户端] ---> [服务端]: {message}")

            try:
                data = json.loads(message)
            except Exception as err:
                log.error(f"[服务端] 解析客户端消息失败: {err}")
                log.error(f"[服务端] 错误堆栈信息: {traceback.format_exc()}")
                await websocket.send(json.dumps({"type": "closeConnection"}))
                await websocket.close()
                break

            if data.get("type") == "getStatus":
                response = get_status_data()
                ret_res = json.dumps(response, ensure_ascii=False)
                log.info(f"[响应][服务端] ---> [客户端]: {ret_res}")
                await websocket.send(ret_res)
            elif data.get("type") == "ping":
                response = json.dumps({"type": "pong"})
                log.info(f"[响应][服务端] ---> [客户端]: {response}")
                await websocket.send(response)
            elif data.get("type") == "closeConnection":
                response = json.dumps({"type": "closeConnection"})
                log.info(f"[响应][服务端] ---> [客户端]: {response}")
                await websocket.send(response)
                await websocket.close()
                break

    except websockets.exceptions.ConnectionClosed:
        log.info("[服务端] 客户端断开连接")
    finally:
        jrt_clients.discard(websocket)
        log.info("[服务端] 客户端已断开连接 ---------------")


async def start_server():
    # 本地测试
    server = await websockets.serve(handle_client, "0.0.0.0", 9393)
    print("[服务端] WebSocket 服务已启动，监听端口 9393")
    await asyncio.gather(server.wait_closed(), notify_status_update())


class WebSocketServerThread(QThread):
    # server_stopped = pyqtSignal()

    def __init__(self, host: str = "0.0.0.0", port: int = 9393):
        super().__init__()
        self.host = host
        self.port = port

        self.loop = None
        self.server = None

    def run(self):
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_until_complete(self.start_server())
        except Exception as e:
            log.error(f"[服务端] 启动WebSocket服务出错: {e}")
            log.error(f"[服务端] 错误堆栈信息: {traceback.format_exc()}")
            queue1.put(False)

    async def start_server(self):
        xglobal.global_data["jrt_server_is_start"] = True
        self.server = await websockets.serve(handle_client, self.host, self.port)
        log.info(f"[服务端] WebSocket 服务已启动，监听端口 {self.port}")
        queue1.put(True)

        await asyncio.gather(self.server.wait_closed(), notify_status_update())

    def stop(self):
        log.info("[服务端] 正在停止 WebSocket 服务...")

        try:
            xglobal.global_data["jrt_server_is_start"] = False
            # 断开所有客户端
            asyncio.run_coroutine_threadsafe(self._close_all_clients(), self.loop)
            # 停止服务器
            if self.server:
                self.server.close()
                asyncio.run_coroutine_threadsafe(self.server.wait_closed(), self.loop)

            time.sleep(0.2)

            # self.loop.stop()
            self.loop.call_soon_threadsafe(self.loop.stop)  # 安全停止事件循环

            log.info(f"[服务端] WebSocket 服务已停止")
        except Exception as err:
            log.error(f"[服务端] 停止 WebSocket 服务时出错: {err}")
            log.warning(f"{traceback.format_exc()}")

        finally:
            self.quit()

    async def _close_all_clients(self):
        log.warning(f"断开所有客户端连接！")
        for ws in list(jrt_clients):
            try:
                await ws.send(json.dumps({"type": "closeConnection"}))
                await ws.close()
            except:
                pass
        jrt_clients.clear()


if __name__ == '__main__':
    # 启动服务端
    asyncio.run(start_server())
