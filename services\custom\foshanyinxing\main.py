# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/17 下午3:03
# Author     ：sch
# version    ：python 3.8
# Description：银星/佛山银星
"""
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine
from services.custom.wuximengchuang.main import alarm_info


def sn_parse_result(standar_qty: int, serials: list, useq: list) -> dict:
    all_positions = set(range(1, standar_qty + 1))
    # 判断叉板位置（不在USEQ中的序号）
    fork_positions = sorted(all_positions - set(useq))
    # 需要分配条码的有效位置（在USEQ中的序号）
    valid_positions = sorted(all_positions & set(useq))
    # 初始化结果字典（默认所有位置为空）
    result = {pos: "" for pos in all_positions}

    # 分配条码到有效位置
    for idx, pos in enumerate(valid_positions):
        result[pos] = serials[idx]
    # 标记叉板位置（可选）
    for pos in fork_positions:
        result[pos] = "BadBoard"
    return result


class Engine(ErrorMapEngine):
    version = {
        "title": "foshanyinxing release v1.0.0.6",
        "device": "401,501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-17 16:51  条码校验，上传数据
date: 2024-07-26 18:14  增加从MES获取条码功能
date: 2024-10-16 11:24  如果发送mes失败，则主软件需要停机报警
date: 2025-05-24 11:33  ATAOI_2019-31555:获取条码时兼容坏板
date: 2025-05-26 15:38  获取条码接口返回STANDAR_QTY为str，转为int
date: 2025-05-27 10:46  坏的拼板，不需要调用条码校验和上传数据
""", }

    other_form = {
        "api_url": {
            "ui_name": "服务器地址",
            "value": "http://192.168.100.252:6688",
        },
        "org_id": {
            "ui_name": "工厂",
            "value": "8400",
        },
        "station_1": {
            "ui_name": "Station(1轨)",
            "value": "",
        },
        "station_2": {
            "ui_name": "Station(2轨)",
            "value": "",
        },
        "workstation_1": {
            "ui_name": "WorkStation(1轨)",
            "value": "",
        },
        "workstation_2": {
            "ui_name": "WorkStation(2轨)",
            "value": "",
        },
    }

    form = {
        "username": {
            "ui_name": "操作员",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        org_id = other_vo.get_value_by_cons_key("org_id")
        station_1 = other_vo.get_value_by_cons_key("station_1")
        station_2 = other_vo.get_value_by_cons_key("station_2")

        sn_list = other_vo.list_sn()
        track_index = other_vo.get_track_index()

        if track_index == 1:
            station = station_1
        else:
            station = station_2

        ret_res = self.x_response()

        check_url = f"{api_url}/api/process/scan/pass_station_check"

        err_list = []
        ix = 0
        for sn in sn_list:
            ix += 1
            if sn == "BadBoard":
                self.log.info(f"条码校验跳过坏板：NO：{ix}")
                continue
            check_param = {
                "ORG_ID": org_id,
                "STATION": station,
                "BARCODE": sn
            }

            ret = xrequest.RequestUtil.post_json(check_url, check_param)
            if not ret.get("success"):
                err_list.append(f"No:{ix} Error:{ret.get('message', {}).get('content')}")

        if err_list:
            err_msg = "\n".join(err_list)
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{err_msg}")

        return ret_res

    @alarm_info
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        org_id = data_vo.get_value_by_cons_key("org_id")
        station_1 = data_vo.get_value_by_cons_key("station_1")
        station_2 = data_vo.get_value_by_cons_key("station_2")
        workstation_1 = data_vo.get_value_by_cons_key("workstation_1")
        workstation_2 = data_vo.get_value_by_cons_key("workstation_2")
        username = data_vo.get_value_by_cons_key("username")

        data_url = f"{api_url}/api/process/scan/post_test_data"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if pcb_entity.track_index == 1:
            station = station_1
            work_station = workstation_1
        else:
            station = station_2
            work_station = workstation_2

        err_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no
            if barcode == "BadBoard":
                self.log.info(f"坏板数据不上传：NO：{board_no}")
                continue
            comp_data_list = []

            comp_ng_code = []
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_ng_code.append(comp_entity.repair_ng_code)

                alg_list1 = []
                alg_list2 = []
                alg_list3 = []
                alg_list4 = []
                for alg_entity in comp_entity.yield_alg_entity():
                    alg_min = alg_entity.min_threshold
                    alg_max = alg_entity.max_threshold

                    try:
                        alg_std = (float(alg_max) + float(alg_min)) / 2
                    except Exception as err:
                        self.log.warning(f"请平均数失败，error：{err}")
                        alg_std = alg_max

                    alg_list1.append(alg_entity.test_val)
                    alg_list2.append(alg_min)
                    alg_list3.append(alg_max)
                    alg_list4.append(alg_std)

                comp_data_list.append({
                    "TEST_ITEM": comp_entity.designator,
                    "TEST_VALUE": alg_list1,
                    "STANDAR_VALUE": alg_list4,
                    "MAX_VALUE": alg_list3,
                    "MIN_VALUE": alg_list2,
                    "ITEM_RESULT": comp_entity.repair_ng_str,
                    "REMARK": ""
                })

            data_param = {
                "ORG_ID": org_id,
                "WORKSTATION": work_station,
                "STATION": station,
                "USERNAME": username,
                "BARCODE": barcode,
                "QC_RESULT": board_entity.get_repair_result("OK", "NG"),
                "DEFECT_CODE": comp_ng_code,
                "TEST_DATA": comp_data_list
            }

            ret = xrequest.RequestUtil.post_json(data_url, data_param)
            if not ret.get("success"):
                err_list.append(f"No:{board_no} Error:{ret.get('message', {}).get('content')}")

        if err_list:
            err_msg = "\n".join(err_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_msg}")

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        org_id = other_vo.get_value_by_cons_key("org_id")

        get_sn_url = f"{api_url}/api/interface/equipment/querysplic"

        get_sn_param = {
            "orgid": org_id,
            "sn": other_vo.get_pcb_sn()
        }

        ret = xrequest.RequestUtil.get(get_sn_url, get_sn_param)
        if not ret.get("success"):
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret.get('message', {}).get('content')}")

        serials = ret.get("data", {}).get("SERIALS")
        standar_qty = ret.get("data", {}).get("STANDAR_QTY")
        useq = ret.get("data", {}).get("USEQ")

        result = sn_parse_result(int(standar_qty), serials, useq)
        return self.x_response("true", ",".join(result.values()))
