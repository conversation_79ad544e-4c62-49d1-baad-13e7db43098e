# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/15 上午10:02
# Author     ：sch
# version    ：python 3.8
# Description：常州大茂/伟瑞柯车灯
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

txt_pcb_panel_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
拼板检测NG数量:{pcb_board_robot_ng_number}
拼板复判NG数量:{pcb_board_user_ng_number}
拼板误报数量:{pcb_board_repass_number}
器件总数:{pcb_comp_number}
器件检测NG总数:{pcb_comp_robot_ng_number}
器件复判NG总数:{pcb_comp_user_ng_number}
器件误报总数:{pcb_comp_repass_number}

{BoardData}
"""

txt_board_panel_template = """
========================
拼板号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}{CompData}
"""

txt_comp_panel_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测类型:{alg_name}----标准值:{alg_min}~{alg_max}----下限:{alg_min}----上限:{alg_max}----实际检测值:{alg_val}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "changzhoudamao release v1.0.0.1",
        "device": "630",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-15 14:37  上传数据
""", }

    path = {
        "save_path": {
            "ui_name": "数据路径",
            "value": ""
        },
        "save_path_img": {
            "ui_name": "整板图路径",
            "value": ""
        },
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path = data_vo.get_value_by_cons_key("save_path")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        pcb_sn = pcb_entity.pcb_barcode

        board_data_str = ''
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data_str = ''
            for comp_entity in board_entity.yield_comp_entity():
                for alg_entity in comp_entity.yield_alg_entity():
                    comp_data_str += txt_comp_panel_template.format(**{
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                        "alg_name": alg_entity.test_name,
                        "alg_min": alg_entity.min_threshold,
                        "alg_max": alg_entity.max_threshold,
                        "alg_val": alg_entity.test_val
                    })

            board_data_str += txt_board_panel_template.format(**{
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "CompData": comp_data_str,
            })

        pcb_content = txt_pcb_panel_template.format(**{
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "BoardData": board_data_str
        })

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        xutil.FileUtil.write_content_to_file(f'{save_path}/{pcb_sn}_{time_file}.txt', pcb_content)

        img_file_path = f'{save_path_img}/{pcb_sn}_{time_file}.jpg'
        pcb_img_list = pcb_entity.list_all_pcb_image()
        if len(pcb_img_list) >= 1:
            xutil.FileUtil.copy_file(pcb_img_list[0], img_file_path)

        return self.x_response()
