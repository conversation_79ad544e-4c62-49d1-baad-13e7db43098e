# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/1/8 下午4:08
# Author     ：sch
# version    ：python 3.8
# Description：
"""
# from datetime import datetime
#
# for i in range(10):
#     print(datetime.now())

# ret_str = "OK,\\\\**********\\RI-CCD\\202403\\1386122\\1386122-038-0996"
# if ret_str.startswith("OK"):
#     result1, data1 = ret_str.split(",")
#     print(f"result: {result1}")
#     print(f"path: {data1}")
#
#     data_list = data1.split('\\')
#     print(data_list)
#     f1 = data_list[-3]
#     f2 = data_list[-2]
#     f3 = data_list[-1]
#     print(f"{f1=}")
#     print(f"{f2=}")
#     print(f"{f3=}")
# else:
#     print("未获取到图片地址")
from common import xutil

data_ret = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">ok</string>"""

ret = xutil.XmlUtil.get_xml_root_by_str(data_ret)
ret_str = ret.text
print(ret_str)
