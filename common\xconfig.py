# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xconstant.py
# Time       ：2022/12/30 上午10:06
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import os

window_dll_server_port = 9099

pwd_dir = os.getcwd()
cache_path = f"{pwd_dir}/cache_data"

home_dir = os.path.expanduser('~')
leichen_dir = f"{home_dir}/.config/Leichen"
log_dir = f"{home_dir}/.aoi/log/pymes_log"

os.makedirs(leichen_dir, exist_ok=True)
os.makedirs(cache_path, exist_ok=True)
os.makedirs(log_dir, exist_ok=True)

mesconfig_file = f"{leichen_dir}/mesconfig.json"
mes_ini_file = f"{leichen_dir}/MES.ini"
mes_cache_data = f"{leichen_dir}/mes_cache_data.json"
mes_limit_cache_data = f"{cache_path}/limit_cache_data.json"
mes_error_code_map = f"{leichen_dir}/mes_error_code_map.json"
pymes_log_file = f"{log_dir}/pymes.log"

backed_task_port = 9125

mes_content = """
[MesMain]
MesSelect=-
NoRemoveRepeat=false
SendFailedShowTips=false
SendResultToWorkStation=false
SendTogether=false
WorkStationIp=
"""

# 设置发件人和收件人
email_sender = "<EMAIL>"
email_auth_key = "xxxxxxxxxxxxxx"
email_recipient = ['<EMAIL>']

common_version = "3"  # 改动到大框架的时候，可以更新一下这个版本号, 或者通用框架功能更新，都可以往上更新

# 大版本更新历史
# 20240328 增加通用版本号
# coming soon
