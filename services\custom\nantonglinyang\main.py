"""
# File       : main.py
# Time       ：2025/07/18 10:40
# Author     ："wxc"
# version    ：python 3.8
# Description：南通林洋
"""
import os
from typing import Any

import boto3
import botocore

from common import xrequest, xutil, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo

user_ticker = "9C3B3847A32D92E4EAB3DC984E4222187473B6909F9AFE4ED80E5AA743858A2E25B0A48A5AB351C49CB68F82BE92C0303AAAD6B5B5FC9A4A5EC56A47F52BEBD84CE2FE1D92D1F02A385CD4E08826C4A771F65C339A0C5E18 "


def upload_object(body, bucket_name, object_name, access_key, secret_key, end_point):
    config = botocore.config.Config(
        connect_timeout=1200,  # 建立连接的超时时间（单位：秒）
        max_pool_connections=20,  # 允许打开的最大HTTP连接数
        retries={"max_attempts": 4},  # 请求失败后最大的重试次数
        s3={'addressing_style': 'path'}  # EDS公有云只支持子域名的方式
    )
    try:
        s3client = boto3.client(
            's3',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            endpoint_url=end_point,
            config=config
        )
        response = s3client.put_object(Body=body, Bucket=bucket_name, Key=object_name)
    except Exception as e:
        raise e


class Engine(ErrorMapEngine):
    version = {
        "customer": ["南通林洋", "nantonglinyang"],
        "version": "release v1.0.0.1",
        "device": "AIS50X",
        "feature": ["发送数据", "上传图片"],
        "author": "wxc",
        "release": """
date: 2025-07-18 10:42 ATAOI_2019-40620：发送数据和上传图片
"""
    }
    form = {
        "mo_code": {
            "ui_name": "工单号",
            "value": "",
        },
        "line_id": {
            "ui_name": "线体",
            "value": "",
        },
        "station": {
            "ui_name": "工序代码",
            "value": "",
        },
        "user_id": {
            "ui_name": "工号",
            "value": "",
        },

    }
    other_form = {
        "api_url": {
            "ui_name": "接口地址",
            "value": "http://192.168.0.100/OrBitWCFServiceR16/OrBitWebAPI.ashx"
        },
        "access_key": {
            "ui_name": "access_key",
            "value": ""
        },
        "secret_key": {
            "ui_name": "secret_key",
            "value": ""
        },
        "end_point": {
            "ui_name": "end_point",
            "value": ""
        },
        "bucket_name": {
            "ui_name": "bucket_name",
            "value": ""
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        mo_code = other_vo.get_value_by_cons_key("mo_code")
        line_id = other_vo.get_value_by_cons_key("line_id")
        station = other_vo.get_value_by_cons_key("station")
        user_id = other_vo.get_value_by_cons_key("user_id")
        api_url = other_vo.get_value_by_cons_key("api_url")

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            data = {
                "mo_code": mo_code,
                "line_id": line_id,
                "station": station,
                "user_id": user_id,
                "sn": sn
            }
            param = {
                "UserTicket": user_ticker,
                "API": "LYAssembleCommonCollectDIP",
                "UserData": data,
                "OutType": "JSON"
            }

            ret = xrequest.RequestUtil.post_form(api_url, param)
            ret_data = ret.get("SQLDataSet")
            if ret_data[0].get("R_TYPE") != "true":
                return self.x_response("false", f"mes接口异常，error：{ret_data[0].get('R_MSG')}")
        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        access_key = data_vo.get_value_by_cons_key("access_key")
        secret_key = data_vo.get_value_by_cons_key("secret_key")
        end_point = data_vo.get_value_by_cons_key("end_point")
        bucket_name = data_vo.get_value_by_cons_key("bucket_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT5)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            for comp_entity in board_entity.yield_comp_entity():
                image_path = comp_entity.image_path
                if comp_entity.is_robot_ng() and xutil.FileUtil.file_is_exists(image_path):
                    image_file_name = os.path.basename(image_path)
                    user_data = {
                        "PCBSN": barcode,  # PCB条码（必填）
                        "SITE": comp_entity.designator,  # 位号
                        "BadInformation": comp_entity.repair_ng_str,  # 不良描述
                        "FileName": image_file_name,  # 文件名（必填）
                        "CarrierLotSN": "",  # 载具编号
                        "CheckDate": test_time  # 检测时间（必填）
                    }
                    try:
                        upload_object(
                            body=open(image_path, 'rb'),
                            bucket_name=bucket_name,
                            object_name=image_file_name,
                            access_key=access_key,
                            secret_key=secret_key,
                            end_point=end_point
                        )
                    except Exception as e:
                        return self.x_response("false", f"mes接口异常，S3上传文件失败: {e}")
                    param = {
                        "UserTicket": user_ticker,
                        "API": "LYAOIPhotoUPLoad",
                        "UserData": user_data,
                        "OutType": "JSON"
                    }
                    ret = xrequest.RequestUtil.post_form(api_url, param)
                    ret_data = ret.get("SQLDataSet")
                    if ret_data[0].get("R_TYPE") != "true":
                        return self.x_response("false", f"mes接口异常，error：{ret_data[0].get('R_MSG')}")
        return self.x_response()
