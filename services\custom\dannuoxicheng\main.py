# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/23 下午3:18
# Author     ：sch
# version    ：python 3.8
# Description：丹诺西城
"""

from typing import Any

from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine
from common.xutil import complete_array


class Engine(BaseEngine):
    version = {
        "title": "dannuoxicheng release v1.0.0.2",
        "device": "40x",
        "feature": ["获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-23 15:18  init
date: 2024-07-17 16:10  del -1,-2 barcode
""", }

    form = {
        "split_index": {
            "ui_name": "切割后N位",
            "value": "4"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        split_index = other_vo.get_value_by_cons_key("split_index")
        try:
            split_index = int(split_index)
        except Exception as err:
            return self.x_response("false", f"切割后N位必须为数字！error：{err}")

        barcode_map = other_vo.get_barcode_map()

        del barcode_map["-1"]
        del barcode_map["-2"]

        self.log.info(f"barcode map: {barcode_map}")

        scan_sn = list(barcode_map.values())

        if not any(scan_sn):
            return self.x_response("false", "未扫到任何条码，无法补录！")

        self.log.info(f"补全前的条码列表：{scan_sn}")
        new_sn_list = complete_array(scan_sn, split_index=split_index)
        self.log.info(f"补全后的条码列表：{scan_sn}")

        return self.x_response("true", ",".join(new_sn_list))
