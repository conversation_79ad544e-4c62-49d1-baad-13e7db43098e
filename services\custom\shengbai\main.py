# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/23 下午4:25
# Author     ：sch
# version    ：python 3.8
# Description：胜百
"""

from typing import Any

from common import xcons, xrequest, xutil
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "shengbai release v1.0.0.1",
        "device": "40x",
        "feature": ["上传设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-23 16:39  上传设备状态，上传数据 
""", }

    form = {
        "api_url": {
            "ui_name": "上传数据接口URL",
            "value": ""
        },

        "device_status_url": {
            "ui_name": "设备状态接口URL",
            "value": ""
        },

        "mo_name": {
            "ui_name": "工单号",
            "value": ""
        },
        "device_number": {
            "ui_name": "AOI设备编号",
            "value": ""
        }
    }

    other_form = {
        "log_number": {
            "ui_name": "单次日志输出",
            "value": "999999"
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        mo_name = data_vo.get_value_by_cons_key("mo_name")
        device_number = data_vo.get_value_by_cons_key("device_number")

        pcb_entity = data_vo.pcb_entity

        log_number = data_vo.get_value_by_cons_key("log_number")
        try:
            log_number = int(log_number)
        except Exception as err:
            return self.x_response("false", f"单次日志输出必须为数字！error:{err}")

        self.log.info(pcb_entity)

        board_data = []
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0

        board_robot_ng_count = 0
        board_repair_ng_count = 0

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not board_entity.robot_result:
                board_robot_ng_count += 1

            if not board_entity.repair_result:
                board_repair_ng_count += 1

            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                comp_data.append({
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_entity.board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        pcb_data = {
            "MOName": mo_name,
            "device_number": device_number,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_repair_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,
            "board_data": board_data
        }

        ret = xrequest.RequestUtil.post_json(api_url, pcb_data, log_number=log_number)
        # if not ret.get("result"):
        #     return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_status_url = other_vo.get_value_by_cons_key("device_status_url")
        device_number = other_vo.get_value_by_cons_key("device_number")

        status_str = other_vo.get_device_status_str()

        req_param = {
            "device_number": device_number,
            "log_type": "设备运行日志",
            "upload_time": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
            "log_info": status_str
        }
        ret = xrequest.RequestUtil.post_json(device_status_url, req_param)

        return self.x_response()
