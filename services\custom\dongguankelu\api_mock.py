# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : api_mock.py
# Time       ：2024/10/21 上午10:46
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import uvicorn
from fastapi import FastAPI

app = FastAPI()


@app.post("/api/OrBitMesInterface/PutData")
def put_data():
    return {
        "code": 1,
        "message": "批号【21030000FK9J】节点正确",
        "details": None,
        "data": {
            "Result": [
                {
                    "ErrorSN": "21030000FK9J",
                    "ErrorMsg": "生产工单的工作中心【】与工序的工作中心【DIP-A线】不一致！"
                },
                {
                    "ErrorSN": "21030000FK9J",
                    "ErrorMsg": "生产工单的工作中心【】与工序的工作中心【DIP-A线】不一致！"
                },
                {
                    "ErrorSN": "21030000FK9J",
                    "ErrorMsg": "生产工单的工作中心【】与工序的工作中心【DIP-A线】不一致！"
                }
            ]
        },
        "validationErrors": None
    }


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8081)
