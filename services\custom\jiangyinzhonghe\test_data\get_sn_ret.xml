<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetMeterInfoBySNResponse xmlns="http://tempuri.org/">
            <GetMeterInfoBySNResult xmlns:a="http://schemas.datacontract.org/2004/07/Soas.Model.Entities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:AccuracyLevel i:nil="true"/>
                <a:Constant i:nil="true"/>
                <a:Current i:nil="true"/>
                <a:DoWhatList i:nil="true"/>
                <a:Frequency i:nil="true"/>
                <a:MaterialNo i:nil="true"/>
                <a:MeterModel i:nil="true"/>
                <a:OrderName i:nil="true"/>
                <a:OrderNo i:nil="true"/>
                <a:Phase i:nil="true"/>
                <a:SN i:nil="true"/>
                <a:SNType i:nil="true"/>
                <a:SchemeList i:nil="true"/>
                <a:Voltage i:nil="true"/>
                <a:WorkOrder i:nil="true"/>
            </GetMeterInfoBySNResult>
            <basedata>false</basedata>
            <result>false</result>
            <message>主条码2301PP110002工序不正确!原因:无此岗位采集点 (Pro_Checkroute:p_type=1;p_Point_Code=50ZH01HJ0202;p_station_code=S0000007;p_r_Sn=2301PP110002;p_t_Sn=2301AA109993;p_s_Sn=2301AA109993)</message>
        </GetMeterInfoBySNResponse>
    </s:Body>
</s:Envelope>