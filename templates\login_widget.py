# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ui/login_widget.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_LoginWidget(object):
    def setupUi(self, LoginWidget):
        LoginWidget.setObjectName("LoginWidget")
        LoginWidget.resize(370, 199)
        self.verticalLayout = QtWidgets.QVBoxLayout(LoginWidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame = QtWidgets.QFrame(LoginWidget)
        self.frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.line_username = QtWidgets.QLineEdit(self.frame)
        self.line_username.setObjectName("line_username")
        self.verticalLayout_2.addWidget(self.line_username)
        self.line_password = QtWidgets.QLineEdit(self.frame)
        self.line_password.setText("")
        self.line_password.setEchoMode(QtWidgets.QLineEdit.Password)
        self.line_password.setObjectName("line_password")
        self.verticalLayout_2.addWidget(self.line_password)
        self.check_box_remember_password = QtWidgets.QCheckBox(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.check_box_remember_password.sizePolicy().hasHeightForWidth())
        self.check_box_remember_password.setSizePolicy(sizePolicy)
        self.check_box_remember_password.setChecked(True)
        self.check_box_remember_password.setObjectName("check_box_remember_password")
        self.verticalLayout_2.addWidget(self.check_box_remember_password)
        self.check_auto_login = QtWidgets.QCheckBox(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.check_auto_login.sizePolicy().hasHeightForWidth())
        self.check_auto_login.setSizePolicy(sizePolicy)
        self.check_auto_login.setObjectName("check_auto_login")
        self.verticalLayout_2.addWidget(self.check_auto_login)
        self.btn_login = QtWidgets.QPushButton(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_login.sizePolicy().hasHeightForWidth())
        self.btn_login.setSizePolicy(sizePolicy)
        self.btn_login.setObjectName("btn_login")
        self.verticalLayout_2.addWidget(self.btn_login)
        self.verticalLayout.addWidget(self.frame)

        self.retranslateUi(LoginWidget)
        QtCore.QMetaObject.connectSlotsByName(LoginWidget)

    def retranslateUi(self, LoginWidget):
        _translate = QtCore.QCoreApplication.translate
        LoginWidget.setWindowTitle(_translate("LoginWidget", "登录"))
        self.line_username.setPlaceholderText(_translate("LoginWidget", "账号"))
        self.line_password.setPlaceholderText(_translate("LoginWidget", "密码"))
        self.check_box_remember_password.setText(_translate("LoginWidget", "记住密码"))
        self.check_auto_login.setText(_translate("LoginWidget", "下次启动自动登录 (勾选此设置必须记住密码)"))
        self.btn_login.setText(_translate("LoginWidget", "登录"))
