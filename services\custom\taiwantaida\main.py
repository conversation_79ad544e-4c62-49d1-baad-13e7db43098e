# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/19 下午2:30
# Author     ：sch
# version    ：python 3.8
# Description：台湾台达
"""
import time
from typing import Any

from common import xrequest, xutil
from common.xglobal import global_data
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine
from services.custom.taiwantaida import taida_module

route_template = """{sn}}}{mo_number}}}{model_name}}}{line_name}}}{section_name}}}{group_name}}}{station_name}}}{error_code}}}{result}}}{emp}}}{machine_no}}}{test_program}}}{error_desc}}}}}}}{jig1}}}}}}}{error_mark}}}"""
TOKENID = "894A0F0DF8494799E0530CCA940AC604"
"""
开发日志：

20241113  
    1. 红灯状态(1/10/11)在待机信号进来之前，不可以被其他信号覆盖
    2. statusCode需要传和客户协商好的状态码  -->  https://kb.cvte.com/x/X-C0Gg
    3. 传整板条码/治具条码  todo, 待确认传到哪个参数

"""


class Engine(ErrorMapEngine):
    version = {
        "title": "taiwantaida release v1.0.0.8",
        "device": "40x",
        "feature": ["条码校验", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-19 14:30  init
date: 2024-09-19 16:56  条码校验，上传数据，设备状态
date: 2024-10-16 16:29  bugfix: 没有传递errorCut,errorTime  +  runningTime传绿灯时间
date: 2024-10-18 15:52  JIG1传治具条码
date: 2024-10-31 19:48  statusCode传的代码和status一样
date: 2024-11-13 15:55  红灯状态(1/10/11)在待机信号进来之前，不可以被其他信号覆盖, statusCode需要传和客户协商好的状态码
date: 2024-11-14 19:28  整板条码和治具条码都要输出给mes，并且改为按整板发送
""", }

    form = {
        "window_ip": {
            "ui_name": "windowIp",
            "value": "",
        },
        "factory": {
            "ui_name": "Factory(工厂)",
            "value": "",
        },
        "machine_no": {
            "ui_name": "Machine_No(機器編號/設備編號)",
            "value": "",
        },
        "emp": {
            "ui_name": "Emp (測試人)",
            "value": "",
        },
        "model_name": {
            "ui_name": "Model Name(機種)",
            "value": "",
        },
        "mo_number": {
            "ui_name": "MO Number(工單)",
            "value": "",
        },
        "line_name": {
            "ui_name": "Line Name(線別)",
            "value": "",
        },
        "section_name": {
            "ui_name": "Section Name(段別)",
            "value": "",
        },
        "group_name": {
            "ui_name": "Group Name(組别)",
            "value": "",
        },
        "station_name": {
            "ui_name": "Station Name(站別)",
            "value": "",
        },
    }

    button = {
        "testConnect": {
            "ui_name": "连接window"
        },
        "maintain": {
            "ui_name": "保养"
        },
    }

    def __init__(self):
        global_data["device_run_time"] = int(time.time())  # 设备的启动时间
        log.info(f"cache run time!")

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 1 * 60)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        factory = other_vo.get_value_by_cons_key("factory")
        model_name = other_vo.get_value_by_cons_key("model_name")
        mo_number = other_vo.get_value_by_cons_key("mo_number")
        line_name = other_vo.get_value_by_cons_key("line_name")
        section_name = other_vo.get_value_by_cons_key("section_name")
        group_name = other_vo.get_value_by_cons_key("group_name")
        station_name = other_vo.get_value_by_cons_key("station_name")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            routing_data = f"{sn}}}{mo_number}}}{model_name}}}{line_name}}}{section_name}}}{group_name}}}{station_name}}}}}}}"
            body_data = {
                "factory": factory,
                "testType": "ROUTING_CHECK",
                "routingData": routing_data,
                "testData": []
            }

            packet_data = {
                "type": 1,
                "request_header": {"TokenID": TOKENID},
                "request_param": {},
                "request_body": body_data
            }
            ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)

            if not ret.get("result"):
                log.warning(f"ret: {ret}")
                ret_res = self.x_response("false", f"Mes API Exception，check barcode error：{ret.get('string')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        window_ip = data_vo.get_value_by_cons_key("window_ip")
        factory = data_vo.get_value_by_cons_key("factory")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        emp = data_vo.get_value_by_cons_key("emp")
        model_name = data_vo.get_value_by_cons_key("model_name")
        mo_number = data_vo.get_value_by_cons_key("mo_number")
        line_name = data_vo.get_value_by_cons_key("line_name")
        section_name = data_vo.get_value_by_cons_key("section_name")
        group_name = data_vo.get_value_by_cons_key("group_name")
        station_name = data_vo.get_value_by_cons_key("station_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        origin_review_path = data_vo.get_review_path()

        cycle_time = pcb_entity.get_cycle_time()
        taida_module.cache_maintain_info(cycle_time, origin_review_path)

        pcb_sn = pcb_entity.pcb_barcode

        ret_res = self.x_response()

        error_code = []
        error_desc = []
        error_mark = []
        test_data = []

        for board_entity in pcb_entity.yield_board_entity():
            log.info(board_entity)

            barcode = board_entity.barcode
            # board_no = board_entity.board_no

            if barcode:
                global_data["last_barcode"] = barcode
                global_data["last_project_name"] = pcb_entity.project_name

            # if not barcode:
            #     parse_barcode = taida_module.get_barcode_by_robot_xml(origin_review_path, board_no)
            #     log.info(f"barcode: {barcode} parse barcode: {parse_barcode}")
            #     barcode = parse_barcode

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.repair_result:
                    # 人工复判OK的不传
                    continue

                robot_ng_code = comp_entity.robot_ng_code
                repair_ng_code = comp_entity.repair_ng_code
                repair_ng_str = comp_entity.repair_ng_str

                if repair_ng_code != "0":
                    error_code.append(repair_ng_code)
                    error_desc.append(repair_ng_str)
                    error_mark.append(comp_entity.designator)

                test_item = []

                for alg_entity in comp_entity.yield_alg_entity():
                    if alg_entity.result != "0":
                        test_item = [comp_entity.designator, robot_ng_code]
                        break

                test_data.append(test_item)

        if pcb_sn:
            global_data["last_barcode"] = pcb_sn
            global_data["last_project_name"] = pcb_entity.project_name

        routing_data = route_template.format(**{
            "sn": pcb_sn,  # 传整板条码
            "mo_number": mo_number,
            "model_name": model_name,
            "line_name": line_name,
            "section_name": section_name,
            "group_name": group_name,
            "station_name": station_name,
            "error_code": ";".join(error_code[:8]),
            "error_desc": ";".join(error_desc[:8]),
            "result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "emp": emp,
            "machine_no": machine_no,
            "test_program": pcb_entity.project_name,
            "jig1": pcb_entity.fixture_barcode,  # 传治具条码
            "error_mark": ";".join(error_mark[:8]),
        })
        test_type = "ROUTING_UPDATE"

        body_data = {
            "factory": factory,
            "testType": test_type,
            "routingData": routing_data,
            "testData": test_data[:8]
        }

        req_param = {}

        packet_data = {
            "type": 2,
            "request_header": {"TokenID": TOKENID},
            "request_param": req_param,
            "request_body": body_data
        }

        ret = xrequest.SocketUtil.x_socket_send_data(window_ip, packet_data)

        if not ret.get("result"):
            log.warning(f"ret: {ret}")
            ret_res = self.x_response("false", f"Mes API Exception，upload data error：{ret.get('string')}")

        return ret_res

    def send_panel_data(self, other_vo: OtherVo, main_window):
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        machine_no = other_vo.get_value_by_cons_key("machine_no")
        model_name = other_vo.get_value_by_cons_key("model_name")

        panel_info = other_vo.get_panel_info()
        self.log.info(f"panel info: {panel_info}")

        green_time = other_vo.json_data.get("greenLightTime", 0)
        global_data["green_time"] = green_time

        cache_data = xutil.CacheUtil.get_cache_data()
        cache_data["total_input_qty"] = panel_info.get("total_qty", 0)
        cache_data["total_pass_qty"] = panel_info.get("pass_qty", 0)
        cache_data["total_repass_qty"] = panel_info.get("repass_qty", 0)
        cache_data["total_fail_qty"] = panel_info.get("fail_qty", 0)

        ct_time = panel_info.get("CTTime", 0)
        pcb_result = panel_info.get("pcbResult", 0)

        ct_q = "0" if pcb_result else "1"

        product_time = float(ct_time)

        xutil.CacheUtil.save_cache_data(cache_data)

        taida_module.upload_device_status_v2(
            window_ip,
            machine_no,
            "01",
            cache_data,
            cycle_time=product_time,
            ct_q=ct_q,
            model_ui=model_name
        )

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        客户PQM状态码，   故障时，statusCode需要有值
        0	M	正常	正常生产
        1	M	故障	设备故障报警
        2	M	暂停	设备无异常报警，手动暂停
        3	M	待机	Bypass状态或者没有开始运行
        4	P	待料	开始运行，但没有板卡进板检测
        5	P	满料	检测完，但下游没有要板，出不了板
        7	P	换线	切换板式(自动切换、MES切换、手动切换)
        10	M	安全停机/安全门
        11	Q	品质停机	设备非故障报警，不良报警、流程报警等
        12	M	调机

        :param other_vo:
        :param main_window:
        :return:
        """
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        machine_no = other_vo.get_value_by_cons_key("machine_no")
        model_name = other_vo.get_value_by_cons_key("model_name")

        status_code = other_vo.get_status_code()

        green_time = other_vo.json_data.get("greenLightTime", 0)
        error_code = other_vo.json_data.get("errorCode", "")
        global_data["green_time"] = green_time

        is_stop = other_vo.get_status_is_stop()

        cache_data = xutil.CacheUtil.get_cache_data()

        is_send_v2_device_status = False
        if status_code in ["01", "03"]:
            # 开始运行/待机
            time_now = time.time()
            time_stop_delta = 0

            time_except = global_data.get("time_except", None)  # 异常时间点
            time_stop = global_data.get("time_stop", None)  # 停止检查时间点
            if time_except:
                # 如果有上一次异常时间，则算一下<故障总时长>，算完之后将异常时间清空
                time_except_delta = int(time_now - time_except)
                cache_data['time_except_count'] = int(cache_data.get('time_except_count', 0) + time_except_delta)

                xutil.CacheUtil.save_cache_data(cache_data)
                global_data['time_except'] = None

            if time_stop:
                time_stop_delta = int(time_now - time_stop)
                global_data['time_stop'] = None  # 时间点用过了之后需要删掉

            self.log.info(f"---wait time: {time_stop_delta}")
            is_send_v2_device_status = True

        elif status_code in ["02"]:
            # 停止检测
            global_data["time_stop"] = int(time.time())

            time_stop_delta = 0
            is_send_v2_device_status = True

        elif is_stop or status_code == "99":  # 停机异常
            """
            开发log
            如果之前 就有 造成设备停机的异常，则此次不需要上传异常状态
            1. 发送异常状态的前提是，上一次的设备状态 必须是正常运行的状态   （那一些设备状态可以去修改那个中间变量：开始，以及造成设备停机的设备状态）
            2. 发送异常的前提是，本次异常会造成设备停机，并且需要人为手动恢复
            """
            # 记录这个东西，只是为了 知道 此次的停机异常需不需要发送
            last_device_code = global_data.get("last_device_code")
            if last_device_code != "01":
                # self.log.info(f"上一次的状态码：{last_device_code} 不是开始运行，不需要上传PQM！")
                is_send_v2_device_status = True
                time_stop_delta = 0
                # return self.x_response()
            else:
                # 运行状态下并且报警之后需要人为干预的状态码 才需要上传PQM
                global_data["time_except"] = int(time.time())  # 记录异常时间点

                cache_data['except_number'] = cache_data.get('except_number', 0) + 1
                xutil.CacheUtil.save_cache_data(cache_data)

                time_stop_delta = 0
                is_send_v2_device_status = True
        elif status_code in ["04", "05", "07", "10", "11", "12"]:
            time_stop_delta = 0
            is_send_v2_device_status = True
        else:
            # 其他未定义的状态码，先不处理
            self.log.warning(f"未启用的状态码：{status_code}，不发送PQM!")
            time_stop_delta = 0

        if is_stop or status_code == "01":
            global_data["last_device_code"] = status_code

        if is_send_v2_device_status:
            taida_module.upload_device_status_v2(
                window_ip,
                machine_no,
                status_code,
                cache_data,
                wait_time=time_stop_delta,
                model_ui=model_name,
                error_code=error_code
            )

        return self.x_response()

    def send_idle_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        # window_ip = other_dao.get_value_by_cons_key("window_ip")
        # machine_no = other_dao.get_value_by_cons_key("machine_no")
        # model_name = other_dao.get_value_by_cons_key("model_name")
        #
        # green_time = other_dao.json_data.get("greenLightTime", 0)
        # global_data["green_time"] = green_time
        #
        # cache_data = xutil.CacheUtil.get_cache_data()
        # taida_module.upload_device_status_v2(
        #     window_ip,
        #     machine_no,
        #     "03",
        #     cache_data,
        #     wait_time=3 * 60,
        #     model_ui=model_name
        # )

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        window_ip = other_vo.get_value_by_cons_key("window_ip")
        machine_no = other_vo.get_value_by_cons_key("machine_no")
        model_name = other_vo.get_value_by_cons_key("model_name")

        # 每隔1分钟需要上传一次状态   ps：需要保持设备在线
        cache_data = xutil.CacheUtil.get_cache_data()
        taida_module.upload_device_status_v2(
            window_ip,
            machine_no,
            "",
            cache_data,
            wait_time=60,
            model_ui=model_name
        )

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        window_ip = btn_vo.get_value_by_cons_key("window_ip")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "testConnect":
            is_connect = xrequest.SocketUtil.check_window_port(window_ip)
            if not is_connect:
                return self.x_response("false", f"连接mes失败，请检查window中转程序是否正常运行！")

        elif btn_key == "maintain":
            cache_data = xutil.CacheUtil.get_cache_data()

            cache_data["total_qty"] = 0
            cache_data["total_cycle_time"] = 0
            cache_data["maintain_camera"] = []
            cache_data["maintain_light_source"] = []
            cache_data["maintain_lens"] = []
            cache_data["maintain_track_belt"] = []
            cache_data["maintain_servo_motor"] = []
            cache_data["maintain_cylinder"] = 0
            cache_data["maintain_solenoid_valve"] = 0

            xutil.CacheUtil.save_cache_data(cache_data)
            return self.x_response("true", "保养数据已清除！")

        return self.x_response()
