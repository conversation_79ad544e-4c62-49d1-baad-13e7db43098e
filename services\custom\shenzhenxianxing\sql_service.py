import pymssql

from common.xutil import log


class DatabaseManager:

    def __init__(self, db_host, db_username, db_password, db_name, db_port, timeout=1):
        """
        """
        self.db_host = db_host
        self.db_username = db_username
        self.db_password = db_password
        self.db_name = db_name
        self.db_port = db_port
        self.conn = None  # 初始化为 None

    def get_conn(self):
        if self.conn is None:
            self.conn = pymssql.connect(
                server=self.db_host,
                user=self.db_username,
                password=self.db_password,
                database=self.db_name,
                port=self.db_port
            )
        return self.conn

    def close(self):
        self.conn.close()

    def query_all_tables(self):
        """
        查询数据库创建的所有表
        """
        all_table = []

        try:
            sql_str = '''SELECT TABLE_NAME
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_TYPE = 'BASE TABLE';'''
            result = self._execute_sql(sql_str, fetch=True)

            for row in result:
                all_table.append(row.get("TABLE_NAME"))

        except Exception as e:
            log.warning(f"查询数据表失败，error：{e}")

        return all_table

    def create_daily_production_stats_table(self, table_name):
        """
        创建表
        :param table_name: 表名
        """
        all_tables = self.query_all_tables()
        if table_name not in all_tables:
            sql = f"""
        CREATE TABLE [dbo].[{table_name}] (
            id BIGINT IDENTITY(1,1) PRIMARY KEY,
            [date] DATE NOT NULL,
            machine VARCHAR(50) NOT NULL,
            board_side TINYINT NOT NULL,
            
            -- 拼板统计
            board_total INT NOT NULL DEFAULT 0,
            board_pass INT NOT NULL DEFAULT 0,
            board_ng INT NOT NULL DEFAULT 0,
            board_false INT NOT NULL DEFAULT 0,
            
            -- 器件统计
            comp_total INT NOT NULL DEFAULT 0,
            comp_pass INT NOT NULL DEFAULT 0,
            comp_ng INT NOT NULL DEFAULT 0,
            comp_false INT NOT NULL DEFAULT 0,
            FID INT NOT NULL DEFAULT 182892
        );
        
        -- 添加唯一约束（防止重复记录）
        ALTER TABLE daily_production_stats
        ADD CONSTRAINT UQ_date_board_side UNIQUE ([date], machine);
        
        -- 添加检查约束（确保数据一致性）
        ALTER TABLE daily_production_stats
        ADD CONSTRAINT CHK_panel_board 
        CHECK (board_total = board_pass + board_ng + board_false);
        
        ALTER TABLE daily_production_stats
        ADD CONSTRAINT CHK_component 
        CHECK (comp_total = comp_pass + comp_ng + comp_false);
        
        -- 创建索引
        CREATE INDEX IDX_date ON daily_production_stats ([date]);
        CREATE INDEX IDX_date_board_side ON daily_production_stats ([date], machine, board_side);"""
            self._execute_sql(sql)
            log.info(f"创建表 {table_name} 成功！")

    def query_daily_production_stats(self, table_name, date, machine, board_side):
        """
        查询每日生产统计数据
        :param table_name: 表名
        :param date: 日期
        :param machine: 设备名称
        :param board_side: 拼板侧面（1或2）
        :return: 查询结果列表
        """
        query_template = f"""
        SELECT * FROM [dbo].[{table_name}] 
        WHERE [date] = %s AND machine = %s AND board_side = %s;
        """

        params = (date, machine, board_side)

        return self._execute_sql(query_template, params, fetch=True)

    def update_daily_production_stats(self, table_name, data):
        """
        更新每日生产统计数据
        :param table_name: 表名
        :param data: 数据字典，包含所有字段
        """
        update_template = f"""
        UPDATE [dbo].[{table_name}] 
        SET board_total = %s, board_pass = %s, board_ng = %s, board_false = %s,
            comp_total = %s, comp_pass = %s, comp_ng = %s, comp_false = %s
        WHERE [date] = %s AND machine = %s AND board_side = %s;
        """

        params = (
            data.get("board_total"), data.get("board_pass"), data.get("board_ng"), data.get("board_false"),
            data.get("comp_total"), data.get("comp_pass"), data.get("comp_ng"), data.get("comp_false"),
            data.get("data_date"), data.get("machine"), data.get("board_side")
        )

        self._execute_sql(update_template, params)
        log.info(f"更新数据 成功")

    def insert_daily_production_stats(self, table_name, data):
        """
        插入每日生产统计数据
        :param table_name: 表名
        :param data: 数据字典，包含所有字段
        """
        insert_template = """
        INSERT INTO [dbo].[{table_name}] ([date], machine, board_side, 
            board_total, board_pass, board_ng, board_false,
            comp_total, comp_pass, comp_ng, comp_false) 
            VALUES (
            '{data_date}', '{machine}', {board_side},{board_total},{board_pass}, {board_ng}, {board_false},
            {comp_total}, {comp_pass}, {comp_ng}, {comp_false}
            )
            """

        data["table_name"] = table_name

        self._execute_sql(insert_template.format(**data))
        log.info(f"插入数据 成功")

    def _execute_sql(self, sql, params=None, fetch=False):
        """执行SQL语句（支持参数化查询）
        :param fetch: 是否返回查询结果（用于 SELECT）
        """
        conn = self.get_conn()
        cursor = conn.cursor(as_dict=True)  # 返回字典格式
        try:
            cursor.execute(sql, params)
            if fetch:
                result = cursor.fetchall()  # 如果是查询，返回结果
            else:
                conn.commit()  # 如果是 INSERT/UPDATE，提交事务
                result = None
            return result
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
