#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date: 2023/4/5
# Author: sunchangheng
"""
常量文件
"""

SOCKET_CLOSE_KEY = b'CLOSE_BY_EVENT'

INSPECTOR = "inspector"
REPAIR = "repair"

# SEARCH_FLAG = "CanSearch"   # 功能已移除！！！ ps：会导致打包体积变大，需要该功能，需自行 开立非标分支

EN_KEY = "EN"

# 日期常用格式
FMT_DATE0 = "%Y%m"
FMT_DATE = "%Y-%m-%d"
FMT_DATE1 = "%Y/%m/%d"
FMT_TIME = "%H:%M:%S"
FMT_TIME_FILE0 = "%Y%m%d"
FMT_TIME_FILE = "%Y%m%d%H%M%S"
FMT_TIME_FILE1 = "%Y%m%d%H%M%S%f"
FMT_TIME_DEFAULT = "%Y-%m-%d %H:%M:%S"
FMT_TIME_DEFAULT1 = "%Y/%m/%d %H:%M:%S"
FMT_TIME_DEFAULT2 = "%Y-%m-%d %H:%M:%S.%f"
FMT_TIME_DEFAULT3 = "%Y-%m-%dT%H:%M:%S.%f"
FMT_TIME_DEFAULT6 = "%Y-%m-%dT%H:%M:%S"
FMT_TIME_DEFAULT4 = "%Y-%m-%d-%H-%M-%S"
FMT_TIME_DEFAULT5 = "%Y-%m-%d %H:%M:%S.000"
FMT_TIME_DEFAULT8 = "%Y-%m-%dT%H:%M:%S.000+08:00"
FMT_TIME_DEFAULT7 = "%Y-%m-%d/%H"
FMT_TIME_DEFAULT9 = "%m/%d/%Y %H:%M"
FMT_TIME_DEFAULT10 = "%Y/%m"
FMT_TIME_DEFAULT11 = "%Y-%m-%dT%H:%M:%SZ"
FMT_TIME_DEFAULT12 = "%Y-%m-%d %H-%M-%S"
FMT_TIME_DEFAULT13 = "%H%M%S"
FMT_TIME_DEFAULT14 = "%Y/%m/%d %H:%M:%S +08:00"

# 设备状态错误码映射
DEVICE_STATUS = {
    "进板": "01",
    "开始检测": "02",
    "停止检查": "03",
    "出板": "04",
    "安全门": "10",
    "调试": "11",
    "数据超时": "20",
    "板卡 NG": "21",
    "紧急故障": "12",
    "流程错误": "13",
    "直通率告警": "22",
    "Marker 错误": "23",
    "其他错误": "99",
}

# 303/401/430/501/630/330
DEVICE_STATUS_V3 = {
    "1001": {"name": "进板", "desc": "板卡开始进板"},
    "1002": {"name": "启动", "desc": "手动运行检测程序"},
    "1003": {"name": "暂停", "desc": "手动停止检测程序"},
    "1004": {"name": "出板", "desc": "板卡离开设备"},
    "1005": {"name": "换线", "desc": "切换板式（自动切换、MES切换、手动切换）"},
    "2001": {"name": "急停", "desc": "操作设备急停按钮"},
    "2002": {"name": "安全门报警", "desc": "安全门打开"},
    "3001": {"name": "待料（空闲）", "desc": "开始运行，但是板卡没有进板检测超过一定时间"},
    "3002": {"name": "出板超时", "desc": "检测完成，但是板卡运到出板口后停留超过一定时间未出板"},
    "3003": {"name": "条码校验过站失败", "desc": "上传条码校验过站失败"},
    "3004": {"name": "上传检测信息失败", "desc": "上传检测结果到MES/EAP等失败"},
    "3005": {"name": "磁盘已满", "desc": "磁盘已满"},
    "3006": {"name": "掉板", "desc": "掉板"},
    "3007": {"name": "EAP告警", "desc": "EAP告警"},
    "4001": {"name": "直通率告警", "desc": "直通率告警"},
    "4002": {"name": "Mark点错误", "desc": "检测板卡有Mark点报错"},
    "4003": {"name": "板卡NG", "desc": "检测板卡为NG"},
    "5001": {"name": "风扇停转", "desc": "监控到风扇停转"},
    "5002": {"name": "相机连接失败", "desc": "监控到相机连接失败"},
    "0001": {"name": "蜂鸣报警解除", "desc": "主动关闭蜂鸣器报警"},

    # 20250512新增， 先进主软件才有
    "1011": {"name": "检测中", "desc": "检测中"},
    "1012": {"name": "检测第二段", "desc": "检测第二段"},
    "1013": {"name": "人工复检", "desc": "人工复检"},  # 等待人工复检（机器内复判），根据轨道区分
    "1014": {"name": "人工复检", "desc": "人工复检"},  # 等待人工复检（机器外复判），根据轨道区分
    "3010": {"name": "等待进板", "desc": "等待进板"},  # 等待进板，根据轨道区分
    "1021": {"name": "主软件启动", "desc": "主软件启动"},
    "1022": {"name": "主软件退出", "desc": "主软件退出"},
    "4010": {"name": "扫码失败", "desc": "扫码失败报警"},  # 开启相关设置之后，扫码失败后报警，根据轨道区分
}

# AIS431
DEVICE_STATUS_431 = {
    # 流程
    "2018": {"name": "进板", "desc": "1轨板卡开始进板（进板开始）"},
    "2035": {"name": "进板", "desc": "2轨板卡开始进板（进板开始）"},
    "2020": {"name": "启动", "desc": "手动运行检测程序"},
    "2021": {"name": "暂停", "desc": "手动停止检测程序"},
    "2019": {"name": "出板", "desc": "1轨板卡刚到达出板卡（出板开始）"},
    "2036": {"name": "出板", "desc": "2轨板卡刚到达出板卡（出板开始）"},
    "2043": {"name": "换线", "desc": "1轨切换板式"},
    "2044": {"name": "换线", "desc": "2轨切换板式"},
    "2033": {"name": "检测中", "desc": "1轨板卡到达相机拍照位置"},
    "2034": {"name": "检测中", "desc": "2轨板卡到达相机拍照位置"},
    "1017": {"name": "人工复检", "desc": "1轨等待机器内人工复检"},
    "1018": {"name": "人工复检", "desc": "2轨等待机器内人工复检"},
    "1019": {"name": "人工复检", "desc": "1轨等待机器外人工复检"},
    "1020": {"name": "人工复检", "desc": "2轨等待机器外人工复检"},
    "2039": {"name": "等待进板", "desc": "1轨等待进板"},
    "2040": {"name": "等待进板", "desc": "2轨等待进板"},
    "2037": {"name": "要板", "desc": "1轨向上接驳台发送要板信号"},
    "2041": {"name": "要板", "desc": "2轨向上接驳台发送要板信号"},
    "2038": {"name": "有板", "desc": "1轨向下接驳台发送有板信号"},
    "2042": {"name": "有板", "desc": "2轨向下接驳台发送有板信号"},

    "2000": {"name": "主软件启动", "desc": "主软件启动"},
    "2001": {"name": "主软件退出", "desc": "主软件退出"},

    # 故障
    "10000": {"name": "急停", "desc": "操作设备急停按钮"},
    "10002": {"name": "安全门报警", "desc": "安全门打开"},
    "2004": {"name": "条码校验过站失败", "desc": "1轨上传条码校验过站失败"},
    "2005": {"name": "条码校验过站失败", "desc": "2轨上传条码校验过站失败"},
    "2008": {"name": "上传检测信息失败", "desc": "1轨上传检测结果到MES/EAP等失败"},
    "2009": {"name": "上传检测信息失败", "desc": "2轨上传检测结果到MES/EAP等失败"},
    "5000": {"name": "磁盘已满", "desc": "磁盘已满"},
    "6005": {"name": "直通率告警", "desc": "设备直通率异常报警"},
    "6006": {"name": "直通率告警", "desc": "设备直通率异常停机"},
    "6007": {"name": "直通率告警", "desc": "产线直通率异常报警"},
    "6008": {"name": "直通率告警", "desc": "产线直通率异常停机"},
    "4000": {"name": "Mark点错误", "desc": "1轨检测板卡有Mark点报错"},
    "4001": {"name": "Mark点错误", "desc": "2轨检测板卡有Mark点报错"},
    "2030": {"name": "板卡NG", "desc": "1轨检测板卡为NG"},
    "2032": {"name": "板卡NG", "desc": "2轨检测板卡为NG"},
    "8102": {"name": "相机连接失败", "desc": "监控到相机连接失败"},
    "1004": {"name": "扫码失败", "desc": "1轨扫码失败报警"},
    "1005": {"name": "扫码失败", "desc": "2轨扫码失败报警"},
}

# 整合了DEVICE_STATUS和DEVICE_STATUS_V3所有中文进行英文映射
# 主软件自己配置为英文时，其实也会转为英文，只是为了双保险这边也配置一份
DEVICE_STATUS_EN_MAP = {
    # v1
    "进板": "Board In",
    "开始检测": "Start Inspection",
    "停止检查": "Stop Inspection",
    "出板": "Board Out",
    "安全门": "Safety Door Alarm",
    "调试": "Debugging",
    "数据超时": "Data Timeout",
    "板卡 NG": "PCB NG",
    "紧急故障": "Emergency Fault",
    "流程错误": "Process Error",
    "直通率告警": "Pass Through Rate Warning",
    "Marker 错误": "Marker Error",
    "清除机器错误": "Buzzer Alarm Release",
    "其他错误": "Other Errors",
    # v3
    "启动": "Start Inspection",
    "暂停": "Stop Inspection",
    "换线": "Program Switched",
    "急停": "Emergency Stop",
    "安全门报警": "Safety Door Alarm",
    "待料（空闲）": "Idle (Waiting Material)",
    "出板超时": "Board Out Timeout",
    "条码校验过站失败": "Check Barcode Failed",
    "上传检测信息失败": "Upload Inspection Data Failed",
    "磁盘已满": "Disk Full",
    "掉板": "Board Drop",
    "EAP告警": "EAP Alarm",
    "板卡NG": "PCB NG",
    "引导换线失败": "Switch Program Failed",
    "风扇停转": "Fan Stopped",
    "相机连接失败": "Camera Connection Failed",
    "蜂鸣报警解除": "Buzzer Alarm Release"
}

AIS630_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "锡型不良", "custom_code": "1", "custom_str": "锡型不良"},
    "2": {"standard": "水平偏移", "custom_code": "2", "custom_str": "水平偏移"},
    "3": {"standard": "竖直偏移", "custom_code": "3", "custom_str": "竖直偏移"},
    "4": {"standard": "连锡", "custom_code": "4", "custom_str": "连锡"},
    "5": {"standard": "面积偏小", "custom_code": "5", "custom_str": "面积偏小"},
    "6": {"standard": "面积偏大", "custom_code": "6", "custom_str": "面积偏大"},
    "7": {"standard": "高度偏低", "custom_code": "7", "custom_str": "高度偏低"},
    "8": {"standard": "高度偏高", "custom_code": "8", "custom_str": "高度偏高"},
    "9": {"standard": "少锡", "custom_code": "9", "custom_str": "少锡"},
    "10": {"standard": "多锡", "custom_code": "10", "custom_str": "多锡"},
    "11": {"standard": "无锡", "custom_code": "11", "custom_str": "无锡"},
    "12": {"standard": "共面性", "custom_code": "12", "custom_str": "共面性"},
    "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "条码识别"},
    "1000": {"standard": "Unknown", "custom_code": "1000", "custom_str": "Unknown"},
}

# 包含203/303/40X/50X设备
AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "2", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "3", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "5", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "6", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "7", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "8", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "9", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "10", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "11", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "12", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "13", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "14", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "15", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "17", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "23", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
}

# 只针对301设备
AIS_30X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "不合格", "custom_code": "1", "custom_str": "NG"},
    "2": {"standard": "多锡", "custom_code": "2", "custom_str": "ExcessSolder"},
    "3": {"standard": "连锡", "custom_code": "3", "custom_str": "Bridge"},
    "4": {"standard": "少锡", "custom_code": "4", "custom_str": "InsufficientSolder"},
    "5": {"standard": "孔洞", "custom_code": "5", "custom_str": "PinHole"},
    "6": {"standard": "未出脚", "custom_code": "6", "custom_str": "NoPin"},
    "7": {"standard": "异常出脚", "custom_code": "7", "custom_str": "ExceptionPin"},
    "8": {"standard": "缺件", "custom_code": "8", "custom_str": "MissingPart"},
    "9": {"standard": "偏位", "custom_code": "9", "custom_str": "ShiftPart"},
    "10": {"standard": "露铜", "custom_code": "10", "custom_str": "ExposeCopper"},
    "11": {"standard": "错件", "custom_code": "11", "custom_str": "WrongPart"},
    "12": {"standard": "极性错误", "custom_code": "12", "custom_str": "ReversePart"},
    "13": {"standard": "条码识别错误", "custom_code": "13", "custom_str": "BarcodeRecognition"},
    "14": {"standard": "数据错误", "custom_code": "14", "custom_str": "CountError"},
    "15": {"standard": "定位错误", "custom_code": "15", "custom_str": "Locate"},
    "16": {"standard": "流程错误", "custom_code": "16", "custom_str": "ProcessError"},
    "17": {"standard": "锡珠", "custom_code": "17", "custom_str": "SolderBall"},
    "18": {"standard": "拼版特征不匹配", "custom_code": "18", "custom_str": "FeatureMismatch"},
}

AIS_20X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
    "-1": {"standard": "未检查", "custom_code": "-1", "custom_str": "NoCheck"},
    "4": {"standard": "反件", "custom_code": "4", "custom_str": "ReversePart"},
    "5": {"standard": "错件", "custom_code": "5", "custom_str": "WrongPart"},
    "20": {"standard": "引脚未插", "custom_code": "20", "custom_str": "Pin Not Found"},
    "21": {"standard": "不是引脚", "custom_code": "21", "custom_str": "NoPin"},
    "101": {"standard": "多件", "custom_code": "101", "custom_str": "多件"},
    "102": {"standard": "浮高", "custom_code": "102", "custom_str": "Part Lift"},
    "103": {"standard": "歪斜", "custom_code": "103", "custom_str": "Part Tilt"},
    "104": {"standard": "条码错误", "custom_code": "104", "custom_str": "Barcode Error"},
    "105": {"standard": "内部错误", "custom_code": "105", "custom_str": "Internal Error"},
    "80": {"standard": "多涂", "custom_code": "80", "custom_str": "MoreCoating"},
    "81": {"standard": "少涂", "custom_code": "81", "custom_str": "LessCoating"},
    "82": {"standard": "气泡", "custom_code": "82", "custom_str": "BubbleCoating"},
}
