import xml.etree.ElementTree as ET

import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import Response

app = FastAPI()


@app.post("/WebService1.asmx")
async def handle_get_api_coll():
    # content_type = request.headers.get("Content-Type")
    # if content_type != "text/xml; charset=utf-8":
    #     return Response(status_code=400, content="Invalid Content-Type")
    #
    # try:
    #     xml_data = await request.body()
    #     root = ET.fromstring(xml_data)
    #     # 这里简单提取下传入的参数（示例中只是提取展示，实际要按业务逻辑使用）
    #     task_no = None
    #     line_no = None
    #     board_barcode = None
    #     # 查找GetApiColl节点下的各个子元素
    #     for element in root.findall('.//{http://tempuri.org/}GetApiColl/*'):
    #         if element.tag.endswith('TASK_NO'):
    #             task_no = element.text
    #         elif element.tag.endswith('LINE_NO'):
    #             line_no = element.text
    #         elif element.tag.endswith('BOARD_BARCODE'):
    #             board_barcode = element.text

    # 构建响应的SOAP XML内容（这里只是简单示例返回固定的结果，实际要按真实业务逻辑改变）
    response_xml = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetApiCollResponse xmlns="http://tempuri.org/">
      <GetApiCollResult>true</GetApiCollResult>
      <StrReturn>Success</StrReturn>
    </GetApiCollResponse>
  </soap:Body>
</soap:Envelope>"""
    return Response(content=response_xml, media_type="text/xml")


@app.get("/username")
def username():
    return {
        "username": "111",
        "age": 11
    }


# except ET.ParseError:
#     return Response(status_code=400, content="Invalid XML format")


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8081)
