# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2024/1/18 下午4:14
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

# ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><CheckBaseDataResponse xmlns="http://tempuri.org/"><CheckBaseDataResult>false</CheckBaseDataResult><userInfo xmlns:a="http://schemas.datacontract.org/2004/07/Soas.Model.Entities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><a:FullName i:nil="true"/><a:UserGrade>0</a:UserGrade><a:UserID i:nil="true"/><a:UserName i:nil="true"/><a:UserRoles i:nil="true"/></userInfo><result>false</result><message>用户名或密码错误！</message></CheckBaseDataResponse></s:Body></s:Envelope>"""

# ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
#     <s:Body>
#         <GetMeterInfoBySNResponse xmlns="http://tempuri.org/">
#             <GetMeterInfoBySNResult xmlns:a="http://schemas.datacontract.org/2004/07/Soas.Model.Entities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
#                 <a:AccuracyLevel i:nil="true"/>
#                 <a:Constant i:nil="true"/>
#                 <a:Current i:nil="true"/>
#                 <a:DoWhatList i:nil="true"/>
#                 <a:Frequency i:nil="true"/>
#                 <a:MaterialNo i:nil="true"/>
#                 <a:MeterModel i:nil="true"/>
#                 <a:OrderName i:nil="true"/>
#                 <a:OrderNo i:nil="true"/>
#                 <a:Phase i:nil="true"/>
#                 <a:SN i:nil="true"/>
#                 <a:SNType i:nil="true"/>
#                 <a:SchemeList i:nil="true"/>
#                 <a:Voltage i:nil="true"/>
#                 <a:WorkOrder i:nil="true"/>
#             </GetMeterInfoBySNResult>
#             <basedata>false</basedata>
#             <result>false</result>
#             <message>主条码2301PP110002工序不正确!原因:无此岗位采集点 (Pro_Checkroute:p_type=1;p_Point_Code=50ZH01HJ0202;p_station_code=S0000007;p_r_Sn=2301PP110002;p_t_Sn=2301AA109993;p_s_Sn=2301AA109993)</message>
#         </GetMeterInfoBySNResponse>
#     </s:Body>
# </s:Envelope>"""

# ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
#     <s:Body>
#         <Save_Tr_Common_DataInfoResponse xmlns="http://tempuri.org/">
#             <Save_Tr_Common_DataInfoResult>false</Save_Tr_Common_DataInfoResult>
#             <sql/>
#             <message>上传完成:0条,失败:1条(失败详情:条码:D3VZ15KBT4021000PO2050,错误原因:无法识别主条码:D3VZ15KBT4021000PO2050;)</message>
#         </Save_Tr_Common_DataInfoResponse>
#     </s:Body>
# </s:Envelope>"""
ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <ActivityId CorrelationId="88fed01c-faeb-400b-95db-5a71da15278e"
                    xmlns="http://schemas.microsoft.com/2004/09/ServiceModel/Diagnostics">
            00000000-0000-0000-0000-000000000000
        </ActivityId>
    </s:Header>
    <s:Body>
        <Save_Tr_Common_DataInfoResponse xmlns="http://tempuri.org/">
            <Save_Tr_Common_DataInfoResult>false</Save_Tr_Common_DataInfoResult>
            <sql>INSERT INTO TR_COMMON_L (ID, HEADER_ID, SEQUENCE, TEST_SEQ, TEST_NAME, ALLOW_DEVIATION, TEST_VALUE,
                ACTUAL_DEVIATION, TEST_RESULT, ICT_PART_NAME, ICT_MEASURED_VALUE, ICT_DEVIATION, ICT_ACTUAL_VALUE,
                ICT_PIN_NEGACTIVEID, ICT_PIN_POSTIVEID, CREATED_BY, CREATION_DATE, EXT1, EXT2, EXT3, EXT4, EXT5, EXT6,
                EXT7, EXT8, EXT9, EXT10, FCT_COM_PORT) VALUES (:vID, :vHEADER_ID, :vSEQUENCE, :vTEST_SEQ, :vTEST_NAME,
                :vALLOW_DEVIATION, :vTEST_VALUE, :vACTUAL_DEVIATION, :vTEST_RESULT, :vICT_PART_NAME,
                :vICT_MEASURED_VALUE, :vICT_DEVIATION, :vICT_ACTUAL_VALUE, :vICT_PIN_NEGACTIVEID, :vICT_PIN_POSTIVEID,
                :vCREATED_BY, :vCREATION_DATE, :vEXT1, :vEXT2, :vEXT3, :vEXT4, :vEXT5, :vEXT6, :vEXT7, :vEXT8, :vEXT9,
                :vEXT10, :vFCT_COM_PORT)
            </sql>
            <message>上传完成:0条,失败:1条(失败详情:条码:2301PP110007,错误原因:测试主数据中，其结果数据[]不合法(应为：1或0、T或F)!条码:2301PP110007;)</message>
        </Save_Tr_Common_DataInfoResponse>
    </s:Body>
</s:Envelope>"""

root = xutil.XmlUtil.get_xml_root_by_str(ret_str)

result = root[1][0].find('{http://tempuri.org/}Save_Tr_Common_DataInfoResult').text
message = root[1][0].find('{http://tempuri.org/}message').text

print(result)
print(message)
