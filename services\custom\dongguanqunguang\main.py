# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/15 下午4:12
# Author     ：sch
# version    ：python 3.8
# Description：东莞群光 、 泰国群电基础上更改。    东莞群光电
"""
import time
from datetime import timedelta, datetime
from typing import Any

from common import xrequest, xcons, xutil
from common.xrequest import send_error_info_to_repair
from common.xutil import log, complete_array
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import ErrorMapEngine
from services.custom.dongguanqunguang.bfh_client import BFHClient

global_data = {}

circular_list = xutil.CircularList(200)

circular_list1 = xutil.CircularList(100)

NG_CODE_BIT_MAP = {
    "14": {"desc": "短路", "bit": 1},  # 对应 连锡
    "23": {"desc": "空焊", "bit": 2},  # 对应 虚焊
    "10": {"desc": "少锡", "bit": 4},
    "2": {"desc": "错件", "bit": 8},
    "1": {"desc": "漏件", "bit": 16},
    "3": {"desc": "反向", "bit": 32},  # 对应 反件
    "12": {"desc": "脚未出", "bit": 64},  # 对应 未出脚
    "5": {"desc": "偏移", "bit": 128},
    "13": {"desc": "锡孔", "bit": 256},  # 对应 孔洞
    "4": {"desc": "立碑", "bit": 512},
    "8": {"desc": "破损", "bit": 1024},  # 对应 损件
}
# 其它无法识别的不良代码，bit统一为2048
OTHER_CODE_BIT = 2048


def x_request_device_status(
        device_status_url,
        device_status_version,
        device_status_line_id,
        device_status_device_id,
        status_code_v3,
):
    cache_data = xutil.CacheUtil.get_cache_data()
    if status_code_v3 == "xxxx":
        status_code_v3 = cache_data.get("last_device_status_v3", "1001")

    if status_code_v3 in ["1001", "1002", "1003", "1004", "1005", "3002", "0001", "4001", "4002", "4003"]:
        status_1 = 1
    elif status_code_v3 in ["2001", "2002", "5001", "5002"]:
        status_1 = 4
    elif status_code_v3 in ["3001"]:
        status_1 = 2
    elif status_code_v3 in ["3003", "3004", "3005", "3006", "3007"]:
        status_1 = 3
    else:
        status_1 = 0

    d1 = datetime.now()

    mes_start_time_on_today = cache_data.get("mes_start_time_on_today")
    board_pass_count = cache_data.get("board_pass_count", 0)
    board_ng_count = cache_data.get("board_ng_count", 0)
    cycle_time_status_data = cache_data.get("cycle_time_status_data", 0)
    total_count = board_pass_count + board_ng_count

    cache_data["last_device_status_v3"] = status_code_v3
    xutil.CacheUtil.save_cache_data(cache_data)

    if mes_start_time_on_today:
        timer_1 = int(time.time()) - mes_start_time_on_today
    else:
        timer_1 = 0

    if status_1 != 0:
        try:
            device_status_param1 = {
                "$id": "1",
                "Version": device_status_version,
                "LineId": device_status_line_id,
                "DeviceId": device_status_device_id,
                "Timestamp": int(d1.timestamp() * 1000),
                "AT": d1.strftime(xcons.FMT_TIME_DEFAULT14),
                "Params": {
                    "$id": "2",
                    "Timer": timer_1,
                    "Status": status_1,
                    "Alarms": status_code_v3,
                    "Input": total_count,
                    "Output": total_count,
                    "CT": int(cycle_time_status_data),
                    "OK": total_count,
                    "NG": 0
                },
            }
            xrequest.RequestUtil.post_json(device_status_url, device_status_param1)
        except Exception as err:
            log.warning(f"上传设备状态失败，error：{err}")
    else:
        log.warning(f"未知的设备状态码，本次不发送！")


def cache_maintain_info(review_path: str, cycle_time: int = 0):
    """
    缓存维修保养信息
    维护保养信息, 改成使用了多少天

    ----------统计信息----------
    ## 按天数统计
    相机   5年
    光源   3年
    镜头   5年
    轨道皮带   1年
    伺服电机   5年

    ## 按使用次数统计
    气缸   10万次
    电磁阀  10万次

    :param review_path: 缓存哪个数据包
    :param cycle_time: CT时间
    :return:
    """
    if review_path:
        if circular_list.is_exist_item(review_path):
            log.warning(f"review path: {review_path} 该数据包数据已统计保养信息！")
            return
        else:
            circular_list.add_item(review_path)
    else:
        log.warning(f"review path为空，本次缓存丢弃！")
        return

    # 记录CT时间和发送次数
    cache_data = xutil.CacheUtil.get_cache_data()

    total_cycle_time = cache_data.get("total_cycle_time", 0)
    total_qty = cache_data.get("total_qty", 0)

    cache_data["total_cycle_time"] = total_cycle_time + cycle_time
    cache_data["total_qty"] = total_qty + 1

    maintain_cylinder = cache_data.get("maintain_cylinder", 0)  # 气缸，10万次
    maintain_solenoid_valve = cache_data.get("maintain_solenoid_valve", 0)  # 电磁阀，10万次

    date_str = xutil.DateUtil.get_datetime_now(xcons.FMT_DATE)

    def append_date_str_to_cache_data(key_str: str):
        """
        不存在则添加到列表
        """
        old_list = cache_data.get(key_str, [])

        if date_str not in old_list:
            old_list.append(date_str)
            cache_data[key_str] = old_list

    append_date_str_to_cache_data("maintain_all_used_date")
    append_date_str_to_cache_data("maintain_camera")  # 相机
    append_date_str_to_cache_data("maintain_light_source")  # 光源
    append_date_str_to_cache_data("maintain_lens")  # 镜头
    append_date_str_to_cache_data("maintain_track_belt")  # 轨道
    append_date_str_to_cache_data("maintain_servo_motor")  # 伺服电机

    cache_data["maintain_cylinder"] = maintain_cylinder + 1
    cache_data["maintain_solenoid_valve"] = maintain_solenoid_valve + 1

    xutil.CacheUtil.save_cache_data(cache_data)
    log.info(
        f"缓存保养信息成功： total_qty: {cache_data['total_qty']}  total_cycle_time: {cache_data['total_cycle_time']}"
    )
    return cache_data


def login_mes(window_ip: str, username: str) -> str:
    pocket_data = {
        "type": 1,
        "request_param": f"{username};"
    }

    ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_data)
    msg = ret.get("string")
    return msg


class Engine(ErrorMapEngine):
    version = {
        "customer": ["东莞群光", "dongguanqunguang"],
        "version": "release v1.0.0.16",
        "device": "AIS203、AIS40X、AIS50X",
        "feature": ["条码校验", "上传数据", "设备状态", "条码补录", "数据上传波峰焊"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-16 16:31  条码校验，上传数据，设备状态，定时触发心跳接口
date: 2024-05-08 10:27  上传统计信息
date: 2024-05-08 19:19  catch tpms exception
date: 2024-05-12 10:52  修改参数
date: 2024-06-06 17:03  result传复判结果
date: 2024-07-04 18:36  使用V3版本设备状态码
date: 2024-07-19 09:59  增加进出板时间调整
date: 2024-07-25 10:53  从泰国群光拷贝
date: 2024-07-25 15:49  增加条码补录功能，增加上传NG数据配置项，command2接口返回NG不需要停机
date: 2024-08-01 17:40  增加上传设备状态
date: 2024-09-04 17:38  根据轨道回显错误信息到不同维修站
date: 2024-09-05 14:38  优化：根据轨道回显错误信息到不同维修站
date: 2025-04-11 16:26  jira:31867,修改设备状态
date: 2025-05-16 11:00  jira:31867,增加波峰焊的功能,包括心跳长连接和数据发送，推翻之前的实现，按新的文档实现
date: 2025-05-21 14:50  jira:31867,上传波峰焊数据改为按拼板发送
""", }

    form = {
        "operator_id_tpms": {
            "ui_name": "作业员工号(TPMS)",
            "ui_name_en": "OperatorId(TPMS)",
            "value": "",
        },
        "username_mes": {
            "ui_name": "登录员工号(MES)",
            "ui_name_en": "User(MES)",
            "value": "",
        },
        "split_index": {
            "ui_name": "切割后N位(条码补录)",
            "value": "4"
        },
        "bfh_ip": {
            "ui_name": "波峰焊地址",
            "value": ""
        },
        "bfh_port": {
            "ui_name": "波峰焊端口",
            "value": ""
        }
    }

    other_form = {
        "repair_ip1": {
            "ui_name": "维修站IP(repair1)",
            "ui_name_en": "RepairIp(repair1)",
            "value": "",
        },
        "repair_ip2": {
            "ui_name": "维修站IP(repair2)",
            "ui_name_en": "RepairIp(repair2)",
            "value": "",
        },
        "window_ip": {
            "ui_name": "windowIp/中转电脑(MES)",
            "ui_name_en": "windowIp(MES)",
            "value": "",
        },
        "api_url_tpms": {
            "ui_name": "接口URL(TPMS)",
            "ui_name_en": "APIURL(TPMS)",
            "value": "",
        },
        "device_id_tpms": {
            "ui_name": "设备ID(TPMS)",
            "ui_name_en": "DeviceId(TPMS)",
            "value": "",
        },
        "device_name_tpms": {
            "ui_name": "设备名称(TPMS)",
            "ui_name_en": "DeviceName(TPMS)",
            "value": "",
        },
        "fixture_id_tpms": {
            "ui_name": "FixtureId(TPMS)",
            "ui_name_en": "FixtureId(TPMS)",
            "value": "",
        },
        "device_status_url": {
            "ui_name": "接口URL(设备状态)",
            "ui_name_en": "APIURL(DeviceStatus)",
            "value": "http://<边缘服务器IP>:8080/api/snapshot/<设备Id>/<线体Id>",
        },
        "device_status_version": {
            "ui_name": "Version(设备状态)",
            "ui_name_en": "Version(DeviceStatus)",
            "value": "1.0",
        },
        "device_status_line_id": {
            "ui_name": "LineId(设备状态)",
            "ui_name_en": "LineId(DeviceStatus)",
            "value": "ML12",
        },
        "device_status_device_id": {
            "ui_name": "DeviceId(设备状态)",
            "ui_name_en": "DeviceId(DeviceStatus)",
            "value": "aoi01",
        },
    }

    combo = {
        "tpms_switch": {
            "ui_name": "TPMS接口总开关",
            "ui_name_en": "IsUseTpms",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "is_upload_ng_data": {
            "ui_name": "是否上传NG数据",
            "ui_name_en": "IsUploadNgData",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "bfh_upload_data": {
            "ui_name": "上传数据给波峰焊",
            "item": ["Yes", "No"],
            "value": "No",
        }
    }

    other_combo = {
        "is_cron_connect_tpms": {
            "ui_name": "心跳检测开启(TPMS)",
            "ui_name_en": "HeartBeatSwitch(TPMS)",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "cron_second": {
            "ui_name": "心跳间隔/s(TPMS)",
            "ui_name_en": "HeartBeatInterval/s(TPMS)",
            "item": ["10", "20", "30", "60", "120", "300", "600", "1500", "3000", "6000", "12000", "24000"],
            "value": "60",
        },
        "in_board_fix": {
            "ui_name": "进板时间调整(秒)",
            "ui_name_en": "BoardInTimeFix(s)",
            "item": ["-10", "-8", "-6", "-4", "-3", "-2", "-1", "0", "+1", "+2", "+3", "+4", "+6", "+8", "+10"],
            "value": "0",
        },
        "out_board_fix": {
            "ui_name": "出板时间调整(秒)",
            "ui_name_en": "BoardOutTimeFix(s)",
            "item": ["-10", "-8", "-6", "-4", "-3", "-2", "-1", "0", "+1", "+2", "+3", "+4", "+6", "+8", "+10"],
            "value": "0",
        },
        "is_cron_device_status": {
            "ui_name": "定时触发设备状态",
            "ui_name_en": "CronUploadDeviceStatus",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "cron_second_device_status": {
            "ui_name": "定时触发设备状态间隔/s",
            "ui_name_en": "CronUploadDeviceStatusInterval/s",
            "item": ["5", "10", "20", "30", "60", "120", "300", "600", "1500", "3000", "6000", "12000", "24000"],
            "value": "60",
        },
        "send_type_repair": {
            "ui_name": "报错信息同步至维修站",
            "item": ["不同步", "1轨", "2轨", "1轨+2轨"],
            "value": "1轨+2轨",
        },
    }

    button = {
        "check_connect": {
            "ui_name": "connectWindow"
        },
        "login_mes": {
            "ui_name": "登录工号",
            "ui_name_en": "LoginUser"
        },
        "maintain_clear": {
            "ui_name": "保养",
            "ui_name_en": "MaintainClear",
        },
        "bfh_setting_click": {
            "ui_name": "连接波峰焊",
        }
    }

    def __init__(self):
        self.other_vo = None
        self.bfh_client = None

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.other_vo = other_vo

        cache_data = xutil.CacheUtil.get_cache_data()

        date_now = datetime.now().strftime(xcons.FMT_DATE)  # 现在是几号
        current_date2 = cache_data.get("current_date2", "")

        if date_now == current_date2:
            # 还是当天，启动时间不需要重新计算
            pass
        else:
            # 第二天了, 启动时间需要重新计算
            cache_data["mes_start_time_on_today"] = int(time.time())
            cache_data["current_date2"] = date_now
            xutil.CacheUtil.save_cache_data(cache_data)
            log.info(f"缓存该天第一次启动MES配置器的时间点！")

        other_vo = OtherVo({}, main_window.config_data)

        is_cron_connect_tpms = other_vo.get_value_by_cons_key("is_cron_connect_tpms")
        cron_second = other_vo.get_value_by_cons_key("cron_second")

        if is_cron_connect_tpms == "Yes":
            interval_cron = True
        else:
            interval_cron = False

        main_window.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        main_window.config_data["app_setting"]["custom_interval_time"] = int(cron_second)  # 3s

        is_cron_device_status = other_vo.get_value_by_cons_key("is_cron_device_status")
        cron_second_device_status = other_vo.get_value_by_cons_key("cron_second_device_status")
        if is_cron_device_status == "Yes":
            interval_cron2 = True
        else:
            interval_cron2 = False

        main_window.config_data["app_setting"]["custom_interval_cron2"] = interval_cron2
        main_window.config_data["app_setting"]["custom_interval_time2"] = int(cron_second_device_status)  # 3s

        self.log.info("init main window done!")

        bfh_upload_data = other_vo.get_value_by_cons_key("bfh_upload_data")
        if bfh_upload_data == "Yes":
            # 创建波峰焊client，0.2s再启动连接，目的是让初始化流程先走完
            bfh_ip = other_vo.get_value_by_cons_key("bfh_ip")
            bfh_port = other_vo.get_value_by_cons_key("bfh_port")
            self.bfh_client = BFHClient(main_window, bfh_ip, bfh_port)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        # window_ip = other_dao.get_value_by_cons_key("window_ip")
        # username = other_dao.get_value_by_cons_key("username_mes")

        ret_res = self.x_response()
        # for sn in other_dao.list_sn():
        #     check_param = {
        #         "type": 7,
        #         "request_param": {
        #             "pData": sn.ljust(40)[:40]
        #         }
        #     }
        #
        #     ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, check_param)
        #
        #     ret_str = ret.get('string')
        #     if str(ret_str) != '0':
        #         ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret_str}")

        # if not global_data.get("is_login"):
        #     msg = login_mes(window_ip, username)
        #
        #     if 'ok' not in msg.lower():
        #         err_msg = f"接口响应异常，校验工号失败，cmd1调用失败，error：{msg}"
        #
        #         if other_param.lang_is_en():
        #             err_msg = f"api error, cmd1 call failed, error：{msg}"
        #
        #         return self.x_response("false", err_msg)
        #
        #     global_data["is_login"] = True
        #
        # for sn in other_dao.list_sn():
        #     pocket_param = {
        #         "type": 2,
        #         "request_param": f"{sn};"
        #     }
        #
        #     ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param)
        #     ret_str = ret.get('string')
        #     if "ok" not in ret_str.lower():
        #         err_msg = f"接口响应异常，条码校验，cmd2调用失败，error：{ret_str}"
        #
        #         if other_param.lang_is_en():
        #             err_msg = f"api error, cmd2 call failed，error：{ret_str}"
        #
        #         return self.x_response("false", err_msg)

        return ret_res

    @send_error_info_to_repair
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        window_ip = data_vo.get_value_by_cons_key("window_ip")
        api_url_tpms = data_vo.get_value_by_cons_key("api_url_tpms")
        device_id_tpms = data_vo.get_value_by_cons_key("device_id_tpms")
        operator_id_tpms = data_vo.get_value_by_cons_key("operator_id_tpms")
        device_name_tpms = data_vo.get_value_by_cons_key("device_name_tpms")
        fixture_id_tpms = data_vo.get_value_by_cons_key("fixture_id_tpms")
        tpms_switch = data_vo.get_value_by_cons_key("tpms_switch")
        is_upload_ng_data = data_vo.get_value_by_cons_key("is_upload_ng_data")
        bfh_upload_data = data_vo.get_value_by_cons_key("bfh_upload_data")

        inspect_type = other_data.get("inspect_type")
        review_path = other_data.get("review_path")

        cache_maintain_info(review_path)

        username = data_vo.get_value_by_cons_key("username_mes")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        pcb_sn = pcb_entity.pcb_barcode

        # ret_res = self.x_response()

        board_data_list = []

        start_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time1 = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        mes_param_list = []

        pass_count = 0
        ng_count = 0

        repair_user = pcb_entity.repair_user

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_robot_ng():
                ng_count += 1
            else:
                pass_count += 1

            if not pcb_sn and barcode:
                pcb_sn = barcode

            dc_list = []
            lc_list = []
            fw_list = []
            img_list = []

            user_result = board_entity.get_repair_result("OK", "NG")

            param3_str = f"{username};{barcode};{user_result};"

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    dc_list.append(comp_entity.designator)
                    lc_list.append(comp_entity.repair_ng_code)
                    fw_list.append(comp_entity.repair_ng_str)
                    img_list.append(comp_entity.image_path)

            if lc_list:
                param3_str = f"{param3_str}{';'.join(lc_list)}"

            mes_param_list.append(param3_str)

            board_data_list.append({
                "sequence": int(board_no),
                "sn": barcode,
                "startTime": start_time1,
                "endTime": end_time1,
                "cycleTime": pcb_entity.get_cycle_time(),
                "skip": board_entity.get_final_result(False, False, False, True),
                "result": board_entity.get_repair_result(True, False),
                "defectCode": "",
                "message": "",
                "parameter": {
                    "DC": ",".join(dc_list),
                    "LC": ",".join(lc_list),
                    "FW": ",".join(fw_list),
                    "picture": ",".join(img_list),
                }
            })

            # board_result = board_entity.get_repair_result('0', '1')

            # data_str = f"{barcode.ljust(40)[:40]}{start_time}{board_result}"

            # data_param = {
            #     "type": 2,
            #     "request_param": {
            #         "pData": data_str
            #     }
            # }

            # ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, data_param)
            #
            # ret_str = ret.get('string')
            # if str(ret_str) != '0':
            #     ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret_str}")

        project_name = pcb_entity.project_name
        board_count = pcb_entity.board_count
        pcb_image_list = pcb_entity.list_all_pcb_image()

        tpms_param = {
            "deviceId": device_id_tpms,
            "fixtureId": fixture_id_tpms,
            "operatorId": operator_id_tpms,
            "data": {
                "sn": pcb_sn,
                "parameter": {
                    "DC": project_name,
                    "LC": device_name_tpms,
                    "FW": board_count,
                    "picture": ",".join(pcb_image_list),

                },
                "result": pcb_entity.get_repair_result(True, False),
                "detail": board_data_list
            }
        }

        error_msg_list = []
        # 先发送波峰焊数据，避免后面异常导致波峰焊数据不能正常发送，只发复判后的数据
        if bfh_upload_data == "Yes" and inspect_type == "repair":
            for board_entity in pcb_entity.yield_board_entity():
                defective_message = None if board_entity.repair_result else {}
                comp_repair_ng_count = 0
                board_code = board_entity.barcode
                if board_entity.is_repair_ng():
                    comp_repair_ng_count = board_entity.comp_repair_ng_number
                    for comp_entity in board_entity.yield_comp_entity():
                        if comp_entity.repair_result:
                            continue

                        designator = comp_entity.designator
                        err_bit_list = set()
                        for alg in comp_entity.yield_alg_entity():
                            ng_code = alg.result
                            if ng_code != '0':
                                # 取不到对应的不良代码，统一为2048
                                err_bit = NG_CODE_BIT_MAP.get(ng_code, {}).get('bit', OTHER_CODE_BIT)
                                err_bit_list.add(err_bit)

                        defective_message.update({designator: sum(err_bit_list)})

                review_data = {
                    "eMessageType": 2,
                    "ProductionData": {
                        "BoardEntryTime": start_time1,
                        "BoardCode": board_code,
                        "DefectiveQuantity": comp_repair_ng_count,
                        "DefectiveMessage": defective_message
                    }
                }
                err_msg = self.bfh_client.send_aoi_data(review_data)
                if err_msg:
                    error_msg_list.append(err_msg)

        if inspect_type == "repair":
            if not global_data.get("is_login"):
                msg = login_mes(window_ip, username)

                if 'ok' not in msg.lower():
                    error_msg = f"接口响应异常，校验工号失败，cmd1调用失败，error：{msg}"

                    if other_param.lang_is_en():
                        error_msg = f"api error, cmd1 call failed, error：{msg}"

                    return self.x_response("false", error_msg)

                global_data["is_login"] = True

            self.log.info(f"--------command2--------------")
            error_msg = []
            for board_entity in pcb_entity.yield_board_entity():
                sn = board_entity.barcode
                pocket_param = {
                    "type": 2,
                    "request_param": f"{sn};"
                }

                ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param)
                ret_str = ret.get('string')
                if "ok" not in ret_str.lower():
                    err_msg = f"条码校验，cmd2调用失败，error：{ret_str}"

                    if other_param.lang_is_en():
                        err_msg = f"api error, cmd2 call failed，error：{ret_str}"

                    # return self.x_response("false", err_msg)
                    error_msg.append(err_msg)
            self.log.info(f"--------command2--------------")

            if error_msg:
                err_msg_str = "\n".join(error_msg)
                return self.x_response("false", f"mes接口异常，{err_msg_str}")

            try:
                if tpms_switch == "Yes":
                    data_url = f"{api_url_tpms}/api/dap/productionresult"
                    ret = xrequest.RequestUtil.post_json(data_url, tpms_param)
                    if not ret.get('result'):
                        err_msg = f"TPMS接口调用异常，上传数据失败，error：{ret.get('message')}"

                        if other_param.lang_is_en():
                            err_msg = f"TPMS API error，upload data failed，error：{ret.get('message')}"

                        # ret_res = self.x_response("false", err_msg)
                        error_msg_list.append(err_msg)
                else:
                    self.log.warning(f"未启用TPMS，不调用 /api/dap/productionresult 接口！")

            except Exception as err:
                err_msg = f"TPMS接口调用异常，上传数据失败，error：{err}"

                if other_param.lang_is_en():
                    err_msg = f"TPMS API error，upload data failed，error：{err}"

                # ret_res = self.x_response("false", err_msg)
                error_msg_list.append(err_msg)

            self.log.info(f"--------command3--------------")
            if pcb_entity.is_repair_ng():
                if is_upload_ng_data == "Yes":
                    self.log.warning(f"部分拼板NG，只上传NG的拼板数据！")

                    for item in mes_param_list:
                        if "NG" in item:
                            pocket_param = {
                                "type": 3,
                                "request_param": item
                            }

                            ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param)

                            ret_str = ret.get('string')
                            if "ok" not in ret_str.lower():
                                error_msg = f"上传数据失败，cmd3调用失败，error：{ret_str}"

                                if other_param.lang_is_en():
                                    error_msg = f"api error, cmd3 call failed, error：{ret_str}"

                                error_msg_list.append(error_msg)
                                # return self.x_response("false", error_msg)
                        else:
                            log.warning("部分拼板NG，PASS拼板不调用cmd3接口！")
                else:
                    self.log.warning(f"部分拼板NG，不调用cmd3接口上传数据！")

            else:
                for item in mes_param_list:
                    pocket_param = {
                        "type": 3,
                        "request_param": item
                    }

                    ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, pocket_param)

                    ret_str = ret.get('string')
                    if "ok" not in ret_str.lower():
                        error_msg = f"上传数据失败，cmd3调用失败，error：{ret_str}"

                        if other_param.lang_is_en():
                            error_msg = f"api error, cmd3 call failed, error：{ret_str}"

                        error_msg_list.append(error_msg)
                        # return self.x_response("false", error_msg)
            self.log.info(f"--------command3--------------")

        else:
            cache_data = xutil.CacheUtil.get_cache_data()

            if not circular_list1.is_exist_item(review_path):
                date_now = datetime.now().strftime(xcons.FMT_DATE)  # 现在是几号
                last_date_now = cache_data.get("current_date", "")

                board_pass_count = cache_data.get("board_pass_count", 0)
                board_ng_count = cache_data.get("board_ng_count", 0)

                if date_now == last_date_now:
                    # 还是当天，投入量直接累加就好
                    board_pass_count += pass_count
                    board_ng_count += ng_count

                    cache_data["board_pass_count"] = board_pass_count
                    cache_data["board_ng_count"] = board_ng_count
                else:
                    # 第二天了, 投入量需要重新计算
                    cache_data["board_pass_count"] = pass_count
                    cache_data["board_ng_count"] = ng_count

                    cache_data["current_date"] = date_now  # 时间改变了，改变一下缓存的数据

                cache_data["cycle_time_status_data"] = pcb_entity.get_cycle_time()
                xutil.CacheUtil.save_cache_data(cache_data)
                circular_list1.add_item(review_path)
            else:
                self.log.warning(f"该数据包已统计过，本次不统计！")

            # 检测后发送！
            # 1. 从本地缓存的数据中获取 保养维护信息、直通率等信息、故障和时间
            maintain_camera = len(cache_data.get("maintain_camera", []))
            maintain_light_source = len(cache_data.get("maintain_light_source", []))
            maintain_lens = len(cache_data.get("maintain_lens", []))
            maintain_track_belt = len(cache_data.get("maintain_track_belt", []))
            maintain_servo_motor = len(cache_data.get("maintain_servo_motor", []))
            maintain_cylinder = cache_data.get("maintain_cylinder", 0)
            maintain_solenoid_valve = cache_data.get("maintain_solenoid_valve", 0)

            if tpms_switch == "Yes":
                param_url = f"{api_url_tpms}/api/dap/parameter"
                dap_param = {
                    "deviceId": device_id_tpms,
                    "data": {
                        "sn": pcb_sn,
                        "maintain_camera": maintain_camera,
                        "maintain_light_source": maintain_light_source,
                        "maintain_lens": maintain_lens,
                        "maintain_track_belt": maintain_track_belt,
                        "maintain_servo_motor": maintain_servo_motor,
                        "maintain_cylinder": maintain_cylinder,
                        "maintain_solenoid_valve": maintain_solenoid_valve,
                    }
                }

                ret = xrequest.RequestUtil.post_json(param_url, dap_param)
                if not ret.get('result'):
                    err_msg = f"TPMS接口调用异常，上传设备信息失败，error：{ret.get('message')}"
                    if other_param.lang_is_en():
                        err_msg = f"TPMS API error，upload dap parameter failed，error：{ret.get('message')}"

                    # ret_res = self.x_response("false", err_msg)
                    error_msg_list.append(err_msg)
            else:
                self.log.warning(f"未启用TPMS，不调用 /api/dap/parameter 接口！")

        if error_msg_list:
            err_msg_str = "\n".join(error_msg_list)
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{err_msg_str}")

        return self.x_response()

    # def login_mes(self, other_dao: OtherDao, other_param: Any):
    #     username, password = other_dao.get_login_info()
    #     window_ip = other_dao.get_value_by_cons_key('window_ip')
    #
    #     login_param = {
    #         "type": 6,
    #         "request_param": {
    #             "pIDData": username.ljust(40)[:40],
    #             "pPWData": password.ljust(40)[:40]
    #         }
    #     }
    #
    #     ret = xrequest.SocketUtil.send_data_to_window_station(window_ip, login_param)
    #     ret_str = ret.get('string')
    #     if str(ret_str) != '0':
    #         return self.x_response("false", f"mes接口异常，登录失败，error：{ret_str}")
    #
    #     return self.x_response()

    def on_exit(self):
        # 关闭配置器需关闭连接，否则连接会一直连着
        if self.bfh_client:
            self.bfh_client.diconnect()
            self.bfh_client = None

    def save_btn_on_window(self, main_window):
        bfh_upload_data = self.other_vo.get_value_by_cons_key("bfh_upload_data")
        if bfh_upload_data == "Yes":
            if not self.bfh_client:
                bfh_ip = self.other_vo.get_value_by_cons_key("bfh_ip")
                bfh_port = self.other_vo.get_value_by_cons_key("bfh_port")
                self.bfh_client = BFHClient(main_window, bfh_ip, bfh_port)
        else:
            if self.bfh_client:
                self.bfh_client.diconnect()
                self.bfh_client = None

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        window_ip = btn_vo.get_value_by_cons_key("window_ip")
        username_mes = btn_vo.get_value_by_cons_key("username_mes")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "check_connect":
            is_connect = xrequest.SocketUtil.check_window_port(window_ip)
            if not is_connect:
                err_msg = f"连接失败，请检查window网络是否连接，或者中转程序是否打开！"

                if other_param.lang_is_en():
                    err_msg = f"connect failed,please check window internet is connect, or transfer app is open!"

                return self.x_response("false", err_msg)
        elif btn_key == "login_mes":
            msg = login_mes(window_ip, username_mes)

            if 'ok' not in msg.lower():
                err_msg = f"接口响应异常，校验工号失败，cmd1调用失败，error：{msg}"

                if other_param.lang_is_en():
                    err_msg = f"api error, cmd1 call failed, error：{msg}"

                return self.x_response("false", err_msg)

            global_data["is_login"] = True

        elif btn_key == "maintain_clear":
            cache_data = xutil.CacheUtil.get_cache_data()

            cache_data["total_qty"] = 0
            cache_data["total_cycle_time"] = 0
            cache_data["maintain_camera"] = []
            cache_data["maintain_light_source"] = []
            cache_data["maintain_lens"] = []
            cache_data["maintain_track_belt"] = []
            cache_data["maintain_servo_motor"] = []
            cache_data["maintain_cylinder"] = 0
            cache_data["maintain_solenoid_valve"] = 0

            xutil.CacheUtil.save_cache_data(cache_data)

        elif btn_key == "bfh_setting_click":
            bfh_upload_data = btn_vo.get_value_by_cons_key("bfh_upload_data")
            if bfh_upload_data == "Yes":
                bfh_ip = btn_vo.get_value_by_cons_key("bfh_ip")
                bfh_port = btn_vo.get_value_by_cons_key("bfh_port")
                if self.bfh_client:
                    ret = self.bfh_client.connect(bfh_ip, bfh_port)
                else:
                    self.bfh_client = BFHClient(other_param, bfh_ip, bfh_port)
                    ret = self.bfh_client.connect(bfh_ip, bfh_port)

                if not ret:
                    return self.x_response('false', '连接失败')
            else:
                return self.x_response('false', '【上传数据给波峰焊】开关未开！')

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_tpms = other_vo.get_value_by_cons_key("api_url_tpms")
        device_id_tpms = other_vo.get_value_by_cons_key("device_id_tpms")
        tpms_switch = other_vo.get_value_by_cons_key("tpms_switch")

        old_code = other_vo.get_old_device_status_code()
        old_str = other_vo.get_device_status_str()

        # time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        # ---------------上传数据到设备状态----start
        device_status_url = other_vo.get_value_by_cons_key("device_status_url")
        device_status_version = other_vo.get_value_by_cons_key("device_status_version")
        device_status_line_id = other_vo.get_value_by_cons_key("device_status_line_id")
        device_status_device_id = other_vo.get_value_by_cons_key("device_status_device_id")

        status_code_v3 = other_vo.get_status_code_v3()
        x_request_device_status(
            device_status_url,
            device_status_version,
            device_status_line_id,
            device_status_device_id,
            status_code_v3,
        )
        # ---------------上传数据到设备状态----end

        event_id = 0
        if old_code == "02":
            event_id = 3
        elif old_code == "03":
            event_id = 4
        elif old_code == "01":
            event_id = 1
        elif old_code == "04":
            event_id = 6
        elif old_code == "11":
            event_id = 17
        else:
            pass

        device_code_v3 = other_vo.get_status_code_v3()
        device_str_v3 = other_vo.get_status_desc_v3()

        if event_id == 0:
            if device_code_v3 == "1001":
                event_id = 1
            elif device_code_v3 == "1002":
                event_id = 3
            elif device_code_v3 == "1003":
                event_id = 4
            elif device_code_v3 == "1004":
                event_id = 6

        time_now = datetime.now()

        if event_id != 0:
            in_board_fix = other_vo.get_value_by_cons_key("in_board_fix")
            out_board_fix = other_vo.get_value_by_cons_key("out_board_fix")

            if device_code_v3 == "1001":
                fix_time = in_board_fix
            elif device_code_v3 == "1004":
                fix_time = out_board_fix
            else:
                fix_time = "0"

            log.info(f"fix time: {fix_time}")

            if "+" in fix_time:
                fix_int = int(fix_time.replace("+", ""))
                time_now = time_now + timedelta(seconds=fix_int)

            elif "-" in fix_time:
                fix_int = int(fix_time.replace("-", ""))
                time_now = time_now - timedelta(seconds=fix_int)

            time_now_str = time_now.strftime(xcons.FMT_TIME_DEFAULT)

            status_param = {
                "deviceId": device_id_tpms,
                "eventId": event_id,
                "timeStamp": time_now_str,
                "parameter": ""
            }

            if tpms_switch == "Yes":
                status_url = f"{api_url_tpms}/api/dap/event"
                xrequest.RequestUtil.post_json(status_url, status_param)
            else:
                self.log.warning(f"未启用TPMS，不调用 /api/dap/event 接口！")
        else:
            self.log.warning(f"其他设备状态，不调用DeviceEventNotice接口！")

        if device_code_v3 in ["1001", "1003", "1004", "1005", "3001", "3002", "0001", "3004", "4003"]:
            self.log.warning(f"该状态不上传数据到接口：DeviceAlarmNotice, status:{device_code_v3}")
            return self.x_response()
        elif device_code_v3 == '1002':
            # 开始
            last_status_code = global_data.get('last_status_code', None)
            last_status_str = global_data.get('last_status_str', None)
            if last_status_code:
                # 有出现过异常状态, 上报解除异常
                if last_status_code in ["2001", "2002", "3003", "3005", "3006", "3007", "4001",
                                        "4002", "5001", "5002"]:
                    # 影响生产的 解除异常
                    _type = 4
                else:
                    _type = 2

                alarm_param = {
                    "deviceId": device_id_tpms,
                    "type": _type,
                    "code": last_status_code,
                    "message": last_status_str,
                }

                if tpms_switch == "Yes":
                    alarm_url = f"{api_url_tpms}/api/dap/alarm"
                    xrequest.RequestUtil.post_json(alarm_url, alarm_param)
                else:
                    self.log.warning(f"未启用TPMS，不调用 /api/dap/alarm 接口！")

                # 异常已解除，清空
                global_data['last_status_code'] = None
                global_data['last_status_str'] = None

        elif device_code_v3 in xcons.DEVICE_STATUS_V3:
            log.info(f"异常状态")
            # 异常
            if device_code_v3 in ["2001", "2002", "3003", "3005", "3006", "3007", "4001",
                                  "4002", "5001", "5002"]:
                _type = 3
            else:
                _type = 1

            alarm_param = {
                "deviceId": device_id_tpms,
                "type": _type,
                "code": device_code_v3,
                "message": device_str_v3,
            }

            if tpms_switch == "Yes":
                alarm_url = f"{api_url_tpms}/api/dap/alarm"
                xrequest.RequestUtil.post_json(alarm_url, alarm_param)
            else:
                self.log.warning(f"未启用TPMS，不调用 /api/dap/alarm 接口！")

            global_data['last_status_code'] = device_code_v3
            global_data['last_status_str'] = device_str_v3
        else:
            log.warning(f"未知的设备状态： {device_code_v3}，不处理！")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_tpms = other_vo.get_value_by_cons_key("api_url_tpms")
        device_id_tpms = other_vo.get_value_by_cons_key("device_id_tpms")
        tpms_switch = other_vo.get_value_by_cons_key("tpms_switch")

        body_param = {
            "deviceId": device_id_tpms,
            "timestamp": int(time.time() * 1000)
        }

        if tpms_switch == "Yes":
            heart_url = f"{api_url_tpms}/api/dap/heartbeat"
            xrequest.RequestUtil.post_json(heart_url, body_param)
        else:
            self.log.warning(f"未启用TPMS，不调用 /api/dap/heartbeat 接口！")

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        split_index = other_vo.get_value_by_cons_key("split_index")

        try:
            split_index = int(split_index)
        except Exception as err:
            return self.x_response("false", f"切割后N位必须为数字！error：{err}")

        barcode_map = other_vo.get_barcode_map()

        del barcode_map["-1"]
        del barcode_map["-2"]

        self.log.info(f"barcode map: {barcode_map}")

        scan_sn = list(barcode_map.values())

        if not any(scan_sn):
            return self.x_response("false", "未扫到任何条码，无法补录！")

        self.log.info(f"补全前的条码列表：{scan_sn}")
        new_sn_list = complete_array(scan_sn, split_index=split_index)
        self.log.info(f"补全后的条码列表：{scan_sn}")

        return self.x_response("true", ",".join(new_sn_list))

    def custom_cron_function2(self, other_vo: OtherVo, main_window, other_param: Any):
        device_status_url = other_vo.get_value_by_cons_key("device_status_url")
        device_status_version = other_vo.get_value_by_cons_key("device_status_version")
        device_status_line_id = other_vo.get_value_by_cons_key("device_status_line_id")
        device_status_device_id = other_vo.get_value_by_cons_key("device_status_device_id")

        # xutil.CacheUtil.get("last_device_status_v3")

        x_request_device_status(
            device_status_url,
            device_status_version,
            device_status_line_id,
            device_status_device_id,
            "xxxx",  # 定时上传时默认取上一个状态
        )
