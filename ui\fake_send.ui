<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>764</width>
    <height>823</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>模拟主软件触发</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QLabel" name="label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Plain</enum>
        </property>
        <property name="text">
         <string>从Mes获取条码</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_6">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <property name="sizeConstraint">
          <enum>QLayout::SetDefaultConstraint</enum>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>条码:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="line_pcb_sn">
           <property name="text">
            <string>pcb_barcode001</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_get_sn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模拟触发</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QFrame" name="frame_4">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <widget class="QLabel" name="label_5">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>发送设备状态</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_8">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="label_8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>设备状态:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="combo_device_status">
           <item>
            <property name="text">
             <string>进板</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>开始检测</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>停止检查</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>出板</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>安全门</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>调试</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>数据超时</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>板卡 NG</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>紧急故障</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>流程错误</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>直通率告警</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Marker 错误</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>其他错误</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_send_device_status">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模拟触发</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QFrame" name="frame_3">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <widget class="QLabel" name="label_6">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>发送数据 (数据包路径也可直接复制过来)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="send_type_inspector">
        <property name="text">
         <string>检测完发送</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="send_type_repair">
        <property name="text">
         <string>复判完发送</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_7">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>数据包路径:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="line_review_path"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_9">
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <widget class="QPushButton" name="btn_select_review_path">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>选择文件夹</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_send_data">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>模拟触发</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QFrame" name="frame_2">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <widget class="QLabel" name="label_3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>条码校验</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_5">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>条码:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="line_sn_list">
           <property name="text">
            <string>sn001,sn002,sn003</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_check_sn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模拟触发</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1" rowspan="7">
    <widget class="QFrame" name="frame_11">
     <property name="layoutDirection">
      <enum>Qt::LeftToRight</enum>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_6">
      <item>
       <widget class="QLabel" name="label_10">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>日志打印</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPlainTextEdit" name="text_log_print">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="maximumBlockCount">
         <number>1000</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_clear_log">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="layoutDirection">
         <enum>Qt::RightToLeft</enum>
        </property>
        <property name="text">
         <string>清除日志</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="5" column="0">
    <widget class="QFrame" name="frame_12">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_7">
      <item>
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>发送设备状态v3</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_13">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <property name="leftMargin">
          <number>1</number>
         </property>
         <property name="topMargin">
          <number>1</number>
         </property>
         <property name="rightMargin">
          <number>1</number>
         </property>
         <property name="bottomMargin">
          <number>1</number>
         </property>
         <item>
          <widget class="QLabel" name="label_12">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>设备状态:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="combo_device_status_v3">
           <item>
            <property name="text">
             <string>1001--&gt;进板</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1002--&gt;启动</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1003--&gt;暂停</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1004--&gt;出板</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1005--&gt;换线</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>2001--&gt;急停</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>2002--&gt;安全门报警</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3001--&gt;待料（空闲）</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3002--&gt;出板超时</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3003--&gt;条码校验过站失败</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3004--&gt;上传检测信息失败</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3005--&gt;磁盘已满</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3006--&gt;掉板</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3007--&gt;EAP告警</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4001--&gt;直通率告警</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4002--&gt;Mark点错误</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4003--&gt;板卡NG</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>5001--&gt;风扇停转</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>5002--&gt;相机连接失败</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>0001--&gt;蜂鸣报警解除</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1011--&gt;检测中</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1012--&gt;检测第二段</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1013--&gt;人工复检</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1014--&gt;人工复检</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3010--&gt;等待进板</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1021--&gt;主软件启动</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1022--&gt;主软件退出</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4010--&gt;扫码失败</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_device_status_v3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模拟触发</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="4" column="0">
    <widget class="QFrame" name="frame_10">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QLabel" name="label_9">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>发送空闲状态</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_send_idle_status">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模拟触发</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
