# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/06/11 16:40
# Author     ：chencb
# version    ：python 3.8
# Description：中山紫文星 https://jira.cvte.com/browse/ATAOI_2019-40123
"""
from typing import Any
from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "customer": ["中山紫文星", "zhongshanziwenxing"],
        "version": "release v1.0.0.1",
        "device": " AIS43X",
        "feature": ["生成txt文档"],
        "author": "chenchongbing",
        "release": """
date: 2025-06-12 17:30  生成双轨txt文档
""", }

    path = {
        "save_txt_path_1": {
            "ui_name": "轨道1 txt保存路径",
            "value": "",
        },
        "save_txt_path_2": {
            "ui_name": "轨道2 txt保存路径",
            "value": "",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()
        # 只生成复判后的数据
        if inspect_type == xcons.INSPECTOR:
            self.log.info("复判后才生成txt，这次为机器检测发送，直接返回不处理")
            return self.x_response()

        save_txt_path_1 = data_vo.get_value_by_cons_key("save_txt_path_1", not_null=True)
        save_txt_path_2 = data_vo.get_value_by_cons_key("save_txt_path_2", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        # 文件命名：时间戳.txt
        if pcb_entity.track_index == 1:
            save_txt_path = save_txt_path_1
        else:
            save_txt_path = save_txt_path_2
        file_path = f"{save_txt_path}/{test_time}.txt"

        txt_content = pcb_entity.pcb_barcode
        xutil.FileUtil.write_content_to_file(file_path, txt_content)

        return self.x_response()
