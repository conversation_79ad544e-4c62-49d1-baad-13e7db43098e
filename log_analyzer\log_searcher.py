#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志搜索模块
提供多种搜索和过滤功能
"""

import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable
from dataclasses import dataclass
try:
    from .log_parser import LogEntry
except ImportError:
    from log_parser import LogEntry


@dataclass
class SearchCriteria:
    """搜索条件"""
    keywords: List[str] = None
    levels: List[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_codes: List[str] = None
    files: List[str] = None
    regex_pattern: Optional[str] = None
    case_sensitive: bool = False
    exclude_keywords: List[str] = None


class LogSearcher:
    """日志搜索器"""
    
    def __init__(self):
        self.entries: List[LogEntry] = []
        self.indexed_entries: Dict[str, List[LogEntry]] = {}
        
    def load_entries(self, entries: List[LogEntry]):
        """加载日志条目"""
        self.entries = entries
        self._build_indexes()
    
    def _build_indexes(self):
        """构建索引以提高搜索性能"""
        self.indexed_entries = {
            'level': {},
            'file': {},
            'error_code': {},
            'date': {}
        }
        
        for entry in self.entries:
            # 按级别索引
            level = entry.level
            if level not in self.indexed_entries['level']:
                self.indexed_entries['level'][level] = []
            self.indexed_entries['level'][level].append(entry)
            
            # 按文件索引
            file_path = entry.file_path
            if file_path not in self.indexed_entries['file']:
                self.indexed_entries['file'][file_path] = []
            self.indexed_entries['file'][file_path].append(entry)
            
            # 按错误码索引
            if entry.error_code:
                error_code = entry.error_code
                if error_code not in self.indexed_entries['error_code']:
                    self.indexed_entries['error_code'][error_code] = []
                self.indexed_entries['error_code'][error_code].append(entry)
            
            # 按日期索引
            if entry.timestamp:
                date_key = entry.timestamp.strftime('%Y-%m-%d')
                if date_key not in self.indexed_entries['date']:
                    self.indexed_entries['date'][date_key] = []
                self.indexed_entries['date'][date_key].append(entry)

    def search(self, criteria: SearchCriteria) -> List[LogEntry]:
        """根据条件搜索日志"""
        results = self.entries.copy()
        
        # 按级别过滤
        if criteria.levels:
            results = self._filter_by_levels(results, criteria.levels)
        
        # 按时间范围过滤
        if criteria.start_time or criteria.end_time:
            results = self._filter_by_time_range(results, criteria.start_time, criteria.end_time)
        
        # 按文件过滤
        if criteria.files:
            results = self._filter_by_files(results, criteria.files)
        
        # 按错误码过滤
        if criteria.error_codes:
            results = self._filter_by_error_codes(results, criteria.error_codes)
        
        # 按关键词过滤
        if criteria.keywords:
            results = self._filter_by_keywords(results, criteria.keywords, criteria.case_sensitive)
        
        # 排除关键词
        if criteria.exclude_keywords:
            results = self._exclude_keywords(results, criteria.exclude_keywords, criteria.case_sensitive)
        
        # 正则表达式过滤
        if criteria.regex_pattern:
            results = self._filter_by_regex(results, criteria.regex_pattern, criteria.case_sensitive)
        
        return results

    def _filter_by_levels(self, entries: List[LogEntry], levels: List[str]) -> List[LogEntry]:
        """按日志级别过滤"""
        return [entry for entry in entries if entry.level in levels]

    def _filter_by_time_range(self, entries: List[LogEntry], 
                             start_time: Optional[datetime], 
                             end_time: Optional[datetime]) -> List[LogEntry]:
        """按时间范围过滤"""
        filtered = []
        for entry in entries:
            if not entry.timestamp:
                continue
            
            if start_time and entry.timestamp < start_time:
                continue
            
            if end_time and entry.timestamp > end_time:
                continue
            
            filtered.append(entry)
        
        return filtered

    def _filter_by_files(self, entries: List[LogEntry], files: List[str]) -> List[LogEntry]:
        """按文件过滤"""
        return [entry for entry in entries if entry.file_path in files]

    def _filter_by_error_codes(self, entries: List[LogEntry], error_codes: List[str]) -> List[LogEntry]:
        """按错误码过滤"""
        return [entry for entry in entries if entry.error_code in error_codes]

    def _filter_by_keywords(self, entries: List[LogEntry], keywords: List[str], 
                           case_sensitive: bool = False) -> List[LogEntry]:
        """按关键词过滤"""
        filtered = []
        for entry in entries:
            message = entry.message if case_sensitive else entry.message.lower()
            
            # 所有关键词都必须存在（AND逻辑）
            if all((keyword if case_sensitive else keyword.lower()) in message 
                   for keyword in keywords):
                filtered.append(entry)
        
        return filtered

    def _exclude_keywords(self, entries: List[LogEntry], exclude_keywords: List[str], 
                         case_sensitive: bool = False) -> List[LogEntry]:
        """排除包含指定关键词的条目"""
        filtered = []
        for entry in entries:
            message = entry.message if case_sensitive else entry.message.lower()
            
            # 如果不包含任何排除关键词，则保留
            if not any((keyword if case_sensitive else keyword.lower()) in message 
                      for keyword in exclude_keywords):
                filtered.append(entry)
        
        return filtered

    def _filter_by_regex(self, entries: List[LogEntry], pattern: str, 
                        case_sensitive: bool = False) -> List[LogEntry]:
        """按正则表达式过滤"""
        try:
            flags = 0 if case_sensitive else re.IGNORECASE
            regex = re.compile(pattern, flags)
            
            return [entry for entry in entries if regex.search(entry.message)]
        except re.error:
            return entries

    def quick_search(self, query: str, limit: int = 100) -> List[LogEntry]:
        """快速搜索"""
        if not query:
            return self.entries[:limit]
        
        criteria = SearchCriteria(
            keywords=[query],
            case_sensitive=False
        )
        
        results = self.search(criteria)
        return results[:limit]

    def get_recent_errors(self, hours: int = 24, limit: int = 50) -> List[LogEntry]:
        """获取最近的错误日志"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        criteria = SearchCriteria(
            levels=['ERROR', 'CRITICAL'],
            start_time=start_time,
            end_time=end_time
        )
        
        results = self.search(criteria)
        return sorted(results, key=lambda x: x.timestamp or datetime.min, reverse=True)[:limit]

    def get_error_patterns(self) -> Dict[str, int]:
        """分析错误模式"""
        error_patterns = {}
        
        for entry in self.entries:
            if entry.level in ['ERROR', 'CRITICAL']:
                # 简化错误消息，提取模式
                pattern = self._extract_error_pattern(entry.message)
                error_patterns[pattern] = error_patterns.get(pattern, 0) + 1
        
        # 按频率排序
        return dict(sorted(error_patterns.items(), key=lambda x: x[1], reverse=True))

    def _extract_error_pattern(self, message: str) -> str:
        """提取错误模式"""
        # 移除具体的数值、路径等变化的部分
        pattern = re.sub(r'\d+', 'N', message)  # 数字替换为N
        pattern = re.sub(r'[/\\][^\s]+', '/PATH', pattern)  # 路径替换
        pattern = re.sub(r'\b\w+@\w+\.\w+', 'EMAIL', pattern)  # 邮箱替换
        pattern = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', 'IP', pattern)  # IP替换
        
        return pattern.strip()

    def get_statistics(self, entries: List[LogEntry] = None) -> Dict:
        """获取统计信息"""
        if entries is None:
            entries = self.entries
        
        if not entries:
            return {}
        
        stats = {
            'total': len(entries),
            'levels': {},
            'files': {},
            'error_codes': {},
            'hourly_distribution': {},
            'daily_distribution': {}
        }
        
        for entry in entries:
            # 级别统计
            level = entry.level
            stats['levels'][level] = stats['levels'].get(level, 0) + 1
            
            # 文件统计
            file_name = entry.file_path.split('/')[-1] if '/' in entry.file_path else entry.file_path
            stats['files'][file_name] = stats['files'].get(file_name, 0) + 1
            
            # 错误码统计
            if entry.error_code:
                stats['error_codes'][entry.error_code] = stats['error_codes'].get(entry.error_code, 0) + 1
            
            # 时间分布统计
            if entry.timestamp:
                hour_key = entry.timestamp.strftime('%H')
                day_key = entry.timestamp.strftime('%Y-%m-%d')
                
                stats['hourly_distribution'][hour_key] = stats['hourly_distribution'].get(hour_key, 0) + 1
                stats['daily_distribution'][day_key] = stats['daily_distribution'].get(day_key, 0) + 1
        
        return stats
