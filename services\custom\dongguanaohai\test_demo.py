# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_demo.py
# Time       ：2025/6/27 上午10:41
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

ret_str1 = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <GetMultiboardListResponse xmlns="http://tempuri.org/">
            <GetMultiboardListResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"
                                     xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>false</a:string>
                <a:string>获取失败</a:string>
            </GetMultiboardListResult>
        </GetMultiboardListResponse>
    </s:Body>
</s:Envelope>
"""

ret_str2 = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CheckRouteForSerialNumberResponse xmlns="http://tempuri.org/">
            <CheckRouteForSerialNumberResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"
                                             xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>false</a:string>
                <a:string>条码校验失败</a:string>
            </CheckRouteForSerialNumberResult>
        </CheckRouteForSerialNumberResponse>
    </s:Body>
</s:Envelope>
"""

ret_str3 = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <h:Message xmlns:h="http://tempuri.org/">上传失败</h:Message>
    </s:Header>
    <s:Body>
        <ResultMessage xmlns="http://tempuri.org/">
            <IsSuccessed>false</IsSuccessed>
        </ResultMessage>
    </s:Body>
</s:Envelope>"""

if __name__ == '__main__':
    # root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    #
    # get_result = root1[0][0][0]
    #
    # r1 = get_result[0].text
    # r2 = get_result[1].text
    #
    # # print(root1[0][0][0].tag)
    # print(r1, r2)

    # root2 = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
    # get_result = root2[0][0][0]
    #
    # r1 = get_result[0].text
    # r2 = get_result[1].text
    # print(r1, r2)

    root3 = xutil.XmlUtil.get_xml_root_by_str(ret_str3)
    item_header = root3[0][0].text
    item_body = root3[1][0][0].text
    print(item_header)
    print(item_body)
