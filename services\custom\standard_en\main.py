# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/7/14 下午4:03
# Author     ：sch
# version    ：python 3.8
# Description：标准版 -- 英文版
"""
import json
from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

csv_pcb_panel_template = """device_name,{device_name}
pcb_sn,{pcb_sn}
pcb_track_line,{pcb_track_line}
pcb_board_side,{pcb_board_side}
pcb_test_time,{pcb_test_time}
pcb_cycle_time,{pcb_cycle_time}
pcb_project_name,{pcb_project_name}
pcb_robot_result,{pcb_robot_result}
pcb_user_result,{pcb_user_result}
pcb_final_result,{pcb_final_result}
pcb_repair_user,{pcb_repair_user}
pcb_board_number,{pcb_board_number}
pcb_board_robot_ng_number,{pcb_board_robot_ng_number}
pcb_board_user_ng_number,{pcb_board_user_ng_number}
pcb_board_repass_number,{pcb_board_repass_number}
pcb_comp_number,{pcb_comp_number}
pcb_comp_robot_ng_number,{pcb_comp_robot_ng_number}
pcb_comp_user_ng_number,{pcb_comp_user_ng_number}
pcb_comp_repass_number,{pcb_comp_repass_number}

board_no,board_sn,board_robot_result,board_user_result,board_final_result,comp_designator,comp_part,comp_package,comp_type,comp_robot_code,comp_robot_result,comp_user_code,comp_user_result,comp_image{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

txt_pcb_panel_template = """device_name:{device_name}
pcb_sn:{pcb_sn}
pcb_track_line:{pcb_track_line}
pcb_board_side:{pcb_board_side}
pcb_test_time:{pcb_test_time}
pcb_cycle_time:{pcb_cycle_time}
pcb_project_name:{pcb_project_name}
pcb_robot_result:{pcb_robot_result}
pcb_user_result:{pcb_user_result}
pcb_final_result:{pcb_final_result}
pcb_repair_user:{pcb_repair_user}
pcb_board_number:{pcb_board_number}
pcb_board_robot_ng_number:{pcb_board_robot_ng_number}
pcb_board_user_ng_number:{pcb_board_user_ng_number}
pcb_board_repass_number:{pcb_board_repass_number}
pcb_comp_number:{pcb_comp_number}
pcb_comp_robot_ng_number:{pcb_comp_robot_ng_number}
pcb_comp_user_ng_number:{pcb_comp_user_ng_number}
pcb_comp_repass_number:{pcb_comp_repass_number}

{BoardData}
"""

txt_board_panel_template = """
========================
board_no:{board_no}
board_sn:{board_sn}
board_robot_result:{board_robot_result}
board_user_result:{board_user_result}
board_final_result:{board_final_result}{CompData}
"""

txt_comp_panel_template = """
comp_designator:{comp_designator}----comp_part:{comp_part}----comp_package:{comp_package}----comp_type:{comp_type}----comp_robot_code:{comp_robot_code}----comp_robot_result:{comp_robot_result}----comp_user_code:{comp_user_code}----comp_user_result:{comp_user_result}----comp_image:{comp_image}"""

xml_pcb_panel_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_board_side="{pcb_board_side}" pcb_test_time="{pcb_test_time}" pcb_cycle_time="{pcb_cycle_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_robot_ng_number="{pcb_board_robot_ng_number}" pcb_board_user_ng_number="{pcb_board_user_ng_number}" pcb_board_repass_number="{pcb_board_repass_number}"
  pcb_comp_number="{pcb_comp_number}" pcb_comp_robot_ng_number="{pcb_comp_robot_ng_number}" pcb_comp_user_ng_number="{pcb_comp_user_ng_number}" pcb_comp_repass_number="{pcb_comp_repass_number}" >
    <BoardList>{BoardData}
    </BoardList>
</Panel>
"""

xml_board_panel_template = """
        <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}">{CompData}
        </Board>"""

xml_comp_panel_template = """
            <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""

txt_board_board_template = """device_name:{device_name}
pcb_sn:{pcb_sn}
pcb_track_line:{pcb_track_line}
pcb_board_side:{pcb_board_side}
pcb_test_time:{pcb_test_time}
pcb_cycle_time:{pcb_cycle_time}
pcb_project_name:{pcb_project_name}
pcb_robot_result:{pcb_robot_result}
pcb_user_result:{pcb_user_result}
pcb_final_result:{pcb_final_result}
pcb_repair_user:{pcb_repair_user}
pcb_board_number:{pcb_board_number}
pcb_comp_number:{pcb_comp_number}
board_no:{board_no}
board_sn:{board_sn}
board_robot_result:{board_robot_result}
board_user_result:{board_user_result}
board_final_result:{board_final_result}
board_comp_number:{board_comp_number}
board_comp_robot_ng_number:{board_comp_robot_ng_number}
board_comp_user_ng_number:{board_comp_user_ng_number}
board_comp_repass_number:{board_comp_repass_number}

{CompData}
"""

txt_comp_board_template = """
comp_designator:{comp_designator}----comp_part:{comp_part}----comp_package:{comp_package}----comp_type:{comp_type}----comp_robot_code:{comp_robot_code}----comp_robot_result:{comp_robot_result}----comp_user_code:{comp_user_code}----comp_user_result:{comp_user_result}----comp_image:{comp_image}"""

csv_board_board_template = """device_name,{device_name}
pcb_sn,{pcb_sn}
pcb_track_line,{pcb_track_line}
pcb_board_side,{pcb_board_side}
pcb_test_time,{pcb_test_time}
pcb_cycle_time,{pcb_cycle_time}
pcb_project_name,{pcb_project_name}
pcb_robot_result,{pcb_robot_result}
pcb_user_result,{pcb_user_result}
pcb_final_result,{pcb_final_result}
pcb_repair_user,{pcb_repair_user}
pcb_board_number,{pcb_board_number}
pcb_comp_number,{pcb_comp_number}
board_no,{board_no}
board_sn,{board_sn}
board_robot_result,{board_robot_result}
board_user_result,{board_user_result}
board_final_result,{board_final_result}
board_comp_number,{board_comp_number}
board_comp_robot_ng_number,{board_comp_robot_ng_number}
board_comp_user_ng_number,{board_comp_user_ng_number}
board_comp_repass_number,{board_comp_repass_number}

comp_designator,comp_part,comp_package,comp_type,comp_robot_code,comp_robot_result,comp_user_code,comp_user_result,comp_image{CompData}
"""

csv_comp_board_template = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

xml_board_board_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_board_side="{pcb_board_side}" pcb_test_time="{pcb_test_time}" pcb_cycle_time="{pcb_cycle_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_number="{pcb_board_number}" pcb_comp_number="{pcb_comp_number}">
    <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}"
    board_comp_number="{board_comp_number}" board_comp_robot_ng_number="{board_comp_robot_ng_number}" board_comp_user_ng_number="{board_comp_user_ng_number}" board_comp_repass_number="{board_comp_repass_number}" >{CompData}
    </Board>
</Panel>
"""

xml_comp_board_template = """
        <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "standard_en release v1.0.0.7",
        "device": "20x,30x,40x",
        "feature": ["UploadData"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-14 16:04  init
date: 2023-07-17 16:06  Save data locally
date: 2024-01-16 10:41  Add newline configuration item
date: 2024-03-08 11:43  output board side info
date: 2024-04-01 11:27  add ftp upload and add param -> `pcb_board_side` `pcb_cycle_time`
date: 2025-05-29 10:20  Lock English option
""", }

    other_combo = {
        "is_upload_mes": {
            "ui_name": "Upload Json Data To Mes",
            "item": [
                "Yes",
                "No"
            ],
            "value": "No"
        },
        "is_upload_ftp": {
            "ui_name": "Upload Data To Ftp",
            "item": [
                "Yes",
                "No"
            ],
            "value": "No"
        },
        "ftp_path_rule": {
            "ui_name": "FTP Path Rule",
            "item": [
                "FTP Path",
                "FTP Path/DeviceName",
                "FTP Path/Date(yyyymmdd)",
                "FTP Path/DeviceName/Date(yyyymmdd)",
                "FTP Path/Date(yyyymmdd)/DeviceName",
            ],
            "value": "FTP Path/DeviceName/Date(yyyymmdd)"
        }
    }

    other_form = {
        "mes_api_url": {
            "ui_name": "Mes API Url",
            "value": ""
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP Path",
            "value": "/MES/SMT"
        },
    }

    combo = {
        "is_save_local": {
            "ui_name": "Save Data To Path",
            "item": [
                "Yes",
                "No",
            ],
            "value": "No"
        },
        "file_type": {
            "ui_name": "File Type",
            "item": [
                "csv",
                "txt",
                "json",
                "xml",
            ],
            "value": "csv"
        },
        "save_type": {
            "ui_name": "Save Type",
            "item": [
                "Panel",
                "Board",
            ],
            "value": "Panel"
        },
        "panel_filename": {
            "ui_name": "Panel FileName Rule",
            "item": [
                "Time_PcbSn",
                "PcbSn_Time",
                "PcbSn",
                "Time",
                "---",
            ],
            "value": "Time_PcbSn"
        },
        "board_filename": {
            "ui_name": "Board FileName Rule",
            "item": [
                'Time_BoardSn',
                'BoardSn_Time',
                'BoardSn',
                'Time_BoardSn_BoardNo',
                'Time_BoardNo',
                'BoardNo_Time',
                "---",
            ],
            "value": "Time_BoardNo"
        },
        "save_path_type": {
            "ui_name": "SavePath Rule",
            "item": [
                'SavePath',
                'SavePath/Date(yyyymmdd)',
                'SavePath/DeviceName',
                'SavePath/DeviceName/Date(yyyymmdd)',
                'SavePath/Date(yyyymmdd)/DeviceName',
            ],
            "value": "SavePath/DeviceName/Date(yyyymmdd)"
        },
        "comp_data_output": {
            "ui_name": "CompDataOutput",
            "item": [
                "All",
                "OnlyRepairNG",
                "OnlyInspectNG",
            ],
            "value": "All"
        },
        "newline_type": {
            "ui_name": "NewLine Rule",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        },
    }

    path = {
        "save_path": {
            "ui_name": "SavePath",
            "value": ""
        },
    }

    form = {
        "device_name": {
            "ui_name": "DeviceName",
            "value": ""
        }
    }

    def __init__(self):
        self.set_lang_to_en(force_set=True)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        is_upload_mes = data_vo.get_value_by_cons_key("is_upload_mes")
        mes_api_url = data_vo.get_value_by_cons_key("mes_api_url")
        is_save_local = data_vo.get_value_by_cons_key("is_save_local")
        file_type = data_vo.get_value_by_cons_key("file_type")
        save_type = data_vo.get_value_by_cons_key("save_type")
        panel_filename = data_vo.get_value_by_cons_key("panel_filename")
        board_filename = data_vo.get_value_by_cons_key("board_filename")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path_type = data_vo.get_value_by_cons_key("save_path_type")
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")
        newline_type = data_vo.get_value_by_cons_key("newline_type")

        is_upload_ftp = data_vo.get_value_by_cons_key("is_upload_ftp")
        ftp_path_rule = data_vo.get_value_by_cons_key("ftp_path_rule")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        if save_type == "Panel" and panel_filename == "---":
            return self.x_response("false", "Please Select Panel FileName Rule！")

        if save_type == "Board" and board_filename == "---":
            return self.x_response("false", "Please Select Board FileName Rule！")

        if file_type != "json" and is_upload_mes == "Yes":
            return self.x_response("false",
                                   "Only JSON format is supported for uploading"
                                   " to the Mes server. Please select the File Type to JSON")

        self.log.info(f"file type：{file_type}")

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        time_date = time_str[:8]
        if save_path_type == "SavePath":
            dst_path = f"{save_path}"
        elif save_path_type == "SavePath/Date(yyyymmdd)":
            dst_path = f"{save_path}/{time_date}"
        elif save_path_type == "SavePath/DeviceName":
            dst_path = f"{save_path}/{device_name}"
        elif save_path_type == "SavePath/DeviceName/Date(yyyymmdd)":
            dst_path = f"{save_path}/{device_name}/{time_date}"
        elif save_path_type == "SavePath/Date(yyyymmdd)/DeviceName":
            dst_path = f"{save_path}/{time_date}/{device_name}"
        else:
            return self.x_response("false", f"Unsupported save path format：{save_path_type}")

        if is_save_local == "Yes":
            xutil.FileUtil.ensure_dir_exist(dst_path)

        if is_upload_ftp == "Yes":
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()

            if ftp_path_rule == "FTP Path/DeviceName":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{device_name}")
            elif ftp_path_rule == "FTP Path/Date(yyyymmdd)":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{time_date}")
            elif ftp_path_rule == "FTP Path/DeviceName/Date(yyyymmdd)":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{device_name}/{time_date}")
            elif ftp_path_rule == "FTP Path/Date(yyyymmdd)/DeviceName":
                ftp_client.cd_or_mkdir(f"{ftp_path}/{time_date}/{device_name}")
            else:
                ftp_client.cd_or_mkdir(ftp_path)

        else:
            ftp_client = None

        if save_type == "Panel":
            board_data = []
            pcb_board_user_ng_number = 0
            pcb_board_robot_ng_number = 0
            pcb_comp_user_ng_number = 0
            pcb_comp_robot_ng_number = 0
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                barcode = board_entity.barcode
                board_no = board_entity.board_no

                if board_entity.is_repair_ng():
                    pcb_board_user_ng_number += 1

                if board_entity.is_robot_ng():
                    pcb_board_robot_ng_number += 1

                pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
                pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

                comp_data = []
                for comp_entity in board_entity.yield_comp_entity():

                    if comp_data_output == "All" or \
                            (comp_data_output == "OnlyRepairNG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "OnlyInspectNG" and comp_entity.is_robot_ng()):
                        comp_data.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

                board_data.append({
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_data": comp_data,
                })
            pcb_data = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
                "pcb_board_user_ng_number": pcb_board_user_ng_number,
                "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
                "pcb_comp_number": pcb_entity.comp_count,
                "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
                "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
                "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
                "board_data": board_data
            }

            if panel_filename == "Time_PcbSn":
                dst_filename = f"{time_str}_{pcb_sn}"
            elif panel_filename == "PcbSn_Time":
                dst_filename = f"{pcb_sn}_{time_str}"
            elif panel_filename == "PcbSn":
                dst_filename = f"{pcb_sn}"
            elif panel_filename == "Time":
                dst_filename = f"{time_str}"
            else:
                return self.x_response("false", f"Unsupported panel file name format：{panel_filename}")

            if file_type == "csv":
                dst_filename = f"{dst_filename}.csv"
                dst_filepath = f"{dst_path}/{dst_filename}"

                comp_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    for _comp_data in _board_data.get("comp_data", {}):
                        _comp_data.update(_board_data)
                        comp_data_str += csv_comp_panel_template.format(**_comp_data)

                pcb_data["CompData"] = comp_data_str
                pcb_content = csv_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                if is_save_local == "Yes":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

                if is_upload_ftp == "Yes":
                    ftp_client.upload_content(dst_filename, pcb_content)

            elif file_type == "txt":
                dst_filename = f"{dst_filename}.txt"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += txt_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += txt_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = txt_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                if is_save_local == "Yes":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

                if is_upload_ftp == "Yes":
                    ftp_client.upload_content(dst_filename, pcb_content)

            elif file_type == "json":
                dst_filename = f"{dst_filename}.json"
                dst_filepath = f"{dst_path}/{dst_filename}"

                pcb_data_str = json.dumps(pcb_data, indent=4, ensure_ascii=False)

                if newline_type == 'window':
                    pcb_data_str = pcb_data_str.replace('\n', '\r\n')

                if is_save_local == "Yes":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_data_str)

                if is_upload_mes == "Yes":
                    ret = xrequest.RequestUtil.post_json(mes_api_url, pcb_data)
                    if str(ret.get("code")) != "200":
                        return self.x_response("false",
                                               f"mes api exception，upload data failed，error：{ret.get('message')}")

                if is_upload_ftp == "Yes":
                    ftp_client.upload_content(dst_filename, pcb_data_str)

            elif file_type == "xml":
                dst_filename = f"{dst_filename}.xml"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += xml_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += xml_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = xml_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                if is_save_local == "Yes":
                    xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

                if is_upload_ftp == "Yes":
                    ftp_client.upload_content(dst_filename, pcb_content)

            else:
                return self.x_response("false", f"Unsupported file type：{file_type}")

        elif save_type == "Board":
            for board_entity in pcb_entity.yield_board_entity():
                board_sn = board_entity.barcode
                board_no = board_entity.board_no

                comp_data_list = []
                for comp_entity in board_entity.yield_comp_entity():

                    if comp_data_output == "All" or \
                            (comp_data_output == "OnlyRepairNG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "OnlyInspectNG" and comp_entity.is_robot_ng()):
                        comp_data_list.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

                board_data_fmt = {
                    "device_name": device_name,
                    "pcb_sn": pcb_sn,
                    "pcb_track_line": pcb_entity.track_index,
                    "pcb_board_side": pcb_entity.board_side,
                    "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                    "pcb_cycle_time": pcb_entity.get_cycle_time(),
                    "pcb_project_name": pcb_entity.project_name,
                    "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                    "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                    "pcb_final_result": pcb_entity.get_final_result(),
                    "pcb_repair_user": pcb_entity.repair_user,
                    "pcb_board_number": pcb_entity.board_count,
                    "pcb_comp_number": pcb_entity.comp_count,

                    "board_sn": board_sn,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),

                    "board_comp_number": board_entity.comp_total_number,
                    "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                    "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                    "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                }

                if board_filename == "Time_BoardSn":
                    filename = f"{time_str}_{board_sn}"
                elif board_filename == "BoardSn_Time":
                    filename = f"{board_sn}_{time_str}"
                elif board_filename == "BoardSn":
                    filename = f"{board_sn}"
                elif board_filename == "Time_BoardSn_BoardNo":
                    filename = f"{time_str}_{board_sn}_{board_no}"
                elif board_filename == "Time_BoardNo":
                    filename = f"{time_str}_{board_no}"
                elif board_filename == "BoardNo_Time":
                    filename = f"{board_no}_{time_str}"
                else:
                    return self.x_response("false", f"Unsupported board file name format")

                comp_data = ""
                if file_type == "txt":
                    filename = f"{filename}.txt"
                    for item in comp_data_list:
                        comp_data += txt_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = txt_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "Yes":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_ftp == "Yes":
                        ftp_client.upload_content(filename, board_data)

                elif file_type == "xml":
                    filename = f"{filename}.xml"
                    for item in comp_data_list:
                        comp_data += xml_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = xml_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "Yes":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_ftp == "Yes":
                        ftp_client.upload_content(filename, board_data)

                elif file_type == "json":
                    filename = f"{filename}.json"
                    board_data_fmt["comp_data"] = comp_data_list

                    board_data = json.dumps(board_data_fmt, indent=4, ensure_ascii=False)
                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "Yes":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_mes == "Yes":
                        ret = xrequest.RequestUtil.post_json(mes_api_url, board_data_fmt)
                        if str(ret.get("code")) != "200":
                            return self.x_response("false",
                                                   f"mes api exception，upload data failed，error：{ret.get('message')}")

                    if is_upload_ftp == "Yes":
                        ftp_client.upload_content(filename, board_data)

                elif file_type == "csv":
                    filename = f"{filename}.csv"
                    for item in comp_data_list:
                        comp_data += csv_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = csv_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    if is_save_local == "Yes":
                        xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                    if is_upload_ftp == "Yes":
                        ftp_client.upload_content(filename, board_data)

                else:
                    return self.x_response("false", "Unsupported file type")

        else:
            return self.x_response("false", f"Unsupported save type")

        if ftp_client is not None:
            ftp_client.close()

        return self.x_response()

    def hock_other_setting(self, other_view_obj):
        other_view_obj.setting_lang.removeItem(0)
