# !/usr/bin/env python
# -*-coding:utf-8 -*-
"""
# File       : main.py
# Time       ：2025/05/06 18:29
# Author     ："wxc"
# version    ：python 3.8
# Description：电科54所
"""
import json
import math
import os
import traceback
from datetime import datetime
from typing import Any

from common import xcons, xutil, xrequest, xglobal
from common.xutil import log
from services.custom.dianke54suo.diankemodels import parse_report_xml, parse_printer_xml
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

txt_pcb_panel_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
拼板检测NG数量:{pcb_board_robot_ng_number}
拼板复判NG数量:{pcb_board_user_ng_number}
拼板误报数量:{pcb_board_repass_number}
器件总数:{pcb_comp_number}
器件检测NG总数:{pcb_comp_robot_ng_number}
器件复判NG总数:{pcb_comp_user_ng_number}
器件误报总数:{pcb_comp_repass_number}

{BoardData}
"""

txt_board_panel_template = """
========================
拼板号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}{CompData}
"""

txt_comp_panel_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""

xml_pcb_panel_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_board_side="{pcb_board_side}" pcb_test_time="{pcb_test_time}" pcb_cycle_time="{pcb_cycle_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_robot_ng_number="{pcb_board_robot_ng_number}" pcb_board_user_ng_number="{pcb_board_user_ng_number}" pcb_board_repass_number="{pcb_board_repass_number}"
  pcb_comp_number="{pcb_comp_number}" pcb_comp_robot_ng_number="{pcb_comp_robot_ng_number}" pcb_comp_user_ng_number="{pcb_comp_user_ng_number}" pcb_comp_repass_number="{pcb_comp_repass_number}" >
    <BoardList>{BoardData}
    </BoardList>
</Panel>
"""

xml_board_panel_template = """
        <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}">{CompData}
        </Board>"""

xml_comp_panel_template = """
            <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""

txt_board_board_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
整板器件总数:{pcb_comp_number}
拼板序号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}
拼板器件数量:{board_comp_number}
拼板器件检测NG总数:{board_comp_robot_ng_number}
拼板器件复判NG总数:{board_comp_user_ng_number}
拼板器件误报总数:{board_comp_repass_number}

{CompData}
"""

txt_comp_board_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""

csv_board_board_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
整板器件总数,{pcb_comp_number}
拼板序号,{board_no}
拼板条码,{board_sn}
拼板检测结果,{board_robot_result}
拼板复判结果,{board_user_result}
拼板最终结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件检测NG总数,{board_comp_robot_ng_number}
拼板器件复判NG总数,{board_comp_user_ng_number}
拼板器件误报总数,{board_comp_repass_number}

器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_board_template = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

xml_board_board_template = """<?xml version="1.0" encoding="utf-8"?>
<Panel device_name="{device_name}" pcb_sn="{pcb_sn}" pcb_track_line="{pcb_track_line}" pcb_board_side="{pcb_board_side}" pcb_test_time="{pcb_test_time}" pcb_cycle_time="{pcb_cycle_time}" pcb_project_name="{pcb_project_name}" pcb_robot_result="{pcb_robot_result}" pcb_user_result="{pcb_user_result}" pcb_final_result="{pcb_final_result}" 
  pcb_repair_user="{pcb_repair_user}" pcb_board_number="{pcb_board_number}" pcb_comp_number="{pcb_comp_number}">
    <Board board_sn="{board_sn}" board_no="{board_no}" board_robot_result="{board_robot_result}" board_user_result="{board_user_result}" board_final_result="{board_final_result}"
    board_comp_number="{board_comp_number}" board_comp_robot_ng_number="{board_comp_robot_ng_number}" board_comp_user_ng_number="{board_comp_user_ng_number}" board_comp_repass_number="{board_comp_repass_number}" >{CompData}
    </Board>
</Panel>
"""

xml_comp_board_template = """
        <Comp comp_designator="{comp_designator}" comp_part="{comp_part}" comp_package="{comp_package}" comp_type="{comp_type}" comp_robot_code="{comp_robot_code}" comp_robot_result="{comp_robot_result}" comp_user_code="{comp_user_code}" comp_user_result="{comp_user_result}" comp_image="{comp_image}"/>"""

# 印刷机模版
marker_template = """
            <Fiducial Name="{comp_des}">
                <X Origin="{cx}">{offset_x}</X>
                <Y Origin="{cy}">{offset_y}</Y>
            </Fiducial>"""

to_printer_template = """<?xml version="1.0" ?>
<Data>
    <Message version="1.0">
        <Date_and_Time>{generate_time}</Date_and_Time>
    </Message>
    <Equipment reference="TOP_LEFT" version="Vision 1.0.0">
        <Name>{machine_name}</Name>
    </Equipment>
    <Process>
        <Product_ID>{product_id}</Product_ID>
        <Panel_ID>{barcode}</Panel_ID>
        <Panel_Status>{pcb_result}</Panel_Status>
        <Inspected_Date_and_Time>{test_time}</Inspected_Date_and_Time>
        <Units>
            <Distance>mm</Distance>
            <Angle>deg</Angle>
            <Time>seconds</Time>
        </Units>
        <Offset_Correction>
            <Y>{board_y}</Y>
            <X>{board_x}</X>
            <Theta CoR_X="{b_cx}" CoR_Y="{b_cy}">{theta}</Theta>
        </Offset_Correction>
        <Fiducials Count="{maker_count}">{maker_data}
        </Fiducials>
        <Print_Direction>{print_direction}</Print_Direction>
        <Command>{command}</Command>
        <Warning TotalNum="{comp_num}" DefectNum="0">
            <Volume Num="0">
                <High>0</High>
                <Low>0</Low>
            </Volume>
            <Height Num="0">
                <High>0</High>
                <Low>0</Low>
            </Height>
            <Area Num="0">
                <High>0</High>
                <Low>0</Low>
            </Area>
            <Bridge>0</Bridge>
            <NoPaste>0</NoPaste>
        </Warning>
        <Alarm TotalNum="{comp_num}" DefectNum="{comp_user_ng_count}">
            <Volume Num="{v_total}">
                <High>{v_high}</High>
                <Low>{v_low}</Low>
            </Volume>
            <Height Num="{h_total}">
                <High>{h_high}</High>
                <Low>{h_low}</Low>
            </Height>
            <Area Num="{a_total}">
                <High>{a_high}</High>
                <Low>{a_low}</Low>
            </Area>
            <Bridge>{bridge}</Bridge>
            <NoPaste>{no_paste}</NoPaste>
        </Alarm>
    </Process>
</Data>"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["电科54所", "dianke54suo"],
        "version": "release v1.0.0.4",
        "device": "AIS43X,AIS63X",
        "feature": ["条码校验", "上传数据", "设备状态"],
        "author": "wxc",
        "release": """
date: 2025-05-06 18:37  ATAOI_2019-39307：条码校验,上传数据,设备状态,开工完工接口
date: 2025-06-13 09:15  设备状态接口返回判断修改，调用完工接口逻辑修改
date: 2025-06-14 12:07  设备状态发送没有默认return
date: 2025-07-18 17:49  开工接口放条码校验里
"""}

    other_form = {
        "check_sn_url": {
            "ui_name": "条码校验接口",
            "value": "http://ip:port/api/ExternalInterface/PostSerialNumberProcessInfo"
        },
        "process_url": {
            "ui_name": "开工完工接口",
            "value": "http://ip:port/api/ExternalInterface/PostSerialNumberProcess"
        },
        "status_url": {
            "ui_name": "设备状态变化上传接口",
            "value": "http://ip:port/api/DataAcquisition/PostStationStatus"
        },
    }

    combo = {
        # "is_save_local": {
        #     "ui_name": "保存数据到本地",
        #     "item": [
        #         "是",
        #         "否",
        #     ],
        #     "value": "是"
        # },
        # "is_save_comp_image": {
        #     "ui_name": "保存NG器件图到本地",
        #     "item": [
        #         "是",
        #         "否",
        #     ],
        #     "value": "否"
        # },
        "file_type": {
            "ui_name": "文件类型",
            "item": [
                "csv",
                "txt",
                "json",
                "xml",
            ],
            "value": "csv"
        },
        "save_type": {
            "ui_name": "文件生成方式",
            "item": [
                "整板生成",
                "拼板生成",
            ],
            "value": "整板生成"
        },
        "panel_filename": {
            "ui_name": "整板文件名格式",
            "item": [
                "时间_整板条码",
                "整板条码_时间",
                "整板条码",
                "时间",
                "---",
            ],
            "value": "时间_整板条码"
        },
        "board_filename": {
            "ui_name": "拼板文件名格式",
            "item": [
                '时间_拼板条码',
                '拼板条码_时间',
                '拼板条码',
                '时间_拼板条码_拼板序号',
                '时间_拼板序号',
                '拼板序号_时间',
                "---",
            ],
            "value": "时间_拼板序号"
        },
        "save_path_type": {
            "ui_name": "保存数据路径格式",
            "item": [
                '数据路径',
                '数据路径/日期(yyyymmdd)',
                '数据路径/设备名称',
                '数据路径/设备名称/日期(yyyymmdd)',
                '数据路径/日期(yyyymmdd)/设备名称',
            ],
            "value": "数据路径/设备名称/日期(yyyymmdd)"
        },
        # "save_image_path_fmt": {
        #     "ui_name": "保存NG器件图路径格式",
        #     "item": [
        #         '图片路径/条码_拼板号_位号_NG描述.png',
        #         '图片路径/日期(yyyymmdd)/条码_拼板号_位号_NG描述.png',
        #         '图片路径/设备名称/条码_拼板号_位号_NG描述.png',
        #         '图片路径/设备名称/日期(yyyymmdd)/条码_拼板号_位号_NG描述.png',
        #         '图片路径/日期(yyyymmdd)/设备名称/条码_拼板号_位号_NG描述.png',
        #     ],
        #     "value": "图片路径/日期(yyyymmdd)/设备名称/条码_拼板号_位号_NG描述.png"
        # },
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "全部"
        },
        "newline_type": {
            "ui_name": "换行符格式",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        },
        "is_check_sn": {
            "ui_name": "校验印刷机条码",
            "item": [
                "Yes",
                "No",
            ],
            "value": "No"
        },
        "is_printer": {
            "ui_name": "是否生成印刷机格式",
            "item": [
                "是",
                "否",
            ],
            "value": "否"
        },

    }

    path = {
        "save_path": {
            "ui_name": "数据路径",
            "value": ""
        },
        "from_path": {
            "ui_name": "交互文件夹（PrinterToSPI）",
            "value": ""
        },
        "to_path": {
            "ui_name": "交互文件夹（SPIToPrinter）",
            "value": ""
        },
        # "save_image_path": {
        #     "ui_name": "图片路径",
        #     "value": ""
        # }
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""

        },
        "station_id": {
            "ui_name": "工站编号",
            "value": ""
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        check_sn_url = other_vo.get_value_by_cons_key("check_sn_url", not_null=True)
        station_id = other_vo.get_value_by_cons_key("station_id", not_null=True)
        process_url = other_vo.get_value_by_cons_key("process_url", not_null=True)
        barcode_map = other_vo.get_barcode_map()
        pcb_sn = barcode_map.get("-1")

        for sn in other_vo.list_sn():
            param = {
                "OperateMode": "4",
                "PostDataList":
                    [{
                        "SerialNumber": sn
                    }]
            }
            ret = xrequest.RequestUtil.post_json(check_sn_url, param)
            if str(ret.get("Code")) != "0":
                return self.x_response("false", f"条码校验接口异常，error：{ret.get('RemindInformation')}")
        # 调开工接口
        param = {
            "OperateMode": "1",
            "PostDataList":
                [{
                    "SerialNumber": pcb_sn,
                    "StationId": station_id
                }]
        }
        ret = xrequest.RequestUtil.post_json(process_url, param)
        if str(ret.get("FlowCode")) != "0":
            return self.x_response("false", f"开工接口异常，error：{ret.get('RemindInformation')}")

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        status_url = other_vo.get_value_by_cons_key("status_url")
        station_id = other_vo.get_value_by_cons_key("station_id", not_null=True)

        device_code = other_vo.get_status_code_v3()
        last_status = xglobal.global_data.get("last_status", "")

        running_status = ["1001", "1002", "1003", "1004", "1005", "0001"]
        # 当前设备状态如果与上次设备状态不一致则需要上传
        if device_code in running_status:
            status_param = {
                "StationID": station_id,
                "StationName": "",
                "Status": "1",
                "PreStatus": last_status,
                "Comment": ""
            }
            ret = xrequest.RequestUtil.post_json(status_url, status_param)
            xglobal.global_data["last_status"] = "1"
            if str(ret.get("Code")) != "0":
                return self.x_response("false", f"设备状态上传接口异常，error：{ret.get('RemindInformation')}")
        # 设备空闲
        elif device_code in ["3001", "0001"]:
            status_param = {
                "StationID": station_id,
                "StationName": "",
                "Status": "2",
                "PreStatus": last_status,
                "Comment": ""
            }
            ret = xrequest.RequestUtil.post_json(status_url, status_param)
            xglobal.global_data["last_status"] = "2"
            if str(ret.get("Code")) != "0":
                return self.x_response("false", f"设备状态上传接口异常，error：{ret.get('RemindInformation')}")
        # 设备故障
        elif device_code not in running_status + ["3001", "0001"]:
            status_param = {
                "StationID": station_id,
                "StationName": "",
                "Status": "3",
                "PreStatus": last_status,
                "Comment": ""
            }
            ret = xrequest.RequestUtil.post_json(status_url, status_param)
            xglobal.global_data["last_status"] = "3"
            if str(ret.get("Code")) != "0":
                return self.x_response("false", f"设备状态上传接口异常，error：{ret.get('RemindInformation')}")
        else:
            return self.x_response("false", f"未知设备状态！！")
        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        is_printer = data_vo.get_value_by_cons_key("is_printer")
        # 1. 生成标准数据
        x_response1 = self._send_standard_data(data_vo)
        if is_printer == "是":
            # 2. 生成印刷机需要的csv数据
            x_response2 = self._send_printer_data(data_vo)

            if not x_response2.get("result"):
                return x_response2

        if not x_response1.get("result"):
            return x_response1

        return self.x_response()

    def _send_standard_data(self, data_vo: DataVo):
        """
        标准版数据
        """
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        file_type = data_vo.get_value_by_cons_key("file_type")
        save_type = data_vo.get_value_by_cons_key("save_type")
        panel_filename = data_vo.get_value_by_cons_key("panel_filename")
        board_filename = data_vo.get_value_by_cons_key("board_filename")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path_type = data_vo.get_value_by_cons_key("save_path_type")
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")
        newline_type = data_vo.get_value_by_cons_key("newline_type")
        station_id = data_vo.get_value_by_cons_key("station_id")
        process_url = data_vo.get_value_by_cons_key("process_url")

        if save_type == "整板生成" and panel_filename == "---":
            return self.x_response("false", "请选择整板文件名格式！")

        if save_type == "拼板生成" and board_filename == "---":
            return self.x_response("false", "请选择拼板文件名格式！")

        self.log.info(f"文件生成格式：{file_type}")

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode
        time_date = time_str[:8]
        if save_path_type == "数据路径":
            dst_path = f"{save_path}"
        elif save_path_type == "数据路径/日期(yyyymmdd)":
            dst_path = f"{save_path}/{time_date}"
        elif save_path_type == "数据路径/设备名称":
            dst_path = f"{save_path}/{device_name}"
        elif save_path_type == "数据路径/设备名称/日期(yyyymmdd)":
            dst_path = f"{save_path}/{device_name}/{time_date}"
        elif save_path_type == "数据路径/日期(yyyymmdd)/设备名称":
            dst_path = f"{save_path}/{time_date}/{device_name}"
        else:
            return self.x_response("false", f"不支持的数据路径格式：{save_path_type}")

        xutil.FileUtil.ensure_dir_exist(dst_path)

        if save_type == "整板生成":
            board_data = []
            pcb_board_user_ng_number = 0
            pcb_board_robot_ng_number = 0
            pcb_comp_user_ng_number = 0
            pcb_comp_robot_ng_number = 0
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                barcode = board_entity.barcode
                board_no = board_entity.board_no

                if board_entity.is_repair_ng():
                    pcb_board_user_ng_number += 1

                if board_entity.is_robot_ng():
                    pcb_board_robot_ng_number += 1

                pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
                pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

                comp_data = []
                for comp_entity in board_entity.yield_comp_entity():
                    print(comp_entity)

                    if comp_data_output == "全部" or \
                            (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                        comp_data.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

                board_data.append({
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_data": comp_data,
                })
            pcb_data = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
                "pcb_board_user_ng_number": pcb_board_user_ng_number,
                "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
                "pcb_comp_number": pcb_entity.comp_count,
                "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
                "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
                "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
                "board_data": board_data
            }

            if panel_filename == "时间_整板条码":
                dst_filename = f"{time_str}_{pcb_sn}"
            elif panel_filename == "整板条码_时间":
                dst_filename = f"{pcb_sn}_{time_str}"
            elif panel_filename == "整板条码":
                dst_filename = f"{pcb_sn}"
            elif panel_filename == "时间":
                dst_filename = f"{time_str}"
            else:
                return self.x_response("false", f"不支持的整板文件名格式：{panel_filename}")

            if file_type == "csv":
                dst_filename = f"{dst_filename}.csv"
                dst_filepath = f"{dst_path}/{dst_filename}"

                comp_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    for _comp_data in _board_data.get("comp_data", {}):
                        _comp_data.update(_board_data)
                        comp_data_str += csv_comp_panel_template.format(**_comp_data)

                pcb_data["CompData"] = comp_data_str
                pcb_content = csv_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

            elif file_type == "txt":
                dst_filename = f"{dst_filename}.txt"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += txt_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += txt_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = txt_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

            elif file_type == "json":
                dst_filename = f"{dst_filename}.json"
                dst_filepath = f"{dst_path}/{dst_filename}"

                pcb_data_str = json.dumps(pcb_data, indent=4, ensure_ascii=False)

                if newline_type == 'window':
                    pcb_data_str = pcb_data_str.replace('\n', '\r\n')

                xutil.FileUtil.write_content_to_file(dst_filepath, pcb_data_str)

            elif file_type == "xml":
                dst_filename = f"{dst_filename}.xml"
                dst_filepath = f"{dst_path}/{dst_filename}"

                board_data_str = ""
                for _board_data in pcb_data.get("board_data", []):
                    comp_data_str = ""

                    for _comp_data in _board_data.get("comp_data", {}):
                        comp_data_str += xml_comp_panel_template.format(**_comp_data)

                    _board_data["CompData"] = comp_data_str

                    board_data_str += xml_board_panel_template.format(**_board_data)

                pcb_data["BoardData"] = board_data_str
                pcb_content = xml_pcb_panel_template.format(**pcb_data)

                if newline_type == 'window':
                    pcb_content = pcb_content.replace('\n', '\r\n')

                xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

            else:
                return self.x_response("false", f"不支持的文件类型：{file_type}")

        elif save_type == "拼板生成":
            for board_entity in pcb_entity.yield_board_entity():
                board_sn = board_entity.barcode
                board_no = board_entity.board_no

                comp_data_list = []
                for comp_entity in board_entity.yield_comp_entity():

                    if comp_data_output == "全部" or \
                            (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                            (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                        comp_data_list.append({
                            "comp_designator": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "comp_package": comp_entity.package,
                            "comp_type": comp_entity.type,
                            "comp_robot_code": comp_entity.robot_ng_code,
                            "comp_robot_result": comp_entity.robot_ng_str,
                            "comp_user_code": comp_entity.repair_ng_code,
                            "comp_user_result": comp_entity.repair_ng_str,
                            "comp_image": comp_entity.image_path,
                        })

                board_data_fmt = {
                    "device_name": device_name,
                    "pcb_sn": pcb_sn,
                    "pcb_track_line": pcb_entity.track_index,
                    "pcb_board_side": pcb_entity.board_side,
                    "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                    "pcb_cycle_time": pcb_entity.get_cycle_time(),
                    "pcb_project_name": pcb_entity.project_name,
                    "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                    "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                    "pcb_final_result": pcb_entity.get_final_result(),
                    "pcb_repair_user": pcb_entity.repair_user,
                    "pcb_board_number": pcb_entity.board_count,
                    "pcb_comp_number": pcb_entity.comp_count,

                    "board_sn": board_sn,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),

                    "board_comp_number": board_entity.comp_total_number,
                    "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                    "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                    "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                }

                if board_filename == "时间_拼板条码":
                    filename = f"{time_str}_{board_sn}"
                elif board_filename == "拼板条码_时间":
                    filename = f"{board_sn}_{time_str}"
                elif board_filename == "拼板条码":
                    filename = f"{board_sn}"
                elif board_filename == "时间_拼板条码_拼板序号":
                    filename = f"{time_str}_{board_sn}_{board_no}"
                elif board_filename == "时间_拼板序号":
                    filename = f"{time_str}_{board_no}"
                elif board_filename == "拼板序号_时间":
                    filename = f"{board_no}_{time_str}"
                else:
                    return self.x_response("false", f"不支持的拼板文件名格式！")

                comp_data = ""
                if file_type == "txt":
                    filename = f"{filename}.txt"
                    for item in comp_data_list:
                        comp_data += txt_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = txt_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                elif file_type == "xml":
                    filename = f"{filename}.xml"
                    for item in comp_data_list:
                        comp_data += xml_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = xml_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                elif file_type == "json":
                    filename = f"{filename}.json"
                    board_data_fmt["comp_data"] = comp_data_list

                    board_data = json.dumps(board_data_fmt, indent=4, ensure_ascii=False)
                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                elif file_type == "csv":
                    filename = f"{filename}.csv"
                    for item in comp_data_list:
                        comp_data += csv_comp_board_template.format(**item)
                    board_data_fmt["CompData"] = comp_data
                    board_data = csv_board_board_template.format(**board_data_fmt)

                    if newline_type == 'window':
                        board_data = board_data.replace('\n', '\r\n')

                    xutil.FileUtil.write_content_to_file(f"{dst_path}/{filename}", board_data)

                else:
                    return self.x_response("false", "不支持的文件类型！")

        else:
            return self.x_response("false", f"不支持的文件生成方式！")

        param = {
            "OperateMode": "2",
            "PostDataList":
                [{
                    "SerialNumber": pcb_sn,
                    "StationId": station_id
                }]
        }
        ret = xrequest.RequestUtil.post_json(process_url, param)
        if str(ret.get("FlowCode")) != "0":
            return self.x_response("false", f"完工接口异常，error：{ret.get('RemindInformation')}")
        return self.x_response()

    def _send_printer_data(self, data_vo: DataVo):
        from_path = data_vo.get_value_by_cons_key("from_path")
        to_path = data_vo.get_value_by_cons_key("to_path")
        device_name = data_vo.get_value_by_cons_key("device_name")
        is_check_sn = data_vo.get_value_by_cons_key("is_check_sn")
        try:
            now = datetime.now()
            file_now = now.strftime(xcons.FMT_TIME_FILE)
            date_now = now.strftime(xcons.FMT_TIME_DEFAULT6)
            # alg_type = mes_data_obj.get_cons(ALGTYPE)
            pcb_entity = data_vo.pcb_entity
            test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT3)

            pcb_sn = pcb_entity.pcb_barcode

            # 获取器件的复判结果  ps:主要为了修复子器件的复判结果
            comp_result_map = {}
            for board_entity in pcb_entity.yield_board_entity():
                for comp_entity in board_entity.yield_comp_entity():
                    comp_result_map[comp_entity.comp_id] = comp_entity.repair_result

            barcode_list = []

            solder_param_data, (_, _), (board_x, board_y, b_cx, b_cy, theta) = parse_report_xml(
                pcb_entity.get_pcb_t_report_xml()
            )
            # log.logger.info(f"{b_cx=}, {b_cy=}")

            self.log.info(f"----> 整板信息：{pcb_entity}  拼板数：{len(pcb_entity.list_board_ids())}")

            comp_num = 0
            comp_user_ng_count = 0

            marker_data = ""
            marker_count = 0

            v_total = 0
            v_high = 0
            v_low = 0
            h_total = 0
            h_high = 0
            h_low = 0
            a_total = 0
            a_high = 0
            a_low = 0
            bridge = 0
            no_paste = 0

            # xy_origin = []
            # xy_offset = []
            #
            # gx_list = []
            # gy_list = []

            # area_list = []  # 器件大小，需要按照比率剔除小器件

            command_count = 0
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                if board_entity.barcode:
                    barcode_list.append(board_entity.barcode)

                for comp_entity in board_entity.yield_comp_entity():
                    comp_uuid = comp_entity.comp_id
                    solder_param = solder_param_data.get(comp_uuid)
                    is_main_marker = solder_param.get("is_main_marker")
                    has_inspect_detect = solder_param.get("has_inspect_detect")

                    if has_inspect_detect:
                        # log.logger.debug(f"---------> {solder_param}")
                        # 坐标原始值，和偏移值
                        # xy_origin.append([solder_param.get("cx"), solder_param.get("cy")])
                        # xy_offset.append([solder_param.get("solder_offset_x"), solder_param.get("solder_offset_y")])
                        #
                        # gx_list.append(solder_param.get("gx"))
                        # gy_list.append(solder_param.get("gy"))

                        # area_list.append(solder_param.get("area"))

                        comp_num += 1

                        comp_uuid_parent = solder_param.get("comp_uuid_parent")

                        comp_result = comp_result_map.get(comp_uuid_parent)

                        # if comp["CompNgType"] != "0":
                        if not comp_result:
                            comp_user_ng_count += 1

                            is_bridge = solder_param.get("is_bridge")
                            is_no_paste = solder_param.get("is_no_paste")

                            if is_bridge or is_no_paste:
                                command_count += 1

                            if is_bridge:
                                bridge += 1

                            if is_no_paste:
                                no_paste += 1

                            # 统计细项
                            for alg in comp_entity.yield_alg_entity():
                                test_name = alg.test_name
                                alg_max = alg.max_threshold
                                alg_min = alg.min_threshold
                                alg_test = alg.test_val

                                if "%" in alg_max:
                                    alg_max = alg_max.replace("%", "")

                                if "%" in alg_min:
                                    alg_min = alg_min.replace("%", "")

                                if "%" in alg_test:
                                    alg_test = alg_test.replace("%", "")

                                t_max = float(alg_max)
                                t_min = float(alg_min)
                                t_val = float(alg_test)
                                t_result = alg.result

                                if test_name == "Area":
                                    if t_result != "0":
                                        a_total += 1
                                        if t_val > t_max:
                                            a_high += 1
                                        if t_val < t_min:
                                            a_low += 1

                                if test_name == "Volume":
                                    if t_result != "0":
                                        v_total += 1
                                        if t_val > t_max:
                                            v_high += 1
                                        if t_val < t_min:
                                            v_low += 1

                                if test_name == "Height":
                                    if t_result != "0":
                                        h_total += 1
                                        if t_val > t_max:
                                            h_high += 1
                                        if t_val < t_min:
                                            h_low += 1

                    else:
                        self.log.warning(f"comp uuid: {comp_uuid} 不检测！")

                    if is_main_marker:
                        marker_count += 1
                        marker_data += marker_template.format(**{
                            "comp_des": comp_entity.designator,
                            "cx": solder_param.get("cx"),
                            "cy": solder_param.get("cy"),
                            "offset_x": solder_param.get("offset_x"),
                            "offset_y": solder_param.get("offset_y"),
                        })

            # log.logger.info("---------xy origin and offset start--------------")
            # log.logger.info(f"{xy_origin}")
            # log.logger.info(f"{xy_offset}")
            # log.logger.info("---------xy origin and offset end--------------")

            # 1. 读取印刷机文档
            # from_path = f"{save_path}/from_printer"
            # to_path = f"{save_path}/to_printer"

            all_file = os.listdir(from_path)
            self.log.debug(f"all file: {all_file}")

            all_file_int = [int(file.split(".")[0]) for file in all_file]

            if not all_file_int:
                return self.x_response("false", f"未找到印刷机生成的xml文件！")

            min_file = min(all_file_int)
            self.log.debug(f"最旧的文件：{min_file}")

            from_file = f"{from_path}/{min_file}.xml"
            product_id, panel_id, print_direction = parse_printer_xml(from_file)
            self.log.debug(f"{product_id=}, {panel_id=}, {print_direction=}")

            # 2.1 条码
            if panel_id == pcb_sn or panel_id in barcode_list:
                # 条码匹配上了
                barcode = panel_id
                is_error = False
            else:
                # 条码未匹配上
                is_error = True
                if pcb_sn:
                    barcode = pcb_sn
                elif barcode_list:
                    barcode = barcode_list[0]
                else:
                    barcode = ""

            self.log.info(f"barcode: {barcode}, not match: {is_error}")

            # 2.2 command， 连锡or少锡超过一定的阈值，则需要清洗钢网
            rows = xutil.FileUtil.read_file("/home/<USER>/.config/Leichen/Inspector.ini").split("\n")
            command_threshold = ""
            is_clean = False

            for row in rows:
                # if row.startswith("CleanPrinterMoreThanPercent"):
                if row.startswith("CleanPrinterMoreThanNumber"):  # 清洗比例改成个数
                    command_threshold = float(row.split("=")[1])

                elif row.startswith("IsCleanPrinter"):
                    is_clean_str = row.split("=")[1]
                    if is_clean_str == "true":
                        is_clean = True

            self.log.debug(f"{command_threshold=}, {is_clean=}")

            self.log.debug(f"阈值：{command_threshold}   command_count: {command_count} comp_num: {comp_num}")
            # if is_clean and (command_count / comp_num * 100) >= command_threshold:
            if is_clean and command_count >= command_threshold:  # 清洗比例改成个数
                is_command = 1
            else:
                is_command = 0

            to_content = to_printer_template.format(**{
                "generate_time": date_now,
                "test_time": test_time,
                "machine_name": device_name,
                "product_id": product_id,
                "barcode": barcode,
                "pcb_result": pcb_entity.get_final_result("OK", "NG"),
                "print_direction": print_direction,

                "maker_count": marker_count,
                "maker_data": marker_data,
                "command": is_command,
                "comp_num": comp_num,
                "comp_user_ng_count": comp_user_ng_count,
                "v_total": v_total,
                "v_high": v_high,
                "v_low": v_low,
                "h_total": h_total,
                "h_high": h_high,
                "h_low": h_low,
                "a_total": a_total,
                "a_high": a_high,
                "a_low": a_low,
                "bridge": bridge,
                "no_paste": no_paste,
                "b_cx": b_cx,
                "b_cy": b_cy,

                "board_x": board_x,
                "board_y": board_y,
                "theta": theta
            })

            # 保存文档到指定目录给印刷机
            to_file = f"{to_path}/{file_now}.xml"
            xutil.FileUtil.write_content_to_file(to_file, to_content)

            # 删除来自印刷机的文档
            os.remove(from_file)

            if is_check_sn == "Yes" and is_error:
                return self.x_response("false", f"条码未匹配上，请检查印刷机与SPI的条码是否匹配！")

            return self.x_response()

        except Exception as e:
            self.log.warning(f"保存测试数据error1001: {traceback.format_exc()}")
            return self.x_response("false", f"其他异常，error: {e}")
