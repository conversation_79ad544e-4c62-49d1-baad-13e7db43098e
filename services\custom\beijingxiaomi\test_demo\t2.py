# # !/usr/bin/env python
# # -*-coding:utf-8 -*-
#
# """
# # File       : t2.py
# # Time       ：2025/3/24 下午3:20
# # Author     ：sch
# # version    ：python 3.8
# # Description：
# """
# import requests
# import hashlib
# import base64
# import json
#
#
# def generate_sign(app_id, app_key, json_body):
#     """生成签名"""
#
#     sign_str = f"{app_id}{json_body}{app_key}"
#
#     sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
#     return sign
#
#
# def xiaomi_request(app_id, app_key, url, json_body):
#     """
#     发送小米请求
#     """
#     json_body_str = json.dumps(json_body, indent=4, ensure_ascii=False)
#     sign = generate_sign(app_id, app_key, json_body_str)
#     data = {
#         "header": {
#             "appid": app_id,
#             "sign": sign,
#             "method": "UnitConfirmDataSetIn"
#         },
#         "body": json_body_str
#     }
#     # 将数据转换为字符串并进行 Base64 编码
#     data_str = json.dumps(data)
#     data_base64 = base64.b64encode(data_str.encode('utf-8')).decode('utf-8')
#
#     # 构建表单数据
#     form_data = {
#         "data": data_base64,
#         "key": json.dumps(json_body)
#     }
#
#     # 发送 POST 请求
#     response = requests.post(url, data=form_data)
#
#     # 打印响应
#     # print("Status Code:", response.status_code)
#     # print("Response Body:", response.text)
#
#     return response.json()
#
#
# if __name__ == "__main__":
#     app_id1 = "MIUAT"
#     app_key1 = "6B3D57E2-F062-471F-932B-76C8A79E0D66"
#     url1 = "http://im.pre.mi.com/mes/x5/pass/station/v2"
#
#     # 请求体数据
#     json_body1 = {
#         "machineId": "A030007000102",
#         "stationId": "G05-AFCCBFP-11",
#         "clientMac": "00155DD01E93",
#         "clientTime": "2024-08-03 09:12:32.176",
#         "unitSn": "50303/N3X900237",
#         "userId": "AKPKTSQAS46NKE4NHY",
#         "factoryId": 1
#     }
#
#     ret = xiaomi_request(app_id1, app_key1, url1, json_body1)
#     print(ret)
