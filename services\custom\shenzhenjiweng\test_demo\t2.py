# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t2.py
# Time       ：2025/4/28 下午5:29
# Author     ：sch
# version    ：python 3.8
# Description：测试字节码
"""
from common import xutil
from services.custom.shenzhenjiweng.jw_module import compress_image_list_to_hex


def f1(num: int) -> bytes:
    ret = bytes(chr(num), 'utf-8')
    print(ret, type(ret))
    return ret


if __name__ == '__main__':
    # ret1 = 'A1' + chr(13) + chr(10) + 'png\tfile\nA2\vpng\tfile'
    #
    # with open("./test.txt", 'w') as f:
    #     f.write(ret1)
    #
    # print("done")
    # print(ret1)

    # text = "Hello\x03World\x03Python"
    # print(text)
    # print(type(text))
    #
    # text_b = bytes(text, 'utf-8')
    # print(text_b)
    #
    # print(chr(3), type(chr(3)))

    # c3 = chr(3)
    # print(c3, type(c3))
    #
    # a = c3 + 'hello world' + c3
    #
    # a = bytes(a, 'utf-8')
    # print(a, type(a))

    # print(bytes(chr(3), 'utf-8'))
    # print(bytes(chr(10), 'utf-8'))
    # print(bytes(chr(13), 'utf-8'))
    # print(bytes(chr(65), 'utf-8'))
    # print(bytes(chr(228), 'utf-8'))

    print(xutil.OtherUtil.convert_int_to_utf8_bytes(13))  # \r
    print(xutil.OtherUtil.convert_int_to_utf8_bytes(10))  # \n
    print(xutil.OtherUtil.convert_int_to_utf8_bytes(9))  # 缩进 \t
    print(xutil.OtherUtil.convert_int_to_utf8_bytes(11))  # \v

    img_list = ['/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172401154_1_NG/thumbnail/0/thumb.jpg',
         '/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172401154_1_NG/images/ng/SolderJoint/0/COMP1004_1004.png',
         '/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172401154_1_NG/images/ng/Chip_R/0/COMP1387_1387.png',
         '/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172401154_1_NG/images/ng/Chip_R/0/COMP1399_1399.png',
         '/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172401154_1_NG/images/ng/Chip_R/0/COMP1379_1379.png']

    ret = compress_image_list_to_hex(img_list)
    print(ret)
