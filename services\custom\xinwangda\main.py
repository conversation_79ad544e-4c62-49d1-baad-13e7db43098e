# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/29 上午9:25
# Author     ：sch
# version    ：python 3.8
# Description：欣旺达21栋
"""
from typing import Any

from common import xrequest, xutil
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import BaseEngine

login_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <CheckUserDo xmlns="WWW.SUNWODA.COM">
            <M_USERNO>{user_no}</M_USERNO>
            <M_PASSWORD>{password}</M_PASSWORD>
            <M_MACHINENO>{machine_no}</M_MACHINENO>
        </CheckUserDo>
    </soap:Body>
</soap:Envelope>"""

check_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTest xmlns="WWW.SUNWODA.COM">
            <M_SN>{sn}</M_SN>
            <M_MACHINCENO>{machine_no}</M_MACHINCENO>
            <M_EMP>{emp}</M_EMP>
        </GroupTest>
    </soap:Body>
</soap:Envelope>"""

test_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <WIPTEST xmlns="WWW.SUNWODA.COM">
            <M_SN>{sn}</M_SN>
            <M_RESULT>{robot_result}</M_RESULT>
            <M_USERNO>{user_no}</M_USERNO>
            <M_MACHINENO>{machine_no}</M_MACHINENO>
            <M_ERROR>无</M_ERROR>
            <M_ITEMVALUE>0</M_ITEMVALUE>
        </WIPTEST>
    </soap:Body>
</soap:Envelope>"""


class Engine(BaseEngine):
    version = {
        "title": "xinwangda-21 release v1.0.0.7",
        "device": "40x",
        "feature": ["登录", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-29 09:31  init
date: 2023-06-02 17:08  接口参数修改
date: 2023-06-03 13:19  上传方式可配置
date: 2023-06-03 16:57  repair ng don't send mes
date: 2023-06-04 09:41  修改成上传复判结果
date: 2023-12-28 09:32  坏板过滤
date: 2024-01-06 12:07  NG时默认上传PASS结果给mes！
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/mesinterface.asmx"
        },
        "user_no": {
            "ui_name": "MES系统账号",
            "value": "admin"
        },
        "password": {
            "ui_name": "密码",
            "value": ""
        },
        "machine_no": {
            "ui_name": "MES系统设备编码",
            "value": ""
        }
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": ""
        }
    }

    button = {
        "login_btn": {
            "ui_name": "登录"
        }
    }

    combo = {
        "upload_type": {
            "ui_name": "上传方式",
            "item": [
                "整板",
                "拼板"
            ],
            "value": "整板"
        }
    }

    @staticmethod
    def parse_ret_str(ret_str: str) -> str:
        """
        解析接口响应
        :return:
        """
        root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        result_str = root[0][0][0].text
        return result_str

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        sn_list = other_vo.list_sn()

        machine_no = other_vo.get_value_by_cons_key("machine_no")
        user_no = other_vo.get_value_by_cons_key("user_no")
        api_url = other_vo.get_value_by_cons_key("api_url")

        error_msg = ""
        for sn in sn_list:

            if sn:
                check_param = check_template.format(**{
                    "sn": sn,
                    "emp": user_no,
                    "machine_no": machine_no,
                })
                ret_str = xrequest.RequestUtil.post_soap(api_url, check_param, soap_action="WWW.SUNWODA.COM/GroupTest")

                result = self.parse_ret_str(ret_str)

                if not result.startswith("TRUE"):
                    error_msg += f"{result}\n"

        if error_msg:
            return self.x_response("false", f"mes接口异常，条码校验失败，{error_msg}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url = btn_vo.get_value_by_cons_key("api_url")
        user_no = btn_vo.get_value_by_cons_key("user_no")
        password = btn_vo.get_value_by_cons_key("password")
        machine_no = btn_vo.get_value_by_cons_key("machine_no")

        if btn_vo.get_btn_key() == "login_btn":

            login_param = login_template.format(**{
                "user_no": user_no,
                "password": password,
                "machine_no": machine_no
            })

            ret_str = xrequest.RequestUtil.post_soap(api_url, login_param, "WWW.SUNWODA.COM/CheckUserDo")
            result = self.parse_ret_str(ret_str)
            if not result.startswith("TRUE"):
                return self.x_response("false", f"mes接口异常，登录失败，{result}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        user_no = data_vo.get_value_by_cons_key("user_no")
        machine_no = data_vo.get_value_by_cons_key("machine_no")

        save_path = data_vo.get_value_by_cons_key("save_path")
        upload_type = data_vo.get_value_by_cons_key("upload_type")

        pcb_entity = data_vo.pcb_entity

        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        if upload_type == "整板":
            self.log.info(f"整板上传中...")
        else:
            self.log.info(f"拼板上传中...")

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not barcode:
                barcode = xutil.DateUtil.get_datetime_now()

            test_param = test_template.format(**{
                "sn": barcode,
                "robot_result": board_entity.get_repair_result("PASS", "FAIL"),
                "user_no": user_no,
                "machine_no": machine_no
            })

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng() and comp_entity.image_path:
                    # 保存检测NG图
                    comp_tag = comp_entity.designator

                    dst_file = f"{save_path}/{barcode}_{comp_tag}.png"
                    xutil.FileUtil.copy_file(comp_entity.image_path, dst_file)

            if upload_type != "整板":
                if board_entity.repair_result:
                    # 检测OK
                    ret_str = xrequest.RequestUtil.post_soap(api_url, test_param, soap_action="WWW.SUNWODA.COM/WIPTEST")

                    result = self.parse_ret_str(ret_str)

                    if not result.startswith("TRUE"):
                        error_msg += f"{result}\n"
                else:
                    self.log.warning(f"拼板机器检测结果为NG时，不需要调用产品过站WipTest接口")

        if upload_type == "整板":
            if pcb_entity.pcb_repair_result:
                test_param1 = test_template.format(**{
                    "sn": pcb_sn,
                    "robot_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                    "user_no": user_no,
                    "machine_no": machine_no
                })

                # 检测OK
                ret_str = xrequest.RequestUtil.post_soap(api_url, test_param1, soap_action="WWW.SUNWODA.COM/WIPTEST")

                result = self.parse_ret_str(ret_str)
                if not result.startswith("TRUE"):
                    return self.x_response("false", f"mes接口异常，产品过站失败，{result}")

            else:
                self.log.warning(f"整板机器检测结果为NG时，默认上传PASS结果给mes！")

                test_param1 = test_template.format(**{
                    "sn": pcb_sn,
                    "robot_result": "PASS",
                    "user_no": user_no,
                    "machine_no": machine_no
                })

                # 检测OK
                ret_str = xrequest.RequestUtil.post_soap(api_url, test_param1, soap_action="WWW.SUNWODA.COM/WIPTEST")

                result = self.parse_ret_str(ret_str)
                if not result.startswith("TRUE"):
                    return self.x_response("false", f"mes接口异常，产品过站失败，{result}")

        if error_msg:
            return self.x_response("false", f"mes接口异常，产品过站失败，{error_msg}")

        return self.x_response()


if __name__ == '__main__':
    ret1 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
 <soap:Body>
 <CheckUserDoResponse xmlns="WWW.SUNWODA.COM">
  <CheckUserDoResult>TRUE:数据插入成功</CheckUserDoResult>
 </CheckUserDoResponse>
 </soap:Body>
</soap:Envelope>"""

    root1 = xutil.XmlUtil.get_xml_root_by_str(ret1)
    ret2 = root1[0][0][0].text
    print(ret2)
