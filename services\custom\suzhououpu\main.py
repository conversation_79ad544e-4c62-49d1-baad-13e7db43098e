# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/28 下午4:17
# Author     ：sch
# version    ：python 3.8
# Description：苏州欧普
"""
import json
import os
import time
from typing import Any, <PERSON><PERSON>

from common import xcons, xrequest, xutil
from common.xutil import log, CircularList, x_response, filter_v3_status_code
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.MesEngine import ErrorMapEngine

cache_path = f"{os.getcwd()}/cache_data"

global_data = {}

circular_list = CircularList(1000)


def upload_offline_data() -> Tuple[int, int]:
    """
    上传离线缓存数据
    :return:
    """
    cache_data_list = os.listdir(cache_path)
    cache_data_list = [i for i in cache_data_list if i.endswith('.json')]
    log.info(f"上传离线缓存数据，离线数据包数量: {len(cache_data_list)}")

    ng_count = 0
    ok_count = 0

    for item in cache_data_list:
        cache_file = f"{cache_path}/{item}"
        log.info(f"正在上传离线数据：{cache_file}")
        cache_item = xutil.FileUtil.load_json_file(cache_file)

        try:
            api_url = cache_item.get('api_url')
            body_param = cache_item.get('body_param')

            ret1 = xrequest.RequestUtil.post_json(api_url, {
                "payload": json.dumps(body_param, ensure_ascii=False, separators=(",", ":"))
            })

            ret_str = ret1.get('payload')
            ret = json.loads(ret_str)

            if str(ret.get('code')) == '400':
                # 需要重传
                log.info(f"状态码400，数据需要重传！")
                ng_count += 1
                continue

            ok_count += 1
            os.remove(cache_file)
            log.info(f"数据上传成功，缓存数据已删除")

        except Exception as err:
            log.info(f"上传离线数据失败！err:{err}")
            ng_count += 1

    log.info(f"upload done!")
    log.info(f"upload total: {ok_count + ng_count} success:{ok_count} fail:{ng_count}")

    return ok_count, ng_count


def upload_device_status(time_now, api_url, param):
    """
    上传设备状态
    :param time_now:
    :param api_url:
    :param param:
    :return:
    """
    time_file = xutil.DateUtil.time_to_fmt_time(time_now, xcons.FMT_TIME_FILE)
    cache_filepath = f"{cache_path}/device_{time_file}_0_offline.json"

    ret_res = None
    try:
        ret1 = xrequest.RequestUtil.post_json(api_url, {
            "payload": json.dumps(param, ensure_ascii=False, separators=(",", ":"))
        })

        ret_str = ret1.get('payload')
        ret = json.loads(ret_str)

        if str(ret.get('code')) == '400':
            # 需要重传
            xutil.FileUtil.dump_json_to_file(cache_filepath, {
                "api_url": api_url,
                "body_param": param
            })
            log.info(f"数据已缓存到本地！")
            ret_res = x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('msg')}")

    except Exception as err:
        ret_res = x_response("false", f"上传数据失败，error：{err}")
        xutil.FileUtil.dump_json_to_file(cache_filepath, {
            "api_url": api_url,
            "body_param": param
        })
        log.info(f"数据已缓存到本地！")

    return ret_res


class Engine(ErrorMapEngine):
    version = {
        "title": "suzhououpu release v1.0.0.8",
        "device": "401",
        "feature": ["上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-29 16:18  上传数据，上传设备状态
date: 2024-05-20 15:28  修改参数
date: 2024-06-04 14:31  优化设备状态
date: 2024-06-04 17:44  在运行状态下，设备会同时触发 停止检查+安全门。这时候安全门的触发时间自动+1秒
date: 2024-06-05 11:32  01,02传绿灯，03,04,空闲传黄灯，其他故障传红灯
""", }

    form = {
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
        "api_url_device": {
            "ui_name": "接口URL(设备状态)",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        }
    }

    other_form = {

    }

    other_combo = {

    }

    combo = {
        "wait_time": {
            "ui_name": "进出板时间(s)",
            "item": ["0", "1", "2", "3", "4", "5",
                     "6", "7", "8", "9", "10",
                     "11", "12", "13", "14", "15",
                     "16", "17", "18", "19", "20",
                     "30", "35", "40", "45", "50",
                     "60", "70", "80", "90", "100",
                     "200", "300", "400", "500", "1000"
                     ],
            "value": "10",
        }
    }

    button = {
        "upload_offline_data": {
            "ui_name": "上传离线数据"
        }
    }

    def __init__(self):
        xutil.FileUtil.ensure_dir_exist(cache_path)

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.config_data["app_setting"]["custom_interval_cron"] = True
        main_window.config_data["app_setting"]["custom_interval_time"] = 2 * 60 * 60  # 2h
        self.log.info("init main window done!")

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        device_name = data_vo.get_value_by_cons_key("device_name")
        wait_time = data_vo.get_value_by_cons_key("wait_time", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_sn = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_data_list.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data_fmt = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_end_time": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time() + wait_time,
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,
                "board_sn": board_sn,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "comp_data": comp_data_list
            }

            cache_filepath = f"{cache_path}/data_{time_file}_{board_no}_offline.json"

            try:
                ret1 = xrequest.RequestUtil.post_json(api_url_data, {
                    "payload": json.dumps(board_data_fmt, ensure_ascii=False, separators=(",", ":"))
                })

                ret_str = ret1.get('payload')
                ret = json.loads(ret_str)

                if str(ret.get('code')) == '400':
                    # 需要重传
                    xutil.FileUtil.dump_json_to_file(cache_filepath, {
                        "api_url": api_url_data,
                        "body_param": board_data_fmt
                    })
                    self.log.info(f"数据已缓存到本地！")
                    ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

            except Exception as err:
                ret_res = self.x_response("false", f"上传数据失败，error：{err}")
                xutil.FileUtil.dump_json_to_file(cache_filepath, {
                    "api_url": api_url_data,
                    "body_param": board_data_fmt
                })
                self.log.info(f"数据已缓存到本地！")

        return ret_res

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        btn_key = btn_vo.get_btn_key()
        if btn_key == "upload_offline_data":
            ok_count, ng_count = upload_offline_data()

            if ng_count > 0:
                return self.x_response("false", f"上传总数量：{ok_count + ng_count} 上传成功：{ok_count} 上传失败：{ng_count}")

            if ok_count + ng_count == 0:
                return self.x_response("false", f"没有离线缓存数据，无需上传！")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_device = other_vo.get_value_by_cons_key("api_url_device")
        device_name = other_vo.get_value_by_cons_key("device_name")

        old_device_code = other_vo.get_old_device_status_code()
        device_status_str = other_vo.get_device_status_str()

        if old_device_code not in ["01", "04", "02", "03", "10", "11", "20", "21", "12", "13", "22", "23", "99"]:
            self.log.warning(f"该设备状态暂不统计！")
            return self.x_response()

        self.log.info(f"device status: {old_device_code}")

        time_now = int(time.time())

        ret_res = self.x_response()

        if old_device_code in ["01", "04", "02", "03"]:
            if old_device_code == "02":
                time_except = global_data.get("time_except", None)  # 异常时间点

                if time_except:
                    # 将异常时间清空
                    last_time = xutil.DateUtil.time_to_fmt_time(time_except)

                    last_time_except = global_data.get("time_except")
                    time_delta = time_now - last_time_except

                    global_data['time_except'] = None

                    log.info(f"上传故障结束....")

                    error_param = {
                        "device_name": device_name,
                        "device_detect_time": xutil.DateUtil.time_to_fmt_time(time_now),
                        "green": "OFF",
                        "yellow": "OFF",
                        "red": "ON",
                        "error_code": global_data.get('last_device_code'),
                        "error_message": global_data.get('last_device_status'),
                        "error_start_time": last_time,
                        "error_end_time": xutil.DateUtil.time_to_fmt_time(time_now),
                        "error_total_time_second": str(time_delta),
                    }

                    ret_3 = upload_device_status(time_now, api_url_device, error_param)
                    if ret_3:
                        ret_res = ret_3

            time_now += 1
            log.warning(f"time now + 1s")

            if old_device_code in ["01", "02"]:
                # 01，02  进板+开始检测
                green = "ON"
                yellow = "OFF"
            else:
                # 03, 04   停止检查+出板
                green = "OFF"
                yellow = "ON"

            green_param = {
                "device_name": device_name,
                "device_detect_time": xutil.DateUtil.time_to_fmt_time(time_now),
                "green": green,
                "yellow": yellow,
                "red": "OFF",
                "error_code": "",
                "error_message": "",
                "error_start_time": "",
                "error_end_time": "",
                "error_total_time_second": "",
            }

            ret_1 = upload_device_status(time_now, api_url_device, green_param)
            if ret_1:
                ret_res = ret_1

        else:
            # 故障开始
            last_device_code = global_data.get("last_device_code")
            # if last_device_code != "02":
            #     log.info(f"上一次的状态码：{last_device_code} 不是开始运行，不需要上传！")
            #     return self.x_response()
            # else:
            if not global_data.get("time_except"):
                global_data["time_except"] = time_now  # 记录异常时间点

                log.info(f"上传故障开始....")

                x_time_last = global_data.get("x_time_last")

                if x_time_last == time_now:
                    time_now += 1

                    log.warning(f"time now + 1s")

                error_param = {
                    "device_name": device_name,
                    "device_detect_time": xutil.DateUtil.time_to_fmt_time(time_now),
                    "green": "OFF",
                    "yellow": "OFF",
                    "red": "ON",
                    "error_code": old_device_code,
                    "error_message": device_status_str,
                    "error_start_time": xutil.DateUtil.time_to_fmt_time(time_now),
                    "error_end_time": "",
                    "error_total_time_second": "",
                }

                ret_2 = upload_device_status(time_now, api_url_device, error_param)
                if ret_2:
                    ret_res = ret_2
            else:
                self.log.warning(f"上一次故障还未清除，本次故障不发送！")

        global_data['last_device_code'] = old_device_code
        global_data['last_device_status'] = device_status_str
        global_data['x_time_last'] = time_now

        log.info("done")

        return ret_res

    def send_idle_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_device = other_vo.get_value_by_cons_key("api_url_device")
        device_name = other_vo.get_value_by_cons_key("device_name")

        ret_res = self.x_response()

        time_now = int(time.time())

        yellow_param = {
            "device_name": device_name,
            "device_detect_time": xutil.DateUtil.time_to_fmt_time(time_now),
            "green": "OFF",
            "yellow": "ON",
            "red": "OFF",
            "error_code": "",
            "error_message": "",
            "error_start_time": "",
            "error_end_time": "",
            "error_total_time_second": "",
        }

        ret4 = upload_device_status(time_now, api_url_device, yellow_param)
        if ret4:
            ret_res = ret4

        return ret_res

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        upload_offline_data()
