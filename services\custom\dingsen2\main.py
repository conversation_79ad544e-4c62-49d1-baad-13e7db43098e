# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/18 下午2:30
# Author     ：sch
# version    ：python 3.8
# Description：鼎森二厂/联新显示科技
"""
import json
from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "dingsen2 release v1.0.0.2",
        "device": "AIS401",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-18 14:32  条码校验，上传数据
date: 2024-07-10 15:13  修改参数格式
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(管控检查)",
            "value": "http://************:8089/api/Txn_SMTEquipment/Txn_SMTEquipment_AOI_CHECK"
        },
        "api_url_data": {
            "ui_name": "接口URL(数据保存)",
            "value": "http://************:8089/api/Txn_SMTEquipment/Txn_SMTEquipment_AOI_SAVE"
        },
    }

    form = {
        "equipment_encode": {
            "ui_name": "设备编号",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
        "process": {
            "ui_name": "工序",
            "value": ""
        },
    }

    combo = {
        "side": {
            "ui_name": "面别",
            "item": ["T", "B"],
            "value": "T"
        },
        "phase": {
            "ui_name": "阶别",
            "item": ["SMT", "DIP", "AI"],
            "value": "SMT"
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        equipment_encode = other_vo.get_value_by_cons_key("equipment_encode")
        operator = other_vo.get_value_by_cons_key("operator")
        process = other_vo.get_value_by_cons_key("process")
        side = other_vo.get_value_by_cons_key("side")
        phase = other_vo.get_value_by_cons_key("phase")

        phase_map = {
            "SMT": "1",
            "DIP": "2",
            "AI": "3",
        }

        phase_tmp = phase_map.get(phase)

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "EquipmentEncode": equipment_encode,
                "Operator": operator,
                "SpecificationName": process,
                "side": side,
                "phase": phase_tmp,
                "pcb_sn": sn,
                "board_sn": "",
            }

            full_param = {
                "JsonData": json.dumps(check_param, ensure_ascii=False)
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, full_param)
            if str(ret.get("code")) != "0":
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('msg')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        equipment_encode = data_vo.get_value_by_cons_key("equipment_encode")
        operator = data_vo.get_value_by_cons_key("operator")
        process = data_vo.get_value_by_cons_key("process")
        side = data_vo.get_value_by_cons_key("side")
        phase = data_vo.get_value_by_cons_key("phase")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        phase_map = {
            "SMT": "1",
            "DIP": "2",
            "AI": "3",
        }

        phase_tmp = phase_map.get(phase)

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "compName": comp_entity.designator,
                    "compBrand": comp_entity.type,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "angle": "",
                    "comp_image": comp_entity.image_path,
                })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        pcb_data = {
            "EquipmentEncode": equipment_encode,
            "Operator": operator,
            "SpecificationName": process,
            "side": side,
            "phase": phase_tmp,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, {
            "JsonData": json.dumps(pcb_data, ensure_ascii=False)
        })
        if str(ret.get("code")) != "0":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()
