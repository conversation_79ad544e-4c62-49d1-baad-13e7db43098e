# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/07/18 下午14:19
# Author     ：gyr
# version    ：python 3.8
# Description：青岛贝云
"""
import json
from datetime import datetime
from typing import Any

from common import xrequest
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["青岛贝云", "qingdaobeiyun"],
        "version": "release v1.0.0.1",
        "device": "40x",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-07-18 14:19 jira:ATAOI_2019-40604,上传数据
""", }

    form = {
        "mes_api_url": {
            "ui_name": "MES接口地址",
            "value": "http://**************:8712/aoi/upload2"
        },
        "work_station": {
            "ui_name": "工作中心",
            "value": ""
        },
        "emp": {
            "ui_name": "操作员工号",
            "value": ""
        },
        "brand_type": {
            "ui_name": "设备品牌",
            "value": ""
        },
        "model_name": {
            "ui_name": "机种名称",
            "value": ""
        },
        "machine_name": {
            "ui_name": "机器名称",
            "value": ""
        },
        "customer_name": {
            "ui_name": "客户名称",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
        "programmer": {
            "ui_name": "编程员",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        mes_api_url = data_dao.get_value_by_cons_key("mes_api_url")
        work_station = data_dao.get_value_by_cons_key("work_station")
        emp = data_dao.get_value_by_cons_key("emp")
        brand_type = data_dao.get_value_by_cons_key("brand_type")
        model_name = data_dao.get_value_by_cons_key("model_name")
        machine_name = data_dao.get_value_by_cons_key("machine_name")
        customer_name = data_dao.get_value_by_cons_key("customer_name")
        operator = data_dao.get_value_by_cons_key("operator")
        programmer = data_dao.get_value_by_cons_key("programmer")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)

        if pcb_entity.pcb_repair_result:  # P为通过，F为不良
            confirmed_result = "P"
        else:
            confirmed_result = "F"

        report_fail_count = 0
        confirmed_fail_count = 0

        inspect_time = datetime.now().strftime("%Y-%m-%d")

        json_data = {
            "brandType": brand_type,
            "mainSN": pcb_entity.pcb_barcode,
            "panelSN": "",
            "panelID": "",
            "modelName": model_name,
            "side": pcb_entity.board_side,
            "machineName": machine_name,
            "customerName": customer_name,
            "operator": operator,
            "programmer": programmer,
            "inspectionDate": datetime.now().strftime("%Y-%m-%d"),
            "beginTime":pcb_entity.get_start_time().strftime("%Y-%m-%d %H:%M:%S"),
            "endTime": pcb_entity.get_end_time().strftime("%Y-%m-%d %H:%M:%S"),
            "cycleTimeSec": pcb_entity.get_cycle_time(),
            "inspectionBatch": pcb_entity.order_id,
            "reportResult": pcb_entity.get_robot_result("P", "F"),  # P为通过，F为不良
            "confirmedResult": confirmed_result,
            "totalComponent": pcb_entity.comp_count,
            "reportFailComponent": report_fail_count,
            "confirmedFailComponent": confirmed_fail_count,
            "List_Detail": []
        }

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            for comp_entity in board_entity.yield_comp_entity():
                detail = {
                    "componentName": brand_type,
                    "mainSN": pcb_entity.pcb_barcode,
                    "panelSN": board_entity.barcode,
                    "panelID": board_entity.board_no,
                    "libraryModel": "",
                    "pN": comp_entity.part,
                    "pack": comp_entity.package,
                    "angle": f"{comp_entity.x_pos}_{comp_entity.y_pos}",
                    "reportResult": comp_entity.robot_ng_str,
                    "reportResultCode": comp_entity.robot_ng_code,
                    "confirmResult": comp_entity.repair_ng_str,
                    "confirmResultCode": comp_entity.repair_ng_code,
                    "img_Path": comp_entity.image_path
                }
                json_data["List_Detail"].append(detail)

                if not comp_entity.robot_result:
                    report_fail_count += 1
                if not comp_entity.repair_result:
                    confirmed_fail_count += 1

        json_data["reportFailComponent"] = report_fail_count
        json_data["confirmedFailComponent"] = confirmed_fail_count

        request_data = {
            "workstation": work_station,
            "emp": emp,
            "jsonData": json.dumps(json_data, ensure_ascii=False)
        }

        ret = xrequest.RequestUtil.post_json(mes_api_url, request_data)

        if ret.get("success"):
            return self.x_response()
        else:
            error_msg = ret.get("message")
            return self.x_response("false", error_msg)
