"""
# File       : main.py
# Time       ：2025/05/15 14:56
# Author     ："wxc"
# version    ：python 3.8
# Description：杭州松下
"""
from datetime import datetime
from typing import Any

from common import xcons, xrequest
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "customer": ["杭州松下", "hangzhousongxia"],
        "version": "release v1.0.0.3",
        "device": "AIS303B-C",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-15 14:59  ATAOI_2019-37769：上传数据
date: 2025-05-19 14:37  ATAOI_2019-37769：发送设备状态
date: 2025-06-16 11:52  按整板上传数据，整板时的拼板序号改为1，设备状态传参修改
"""
    }

    form = {
        "emp_no": {
            "ui_name": "操作人",
            "value": "",
        },
        "inspection_machine": {
            "ui_name": "设备号",
            "value": ""
        },
        "AOI_version": {
            "ui_name": "AOI检测程序版本",
            "value": ""
        }
    }
    other_form = {
        "upload_api_url": {
            "ui_name": "上传数据接口",
            "value": ""
        },
        "upload_status_url": {
            "ui_name": "设备状态上传接口",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        emp_no = data_vo.get_value_by_cons_key("emp_no")
        inspection_machine = data_vo.get_value_by_cons_key("inspection_machine")
        upload_api_url = data_vo.get_value_by_cons_key("upload_api_url")
        aoi_version = data_vo.get_value_by_cons_key("AOI_version")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)
        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT)

        ng_board_list = []
        comp_designators = []
        comp_robot_ng_codes = []
        comp_repair_ng_codes = []
        comp_robot_ng_number = 0
        comp_repair_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            no = board_entity.board_no
            # 整板时这个值不能传0
            if no == "0":
                no = "1"
            if board_entity.get_robot_result() == "NG":
                ng_board_list.append(int(no))
                comp_robot_ng_number += board_entity.comp_robot_ng_number
                comp_repair_ng_number += board_entity.comp_repair_ng_number
                for comp_entity in board_entity.yield_comp_entity():
                    if not comp_entity.robot_result:
                        comp_designators.append(comp_entity.designator)
                        comp_robot_ng_codes.append(comp_entity.robot_ng_code)
                        comp_repair_ng_codes.append(comp_entity.repair_ng_code)
        comp_designator = ",".join(comp_designators)
        comp_robot_ng_code = ",".join(comp_robot_ng_codes)
        comp_repair_ng_code = ",".join(comp_repair_ng_codes)
        board_param = {
            "sn": pcb_entity.get_unique_sn(),
            "snType": "SN",
            "empNo": emp_no,
            "testResult": pcb_entity.get_robot_result("PASS", "FAIL"),
            "testFailQty": comp_robot_ng_number,
            "badQty": comp_repair_ng_number,
            "boardNumber": ng_board_list,
            "remark": "",
            "orgCode": "PAPWMHZ",
            "inspectionMachine": inspection_machine,
            "programName": pcb_entity.project_name,
            "createDate": end_time,
            "reviseEndDate": end_time if pcb_entity.get_robot_result() == "OK" else review_time,
            "partsName": comp_designator,
            "partsArticleNo": "",
            "faultCode": comp_robot_ng_code,
            "revisedFaultId": comp_repair_ng_code,
            "reviseResult": pcb_entity.get_repair_result("PASS", "FAIL"),
            "version": aoi_version
        }

        ret = xrequest.RequestUtil.post_json(upload_api_url, board_param)
        if str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")
        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        upload_status_url = other_vo.get_value_by_cons_key("upload_status_url")
        inspection_machine = other_vo.get_value_by_cons_key("inspection_machine")

        eq_state = other_vo.get_status_code_v3()
        status_param = {
            "sourceType": 2,  # 1-SCADA ,2-心浩,3-其他
            "eqCode": inspection_machine,  # 设备编号
            "data": {  # 参数清单，所有数据放此对象内
                "eqstate": eq_state,  # 设备状态码
            },
            "date": datetime.now().strftime(xcons.FMT_TIME_DEFAULT)  # 时间
        }

        ret = xrequest.RequestUtil.post_json(upload_status_url, status_param)
        if str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('msg')}")
        return self.x_response()
