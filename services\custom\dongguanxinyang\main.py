# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/17 下午12:13
# Author     ：sch
# version    ：python 3.8
# Description：东莞新阳
"""

from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

csv_row_template = """
{拼板号},{拼板条码},{测试时间},{程序名},{拼板检测结果},{拼板复判结果},{拼板最终结果},{器件总数},{器件检测NG},{器件复判NG},{器件误报总数},{器件位号},{器件封装},{器件类型},{器件检测不良},{器件复判不良},{器件复判结果},{器件图片路径}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanxinyang release v1.0.0.2",
        "device": "AIS501、AIS431",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-17 12:13  生成本地文档到本地
date: 2025-03-18 09:23  增加AIS431机型
""",
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT1)
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        date_file = time_file[:8]

        save_full_path = f"{save_path}/{date_file}"

        xutil.FileUtil.ensure_dir_exist(save_full_path)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            csv_content = f"拼板号,拼板条码,测试时间,程序名,拼板检测结果,拼板复判结果,拼板最终结果,器件总数,器件检测NG,器件复判NG,器件误报总数,器件位号,器件封装,器件类型,器件检测不良,器件复判不良,器件复判结果,器件图片路径"

            had_rows = False

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    csv_content += csv_row_template.format(**{
                        "拼板号": board_no,
                        "拼板条码": barcode,
                        "测试时间": start_time,
                        "程序名": pcb_entity.project_name,
                        "拼板检测结果": board_entity.get_robot_result("PASS", "FAIL"),
                        "拼板复判结果": board_entity.get_repair_result("PASS", "FAIL"),
                        "拼板最终结果": board_entity.get_final_result("PASS", "REPASS", "FAIL"),
                        "器件总数": board_entity.comp_total_number,
                        "器件检测NG": board_entity.comp_robot_ng_number,
                        "器件复判NG": board_entity.comp_repair_ng_number,
                        "器件误报总数": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                        "器件位号": comp_entity.designator,
                        "器件封装": comp_entity.package,
                        "器件类型": comp_entity.type,
                        "器件检测不良": comp_entity.robot_ng_str,
                        "器件复判不良": comp_entity.repair_ng_str,
                        "器件复判结果": comp_entity.get_final_result("PASS", "PASS", "FAIL"),
                        "器件图片路径": comp_entity.image_path,
                    })

                    had_rows = True

            if not had_rows:
                csv_content += csv_row_template.format(**{
                    "拼板号": board_no,
                    "拼板条码": barcode,
                    "测试时间": start_time,
                    "程序名": pcb_entity.project_name,
                    "拼板检测结果": board_entity.get_robot_result("PASS", "FAIL"),
                    "拼板复判结果": board_entity.get_repair_result("PASS", "FAIL"),
                    "拼板最终结果": board_entity.get_final_result("PASS", "REPASS", "FAIL"),
                    "器件总数": board_entity.comp_total_number,
                    "器件检测NG": board_entity.comp_robot_ng_number,
                    "器件复判NG": board_entity.comp_repair_ng_number,
                    "器件误报总数": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                    "器件位号": "",
                    "器件封装": "",
                    "器件类型": "",
                    "器件检测不良": "",
                    "器件复判不良": "",
                    "器件复判结果": "",
                    "器件图片路径": "",
                })

            xutil.FileUtil.write_content_to_file(
                f"{save_full_path}/{time_file}_{board_no}_{barcode}.csv",
                csv_content
            )

        return self.x_response()
