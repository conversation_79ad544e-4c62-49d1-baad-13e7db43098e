# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : mar_module.py
# Time       ：2025/4/26 上午11:25
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil
from common.xutil import log


def parse_board_side(review_path: str):
    """
    解析板面
    docs: https://jira.cvte.com/browse/ATAOI_2019-38504

    return (0,顶面) (1,底面)
    """
    project_xml = f"{review_path}/mes/project.xml"

    if not review_path or not xutil.FileUtil.file_is_exists(project_xml):
        log.warning(f"未解析到 project.xml 文件，返回默认值 0")
        return "0"

    root = xutil.XmlUtil.get_xml_root_by_file(project_xml)
    product_board_side = root.attrib.get("ProductSurface")  # 0代表顶面，1代表底面
    return product_board_side


if __name__ == '__main__':
    parse_board_side("/home/<USER>/aoi/run/results/333.999bak/20250423/T_20250423172422008_1_NG")
