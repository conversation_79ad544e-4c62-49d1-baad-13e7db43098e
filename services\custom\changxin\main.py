#!/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/5/9 下午4:58
# Author     ：gyr
# version    ：python 3.8
# Description：长鑫(根据标准版做修改)
"""
from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}  
器件误报总数,{pcb_comp_repass_number}
LotID,{pcb_order}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["长鑫", "changxin"],
        "version": "release v1.0.0.1",
        "device": "AIS40X,AIS431,AIS63X",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2025-05-09 17:01  jira:ATAOI_2019-39395,只要标准版中生成csv功能,增加批次号字段
""", }

    combo = {
        "is_save_local": {
            "ui_name": "保存数据到本地",
            "item": [
                "是",
                "否",
            ],
            "value": "是"
        },
        "panel_filename": {
            "ui_name": "整板文件名格式",
            "item": [
                "时间_整板条码",
                "整板条码_时间",
                "整板条码",
                "时间",
            ],
            "value": "时间_整板条码"
        },
        "save_path_type": {
            "ui_name": "保存数据路径格式",
            "item": [
                '数据路径',
                '数据路径/日期(yyyymmdd)',
                '数据路径/设备名称',
                '数据路径/设备名称/日期(yyyymmdd)',
                '数据路径/日期(yyyymmdd)/设备名称',
            ],
            "value": "数据路径/设备名称/日期(yyyymmdd)"
        },
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "全部"
        },
        "newline_type": {
            "ui_name": "换行符格式",
            "item": [
                "window",
                "linux",
            ],
            "value": "window"
        }
    }

    path = {
        "save_path": {
            "ui_name": "数据路径",
            "value": ""
        },
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        is_save_local = data_vo.get_value_by_cons_key("is_save_local")
        panel_filename = data_vo.get_value_by_cons_key("panel_filename")
        save_path = data_vo.get_value_by_cons_key("save_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")
        save_path_type = data_vo.get_value_by_cons_key("save_path_type")
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")
        newline_type = data_vo.get_value_by_cons_key("newline_type")

        self.log.info(f"文件生成格式：csv")

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        # 批次号
        pcb_order_id = pcb_entity.order_id

        time_date = time_str[:8]
        if save_path_type == "数据路径":
            dst_path = f"{save_path}"
        elif save_path_type == "数据路径/日期(yyyymmdd)":
            dst_path = f"{save_path}/{time_date}"
        elif save_path_type == "数据路径/设备名称":
            dst_path = f"{save_path}/{device_name}"
        elif save_path_type == "数据路径/设备名称/日期(yyyymmdd)":
            dst_path = f"{save_path}/{device_name}/{time_date}"
        elif save_path_type == "数据路径/日期(yyyymmdd)/设备名称":
            dst_path = f"{save_path}/{time_date}/{device_name}"
        else:
            return self.x_response("false", f"不支持的数据路径格式：{save_path_type}")

        if is_save_local == "是":
            xutil.FileUtil.ensure_dir_exist(dst_path)

        board_data = []
        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                print(comp_entity)

                if comp_data_output == "全部" or \
                        (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                        (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

        pcb_data = {
            "device_name": device_name,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "board_data": board_data,
            "pcb_order": pcb_order_id
        }

        if panel_filename == "时间_整板条码":
            dst_filename = f"{time_str}_{pcb_sn}"
        elif panel_filename == "整板条码_时间":
            dst_filename = f"{pcb_sn}_{time_str}"
        elif panel_filename == "整板条码":
            dst_filename = f"{pcb_sn}"
        elif panel_filename == "时间":
            dst_filename = f"{time_str}"
        else:
            return self.x_response("false", f"不支持的整板文件名格式：{panel_filename}")

        dst_filename = f"{dst_filename}.csv"
        dst_filepath = f"{dst_path}/{dst_filename}"

        comp_data_str = ""
        for _board_data in pcb_data.get("board_data", []):
            for _comp_data in _board_data.get("comp_data", {}):
                _comp_data.update(_board_data)
                comp_data_str += csv_comp_panel_template.format(**_comp_data)

        pcb_data["CompData"] = comp_data_str
        pcb_content = csv_pcb_panel_template.format(**pcb_data)

        if newline_type == 'window':
            pcb_content = pcb_content.replace('\n', '\r\n')

        if is_save_local == "是":
            xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)
            self.log.info(f"已生成CSV文件: {dst_filepath}")

        return self.x_response()