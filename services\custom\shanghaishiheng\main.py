"""
# File       : main.py
# Time       ：2025/05/28 15:18
# Author     ："wxc"
# version    ：python 3.8
# Description：上海仕恒
"""
from typing import Any

from PIL import Image

from common import xrequest, xcons
from common.xutil import x_response, log
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo


def get_token(api_url_token: str, userid: str, password: str, dbaias: str, timeout=5):
    """
    获取Token
    :param api_url_token: 获取Token的API URL
    :param userid: 用户名
    :param password: 密码
    :param dbaias: MES数据库配置的实体
    """
    ret = xrequest.RequestUtil.post_json(
        api_url_token, {"userid": userid, "password": password, "dbaias": dbaias}, timeout=timeout)
    data = ret.get("Data", {})
    if ret.get("ErrCode") != 0:
        log.logger.error(f"获取Token失败，错误类型：{data.get('errtype')}")
        return None

    token = data.get("token")

    return token


class Engine(ErrorMapEngine):
    version = {
        "customer": ["上海仕恒", "shanghaishiheng"],
        "version": "release v1.0.0.1",
        "device": "203",
        "feature": ["获取条码", "条码校验", "上传数据", "获取token"],
        "author": "wxc",
        "release": """
date: 2025-05-28 15:21 ATAOI_2019-39862: 获取条码，条码校验，上传数据，获取token
"""
    }
    other_form = {
        "api_url_token": {
            "ui_name": "接口URL(获取Token)",
            "value": ""
        },
        "api_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": ""
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": ""
        },
        "api_url_upload": {
            "ui_name": "接口URL(上传数据)",
            "value": ""
        }
    }
    form = {
        "plan_number": {
            "ui_name": "工单编码",
            "value": ""
        },
        "wp_number": {
            "ui_name": "工序编码",
            "value": ""
        },
        "dev_number": {
            "ui_name": "设备编码",
            "value": ""
        },
        "dbaias": {
            "ui_name": "MES数据库配置的实体",
            "value": ""
        },
        "userid": {
            "ui_name": "登录账号",
            "value": "Admin"
        },
        "password": {
            "ui_name": "登录密码",
            "value": "[D1[1?[213rr]6rD+21rr221+?31D6~+"
        }
    }
    path = {
        "board_img_path": {
            "ui_name": "拼板图保存路径",
            "value": ""
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_get_sn = other_vo.get_value_by_cons_key("api_get_sn")
        api_url_token = other_vo.get_value_by_cons_key("api_url_token")
        userid = other_vo.get_value_by_cons_key("userid")
        password = other_vo.get_value_by_cons_key("password")
        dbaias = other_vo.get_value_by_cons_key("dbaias")
        plan_number = other_vo.get_value_by_cons_key("plan_number")

        token = get_token(api_url_token, userid, password, dbaias)
        if not token:
            return x_response("false", "获取Token失败，请检查配置")

        pcb_sn = other_vo.get_pcb_sn()
        headers = {
            "Authorization": f"Bearer {token}",
        }
        get_sn_param = {
            "PlanNumber": plan_number,  # 工单编码
            "Fixture": pcb_sn  # 载具条码
        }
        ret = xrequest.RequestUtil.post_json(api_get_sn, get_sn_param, headers=headers)
        if ret.get("ErrCode") != 0:
            return x_response("false", f"mes接口异常，error：{ret.get('ErrMsg')}")
        sn_str = ret.get("LabelCode")
        return self.x_response("true", sn_str.replace(";", ","))

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        api_url_token = other_vo.get_value_by_cons_key("api_url_token")
        userid = other_vo.get_value_by_cons_key("userid")
        password = other_vo.get_value_by_cons_key("password")
        dbaias = other_vo.get_value_by_cons_key("dbaias")
        plan_number = other_vo.get_value_by_cons_key("plan_number")
        wp_number = other_vo.get_value_by_cons_key("wp_number")
        dev_number = other_vo.get_value_by_cons_key("dev_number")

        token = get_token(api_url_token, userid, password, dbaias)
        if not token:
            return x_response("false", "获取Token失败，请检查配置")

        headers = {
            "Authorization": f"Bearer {token}",
        }
        pcb_sn = other_vo.get_pcb_sn()
        get_sn_param = {
            "PlanNumber": plan_number,  # 工单编码
            "LabelCode": pcb_sn,  # 标签
            "CheckOnly": "1",
            "WpNumber": wp_number,  # 工序编码
            "DevNumber": dev_number,  # 设备编码 注：设备或工序至少传一个
            "programVer": other_vo.get_project_name()  # 程序名

        }
        ret = xrequest.RequestUtil.post_json(api_url_check, get_sn_param, headers=headers)
        if ret.get("ErrCode") != 0:
            return self.x_response("false", f"mes接口异常，error：{ret.get('ErrMsg')}")
        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_upload = data_vo.get_value_by_cons_key("api_url_upload")
        api_url_token = data_vo.get_value_by_cons_key("api_url_token")
        userid = data_vo.get_value_by_cons_key("userid")
        password = data_vo.get_value_by_cons_key("password")
        dbaias = data_vo.get_value_by_cons_key("dbaias")
        plan_number = data_vo.get_value_by_cons_key("plan_number")
        wp_number = data_vo.get_value_by_cons_key("wp_number")
        dev_number = data_vo.get_value_by_cons_key("dev_number")
        board_img_path = data_vo.get_value_by_cons_key("board_img_path")

        token = get_token(api_url_token, userid, password, dbaias)
        if not token:
            return x_response("false", "获取Token失败，请检查配置")
        headers = {
            "Authorization": f"Bearer {token}",
        }

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        pcb_sn = pcb_entity.pcb_barcode
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        # 拼板图
        box_pos_map = pcb_entity.get_board_box_position()
        pcb_image_obj = Image.open(pcb_entity.get_pcb_t_image())

        barcode_list = []
        file_detil_list = []
        board_detail_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_no = board_entity.board_no
            barcode = board_entity.barcode
            # 裁切保存拼板图
            box = box_pos_map.get(board_no)
            cropped_image = pcb_image_obj.crop(box)
            board_dst_img = f"{board_img_path}/{barcode}_{board_no}_{time_file}.jpg"
            cropped_image.save(board_dst_img)

            barcode_list.append(board_entity.barcode)

            if board_entity.get_final_result() == "NG":
                file_detil_list.append({
                    "SEQ": board_no,
                    "FilePath": board_dst_img,  # 图片路径
                    "Remark": ""
                })
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.get_final_result() == "NG":
                    board_detail_list.append({
                        "SEQ": board_no,
                        "BadNumber": comp_entity.robot_ng_code,  # 不良编码
                        "BadPoint": comp_entity.designator,  # 不良点位
                        "BadName": comp_entity.robot_ng_str,  # 不良名称
                        "PSBadNumber": comp_entity.repair_ng_code,  # 人员判定不良代码
                        "BadQty": 1,  # 不良数量
                        "ImagePath": comp_entity.image_path,  # 图片路径
                        "Remark": "",
                        "FieldEx1": "",  # 预留扩展字段
                        "FieldEx2": "",
                        "FieldEx3": "",
                        "FieldEx4": "",
                        "FieldEx5": ""
                    })

        pcb_data_param = {
            "PlanNumber": plan_number,  # 工单
            "LabelCode": ";".join(barcode_list),
            "CheckOnly": "0",
            "Result": pcb_entity.get_final_result("1", "1", "0"),
            "WpNumber": wp_number,  # 工序
            "DevNumber": dev_number,  # 设备 -工序至少传一个
            "Fixture": pcb_sn,
            "IsBingFisture": 1,
            "programVer": pcb_entity.project_name,  # 程序
            "Remark": "",
            "FileDetail": file_detil_list,  # 文件附件
            "BadDetail": board_detail_list,  # 不良明细
            "InfoDetail": []  # 关键信息
        }
        ret = xrequest.RequestUtil.post_json(api_url_upload, pcb_data_param, headers=headers)
        if ret.get("ErrCode") != 0:
            return self.x_response("false", f"mes接口异常，error：{ret.get('ErrMsg')}")

        return self.x_response()
