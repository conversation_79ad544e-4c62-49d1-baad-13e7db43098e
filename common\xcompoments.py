# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xcompoments.py
# Time       ：2023/7/14 下午4:26
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
import traceback
from typing import Tuple

import pymysql
from PyQt5 import QtGui
from PyQt5.QtWidgets import *
from PyQt5.QtWidgets import QComboBox, QLineEdit

from common import xutil
from common.xutil import log
from services.route import engine
from vo.mes_vo import ComboVo


def get_user_role_by_username(username: str) -> Tuple:
    """
    根据用户名获取用户权限
    """
    db = pymysql.connect(host='127.0.0.1',
                         user='root',
                         password='aoi2014',
                         database='Authority')

    cursor = db.cursor()

    sql = "SELECT t1.username, t1.password, t2.role_name FROM tb_user t1 LEFT JOIN tb_role t2 on t1.role_id = t2.id where t1.username=%s"
    ret = cursor.execute(sql, username)
    if ret == 0:
        return "", "", ""

    row = cursor.fetchone()

    return row


class LoginDialog(QDialog):
    def __init__(self, username, dialog_title: str = "登录Mes超级管理员"):
        super().__init__()
        self.username = xutil.CacheUtil.get("super_user", username)

        self.init_ui(dialog_title)

        screen = QDesktopWidget().screenGeometry()

        # 获取窗口坐标系
        size = self.geometry()  # noqa

        new_left = (screen.width() - size.width()) / 2
        new_top = (screen.height() - size.height()) / 2

        self.move(int(new_left), int(new_top))  # noqa

    def init_ui(self, dialog_title: str = "登录Mes超级管理员"):
        print(dialog_title)
        layout = QVBoxLayout()

        self.username_edit = QLineEdit(self)
        layout.addWidget(self.username_edit)

        self.password_edit = QLineEdit(self)
        self.password_edit.setEchoMode(QLineEdit.Password)  # 隐藏密码
        layout.addWidget(self.password_edit)

        if self.username:
            self.username_edit.setText(self.username)
            self.password_edit.setFocus()
        else:
            self.username_edit.setFocus()

        login_button = QPushButton("Login", self)
        login_button.clicked.connect(self.login)
        layout.addWidget(login_button)

        self.message_label = QLabel("", self)
        layout.addWidget(self.message_label)

        self.setLayout(layout)
        self.setWindowTitle(dialog_title)

    def login(self):
        username = self.username_edit.text()
        password = self.password_edit.text()

        # 在实际应用中，这里应该进行用户名和密码的验证
        # 如果验证成功，可以关闭登录对话框并打开目标 QDialog

        u, p, r = get_user_role_by_username(username)

        if u != username or not u:
            self.message_label.setText("账号不存在")
        elif p != password:
            self.message_label.setText("密码不正确！")
        else:
            self.accept()

        xutil.CacheUtil.set("super_user", username)


class CustomCombo(QComboBox):

    def __init__(self, main_window, combo_key="", ui_name="", *args):
        self.main_window = main_window
        self.combo_key = combo_key
        self.ui_name = ui_name
        super(CustomCombo, self).__init__(*args)

        # self.currentIndexChanged.connect(self.combo_index_changed)
        self.activated.connect(self.on_combo_activated)

    def wheelEvent(self, e: QtGui.QWheelEvent) -> None:
        """
        重写这个方法
        鼠标悬停时，不改变combo的选项
        """
        e.accept()
        # super().wheelEvent(e)

    def log_info(self, msg: str, status=True, send_to_repair=False):
        """
        复现主窗口的打印函数
        """
        return self.main_window.log_info(msg, status, send_to_repair, pop_prompt=False)

    def mousePressEvent(self, e: QtGui.QMouseEvent) -> None:
        """
        combo组件被点击
        :param e:
        :return:
        """
        tmp_combo = getattr(self.main_window, f"combo_{self.combo_key}")

        combo_file = self.main_window.config_data.get("combo")
        ui_name_combo = combo_file.get(self.combo_key, {}).get("ui_name", "")

        try:
            combo_vo = {
                "combo_key": self.combo_key,
                "combo_value": tmp_combo.currentText(),
            }
            log.info(f"-----下拉组件被点击------{ui_name_combo}")
            combo_ret = engine.combo_mouse_press(ComboVo(combo_vo, self.main_window.config_data), self.main_window)
            if not combo_ret:
                QComboBox.mousePressEvent(self, e)
                return

            if not combo_ret.get("result"):
                self.log_info(f"获取{ui_name_combo}列表失败，error：{combo_ret.get('string')}", False)

                QComboBox.mousePressEvent(self, e)
                return
            else:
                ret_str = combo_ret.get("string")
                ret_json = json.loads(ret_str)
                new_items = ret_json.get("new_items")

                tmp_combo.clear()
                for item in new_items:
                    tmp_combo.addItem(item)

                # config_data = xutil.FileUtil.load_config_file()
                # combo_file = config_data.get("combo", {})
                # combo_obj = combo_file.get(self.combo_key, {})
                # combo_obj["item"] = new_items
                # combo_obj["value"] = new_items[0]
                # combo_file[self.combo_key] = combo_obj
                # config_data["combo"] = combo_file
                #
                # self.main_window.config_data["combo"] = combo_file
                # self.main_window.save_config_data_to_file()

        except Exception as err:
            self.log_info(f"其他异常，获取{ui_name_combo}列表失败，error: {err}", False)
            log.warning(traceback.format_exc())
        finally:
            QComboBox.mousePressEvent(self, e)

    # def combo_index_changed(self):
    #     """
    #     combo组件变化, 只有选项切换，并且切换完成的时候才会触发
    #     :return:
    #     """
    #     combo_key = self.combo_key
    #     combo_current_text = self.currentText()
    #
    #     try:
    #         combo_vo = {
    #             "combo_key": combo_key.replace("combo_", ""),
    #             "combo_value": combo_current_text,
    #         }
    #
    #         if combo_current_text:
    #             config_data = xutil.FileUtil.load_config_file()
    #             combo_file = config_data.get("combo", {})
    #             combo_obj = combo_file.get(combo_vo["combo_key"], {})
    #             combo_obj["value"] = combo_current_text
    #             combo_file[combo_vo["combo_key"]] = combo_obj
    #
    #             self.main_window.config_data["combo"] = combo_file
    #             self.main_window.save_config_data_to_file()
    #
    #             combo_ui_name = combo_obj.get("ui_name")
    #
    #             self.log_info(f"[{combo_ui_name}]已切换到[{combo_current_text}]")
    #
    #         combo_ret = engine.combo_index_changed(ComboDao(combo_vo, self.main_window.config_data), "")
    #         if not combo_ret:
    #             return
    #
    #         if not combo_ret.get("result"):
    #             self.log_info(f"下拉定制功能触发失败，error：{combo_ret.get('string')}", False)
    #             return
    #
    #         else:
    #             pass
    #
    #     except Exception as e:
    #         self.log_info(f"其他异常，下拉定制功能出错，error: {e}", False)
    #         log.warning(traceback.format_exc())

    def on_combo_activated(self):
        """
        切换combo按钮时的操作
        """
        try:
            combo_current_text = self.currentText()
            combo_key = self.combo_key

            combo_vo = {
                "combo_key": combo_key.replace("combo_", ""),
                "combo_value": combo_current_text,
            }

            # if combo_current_text:
            #     config_data = self.main_window.config_data
            #     combo_file = config_data.get("combo", {})
            #     combo_obj = combo_file.get(combo_vo["combo_key"], {})
            #     combo_obj["value"] = combo_current_text
            #     combo_file[combo_vo["combo_key"]] = combo_obj
            # self.main_window.config_data["combo"] = combo_file
            # self.main_window.save_config_data_to_file()

            # self.log_info(f"[{self.ui_name}]已切换到[{combo_current_text}]")

            combo_dao = ComboVo(combo_vo, self.main_window.config_data)

            engine.combo_index_changed(combo_dao, self.main_window)

        except Exception as err:
            self.log_info(f"其他异常，error：{err}", False)
            log.warning(traceback.format_exc())


class CustomComboOther(QComboBox):

    def __init__(self, parent=None, combo_key=None, main_window=None):
        super().__init__(parent)

        self.combo_key = combo_key
        self._previous_index = -1  # 初始化前一个索引为 -1
        self.main_window = main_window

        self.currentIndexChanged.connect(self.combo_index_changed)

    def wheelEvent(self, e: QtGui.QWheelEvent) -> None:
        """
        重写这个方法
        鼠标悬停时，不改变combo的选项
        """
        e.accept()

    def showPopup(self):
        """
        重写 showPopup 方法，在下拉框显示前保存当前的索引。
        """
        self._previous_index = self.currentIndex()  # 保存当前的索引
        super().showPopup()

    def combo_index_changed(self):
        """
        combo组件变化, 只有选项切换，并且切换完成的时候才会触发
        :return:
        """
        if self._previous_index == -1:
            return

        combo_current_text = self.currentText()
        combo_key = self.combo_key

        combo_vo = {
            "combo_key": combo_key.replace("combo_", ""),
            "combo_value": combo_current_text,
        }

        combo_dao = ComboVo(combo_vo, self.main_window.config_data)

        try:
            engine.combo_index_changed_other(combo_dao, {
                "last_index": self._previous_index,
                "current_index": self.currentIndex(),
            })
        except Exception as err:
            log.warning(f"其他异常，error：{err}")
            log.warning(traceback.format_exc())


# class SearchCustomCombo(CustomCombo):
#     """
#     带搜索功能的下拉框
#     """
#     activated = pyqtSignal(str)
#
#     def __init__(self, main_window, combo_key="", ui_name="", *args):
#         super(SearchCustomCombo, self).__init__(main_window, combo_key, ui_name, *args)
#
#         self.setEditable(True)
#         self._filter_model = QSortFilterProxyModel(self)
#         self._filter_model.setFilterCaseSensitivity(Qt.CaseInsensitive)
#         self._filter_model.setSourceModel(self.model())
#         self.completer = QCompleter(self._filter_model, self)
#         self.completer.setCompletionMode(QCompleter.UnfilteredPopupCompletion)
#         self.completer.setCaseSensitivity(Qt.CaseInsensitive)
#         self.setCompleter(self.completer)
#         self.lineEdit().textEdited.connect(self._filter_model.setFilterFixedString)
#         self.completer.activated.connect(self.on_completer_activated)
#
#     def on_completer_activated(self, text):
#         if text:
#             index = self.findText(text)
#             self.setCurrentIndex(index)
#             self.activated.emit(self.itemText(index))
#
#     def setModel(self, model):
#         super(SearchCustomCombo, self).setModel(model)
#         self._filter_model.setSourceModel(model)
#         self.completer.setModel(self._filter_model)
#
#     def setModelColumn(self, column):
#         self.completer.setCompletionColumn(column)
#         self._filter_model.setFilterKeyColumn(column)
#         super(SearchCustomCombo, self).setModelColumn(column)
#
#     def keyPressEvent(self, e):
#         if e.key() in (Qt.Key_Enter, Qt.Key_Return):
#             text = self.currentText()
#             index = self.findText(text, Qt.MatchExactly | Qt.MatchCaseSensitive)
#             if index != -1:
#                 self.setCurrentIndex(index)
#                 self.hidePopup()
#             else:
#                 # 处理未找到匹配项的情况
#                 # 可以添加新项，显示警告框，或者什么也不做
#                 pass  # 示例: 什么也不做
#             super(SearchCustomCombo, self).keyPressEvent(e)
#         else:
#             super(SearchCustomCombo, self).keyPressEvent(e)
