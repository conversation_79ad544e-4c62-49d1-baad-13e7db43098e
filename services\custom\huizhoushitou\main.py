# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/11/22 上午9:21
# Author     ：sch
# version    ：python 3.8
# Description：惠州石头
"""

from typing import Any

from common import xrequest, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoushitou release v1.0.0.4",
        "device": "AIS501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-11-22 10:19  条码校验，上传数据到mes
date: 2024-11-22 17:04  优化异常弹窗
date: 2024-11-22 17:36  修改请求参数
date: 2024-11-26 09:58  优化弹窗提示信息
""",
    }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/WebService/LaserCarvingWebService.asmx/ATECommandCode",
        }
    }

    form = {
        "emp": {
            "ui_name": "工号",
            "value": "",
        },
        "station": {
            "ui_name": "工序名称",
            "value": "",
        },
        "resource": {
            "ui_name": "资源名称",
            "value": "",
        },
        "machine": {
            "ui_name": "设备编码",
            "value": "",
        },
        "fixture_id": {
            "ui_name": "工治具编码",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        emp = other_vo.get_value_by_cons_key("emp")
        station = other_vo.get_value_by_cons_key("station")
        resource = other_vo.get_value_by_cons_key("resource")
        machine = other_vo.get_value_by_cons_key("machine")
        fixture_id = other_vo.get_value_by_cons_key("fixture_id")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = f"01;{emp};{sn};{station};{resource};{machine};{fixture_id};"
            ret = xrequest.RequestUtil.post_form(api_url, {'commandString': check_param}, to_json=False)
            result = xutil.XmlUtil.get_xml_root_by_str(ret).text
            res_data = result.split(";")
            # if res_data[0] != "OK":
            #     ret_res = self.x_response("false", f"接口异常，条码校验失败，error：{res_data[1]}")
            if "OK" not in res_data:
                ret_res = self.x_response("false", f"接口异常，条码校验失败，error：{result}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        emp = data_vo.get_value_by_cons_key("emp")
        station = data_vo.get_value_by_cons_key("station")
        resource = data_vo.get_value_by_cons_key("resource")
        machine = data_vo.get_value_by_cons_key("machine")
        fixture_id = data_vo.get_value_by_cons_key("fixture_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_list = []

            for ix, comp_entity in enumerate(board_entity.yield_comp_entity()):
                ix += 1

                if comp_entity.is_repair_ng():
                    repair_ng_str = comp_entity.repair_ng_str
                    comp_tag = comp_entity.designator

                    comp_ng_list.append(f"{str(board_no).zfill(2)}:{repair_ng_str}:{comp_tag}")

            comp_info = ""
            if comp_ng_list:
                comp_info = f"{','.join(comp_ng_list)};"

            board_result = board_entity.get_repair_result("OK", "NG")

            save_param5 = f"05;{emp};{barcode};{station};{resource};{machine};{fixture_id};{board_result};{comp_info}"

            ret = xrequest.RequestUtil.post_form(api_url, {'commandString': save_param5}, to_json=False)
            result = xutil.XmlUtil.get_xml_root_by_str(ret).text
            res_data = result.split(";")
            # if res_data[0] != "OK":
            if "OK" not in res_data:
                ret_res = self.x_response("false", f"接口异常，上传测试数据失败，error：{result}")

        return ret_res
