# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main_window.py
# Time       ：2022/12/28 下午5:31
# Author     ：sch
# version    ：python 3.8
# Description：主窗口
"""
import functools
import os
import stat
import sys
import traceback
from datetime import datetime

from PyQt5 import QtGui
from PyQt5.QtCore import QTime, pyqtSignal, QRegExp
from PyQt5.QtGui import QInt<PERSON>alida<PERSON>, QRegExpValidator
from PyQt5.QtWidgets import *

from common import xutil, xconfig, xrequest
from common.xcompoments import CustomCombo, LoginDialog
from common.xcons import EN_KEY
from common.xutil import log
from engine.DataEngine import data_engine
from services.backed_task_service import TaskThread
from services.http_server import HttpServerQThread
from services.route import engine
from services.schedule_service import ScheduleThread
from services.socket_service import SocketThread
from static import xicon
from templates.main_window import U<PERSON>_MesMainWindow
from views.about_me_dialog import AboutMeDialog
from views.config_dialog import CommonConfigViews
from views.cron_setting import CronSettingViews
from views.error_code_map_widget import ErrorCodeWidget
from views.fake_send import FakeSendViews
from views.login_widget import LoginViews
from views.other_param_setting import OtherParamViews
from views.other_setting import OtherSettingViews
from vo.mes_vo import OtherVo, ButtonVo


# 定义启动FastAPI服务的函数
# def start_fastapi_server():  # noqa
#     host = "0.0.0.0"
#     port = 8082
#     log.info(f"http服务已启动 -----> http://{host}:{port}  test connect: http://127.0.0.1:{port}/ping")  # noqa
#     uvicorn.run(app, host=host, port=port)


class MainWindow(QMainWindow, Ui_MesMainWindow):
    upload_log = pyqtSignal(str, str, bool)

    # def start_server(self):
    #     # 在单独的线程中启动FastAPI服务
    #     self.server_thread = threading.Thread(target=start_fastapi_server)
    #     self.server_thread.daemon = True  # 设置为守护线程
    #     self.server_thread.start()
    #     print("starting")

    def __init__(self, tray_icon):
        """
        tray_icon：系统托盘
        """
        super(MainWindow, self).__init__()
        self.tray_icon = tray_icon

        # 显示错误信息时，当新的消息需要显示时，先关闭旧的实例，再显示新的消息
        self.current_message_box = None  # 错误弹窗

        # qt ui相关
        self.setupUi(self)

        # /home/<USER>/.config/Leichen/mesconfig.json 的内存缓存
        self.config_data = {}  # 将会在`self.load_config()`中加载

        # 确保环境ok
        self.ensure_env_ok()

        # 加载配置文件
        self.load_config()

        self.other_setting_views = OtherSettingViews(self)
        self.other_param_views = OtherParamViews(self)
        self.fake_send_views = FakeSendViews()
        self.cron_setting_views = CronSettingViews(self)
        self.common_config_views = CommonConfigViews(self)
        self.login_views = LoginViews(self)
        # self.socket_service_status = True

        self.btn_layout1 = QHBoxLayout(self.frame_btn_1)
        self.btn_layout1.setContentsMargins(5, 0, 5, 0)
        self.btn_layout1.setObjectName("btn_frame_layout2")

        self.btn_layout2 = QHBoxLayout(self.frame_btn_2)
        self.btn_layout2.setContentsMargins(5, 0, 5, 0)
        self.btn_layout2.setObjectName("btn_frame_layout2")

        self.other_ui_setting()
        self.load_param_key_to_ui()

        self.update_slot()

        # 一些简单的初始化
        self.auto_login()

        try:
            # 自定义初始化
            other_dao = OtherVo({}, self.config_data)
            engine.init_main_window(self, other_dao)
        except Exception as err:
            log.error(f"初始化主窗口失败，error: {err}")
            log.warning(traceback.format_exc())

        # socket接口服务
        self.socket_service = SocketThread(self)
        self.socket_service.daemon = True  # 设置为守护线程
        self.socket_service.start()

        # 定时任务框架
        self.scheduler = ScheduleThread(self)
        self.scheduler.daemon = True  # 设置为守护线程
        self.scheduler.start()

        # 后台任务
        self.task_thread = TaskThread(self)
        self.task_thread.daemon = True  # 设置为守护线程
        self.task_thread.start()

        self.can_custom_error_code = False
        self.init_error_code_map()

        self.error_code_widget = ErrorCodeWidget(self)

        engine.main_window = self

        http_server_run = self.config_data.get('app_setting', {}).get('http_server_run', False)

        if http_server_run:
            self.http_server = HttpServerQThread()
            self.http_server.daemon = True  # 设置为守护线程
            self.http_server.start()

        # 布局与显示
        self.center()
        self.show()

    def set_cron_setting(self, interval_cron: bool, interval_time: int):
        """
        设置定制化的定时触发
        :param interval_cron: 是否定时触发
        :param interval_time: 多久触发一次，单位秒
        :return:
        """
        self.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        self.config_data["app_setting"]["custom_interval_time"] = int(interval_time)

    def get_lang(self) -> str:
        """
        获取语言
        :return: ZH/EN
        """
        return self.config_data.get("app_setting", {}).get("setting_lang", "ZH")

    def lang_is_en(self):
        """
        语言是英语
        :return: True、False
        """
        return self.get_lang() == EN_KEY

    def btn_path_select_on_click(self, pure_key: str):
        ret = QFileDialog.getExistingDirectory(self, "选择文件夹", "~")
        path_value_tmp = getattr(self, f"path_value_{pure_key}")
        path_value_tmp.setText(str(ret))

        config_data = xutil.FileUtil.load_config_file()
        path_map = config_data.get("path", {})

        path_item = path_map.get(pure_key, {})
        path_item["value"] = path_value_tmp.text()
        path_map[pure_key] = path_item

        # self.config_data["path"] = path_map
        # self.save_config_data_to_file()

    def center(self):
        """
        屏幕居中
        :return:
        """
        # 获取屏幕坐标系
        screen = QDesktopWidget().screenGeometry()

        # 获取窗口坐标系
        size = self.geometry()  # noqa

        new_left = (screen.width() - size.width()) / 2
        new_top = (screen.height() - size.height()) / 2

        self.move(int(new_left), int(new_top))  # noqa

    def update_slot(self):
        """
        连接各种槽
        :return:
        """
        self.btn_save_setting.clicked.connect(self.save_app_setting_on_btn)
        self.btn_reset_data.clicked.connect(self.reset_data_on_click)
        self.btn_clear_log_info.clicked.connect(self.clear_log_info_on_click)
        self.btn_detail_log.clicked.connect(self.detail_log_on_click)
        self.btn_cron_clear.clicked.connect(self.cron_clear_dialog_show)

        self.action_other_setting.triggered.connect(self.other_setting_views.show_window)
        self.action_param_setting.triggered.connect(self.other_param_views.param_dialog_show)
        self.action_about.triggered.connect(self.action_about_on_click)
        self.action_auto_start.triggered.connect(self.auto_start_on_menu)
        self.action_test_send.triggered.connect(self.fake_send_dialog_show)
        self.action_create_desktop_icon.triggered.connect(self.create_desktop_on_menu)
        # self.action_send_email.triggered.connect(self.send_email_on_menu)
        self.action_send_email.triggered.connect(self.non_develop_message_on_click)
        self.action_custom_mes_code.triggered.connect(self.custom_mes_code_on_menu)  # 自定义Mes不良代码

        self.action_common_config.triggered.connect(self.common_config_dialog_show)

        self.action_login.triggered.connect(self.login_on_menu)

        self.upload_log.connect(self.update_log_on_emit)  # noqa

        self.actionUsePassWord.triggered.connect(self.use_password_on_menu)

        # for k in engine.combo:
        #     combo_key = f"combo_{k}"
        #     getattr(self, combo_key).currentIndexChanged.connect(functools.partial(self.combo_index_changed, combo_key))

        for k in engine.button:
            btn_key = f"btn_{k}"
            getattr(self, btn_key).clicked.connect(functools.partial(self.custom_btn_on_click, btn_key))

        for pure_k in engine.path:
            path_btn_key = f"path_btn_{pure_k}"

            getattr(self, path_btn_key).clicked.connect(functools.partial(self.btn_path_select_on_click, pure_k))

    def auto_login(self):
        """
        是否自动登录
        :return:
        """
        cache_data = xutil.CacheUtil.get_cache_data()

        login_auto_login = cache_data.get("login_auto_login")
        login_username = cache_data.get("login_username")
        login_password = cache_data.get("login_password")

        if login_auto_login:
            try:
                other_dao = OtherVo({
                    "login_username": login_username,
                    "login_password": login_password
                }, self.config_data)

                custom_ret = engine.login_mes(other_dao, "")
                if not custom_ret:
                    self.log_info("未开发登录功能，无需登录")
                    return

                else:
                    if not custom_ret.get("result"):
                        self.log_info(f"登录失败，error：{custom_ret.get('string')}", False)
                        return

                    else:
                        self.log_info(f"用户[{login_username}]自动登录成功！")
                        # self.statusbar.showMessage(login_username, 0)

            except Exception as e:
                self.log_info(f"其他异常，自动登录失败，error：{e}", False)

    def send_email_on_menu(self):
        """
        发送邮件
        :return:
        """
        # 异步发送
        xrequest.send_backed_task(b'SEND_EMAIL')

    def login_on_menu(self):
        """
        菜单栏触发登录按钮
        :return:
        """
        login_remember_password = xutil.CacheUtil.get("login_remember_password", True)
        login_auto_login = xutil.CacheUtil.get("login_auto_login", False)
        login_username = xutil.CacheUtil.get("login_username", "")
        login_password = xutil.CacheUtil.get("login_password", "")

        self.login_views.check_box_remember_password.setChecked(login_remember_password)
        self.login_views.check_auto_login.setChecked(login_auto_login)
        self.login_views.line_username.setText(login_username)
        self.login_views.line_password.setText(login_password)

        login_ret = self.login_views.exec_()
        if not login_ret:
            return

    def detail_log_on_click(self):
        """
        详细日志
        :return:
        """
        if sys.platform == "linux":
            os.system(f'xdg-open {xconfig.pymes_log_file}')

            self.log_info("~/.aoi/log/pymes.log 文件已打开!")

    def auto_start_on_menu(self):
        """
        设置开机自启动
        :return:
        """
        autostart_file = f"{xconfig.home_dir}/.config/autostart/mesconfig_python.desktop"
        # if os.path.exists(autostart_file):
        #     self.log_info(f"已经设置了开机自启动，无需重复设置！")
        #     return

        desktop_file1 = f"{xconfig.home_dir}/桌面"
        desktop_file2 = f"{xconfig.home_dir}/Desktop"

        if os.path.exists(desktop_file2):
            desktop_dir = desktop_file2
        elif os.path.exists(desktop_file1):
            desktop_dir = desktop_file1
        else:
            self.log_info("找不到桌面，创建桌面快捷方式失败", False)
            return

        desk_file = f"{desktop_dir}/mesconfig_python.desktop"
        # if not os.path.exists(desk_file):
        self.create_desktop_on_menu()

        # 复制到 /home/<USER>/.config/autostart
        xutil.FileUtil.copy_file(desk_file, autostart_file)
        self.log_info("设置开机自启动成功！")

    def create_desktop_on_menu(self):
        """
        创建桌面快捷方式
        :return:
        """
        desktop_file1 = f"{xconfig.home_dir}/桌面"
        desktop_file2 = f"{xconfig.home_dir}/Desktop"

        if os.path.exists(desktop_file2):
            desktop_dir = desktop_file2
        elif os.path.exists(desktop_file1):
            desktop_dir = desktop_file1
        else:
            self.log_info("找不到桌面，创建桌面快捷方式失败", False)
            return
        desk_file = f"{desktop_dir}/mesconfig_python.desktop"

        # if os.path.exists(desk_file):
        #     # self.log_info("桌面快捷方式已经存在，无需重复创建", False)
        #     return

        # 1. 在当前路径下创建个MES_STATIC文件夹
        cwd = os.getcwd()
        static_dir = f"{cwd}/MES_STATIC"
        if not os.path.exists(static_dir):
            os.makedirs(static_dir)

        icon_file = f"{static_dir}/mes_icon.png"
        if not os.path.exists(icon_file):
            xutil.OtherUtil.base64_to_file(icon_file, xicon.mes_icon_base64)

        desktop_content = f"""[Desktop Entry]
Name=pymesApp
Icon={icon_file}
Exec={cwd}/mesconfig
Path={cwd}
Comment=CVTE AOI MES OL Application
Categories=Application
Terminal=false
Type=Application"""

        xutil.FileUtil.write_content_to_file_atomic(desk_file, desktop_content)
        os.chmod(desk_file, stat.S_IRWXU)
        self.log_info(f"创建桌面快捷方式成功！")

    @staticmethod
    def get_commit_info():
        """
        获取提示信息
        :return:
        """
        app_info_path = f"{os.getcwd()}/static/app_info.json"

        commit_id = ""
        update_time = ""

        if os.path.exists(app_info_path):
            app_info = xutil.FileUtil.load_json_file(app_info_path)

            commit_id = app_info.get("commit_id")
            update_time = app_info.get("update_time")

        return commit_id, update_time

    def action_about_on_click(self):
        """
        关于按钮
        :return:
        """
        x_version = engine.version
        release = x_version.get("release", "")
        device = x_version.get("device", "")
        author = x_version.get("author", "")
        feat = x_version.get("feature", [])

        commit_id, commit_time = self.get_commit_info()
        #
        #         commit_info_str = ""
        #         if commit_id:
        #             commit_info_str = f"""Built on {commit_time}
        # From revision: {commit_id}
        #
        # """
        #
        #         content = f"""{commit_info_str}作者：{author}
        # 适用机型：{device}
        # 功能：{'，'.join(feat)}
        # 发布历史：{release}"""
        #         QMessageBox.about(self, "关于", content)

        built_time = f"Built on {commit_time}"
        built_id = f"From revision: {commit_id}"
        built_content = f"""作者：{author}
适用机型：{device}
功能：{'，'.join(feat)}
发布历史：{release}"""

        AboutMeDialog(built_time, built_id, built_content, self).exec_()

    def non_develop_message_on_click(self):
        """
        未开发弹窗
        :return:
        """
        msg = """该功能正在开发中！"""
        QMessageBox.about(self, "提示", msg)  # noqa

    def log_info(self, msg: str, status=True, send_to_repair=False, pop_prompt: bool = True):
        """
        打印简要日志
        :param msg:
        :param status: 日志状态（True，Info） （False，Error）
        :param send_to_repair: 是否发送到维修站
        :param pop_prompt: 是否弹窗提示
        :return:
        """
        if status:
            log.info(msg)
        else:
            log.warning(msg)

        if self.config_data is None:
            return

        # 发送结果给维修站
        app_setting = self.config_data.get("app_setting", {})

        if send_to_repair:
            is_send_to_repair = app_setting.get("send_info_to_repair")
            repair_ip = app_setting.get("repair_ip")
            if is_send_to_repair:
                if not status:
                    log.info(f"发送结果给维修站...")
                    xrequest.post_msg_to_repair(repair_ip, status, msg)

        time_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        origin_txt = msg

        if status:
            # msg = f"<span style='color:black'>{time_now} {msg}</span>"
            msg = f"<span style='color:green'>{time_now} {msg}</span>"
        else:
            msg = f"<span style='color:red'>{time_now} {msg}</span>"

        if not pop_prompt:
            status = True

        self.upload_log.emit(msg, origin_txt, status)  # noqa
        # self.log_edit.appendHtml(msg)

    def update_log_on_emit(self, txt, origin_txt, status):
        self.log_edit.appendHtml(txt)

        if not status:
            send_error_info = self.config_data.get("app_setting", {}).get("send_error_info")
            if send_error_info:
                if self.current_message_box:
                    self.current_message_box.close()

                self.current_message_box = QMessageBox()
                self.current_message_box.setIcon(QMessageBox.Warning)  # 设置为警告图标
                msg = "处理异常"
                if self.lang_is_en():
                    msg = "Warning"
                self.current_message_box.setWindowTitle(msg)
                self.current_message_box.setText(origin_txt)
                self.current_message_box.show()

    def clear_log_info_on_click(self):
        """
        清除日志
        :return:
        """
        self.log_edit.setPlainText("")

    def reset_data_on_click(self):
        """
        统计面板数据清零
        :return:
        """
        self.send_total_number.setText("0")
        self.send_err_number.setText("0")
        self.check_total_number.setText("0")
        self.check_err_number.setText("0")
        self.status_total_number.setText("0")
        self.status_err_number.setText("0")
        self.get_sn_number.setText("0")
        self.get_sn_err_number.setText("0")

        s1 = "计数已清零"
        if self.get_lang() == EN_KEY:
            s1 = "Count cleared"
        self.log_info(s1)

    def custom_btn_on_click(self, btn_key):
        """
        定制按钮点击
        :param btn_key: 如：btn_get_order,  btn_login_btn
        :return:
        """
        pure_key = btn_key.replace("btn_", "")
        btn_name = engine.button.get(pure_key, {}).get('ui_name')
        self.log_info(f"{btn_name}中...")

        try:
            btn_vo = {
                "pure_key": pure_key
            }
            btn_dao = ButtonVo(btn_vo, self.config_data)
            btn_ret = engine.custom_button_clicked(btn_dao, self)

            if not btn_ret:
                self.log_info(f"功能未开发！")
                return

            if not btn_ret.get("result"):
                err_msg2 = f"{btn_name}触发失败，error：{btn_ret.get('string')}"

                if self.lang_is_en():
                    err_msg2 = f"Error: {btn_ret.get('string')}"

                self.log_info(err_msg2, False, pop_prompt=False)
                return
            else:
                self.log_info(f"{btn_name}成功！")

        except Exception as err:
            err_msg3 = f"其他异常，{btn_name}出错，error: {err}"

            if self.lang_is_en():
                err_msg3 = f"Other Exception, Error: {err}"

            self.log_info(err_msg3, False)
            log.warning(traceback.format_exc())

    # def combo_index_changed(self, combo_key):
    #     """
    #     combo组件变化
    #     :param combo_key 如：combo_line, combo_workshop
    #     :return:
    #     """
    #     combo_tmp = getattr(self, combo_key)
    #     combo_current_text = combo_tmp.currentText()
    #
    #     try:
    #         combo_vo = {
    #             "combo_key": combo_key.replace("combo_", ""),
    #             "combo_value": combo_current_text,
    #         }
    #
    #         if combo_current_text:
    #             config_data = xutil.FileUtil.load_config_file()
    #             combo_file = config_data.get("combo", {})
    #             combo_obj = combo_file.get(combo_vo["combo_key"], {})
    #             combo_obj["value"] = combo_current_text
    #             combo_file[combo_vo["combo_key"]] = combo_obj
    #
    #             self.config_data["combo"] = combo_file
    #             self.save_config_data_to_file()
    #
    #             combo_ui_name = combo_obj.get("ui_name")
    #
    #             self.log_info(f"[{combo_ui_name}]已切换到[{combo_current_text}]")
    #
    #         combo_ret = engine.combo_index_changed(ComboDao(combo_vo, self.config_data), "")
    #         if not combo_ret:
    #             return
    #
    #         if not combo_ret.get("result"):
    #             self.log_info(f"下拉定制功能触发失败，error：{combo_ret.get('string')}", False)
    #             return
    #         else:
    #             pass
    #
    #     except Exception as e:
    #         self.log_info(f"其他异常，下拉定制功能出错，error: {e}", False)
    #         log.warning(traceback.format_exc())

    def load_param_key_to_ui(self):
        """
        加载[参数key] 并设置配置文件保存的[参数value]到界面上
        :return:
        """
        lang = self.get_lang()

        # 加载选中的combo
        config_data = xutil.FileUtil.load_config_file()
        combo_file = config_data.get("combo", {})
        form_file = config_data.get("form", {})
        path_file = config_data.get("path", {})

        # line_index = [7, 10, 13]
        line_index = engine.line_index

        def add_line_to_layout():
            """
            添加一条下划线到界面布局里
            """
            line = QFrame(self)
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            self.setting_param_layout.addRow(line)

        comp_ix = 0  # 组件索引

        # 添加定制的保存路径到界面
        for k, v in engine.path.items():
            path_btn_key = f"path_btn_{k}"
            path_value_key = f"path_value_{k}"

            path_ui_name = v.get("ui_name")

            if lang == EN_KEY:
                ui_name_en = v.get("ui_name_en")
                if ui_name_en:
                    path_ui_name = ui_name_en

            path_btn_tmp = QPushButton(self.scrollAreaWidgetContents)
            path_btn_tmp.setText(path_ui_name)

            setattr(self, path_btn_key, path_btn_tmp)
            path_value_tmp = QLineEdit(self)
            path_value_tmp.setEnabled(False)

            path_value_value = path_file.get(k, {}).get("value", "")
            if not path_value_value:
                path_value_value = v.get("value")

            path_value_tmp.setText(path_value_value)

            setattr(self, path_value_key, path_value_tmp)

            self.setting_param_layout.addRow(path_btn_tmp, path_value_tmp)

            comp_ix += 1

            if comp_ix in line_index:
                add_line_to_layout()

        # 添加定制的按钮到底部
        btn_key_list = list(engine.button.keys())
        # 第一排按钮
        btn_count = len(btn_key_list)
        if btn_count != 0:
            n1 = btn_count // 2
            n2 = btn_count % 2

            split_index = n1 + n2

            if btn_count <= 5:
                split_index = 5

            for k in btn_key_list[:split_index]:
                v = engine.button.get(k, {})
                btn_ui_name = v.get("ui_name")
                is_customized = v.get("customized", False)

                if lang == EN_KEY:
                    ui_name_en = v.get("ui_name_en")
                    if ui_name_en:
                        btn_ui_name = ui_name_en

                if is_customized:
                    # 如果是自定义按钮，传递给engine进行按钮生成
                    btn_tmp = engine.create_custom_button(self.btn_frame)
                else:
                    btn_tmp = QPushButton(self.btn_frame)
                    btn_tmp.setText(btn_ui_name)
                self.btn_layout1.addWidget(btn_tmp)

                btn_key = f"btn_{k}"
                setattr(self, btn_key, btn_tmp)

            for k in btn_key_list[split_index:]:
                v = engine.button.get(k, {})
                btn_ui_name = v.get("ui_name")
                is_customized = v.get("customized", False)

                if lang == EN_KEY:
                    ui_name_en = v.get("ui_name_en")
                    if ui_name_en:
                        btn_ui_name = ui_name_en

                if is_customized:
                    # 如果是自定义按钮，传递给engine进行按钮生成
                    btn_tmp = engine.create_custom_button(self.btn_frame)
                else:
                    btn_tmp = QPushButton(self.btn_frame)
                    btn_tmp.setText(btn_ui_name)
                self.btn_layout2.addWidget(btn_tmp)

                btn_key = f"btn_{k}"
                setattr(self, btn_key, btn_tmp)
            #
            #
            # for k, v in engine.button.items():
            #     btn_ui_name = v.get("ui_name")
            #     btn_tmp = QPushButton(self.btn_frame)
            #     btn_tmp.setText(btn_ui_name)
            #     self.btn_layout1.addWidget(btn_tmp)
            #
            #     btn_key = f"btn_{k}"
            #     setattr(self, btn_key, btn_tmp)

        # 加载combo参数
        for ix, (k, v) in enumerate(engine.combo.items()):
            param_key = v.get("ui_name")

            if lang == EN_KEY:
                ui_name_en = v.get("ui_name_en")
                if ui_name_en:
                    param_key = ui_name_en

            combo_item = v.get("item")

            label = QLabel(self)
            label.setText(param_key)

            # combo_type = v.get("type", "standard")
            # if combo_type == xcons.SEARCH_FLAG:
            #     combo = SearchCustomCombo(self, k, param_key, self.scrollAreaWidgetContents)
            # else:
            combo = CustomCombo(self, k, param_key, self.scrollAreaWidgetContents)

            select_combo = combo_file.get(k, {}).get("value")
            file_item = combo_file.get(k, {}).get("item", [])

            for i in file_item:
                if i not in combo_item:
                    combo_item.append(i)

            if select_combo and select_combo not in combo_item:
                combo_item.append(select_combo)

            for combo_value in combo_item:
                combo.addItem(combo_value)

            if select_combo is None:
                select_combo = v.get("value")

            if select_combo in combo_item:
                combo.setCurrentIndex(combo_item.index(select_combo))

            combo_key = f"combo_{k}"
            setattr(self, combo_key, combo)

            self.setting_param_layout.addRow(label, combo)

            comp_ix += 1

            if comp_ix in line_index:
                add_line_to_layout()

        # 1. 加载form参数
        for k, v in engine.form.items():
            label = QLabel(self)

            ui_name_form = v.get("ui_name")

            if lang == EN_KEY:
                ui_name_en = v.get("ui_name_en")
                if ui_name_en:
                    ui_name_form = ui_name_en

            label.setText(ui_name_form)
            edit_key = f"form_{k}"

            tmp_edit = QLineEdit(self)

            if v.get("is_int"):
                tmp_edit.setValidator(QIntValidator())

            if v.get("is_port"):
                tmp_edit.setValidator(QIntValidator(0, 65535))

            if v.get("is_ip"):
                ip_regex = QRegExp(
                    r'^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
                tmp_edit.setValidator(QRegExpValidator(ip_regex))

            if v.get('is_read_only'):  # 不可编辑
                tmp_edit.setReadOnly(True)
                tmp_edit.setStyleSheet("background-color: #f0f0f0;")  # 设置灰色背景

            form_value = form_file.get(k, {}).get("value")
            if form_value is None:
                form_value = v.get("value")

            tmp_edit.setText(form_value)

            if k in engine.password_style:
                tmp_edit.setEchoMode(QLineEdit.Password)  # noqa

            setattr(self, edit_key, tmp_edit)

            self.setting_param_layout.addRow(label, tmp_edit)
            comp_ix += 1

            if comp_ix in line_index:
                add_line_to_layout()

    def reload_param_key_to_ui(self):
        """
        重新加载配置文件保存的[参数value]到界面上
        :return:
        """
        # 加载选中的combo
        config_data = xutil.FileUtil.load_config_file()
        combo_file = config_data.get("combo", {})
        form_file = config_data.get("form", {})
        path_file = config_data.get("path", {})

        # 添加定制的保存路径到界面
        for k, v in engine.path.items():
            path_value_key = f"path_value_{k}"
            path_obj = getattr(self, path_value_key)

            path_value_value = path_file.get(k, {}).get("value", "")
            if not path_value_value:
                path_value_value = v.get("value")

            path_obj.setText(path_value_value)

        # 加载combo参数
        for k, v in engine.combo.items():
            combo_item = v.get("item")

            combo_key = f"combo_{k}"
            combo_obj = getattr(self, combo_key)

            select_combo = combo_file.get(k, {}).get("value")
            file_item = combo_file.get(k, {}).get("item", [])

            for i in file_item:
                if i not in combo_item:
                    combo_item.append(i)

            if select_combo and select_combo not in combo_item:
                combo_item.append(select_combo)

            if select_combo is None:
                select_combo = v.get("value")

            if select_combo in combo_item:
                combo_obj.setCurrentIndex(combo_item.index(select_combo))

        # 1. 加载form参数
        for k, v in engine.form.items():
            edit_key = f"form_{k}"

            form_obj = getattr(self, edit_key)

            form_value = form_file.get(k, {}).get("value")
            if form_value is None:
                form_value = v.get("value")

            form_obj.setText(form_value)

    def load_config(self):
        """
        加载配置文件, 并加载到程序的内存中： main_window.config_data
        :return:
        """
        # 1. 加载配置文件
        config_data = xutil.FileUtil.load_config_file()
        form_file = config_data.get("form", {})
        combo_file = config_data.get("combo", {})
        path_file = config_data.get("path", {})
        other_form_file = config_data.get("other_form", {})
        other_combo_file = config_data.get("other_combo", {})
        other_path_file = config_data.get("other_path", {})

        new_other_path_values = {}
        for k, default_v in engine.other_path.items():
            new_other_path_values[k] = other_path_file.get(k, default_v)

        new_path_values = {}
        for k, default_v in engine.path.items():
            new_path_values[k] = path_file.get(k, default_v)

        new_form_values = {}

        # 2. 更新界面参数值
        for k, default_v in engine.form.items():
            new_form_values[k] = form_file.get(k, default_v)

        new_other_form_values = {}
        for k, default_v in engine.other_form.items():
            new_other_form_values[k] = other_form_file.get(k, default_v)

        # 更新combo
        new_combo_values = {}

        for k, default_v in engine.combo.items():
            combo_obj = combo_file.get(k, default_v)

            if combo_obj:
                combo_select = combo_obj.get("value")
                items = combo_obj.get("item", [])

                if combo_select and combo_select not in items:
                    items.append(combo_select)

            new_combo_values[k] = combo_obj

        new_other_combo_values = {}
        for k, default_v in engine.other_combo.items():
            other_combo_obj = other_combo_file.get(k, default_v)

            if other_combo_obj:
                other_combo_select = other_combo_obj.get("value")
                other_combo_items = other_combo_obj.get("item", [])
                if other_combo_select and other_combo_select not in other_combo_items:
                    other_combo_items.append(other_combo_select)

            new_other_combo_values[k] = other_combo_obj

        # 将代码里的设置更新到配置文件
        app_setting = config_data.get("app_setting", {})
        for k, default_value in engine.app_setting.items():
            if k not in app_setting:
                app_setting[k] = default_value

        send_info_to_repair = app_setting.get("send_info_to_repair")
        repair_ip = app_setting.get("repair_ip")

        config_data["combo"] = new_combo_values
        config_data["form"] = new_form_values
        config_data["path"] = new_path_values
        config_data["other_path"] = new_other_path_values
        config_data["other_form"] = new_other_form_values
        config_data["other_combo"] = new_other_combo_values

        config_data["app_setting"] = app_setting

        common_config = config_data.get("common_config", {})
        for k, v in engine.common_config.items():
            if k not in common_config:
                common_config[k] = v

        config_data["common_config"] = common_config

        # 首次加载配置文件到内存中， 之后的操作都是操作修改内存中的配置，再保存到配置文件
        # ps: 程序优先用内存中的配置文件，注意：内存中的配置文件要和硬盘上的配置文件同步
        self.config_data = config_data
        self.save_config_data_to_file()

        # 更新 MES.ini
        if not os.path.exists(xconfig.mes_ini_file):
            xutil.FileUtil.write_content_to_file_atomic(xconfig.mes_ini_file, xconfig.mes_content, "gbk")

        with open(xconfig.mes_ini_file, "r+", encoding="gbk") as f:
            mes_ini_content = f.read()
            if "SendTogether" not in mes_ini_content:
                xutil.FileUtil.write_content_to_file_atomic(xconfig.mes_ini_file, xconfig.mes_content, "gbk")

        xutil.OtherUtil.update_config_by_key("SendTogether", "true" if app_setting.get("merge_send") else "false")
        xutil.OtherUtil.update_config_by_key("WorkStationIp", repair_ip)
        xutil.OtherUtil.update_config_by_key("SendResultToWorkStation", "true" if send_info_to_repair else "false")

    def save_app_setting_on_btn(self):
        """
        保存主界面上的[参数value]设置到配置文件
        1. 参数配置
        2. 发送设置
        :return:
        """
        if self.is_use_password():
            login_dialog = LoginDialog("", "登录账号")

            result = login_dialog.exec_()

            if result != QDialog.Accepted:
                self.reload_param_key_to_ui()
                log.warning(f"参数未保存！！！")
                return

        # 界面上的参数
        form_param = engine.form.keys()
        comb_param = engine.combo.keys()

        form = self.config_data.get("form")
        combo = self.config_data.get("combo")
        path_map = self.config_data.get("path")

        for k in form_param:
            edit_key = f"form_{k}"

            tmp_edit = getattr(self, edit_key)
            ui_v = tmp_edit.text()  # ui上的值

            form[k]["value"] = ui_v

        for k in comb_param:
            combo_key = f"combo_{k}"

            tmp_combo = getattr(self, combo_key)

            tmp_list = []
            for i in range(tmp_combo.count()):
                tmp_list.append(tmp_combo.itemText(i))

            combo_item = combo.get(k, {})
            combo_item["item"] = tmp_list
            combo_item["value"] = tmp_combo.currentText()
            combo[k] = combo_item

        for k in engine.path.keys():
            path_value_key = f"path_value_{k}"
            tmp_edit = getattr(self, path_value_key)

            path_item = path_map.get(k, {})
            path_item["value"] = tmp_edit.text()
            path_map[k] = path_item

        self.config_data["form"] = form
        self.config_data["combo"] = combo
        self.config_data["path"] = path_map
        self.save_config_data_to_file()

        l1 = f"保存界面参数成功！"
        if self.get_lang() == EN_KEY:
            l1 = f"save successful!"

        self.log_info(l1)
        # QMessageBox.information(self, "保存配置", "保存成功", QMessageBox.Yes, QMessageBox.Yes)  # noqa

        try:
            engine.save_btn_on_window(self)
        except Exception as err:
            log.error(f"保存按钮自定义功能触发失败，error：{err}")
            log.warning(traceback.format_exc())

    def other_ui_setting(self):
        """
        组件的一些其他设置
        :return:
        """
        # log框不可以编辑
        self.log_edit.setReadOnly(True)

        version_release = engine.get_release()

        self.setWindowTitle(f"Mes System {version_release}")

        # 初始化设置密码的设置
        self.actionUsePassWord.setChecked(self.is_use_password())

    def close_event_by_tray_app(self):
        """
        通过右上角点击鼠标右键的退出事件
        """
        log.warning("鼠标右键关闭事件触发...")
        title1 = "退出"
        message = "是否退出程序，退出程序将导致Mes无法正常连接！"
        self.showNormal()

        if self.lang_is_en():
            title1 = "Exit"
            message = "Are you sure to exit the program, exit the program will cause Mes to be disconnected!"

        reply = QMessageBox.question(self, title1, message,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        # 判断返回值，如果点击的是Yes按钮，我们就关闭组件和应用，否则就忽略关闭事件
        if reply == QMessageBox.Yes:
            xrequest.close_mes_socket_server(engine.mes_config_port)
            # 如果想在退出时执行一些事情，可以在on_exit里添加
            engine.on_exit()

            self.socket_service.quit()
            self.socket_service.wait()
            self.tray_icon.hide()
            QApplication.quit()
            log.warning(f"Mes配置器已退出")

    def closeEvent(self, event: QtGui.QCloseEvent) -> None:
        """
        拦截关闭按钮
        https://stackoverflow.com/questions/68603658/how-to-terminate-a-uvicorn-fastapi-application-cleanly-with-workers-2-when
        :param event:
        :return:
        """
        event.ignore()
        self.hide()
        # self.tray_icon.showMessage(
        #     "提示",
        #     "窗口已最小化到系统托盘。",
        #     QSystemTrayIcon.Information,
        #     3000
        # )

    def save_config_data_to_file(self):
        """
        保存内存中的配置到硬盘
        ！！！注意，一定要先保存到内存[self.config_data]，再保存到硬盘
        :return:
        """
        xutil.FileUtil.save_config_file(self.config_data)

    def other_setting_dialog_show(self):
        """
        其他设置  菜单
        :return:
        """
        pass
        # app_setting = self.config_data["app_setting"]
        # self.other_setting_views.setting_filter_repeat_send.setChecked(app_setting["filter_repeat_send"])
        # self.other_setting_views.setting_merge_send.setChecked(app_setting["merge_send"])
        # self.other_setting_views.setting_send_error_info.setChecked(app_setting["send_error_info"])
        # self.other_setting_views.setting_send_info_to_repair.setChecked(app_setting["send_info_to_repair"])
        # self.other_setting_views.edit_repair_ip.setText(app_setting["repair_ip"])
        # ret = self.other_setting_views.exec_()
        #
        # if ret == 1:
        #     # ok button
        #     self.other_setting_views.save_app_setting_on_btn()

    def other_param_setting_dialog_show(self):
        """
        展示其他设置
        :return:
        """

        pass

    def fake_send_dialog_show(self):
        """
        展示模拟发送窗口
        """
        self.clearFocus()
        self.fake_send_views.show()

    def cron_clear_dialog_show(self):
        """
        定时清除设置
        :return:
        """
        # 从配置文件获取保存的配置
        app_config = self.config_data["app_setting"]

        self.cron_setting_views.check_is_cron_clear.setChecked(app_config["is_cron_clear"])
        self.cron_setting_views.check_fixed_status_1.setChecked(app_config["fixed_status_1"])
        self.cron_setting_views.check_fixed_status_2.setChecked(app_config["fixed_status_2"])

        # 修改时间
        hour1, minute1 = app_config["fixed_time_1"].split(":")
        hour2, minute2 = app_config["fixed_time_2"].split(":")

        self.cron_setting_views.edit_fixed_time_1.setTime(QTime(int(hour1), int(minute1)))
        self.cron_setting_views.edit_fixed_time_2.setTime(QTime(int(hour2), int(minute2)))

        self.cron_setting_views.radio_fixed_time_clear.setChecked(app_config["fixed_time_clear"])
        self.cron_setting_views.radio_interval_time_clear.setChecked(app_config["interval_time_clear"])
        self.cron_setting_views.spin_interval_time.setValue(app_config["interval_time"])

        # 更新设置
        ret = self.cron_setting_views.exec_()
        if ret == 1:
            self.cron_setting_views.save_app_setting_on_btn()

    @staticmethod
    def ensure_env_ok():
        """
        自动初始化环境
        """
        if not os.path.exists(xconfig.leichen_dir):
            os.makedirs(xconfig.leichen_dir)
            log.info(f"自动创建[{xconfig.leichen_dir}]目录")

        if not os.path.exists(xconfig.log_dir):
            os.makedirs(xconfig.log_dir)
            log.info(f"自动创建[{xconfig.log_dir}]目录")

        if not os.path.exists(xconfig.mes_cache_data):
            xutil.CacheUtil.save_cache_data({})

    def common_config_dialog_show(self):
        self.common_config_views.exec_dialog()
        # print("展示通用配置")
        # ret = self.common_config_views.exec_()
        # print("ret", ret)
        # print(json.dumps(self.config_data, ensure_ascii=False, indent=4))

    def custom_mes_code_on_menu(self):
        self.error_code_widget.show_widget()

    def init_error_code_map(self):
        """
        初始化不良代码
        :return:
        """
        is_has_error_map = hasattr(engine, "ERROR_MAP")

        if is_has_error_map:
            self.can_custom_error_code = True

            log.info(f"set error code map!")

            if os.path.exists(xconfig.mes_error_code_map):
                if not xutil.FileUtil.is_valid_json_file(xconfig.mes_error_code_map):
                    # 文件异常
                    error_data_on_setting = getattr(engine, "ERROR_MAP")

                    xutil.FileUtil.dump_json_to_file_pro(
                        xconfig.mes_error_code_map,
                        error_data_on_setting
                    )

            if not data_engine.exist_error_code_map():
                error_data_on_setting = getattr(engine, "ERROR_MAP")
                data_engine.set_all_error_code_map(error_data_on_setting)

            else:
                init_error_map = getattr(engine, "ERROR_MAP")
                file_error_map = data_engine.get_all_error_code_map()

                need_to_update_to_file = False
                for device_id, error_data in init_error_map.items():
                    if device_id not in file_error_map:
                        file_error_map[device_id] = {}

                    file_error_data = file_error_map.get(device_id, {})

                    for _error_code, _error_info in error_data.items():
                        if _error_code not in file_error_data:
                            file_error_map[device_id][_error_code] = _error_info
                            need_to_update_to_file = True

                if need_to_update_to_file:
                    data_engine.set_all_error_code_map(file_error_map)
                    log.info(f"更新不良代码映射！")

        else:
            log.info(f"not had error code map!")
            self.can_custom_error_code = False

    def custom_cron_function(self):
        """
        定制的定时任务
        :return:
        """
        other_dao = OtherVo({}, self.config_data)

        try:
            log.info("--------------定时任务触发-------------")
            engine.custom_cron_function(other_dao, self, "")
        except Exception as err:
            err_msg1 = f"定时任务触发失败，error：{err}"
            if self.lang_is_en():
                err_msg1 = f"Error: {err}"

            log.warning(err_msg1)

    def custom_cron_function2(self):
        """
        定制的定时任务
        :return:
        """
        other_dao = OtherVo({}, self.config_data)

        try:
            log.info("--------------定时任务2触发-------------")
            engine.custom_cron_function2(other_dao, self, "")
        except Exception as err:
            err_msg1 = f"定时任务2触发失败，error：{err}"
            if self.lang_is_en():
                err_msg1 = f"Error: {err}"

            log.warning(err_msg1)

    def custom_cron_function3(self):
        """
        定制的定时任务3，用于固定某个时间点发送，比如一天或者一周或者一个月某天某时某分发送等
        :return:
        """
        other_dao = OtherVo({}, self.config_data)

        try:
            log.info("--------------定时任务3触发-------------")
            engine.custom_cron_function3(other_dao, self, "")
        except Exception as err:
            err_msg1 = f"定时任务3触发失败，error：{err}"
            if self.lang_is_en():
                err_msg1 = f"Error: {err}"

            log.warning(err_msg1)

    def use_password_on_menu(self, checked):
        """
        是否使用密码
        :param checked:
        :return:
        """
        login_dialog = LoginDialog("", "登录账号")

        result = login_dialog.exec_()

        if result != QDialog.Accepted:
            # 将设置改回去
            is_check = self.actionUsePassWord.isChecked()
            self.actionUsePassWord.setChecked(not is_check)
            log.warning(f"参数未保存！！！")
            return

        self.config_data["app_setting"]["use_password"] = checked
        self.save_config_data_to_file()

    def is_use_password(self) -> bool:
        """
        获取是否使用密码的设置
        :return:
        """
        return self.config_data.get('app_setting', {}).get('use_password', False)
