# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t1_parse_ret.py
# Time       ：2025/4/12 上午10:33
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import unittest

from services.custom.dongguanaohai.ah_module import parse_ret

ret = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <BarcodeRelationResponse xmlns="http://tempuri.org/">
            <BarcodeRelationResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>false</a:string>
                <a:string>条码:A001不存在注册信息,请确认!</a:string>
            </BarcodeRelationResult>
        </BarcodeRelationResponse>
    </s:Body>
</s:Envelope>"""

ret2 = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CheckAndwriteResultResponse xmlns="http://tempuri.org/">
            <CheckAndwriteResultResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>true</a:string>
                <a:string>验证失败:当前站假如是首站需要配置工单流程绑定!传入参数:A001;{"comp01": "PASS"}线体ID:29</a:string>
            </CheckAndwriteResultResult>
        </CheckAndwriteResultResponse>
    </s:Body>
</s:Envelope>"""


class TestParseCase(unittest.TestCase):

    def test_parse_barcode_relation(self):
        r1, r2 = parse_ret(ret)
        self.assertEqual(r1, 'false')
        self.assertIsInstance(r2, str)  # 检查r2是否为字符串类型

    def test_parse_barcode_relation2(self):
        r1, r2 = parse_ret(ret2)
        self.assertEqual(r1, 'true')
        self.assertIsInstance(r2, str)  # 检查r2是否为字符串类型


if __name__ == '__main__':
    unittest.main()
