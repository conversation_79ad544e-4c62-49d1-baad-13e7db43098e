# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api2.py
# Time       ：2023/12/8 上午9:20
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import binascii
import socket

ret = f"01-E0-03-00-05-BD-01-64-01-00-26-10-31-30-33-38-33-38-33-38-36-35-39-38-32-39-37-36-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-0C-01-00-00-01-00-03-00-00-00-0C-1E-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-0C-96-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-0C-1E-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-0C-1E-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-0C-1E-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-0C-1E-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-01-00-41-84-ED-91-01-00-41-8E-18-93-01-00-41-A9-97-8D-01-00-41-80-58-10-01-00-41-80-58-10-01-00-41-89-83-12-00-00-00-00-00-00-00-00-00-00-00-00-01-00-40-DE-B8-52-01-00-40-E0-51-EC-01-00-40-E2-3D-71-01-00-40-DE-B8-52-01-00-40-E1-EB-85-01-00-40-E0-51-EC-00-00-00-00-00-00-00-00-00-00-00-00"
ret1 = f"00-B8-01-00-05-CF-01-64-01-00-80-14-45-32-36-46-41-30-4B-4C-31-32-33-31-31-32-39-30-39-34-34-37-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-26-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"
ret2 = f"01-00-00-01-00-00-00-01-1C-05-45-32-36-46-41-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-26-10-31-30-33-38-33-38-33-38-36-35-39-38-32-39-37-36-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-80-14-45-32-36-46-41-30-4B-4C-31-32-33-31-31-32-39-30-39-34-34-37-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00"
ret_list = ret2.split("-")
print(ret_list)
print("len", len(ret_list))

ret_bytes = b''
for ix, i in enumerate(ret_list):
    d = binascii.a2b_hex(i)
    print(f"---> {ix}  origin: {i} bytes:{d} 十进制：{int(i, 16)}")

    ret_bytes += d


def send_data_to_socket_server(socket_ip: str, socket_port: int, param: bytes) -> str:
    """
    发送数据到socket服务端
    """
    print(f"param len: {len(param)}")
    print(f"请求地址: {socket_ip}:{socket_port} 请求参数:\n{param}")

    sk = socket.socket()
    sk.connect((socket_ip, socket_port))
    sk.send(param)
    ret_str = sk.recv(1024)
    print(f"接口响应: {ret_str}")
    sk.close()
    return ret_str.decode('utf-8')


def cons_shi_to_hex(string_shi: int) -> bytes:
    """
    十进制转十六进制
    :return:
    """
    hex_str = hex(string_shi)[2:]

    return binascii.a2b_hex(hex_str.zfill(2))


def cons_shi_to_hex_str(string_shi: int) -> str:
    """
    十进制转十六进制
    :return:
    """
    hex_str = hex(string_shi)[2:]

    return hex_str.zfill(2).upper()


# send_data_to_socket_server("*************", 8080, )
# send_data_to_socket_server("*************", 1024, cons_shi_to_hex(1))

print(f"{cons_shi_to_hex(100)=}")
print(f"{cons_shi_to_hex_str(182)=}")
