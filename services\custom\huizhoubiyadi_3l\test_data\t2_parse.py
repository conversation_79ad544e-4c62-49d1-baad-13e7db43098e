# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t2_parse.py
# Time       ：2025/5/19 上午9:31
# Author     ：sch
# version    ：python 3.8
# Description：
"""
from common import xutil

if __name__ == '__main__':
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">{
	"Result": "PASS",
	"Code": 0,
	"Message": "OKOK,",
	"DATA": {
		"id": 685614045,
		"status": "IN_WORK",
		"createTime": "2025-04-23 12:27:38",
		"qaProcess": null,
		"shoporder": "4509518782",
		"qaStatus": "NORMAL",
		"newShoporder": null,
		"isForce": 1,
		"routeVersion": "A",
		"lineName": "A503_H6",
		"resourceCode": "70:85:C2:A6:63:83",
		"updateTime": "2025-04-23 14:14:33",
		"shard": null,
		"modelType": "显卡-PG172",
		"processBomName": "14548160-00组装",
		"qaOpName": null,
		"matVersion": "A",
		"resourceName": "NBL",
		"sn": "1421625040176",
		"qaOpTime": null,
		"updateName": "6517645",
		"batchNo": null,
		"shoporderSap": "4509518782",
		"sectionName": "组装",
		"processBomVersion": "A5",
		"printSeq": null,
		"processName": "M_CHECK_MODULE",
		"routeName": "14548160-00-组装-A0",
		"remark": null,
		"qty": 1,
		"createName": "FLAMES",
		"customerCode": "19543",
		"previousProcess": "ICT",
		"matNo": "14548160-00"
	}
}</string>"""
    root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    print(root.text)