"""
# File       : main.py
# Time       ：2025/05/13 15:06
# Author     ："wxc"
# version    ：python 3.8
# Description：中山盈利佳
"""
import hashlib
import json
from datetime import datetime, timedelta
from typing import Any

from common import xcons, xrequest, xglobal, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo, ComboVo


def sha256_encrypt(input_str: str) -> str:
    # 创建 SHA-256 哈希对象
    sha256_hash = hashlib.sha256()
    # 更新哈希计算（输入必须是 bytes 类型）
    sha256_hash.update(input_str.encode("utf-8"))
    # 返回十六进制哈希值
    return sha256_hash.hexdigest()


class Engine(ErrorMapEngine):
    version = {
        "customer": ["中山盈利佳", "zhongshanyinglijia"],
        "version": "release v1.0.0.1",
        "device": "AIS401",
        "feature": ["上传数据"],
        "author": "wxc",
        "release": """
date: 2025-05-13 15:11  ATAOI_2019-39380:获取token,获取订单号,物料信息,上传数据
"""
    }
    combo = {
        "order_id": {
            "ui_name": "订单号",
            "item": [],
            "value": "",
        },
        "order_line_id": {
            "ui_name": "采购订单行号",
            "item": [],
            "value": "",
        },
        "material_code": {
            "ui_name": "物料号",
            "item": [],
            "value": "",
        },
        "comp_data_output": {
            "ui_name": "器件数据输出",
            "item": [
                "全部",
                "仅复判NG",
                "仅检测NG",
            ],
            "value": "全部"
        },
    }
    other_form = {
        "get_token_api_url": {
            "ui_name": "获取token接口",
            "value": ""
        },
        "get_order_api_url": {
            "ui_name": "获取订单信息接口",
            "value": ""
        },
        "get_materialcode_api_url": {
            "ui_name": "获取物料信息接口地址",
            "value": ""
        },
        "upload_api_url": {
            "ui_name": "上传接口",
            "value": ""
        },
    }

    form = {
        "username": {
            "ui_name": "账号",
            "value": ""
        },
        "password": {
            "ui_name": "密码",
            "value": ""
        },
        "product_line": {
            "ui_name": "生产拉线",
            "value": ""
        },
        "process": {
            "ui_name": "制程段",
            "value": ""
        },
        "machine": {
            "ui_name": "机器设备",
            "value": ""
        },
        "product_opera": {
            "ui_name": "操作人",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        upload_api_url = data_vo.get_value_by_cons_key("upload_api_url", not_null=True)
        comp_data_output = data_vo.get_value_by_cons_key("comp_data_output")
        product_line = data_vo.get_value_by_cons_key("product_line")
        process = data_vo.get_value_by_cons_key("process")
        machine = data_vo.get_value_by_cons_key("machine")
        product_opera = data_vo.get_value_by_cons_key("product_opera")
        order_id = data_vo.get_value_by_cons_key("order_id", not_null=True)
        order_line_id = data_vo.get_value_by_cons_key("order_line_id", not_null=True)
        material_code = data_vo.get_value_by_cons_key("material_code", not_null=True)

        # 获取token
        get_token_api_url = data_vo.get_value_by_cons_key("get_token_api_url")
        username = data_vo.get_value_by_cons_key("username", not_null=True)
        password = data_vo.get_value_by_cons_key("password", not_null=True)

        current_time = datetime.now().strftime(xcons.FMT_TIME_FILE)
        self.log.info(f"sha256加密后：{sha256_encrypt(username + password + current_time)}")
        login_param = {
            "username": username,
            "password": password,
            "currentTime": current_time,
            "code": sha256_encrypt(username + password + current_time)
        }

        ret = xrequest.RequestUtil.post_json(get_token_api_url, login_param)
        if ret.get("code") != 200:
            return self.x_response("flase", f"获取token失败,error:{ret.get('msg')}")
        token = ret.get("data").get("token")
        self.log.info(f"获取token成功：{token}")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        pcb_entity = data_vo.pcb_entity
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_data_output == "全部" or \
                        (comp_data_output == "仅复判NG" and comp_entity.is_repair_ng()) or \
                        (comp_data_output == "仅检测NG" and comp_entity.is_robot_ng()):
                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })
            # 组装上传数据
            board_data = {
                "orderNumber": order_id,
                "orderItem": order_line_id,
                "materialCode": material_code,
                "woNo": order_id if not pcb_entity.order_id else pcb_entity.order_id,
                "productTime": test_time,
                "productLine": product_line,
                "process": process,
                "machine": machine,
                "productOpera": product_opera,
                "sn": barcode,
                "testResult": board_entity.get_repair_result(),
                "testInfo": comp_data
            }
            ret = xrequest.RequestUtil.post_json(upload_api_url, board_data, headers=headers)
            if ret.get("code") != 200:
                return self.x_response("flase", f"mes接口异常，上传过站数据失败，error：{ret.get('msg')}")
        return self.x_response()

    def combo_mouse_press(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()
        get_order_api_url = combo_vo.get_value_by_cons_key("get_order_api_url", not_null=True)
        get_token_api_url = combo_vo.get_value_by_cons_key("get_token_api_url")
        get_materialcode_api_url = combo_vo.get_value_by_cons_key("get_materialcode_api_url")
        username = combo_vo.get_value_by_cons_key("username", not_null=True)
        password = combo_vo.get_value_by_cons_key("password", not_null=True)

        order_id = combo_vo.get_value_by_cons_key("order_id")
        if combo_key != "comp_data_output":
            # 获取token
            current_time = datetime.now().strftime(xcons.FMT_TIME_FILE)
            self.log.info(f"sha256加密后：{sha256_encrypt(username + password + current_time)}")
            login_param = {
                "username": username,
                "password": password,
                "currentTime": current_time,
                "code": sha256_encrypt(username + password + current_time)
            }
            ret = xrequest.RequestUtil.post_json(get_token_api_url, login_param)
            if ret.get("code") != 200:
                return self.x_response("flase", f"获取token失败,error:{ret.get('msg')}")
            token = ret.get("data").get("token")
            headers = {
                "Authorization": f"Bearer {token}"
            }
        if combo_key == "order_id":

            # 获取订单号
            order_ret = xrequest.RequestUtil.get(get_order_api_url, {}, headers=headers)
            if order_ret.get("code") != 200:
                return self.x_response("flase", f"获取订单号失败,error:{order_ret.get('msg')}")
            order_data = order_ret.get("data")

            return self.x_response("true", json.dumps({
                "new_items": order_data
            }))
        elif combo_key == "order_line_id":
            # 获取物料信息
            machine_url = f"{get_materialcode_api_url}/?ebeln={order_id}"
            machine_ret = xrequest.RequestUtil.get(get_materialcode_api_url, {"ebeln": order_id}, headers=headers)
            if machine_ret.get("code") != 200:
                return self.x_response("flase", f"获取物料信息失败,error:{machine_ret.get('msg')}")
            material_info = machine_ret.get("data")
            material_code_map = {}
            for item in material_info:
                material_code_map[item.get("ebelp")] = item.get("matnr")

            xutil.CacheUtil.set("material_code_map", material_code_map)
            return self.x_response("true", json.dumps({
                "new_items": list(material_code_map.keys())
            }))
        elif combo_key == "material_code":
            line_order_id_select = getattr(main_window, f"combo_order_line_id").currentText()
            line_order_id = xutil.CacheUtil.get("material_code_map", {}).get(line_order_id_select)

            if not line_order_id:
                return self.x_response("false", "未选中采购订单行号，请先选采购订单行号！")
            return self.x_response("true", json.dumps({
                "new_items": [line_order_id]
            }))


