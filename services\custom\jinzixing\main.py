# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/21 下午5:35
# Author     ：sch
# version    ：python 3.8
# Description：金子星 / 浙江金子星
"""

from typing import Any

from common import xrequest, xutil, xglobal, xcons
from common.xutil import log, x_response
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


def jzx_x_request(api_url, command_string):
    """
    统一请求方式
    """
    ret = xrequest.RequestUtil.post_form(api_url, {"commandString": command_string}, to_json=False)

    root = xutil.XmlUtil.get_xml_root_by_str(ret)

    ret_str = root.text
    ret_list = ret_str.split(";")
    log.debug(f"ret list: {ret_list}")
    if ret_list[0] != "OK":
        return x_response("false", f"接口响应异常, error: {ret_list[1]}")


class Engine(ErrorMapEngine):
    version = {
        "title": "jinzixing release v1.0.0.2",
        "device": "AIS43X,AIS63X",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-22 09:15  用户校验，条码校验，上传数据
date: 2024-08-26 10:29  改成按拼板上传数据
""", }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(用户校验)",
            "value": "http://127.0.0.1:8081/WebService/MesATEApi.asmx/WS_UserPassValid",
        },
        "api_url_check": {
            "ui_name": "接口URL(验证序号)",
            "value": "http://127.0.0.1:8081/WebService/MesATEApi.asmx/ATECommandCode",
        },
        "api_url_data": {
            "ui_name": "接口URL(过站上传)",
            "value": "http://127.0.0.1:8081/WebService/MesATEApi.asmx/ATECommandCode",
        },
    }

    form = {
        "username": {
            "ui_name": "账号",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
        "station": {
            "ui_name": "工序名称",
            "value": "",
        },
        "res_name": {
            "ui_name": "资源名称",
            "value": "",
        },
        "machine_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "fixture_code": {
            "ui_name": "工治具编码",
            "value": "",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "用户校验",
        }
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        station = other_vo.get_value_by_cons_key("station")
        res_name = other_vo.get_value_by_cons_key("res_name")
        machine_code = other_vo.get_value_by_cons_key("machine_code")
        fixture_code = other_vo.get_value_by_cons_key("fixture_code")
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")

        username = xglobal.global_data.get("login_user")
        if not username:
            return self.x_response("false", f"未登录，请先登录！")

        sn_list = other_vo.list_sn()

        ret_res = self.x_response()
        for sn in sn_list:
            check_param = f"01;{username};{sn};{station};{res_name};{machine_code};{fixture_code};"
            if x_res := jzx_x_request(api_url_check, check_param):
                ret_res = x_res

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        station = data_vo.get_value_by_cons_key("station")
        res_name = data_vo.get_value_by_cons_key("res_name")
        machine_code = data_vo.get_value_by_cons_key("machine_code")
        fixture_code = data_vo.get_value_by_cons_key("fixture_code")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        username = xglobal.global_data.get("login_user")
        if not username:
            return x_response("false", f"未登录，请先登录！")

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_ng_list05 = []
            comp_ng_list04 = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    repair_ng_str = comp_entity.repair_ng_str
                    comp_tag = comp_entity.designator

                    comp_ng_list04.append(f"{comp_tag}:{repair_ng_str}")

                    if not comp_ng_list05:
                        comp_ng_list05.append("NG")
                        comp_ng_list05.append(repair_ng_str)

            test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

            base_data05 = ["05", username, barcode, station, res_name, machine_code, fixture_code]
            base_data04 = ["04", username, barcode, station, res_name, machine_code, fixture_code, test_time, ]
            base_data04.append(','.join(comp_ng_list04))

            param04 = ";".join(base_data04) + ';'

            log.info(f"开始调用04接口....")
            if res := jzx_x_request(api_url_data, param04):
                return res

            if comp_ng_list05:
                base_data = base_data05 + comp_ng_list05
                base_param05 = ";".join(base_data) + ";"
            else:
                base_data05.append("OK;")
                base_param05 = ";".join(base_data05)

            log.info(f"开始调用05接口....")
            if res := jzx_x_request(api_url_data, base_param05):
                return res

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
        username = btn_vo.get_value_by_cons_key("username")
        password = btn_vo.get_value_by_cons_key("password")

        btn_key = btn_vo.get_btn_key()
        if btn_key == "login_btn":
            ret = xrequest.RequestUtil.post_form(api_url_login, {
                "UserName": username,
                "Password": password
            }, to_json=False)

            root = xutil.XmlUtil.get_xml_root_by_str(ret)
            ret_str = root.text

            if "ok" not in ret_str.lower():
                return self.x_response("false", f"mes接口异常，登录失败, error: {ret_str}")

            xglobal.global_data["login_user"] = username

        return self.x_response()
