# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : dll_service.py
# Time       ：2024/1/15 下午5:44
# Author     ：sch
# version    ：python 3.8
# Description：
"""
# import subprocess

# # 使用mono运行可执行文件
# subprocess.call(['mono', '/home/<USER>/.config/Code/py/taida/SDKTEST.exe'])
import os

import clr

# 加载CFX动态库
from common.xutil import log


base_path = os.getcwd()
print("os.get pwd", base_path)
dll_path = f'{base_path}/NENGXIAO_MESDll'
print("dll_path", dll_path)

dll = clr.AddReference(dll_path)

# 导入CFX命名空间
from MESDll import *
from MESDll import CICSharpinterface
from System import *
from System.Reflection import *


def check_station(mac_id: str, sn: str, user: str) -> str:
    """
    条码过站
    :param mac_id:
    :param sn:
    :param user:
    :return:
    """
    log.info(f"请求参数：{locals()}")
    my_instance = CICSharpinterface()
    result = my_instance.CheckStation(mac_id, sn, user)
    return result


def upload_res(mac_id: str, sn: str, user: str, res: str, ng_code: str, test_data: str):
    """
    上传数据
    :param mac_id:
    :param sn:
    :param user:
    :param res:
    :param ng_code:
    :param test_data:
    :return:
    """
    log.info(f"请求参数：{locals()}")
    my_instance = CICSharpinterface()
    result = my_instance.UploadRes(mac_id, sn, user, res, ng_code, test_data)
    return result
