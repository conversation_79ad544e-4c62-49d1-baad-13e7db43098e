# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : MesEngine.py
# Time       ：2023/4/8 上午11:20
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import abc
from datetime import datetime
from typing import Any

from PyQt5.QtWidgets import <PERSON><PERSON>rame, QBoxLayout

from common import xutil, xconfig, xenum
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo, ComboVo, ButtonVo


class BaseEngine(metaclass=abc.ABCMeta):

    def get_release(self) -> str:
        """
        获取版本信息
        :return:
        """
        old_version = self.version.get("title", "")
        # 存在title字段，则使用旧的version格式，否则使用新的version格式
        if old_version:
            release_info = old_version
        else:
            # 获取第二位别名["标准版", "standard"]
            customer_alias = self.version['customer'][1]
            version = self.version.get("version", "")
            release_info = f'{customer_alias} {version}'
        new_version = release_info.replace("release v1", f"release v{xconfig.common_version}")
        return new_version

    log = log

    mes_config_port = 9090

    version = {
        # 客户信息统一在customer中填写
        # customer填写规范    第一位：客户名称(有中文名则填写中文名，没有则填写英文名 ---> 影响范围：打包时上传到坚果云的目录)
        # customer填写规范    第二位：中文拼音/英文(与该main.py所处的上层目录同名)  ---> 影响范围：主窗口标题显示
        "customer": ["标准版", "standard"],
        "version": "release v1.0.0.1",
        "device": "20x,30x,40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-25 11:07  init
""", }

    # 保存路径
    path = {

    }

    other_path = {}

    # 按钮
    button = {
        # "check_order": {
        #     "ui_name": "校验工单"
        # }
    }

    # 下拉框
    combo = {
        # "device_type": {
        #     "item": [
        #         "AOI", "SPI"
        #     ],
        #     "value": "AOI",
        #     "ui_name": "机台类型",
        #     "type": "standard"   type: CanSearch 下拉框将变更可搜索的下拉框
        # },
    }

    # 普通输入框
    form = {
        # "username": {
        #     "ui_name": "用户名",
        #     "value": "admin"
        # },
        # "password": {
        #     "ui_name": "密码",
        #     "value": "fake_password"
        # },
    }

    # 其他的普通输入框
    other_form = {
        # "ftp_host": {
        #     "ui_name": "FTP Host",
        #     "value": "127.0.0.1"
        # },
        # "ftp_port": {
        #     "ui_name": "FTP Port",
        #     "value": "21"
        # },
        # "ftp_user": {
        #     "ui_name": "FTP 账号",
        #     "ui_name_en": "FTP User",
        #     "value": "sch"
        # },
        # "ftp_password": {
        #     "ui_name": "FTP 密码",
        #     "ui_name_en": "FTP Password",
        #     "value": "123456"
        # },
        # "ftp_path": {
        #     "ui_name": "FTP 路径",
        #     "ui_name_en": "FTP Path",
        #     "value": "/AOI"
        # },
    }

    # 其他combo框
    other_combo = {

    }

    password_style = [
        "password",
        "ftp_password",
    ]

    # 在界面画线区分配置项
    line_index = []

    # 配置文件没有的话，则来这里取默认的配置文件
    app_setting = {
        # 界面上的设置
        "merge_send": False,  # 合并上下板面数据, ps: 合并的设置需要同步到另外的文件给维修站使用！！！
        "send_error_info": True,  # 发送失败弹窗提示
        "send_info_to_repair": False,  # 发送结果给维修工站
        "repair_ip": "",

        # 去除重复发送
        "filter_repeat_send": False,  # 去除重复发送的mes数据

        # 定时设置
        "is_cron_clear": False,  # 开启定时清除功能
        "fixed_status_1": True,  # 固定1
        "fixed_status_2": True,  # 固定2
        "fixed_time_1": "8:00",  # 固定在第几个小时
        "fixed_time_2": "18:00",  # 固定在第几个小时
        "fixed_time_clear": True,  # 固定时间清除
        "interval_time_clear": False,  # 间隔时间清除
        "interval_time": 2,  # 间隔多少小时

        # 自定义的定时设置

        # 定时任务设置
        "custom_interval_cron": False,
        "custom_interval_time": 120,  # 间隔多少秒

        "custom_interval_cron2": False,
        "custom_interval_time2": 120,  # 间隔多少秒

        # 这个定时器用于固定某个时间点执行任务
        "custom_interval_cron3": False,
        "custom_fixed_time": {  # 这些key是BlockingScheduler的add_job定义的参数，* 表示不作限制，以下默认值表示凌晨1点0分执行任务
            'minute': '0',  # 0到59之间的整数，表示在一小时内哪一分钟执行任务
            'hour': '1',  # 0到23之间的整数，表示一天中哪个小时执行任务。
            'day': '*',  # 1到31之间的整数，表示一个月中的哪一天执行任务。具体有效的日期取决于月份以及是否是闰年。
            'month': '*',  # 1到12之间的整数，表示一年中哪个月份执行任务。
            'day_of_week': '*',  # 0到6之间的整数（0代表星期天），表示一周中的哪一天执行任务。
        },

        # 其他设置
        # AIS203/AIS303/AIS40X/AIS50x     AIS301     AIS201    AIS63X
        "error_code_device": "AIS203/AIS303/AIS40X/AIS50x",
        "http_server_run": False,
        "setting_lang": "ZH",  # ZH/EN
        "use_password": False  # 保存是否使用密码确认
    }

    common_config = {
        "check_barcode_setting1": xenum.CheckSetting1.CheckAll,
        "check_barcode_setting2": xenum.CheckSetting2.CheckFailNotSendData,
        "sendmes_setting1": xenum.SendMesSetting1.SaveAll,
        "sendmes_setting2": xenum.SendMesSetting2.SaveAll,
        "sendmes_setting3": xenum.SendMesSetting3.NotSend,
    }

    def set_lang_to_en(self, force_set: bool = False):
        """
        设置初始语言为英语

        force_set: 是否强制设置成英文，如果为True，则每次启动都会强制设置成英文
        :return:
        """
        self.app_setting["setting_lang"] = "EN"
        config_data = xutil.FileUtil.load_config_file()

        if "app_setting" not in config_data:
            config_data["app_setting"] = {}
            if "setting_lang" not in config_data["app_setting"]:
                config_data["app_setting"]["setting_lang"] = "EN"
                xutil.FileUtil.save_config_file(config_data)

        elif force_set:
            config_data["app_setting"]["setting_lang"] = "EN"
            xutil.FileUtil.save_config_file(config_data)

    # def force_set_lang_to_en(self):
    #     """
    #     强制设置语言为英语
    #     :return:
    #     """
    #     self.app_setting["setting_lang"] = "EN"
    #     config_data = xutil.FileUtil.load_config_file()
    #     if "app_setting" not in config_data:
    #         config_data["app_setting"] = {}
    #         if "setting_lang" not in config_data["app_setting"]:
    #             config_data["app_setting"]["setting_lang"] = "EN"

    @staticmethod
    def x_response(status: str = "true", msg: str = "发送成功"):
        """
        返回一个标准的响应
        :param status: 是否成功响应  (true, 成功) (true, 失败)
        :param msg: 返回给界面的信息
        :return:
        """
        # if status == "False":
        #     log.error(msg)

        if status == "true":
            result = True
        else:
            result = False

        time_now = datetime.now().strftime("%Y.%m.%d %H:%M:%S")
        return {'key': time_now, 'result': result, 'string': msg}

    @abc.abstractmethod
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        发送数据到mes
        :param data_vo: 前端数据访问层
        :param other_data: inspect_type --> (inspector,主软件检测完发送)  (repair,维修站复判后发送)
        :param other_param:
        :return:
        """
        pass

    def init_main_window(self, main_window, other_vo: OtherVo):
        """
        初始化主窗口
        :return:
        """
        pass

    def save_btn_on_window(self, main_window):
        """
        主软件的保存按钮触发
        :param main_window:
        :return:
        """
        pass

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        条码校验
        :param other_vo: 其他数据访问层
        :param main_window:
        :return:
        """
        pass

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        发送设备状态到mes
        :param other_vo: 其他数据访问层
        :param main_window:
        :return:
        """
        pass

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        从mes获取条码
        :param other_vo: 其他数据访问层
        :param main_window:
        :return:
        """
        pass

    def send_idle_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        发送空闲状态给mes
        :param other_vo: 其他数据访问层
        :param main_window:
        :return:
        """
        pass

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        """
        登录到Mes
        :return:
        """
        pass

    def combo_index_changed(self, combo_vo: ComboVo, main_window: Any):
        """
        combo组件下拉改变
        :param combo_vo:
        :param main_window:
        :return:
        """
        pass

    def combo_index_changed_other(self, combo_vo: ComboVo, other_data: dict):
        """
        其他combo组件下拉改变
        :param combo_vo:
        :param other_data:
        :return:
        """
        pass

    def combo_mouse_press(self, combo_vo: ComboVo, main_window: Any):
        """
        combo组件下拉点击
        :param combo_vo:
        :param main_window:
        :return:
        """
        pass

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        """
        定制按钮触发
        :param btn_vo:
        :param other_param:
        :return:
        """
        pass

    def create_custom_button(self, parent: QFrame):
        """
        新建自定义按钮
        :param parent: 父控件
        :param layout: 布局
        :return:
        """
        pass

    def get_bad_board_info(self, other_vo: OtherVo, other_param: Any):
        """
        从mes获取坏板信息
        :return:
        """
        pass

    def send_panel_data(self, other_vo: OtherVo, main_window):
        """
        发送面板数据到mes  (台达专用)
        :param other_vo:
        :param main_window:
        :return:
        """
        pass

    def send_production_to_mes(self, other_vo: OtherVo, other_param: Any):
        """
        发送生产过程
        :param other_vo:
        :param other_param:
        :return:
        """
        pass

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        """
        定制的定时任务
        :param other_vo:
        :param main_window:
        :param other_param:
        :return:
        """
        pass

    def custom_cron_function2(self, other_vo: OtherVo, main_window, other_param: Any):
        """
        定制的定时任务2
        :param other_vo:
        :param main_window:
        :param other_param:
        :return:
        """
        pass

    def custom_cron_function3(self, other_vo: OtherVo, main_window, other_param: Any):
        """
        定制的定时任务3，用于固定某个时间点发送，比如一天或者一周或者一个月某天某时某分发送等
        :param other_vo:
        :param main_window:
        :param other_param:
        :return:
        """
        pass

    def send_project_info_to_mes(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        """
        触发时机：
        1. 需要主软件同时打开这个板式，在离线编辑时，点发布按钮时请求一次接口
        2. 在主软件点开始检测时，请求一次接口

        funcName: sendProjectInfo

        数据格式
        {
            "funcArgs": [""],
            "funcName": "sendProjectInfo",
            "order": "",
            "projectName": "333.001",
            "pwd": "admin",
            "trackIndex": 0,
            "userName": "admin"
        }
        :return:
        """
        pass

    def send_out_board_info_to_mes(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        """
        每块板卡出板时调用一次
        数据格式
        {
            "funcArgs":[""],
            "funcName":"sendOutBoardInfo",
            "order":"",
            "runTime":"0h13m",
            "timestamp":"20231202105304658",
            "trackIndex":0,
            "trackWidth":0
        }
        :return:
        """
        pass

    def get_project_name_by_sn(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        """
        从mes获取板式，并且自动切换
        每块板卡进板前，调用一次
        仅支持条码枪扫码, 需要在主软件配置切换的板式版本
        数据格式
        GetProjectNameBySn
        入参：
        {
          "constants": {
            "用户名": "123456",
            "密码": "123456"
          },
          "data": {
            "funcArgs": [["SN001", "SN002"]],
            "funcName": "GetProjectNameBySn"
          }
        }

        返回形式
        {"result": true, "string": "板式名"}
        {"result:: false, "string": "失败"}
        :return:
        """
        pass

    # bosch需求场景使用（其它场景是否能用需版本验证），相机扫码到条码后，向MES发起条码校验确认是否要进行板式切换
    def check_project_by_barcode(self, other_vo: OtherVo, param: dict):
        """
        协议文档参考：https://jira.cvte.com/browse/ATAOI_2019-37766
        板式切换的条码校验，扫到条码（条码枪扫码或相机扫码）后会发起条码校验请求。
        请求参数如下：
        {
            "funcName":"CheckProjectByBarcode",
            "barcode": [["条码"]],
            "trackIndex":0,
        }
        返回参数如下:
        {
            "key": "2025.03.06 10:55:49",
            "result": true,
            "string": ""
        }
        result为true时，如果需板式切换string是一个字典的序列化，如果正常检测string为空，false时是异常信息
        {
            "projectName": "切换板式名",
            "topSideVersion": '',
            "bottomSideVersion": '',
        }
        """
        pass

    def send_check_project_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        """
        每调用一个程序，触发一次
        :return:
        """
        pass

    def send_board_data_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        """
        每检测一块板，发送一次界面信息
        :return:
        """
        pass

    def send_production_info_to_mes(self, data_vo: DataVo, other_data: dict, main_window):
        """
        发送稼动数据到MES，视源睿创

        {
          "funcName": "UploadProductionInfo",
          "orderID": "A0001005006",
          "projectName": "333.001",
          "ReviewPath": "/home/<USER>/aoi/run/results/333.001/20221117/T_20221117100335938_1_NG;/home/<USER>/aoi/run/results/333.001/20221117/B_20221117101645789_1",
          "productionInfo": {
            "laneIndex": 1,
            "proName": "333",
            "proVersion": "001",
            "ErrorCount": 2,
            "totalRunTime": 60,
            "productionStartTime": "2024-07-23 17:55:00",
            "productionEndTime": "2024-07-23 17:55:10",
            "actualTime": 60,
            "transferTime": 60,
            "downTime": 60,
            "errorTime": 60,
            "cycleTime": 60,
            "frontWait": 60,
            "rearWait": 60,
            "otherTime": 60,
            "reportPaths": [
              "/home/<USER>/aoi/run/results/333.001/20221117/T_20221117100335938_1_NG",
              "/home/<USER>/aoi/run/results/333.001/20221117/B_20221117101645789_1"
            ]
          }
        }
        :param data_vo:
        :param other_data:
        :param main_window:
        :return:
        """
        pass

    def send_btn_click_push_event(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        """
        主软件：点击类事件推送
        ### 请求
        {
            "funcName": "BtnClickPush",  # 固定传：BtnClickPush
            "type": "ClearPanelData",  # 固定传：ClearPanelData
            "trackIndex": 0,  # 轨道: (0, 1轨)   (1, 2轨)
            "projectName": "333.001"  # 板式名称
        }
        ### 响应
        {
            "key": "2023.07.12 10:55:49",
            "result": true,
            "string": "ok"
        }

        ------  轨道设置宽度推送
        ### 请求
        {
            "funcName": "BtnClickPush",  # 固定传：BtnClickPush
            "type": "TrackWidthSet",  # 固定传：TrackWidthSet
            "trackIndex": 0,  # 轨道: (0, 1轨)   (1, 2轨)
            "width": 100.01  # float类型，设置的轨道实际宽度，单位mm毫米
        }
        ### 响应
        {
            "key": "2023.07.12 10:55:49",
            "result": true,
            "string": "ok"
        }
        """
        pass

    def on_exit(self):
        """
        如果想在关闭配置器之前执行一些事情，可以在这里添加执行逻辑
        """
        pass

    def hock_other_setting(self, other_view_obj):
        """
        其他设置的钩子函数
        :param other_view_obj:
        """
        pass

    def call_custom_function(self, func_name: str, other_vo: OtherVo):
        """
        客户自定义的和检测软件或者维修站的接口统一在自己内部函数里进行实现。
        socket_service的call_other_function里定位为实现所有客户都使用的共用函数接口
        """
        pass


class ErrorMapEngine(BaseEngine):
    ERROR_MAP = {
        "AIS203/AIS303/AIS40X/AIS50x": {
            "1": {
                "standard": "漏件",
                "custom_code": "1",
                "custom_str": "MissingPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "2": {
                "standard": "错件",
                "custom_code": "2",
                "custom_str": "WrongPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "3": {
                "standard": "反件",
                "custom_code": "3",
                "custom_str": "ReversePart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "4": {
                "standard": "立碑",
                "custom_code": "4",
                "custom_str": "Tomstone",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "5": {
                "standard": "偏移",
                "custom_code": "5",
                "custom_str": "ShiftPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "6": {
                "standard": "翻转",
                "custom_code": "6",
                "custom_str": "UpsideDown",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "7": {
                "standard": "浮高",
                "custom_code": "7",
                "custom_str": "LiftedPackage",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "8": {
                "standard": "损件",
                "custom_code": "8",
                "custom_str": "Broken",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "9": {
                "standard": "露铜",
                "custom_code": "9",
                "custom_str": "ExposeCopper",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "10": {
                "standard": "少锡",
                "custom_code": "10",
                "custom_str": "InsufficientSolder",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "11": {
                "standard": "多锡",
                "custom_code": "11",
                "custom_str": "ExcessSolder",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "12": {
                "standard": "未出脚",
                "custom_code": "12",
                "custom_str": "NoPin",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "13": {
                "standard": "孔洞",
                "custom_code": "13",
                "custom_str": "PinHole",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "14": {
                "standard": "连锡",
                "custom_code": "14",
                "custom_str": "Bridge",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "15": {
                "standard": "锡珠",
                "custom_code": "15",
                "custom_str": "SolderBall",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "16": {
                "standard": "翘脚",
                "custom_code": "16",
                "custom_str": "LiftedLead",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "17": {
                "standard": "弯脚",
                "custom_code": "17",
                "custom_str": "ShiftedLead",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "18": {
                "standard": "异物",
                "custom_code": "18",
                "custom_str": "ForeignMaterial",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "19": {
                "standard": "条码识别",
                "custom_code": "19",
                "custom_str": "BarcodeRecognition",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "20": {
                "standard": "Marker搜索",
                "custom_code": "20",
                "custom_str": "MarkerSearch",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "21": {
                "standard": "多件",
                "custom_code": "21",
                "custom_str": "ForeignPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "22": {
                "standard": "溢胶",
                "custom_code": "22",
                "custom_str": "Overflow",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "23": {
                "standard": "虚焊",
                "custom_code": "23",
                "custom_str": "IncompleteWeld",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "24": {
                "standard": "脏污",
                "custom_code": "24",
                "custom_str": "Dirty",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "25": {
                "standard": "坏板",
                "custom_code": "25",
                "custom_str": "BadPanel",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "26": {
                "standard": "定位",
                "custom_code": "26",
                "custom_str": "Locate",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "27": {
                "standard": "数目错误",
                "custom_code": "27",
                "custom_str": "CountError",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "28": {
                "standard": "少涂/多涂",
                "custom_code": "28",
                "custom_str": "LessMoreCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "29": {
                "standard": "少涂",
                "custom_code": "29",
                "custom_str": "LessCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "30": {
                "standard": "多涂",
                "custom_code": "30",
                "custom_str": "MoreCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "31": {
                "standard": "气泡",
                "custom_code": "31",
                "custom_str": "Bubble",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "32": {
                "standard": "划痕",
                "custom_code": "32",
                "custom_str": "Scratch",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "33": {
                "standard": "距离",
                "custom_code": "33",
                "custom_str": "Distance",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "34": {
                "standard": "锡膏检测",
                "custom_code": "34",
                "custom_str": "SPIDetect",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "35": {
                "standard": "共线性",
                "custom_code": "35",
                "custom_str": "Collinearity",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "36": {
                "standard": "CPU插针检测",
                "custom_code": "36",
                "custom_str": "CPUPinDetect",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "37": {
                "standard": "3D异物检测",
                "custom_code": "37",
                "custom_str": "ForeignBodyDetect",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "38": {
                "standard": "厚度异常",
                "custom_code": "38",
                "custom_str": "ThicknessAbnormal",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "39": {
                "standard": "断胶",
                "custom_code": "39",
                "custom_str": "GlueOff",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "40": {
                "standard": "尺寸异常",
                "custom_code": "40",
                "custom_str": "WrongSize",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
        },
        "AIS301": {
            "1": {
                "standard": "不合格",
                "custom_code": "1",
                "custom_str": "NG",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "2": {
                "standard": "多锡",
                "custom_code": "2",
                "custom_str": "ExcessSolder",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "3": {
                "standard": "连锡",
                "custom_code": "3",
                "custom_str": "Bridge",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "4": {
                "standard": "少锡",
                "custom_code": "4",
                "custom_str": "InsufficientSolder",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "5": {
                "standard": "孔洞",
                "custom_code": "5",
                "custom_str": "PinHole",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "6": {
                "standard": "未出脚",
                "custom_code": "6",
                "custom_str": "NoPin",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "7": {
                "standard": "异常出脚",
                "custom_code": "7",
                "custom_str": "ExceptionPin",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "8": {
                "standard": "缺件",
                "custom_code": "8",
                "custom_str": "MissingPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "9": {
                "standard": "偏位",
                "custom_code": "9",
                "custom_str": "ShiftPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "10": {
                "standard": "露铜",
                "custom_code": "10",
                "custom_str": "ExposeCopper",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "11": {
                "standard": "错件",
                "custom_code": "11",
                "custom_str": "WrongPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "12": {
                "standard": "极性错误",
                "custom_code": "12",
                "custom_str": "ReversePart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "13": {
                "standard": "条码识别错误",
                "custom_code": "13",
                "custom_str": "BarcodeRecognition",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "14": {
                "standard": "数据错误",
                "custom_code": "14",
                "custom_str": "CountError",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "15": {
                "standard": "定位错误",
                "custom_code": "15",
                "custom_str": "Locate",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "16": {
                "standard": "流程错误",
                "custom_code": "16",
                "custom_str": "ProcessError",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "17": {
                "standard": "锡珠",
                "custom_code": "17",
                "custom_str": "SolderBall",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "18": {
                "standard": "拼版特征不匹配",
                "custom_code": "18",
                "custom_str": "FeatureMismatch",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            }
        },
        "AIS201": {
            "1": {
                "standard": "漏件",
                "custom_code": "1",
                "custom_str": "MissingPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "-1": {
                "standard": "未检查",
                "custom_code": "-1",
                "custom_str": "NoCheck",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "4": {
                "standard": "反件",
                "custom_code": "4",
                "custom_str": "ReversePart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "5": {
                "standard": "错件",
                "custom_code": "5",
                "custom_str": "WrongPart",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "20": {
                "standard": "引脚未插",
                "custom_code": "20",
                "custom_str": "Pin Not Found",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "21": {
                "standard": "不是引脚",
                "custom_code": "21",
                "custom_str": "NoPin",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "101": {
                "standard": "多件",
                "custom_code": "101",
                "custom_str": "多件",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "102": {
                "standard": "浮高",
                "custom_code": "102",
                "custom_str": "Part Lift",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "103": {
                "standard": "歪斜",
                "custom_code": "103",
                "custom_str": "Part Tilt",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "104": {
                "standard": "条码错误",
                "custom_code": "104",
                "custom_str": "Barcode Error",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "105": {
                "standard": "内部错误",
                "custom_code": "105",
                "custom_str": "Internal Error",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "80": {
                "standard": "多涂",
                "custom_code": "80",
                "custom_str": "MoreCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "81": {
                "standard": "少涂",
                "custom_code": "81",
                "custom_str": "LessCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "82": {
                "standard": "气泡",
                "custom_code": "82",
                "custom_str": "BubbleCoating",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            }
        },
        "AIS63X": {
            "1": {
                "standard": "锡型不良",
                "custom_code": "1",
                "custom_str": "锡型不良",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "2": {
                "standard": "水平偏移",
                "custom_code": "2",
                "custom_str": "水平偏移",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "3": {
                "standard": "竖直偏移",
                "custom_code": "3",
                "custom_str": "竖直偏移",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "4": {
                "standard": "连锡",
                "custom_code": "4",
                "custom_str": "连锡",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "5": {
                "standard": "面积偏小",
                "custom_code": "5",
                "custom_str": "面积偏小",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "6": {
                "standard": "面积偏大",
                "custom_code": "6",
                "custom_str": "面积偏大",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "7": {
                "standard": "高度偏低",
                "custom_code": "7",
                "custom_str": "高度偏低",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "8": {
                "standard": "高度偏高",
                "custom_code": "8",
                "custom_str": "高度偏高",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "9": {
                "standard": "少锡",
                "custom_code": "9",
                "custom_str": "少锡",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "10": {
                "standard": "多锡",
                "custom_code": "10",
                "custom_str": "多锡",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "11": {
                "standard": "无锡",
                "custom_code": "11",
                "custom_str": "无锡",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "12": {
                "standard": "共面性",
                "custom_code": "12",
                "custom_str": "共面性",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "19": {
                "standard": "条码识别",
                "custom_code": "19",
                "custom_str": "条码识别",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            },
            "1000": {
                "standard": "Unknown",
                "custom_code": "1000",
                "custom_str": "Unknown",
                "upload_mes": True,
                "check_flag2": True,
                "check_flag3": False
            }
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pass

    def custom_str_to_chinese(self):
        """
        将不良描述改为中文
        :return:
        """
        error_map = self.ERROR_MAP
        for i, k in error_map.items():
            for d, j in k.items():
                self.ERROR_MAP[i][d]["custom_str"] = j.get("standard")
