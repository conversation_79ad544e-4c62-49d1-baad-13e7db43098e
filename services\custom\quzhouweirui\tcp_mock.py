# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : tcp_mock.py
# Time       ：2025/3/21 16:42
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import socket

HOST = '127.0.0.1'  # Standard loopback interface address (localhost)
PORT = 8082  # Port to listen on (non-privileged ports are > 1023)

with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
    s.bind((HOST, PORT))
    s.listen()
    print(f"Server listening on {HOST}:{PORT}")
    conn, addr = s.accept()  # Wait for a connection
    with conn:
        print(f"Connected by {addr}")
        while True:
            data = conn.recv(1024)  # Receive data in 1024-byte chunks
            if not data:
                break
            print(f"Received: {data.decode()}")  # Decode bytes to string
            conn.sendall(b'OK')  # Echo back the data
            print(f"Sent back: {data.decode()}")

print("Server stopped")
