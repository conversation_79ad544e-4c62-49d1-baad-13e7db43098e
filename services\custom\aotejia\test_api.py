# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/12/8 上午9:20
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import binascii

import struct

# 0110 0100	0144	100	0x64	d

# 十六进制转bytes字节
d = binascii.a2b_hex("64")
print(d, type(d), len(d))  # b'd' <class 'bytes'> 1

# 十六进制转十进制
d = int('64', 16)
print(d, type(d))  # 100 <class 'int'>

# 十进制转十六进制
d = hex(100)
print(d, type(d), len(d))  # 0x64 <class 'str'> 4

# 十进制转 字符传
d = chr(100)
print(d, type(d), len(d))  # d <class 'str'> 1

# int 转 字节
c = struct.pack('>h', 100)
print(c, type(c), len(c))  # b'\x00d' <class 'bytes'> 2

print(f"{struct.unpack('>h', c)=}")


"""
b'd' <class 'bytes'> 1
100 <class 'int'>
0x64 <class 'str'> 4
d <class 'str'> 1
b'\x00d' <class 'bytes'> 2
"""
print("----------------")
print(f"{struct.pack('>2s', b'1')=}")
print(f"{struct.pack('2s', b'1')=}")
print("----------------")

print(f"{struct.pack('>h', 1)=}")
print(f"{struct.pack('h', 1)=}")

# 字节转16进制
print(f"{struct.pack('>h', 184).hex()=}")
print(f"{struct.pack('>h', 1)=}")

print(f'{binascii.a2b_hex("0100")=}')
