# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/9 上午10:22
# Author     ：sch
# version    ：python 3.8
# Description：烟台力高/山东力高
"""

from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "yantailigao release v1.0.0.4",
        "device": "AIS303,AIS50X",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-09 11:05  上传测试数据
date: 2024-04-15 16:45  取不到拼板条码则取整板条码
date: 2024-04-16 11:05  增加不良代码映射
date: 2024-04-23 14:11  增加接口超时时间配置项
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "",
        },
        "station_id": {
            "ui_name": "工站",
            "value": "COATING01",
        },
    }

    other_combo = {
        "time_sleep_api": {
            "ui_name": "接口超时时间(s)",
            "item": ["1", "2", "5", "6", "7", "10", "15", "20"],
            "value": "10"
        }
    }

    other_form = {}

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        station_id = data_vo.get_value_by_cons_key("station_id")
        time_sleep_api = data_vo.get_value_by_cons_key("time_sleep_api", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        project_name = pcb_entity.project_name
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        test_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not barcode and pcb_sn:
                barcode = pcb_sn

            # 板子序列号,不良代码1,不良位置代码1@板子序列号,不良代码2,不良位置代码2
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    repair_ng_code = comp_entity.repair_ng_code

                    test_data_list.append(f"{barcode},{comp_tag},{repair_ng_code}")

        r = pcb_entity.get_final_result("GOOD", "PASS", "NG")

        pcb_param = {
            "PROCEDURE": "MES.P_GET_CHECK_INFO",
            "FUNCTION_CODE": "PASSSTATIONBYCOATING",
            "INALLDATA": f"PROGRAMNAME={project_name};STATION={station_id};SN={pcb_sn};RESULT={r};TESTDATA={'@'.join(test_data_list)};TESTTIME={start_time};"
        }

        ret = xrequest.RequestUtil.post_json(api_url, pcb_param, timeout=time_sleep_api)
        if str(ret.get('code')) != '1':
            return self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret.get('message')}")

        return self.x_response()
