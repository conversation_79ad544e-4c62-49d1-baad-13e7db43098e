# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xsql.py
# Time       ：2023/8/7 下午2:37
# Author     ：sch
# version    ：python 3.8
# Description：数据库操作相关
"""
import json
import os
import sqlite3
from collections import defaultdict
from typing import Tuple

import pymssql
import pymysql

from common.xutil import log


class MySQLService(object):
    def __init__(self, host: str, db_name: str, port: int = 3306, username: str = "root", password: str = "aoi2014"):
        self.db = pymysql.connect(host=host,
                                  port=port,
                                  user=username,
                                  password=password,
                                  database=db_name)

    def get_db(self):
        """
        获取一个数据库链接（都是同一个）
        :return:
        """
        return self.db

    def update_barcode_by_board_uuid(self, board_uuid: str, barcode: str):
        """
        更新`AIS400`数据库中的`board`表

        ps:        旧版本
        :param board_uuid:
        :param barcode:
        :return:
        """
        sql_str = f"UPDATE board set board_sn='{barcode}' where board_uuid='{board_uuid}';"
        log.info(f"update sql: {sql_str}")

        with self.db.cursor() as cursor:
            cursor.execute(sql_str)

            # 提交事务
            self.db.commit()
            log.info(f"Record updated successfully.")

    def update_barcode_by_board_uuid_v2(self, board_uuid: str, barcode: str):
        """
        更新`makerray`数据库中的`tb_board`表

        ps:        新版本
        :param board_uuid:
        :param barcode:
        :return:
        """
        sql_str = f"UPDATE tb_board set board_sn='{barcode}' where board_uuid='{board_uuid}';"
        log.info(f"update sql: {sql_str}")

        with self.db.cursor() as cursor:
            cursor.execute(sql_str)

            # 提交事务
            self.db.commit()
            log.info(f"Record updated successfully.")

    def close(self):
        self.db.close()

    def get_unique_sn_by_board_uuid(self, board_uuid: str) -> str:
        """
        从数据库获取板边条码
        :param board_uuid:
        :return:
        """
        sql_str = f"select unique_sn from tb_board where board_uuid='{board_uuid}';"
        log.info(f"从数据库获取板边条码: {sql_str}")

        with self.db.cursor() as cursor:
            cursor.execute(sql_str)

            result = cursor.fetchall()

        log.info(f"ret: {result}")

        if result:
            return result[0][0]
        else:
            return ""

    def select_data_by_sql_str(self, select_sql: str) -> tuple:
        """
        查询记录
        """
        log.info(f"Select Data, Sql Command：\n{select_sql}")
        with self.db.cursor() as cur:
            cur.execute(select_sql)

            result = cur.fetchall()

        log.info(f"ret: {result}")

        log.info(f"Select Data Successful！")

        if result:
            return result
        else:
            return ()

    def select_t_b_barcode_list(self, b_report_uuid: str, pcb: str, bom: str) -> Tuple[dict, dict]:
        """
        获取t/b面拼板条码列表
        """
        # 0. 获取b面的拼板条码
        ret0 = self.select_data_by_sql_str(
            f"SELECT b.board_index, b.board_sn FROM board b WHERE b.report_uuid = '{b_report_uuid}';"
        )

        b_barcode = {str(i[0]): i[1] for i in ret0}

        # 1. 先获取project_id
        ret1 = self.select_data_by_sql_str(f"select project_id from project where pcb='{pcb}' and bom='{bom}';")
        project_id = ret1[0][0]

        # 2. 去关联表获取T面板卡的report_uuid
        # ret2 = self.select_data_by_sql_str(
        #     f"select board_uuid from {pcb}_{bom}_boardBindInfo where board_oppoSite_uuid='{b_report_uuid}';"
        # )

        # if not ret2:
        ret2 = self.select_data_by_sql_str(
            f"select board_uuid from {project_id}_boardBindInfo where board_oppoSite_uuid='{b_report_uuid}';"
        )

        if ret2:
            t_report_uuid = ret2[0][0]

            # 3. 获取t面的拼板条码
            ret3 = self.select_data_by_sql_str(
                f"SELECT b.board_index, b.board_sn FROM board b WHERE b.report_uuid = '{t_report_uuid}';"
            )

            t_barcode = {str(i[0]): i[1] for i in ret3}
        else:
            t_barcode = {}

        return t_barcode, b_barcode

    def get_barcode_by_board_uuid(self, board_uuid: str) -> str:
        """
        从数据库获取拼板条码
        :param board_uuid:
        :return:
        """
        ret0 = self.select_data_by_sql_str(
            f"SELECT b.board_index, b.board_sn, b.whole_barcode FROM board b WHERE b.board_uuid = '{board_uuid}';"
        )

        barcode = ''

        if ret0:
            barcode = ret0[0][2]

            if not barcode:
                barcode = ret0[0][1]

        return barcode


def get_mssql_cursor(db_host, username, password, database, db_port=1433):
    """
    获得一个conn

    ps：记得，  cursor.close(), conn.close()
    :param db_host:
    :param username:
    :param password:
    :param database:
    :param db_port:
    :return:
    """
    conn = pymssql.connect(server=db_host, user=username, password=password, database=database, port=db_port)
    return conn


def insert_row_into_table(conn, insert_sql: str):
    """
    插入一条数据到 板卡信息表
    """
    log.info(f"Insert Data To Database, Sql Command：\n{insert_sql}")
    with conn.cursor() as cur:
        cur.execute(insert_sql)

    conn.commit()
    log.info(f"Insert Successful！")


def insert_row_into_table_and_return_primary_id(conn, insert_sql: str) -> int:
    """
    插入一条数据到 板卡信息表 并且返回主键ID
    ps: mssql
    """
    log.info(f"Insert Data To Database, Sql Command：\n{insert_sql}")

    ret_id = 0
    with conn.cursor() as cur:
        cur.execute(insert_sql)

        data = cur.fetchone()
        if data:
            ret_id = data[0]

    conn.commit()
    log.info(f"Insert Successful！")
    return ret_id


def select_one_by_sql_str(conn, sql_str) -> list:
    """
    sql server 根据查询语句，只查询一条记录
    :param conn:
    :param sql_str:
    :return:
    """
    log.info(f"查询语句：{sql_str}")

    with conn.cursor() as cur:
        # 执行查询
        cur.execute(sql_str)

        # 获取查询结果
        rows = cur.fetchall()

    log.info(f"查询结果： {rows}")

    row = []
    if rows:
        row = list(rows[0])

    return row


class SqlLiteHelper(object):
    """
    该类主要给431机型使用
    """
    def __init__(
            self,
            review_path: str
    ):
        self.review_path = review_path
        self.conn = None

    def __enter__(self):
        db_file = ""
        for item in os.listdir(self.review_path):
            if item.endswith(".db"):
                db_file = os.path.join(self.review_path, item)

        log.info(f"parse db file: {db_file}")

        if db_file and os.path.exists(db_file):
            # 连接到数据库（如果不存在则会自动创建）
            self.conn = sqlite3.connect(db_file)
            log.info(f"sqlLite连接成功！")
        else:
            log.warning(f"sqlLite连接失败！")

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.conn:
            self.conn.close()

    def get_board_geometry(self) -> dict:
        """
        获取431图像转换比，以及缩放比
        """
        if self.conn:
            # 创建游标对象
            cursor = self.conn.cursor()
            cursor.execute("""
                            SELECT name 
                            FROM sqlite_master 
                            WHERE type='table' 
                            AND name=?
                        """, ("ReportBoard",))

            is_exist = cursor.fetchone()

            if is_exist:
                # 查询数据
                cursor.execute("SELECT indexId,cx,cy,width,height FROM ReportBoard;")
                all_rows = cursor.fetchall()

                ret_data = {}
                for row in all_rows:
                    if row[0] != 0:
                        ret_data[str(row[0])] = {
                            "cx": row[1],
                            "cy": row[2],
                            "width": row[3],
                            "height": row[4],
                        }

            else:
                print("ReportBoard 表不存在")
                ret_data = {}

            return ret_data

    def get_alg_data(self) -> defaultdict:
        """
        获取算法数据
        """
        data_map = defaultdict(list)  # 用于存储每个uuid对应的data列表

        if self.conn:
            cur = self.conn.cursor()
            try:
                cur.execute("""SELECT
    rc.uuid,
    rc.componentId,
    riw.inspectItemInfo
FROM
    ReportComponent rc
LEFT JOIN
    ReportInspectWindow riw ON rc.componentId = riw.componentId;
""")

                # 获取所有行的结果列表
                rows = cur.fetchall()

                # 遍历结果并打印每一行
                for row in rows:
                    data_map[row[0]].append(row[2])

            finally:
                cur.close()

            print(f"算法数据： {len(data_map)}")

            return data_map

    def get_height_measure_and_color_width_data(self) -> defaultdict:
        """
        获取431机型`3D高度及颜色宽度算法`的数据

        3D高度:heightMeasure
        颜色宽度算法:colorwidth

        {
        "f52cd138-2ed1-4e8d-aee2-c4d67dcc5c14": [
                {
                    "alg_name": "colorwidth",
                    "alg_value": {
                        "displayAlgInfo": "{\"averageWidth\":\"8561.24\",\"maxWidth\":\"8626.29\",\"minWidth\":\"8483.17\"}",
                        "status": false
                    }
                }
            ],
            "f0f274d4-40f6-4d06-b17a-adfca6dff243": [
                {
                    "alg_name": "heightMeasure",
                    "alg_value": {
                        "displayAlgInfo": "{\"PASS/NG\":false,\"height\":\"56.93\",\"maxHeightThreshold\":\"20.00um\",\"minHeightThreshold\":\"-20.00um\",\"rectArray\":[]}",
                        "status": false
                    }
                },
                {
                    "alg_name": "colorwidth",
                    "alg_value": {
                        "displayAlgInfo": "{\"averageWidth\":\"0.00\",\"maxWidth\":\"0.00\",\"minWidth\":\"0.00\"}",
                        "status": true
                    }
                }
            ]
        }
        """
        ret_map = defaultdict(list)
        data_map = self.get_alg_data()

        for k, v in data_map.items():
            for item in v:
                item_json = json.loads(item)

                for data in item_json.values():
                    if "heightMeasure" in data:
                        ret_map[k].append({
                            "alg_name": "heightMeasure",
                            "alg_value": data.get("heightMeasure"),
                        })

                    if "colorwidth" in data:
                        ret_map[k].append({
                            "alg_name": "colorwidth",
                            "alg_value": data.get("colorwidth"),
                        })

        return ret_map


if __name__ == '__main__':
    # sql_service = MySQLService("127.0.0.1", "AIS400")
    # ret = sql_service.get_barcode_by_board_uuid("5ea14824-9506-4b30-9893-815a2337d922")
    # print(ret)

    # with SqlLiteHelper() as sqlite_helper:
    #     data_list = sqlite_helper.get_board_geometry()
    #     # print(data)
    #     for i in data_list:
    #         print(i)
    pass

