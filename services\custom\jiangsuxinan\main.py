# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/8 上午10:18
# Author     ：sch
# version    ：python 3.8
# Description：江苏新安
"""
import os.path
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "jiangsuxinan release v1.0.0.4",
        "device": "AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-08 17:18  条码校验，上传数据
date: 2024-03-12 09:14  增加上传设备编号、站点
date: 2024-05-28 09:19  换行符改成window的换行符，图片路径只要名称
date: 2024-07-24 11:12  增加条码防呆功能
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "Mes接口地址(条码校验)",
            "value": "http://**************:81/api/technical/SnCheckRoute",
        },
        "api_url_data": {
            "ui_name": "Mes接口地址(上传数据)",
            "value": "http://**************:81/api/technical/MIAOITestData",
        },
        "workshop_number": {
            "ui_name": "车间编号",
            "value": "",
        },
        "line_number": {
            "ui_name": "线体编号",
            "value": "",
        },
        "station": {
            "ui_name": "站点",
            "value": "",
        },
        "process": {
            "ui_name": "制程",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备编号",
            "value": "",
        },
        "software_version": {
            "ui_name": "系统软件标识",
            "value": "",
        },
        "device_ip": {
            "ui_name": "设备IP(图片地址前缀)",
            "value": "\\\\**************\\smt13_spc",
        },
    }

    form = {
        "username": {
            "ui_name": "人员工号",
            "value": "",
        },
    }

    path = {
        "save_path_txt": {
            "ui_name": "保存路径(txt文档)",
            "value": "",
        },
        "save_path_img": {
            "ui_name": "保存路径(图片)",
            "value": "",
        },
        "rule_path": {
            "ui_name": "条码规则路径",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        workshop_number = other_vo.get_value_by_cons_key("workshop_number")
        line_number = other_vo.get_value_by_cons_key("line_number")
        station = other_vo.get_value_by_cons_key("station")
        process = other_vo.get_value_by_cons_key("process")
        device_code = other_vo.get_value_by_cons_key("device_code")
        username = other_vo.get_value_by_cons_key("username")
        software_version = other_vo.get_value_by_cons_key("software_version")
        rule_path = other_vo.get_value_by_cons_key("rule_path", not_null=True)

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", f"未扫到条码！")

        rule_filepath = f"{rule_path}/rule.txt"

        if os.path.exists(rule_path):
            project_name = other_vo.get_project_name()

            self.log.info(f"projectName: {project_name}")
            if not project_name:
                return self.x_response("false", f"未获取到板式名参数[projectName]，请检查主软件版本！")

            rule_content = xutil.FileUtil.read_file(rule_filepath)

            rule_content = rule_content.replace("，", ",")

            split_flag = "\n"

            if "\r\n" in rule_content:
                split_flag = "\r\n"
                self.log.info(f"window newLine")

            rule_list = rule_content.split(split_flag)
            new_rule_data = filter(lambda x: "," in x, rule_list)

            rule_map = {}
            for item in new_rule_data:
                row = item.split(",")
                rule_map[row[0]] = row[1]

            if project_name in rule_map:
                rule = rule_map.get(project_name)
                rule_len = len(rule)
                for sn in sn_list:
                    sn_len = len(sn)

                    if rule_len != sn_len:
                        return self.x_response("false", f"条码防呆，条码长度不匹配，测试条码：{sn}  条码规则：{rule}")

                    for c1, c2 in zip(sn, rule):
                        if c2 == "*":
                            # 不需要做匹配
                            continue

                        if c1 != c2:
                            self.log.warning(f"{c1=} {c2=}")
                            return self.x_response("false", f"条码防呆，测试条码与条码规则不匹配，测试条码：{sn}  条码规则：{rule}")

            else:
                self.log.warning(f"该板式[{project_name}]未配置条码规则，无需做条码防呆！如需启用条码防呆，请在配置文件配置规则:{rule_filepath}")
        else:
            self.log.warning(f"条码防呆功能失效，规则文件不存在，{rule_filepath}")

        ret_res = self.x_response()
        for sn in sn_list:
            check_param = {
                "sn": sn,
                "workShop": workshop_number,
                "pL_ID": line_number,
                "station": station,
                "section": process,
                "machineId": device_code,
                "CreateBy": username,
                "sysName": software_version,
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
            if str(ret.get('statusCode')) != '200':
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path_txt = data_vo.get_value_by_cons_key("save_path_txt", not_null=True)
        save_path_img = data_vo.get_value_by_cons_key("save_path_img", not_null=True)

        api_url_data = data_vo.get_value_by_cons_key("api_url_data", not_null=True)
        workshop_number = data_vo.get_value_by_cons_key("workshop_number")
        line_number = data_vo.get_value_by_cons_key("line_number")
        device_code = data_vo.get_value_by_cons_key("device_code")
        software_version = data_vo.get_value_by_cons_key("software_version")
        device_ip = data_vo.get_value_by_cons_key("device_ip")
        station = data_vo.get_value_by_cons_key("station")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        project_name = pcb_entity.project_name
        board_side = pcb_entity.board_side
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        time_year = start_time[:4]
        time_month = start_time[4:6]

        time_ymd = start_time[:8]

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_str = ""

            comp_ng_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_tag = comp_entity.designator
                    c_r_result = comp_entity.get_final_result("OK", "OK", "NG")

                    comp_src_image = comp_entity.image_path

                    if comp_src_image:
                        save_full_path = f"{save_path_img}/{time_year}/{time_year}{time_month}/{time_ymd}"
                        xutil.FileUtil.ensure_dir_exist(save_full_path)
                        comp_dst_img = f"{save_full_path}/{start_time}_{barcode}_{comp_tag}.png"

                        xutil.FileUtil.copy_file(comp_src_image, comp_dst_img)

                        # file_dst_img = f"{device_ip}\\{time_year}\\{time_year}{time_month}\\{time_ymd}\\" \
                        #                f"{start_time}_{barcode}_{comp_tag}.png"
                        file_dst_img = f"{start_time}_{barcode}_{comp_tag}.png"

                    else:
                        file_dst_img = ""

                    comp_ng_str += f"\r\n{comp_tag},FAIL,{c_r_result},{file_dst_img}"

                    comp_ng_list.append({
                        "ReferenceName": comp_tag,
                        "Ngname": comp_entity.repair_ng_str,
                        "Image": file_dst_img
                    })

            repass_number = board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number
            ng_number = board_entity.comp_repair_ng_number
            board_result = board_entity.get_repair_result("OK", "NG")
            board_content = f"{project_name},{board_side},{start_time},{barcode},{board_result}," \
                            f"{board_no},{repass_number},{ng_number}{comp_ng_str}"

            txt_filename = f"{save_path_txt}/{device_code}_{line_number}_{start_time}_{barcode}_{board_no}.txt"
            xutil.FileUtil.write_content_to_file(txt_filename, board_content)

            data_param = {
                "sysName": software_version,
                "DateTime": start_time,
                "Code": barcode,
                "BoardName": project_name,
                "Result": board_entity.get_repair_result("OK", "NG"),
                "FalseCall": repass_number,
                "NG": ng_number,
                "Line": line_number,
                "WorkShop": workshop_number,
                "MachineNo": device_code,
                "Station": station,
                "errorlists": comp_ng_list,
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
            if str(ret.get('statusCode')) != '200':
                ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

        return ret_res
