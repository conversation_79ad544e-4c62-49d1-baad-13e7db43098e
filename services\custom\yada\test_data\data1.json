{"barcode": "<dsWIPTD>\n    <WIPTDHeader>\n        <IntSerial>{barcode}</IntSerial>\n        <Process>{process}</Process>\n        <OperatorName>{operator}</OperatorName>\n        <Tester>{device_name}</Tester>\n        <ProgramName>{program_name}</ProgramName>\n        <ProgramVersion>v1.0.0.1</ProgramVersion>\n        <IPSNo>{ips_no}</IPSNo>\n        <IPSVersion>{ips_version}</IPSVersion>\n        <Remark>{remark}</Remark>\n    </WIPTDHeader>\n    <WIPTDItem>\n        <Item>{ix}</Item>\n        <TestStep>{test_step}</TestStep>\n        <TestName>{test_name}</TestName>\n        <OutputName />\n        <InputCondition />\n        <OutputLoading />\n        <LowerLimit>{lower_limit}</LowerLimit>\n        <Result>{result}</Result>\n        <UpperLimit>{upper_limit}</UpperLimit>\n        <Unit />\n        <Status>{status}</Status>\n        <IPSReference />\n        <TestID>{ix}</TestID>\n    </WIPTDItem>\n</dsWIPTD>\n"}