# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/12 下午3:30
# Author     ：sch
# version    ：python 3.8
# Description：天津敏宁
"""
from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

txt_board_board_template = """设备名称:{device_name}
整板条码:{pcb_sn}
轨道:{pcb_track_line}
面别:{pcb_board_side}
测试时间:{pcb_test_time}
测试耗时:{pcb_cycle_time}
程序名:{pcb_project_name}
整板检测结果:{pcb_robot_result}
整板复判结果:{pcb_user_result}
整板最终结果:{pcb_final_result}
复判操作员:{pcb_repair_user}
拼板数量:{pcb_board_number}
整板器件总数:{pcb_comp_number}
拼板序号:{board_no}
拼板条码:{board_sn}
拼板检测结果:{board_robot_result}
拼板复判结果:{board_user_result}
拼板最终结果:{board_final_result}
拼板器件数量:{board_comp_number}
拼板器件检测NG总数:{board_comp_robot_ng_number}
拼板器件复判NG总数:{board_comp_user_ng_number}
拼板器件误报总数:{board_comp_repass_number}

{CompData}
"""

txt_comp_board_template = """
位号:{comp_designator}----料号:{comp_part}----封装:{comp_package}----类型:{comp_type}----检测不良代码:{comp_robot_code}----检测结果:{comp_robot_result}----复判不良代码:{comp_user_code}----复判结果:{comp_user_result}----器件图片:{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "tianjinminning release v1.0.0.6",
        "device": "40x,501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-12 15:40  init
date: 2024-06-12 17:10  上传数据到MES
date: 2024-06-13 16:40  整板图默认存到第一拼版的文件夹里
date: 2024-09-13 15:33  新增从MES获取条码功能（根据固定规则生成条码）
date: 2024-09-14 16:42  上传数据时，使用特定的规则生成拼版条码
date: 2024-09-20 09:44  修改换行符为window换行符
""", }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    path = {
        "save_path": {
            "ui_name": "保存路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        save_path = data_vo.get_value_by_cons_key("save_path")
        device_name = data_vo.get_value_by_cons_key("device_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = pcb_entity.get_start_time().strftime(xcons.FMT_DATE)

        project_name = pcb_entity.project_name

        only_sn = pcb_sn

        # the_first_sn = ""
        # the_first_no = ""

        the_first_path = ""

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode

            if not only_sn and barcode:
                only_sn = barcode

        self.log.info(f"only sn: {only_sn}")

        if "-" in only_sn:
            split_flag = "-"
        elif "_" in only_sn:
            split_flag = "_"
        else:
            split_flag = "_"

        main_sn = only_sn.split(split_flag)[0]
        self.log.info(f"main sn: {main_sn}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            # barcode = board_entity.barcode
            board_no = board_entity.board_no

            # if not the_first_no:
            #     board_no = the_first_no
            #
            # if not the_first_sn:
            #     the_first_sn = barcode

            # if not only_sn and barcode:
            #     only_sn = barcode

            barcode = f"{main_sn}_{board_no}"
            self.log.info(f"条码已变更为：{barcode}")

            full_path = f"{save_path}/{date_file}/{project_name}/{board_no}_{time_file}_{barcode}"
            xutil.FileUtil.ensure_dir_exist(full_path)

            if not the_first_path:
                the_first_path = full_path

            txt_comp_data = ""
            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator

                comp_result = comp_entity.get_final_result("OK", "PASS", "FAIL")

                if comp_result in ["PASS", "FAIL"]:

                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        if comp_result == "PASS":
                            # 误报
                            comp_dst_filename = f"{full_path}/{comp_tag}_{comp_entity.robot_ng_str}_{comp_result}.png"
                        else:
                            # NG
                            comp_dst_filename = f"{full_path}/{comp_tag}_{comp_entity.repair_ng_str}_{comp_result}.png"

                        xutil.FileUtil.copy_file(comp_src_img, comp_dst_filename)
                    else:
                        comp_dst_filename = ""

                else:
                    comp_dst_filename = ""

                txt_comp_data += txt_comp_board_template.format(**{
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_dst_filename,
                })

            board_data_fmt = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "CompData": txt_comp_data
            }

            board_data = txt_board_board_template.format(**board_data_fmt)

            board_result = board_entity.get_repair_result("PASS", "FAIL")
            dst_filename = f"{full_path}/{time_file}_{barcode}_{board_result}.txt"
            xutil.FileUtil.write_content_to_file(dst_filename, board_data, window_line=True)

        all_pcb_image = pcb_entity.list_all_pcb_image()

        for item in all_pcb_image:
            if "B_" in item:
                board_side = "B"
            else:
                board_side = "T"

            # full_path1 = f"{save_path}/{date_file}/{project_name}/{the_first_no}_{time_file}_{the_first_sn}"
            # full_path1 = f"{save_path}/{date_file}/{project_name}/{the_first_no}_{time_file}_{the_first_sn}"
            # xutil.FileUtil.ensure_dir_exist(full_path1)

            if the_first_path:
                pcb_result = pcb_entity.get_repair_result("PASS", "FAIL")
                dst_filename1 = f"{the_first_path}/{board_side}_{project_name}_{time_file}_{pcb_result}.jpg"
                xutil.FileUtil.copy_file(item, dst_filename1)
            else:
                self.log.warning(f"找不到路径保存整板图！")

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        pcb_sn = other_vo.get_pcb_sn()
        barcode_map = other_vo.get_barcode_map()

        data_len = len(barcode_map)
        sn_number = data_len - 2

        if "-" in pcb_sn:
            split_flag = "-"
        elif "_" in pcb_sn:
            split_flag = "_"
        else:
            split_flag = "_"

        main_sn = pcb_sn.split(split_flag)[0]

        ret_sn = []
        for i in range(1, sn_number + 1):
            ret_sn.append(f"{main_sn}_{i}")

        return self.x_response("true", ",".join(ret_sn))
