# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : compress_and_hex_img.py
# Time       ：2025/4/28 下午3:39
# Author     ：sch
# version    ：python 3.8
# Description：
"""

from services.custom.shenzhenjiweng.jw_module import compress_image_list_to_hex

# 示例调用
if __name__ == "__main__":
    # hex_str = compress_image_to_hex("./qt.png")
    #
    # print(hex_str)
    # print(type(hex_str))
    # print(len(hex_str))

    ret = compress_image_list_to_hex([
        "./qt.png",
        "./qt.png",

    ])
    # print(ret)
    # print(type(ret))
    print(ret.decode('utf8'))
