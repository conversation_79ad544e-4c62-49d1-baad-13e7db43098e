<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CronSettingDialog</class>
 <widget class="QDialog" name="CronSettingDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>441</width>
    <height>233</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>定时设置</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QCheckBox" name="check_is_cron_clear">
        <property name="text">
         <string>开启定时清除功能</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_2">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QFormLayout" name="formLayout">
         <item row="0" column="0">
          <widget class="QRadioButton" name="radio_fixed_time_clear">
           <property name="text">
            <string>固定时间清除</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QFrame" name="frame_3">
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QCheckBox" name="check_fixed_status_1">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTimeEdit" name="edit_fixed_time_1">
              <property name="displayFormat">
               <string>HH:mm</string>
              </property>
              <property name="time">
               <time>
                <hour>8</hour>
                <minute>0</minute>
                <second>0</second>
               </time>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="check_fixed_status_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTimeEdit" name="edit_fixed_time_2">
              <property name="displayFormat">
               <string>HH:mm</string>
              </property>
              <property name="time">
               <time>
                <hour>18</hour>
                <minute>0</minute>
                <second>0</second>
               </time>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="1" column="0" colspan="2">
          <widget class="Line" name="line">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QRadioButton" name="radio_interval_time_clear">
           <property name="text">
            <string>间隔时间清除</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QFrame" name="frame_4">
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QSpinBox" name="spin_interval_time">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="value">
               <number>1</number>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>小时</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="3" column="0" colspan="2">
          <widget class="QLabel" name="label_2">
           <property name="styleSheet">
            <string notr="true">color:red</string>
           </property>
           <property name="text">
            <string>修改定时设置后重启本软件生效！！！</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>CronSettingDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>CronSettingDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
