# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/26 下午2:14
# Author     ：sch
# version    ：python 3.8
# Description：石家庄海康

备注：1线不需要发送条码，2线需要发送条码（配置项：是否传递条码）

需求：若识别到子板条码为 0，则该子板结果发送为 NG      ----> 仅为2线需求
"""
import string
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "shijiazhuanghaikang release v1.0.0.5",
        "device": "AIS630",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-26 16:03  上传测试数据
date: 2024-07-29 09:17  修改上传数据格式
date: 2024-09-06 14:29  增加配置项：是否传递条码
date: 2024-09-13 20:01  修改请求参数
date: 2025-02-13 09:33  配置传递条码时，未识别到条码 则将子板结果设置为NG
""",
    }

    form = {
        "api_host": {
            "ui_name": "tcp host",
            "value": "*************",
        },
        "api_port": {
            "ui_name": "tcp port",
            "value": "2000",
        }
    }

    other_form = {}
    other_combo = {
        "api_timeout": {
            "ui_name": "接口超时时间(秒)",
            "item": ["1", "2", "3", "5", "10", "15"],
            "value": "5",
        },
        "is_send_sn": {
            "ui_name": "是否传递条码",
            "item": ["Yes", "No"],
            "value": "No",
        },

    }

    button = {
        "test_connect": {
            "ui_name": "testConnect",
        }
    }

    def __init__(self):
        self.app_setting["error_code_device"] = "AIS63X"

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        api_port = data_vo.get_value_by_cons_key("api_port", to_int=True)
        api_timeout = data_vo.get_value_by_cons_key("api_timeout", to_int=True)
        is_send_sn = data_vo.get_value_by_cons_key("is_send_sn")

        self.log.info(f"是否传递条码：{is_send_sn}")

        if other_data.get("inspect_type") == "inspector":
            is_repaired = "2"  # 非人为复判
        else:
            is_repaired = "1"  # 人为复判

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_index_str = string.ascii_uppercase * 5

        project_name = pcb_entity.project_name

        pcb_data = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            any_x_offset = ""
            any_y_offset = ""

            area_total = 0
            height_list = []
            is_lianxi = "1"  # 默认没有连锡，有连锡，则发2

            for comp_entity in board_entity.yield_comp_entity():
                for alg_entity in comp_entity.yield_alg_entity():
                    if alg_entity.test_name == "XOffset" and not any_x_offset:
                        any_x_offset = alg_entity.test_val

                    elif alg_entity.test_name == "YOffset" and not any_y_offset:
                        any_y_offset = alg_entity.test_val

                    elif alg_entity.test_name == "Area":
                        area_total += float(alg_entity.test_val)

                    elif alg_entity.test_name == "Height":
                        height_list.append(float(alg_entity.test_val))

                if comp_entity.repair_ng_code == "4":
                    is_lianxi = "2"

            board_result = board_entity.get_repair_result("1", "2")
            height_list.sort()

            # self.log.info(f"all height: {height_list}")

            if height_list:
                low_height = height_list[0]
                upper_height = height_list[-1]
            else:
                low_height = 0
                upper_height = 0

            board_no_int = int(board_entity.board_no)

            board_flag = board_index_str[board_no_int - 1]

            board_param = [
                board_result,
                f"{any_x_offset},{any_y_offset}",
                str(int(area_total)),
                f"{low_height},{upper_height}",
                is_lianxi,
                project_name,
                is_repaired
            ]

            if not barcode:
                barcode = "0"

            if is_send_sn == "Yes":
                if barcode == "0":
                    board_param[0] = "2"
                    self.log.warning(f"未识别到条码（也就是条码为0），已将结果设置为NG！")

                board_param.insert(1, barcode)

            pcb_data += f"{board_flag}:{';'.join(board_param)};"

        if is_send_sn == "Yes":
            b_flag2 = "条码;"
        else:
            b_flag2 = ""

        self.log.info(f"格式参考--->   A:测试结果;{b_flag2}x偏移,y偏移;总面积;最小高度,最大高度;是否有连锡;程序名;是否人工复判;")
        xrequest.SocketUtil.send_data_to_socket_server(api_host, api_port, pcb_data, timeout=api_timeout)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "test_connect":
            api_host = btn_vo.get_value_by_cons_key("api_host")
            api_port = btn_vo.get_value_by_cons_key("api_port", to_int=True)
            is_conn = xrequest.SocketUtil.check_window_port(api_host, api_port)
            if not is_conn:
                err_msg = f"连接服务器失败，请检查网络或者接口服务[tcp://{api_host}:{api_port}]是否正常启动！"
                return self.x_response("false", err_msg)

        return self.x_response()
