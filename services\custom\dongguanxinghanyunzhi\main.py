"""
# File       : main.py
# Time       ：2025/06/04 17:13
# Author     ："wxc"
# version    ：python 3.8
# Description：东莞兴汉云智
"""
import re
from typing import Any

from common import xrequest, xcons
from collections import Counter
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo

upload_template = """04;{work_id};{sn};{process_name};{res_name};{dev_code};{fixed_sn};{test_time};{comp_str}"""
comp_template = """{test_name}:{test_val}"""
barcode_pass_template = """05;{work_id};{sn};{process_name};{res_name};{dev_code};{fixed_sn};{final_result};{user_ng_str}"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["东莞兴汉云智", "dongguanxinghanyunzhi"],
        "version": "release v1.0.0.2",
        "device": "501",
        "feature": ["条码校验", "上传数据", "过站"],
        "author": "wxc",
        "release": """
date: 2025-06-04 17:20 ATAOI_2019-40025 条码校验、上传数据、过站
date: 2025-06-04 17:20 修改参数工治具编码:fixed_sn默认值为""，修改报错返回数组越界问题
"""
    }
    form = {
        "work_id": {
            "ui_name": "工号",
            "value": ""
        },
        "process_name": {
            "ui_name": "工序名称",
            "value": ""
        },
        "res_name": {
            "ui_name": "资源名称",
            "value": ""
        },
        "dev_code": {
            "ui_name": "设备编码",
            "value": "",
        },
    }

    other_form = {
        "sum_api": {
            "ui_name": "接口地址",
            "value": ""
        },

    }

    @staticmethod
    def send_upload_data_to_mes(sum_api: str, work_id: str, process_name: str, res_name: str, dev_code: str,
                                comp_str: str, barcode: str, test_time: str, fixed_sn: str = "") -> list:
        """
        发送结果数据到MES系统
        :param sum_api: MES接口地址
        :param work_id: 工号
        :param process_name: 工序名称
        :param res_name: 资源名称
        :param dev_code: 设备编码
        :param comp_str: 测试数据字符串
        :param barcode: 条码
        :param test_time: 测试时间
        :param fixed_sn: 工治具编码（可选）
        """
        ret = xrequest.RequestUtil.get(sum_api,
                                       {"commandString": upload_template.format(
                                           work_id=work_id,
                                           sn=barcode,
                                           process_name=process_name,
                                           res_name=res_name,
                                           dev_code=dev_code,
                                           fixed_sn=fixed_sn,
                                           test_time=test_time,
                                           comp_str=comp_str)}, to_json=False)
        match = re.search(r'<string[^>]*>(.*?)</string>', ret, re.DOTALL)
        ret_content = []
        if match:
            ret_content = match.group(1)
            ret_content = ret_content.split(";")
            return ret_content
        else:
            return ret_content

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        sum_api = other_vo.get_value_by_cons_key("sum_api")
        work_id = other_vo.get_value_by_cons_key("work_id")
        process_name = other_vo.get_value_by_cons_key("process_name")
        res_name = other_vo.get_value_by_cons_key("res_name")
        dev_code = other_vo.get_value_by_cons_key("dev_code")
        sn_list = other_vo.list_sn()
        for sn in sn_list:
            ret = xrequest.RequestUtil.get(sum_api,
                                           {"commandString": f"01;{work_id};{sn};{process_name};{res_name};{dev_code}"},
                                           to_json=False)
            match = re.search(r'<string[^>]*>(.*?)</string>', ret, re.DOTALL)
            if match:
                ret_content = match.group(1)
                ret_content = ret_content.split(";")
                if "NG" in ret_content:
                    return self.x_response("false", f"mes接口异常: error：{ret_content[1]}")
            else:
                return self.x_response("false", f"mes接口异常: 返回数据为空,Response:{ret}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        sum_api = data_vo.get_value_by_cons_key("sum_api")
        work_id = data_vo.get_value_by_cons_key("work_id")
        process_name = data_vo.get_value_by_cons_key("process_name")
        res_name = data_vo.get_value_by_cons_key("res_name")
        dev_code = data_vo.get_value_by_cons_key("dev_code")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            test_data = []
            comp_user_ng_str = []
            for comp_entity in board_entity.yield_comp_entity():
                # 添加复判不良描述
                if comp_entity.is_repair_ng():
                    comp_user_ng_str.append(comp_entity.repair_ng_str)
                for alg in comp_entity.yield_alg_entity():
                    if alg.result != "0":
                        test_data.append({
                            alg.test_name: alg.test_val
                        })

            # 算法名称最多传7个
            while len(test_data) > 7:
                ix = 0
                not7_comp_str = ""
                for item in test_data:
                    not7_comp_str += ";".join(
                        [comp_template.format(test_name=k, test_val=v) for k, v in item.items()])
                    ix += 1
                    if ix >= 7:
                        break
                del test_data[:7]
                ret = self.send_upload_data_to_mes(sum_api, work_id, process_name, res_name, dev_code, not7_comp_str,
                                                   barcode,
                                                   test_time)
                if "OK" not in ret:
                    return self.x_response("false", f"mes接口异常: 序号过站error：{ret}")
            # 处理剩余的测试数据
            if test_data:
                comp_str = ""
                for item in test_data:
                    comp_str += ";".join(
                        [comp_template.format(test_name=k, test_val=v) for k, v in item.items()])
                ret = self.send_upload_data_to_mes(sum_api, work_id, process_name, res_name, dev_code, comp_str,
                                                   barcode,
                                                   test_time)
                if ret[0] != "OK":
                    return self.x_response("false", f"mes接口异常: 序号过站error：{ret}")

            # 取不良描述最多为代表
            counter = Counter(comp_user_ng_str)  # 统计每个不良描述的出现次数
            max_ng_str = ""
            if counter:
                # 找出出现次数最多的元素
                max_ng_str = counter.most_common(1)[0][0]  # 返回 (元素, 次数) 的列表，取第一个
            # 序号过站
            ret = xrequest.RequestUtil.get(sum_api,
                                           {"commandString": barcode_pass_template.format(
                                               work_id=work_id,
                                               sn=barcode,
                                               process_name=process_name,
                                               res_name=res_name,
                                               dev_code=dev_code,
                                               fixed_sn="",
                                               final_result=board_entity.get_final_result("OK", "OK", "NG"),
                                               user_ng_str=max_ng_str
                                           )}, to_json=False)
            match = re.search(r'<string[^>]*>(.*?)</string>', ret, re.DOTALL)
            if match:
                ret_content = match.group(1)
                ret_content = ret_content.split(";")
                if "NG" in ret_content:
                    return self.x_response("false", f"mes接口异常: error：{ret_content[1]}")
            else:
                return self.x_response("false", f"mes接口异常: 返回数据为空,Response:{ret}")
        return self.x_response()
