# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/4/26 上午11:00
# Author     ：sch
# version    ：python 3.8
# Description：重庆九洲
"""
from typing import Any

from common import xrequest, xutil, xcons
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine
from vo.mes_vo import DataVo

pcb_template = """
<Panel projectName="{pcb_project_name}" pcbBarcode="{pcb_sn}" pcbUserResult="{pcb_user_result}" pcbFinalResult="{pcb_final_result}" testTime="{pcb_test_time}" repairUser="{pcb_repair_user}">
<BoardList>
{BoardData}
</BoardList>
<Data boardNumber="{pcb_board_number}" boardUserNgNumber="{pcb_board_user_ng_number}" ver=""/>
</Panel>
"""

board_template = """      <Board barcode="{board_sn}" id="{board_no}" userResult="{board_user_result}" finalResult="{board_final_result}">
{CompData}
      </Board>
"""

comp_template = """        <Comp designator="{comp_designator}" part="{comp_part}" package="{comp_package}" type="{comp_type}" robotCode="{comp_robot_code}" robotResult="{comp_robot_result}" userCode="{comp_user_code}" userResult="{comp_user_result}" compImagePath="{comp_image}"/>
"""


class Engine(BaseEngine):
    version = {
        "title": "chongqingjiuzhou release v1.0.0.15",
        "device": "40x",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-04-25 11:07  init
date: 2023-04-26 17:55  上传数据
date: 2023-04-27 11:04  不需要xml头信息
date: 2023-04-27 15:31  NG图片保存路径增加条码文件夹
date: 2023-04-27 19:49  result->repair result
date: 2023-05-08 19:07  在ftp创建文件夹时*变成_
date: 2023-06-14 10:33  增加异常打印
date: 2025-01-14 15:25  检测阶段PASS时发送一次(包含图片)，NG时发送一次(包含检测NG器件和图片)，复判阶段检测PASS不发送，检测NG时发送一次(只包含复判NG器件,不含图片)，图片处理阶段只在检测阶段上传，只上传检测NG的器件图片
date: 2025-01-23 17:45  jira:36527,修改上传ftp的路径，修改上传逻辑
date: 2025-02-25 14:17  jira:36527,修改接口发送的xml字段,增加误报时只上传复判OK的数据
date: 2025-03-03 15:08  jira:36527,机器检测PASS时，每个Board中也加上传整板图信息和路径
date: 2025-03-27 16:00  bugfix:在复判数据中找不到ftp上传整板图路径，原因是：生成ftp路径的时间使用当前时间导致在机器检测和复判阶段不一致
""",
    }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://127.0.0.1:8081/WebService/AOITestService.asmx/LC_AOISaveTestData1"
        },
        "resource_name_inspector": {
            "ui_name": "检测资源名称",
            "value": ""
        },
        "station_name_inspector": {
            "ui_name": "检测工站名称",
            "value": ""
        },
        "resource_name_repair": {
            "ui_name": "复判资源名称",
            "value": ""
        },
        "station_name_repair": {
            "ui_name": "复判工站名称",
            "value": ""
        },
    }

    combo = {
        "cons_type": {
            "ui_name": "类型",
            "item": [
                "SMT线",
                "涂覆线"
            ],
            "value": "SMT线",
        }
    }

    type_map = {
        "SMT线": "0",
        "涂覆线": "1",
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")
        cons_type = data_vo.get_value_by_cons_key("cons_type")
        send_type = data_vo.get_inspect_type()

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP Port必须为数字！error： {err}")

        # 判断是否为检测阶段
        is_inspection = send_type == "inspector"
        is_pass = pcb_entity.get_robot_result("PASS", "FAIL") == "PASS"
        is_pass2 = pcb_entity.pcb_repair_result

        board_data = ""
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0
        board_robot_ng_count = 0
        board_user_ng_count = 0

        pcb_sn = pcb_entity.pcb_barcode
        time_now = xutil.DateUtil.get_datetime_now()

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()

        ftp_path_base = ftp_path

        inspect_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        inspect_date = pcb_entity.get_start_time().strftime("%Y%m%d")
        sub_dir = pcb_sn if pcb_sn else inspect_time
        ftp_path_current = f"{ftp_path_base}/{inspect_date}/{sub_dir}"

        # # 存储整版图路径信息
        light_image_paths = []
        pcb_image_path = pcb_entity.list_all_pcb_image()
        pcb_ftp_path = f"{ftp_path_base}/{inspect_date}/{sub_dir}"
        for index, _ in enumerate(pcb_image_path):
            pcb_image_name = f"{pcb_sn}_{inspect_time}_light{index}.jpg"
            light_image_paths.append(f"{pcb_ftp_path}/{pcb_image_name}")

        # 只在检测阶段上传整版图
        if is_inspection:
            ftp_client.cd_or_mkdir(pcb_ftp_path)
            for index, image_path in enumerate(pcb_image_path):
                pcb_image_name = f"{pcb_sn}_{inspect_time}_light{index}.jpg"
                self.log.info(f"上传整版图: {pcb_image_name}")
                ftp_client.upload_file(image_path, pcb_image_name)

        # 记录处理状态
        if is_inspection:
            if is_pass:
                self.log.info("当前是检测阶段且检测PASS，只上传整版图")
            else:
                self.log.info("当前是检测阶段且检测NG，将处理NG器件并上传图片")
        else:
            if is_pass2:
                self.log.info("当前是复判阶段且复判PASS(误报)，将处理复判OK的器件")
            else:
                self.log.info("当前是复判阶段且复判NG，将只处理NG器件")

        light_data = ""
        for i, light_path in enumerate(light_image_paths):
            light_data += comp_template.format(**{
                "comp_designator": f"Light{i}",
                "comp_part": "",
                "comp_package": "",
                "comp_type": "",
                "comp_robot_code": "0" if pcb_entity.pcb_robot_result else "1",
                "comp_robot_result": "OK" if pcb_entity.pcb_robot_result else "NG",
                "comp_user_code": "0" if pcb_entity.pcb_repair_result else "1",
                "comp_user_result": "OK" if pcb_entity.pcb_repair_result else "NG",
                "comp_image": light_path
            })

        for board_entity in pcb_entity.yield_board_entity():
            barcode = board_entity.barcode or time_now
            board_no = board_entity.board_no
            barcode_safe = barcode.replace("*", "_")

            if not pcb_sn and barcode:
                pcb_sn = barcode

            # 更新计数器
            if not board_entity.robot_result:
                board_robot_ng_count += 1
                if board_entity.repair_result:
                    board_user_ng_count += 1

            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_data = ""

            is_cd_dir = False

            if is_inspection and is_pass:
                # 机器检测PASS时，每个Board中也加上传整板图信息和路径
                comp_data = light_data
            else:
                for comp_entity in board_entity.yield_comp_entity():
                    # 检测阶段NG时只处理NG器件
                    if is_inspection:
                        if comp_entity.robot_result:  # 不是NG器件
                            continue

                        # 处理NG器件图片
                        comp_src_image = comp_entity.image_path
                        comp_tag = comp_entity.designator
                        comp_ftp_path = ""

                        # 上传NG器件图片
                        if comp_src_image and ftp_client:
                            if not is_cd_dir:
                                ftp_client.cd_or_mkdir(ftp_path_current)
                                self.log.info(f"创建NG器件图片目录: {ftp_path_current}")
                                is_cd_dir = True

                            comp_file_name = f"{barcode_safe}_{board_no}_{comp_tag}.png"
                            try:
                                ftp_client.upload_file(comp_src_image, comp_file_name)
                            except Exception as e:
                                self.log.error(f"上传NG器件图片失败: {comp_file_name}, 错误: {str(e)}")
                            comp_ftp_path = f"{ftp_path_current}/{comp_file_name}"

                    else:
                        # 复判PASS传全部复判PASS器件，复判NG只传复判不良器件
                        if is_pass2:
                            # 机器检测PASS的不传
                            if comp_entity.robot_result:
                                continue
                        else:
                            if comp_entity.repair_result:  # 不是复判NG器件
                                continue

                        comp_tag = comp_entity.designator
                        comp_ftp_path = ""
                        comp_src_image = comp_entity.image_path
                        if comp_src_image:
                            comp_file_name = f"{barcode_safe}_{board_no}_{comp_tag}.png"
                            comp_ftp_path = f"{ftp_path_current}/{comp_file_name}"

                    # 添加器件数据
                    comp_data += comp_template.format(**{
                        "comp_designator": comp_tag,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code if comp_entity.repair_ng_code else "",
                        "comp_user_result": (comp_entity.repair_ng_str or "").replace("&",
                                                                                      " ") if comp_entity.repair_ng_str else "",
                        "comp_image": comp_ftp_path
                    })

                # 所有器件处理完后，将Light数据添加到comp_data
                if comp_data:
                    comp_data += light_data

            board_data += board_template.format(**{
                "board_sn": barcode_safe,
                "board_no": board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "CompData": comp_data.rstrip()
            })

        ftp_client.close()

        pcb_data = pcb_template.format(**{
            "pcb_sn": pcb_sn,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_user_ng_count,
            "BoardData": board_data.rstrip(),
        })

        # 区分检测工序
        if send_type == "inspector":
            resource_name = data_vo.get_value_by_cons_key("resource_name_inspector")
            station_name = data_vo.get_value_by_cons_key("station_name_inspector")
        else:
            resource_name = data_vo.get_value_by_cons_key("resource_name_repair")
            station_name = data_vo.get_value_by_cons_key("station_name_repair")

        data_param = {
            "type": self.type_map.get(cons_type),
            "snCode": pcb_sn,
            "resourceName": resource_name,
            "stationName": station_name,
            "result": pcb_entity.get_repair_result("OK", "NG"),
            "xmlData": pcb_data,
        }

        self.log.info("==== Begin sending data to MES ====")
        self.log.info(f"API URL: {api_url}")
        ret_str = xrequest.RequestUtil.post_form(api_url, data_param, to_json=False)
        self.log.info(f"MES Response: {ret_str}")
        self.log.info("==== End sending data to MES ====")

        res = xutil.XmlUtil.get_xml_root_by_str(ret_str).text

        if not res.startswith("OK"):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{res}")

        return self.x_response()
