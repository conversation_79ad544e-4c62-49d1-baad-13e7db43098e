# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/24 下午7:12
# Author     ：sch
# version    ：python 3.8
# Description：广州菲利斯    https://jira.cvte.com/browse/ATAOI_2019-38148
"""

from typing import Any

from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "guangzhoufeilisi release v1.0.0.1",
        "device": "AIS203、AIS303、AIS401",
        "feature": ["获取条码"],
        "author": "sunchangheng",
        "release": """
date: 2025-03-24 19:29  生成条码功能
""", }

    combo = {
        "split_last_index": {
            "ui_name": "使用后N位条码生成条码",
            "item": [str(i) for i in range(1, 11)],
            "value": "5"
        }
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        split_last_index = other_vo.get_value_by_cons_key("split_last_index", to_int=True)

        barcode_map = other_vo.get_barcode_map()

        if not barcode_map:
            return self.x_response("false", f"未接收到barcodeList参数，请检查主软件版本！")

        if "-2" in barcode_map:
            del barcode_map["-2"]

        if "-1" in barcode_map:
            del barcode_map["-1"]

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", f"未扫到条码！")

        board_number = len(barcode_map)

        part0 = sn_list[:-split_last_index]
        part1 = sn_list[-split_last_index:]

        ret_sn_list = []
        for i in range(0, board_number):
            tmp_part = str(int(part1) + i).zfill(split_last_index)
            ret_sn_list.append(f"{part0}{tmp_part}")

        return self.x_response("true", ",".join(ret_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        return self.x_response()
