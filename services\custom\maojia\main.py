# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/9/5 上午10:13
# Author     ：sch
# version    ：python 3.8
# Description：茂佳 https://jira.cvte.com/browse/ATAOI_2019-24037
"""
import asyncio
import base64
import json
import time
from typing import Any
import httpx
from common import xcons, xutil, xrequest
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine
from services.custom.maojia import xdata
from minio import Minio
from minio.error import S3Error

global_data = {
    "last_status": "停机",
    "status_code": "02",
    "status_desc": "停止检测"
}


async def http_mes(api_url, json_param: dict, headers: dict):
    '''
    定义一个统一的内部返回数据
    response = {
        'code': 0, # 成功0，失败-1
        'message':'', # 失败时记录错误信息
        'data':{} # 成功时存放服务器返回的json数据
    }
    '''
    try:
        xutil.log.info(f"请求地址：{api_url} Body Json 请求参数：{json.dumps(json_param, ensure_ascii=False)} Headers: {headers}")
        async with httpx.AsyncClient(verify=False) as client:
            res = await client.post(api_url, json=json_param, headers=headers)
            xutil.log.info(f"接口响应：{res}  msg:{res.text.rstrip()}")
            try:
                ret_data = res.json()
                response = {
                    'code': 0,
                    'message': '',
                    'data': ret_data
                }
                return response
            except Exception as e:
                xutil.log.info(f"返回非json数据：error:{e}")
                err_msg = res.text.rstrip()
                response = {
                    'code': -1,
                    'message': err_msg,
                    'data': None
                }
                return response
    except Exception as e:
        xutil.log.info(f"网络异常，error: {e}")
        response = {
            'code': -1,
            'message': e,
            'data': None
        }
        return response


def get_org_str(v: str) -> str:
    return base64.b64decode(v).decode('utf-8')


class Engine(BaseEngine):
    version = {
        "customer": ["惠州茂佳", "maojia"],
        "version": "release v1.0.0.21",
        "device": "203、303",
        "feature": ["条码校验", "设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-09-05 10:38  init
date: 2023-11-02 17:38  增加设备状态接口URL，以及 ArrayStatus 改成全大写
date: 2023-11-03 14:16  上传文件接口参数调整
date: 2023-11-10 09:29  屏蔽调用dll接口上传图片功能，待改为window中转上传
date: 2023-11-10 11:03  增加window中转图片上传功能
date: 2023-11-29 13:52  使用http上传图片
date: 2024-02-28 10:43  图片存储路径更改为： /年/月/日/时/xxxx_file.jpg
date: 2024-03-27 14:46  图片存储路径更改为： /年/月/日/时/xxxx_file.jpg  bugfix
date: 2024-03-27 16:12  图片存储路径更改为： 年/月/日/时/xxxx_file.jpg  bugfix 1
date: 2025-04-15 09:40  增加复判后不良图片上传，和整板图上传在一起
date: 2025-07-11 10:48  增加误报图片上传
""", }

    ERROR_MAP = {
        "AIS203/AIS303/AIS40X/AIS50x": {
            "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
            "2": {"standard": "错件", "custom_code": "2", "custom_str": "WrongPart"},
            "3": {"standard": "反件", "custom_code": "3", "custom_str": "ReversePart"},
            "4": {"standard": "立碑", "custom_code": "4", "custom_str": "Tomstone"},
            "5": {"standard": "偏移", "custom_code": "5", "custom_str": "ShiftPart"},
            "6": {"standard": "翻转", "custom_code": "6", "custom_str": "UpsideDown"},
            "7": {"standard": "浮高", "custom_code": "7", "custom_str": "LiftedPackage"},
            "8": {"standard": "损件", "custom_code": "8", "custom_str": "Broken"},
            "9": {"standard": "露铜", "custom_code": "9", "custom_str": "ExposeCopper"},
            "10": {"standard": "少锡", "custom_code": "10", "custom_str": "InsufficientSolder"},
            "11": {"standard": "多锡", "custom_code": "11", "custom_str": "ExcessSolder"},
            "12": {"standard": "未出脚", "custom_code": "12", "custom_str": "NoPin"},
            "13": {"standard": "孔洞", "custom_code": "13", "custom_str": "PinHole"},
            "14": {"standard": "连锡", "custom_code": "14", "custom_str": "Bridge"},
            "15": {"standard": "锡珠", "custom_code": "15", "custom_str": "SolderBall"},
            "16": {"standard": "翘脚", "custom_code": "16", "custom_str": "LiftedLead"},
            "17": {"standard": "弯脚", "custom_code": "17", "custom_str": "ShiftedLead"},
            "18": {"standard": "异物", "custom_code": "18", "custom_str": "ForeignMaterial"},
            "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "BarcodeRecognition"},
            "20": {"standard": "Marker搜索", "custom_code": "20", "custom_str": "MarkerSearch"},
            "21": {"standard": "多件", "custom_code": "21", "custom_str": "ForeignPart"},
            "22": {"standard": "溢胶", "custom_code": "22", "custom_str": "Overflow"},
            "23": {"standard": "虚焊", "custom_code": "23", "custom_str": "IncompleteWeld"},
            "24": {"standard": "脏污", "custom_code": "24", "custom_str": "Dirty"},
            "25": {"standard": "坏板", "custom_code": "25", "custom_str": "BadPanel"},
            "26": {"standard": "定位", "custom_code": "26", "custom_str": "Locate"},
            "27": {"standard": "数目错误", "custom_code": "27", "custom_str": "CountError"},
            "28": {"standard": "少涂/多涂", "custom_code": "28", "custom_str": "LessMoreCoating"},
            "29": {"standard": "少涂", "custom_code": "29", "custom_str": "LessCoating"},
            "30": {"standard": "多涂", "custom_code": "30", "custom_str": "MoreCoating"},
            "31": {"standard": "气泡", "custom_code": "31", "custom_str": "Bubble"},
            "32": {"standard": "划痕", "custom_code": "32", "custom_str": "Scratch"},
            "33": {"standard": "距离", "custom_code": "33", "custom_str": "Distance"},
            "34": {"standard": "锡膏检测", "custom_code": "34", "custom_str": "SPIDetect"},
            "35": {"standard": "共线性", "custom_code": "35", "custom_str": "Collinearity"},
            "36": {"standard": "CPU插针检测", "custom_code": "36", "custom_str": "CPUPinDetect"}
        },
        "AIS301": {
            "1": {"standard": "不合格", "custom_code": "1", "custom_str": "NG"},
            "2": {"standard": "多锡", "custom_code": "2", "custom_str": "ExcessSolder"},
            "3": {"standard": "连锡", "custom_code": "3", "custom_str": "Bridge"},
            "4": {"standard": "少锡", "custom_code": "4", "custom_str": "InsufficientSolder"},
            "5": {"standard": "孔洞", "custom_code": "5", "custom_str": "PinHole"},
            "6": {"standard": "未出脚", "custom_code": "6", "custom_str": "NoPin"},
            "7": {"standard": "异常出脚", "custom_code": "7", "custom_str": "ExceptionPin"},
            "8": {"standard": "缺件", "custom_code": "8", "custom_str": "MissingPart"},
            "9": {"standard": "偏位", "custom_code": "9", "custom_str": "ShiftPart"},
            "10": {"standard": "露铜", "custom_code": "10", "custom_str": "ExposeCopper"},
            "11": {"standard": "错件", "custom_code": "11", "custom_str": "WrongPart"},
            "12": {"standard": "极性错误", "custom_code": "12", "custom_str": "ReversePart"},
            "13": {"standard": "条码识别错误", "custom_code": "13", "custom_str": "BarcodeRecognition"},
            "14": {"standard": "数据错误", "custom_code": "14", "custom_str": "CountError"},
            "15": {"standard": "定位错误", "custom_code": "15", "custom_str": "Locate"},
            "16": {"standard": "流程错误", "custom_code": "16", "custom_str": "ProcessError"},
            "17": {"standard": "锡珠", "custom_code": "17", "custom_str": "SolderBall"},
            "18": {"standard": "拼版特征不匹配", "custom_code": "18", "custom_str": "FeatureMismatch"},
        },
        "AIS201": {
            "1": {"standard": "漏件", "custom_code": "1", "custom_str": "MissingPart"},
            "-1": {"standard": "未检查", "custom_code": "-1", "custom_str": "NoCheck"},
            "4": {"standard": "反件", "custom_code": "4", "custom_str": "ReversePart"},
            "5": {"standard": "错件", "custom_code": "5", "custom_str": "WrongPart"},
            "20": {"standard": "引脚未插", "custom_code": "20", "custom_str": "Pin Not Found"},
            "21": {"standard": "不是引脚", "custom_code": "21", "custom_str": "NoPin"},
            "101": {"standard": "多件", "custom_code": "101", "custom_str": "多件"},
            "102": {"standard": "浮高", "custom_code": "102", "custom_str": "Part Lift"},
            "103": {"standard": "歪斜", "custom_code": "103", "custom_str": "Part Tilt"},
            "104": {"standard": "条码错误", "custom_code": "104", "custom_str": "Barcode Error"},
            "105": {"standard": "内部错误", "custom_code": "105", "custom_str": "Internal Error"},
            "80": {"standard": "多涂", "custom_code": "80", "custom_str": "MoreCoating"},
            "81": {"standard": "少涂", "custom_code": "81", "custom_str": "LessCoating"},
            "82": {"standard": "气泡", "custom_code": "82", "custom_str": "BubbleCoating"},
        },
        "AIS63X": {
            "1": {"standard": "锡型不良", "custom_code": "1", "custom_str": "锡型不良"},
            "2": {"standard": "水平偏移", "custom_code": "2", "custom_str": "水平偏移"},
            "3": {"standard": "竖直偏移", "custom_code": "3", "custom_str": "竖直偏移"},
            "4": {"standard": "连锡", "custom_code": "4", "custom_str": "连锡"},
            "5": {"standard": "面积偏小", "custom_code": "5", "custom_str": "面积偏小"},
            "6": {"standard": "面积偏大", "custom_code": "6", "custom_str": "面积偏大"},
            "7": {"standard": "高度偏低", "custom_code": "7", "custom_str": "高度偏低"},
            "8": {"standard": "高度偏高", "custom_code": "8", "custom_str": "高度偏高"},
            "9": {"standard": "少锡", "custom_code": "9", "custom_str": "少锡"},
            "10": {"standard": "多锡", "custom_code": "10", "custom_str": "多锡"},
            "11": {"standard": "无锡", "custom_code": "11", "custom_str": "无锡"},
            "12": {"standard": "共面性", "custom_code": "12", "custom_str": "共面性"},
            "19": {"standard": "条码识别", "custom_code": "19", "custom_str": "条码识别"},
            "1000": {"standard": "Unknown", "custom_code": "1000", "custom_str": "Unknown"},
        }
    }

    form = {
        # "window_ip": {
        #     "ui_name": "window中转IP",
        #     "value": ""
        # },
        "api_url": {
            "ui_name": "其他接口URL",
            "value": ""
        },
        "data_api_url": {
            "ui_name": "数据接口URL",
            "value": ""
        },
        "check_api_url": {
            "ui_name": "检查工艺接口URL",
            "value": ""
        },
        "device_api_url": {
            "ui_name": "设备状态接口URL",
            "value": ""
        },
        "order_id_1": {
            "ui_name": "工单号(轨道1)",
            "value": ""
        },
        "order_id_2": {
            "ui_name": "工单号(轨道2)",
            "value": ""
        },
        "device_id": {
            "ui_name": "设备ID",
            "value": ""
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        },
        "operator": {
            "ui_name": "操作员",
            "value": ""
        },
        "project_name": {
            "ui_name": "程序名称",
            "value": ""
        },
        "line_name": {
            "ui_name": "线体名称",
            "value": ""
        },
        "workstation1": {
            "ui_name": "工站(轨道1)",
            "value": ""
        },
        "workstation2": {
            "ui_name": "工站(轨道2)",
            "value": ""
        },
        "device_ip": {
            "ui_name": "设备IP",
            "value": ""
        },
        "mes_station": {
            "ui_name": "MES工位",
            "value": ""
        },
        "equipment_id": {
            "ui_name": "设备编码",
            "value": ""
        },

    }

    combo = {
        "board_side1": {
            "ui_name": "板面(轨道1)",
            "item": ["T", "B"],
            "value": "T"
        },
        "board_side2": {
            "ui_name": "板面(轨道2)",
            "item": ["T", "B"],
            "value": "T"
        },
        # "upload_type": {
        #     "ui_name": "上传范围",
        #     "item": ["全部", "复判NG"],
        #     "value": "复判NG"
        # },
    }

    other_form = {
        "access_token": {
            "ui_name": "Token",
            "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjQzNDk0NDA1OTkwMDIyOSwiVGVuYW50SWQiOjE0MjMwNzA3MDkxODc4MCwiVXNlck5hbWUiOiJBT0lNYWMyIiwiUmVhbE5hbWUiOiJBT0lNYWMiLCJTdXBlckFkbWluIjozLCJPcmdJZCI6ODQyMTQsIk9yZ05hbWUiOm51bGwsIk9yZ0xldmVsIjpudWxsLCJpYXQiOjE2ODgzNTI4MzEsIm5iZiI6MTY4ODM1MjgzMSwiZXhwIjoyMTI5ODU2ODMxLCJpc3MiOiJBZG1pbi5ORVQiLCJhdWQiOiJBZG1pbi5ORVQifQ.xATtSy5LIKZNa77TYj2vEd8GlsLe9ZV7EdM59CTeYaY"
        },
        # "minio_server": {
        #     "ui_name": "文件服务器",
        #     "value": "127.0.0.1：9000"
        # },
        # "minio_bucket_name": {
        #     "ui_name": "文件BucketName",
        #     "value": "image"
        # },
        # "minio_access_key": {
        #     "ui_name": "文件AccessKey",
        #     "value": "kh5LrFNdETJhdGFIiBbn"
        # },
        # "minio_secret_key": {
        #     "ui_name": "文件SecretKey",
        #     "value": "zclayJCC3NXYruZr9P3W1KolFDGVXfkjvyidTVDr"
        # },

        # "ftp_host": {
        #     "ui_name": "FTP Host",
        #     "value": "*************"
        # },
        # "ftp_port": {
        #     "ui_name": "FTP Port",
        #     "value": "21"
        # },
        # "ftp_user": {
        #     "ui_name": "FTP 账号",
        #     "value": "sch"
        # },
        # "ftp_password": {
        #     "ui_name": "FTP 密码",
        #     "value": "sch_password"
        # },
        # # "ftp_path": {
        # #     "ui_name": "FTP 路径",
        # #     "value": "/"
        # # },
        # "window_ftp_base_path1": {
        #     "ui_name": "Window Ftp 存储路径",
        #     "value": "D:\\image_path"
        # },
    }

    other_combo = {
        "is_cron_upload": {
            "ui_name": "定时上传设备状态",
            "item": ["不开启", "开启"],
            "value": "不开启",
        },
        "upload_time": {
            "ui_name": "上传频率(s)",
            "item": ["1", "3", "5", "10", "15", "30", "60", "120", "500", "1200", "3600"],
            "value": "10"
        }
    }

    password_style = ["password", "ftp_password", "minio_secret_key"]

    def init_main_window(self, main_window, other_vo: OtherVo):
        other_combo = main_window.config_data.get('other_combo')
        is_cron_upload = other_combo.get('is_cron_upload', {}).get('value')
        upload_time = other_combo.get('upload_time', {}).get('value')

        if is_cron_upload == "开启":
            interval_cron = True
        else:
            interval_cron = False

        main_window.config_data["app_setting"]["custom_interval_cron"] = interval_cron
        main_window.config_data["app_setting"]["custom_interval_time"] = int(upload_time)  # 3s
        self.log.info("init main window done!")

    async def _upload_image(self, api_url, headers: dict, image_list: list):
        '''
        使用minio库上传，实现为异步上传，先上传minio服务，再调用接口 /product/sn/savefilerec 保存上传记录
        image_list为要上传的图片列表，每个图片信息为：
        {
        "sn":xxx,  # 整板图sn为整板条码，不良器件图sn为拼板条码
        "work_station":xxx, # 对应的工作台
        "src_image_path":xxx, # 图片本地文件路径
        "filename":xxxx, # 上传到oss的图片文件名
        }
        '''
        error_msg = ''
        try:
            # getossconninfo只获取一次即可
            if not xdata.minio_server:
                # 1. /system/config/oss/getossconninfo  先获取基本信息
                get_info_url = f"{api_url}/system/config/oss/getossconninfo"
                ret = await http_mes(get_info_url, {}, headers)
                if ret.get('code') == 0:
                    get_info_ret = ret.get('data')
                    xdata.minio_server = get_org_str(get_info_ret.get('endPoint'))
                    xdata.minio_access_key = get_org_str(get_info_ret.get('accessKey'))
                    xdata.minio_secret_key = get_org_str(get_info_ret.get('secretKey'))
                    xdata.minio_bucket_name = get_org_str(get_info_ret.get('bucketName'))
                    self.log.info(f"获取到基本信息: endPoint:{xdata.minio_server}；accessKey:{xdata.minio_access_key}；"
                                  f"secretKey:{xdata.minio_secret_key}；bucketName:{xdata.minio_bucket_name}")
                else:
                    # 出错就不再继续上传图片，直接返回
                    return ret.get('message')

            t1 = time.time()
            self.log.info(f"------- 开始上传图片到minio服务器 ------")
            # 创建MinIO客户端对象
            client = Minio(
                xdata.minio_server,
                access_key=xdata.minio_access_key,
                secret_key=xdata.minio_secret_key,
                secure=False
            )

            for image in image_list:
                work_station = image.get('work_station', '')
                sn = image.get('sn', '')
                src_filepath = image.get('src_image_path', '')
                dst_filename = image.get('filename', '')
                self.log.info(f"源文件路径：{src_filepath}")
                self.log.info(f"目标位置：bucket_name:{xdata.minio_bucket_name} filename:{dst_filename}")
                # 先上传minio服务
                ret = client.fput_object(xdata.minio_bucket_name, dst_filename, src_filepath)
                self.log.info(f"上传oss成功")

                # 上传成功，调用接口 /product/sn/savefilerec 记录上传数据
                save_file_rec = f"{api_url}/product/sn/savefilerec"
                save_file_param = {
                    "WorkStation": work_station,
                    "Sn": sn,
                    "FileName": dst_filename
                }
                save_file_ret = await http_mes(save_file_rec, save_file_param, headers)
                if save_file_ret.get('code') == 0:
                    self.log.info(f"记录上传数据成功")
                else:
                    self.log.info(f"记录上传数据失败，error:{save_file_ret.get('message')}")

            self.log.info(f"总共上传成功{len(image_list)}个文件，花费时间：{time.time() - t1}")
        except S3Error as err:
            error_msg = f"Error occurred: {err=}"
            self.log.error(error_msg)
        except Exception as err:
            error_msg = f"上传失败，其他异常：error:{err}"
            self.log.error(error_msg)
        finally:
            return error_msg

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        operator = data_vo.get_value_by_cons_key("operator")
        device_id = data_vo.get_value_by_cons_key("device_id")
        device_name = data_vo.get_value_by_cons_key("device_name")
        # upload_type = data_dao.get_value_by_cons_key("upload_type")
        access_token = data_vo.get_value_by_cons_key("access_token")
        data_api_url = data_vo.get_value_by_cons_key("data_api_url")
        # window_ip = data_dao.get_value_by_cons_key("window_ip")
        # ftp_host = data_dao.get_value_by_cons_key("ftp_host")
        # ftp_port = data_dao.get_value_by_cons_key("ftp_port")
        # ftp_user = data_dao.get_value_by_cons_key("ftp_user")
        # ftp_password = data_dao.get_value_by_cons_key("ftp_password")
        # # ftp_path = data_dao.get_value_by_cons_key("ftp_path")
        # window_ftp_base_path1 = data_dao.get_value_by_cons_key("window_ftp_base_path1")

        # try:
        #     ftp_port = int(ftp_port)
        # except Exception as err:
        #     return self.x_response("false", f"FTP Port必须位数字！error：{err}")

        error_msg_list = []

        headers = {
            "Authorization": f"Bearer {access_token}",
        }

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        track_index = pcb_entity.track_index
        if track_index == 1:
            order_id = data_vo.get_value_by_cons_key("order_id_1")
            workstation = data_vo.get_value_by_cons_key("workstation1")
            board_side = data_vo.get_value_by_cons_key("board_side1")

        else:
            order_id = data_vo.get_value_by_cons_key("order_id_2")
            workstation = data_vo.get_value_by_cons_key("workstation2")
            board_side = data_vo.get_value_by_cons_key("board_side2")

        line_name = data_vo.get_value_by_cons_key("line_name")
        project_name = pcb_entity.pcb
        pcb_sn = pcb_entity.pcb_barcode
        start_datetime = pcb_entity.get_start_time()
        start_time = start_datetime.strftime(xcons.FMT_TIME_DEFAULT)

        time_file = start_datetime.strftime(xcons.FMT_TIME_FILE)
        time_path = start_datetime.strftime('%Y/%m/%d/%H')

        board_data = []
        ng_pic_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_list = []
            ix = 0
            for comp_entity in board_entity.yield_comp_entity():
                # if upload_type == "全部" or (upload_type == "复判NG" and comp_entity.is_repair_ng()):
                if comp_entity.is_robot_ng():
                    ix += 1
                    comp_list.append({
                        "ArrayBarCode": barcode,
                        "ArrayID": board_no,
                        "ComponentIndex": str(ix),
                        "PartNo": comp_entity.part,
                        "PartDesignate": comp_entity.designator,
                        "PackageType": comp_entity.part,
                        "Result": "PASS" if comp_entity.repair_result else "NG",
                        "Errcode": comp_entity.repair_ng_code,
                        "ImagePath": comp_entity.image_path,
                        "CarrierSN": pcb_sn,
                        "finalResult": comp_entity.get_final_result("GOOD", "PASS", "NG")
                    })

                if comp_entity.is_robot_ng():
                    filename = f"{time_path}/{barcode}_{comp_entity.designator}_{time_file}.jpg"
                    ng_pic_list.append({
                        "sn": barcode,
                        "work_station": workstation,
                        "src_image_path": comp_entity.image_path,
                        "filename": filename,
                    })

            board_data.append({
                "ArrayID": board_no,
                # "ArrayStatus": board_entity.get_repair_result("OK", "FAIL"),
                "ArrayStatus": board_entity.get_final_result("GOOD", "PASS", "NG"),
                "ArrayBarCode": barcode,
                "Components": comp_list
            })

        pcb_param = {
            "MachineID": device_id,
            "MachineName": device_name,
            "Operator": operator,
            "Side": board_side,
            "JobName": project_name,
            "LineName": line_name,
            "WorkStation": workstation,
            "WorkNo": order_id,
            "BoardBarCode": pcb_sn,
            "BoardStatus": "PASS" if pcb_entity.pcb_repair_result else "NG",
            "DateTime": start_time,
            "Array": board_data
        }

        # 上传坏板信息，如果有坏板的话，需要上传坏板信息
        bad_info = pcb_entity.dict_t_board_info()

        bad_str = ""
        barcode_list = []
        for k, v in bad_info.items():
            bad_str += v.get("bad_board", "0")
            barcode_list.append(v.get('barcode'))

        # 1,坏板。  0,好板。
        if "1" in bad_str:
            # 需要上传坏板信息
            self.log.info(f"有坏板，需要上传坏板信息！")
            bad_param = {
                "BarCode": pcb_sn,
                "MachineName": device_name,
                "Operator": operator,
                "Side": board_side,
                "JobName": order_id,
                "LineName": line_name,
                "WorkStation": workstation,
                "GroupBarCode": [],
                "ArrayBarCode": barcode_list,
                "BadMarkInfo": bad_str,
            }

            bad_info_url = f"{api_url}/collet/aoi/normal/savebadmark"

            # bad_ret = xrequest.RequestUtil.post_json(bad_info_url, bad_param)
            ret = asyncio.run(http_mes(bad_info_url, bad_param, headers))
            if ret.get('code') == 0:
                bad_ret = ret.get('data')
                if str(bad_ret.get("eventCode")) != "0":
                    error_msg_list.append(f"mes接口异常，上传坏板信息失败，error：{bad_ret.get('message')}")
                    # return self.x_response("false", f"mes接口异常，上传坏板信息成功，error：{bad_ret.get('message')}")
            else:
                error_msg_list.append(f"网络异常，上传坏板信息失败，error：{ret.get('message')}")

        # data_url = f"{api_url}/collet/aoi/normal/mesoutput"
        data_url = data_api_url
        # ret = xrequest.RequestUtil.post_json(data_url, pcb_param)
        ret = asyncio.run(http_mes(data_url, pcb_param, headers))
        if ret.get('code') == 0:
            mes_ret = ret.get('data')
            if str(mes_ret.get("eventCode")) != "0":
                error_msg_list.append(f"mes接口异常，上传测试数据失败，error：{mes_ret.get('message')}")
        else:
            error_msg_list.append(f"网络异常，上传测试数据失败，error：{ret.get('message')}")

        # 上传整板图和复判后不良器件图片
        image_list = []
        src_image_path = pcb_entity.get_unknown_t_pcb_image()
        if src_image_path:
            filename = f"{time_path}/{pcb_sn}_{time_file}.jpg"
            image_list.append({
                "sn": pcb_sn,
                "work_station": workstation,
                "src_image_path": src_image_path,
                "filename": filename,
            })
        else:
            self.log.warning(f"找不到要上传的大板图片！")

        if ng_pic_list:
            image_list.extend(ng_pic_list)
        else:
            self.log.warning(f"没有需要上传的复判后NG器件图")

        if image_list:
            ret = asyncio.run(self._upload_image(api_url, headers, image_list))
            if ret:  # 不为空则上传失败
                error_msg_list.append(ret)

        if error_msg_list:
            return self.x_response("false", "\n".join(error_msg_list))

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_url = other_vo.get_value_by_cons_key("check_api_url")
        access_token = other_vo.get_value_by_cons_key("access_token")

        track_index = other_vo.get_track_index()

        if track_index == 1:
            order_id = other_vo.get_value_by_cons_key("order_id_1")
            workstation = other_vo.get_value_by_cons_key("workstation1")
        else:
            order_id = other_vo.get_value_by_cons_key("order_id_2")
            workstation = other_vo.get_value_by_cons_key("workstation2")

        line_name = other_vo.get_value_by_cons_key("line_name")
        operator = other_vo.get_value_by_cons_key("operator")

        project_name = other_vo.get_project_name()

        if not project_name:
            project_name = other_vo.get_value_by_cons_key("project_name")

        sn_list = other_vo.list_sn()

        param = {
            "barCode": "",
            "workNo": order_id,
            "operator": operator,
            "jobName": project_name,
            "lineName": line_name,
            "workStation": workstation,
            "groupBarCode": [],
            "arrayBarCode": sn_list,
            "arrayCarrierSN": []
        }

        # check_url = f"{api_url}/collet/aoi/normal/barcodecheck"

        headers = {
            "Authorization": f"Bearer {access_token}",
        }

        # ret = xrequest.RequestUtil.post_json(check_url, param, headers=header)
        ret = asyncio.run(http_mes(check_url, param, headers))
        if ret.get('code') == 0:
            check_ret = ret.get('data')
            if str(check_ret.get("eventCode")) != "0":
                return self.x_response("false", f"mes接口异常，条码校验失败，error：{check_ret.get('message')}")
        else:
            return self.x_response("false", f"网络异常，条码校验失败，error：{ret.get('message')}")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        status_url = other_vo.get_value_by_cons_key("device_api_url")
        # device_id = other_dao.get_value_by_cons_key("device_id")
        # device_name = other_dao.get_value_by_cons_key("device_name")
        # workstation1 = other_dao.get_value_by_cons_key("workstation1")
        # device_ip = other_dao.get_value_by_cons_key("device_ip")
        # access_token = other_dao.get_value_by_cons_key("access_token")
        mes_station = other_vo.get_value_by_cons_key("mes_station")
        equipment_id = other_vo.get_value_by_cons_key("equipment_id")

        # status_url = f"{api_url}/collet/aoi/normal/updatedevicestatus"

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        status_code = other_vo.get_status_code()
        status_desc = other_vo.get_status_desc()

        if not status_code:
            status_desc = other_vo.get_device_status_str()
            status_code = xcons.DEVICE_STATUS.get(status_desc, "99")

            if "safedoor" in status_desc:
                status_code = "10"

            if "安全门" in status_desc:
                status_code = "10"

        # param = {
        #     "MachineID": device_id,
        #     "MachineName": device_name,
        #     "IP": device_ip,
        #     "WorkStation": workstation1,
        #     "StatusCode": status_code,
        #     "StatusMessage": status_desc,
        #     "ChangeTime": time_now,
        #     "ErrorList": [
        #         {
        #             "ErrorCode": status_code,
        #             "ErrorMessage": status_desc,
        #             "SuggestedSolutions": ""
        #         }
        #     ]
        # }
        #
        # headers = {
        #     "Authorization": f"Bearer {access_token}",
        # }
        #
        # # ret = xrequest.RequestUtil.post_json(status_url, param)
        # ret = asyncio.run(http_mes(status_url, param, headers))
        #
        # if str(ret.get("eventCode")) != "0":
        #     return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('message')}")

        if status_code == "02":
            equ_status = "运行"
            status_desc = ""
        elif status_code == "03":
            equ_status = "停机"
        elif status_code in ["01", "04"]:
            self.log.info(f"进板和出板不上传到mes！")
            return self.x_response()
        else:
            equ_status = "故障"

        global_data["last_status"] = equ_status
        global_data["status_code"] = status_code
        global_data["status_desc"] = status_desc

        status_param = {
            "stationCode": mes_station,
            "equipmentId": equipment_id,
            "uploadTime": time_now,
            "equipmentInfo": {
                "plcStatus": "连接",
                "equipmentStatus": equ_status,
                "errorCode": status_code,
                "errorMessage": status_desc,
            },
            "otherInfo": [],
        }

        xrequest.RequestUtil.post_json(status_url, status_param)

        return self.x_response()

    def send_idle_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        status_url = other_vo.get_value_by_cons_key("device_api_url")
        mes_station = other_vo.get_value_by_cons_key("mes_station")
        equipment_id = other_vo.get_value_by_cons_key("equipment_id")

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        global_data["last_status"] = "空闲"
        global_data["status_code"] = ""
        global_data["status_desc"] = ""

        status_param = {
            "stationCode": mes_station,
            "equipmentId": equipment_id,
            "uploadTime": time_now,
            "equipmentInfo": {
                "plcStatus": "连接",
                "equipmentStatus": "空闲",
                "errorCode": "",
                "errorMessage": "",
            },
            "otherInfo": [],
        }

        xrequest.RequestUtil.post_json(status_url, status_param)

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        self.log.info(f"开始定时上传设备状态....")
        status_url = other_vo.get_value_by_cons_key("device_api_url")
        mes_station = other_vo.get_value_by_cons_key("mes_station")
        equipment_id = other_vo.get_value_by_cons_key("equipment_id")

        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        status_param = {
            "stationCode": mes_station,
            "equipmentId": equipment_id,
            "uploadTime": time_now,
            "equipmentInfo": {
                "plcStatus": "连接",
                "equipmentStatus": global_data.get('last_status'),
                "errorCode": global_data.get('status_code'),
                "errorMessage": global_data.get('status_desc'),
            },
            "otherInfo": [],
        }

        xrequest.RequestUtil.post_json(status_url, status_param)


if __name__ == '__main__':
    ret = get_org_str('cnVub29i')
    print(ret)
