# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/5/29 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：中山军创
"""

from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "zhongshanjunchuang release v1.0.0.2",
        "device": "40x",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-05-29 10:12  条码校验，上传数据
""", }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "",
        },

    }

    other_combo = {
        "is_cron_flag": {
            "ui_name": "定时连接MES",
            "item": ["Yes", "No"],
            "value": "No",
        },

        "cron_seconds": {
            "ui_name": "定时触发频率(s)",
            "item": ["10", "30", "60", "120", "300", "600", "1200", "3600", "7200", "12000", "14400"],
            "value": "30",
        },
    }

    form = {
        "line_code": {
            "ui_name": "产线代码",
            "value": "",
        },
        "process_code": {
            "ui_name": "工序代码",
            "value": "",
        },
        "device_code": {
            "ui_name": "设备编码",
            "value": "",
        },
        "log_file": {
            "ui_name": "日志文件",
            "value": "",
        },
        "mo_no": {
            "ui_name": "生产工单",
            "value": "",
        },
    }

    button = {
        "connect_mes": {
            "ui_name": "Connect Mes",
            "value": "",
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        is_cron_flag = other_vo.get_value_by_cons_key("is_cron_flag")
        cron_seconds = other_vo.get_value_by_cons_key("cron_seconds", to_int=True)

        main_window.set_cron_setting(True if is_cron_flag == "Yes" else False, cron_seconds)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        line_code = other_vo.get_value_by_cons_key("line_code")
        process_code = other_vo.get_value_by_cons_key("process_code")
        device_code = other_vo.get_value_by_cons_key("device_code")
        log_file = other_vo.get_value_by_cons_key("log_file")

        ret_res = self.x_response()

        check_url = f"{api_url}/api/CheckDipTaskProcessBarCode"

        for sn in other_vo.list_sn():
            check_param = {
                "Sc_prno": line_code,
                "ProcessNo": process_code,
                "DeviceNo": device_code,
                "BarCode": sn,
                "LogFile": log_file,
            }

            ret = xrequest.RequestUtil.get(check_url, check_param)
            if str(ret.get("status")) != "200":
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('msg')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        process_code = data_vo.get_value_by_cons_key("process_code")
        device_code = data_vo.get_value_by_cons_key("device_code")
        log_file = data_vo.get_value_by_cons_key("log_file")
        mo_no = data_vo.get_value_by_cons_key("mo_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        data_url = f"{api_url}/api/ReturnLaserResult"

        comp_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            for comp_entity in board_entity.yield_comp_entity():

                for alg_entity in comp_entity.yield_alg_entity():
                    comp_data_list.append({
                        "ItemCode": comp_entity.designator,
                        "ItemName": alg_entity.test_name,
                        "BaseLowHight": f"{alg_entity.min_threshold}~{alg_entity.max_threshold}",
                        "NgCode": comp_entity.repair_ng_code,
                        "Data": alg_entity.test_val,
                        "PassNg": "Pass" if alg_entity.result == "0" else "Ng",
                    })

        pcb_param = {
            "MoNo": mo_no,
            "ProcessNo": process_code,
            "DeviceNo": device_code,
            "BarCode": ",".join(pcb_entity.all_barcode),
            "Flag": pcb_entity.get_repair_result(),
            "startTime": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "endTime": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
            "Details": comp_data_list,
            "LogFile": log_file
        }

        ret = xrequest.RequestUtil.post_json(data_url, pcb_param)
        if str(ret.get("status")) != "200":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url = btn_vo.get_value_by_cons_key("api_url")

        if btn_vo.get_btn_key() == "connect_mes":
            connect_url = f"{api_url}/api/ApiConnect"

            ret = xrequest.RequestUtil.post_json(connect_url, {})
            if str(ret.get("status")) != "200":
                return self.x_response("false", f"mes接口异常，连接MES失败，error：{ret.get('msg')}")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url = other_vo.get_value_by_cons_key("api_url")
        connect_url = f"{api_url}/api/ApiConnect"

        xrequest.RequestUtil.post_json(connect_url, {})
