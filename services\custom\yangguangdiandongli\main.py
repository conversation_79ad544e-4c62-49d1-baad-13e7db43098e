# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/2/20 下午3:24
# Author     ：sch
# version    ：python 3.8
# Description：阳光电动力/阳光电源
"""
from typing import Any, Optional

from common import xrequest, xcons
from common.xutil import log
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine
import hashlib

source_type_map = {
    "组串终检": "ZCZJ", "智慧能源": "ZHNY", "电动车": "DDC", "优化器": "MLPE", "充电桩": "CDZ"
}


def add_salt(password: str) -> Optional[str]:
    """
    使用 SHA-256 算法对输入字符串进行哈希处理。
    参数:password (str): 要加密的字符串。
    返回:Optional[str]: SHA-256 哈希后的十六进制字符串（小写），若出错则返回 None。
    """
    try:
        # 初始化 SHA-256 哈希对象
        sha256 = hashlib.sha256()
        # 更新数据（使用 UTF-8 编码）
        sha256.update(password.encode('utf-8'))
        # 获取十六进制格式的摘要
        cipher_string = sha256.hexdigest()
        return cipher_string
    except Exception as e:
        log.error(f"哈希计算失败error: {e}")
        return None


class Engine(ErrorMapEngine):
    version = {
        "title": "yangguangdiandongli release v1.0.0.5",
        "device": "303",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-02-20 16:29  条码校验，上传数据
date: 2024-02-22 15:13  条码校验接口，修改传参方式
date: 2024-03-15 09:33  增加器件参数remarks,传复判后不良描述
date: 2024-09-10 14:22  增加整板发送选项
date: 2025-06-24 10:22  ATAOI_2019-40379：从mes获取条码
""",
    }

    other_form = {
        "check_url": {
            "ui_name": "条码校验接口URL",
            "value": "http://***************:8088/tms/api/public/aging/checkProcess",
        },
        "data_url": {
            "ui_name": "上传数据接口URL",
            "value": "http://***************:8088/tms/api/public/aging/saveTestInfo",
        },
        "get_sn_url": {
            "ui_name": "获取条码接口URL",
            "value": "http://10.3.251.104/mes/api/public/unit/getBingSn",
        },
        "app_key": {
            "ui_name": "appKey",
            "value": "19278e56m7ofnbnxu21ao",
        },
        "app_secret": {
            "ui_name": "AppSecret",
            "value": "11m78e5ebz2whu3aq2dx9jc2689qp",
        },
        "salt": {
            "ui_name": "salt",
            "value": "KtjTRwtabDo=",
        },

    }

    form = {
        "relation_room": {
            "ui_name": "车间",
            "value": "",
        },
        "process_id": {
            "ui_name": "工序",
            "value": "",
        },

        "station_code": {
            "ui_name": "工位",
            "value": "",
        },
        "test_user": {
            "ui_name": "测试人员",
            "value": "",
        },
        "user_id": {
            "ui_name": "操作用户/开发用户",
            "value": "",
        },
    }

    combo = {
        "source_type": {
            "ui_name": "来源",
            "item": ["组串终检", "智慧能源", "电动车", "优化器", "充电桩"],
            "value": "组串终检",
        },
        "send_type": {
            "ui_name": "发送类型",
            "item": ["整板", "拼板"],
            "value": "拼板",
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        user_id = other_vo.get_value_by_cons_key("user_id")
        get_sn_url = other_vo.get_value_by_cons_key("get_sn_url")
        app_key = other_vo.get_value_by_cons_key("app_key")
        app_secret = other_vo.get_value_by_cons_key("app_secret")
        salt = other_vo.get_value_by_cons_key("salt")

        pcb_sn = other_vo.get_pcb_sn()
        headers = {
            "appKey": app_key,
            "saltSecret": add_salt(salt + app_secret),
            "userId": user_id
        }
        ret = xrequest.RequestUtil.get(get_sn_url, {"frockNo": pcb_sn}, headers=headers)
        if not ret.get("success"):
            return self.x_response("false", f"mes接口异常，error：{ret.get('message')}")
        ret_sn_list = ret.get("result", [])
        if not ret_sn_list:
            return self.x_response("false", "mes接口异常，未获取到条码数据")
        return self.x_response("true", ",".join(ret_sn_list))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_url = other_vo.get_value_by_cons_key("check_url")
        process_id = other_vo.get_value_by_cons_key("process_id")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            body_param = {
                "process": process_id,
                "serialNumber": sn
            }

            ret = xrequest.RequestUtil.get(check_url, body_param)
            if str(ret.get('code')) != '200':
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_url = data_vo.get_value_by_cons_key("data_url")
        process_id = data_vo.get_value_by_cons_key("process_id")
        relation_room = data_vo.get_value_by_cons_key("relation_room")
        station_code = data_vo.get_value_by_cons_key("station_code")
        test_user = data_vo.get_value_by_cons_key("test_user")
        send_type = data_vo.get_value_by_cons_key("send_type")
        source_type = source_type_map.get(data_vo.get_value_by_cons_key("source_type"))

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        pcb_sn = pcb_entity.pcb_barcode

        comp_data_all = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():

                for alg_entity in comp_entity.yield_alg_entity():
                    max_val = alg_entity.max_threshold
                    min_val = alg_entity.min_threshold

                    test_result = "pass" if alg_entity.result == '0' else 'fail'

                    if comp_entity.repair_result:
                        test_result = "pass"

                    item = {
                        "sn": barcode,
                        "standard": f"{min_val}~{max_val}",
                        "testItem": comp_entity.designator,
                        "testParameter": alg_entity.test_name,
                        "testResult": test_result,
                        "testTime": start_time,
                        "testUser": test_user,
                        "ToolNo": "",
                        "testValue": alg_entity.test_val,
                        "remarks": comp_entity.repair_ng_str
                    }

                    comp_data.append(item)
                    comp_data_all.append(item)

            if send_type == "拼板":
                self.log.info(f"拼板发送MES...")
                param = [{
                    "process": process_id,
                    "relationRoom": relation_room,
                    "result": board_entity.get_repair_result("pass", "fail"),
                    "serialNumber": pcb_sn,
                    "sourceType": source_type,
                    "stationCode": station_code,
                    "testTime": start_time,
                    "testDuration": str(pcb_entity.get_cycle_time()),
                    "testUser": test_user,
                    "testDetailApisList": comp_data,
                }]

                ret = xrequest.RequestUtil.post_json(data_url, param)  # noqa
                if str(ret.get('code')) != '200':
                    ret_res = self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret.get('message')}")

        if send_type == "整板":
            self.log.info(f"整板发送MES...")
            param = [{
                "process": process_id,
                "relationRoom": relation_room,
                "result": pcb_entity.get_repair_result("pass", "fail"),
                "serialNumber": pcb_sn,
                "sourceType": source_type,
                "stationCode": station_code,
                "testTime": start_time,
                "testDuration": str(pcb_entity.get_cycle_time()),
                "testUser": test_user,
                "testDetailApisList": comp_data_all,
            }]

            ret = xrequest.RequestUtil.post_json(data_url, param)  # noqa
            if str(ret.get('code')) != '200':
                ret_res = self.x_response("false", f"mes接口异常，上传测试数据失败，error：{ret.get('message')}")

        return ret_res
