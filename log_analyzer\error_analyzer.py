#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误分析模块
基于项目中的错误码映射，提供错误识别、分类、统计功能
"""

import re
import json
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
try:
    from .log_parser import LogEntry
except ImportError:
    from log_parser import LogEntry


@dataclass
class ErrorInfo:
    """错误信息"""
    code: str
    description: str
    category: str
    severity: str
    count: int = 0
    first_occurrence: Optional[str] = None
    last_occurrence: Optional[str] = None
    affected_files: List[str] = None


class ErrorAnalyzer:
    """错误分析器"""
    
    def __init__(self):
        self.error_maps = self._load_error_maps()
        self.error_patterns = self._build_error_patterns()
        
    def _load_error_maps(self) -> Dict[str, Dict]:
        """加载项目中的错误码映射"""
        error_maps = {
            # MES系统错误码
            'mes_errors': {
                "1": {"description": "可生产/成功", "category": "正常", "severity": "INFO"},
                "16": {"description": "生产线中设备不存在", "category": "设备错误", "severity": "ERROR"},
                "17": {"description": "工单对应工艺下无该设备", "category": "工艺错误", "severity": "ERROR"},
                "18": {"description": "工单不存在", "category": "数据错误", "severity": "ERROR"},
                "19": {"description": "不符合时间锁", "category": "时间错误", "severity": "WARNING"},
                "20": {"description": "不符合物料锁", "category": "物料错误", "severity": "WARNING"},
                "21": {"description": "产品不存在", "category": "数据错误", "severity": "ERROR"},
                "22": {"description": "非混产模式", "category": "模式错误", "severity": "WARNING"},
                "240": {"description": "无型号", "category": "数据错误", "severity": "ERROR"},
                "241": {"description": "不符合工序锁", "category": "工序错误", "severity": "WARNING"},
                "242": {"description": "未处于返修模式下出现不合格", "category": "质量错误", "severity": "ERROR"},
                "243": {"description": "互锁表达式异常", "category": "系统错误", "severity": "ERROR"},
                "244": {"description": "产品已下线", "category": "状态错误", "severity": "WARNING"},
                "245": {"description": "产品已报废", "category": "状态错误", "severity": "ERROR"},
                "246": {"description": "无生产计划", "category": "计划错误", "severity": "WARNING"},
                "247": {"description": "条码校验失败", "category": "数据错误", "severity": "ERROR"},
                "248": {"description": "物料缺料", "category": "物料错误", "severity": "ERROR"},
                "249": {"description": "烧录次数达到3次", "category": "设备错误", "severity": "ERROR"},
                "255": {"description": "服务端未定义异常而不能生产", "category": "系统错误", "severity": "CRITICAL"},
                "2": {"description": "未知异常", "category": "系统错误", "severity": "ERROR"},
                "3": {"description": "产品标识为空", "category": "数据错误", "severity": "ERROR"},
                "4": {"description": "产品信息不完整", "category": "数据错误", "severity": "ERROR"},
                "5": {"description": "该工序无质检单模板", "category": "配置错误", "severity": "ERROR"},
                "6": {"description": "生产线标识错误", "category": "配置错误", "severity": "ERROR"},
                "77": {"description": "物料不齐套", "category": "物料错误", "severity": "ERROR"},
            },
            
            # AOI检测错误码
            'aoi_errors': {
                "0": {"description": "OK", "category": "正常", "severity": "INFO"},
                "1": {"description": "漏件", "category": "检测缺陷", "severity": "ERROR"},
                "-1": {"description": "未检查", "category": "检测状态", "severity": "WARNING"},
                "2": {"description": "错件", "category": "检测缺陷", "severity": "ERROR"},
                "3": {"description": "反件", "category": "检测缺陷", "severity": "ERROR"},
                "4": {"description": "立碑", "category": "检测缺陷", "severity": "ERROR"},
                "5": {"description": "偏移", "category": "检测缺陷", "severity": "ERROR"},
                "20": {"description": "引脚未插", "category": "检测缺陷", "severity": "ERROR"},
                "21": {"description": "不是引脚", "category": "检测缺陷", "severity": "ERROR"},
                "101": {"description": "多件", "category": "检测缺陷", "severity": "ERROR"},
                "102": {"description": "浮高", "category": "检测缺陷", "severity": "ERROR"},
                "103": {"description": "歪斜", "category": "检测缺陷", "severity": "ERROR"},
            },
            
            # 设备状态错误码
            'device_status': {
                "01": {"description": "进板", "category": "设备状态", "severity": "INFO"},
                "02": {"description": "开始检测", "category": "设备状态", "severity": "INFO"},
                "03": {"description": "停止检查", "category": "设备状态", "severity": "INFO"},
                "04": {"description": "出板", "category": "设备状态", "severity": "INFO"},
                "10": {"description": "安全门", "category": "设备异常", "severity": "WARNING"},
                "11": {"description": "调试", "category": "设备状态", "severity": "INFO"},
                "12": {"description": "紧急故障", "category": "设备异常", "severity": "CRITICAL"},
                "13": {"description": "流程错误", "category": "设备异常", "severity": "ERROR"},
                "20": {"description": "数据超时", "category": "通信异常", "severity": "ERROR"},
                "21": {"description": "板卡NG", "category": "质量异常", "severity": "ERROR"},
                "22": {"description": "直通率告警", "category": "质量异常", "severity": "WARNING"},
                "23": {"description": "Marker错误", "category": "设备异常", "severity": "ERROR"},
                "99": {"description": "其他错误", "category": "未知异常", "severity": "ERROR"},
            }
        }
        
        return error_maps
    
    def _build_error_patterns(self) -> List[Tuple[re.Pattern, str, str]]:
        """构建错误模式匹配规则"""
        patterns = [
            # 网络相关错误
            (re.compile(r'connection.*(?:refused|timeout|failed)', re.IGNORECASE), 
             '网络连接', 'CRITICAL'),
            (re.compile(r'socket.*(?:error|closed)', re.IGNORECASE), 
             '网络通信', 'ERROR'),
            
            # 文件相关错误
            (re.compile(r'file.*not.*found', re.IGNORECASE), 
             '文件缺失', 'ERROR'),
            (re.compile(r'permission.*denied', re.IGNORECASE), 
             '权限错误', 'ERROR'),
            (re.compile(r'disk.*full', re.IGNORECASE), 
             '磁盘空间', 'CRITICAL'),
            
            # 数据库相关错误
            (re.compile(r'database.*(?:error|connection)', re.IGNORECASE), 
             '数据库', 'CRITICAL'),
            (re.compile(r'sql.*(?:error|exception)', re.IGNORECASE), 
             'SQL错误', 'ERROR'),
            
            # 内存相关错误
            (re.compile(r'out.*of.*memory', re.IGNORECASE), 
             '内存不足', 'CRITICAL'),
            (re.compile(r'memory.*(?:leak|error)', re.IGNORECASE), 
             '内存错误', 'ERROR'),
            
            # 超时错误
            (re.compile(r'timeout', re.IGNORECASE), 
             '超时', 'WARNING'),
            
            # 认证错误
            (re.compile(r'auth.*(?:failed|error)', re.IGNORECASE), 
             '认证失败', 'ERROR'),
            
            # 配置错误
            (re.compile(r'config.*(?:error|missing)', re.IGNORECASE), 
             '配置错误', 'ERROR'),
        ]
        
        return patterns

    def analyze_errors(self, entries: List[LogEntry]) -> Dict[str, ErrorInfo]:
        """分析错误日志"""
        error_stats = defaultdict(lambda: ErrorInfo(
            code="", description="", category="", severity="", 
            count=0, affected_files=[]
        ))
        
        for entry in entries:
            if entry.level not in ['ERROR', 'CRITICAL', 'WARNING']:
                continue
            
            # 分析错误码
            if entry.error_code:
                error_info = self._get_error_info(entry.error_code)
                key = f"CODE_{entry.error_code}"
                
                if key not in error_stats:
                    error_stats[key] = ErrorInfo(
                        code=entry.error_code,
                        description=error_info['description'],
                        category=error_info['category'],
                        severity=error_info['severity'],
                        count=0,
                        affected_files=[]
                    )
                
                self._update_error_stats(error_stats[key], entry)
            
            # 分析错误模式
            for pattern, category, severity in self.error_patterns:
                if pattern.search(entry.message):
                    key = f"PATTERN_{category}"
                    
                    if key not in error_stats:
                        error_stats[key] = ErrorInfo(
                            code="",
                            description=f"{category}相关错误",
                            category=category,
                            severity=severity,
                            count=0,
                            affected_files=[]
                        )
                    
                    self._update_error_stats(error_stats[key], entry)
                    break  # 只匹配第一个模式
        
        return dict(error_stats)

    def _get_error_info(self, error_code: str) -> Dict[str, str]:
        """获取错误码信息"""
        for error_map in self.error_maps.values():
            if error_code in error_map:
                return error_map[error_code]
        
        return {
            "description": f"未知错误码: {error_code}",
            "category": "未知错误",
            "severity": "ERROR"
        }

    def _update_error_stats(self, error_info: ErrorInfo, entry: LogEntry):
        """更新错误统计信息"""
        error_info.count += 1
        
        if entry.file_path not in error_info.affected_files:
            error_info.affected_files.append(entry.file_path)
        
        timestamp_str = entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') if entry.timestamp else "未知时间"
        
        if not error_info.first_occurrence:
            error_info.first_occurrence = timestamp_str
        
        error_info.last_occurrence = timestamp_str

    def get_error_summary(self, entries: List[LogEntry]) -> Dict:
        """获取错误摘要"""
        errors = self.analyze_errors(entries)
        
        summary = {
            'total_errors': sum(error.count for error in errors.values()),
            'error_categories': defaultdict(int),
            'severity_distribution': defaultdict(int),
            'top_errors': [],
            'recent_critical': []
        }
        
        # 统计分类和严重程度
        for error in errors.values():
            summary['error_categories'][error.category] += error.count
            summary['severity_distribution'][error.severity] += error.count
        
        # 获取最频繁的错误
        sorted_errors = sorted(errors.values(), key=lambda x: x.count, reverse=True)
        summary['top_errors'] = [
            {
                'code': error.code,
                'description': error.description,
                'count': error.count,
                'category': error.category
            }
            for error in sorted_errors[:10]
        ]
        
        # 获取最近的严重错误
        critical_errors = [error for error in errors.values() if error.severity == 'CRITICAL']
        summary['recent_critical'] = sorted(critical_errors, 
                                          key=lambda x: x.last_occurrence or "", 
                                          reverse=True)[:5]
        
        return summary

    def generate_error_report(self, entries: List[LogEntry]) -> str:
        """生成错误报告"""
        errors = self.analyze_errors(entries)
        summary = self.get_error_summary(entries)
        
        report = []
        report.append("=" * 60)
        report.append("错误分析报告")
        report.append("=" * 60)
        report.append(f"分析日志条目数: {len(entries)}")
        report.append(f"发现错误总数: {summary['total_errors']}")
        report.append("")
        
        # 严重程度分布
        report.append("严重程度分布:")
        for severity, count in summary['severity_distribution'].items():
            report.append(f"  {severity}: {count}")
        report.append("")
        
        # 错误分类
        report.append("错误分类统计:")
        for category, count in summary['error_categories'].items():
            report.append(f"  {category}: {count}")
        report.append("")
        
        # 最频繁错误
        report.append("最频繁错误 (Top 10):")
        for i, error in enumerate(summary['top_errors'], 1):
            report.append(f"  {i}. [{error['code']}] {error['description']} - {error['count']}次")
        report.append("")
        
        # 严重错误详情
        if summary['recent_critical']:
            report.append("最近严重错误:")
            for error in summary['recent_critical']:
                report.append(f"  [{error.code}] {error.description}")
                report.append(f"    发生次数: {error.count}")
                report.append(f"    最后发生: {error.last_occurrence}")
                report.append(f"    影响文件: {', '.join(error.affected_files[:3])}")
                report.append("")
        
        return "\n".join(report)

    def get_error_trends(self, entries: List[LogEntry], hours: int = 24) -> Dict:
        """获取错误趋势"""
        from datetime import datetime, timedelta
        
        now = datetime.now()
        time_buckets = {}
        
        # 创建时间桶
        for i in range(hours):
            bucket_time = now - timedelta(hours=i)
            bucket_key = bucket_time.strftime('%Y-%m-%d %H:00')
            time_buckets[bucket_key] = {'total': 0, 'critical': 0, 'error': 0, 'warning': 0}
        
        # 统计每个时间段的错误
        for entry in entries:
            if not entry.timestamp or entry.level not in ['ERROR', 'CRITICAL', 'WARNING']:
                continue
            
            bucket_key = entry.timestamp.strftime('%Y-%m-%d %H:00')
            if bucket_key in time_buckets:
                time_buckets[bucket_key]['total'] += 1
                time_buckets[bucket_key][entry.level.lower()] += 1
        
        return time_buckets
