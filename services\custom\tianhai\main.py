# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/23 上午11:07
# Author     ：sch
# version    ：python 3.8
# Description：天海
"""
from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

txt_pcb_panel_template = """程序名：{pcb_project_name}
测试时间：{pcb_test_time}
操作员：{pcb_repair_user}
整板条码：{pcb_sn}
整板结果：{pcb_final_result}
拼板数量：{pcb_board_number}
拼板复判NG数量：{pcb_board_user_ng_number}
器件总数：{pcb_comp_number}
器件复判不良总数：{pcb_comp_user_ng_number}

{BoardData}
"""

txt_board_panel_template = """
================ No: {board_no} ================ 结果：{board_final_result}  --> 小板SN: {board_sn}{CompData}
"""

txt_comp_panel_template = """
---位号：{comp_designator}  料号：{comp_part}  封装：{comp_package} 　类型：{comp_type}  检测不良代码：{comp_robot_code}　 检测结果：{comp_robot_result}  复判不良代码：{comp_user_code}  复判结果：{comp_user_result}  器件图片：{comp_image}"""


class Engine(BaseEngine):
    version = {
        "title": "tianhai release v1.0.0.5",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-23 15:07  上传数据到mes
date: 2023-08-25 18:25  最外层的MachineResult，RepairResult传PASS/NG
date: 2023-09-14 14:23  PartInfos:传机器检测出的不良器件明细数据和不良图片
date: 2023-10-09 16:38  增加接口超时时间配置项
""", }

    path = {
        "image_path": {
            "ui_name": "图片保存路径",
            "value": ""
        },
        "data_path": {
            "ui_name": "txt保存路径",
            "value": ""
        },
    }

    form = {
        "api_url": {
            "ui_name": "接口地址",
            "value": "http://192.168.8.66:8866/api/pda/UploadDeviceAOI"
        },
        "order_id": {
            "ui_name": "工单号",
            "value": ""
        },
    }

    other_form = {
        "log_number": {
            "ui_name": "单次日志输出",
            "value": "999999"
        },
    }

    other_combo = {
        "api_timeout": {
            "ui_name": "接口超时时间(s)",
            "item": ["1", "5", "10", "15"],
            "value": "5"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        order_id = data_vo.get_value_by_cons_key("order_id")
        data_path = data_vo.get_value_by_cons_key("data_path")
        image_path = data_vo.get_value_by_cons_key("image_path")
        log_number = data_vo.get_value_by_cons_key("log_number")
        api_timeout = data_vo.get_value_by_cons_key("api_timeout")

        api_timeout = int(api_timeout)

        try:
            log_number = int(log_number)
        except Exception as err:
            return self.x_response("false", f"日志输出必须为数字！error：{err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_str = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = time_str[:8]
        pcb_sn = pcb_entity.pcb_barcode

        dst_path = f"{data_path}/{date_file}"
        image_dst_path = f"{image_path}/{date_file}"
        xutil.FileUtil.ensure_dir_exist(dst_path)
        xutil.FileUtil.ensure_dir_exist(image_dst_path)

        track_index = pcb_entity.track_index

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        board_data2 = []
        # board_data_list = []
        board_data_str = ""

        pcb_robot_list = []
        pcb_repair_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            comp_data_str = ""
            comp_data2 = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_src_img = comp_entity.image_path
                    comp_tag = comp_entity.designator

                    robot_ng_str = comp_entity.robot_ng_str
                    repair_ng_str = comp_entity.repair_ng_str
                    pcb_robot_list.append(robot_ng_str)

                    if comp_entity.is_repair_ng():
                        pcb_repair_list.append(repair_ng_str)

                    if comp_src_img:
                        img_name = f"{barcode}_{comp_tag}.png"
                        img_bae64_str = xutil.OtherUtil.file_to_base64_content(comp_src_img)
                        # comp_dst_img = f"{image_dst_path}/{img_name}"
                        # xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)
                    else:
                        img_name = ""
                        img_bae64_str = ""

                    comp_data2.append({
                        "Name": comp_tag,
                        "MachineResult": "PASS" if comp_entity.robot_result else "NG",
                        "RepairResult": "PASS" if comp_entity.repair_result else "NG",
                        "MachineResultDec": robot_ng_str,
                        "RepairResultDec": repair_ng_str,
                        "ImageName": img_name,
                        "uploadFileAsBase64String": img_bae64_str,
                    })

                comp_data_str += txt_comp_panel_template.format(**{
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            board_data_str += txt_board_panel_template.format(**{
                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "CompData": comp_data_str,
            })

            board_data2.append({
                "SN": barcode,
                "BoardNo": board_no,
                "MachineResult": board_entity.get_robot_result("PASS", "NG"),
                "RepairResult": board_entity.get_repair_result("PASS", "NG"),
                "PartInfos": comp_data2,
            })

        pcb_data = {
            "pcb_sn": pcb_sn,
            "pcb_track_line": track_index,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "BoardData": board_data_str
        }

        pcb_content = txt_pcb_panel_template.format(**pcb_data)

        # 1. 拷贝数据文件
        dst_filepath = f"{dst_path}/{pcb_sn}_{time_str}.txt"
        xutil.FileUtil.write_content_to_file(dst_filepath, pcb_content)

        # 2. 拷贝大图
        t_src_image = pcb_entity.get_pcb_t_image()
        t_dst_file = f"{image_dst_path}/{pcb_sn}_{time_str}.jpg"

        xutil.FileUtil.copy_file(t_src_image, t_dst_file)

        # 3. 上传数据到mes
        pcb_param = {
            "MOName": order_id,
            "ProductName": pcb_entity.pcb,
            "Operator": "admin",
            "LineNo": "离线AOI",
            "MachineNo": "离线AOI",
            "PCBSN": pcb_sn,
            "ProcessName": "离线AOI",
            # "MachineResult": ",".join(pcb_robot_list),
            # "RepairResult": ",".join(pcb_repair_list),
            "MachineResult": pcb_entity.get_robot_result("PASS", "NG"),
            "RepairResult": pcb_entity.get_repair_result("PASS", "NG"),
            "CT": str(pcb_entity.get_cycle_time()),
            "Boards": board_data2,
        }

        ret = xrequest.RequestUtil.post_json(api_url, pcb_param, log_number=log_number, timeout=api_timeout)
        if str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

        return self.x_response()
