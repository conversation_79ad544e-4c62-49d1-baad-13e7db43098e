# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/4/15 上午11:33
# Author     ：sch
# version    ：python 3.8
# Description：高科润    https://jira.cvte.com/browse/ATAOI_2019-38768
"""

from typing import Any

from common import xrequest, xcons, xutil
from common.xdecorator import alarm_info
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "gaokerun release v1.0.0.2",
        "device": "AIS203，AIS501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2025-04-15 16:57  ATAOI_2019-38768: 条码校验,上传数据
date: 2025-06-27 16:52  ATAOI_2019-38768: 发送mes失败停机报警
""", }

    other_form = {
        "api_url_checkin": {
            "ui_name": "AOI过站验证接口",
            "value": "http://*************:81/api/mes/QcxCommonValidateAOI",
        },
        "api_url_checkout": {
            "ui_name": "AOI过站接口",
            "value": "http://*************:81/api/mes/QcxPassingStationAOI",
        },
    }

    form = {
        "station_name": {
            "ui_name": "站位名称",
            "value": "AOI",
        },
        "resource_name": {
            "ui_name": "资源名称",
            "value": "SMT-Line01A-AOI",
        },
        "equipment_model": {
            "ui_name": "设备编号",
            "value": "神州-AOI",
        },
        "equipment_user": {
            "ui_name": "操作用户",
            "value": "",
        },
        "work_order": {
            "ui_name": "工单",
            "value": "",
        },
    }

    path = {
        "save_path_img": {
            "ui_name": "图片保存路径",
            "value": "",
        }
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        """
        备注：整板只取一个条码校验一次即可
        """
        api_url_checkin = other_vo.get_value_by_cons_key("api_url_checkin")
        station_name = other_vo.get_value_by_cons_key("station_name")
        resource_name = other_vo.get_value_by_cons_key("resource_name")
        equipment_user = other_vo.get_value_by_cons_key("equipment_user")
        work_order = other_vo.get_value_by_cons_key("work_order")

        only_one_sn = ""
        for sn in other_vo.list_sn():
            if not only_one_sn and sn:
                only_one_sn = sn

        check_in_param = {
            "StationName": station_name,
            "ResourceName": resource_name,
            "EquipmentUser": equipment_user,
            "WorkOrder": work_order,
            "SerialNumber": only_one_sn,
        }

        ret = xrequest.RequestUtil.post_json(api_url_checkin, check_in_param)
        if str(ret.get("Result")) != "1":
            return self.x_response("false", f"mes接口异常，{only_one_sn} 过站验证失败, 错误信息: {ret.get('Message')}")

        return self.x_response()

    @alarm_info
    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        """
        备注：整板只取一个条码过站一次即可
        """
        api_url_checkout = data_vo.get_value_by_cons_key("api_url_checkout")
        station_name = data_vo.get_value_by_cons_key("station_name")
        resource_name = data_vo.get_value_by_cons_key("resource_name")
        equipment_model = data_vo.get_value_by_cons_key("equipment_model")
        equipment_user = data_vo.get_value_by_cons_key("equipment_user")
        work_order = data_vo.get_value_by_cons_key("work_order")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_DATE1)

        time_file2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        pcb_sn = pcb_entity.pcb_barcode

        if not pcb_sn:
            for sn in pcb_entity.all_barcode:
                if sn:
                    pcb_sn = sn
                    break

        if not pcb_sn:
            # 假设一个条码都没有，使用时间戳代替
            pcb_sn = time_file2

        self.log.info(f"barcode: {pcb_sn}")
        base_save_path = f"{save_path_img}/{time_file}/{resource_name}/{work_order}/{pcb_sn}"

        # 1. 拷贝整板图，需要区分203/501机型
        review_path = data_vo.get_review_path()

        pcb_full_save_path = f"{base_save_path}/整板图"
        xutil.FileUtil.ensure_dir_exist(pcb_full_save_path)

        if ";" in review_path:
            # 501合并发送
            pcb_src_img_list = pcb_entity.list_all_pcb_image_v2()

            for pcb_src_filename in pcb_src_img_list:
                if "/T_" in pcb_src_filename:
                    board_side = "Top"
                else:
                    board_side = "Bottom"

                pcb_dst_img = f"{pcb_full_save_path}/{time_file2}_{pcb_sn}_{board_side}.jpg"
                xutil.FileUtil.copy_file(pcb_src_filename, pcb_dst_img)

        else:
            # 203、或者是501的T/B面分开发送
            t_src_img_path = pcb_entity.get_unknown_t_pcb_image()
            if t_src_img_path:
                t_dst_img = f"{pcb_full_save_path}/{time_file2}_{pcb_sn}_Top.jpg"
                xutil.FileUtil.copy_file(t_src_img_path, t_dst_img)

        self.log.info(f"整板图拷贝完成！")

        comp_ng_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_no = board_entity.board_no

            for comp_entity in board_entity.yield_comp_entity():
                comp_result = comp_entity.get_final_result()
                comp_tag = comp_entity.designator

                if comp_result == "PASS":
                    dir_name = "OK图片"
                elif comp_result == "REPASS":
                    dir_name = "NG但人工确认OK图片"
                else:
                    dir_name = "NG图片"

                    comp_ng_list.append({
                        "BoardNo": board_no,
                        "Location": comp_tag,
                        "FailInfo": comp_entity.repair_ng_code,
                        "ReMark": comp_entity.repair_ng_str
                    })

                full_save_path = f"{base_save_path}/{dir_name}"
                xutil.FileUtil.ensure_dir_exist(full_save_path)

                comp_src_img = comp_entity.image_path

                if comp_src_img:
                    comp_dst_img = f"{full_save_path}/{pcb_sn}_{board_no}_{comp_tag}.png"
                    xutil.FileUtil.copy_file(comp_src_img, comp_dst_img)

        checkout_param = {
            "StationName": station_name,
            "ResourceName": resource_name,
            "EquipmentUser": equipment_user,
            "WorkOrder": work_order,
            "SerialNumber": pcb_sn,
            "EquipmentModel": equipment_model,
            "CheckResult": pcb_entity.get_repair_result(1, 0),  # NG:0 OK:1
            "ProgramName": pcb_entity.project_name,
            "InTime": start_time,
            "OutTime": end_time,
            "Cycle": str(pcb_entity.get_cycle_time()),
            "FailInfos": comp_ng_list,
        }

        ret = xrequest.RequestUtil.post_json(api_url_checkout, checkout_param)
        if str(ret.get("Result")) != "1":
            return self.x_response("false", f"mes接口异常，{pcb_sn} 过站失败, 错误信息: {ret.get('Message')}")

        return self.x_response()
