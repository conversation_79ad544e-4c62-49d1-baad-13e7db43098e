# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/05/28 10:08
# Author     ：chencb
# version    ：python 3.8
# Description：宁波舜韵 https://jira.cvte.com/browse/ATAOI_2019-39835
"""
import os
from typing import Any
from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

header = "测试程序,产品条码,设备名称,元件总数,不良总数,测试时间,测试结果,拼板号,不良代码-不良名称-不良位号,|轨道号,批次号,图片存储(base64)\n"
# 数据格式：测试程序,产品条码|设备名称|元件总数|不良总数|测试时间|测试结果|拼板号|不良代码-不良名称-不良位号,不良代码-不良名称-不良位号|轨道号|批次号|不良图片1，不良图片2（base64）
txt_template = "{program_name},{pcb_sn}|{device_name}|{comp_count}|{ng_comp_count}|{test_time}|{test_result}|{board_no}|" \
               "{ng_comp_list}|{track}|{order_id}|{ng_pic_list}\n"


class Engine(ErrorMapEngine):
    version = {
        "customer": ["宁波舜韵", "ningboshunyun"],
        "version": "release v1.0.0.2",
        "device": " AIS20X, AIS30X",
        "feature": ["生成txt文档"],
        "author": "chenchongbing",
        "release": """
date: 2025-05-28 15:00  生成txt文档
date: 2025-06-03 15:50  增加表头
""", }

    path = {
        "txt_path": {
            "ui_name": "txt文档保存路径",
            "value": ""
        }
    }

    form = {
        "device_name": {
            "ui_name": "设备名称",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()
        # 只生成复判后的数据
        if inspect_type == xcons.INSPECTOR:
            self.log.info("只有复判后才生成txt，这次发送为机器检测，直接返回不处理")
            return self.x_response()

        txt_path = data_vo.get_value_by_cons_key("txt_path", not_null=True)
        device_name = data_vo.get_value_by_cons_key("device_name")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            ng_comp_list = []
            ng_pic_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.repair_result:
                    continue

                ng_code = comp_entity.repair_ng_code
                ng_desc = comp_entity.repair_ng_str
                designator = comp_entity.designator
                # 不良代码-不良名称-不良位号
                ng_comp_list.append(f'{ng_code}-{ng_desc}-{designator}')
                ng_pic_list.append(comp_entity.image_path)

            save_data = {
                "program_name": pcb_entity.project_name,
                "pcb_sn": pcb_entity.pcb_barcode,
                "device_name": device_name,
                "comp_count": board_entity.comp_total_number,
                "ng_comp_count": board_entity.comp_repair_ng_number,
                "test_time": test_time,
                "test_result": board_entity.get_final_result('PASS', 'PPASS', 'NG'),
                "board_no": board_entity.board_no,
                "ng_comp_list": ','.join(ng_comp_list),
                "track": pcb_entity.track_index,
                "order_id": pcb_entity.order_id,
                "ng_pic_list": ','.join(ng_pic_list),
            }
            self.log.info("不良图片转base64编码数据太大，日志只输出路径，实际保存的是base64编码！")
            self.log.info(f"生成拼板【{board_entity.board_no}】数据：{save_data}")
            ng_pic_list_base64 = [xutil.ImageUtil.file_to_base64_content(pic) for pic in ng_pic_list]
            save_data["ng_pic_list"] = ','.join(ng_pic_list_base64)
            txt_content = txt_template.format(**save_data)
            # 文件命名：整板条码_时间戳
            file_path = f"{txt_path}/{pcb_entity.pcb_barcode}_{test_time}.txt"

            # 如果文件不存在或为空，写入表头
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                xutil.FileUtil.append_content_to_file(file_path, header)

            xutil.FileUtil.append_content_to_file(file_path, txt_content)

        return self.x_response()
