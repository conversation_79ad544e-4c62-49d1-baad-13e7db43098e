# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/26 下午4:55
# Author     ：sch
# version    ：python 3.8
# Description：首信天发 / 常州首信天发电子有限公司
"""

from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "shouxintianfa release v1.0.0.1",
        "device": "AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-27 10:09  上传数据到MES
""", }

    other_form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "http://**************:89/Odata/OrderItemMarkStationRecord",
        }
    }

    form = {
        "line_name": {
            "ui_name": "线别名称",
            "value": "",
        },
        "station": {
            "ui_name": "站别名称",
            "value": "",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        self.custom_str_to_chinese()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        line_name = data_vo.get_value_by_cons_key("line_name")
        station = data_vo.get_value_by_cons_key("station")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        only_one_sn = pcb_entity.pcb_barcode

        comp_data_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not only_one_sn and barcode:
                only_one_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_data_list.append({
                        "Name": comp_entity.designator,
                        "Value": comp_entity.repair_ng_str
                    })

        data_param = {
            "Item": {
                "SN": only_one_sn
            },
            "Line": {
                "Name": line_name
            },
            "Station": {
                "Station": {
                    "Name": station
                }
            },
            "Status": pcb_entity.get_final_result(True, True, False),
            "ErrorCode": {
                "Code": "AOI不良"
            },
            "Mark": pcb_entity.get_final_result(),
            "ExpandItems": comp_data_list
        }

        ret = xrequest.RequestUtil.post_json(api_url, data_param)
        if ret.get("Error"):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('Msg')}")

        return self.x_response()
