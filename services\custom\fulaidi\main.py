# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/10/9 下午5:31
# Author     ：sch
# version    ：python 3.8
# Description：福莱迪
"""

from typing import Any

from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "fulaidi release v1.0.0.1",
        "device": "203，303，301，401",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-10-09 17:31  写数据到数据库
""", }

    form = {
        "sql_host": {
            "ui_name": "数据库地址",
            "value": "127.0.0.1"
        },
        "sql_port": {
            "ui_name": "数据库端口",
            "value": "1433"
        },
        "sql_username": {
            "ui_name": "数据库账号",
            "value": "sa"
        },
        "sql_password": {
            "ui_name": "数据库密码",
            "value": "Xx123456."
        },
        "device_id": {
            "ui_name": "设备ID",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

        return self.x_response()

