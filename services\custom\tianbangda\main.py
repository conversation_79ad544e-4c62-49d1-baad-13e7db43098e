"""
# File       : main.py
# Time       ：2025/07/01 10:03
# Author     ："wxc"
# version    ：python 3.8
# Description：天邦达
"""
import platform
import re
from typing import Any

from common import xrequest, xutil, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo

checkparam = """<DataRoot Method="Upload" CallType="Post" TypeName="SMTAOICheck">
    <LotSN>{sn}</LotSN>
    <TestSpec>{process}</TestSpec>
</DataRoot>"""

uploadparam = """<DataRoot Method="Upload" CallType="Post" TypeName="SMTAOIUpload">
    <LotSN>{sn}</LotSN>
    <TestDate>{test_time}</TestDate>
    <PadQty>{qty}</PadQty>
    <Side>{side}</Side>
    <LineName>{line_name}</LineName>
    <LineNumber>{line}</LineNumber>
    <TestResult>{result}</TestResult>
    <TestSpec>{process}</TestSpec>
    <TestProgram>{project_name}</TestProgram>
    <MachineVendor>{machine_vendor}</MachineVendor>
    <Data>
{board_data}    </Data>
</DataRoot>"""
boardparam = """        <Item PanelID="{board_no}" PanelSN="{sn}" Result="{result}" BadDes="{ng_str}" />
"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["天邦达", "tianbangda"],
        "version": "release v1.0.0.3",
        "device": "AIS430",
        "feature": ["条码校验", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-07-01 10:56 ATAOI_2019-40432：条码校验和上传数据
date: 2025-07-10 12:02 上传数据中增加面别、电脑名
date: 2025-07-14 14:25 修改获取side逻辑，project_name有AB（阴阳板），也需兼容此面别
"""
    }
    other_form = {
        "upload_api_url": {
            "ui_name": "上传数据接口",
            "value": ""
        },
        "check_api_url": {
            "ui_name": "条码校验接口",
            "value": ""
        }
    }
    form = {
        "process": {
            "ui_name": "工序编码",
            "value": ""
        },
        "opertor": {
            "ui_name": "操作员工号",
            "value": ""
        },
        "line": {
            "ui_name": "线别",
            "value": ""
        },
        "machine_vendor": {
            "ui_name": "供应商名称",
            "value": ""
        },
    }

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        check_api_url = other_vo.get_value_by_cons_key("check_api_url")
        process = other_vo.get_value_by_cons_key("process")

        sn_list = other_vo.list_sn()
        for sn in sn_list:
            param = {
                "sn": sn,
                "process": process
            }
            ret = xrequest.RequestUtil.post_xml(check_api_url, checkparam.format(**param))
            root = xutil.XmlUtil.get_xml_root_by_str(ret)
            msg = root.find("ReturnMsg").text
            ret = root.find("ReturnValue").text
            if ret != "0":
                return self.x_response("false", f"mes接口异常,error:{msg}")
        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        upload_api_url = data_vo.get_value_by_cons_key("upload_api_url")
        process = data_vo.get_value_by_cons_key("process")
        line = data_vo.get_value_by_cons_key("line")
        machine_vendor = data_vo.get_value_by_cons_key("machine_vendor")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        hostname = platform.node()  # LineName取电脑名
        self.log.info(f"计算机名称（主机名）:{hostname}")

        # side 取程序名后三位字符（TOP/BOT）
        project_name = pcb_entity.project_name
        pattern = r'_(BOT|TOP|AB)'  # 匹配 _BOT 或 _TOP 或 _AB
        match = re.search(pattern, project_name)
        if match:
            side = match.group(1)
            self.log.info(f"程序名 {project_name} 截取到side: {side}")
        else:
            self.log.warning(f"程序名 {project_name} 截取不到side，默认给空值")
            side = ""

        board_data_str = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            comp_ng_str = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_ng_str.append(f"{comp_entity.designator}:{comp_entity.repair_ng_str}")

            board_data_str += boardparam.format(**{
                "board_no": board_entity.board_no,
                "sn": board_entity.barcode,
                "result": board_entity.get_final_result("PASS", "PASS", "FAIL"),
                "ng_str": ",".join(comp_ng_str)
            })

        upload_data = {
            "sn": pcb_entity.pcb_barcode,
            "test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "qty": pcb_entity.board_count,
            "side": side,
            "line_name": hostname,  # 电脑名
            "line": line,
            "result": pcb_entity.get_final_result("PASS", "PASS", "FAIL"),
            "process": process,
            "project_name": pcb_entity.project_name,
            "machine_vendor": machine_vendor,
            "board_data": board_data_str
        }
        ret = xrequest.RequestUtil.post_xml(upload_api_url, uploadparam.format(**upload_data))
        root = xutil.XmlUtil.get_xml_root_by_str(ret)
        msg = root.find("ReturnMsg").text
        ret = root.find("ReturnValue").text
        if ret != "0":
            return self.x_response("false", f"mes接口异常,error:{msg}")
        return self.x_response()
