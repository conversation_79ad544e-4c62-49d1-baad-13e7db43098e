# !/usr/bin/env python
# -*-coding:utf-8 -*-


"""
# File       : main.py
# Time       ：2024/12/27 下午14:47
# Author     ：gyr
# version    ：python 3.8
# Description：东莞瑞讯
"""
import os.path
from typing import Any

from common import xcons, xutil
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo

txt_template = """{equipment_model},{pcb_sn},{pcb_side},{test_time},{testing_time},{pcb_robot_result},{pcb_repair_result},{pcb_final_result},{board_no},{barcode},{board_robot_result},{board_repair_result},{board_final_result},{board_info},{repair_person}
"""


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanruixun release v1.0.0.5",
        "device": "AIS401",
        "feature": ["上传数据"],
        "author": "gaoyurui",
        "release": """
date: 2024-12-27 11:30 jira:36263,生成txt文件
date: 2025-01-06 14:40 jira:36263,适配windows,新增自动补全条码
date: 2025-01-15 14:10 jira:36263,增加板面信息
date: 2025-01-17 10:02 jira:36263,board_info不良描述改为不良代码
date: 2025-01-17 14:45 jira:36263,修改board_info参数位置
""", }

    path = {
        "save_path": {
            "ui_name": "文件保存路径",
            "value": ""
        }
    }

    form = {
        "equipment_model": {
            "ui_name": "设备名称",
            "value": ""
        },
        "barcode_length": {
            "ui_name": "条码后缀长度",
            "value": ""
        }
    }

    combo = {
        "auto_barcode": {
            "ui_name": "开启自动补全条码",
            "item": [
                "是",
                "否"
            ],
            "value": "否"
        },
        "board_surface": {
            "ui_name": "板面",
            "item": [
                "T",
                "B"
            ],
            "value": "T"
        }
    }

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        equipment_model = data_dao.get_value_by_cons_key("equipment_model")
        save_path = data_dao.get_value_by_cons_key("save_path")
        barcode_length = data_dao.get_value_by_cons_key("barcode_length", to_int=True)
        auto_barcode = data_dao.get_value_by_cons_key("auto_barcode")
        pcb_entity = data_dao.pcb_entity
        board_surface = data_dao.get_value_by_cons_key("board_surface")
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode  # 整版条码
        time_data = pcb_entity.get_start_time().strftime("%Y%m%d%H%M%S")  # 日期时间
        file_name = f"{pcb_sn}_{time_data}.txt"  # 文件名称
        file_path = os.path.join(save_path, file_name)

        pcb_board_side = board_surface

        file_content = ""

        # 第一块拼版的条码
        one_barcode = None

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode  # 拼板条码
            board_no = board_entity.board_no  # 拼版号

            # 判断是否是第一块拼版，并且自动补全条码功能已开启
            if auto_barcode and int(board_no) == 1:
                # 保存第一块拼版的条码作为基准条码
                one_barcode = barcode
            elif auto_barcode and int(board_no) > 1 and one_barcode and barcode_length > 0:
                try:
                    # 获取条码前缀
                    front_data = one_barcode[:-barcode_length]
                    # 获取条码后缀
                    next_data = int(one_barcode[-barcode_length:])
                    # 获得新的后缀,用0补齐到指定长度
                    new_next_data = str(next_data + int(board_no) - 1).zfill(barcode_length)
                    # 新条码
                    barcode = front_data + new_next_data
                except (ValueError, IndexError):
                    self.log.error(f"条码格式错误或后缀长度设置不正确: {one_barcode}, {barcode_length}")
                    barcode = board_entity.barcode
            else:
                # 如不满足自动补全，使用原条码
                barcode = board_entity.barcode

            final_result = board_entity.get_final_result()  # 最终检测结果

            #  拼版的不良信息
            board_info = []
            if final_result == "NG":
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        #  将元器件的位号和复判的ng不良描述赋给board_info
                        board_info.append({
                            "code_str": comp_entity.repair_ng_code,
                            "comp_designator": comp_entity.designator
                        })

                # 遍历board_info的temp元素，temp中存的是code_str和comp_designator
                board_info_str = ";".join([f"{temp['code_str']}:{temp['comp_designator']}" for temp in board_info])
            else:
                #  如果final_result是OK, 则赋N/A
                # board_info.append({"comp_designator": "N/A", "code_str": "N/A"})
                board_info_str = "N/A"


            file_content += txt_template.format(**{
                "equipment_model": equipment_model,
                "pcb_sn": pcb_sn,
                "pcb_side": pcb_board_side,
                "test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "testing_time": pcb_entity.get_cycle_time(),
                "pcb_robot_result": pcb_entity.get_robot_result(),
                "pcb_repair_result": pcb_entity.get_repair_result(),
                "pcb_final_result": pcb_entity.get_final_result(),
                "board_no": board_no,
                "barcode": barcode,
                "board_robot_result": board_entity.get_robot_result(),
                "board_repair_result": board_entity.get_repair_result(),
                "board_final_result": board_entity.get_final_result(),
                "board_info": board_info_str,
                "repair_person": pcb_entity.repair_user
            })

        xutil.FileUtil.write_content_to_file(file_path, file_content, window_line=True)

        return self.x_response()
