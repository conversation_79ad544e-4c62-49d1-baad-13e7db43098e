# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/6/7 上午9:54
# Author     ：sch
# version    ：python 3.8
# Description：万颗子
"""
import json
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "wankezi release v1.0.0.1",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-06-07 10:53  上传数据到MES
""", }

    form = {
        "tcp_host": {
            "ui_name": "服务器地址",
            "value": "**************",
        },
        "tcp_port": {
            "ui_name": "服务器端口号",
            "value": "6789",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "AOI001",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        device_name = data_vo.get_value_by_cons_key("device_name")
        tcp_host = data_vo.get_value_by_cons_key("tcp_host")
        tcp_port = data_vo.get_value_by_cons_key("tcp_port", to_int=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        board_data = []
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0

        board_user_ng_count = 0
        board_robot_ng_count = 0

        pcb_sn = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if board_entity.is_robot_ng():
                board_robot_ng_count += 1

            if board_entity.is_repair_ng():
                board_user_ng_count += 1

            comp_data = []

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_data.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })

            board_data.append({
                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            })

        json_data = {
            "device_name": device_name,
            "pcb_sn": pcb_entity.pcb_barcode,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_user_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,
            "board_data": board_data
        }

        json_data_str = json.dumps(json_data, ensure_ascii=False,  separators=(",", ":"))

        xrequest.SocketUtil.send_data_to_socket_server(tcp_host, tcp_port, json_data_str)

        pcb_image_list = pcb_entity.list_all_pcb_image()

        pcb_result = pcb_entity.get_repair_result('ok', 'ng')

        for item in pcb_image_list:
            if "B_" in item:
                board_side = "B"
            else:
                board_side = "T"

            filename = f"{pcb_sn}_{board_side}_{pcb_result}.jpg;".encode('utf-8')

            with open(item, 'rb') as f:
                img_bytes = f.read()
                img_len = f"{len(img_bytes)};".encode('utf-8')

                self.log.info(f"开始上传图片---------------参数列表---")
                self.log.info(f"filename: {filename}")
                self.log.info(f"filesize: {img_len}")
                self.log.info(f"源图片地址: {item}")
                self.log.info(f"开始上传图片---------------参数列表---")

                full_param = filename + img_len + img_bytes
                xrequest.SocketUtil.send_bytes_to_socket_server(tcp_host, tcp_port, full_param, is_log=False)

        return self.x_response()
