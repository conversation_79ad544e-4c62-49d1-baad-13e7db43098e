# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : other_setting.py
# Time       ：2022/12/29 下午4:55
# Author     ：sch
# version    ：python 3.8
# Description：系统-登录
"""
from PyQt5.QtWidgets import *

from common import xutil
from vo.mes_vo import OtherVo
from services.route import engine
from templates.login_widget import Ui_LoginWidget


class LoginViews(QDialog, Ui_LoginWidget):
    def __init__(self, main_window):
        super(LoginViews, self).__init__()
        self.setupUi(self)

        self.main_window = main_window

        self.btn_login.clicked.connect(self.login_on_click)

    def login_on_click(self):
        """
        登录
        :return:
        """
        # 保存界面上的配置到缓存文件
        cache_data = xutil.CacheUtil.get_cache_data()
        is_remember_password = self.check_box_remember_password.isChecked()
        username = self.line_username.text()
        password = self.line_password.text()

        cache_data["login_remember_password"] = is_remember_password
        cache_data["login_auto_login"] = self.check_auto_login.isChecked()
        cache_data["login_username"] = username

        if is_remember_password:
            cache_data["login_password"] = password
        else:
            cache_data["login_password"] = ""

        xutil.CacheUtil.save_cache_data(cache_data)

        try:
            # 如果登录成功就关闭窗口
            other_dao = OtherVo({
                "login_username": username,
                "login_password": password
            }, self.main_window.config_data)

            custom_ret = engine.login_mes(other_dao, "")

            if not custom_ret:
                self.non_develop_message_on_click()
                return

            else:
                if not custom_ret.get("result"):
                    QMessageBox.warning(self, "登录失败", f"登录失败，error：{custom_ret.get('string')}")
                    return

                else:
                    self.main_window.log_info(f"用户[{username}]登录成功！")

                    self.close()

        except Exception as e:
            self.main_window.log_info(f"其他异常，登录失败，error：{e}", False)

    def non_develop_message_on_click(self):
        """
        未开发弹窗
        :return:
        """
        msg = """该功能正在开发中！"""
        QMessageBox.about(self, "提示", msg)  # noqa
