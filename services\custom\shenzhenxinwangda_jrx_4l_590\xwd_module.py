# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : xwd_module.py
# Time       ：2024/12/1 下午11:32
# Author     ：sch
# version    ：python 3.8
# Description：存储桶模块
"""
import os
import time
import traceback

import boto3
from PIL import Image, ImageDraw, ImageFont
from PIL.ImageFile import ImageFile
from botocore.exceptions import ClientError

from common import xutil
from common.xutil import log
from entity.MesEntity import BoardEntity


def check_bucket_exists(s3_client, bucket_name):
    """检查桶是否存在"""
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        log.info(f"Bucket '{bucket_name}' exists.")
        return True
    except ClientError as e:
        print(e.response)
        error_code = int(e.response['Error']['Code'])
        if error_code == 404:
            log.info(f"Bucket '{bucket_name}' does not exist.")
            return False
        else:
            raise Exception(f"检查存储桶出错，error: {e.response}")  # 如果是其他错误，重新抛出异常


def create_bucket(s3_client, bucket_name):
    """创建一个新的桶（不设置任何特殊权限）"""
    try:
        response = s3_client.create_bucket(Bucket=bucket_name)
        log.info(f"Bucket '{bucket_name}' created successfully. response: {response}")
        return True
    except ClientError as e:
        log.info(f"Error creating bucket: {e}")
        return False


def upload_file_and_get_public_url(
        s3_client,
        file_path,
        bucket_name,
        object_key,
):
    """上传文件到指定的存储桶中，并返回文件的永久访问链接"""
    try:
        log.info(f"{file_path} ---> start put object")
        xutil.FileUtil.log_file_size(file_path)

        with open(file_path, 'rb') as data:
            response = s3_client.put_object(
                Bucket=bucket_name,
                Key=object_key,
                Body=data,
                ACL='public-read'  # 设置对象的 ACL 为公共可读
            )
        log.info(f"File uploaded successfully. response: {response}")
        log.info(f"{file_path} ---> end put object")

        # 构建非签名的公共 URL
        # public_url = urljoin(endpoint_url + '/', f'{bucket_name}/{object_key}')
        # log.info(f"Public URL: {public_url}")

        presigned_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': object_key},
            ExpiresIn=315360000  # URL有效期为10年
        )
        print(f"下载链接：{presigned_url}")
        return presigned_url
    except Exception as e:
        log.info(f"Error uploading file: {e}")
        log.warning(f"{traceback.format_exc()}")
        return ""


@xutil.time_cost
def upload_image_to_s3(
        endpoint_url,
        aws_access_key_id,
        aws_secret_access_key,
        file_path,
        bucket_name,
        object_key,
        region_name="us-west-2",

) -> str:
    """
    主函数：上传图片到 S3 并获取永久访问链接

    :param endpoint_url: 服务器地址
    :param file_path: 要上传的文件路径
    :param bucket_name: S3 Bucket 的名称
    :param object_key: 在 S3 中保存文件的对象名称 (默认为文件名)
    :param aws_access_key_id: AWS Access Key ID
    :param aws_secret_access_key: AWS Secret Access Key
    :param region_name: AWS Region (默认是 'us-east-1')
    """
    log.info(f"upload_image_to_s3正在上传图片： src:{file_path}    dst:{object_key}")

    if not file_path:
        log.warning(f"无文件可上传！")
        return ""

    if not os.path.exists(file_path):
        log.warning(f"文件不存在！")
        return ""

    time.sleep(0.2)
    # 创建 S3 客户端，并指定自定义终端节点和其他配置选项
    s3_client = boto3.client(
        's3',
        endpoint_url=endpoint_url,  # 自定义终端节点
        # config=Config(
        #     s3={'addressing_style': 'path'},  # 使用路径样式 URL
        #     # signature_version='s3v4'  # 如果适用，可以指定签名版本
        # ),
        region_name=region_name,  # 如果适用，可以指定区域名称
        aws_access_key_id=aws_access_key_id,  # 替换为你的访问密钥
        aws_secret_access_key=aws_secret_access_key  # 替换为你的秘密密钥
    )

    # good --------------
    # s3_client = boto3.client(
    #     's3',
    #     endpoint_url=endpoint_url_,  # 如果是 AWS S3，则不需要此参数
    #     config=Config(
    #         s3={'addressing_style': 'path'},  # 使用路径样式 URL
    #         # signature_version='s3v4'  # 如果适用，可以指定签名版本
    #     ),
    #     region_name='us-west-2',  # 替换为你的实际区域
    #     aws_access_key_id=aws_access_key_id_,  # 替换为你的访问密钥
    #     aws_secret_access_key=aws_secret_access_key_  # 替换为你的秘密密钥
    # )
    # good --------------

    print("链接成功", s3_client)

    # 检查桶是否存在，如果不存在则创建

    is_exists = check_bucket_exists(s3_client, bucket_name)
    print(is_exists)

    if not is_exists:
        if not create_bucket(s3_client, bucket_name):
            log.warning("Failed to create bucket. Exiting.")
            return ""

    # 上传文件并获取永久访问链接
    return upload_file_and_get_public_url(
        s3_client,
        file_path,
        bucket_name,
        object_key,
    )


ImageFile.LOAD_TRUNCATED_IMAGES = True  # 全局允许加载截断图片


def safe_open_image(path: str, retries: int = 10, stability_check: bool = False, stability_timeout: int = 5) -> Image:
    """
    安全加载图片，支持重试和文件稳定性检查
    path: 图片路径
    retries: 重试次数
    stability_check: 检查文件稳定性
    stability_timeout: 文件稳定性检查的超时时间
    """
    xutil.FileUtil.log_file_size(path)

    for i in range(retries):
        try:
            with open(path, 'rb') as f:
                img = Image.open(f)
                # img.verify()
                img.load()  # 确保图片可以完全加载

                log.info(f"图片大小: {img.size}")
                return img
        except Exception as e:
            if retries == i + 1:
                log.warning(f"图片打开失败，error: {traceback.format_exc()}  打开失败的文件：{path}")
                raise e

            # 等待0.2秒后重试
            time.sleep(0.2)


def concat_ng_info_to_board_image(
        board_image_path,
        board_entity: BoardEntity
) -> str:
    # 打开拼版图并创建新图片
    board_no = board_entity.board_no
    board_origin_img = safe_open_image(board_image_path)
    width, height = board_origin_img.size
    new_width = width + 1500
    new_img = Image.new('RGB', (new_width, height), 'white')
    new_img.paste(board_origin_img, (0, 0))

    # 在右侧白板添加该拼版的NG信息
    draw = ImageDraw.Draw(new_img)
    try:
        title_font = ImageFont.truetype("/usr/share/fonts/truetype/freefont/FreeSans.ttf", 120)
        font = ImageFont.truetype("/usr/share/fonts/truetype/freefont/FreeSans.ttf", 100)
    except Exception as font_err:
        log.warning(f"无法加载指定字体：{font_err}，将使用默认字体")
        title_font = font = ImageFont.load_default()

    # 添加标题（包含拼版号）
    title = f"Board {board_no} NG Components:"
    draw.text((width + 10, 50), title, fill='black', font=title_font)

    # 添加该拼版的NG位号列表及详细信息
    y_position = 200

    if board_entity.is_repair_ng():
        log.info(f"共有 {board_entity.comp_repair_ng_number} 个ng器件")

        # 查找对应的组件信息
        for i, comp_entity in enumerate(board_entity.yield_comp_entity(), 1):
            if comp_entity.is_repair_ng():
                # 获取所有需要的信息
                comp_type = comp_entity.type
                ng_code = comp_entity.repair_ng_code
                ng_desc = comp_entity.repair_ng_str
                designator = comp_entity.designator

                # 将所有信息用'-'连接在一行
                ng_info = f"{i}. {designator}-{comp_type}-{ng_code}-{ng_desc}"
                words = ng_info.split()
                line = words[0]
                for word in words[1:]:
                    if len(line + " " + word) * 10 > 750:
                        draw.text((width + 20, y_position), line, fill='red', font=font)
                        y_position += 180
                        line = "    " + word
                    else:
                        line += " " + word
                draw.text((width + 20, y_position), line, fill='red', font=font)
                y_position += 220
                break
        suffix = f"_board{board_no}_ng_info.jpg"
    else:
        draw.text((width + 20, y_position), "No NG components", fill='black', font=font)

        suffix = f"_board{board_no}_ok_info.jpg"

    log.info(f"白板图ng器件信息生成完毕")

    # 3. 保存新图片
    processed_filename = os.path.splitext(board_image_path)[0] + suffix
    new_img.save(processed_filename, quality=95)
    log.info(f"已生成带信息的拼版图: {processed_filename}")

    return processed_filename


def concat_ng_info_to_pcb_image(
        pcb_src_filename: str,
        ng_board_info: list,
        board_side: str
) -> str:
    """
    给整版图拼接NG信息
    """
    Image.MAX_IMAGE_PIXELS = 20000 * 20000
    # 用白图和原整版图拼接一个新图片
    origin_pcb_image = safe_open_image(pcb_src_filename)
    # 获取原图的宽度（width）和高度（height）
    width, height = origin_pcb_image.size
    # 计算新图片的宽度，原图宽度增加 1500 像素
    new_width = width + 1500
    # 创建一个新的空白图片，宽度为 new_width，高度为 height，背景为白色
    new_img = Image.new('RGB', (new_width, height), 'white')
    new_img.paste(origin_pcb_image, (0, 0))

    # 在白板右侧添加NG信息
    draw = ImageDraw.Draw(new_img)
    font = ImageFont.truetype("/usr/share/fonts/truetype/freefont/FreeSans.ttf", 100)
    log.info("成功加载FreeSans字体，大小为100")

    # 添加标题
    title_font = ImageFont.truetype("/usr/share/fonts/truetype/freefont/FreeSans.ttf", 120)

    title = "NG Boards Information:"
    draw.text((width + 10, 50), title, fill='black', font=title_font)

    # 添加NG拼版信息
    y_position = 200
    has_ng_info = False
    for board_info in ng_board_info:
        board_no = board_info['board_no']
        ng_descriptions = board_info['ng_descriptions']
        if ng_descriptions:
            has_ng_info = True
            ng_info = f"Board {board_no}: {', '.join(ng_descriptions)}"
            # 如果一行太长，进行换行处理
            words = ng_info.split()
            line = words[0]
            for word in words[1:]:
                if len(line + " " + word) * 10 > 750:
                    draw.text((width + 20, y_position), line, fill='red', font=font)
                    y_position += 180
                    line = "    " + word
                else:
                    line += " " + word
            draw.text((width + 20, y_position), line, fill='red', font=font)
            y_position += 220

    if not has_ng_info:
        draw.text((width + 20, y_position), "No NG components", fill='black', font=font)

    if has_ng_info:
        suffix_pcb = f"_{board_side}_with_ng_info.jpg"
    else:
        suffix_pcb = f"_{board_side}_with_ok_info.jpg"

    # 保存新图片, 在同级目录下保存
    processed_filename = os.path.splitext(pcb_src_filename)[0] + suffix_pcb
    new_img.save(processed_filename, quality=95)
    log.info(f"已生成带NG信息的整版图: {processed_filename}")

    return processed_filename


# 示例调用
if __name__ == "__main__":
    # endpoint_url_ = "http://zzzx.zhhcp.sunwoda.com"
    # aws_access_key_id_ = "enp6eA=="
    # aws_secret_access_key_ = "47f992d5f604ef025470821d86da8317"
    #
    # file_path_ = './COMP1039_1039.png'  # 替换为你的图片路径
    # bucket_name_ = 'my-new-bucket'
    # object_key_ = '20241216/COMP1039_1039_test13.png'
    #
    # ret_url = upload_image_to_s3(
    #     endpoint_url_,
    #     aws_access_key_id_,
    #     aws_secret_access_key_,
    #     file_path_,
    #     bucket_name_,
    #     object_key_
    # )
    # if ret_url:
    #     print(f"Image uploaded and available at: {ret_url}")
    safe_open_image("1")
