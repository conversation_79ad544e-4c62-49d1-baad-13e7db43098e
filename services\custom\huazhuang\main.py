# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/2 上午9:32
# Author     ：sch
# version    ：python 3.8
# Description：东莞华庄
"""
from typing import Any

from common import xrequest, xutil, xcons
from common.xutil import filter_v3_status_code
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

global_data = {}


class Engine(ErrorMapEngine):
    version = {
        "title": "huazhuang release v1.0.0.1",
        "device": "501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-02 11:01  设备状态，条码校验，上传数据
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://mes.gdhuazhuang.net:20080/mrs/checkRoute"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://mes.gdhuazhuang.net:20080/mrs/createRoute"
        },
        "api_url_status": {
            "ui_name": "接口URL(设备状态)",
            "value": "http://************:7777/dataacquisition-api/aoi/deviceStatus/v1"
        },
        "station_id": {
            "ui_name": "站位编号",
            "value": ""
        },
        "line_no": {
            "ui_name": "线别",
            "value": "LINE1"
        },
        "machine_no": {
            "ui_name": "设备编号",
            "value": "SMT00001"
        },
    }

    form = {
        "operator_id": {
            "ui_name": "员工工号",
            "value": ""
        },
        "order_id": {
            "ui_name": "工单号",
            "value": ""
        },
        "weight": {
            "ui_name": "单重",
            "value": "0"
        },
        "pack_no": {
            "ui_name": "箱号",
            "value": ""
        },
    }

    combo = {
        "re_test": {
            "ui_name": "是否允许重复测试",
            "item": ["是", "否"],
            "value": "是"
        },
    }

    path = {
        "save_path_img": {
            "ui_name": "整板图路径",
            "value": ""
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        operator_id = data_vo.get_value_by_cons_key("operator_id")
        order_id = data_vo.get_value_by_cons_key("order_id")
        station_id = data_vo.get_value_by_cons_key("station_id")

        weight = data_vo.get_value_by_cons_key("weight")
        pack_no = data_vo.get_value_by_cons_key("pack_no")
        save_path_img = data_vo.get_value_by_cons_key("save_path_img", not_null=True)

        try:
            weight = float(weight)
        except Exception as err:
            return self.x_response("false", f"单重必须为数字！error：{err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            cache_sn_list = global_data.get("cache_sn_list", [])
            if barcode in cache_sn_list:
                cache_sn_map = global_data.get("cache_sn_map", {})

                error_msg = cache_sn_map.get(barcode, "")

                return self.x_response("false", f"该条码校验失败，error：{error_msg}，本次不上传数据到mes！")

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag = comp_entity.designator
                    comp_ng_str = comp_entity.repair_ng_str
                    comp_data_list.append(f"{comp_tag}_{comp_ng_str}")

            data_param = {
                "pcbSeq": barcode,
                "prodNo": order_id,
                "stationNo": station_id,
                "result": "PASS" if board_entity.repair_result else "FAIL",
                "testItem": ",".join(comp_data_list),
                "userNo": operator_id,
                "weight": weight,
                "packNo": pack_no,
                "rmk1": pcb_entity.pcb_barcode,
                "rmk2": "",
                "rmk3": "",
                "rmk4": "",
            }

            ret = xrequest.RequestUtil.get(api_url_data, data_param)

            if str(ret.get("msgId")) != "0":
                ret_res = self.x_response("false", f"接口响应异常, 上传数据失败, error: {ret.get('msgStr')}")

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        for item in pcb_entity.list_all_pcb_image():
            if "B_" in item:
                board_side = "B"
            else:
                board_side = "T"

            dst_filename = f"{save_path_img}/{board_side}_{start_time}_{pcb_sn}.jpg"
            xutil.FileUtil.copy_file(item, dst_file=dst_filename)

        return ret_res

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station_id = other_vo.get_value_by_cons_key("station_id")
        order_id = other_vo.get_value_by_cons_key("order_id")
        re_test = other_vo.get_value_by_cons_key("re_test")

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            param = {
                "pcbSeq": sn,
                "prodNo": order_id,
                "stationNo": station_id,
                "retest": 1 if re_test == "是" else 0
            }

            ret = xrequest.RequestUtil.get(api_url_check, params=param)
            if str(ret.get("msgId")) != "0":
                return self.x_response("false", f"接口响应异常, 条码校验失败, error: {ret.get('msgStr')}")

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_status = other_vo.get_value_by_cons_key("api_url_status")
        line_no = other_vo.get_value_by_cons_key("line_no")
        machine_no = other_vo.get_value_by_cons_key("machine_no")

        device_status = other_vo.get_device_status_str()
        status_code = other_vo.get_old_device_status_code()

        if status_code in ["01", "02", "04"]:
            state = "RUN"
        elif status_code == "03":
            state = "STOP"
        elif status_code in ["10", "11", "20", "21", "12", "13", "22", "23", "99"]:
            state = "ALARM"
        else:
            self.log.warning(f"未知的设备状态，不上传MES！")
            return self.x_response()

        status_param = {
            "machineNo": machine_no,
            "machineState": state,
            "lineNo": line_no,
            "reasonCode": status_code,
            "reasonText": device_status,
            "reportTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
        }

        ret = xrequest.RequestUtil.post_json(api_url_status, status_param)
        if str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('message')}")

        return self.x_response()
