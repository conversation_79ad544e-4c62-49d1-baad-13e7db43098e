# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/11/30 上午10:07
# Author     ：sch
# version    ：python 3.8
# Description：欣旺达_摩洛哥
"""
from typing import Any

from PyQt5.QtWidgets import QDialog, QVBoxLayout, QComboBox, QPushButton, QDesktopWidget

from common import xrequest, xcons, xutil, xenum
from common.xutil import log, filter_v3_status_code
from vo.mes_vo import DataVo, ButtonVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine

device_map = {
    "品质异常": "21",
    "环境超标": "22",
    "能源异常": "23",
    "IT问题": "24",
    "等待物料": "25",
    "品种切换": "26",
    "来料异常": "27",
    "工艺验证": "28",
    "换工装": "29",
    "培训": "61",
    "休息就餐": "62",
    "设备清洁点检": "63",
    "首件制作": "64",
    "无排产": "65",
    "设备保养": "66",
    "盘点": "67",
}


class MyDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.button = QPushButton("上传")
        self.combo_box = QComboBox()

        self.initUI()
        self.center()

        self.button.clicked.connect(self.accept)

    def initUI(self):
        # 创建垂直布局管理器
        layout = QVBoxLayout()

        # 创建下拉框并添加选项

        for k in device_map:
            self.combo_box.addItem(k)

        # self.combo_box.addItem("品质异常")
        # self.combo_box.addItem("环境超标")
        # self.combo_box.addItem("能源异常")
        # self.combo_box.addItem("IT问题")
        # self.combo_box.addItem("等待物料")
        # self.combo_box.addItem("品种切换")
        # self.combo_box.addItem("来料异常")
        # self.combo_box.addItem("工艺验证")
        # self.combo_box.addItem("换工装")
        # self.combo_box.addItem("培训")
        # self.combo_box.addItem("休息就餐")
        # self.combo_box.addItem("设备清洁点检")
        # self.combo_box.addItem("首件制作")
        # self.combo_box.addItem("无排产")
        # self.combo_box.addItem("设备保养")
        # self.combo_box.addItem("盘点")
        layout.addWidget(self.combo_box)

        # 创建按钮
        layout.addWidget(self.button)

        # 将布局设置为此对话框的布局
        self.setLayout(layout)

        # 设置对话框的标题和大小
        self.setWindowTitle("上传设备状态")
        self.setGeometry(200, 150, 300, 200)

    def center(self):
        """
        屏幕居中
        :return:
        """
        # 获取屏幕坐标系
        screen = QDesktopWidget().screenGeometry()

        # 获取窗口坐标系
        size = self.geometry()  # noqa

        new_left = (screen.width() - size.width()) / 2
        new_top = (screen.height() - size.height()) / 2

        self.move(int(new_left), int(new_top))  # noqa


global_data = {}

alarm_global_data = {}


def remove_alarm_by_api(alarm_url: str) -> str:
    """
    解除设备报警
    :param alarm_url:
    :return:
    """
    alarm_list = list(alarm_global_data.keys())
    log.info(f"将解除的设备报警列表：{alarm_list}")

    if not alarm_list:
        return ""

    error_msg = ""
    for k in alarm_list:
        _alarm_param = alarm_global_data.get(k, {})
        if not _alarm_param:
            log.warning(f"BAD---未获取到解除报警的参数！")
            continue

        log.info(f"解除设备报警中，异常代码：{k}  ...")

        _alarm_param["values"]["removeAlarm"] = "true"
        ret = xrequest.RequestUtil.post_json(alarm_url, _alarm_param)
        if str(ret.get('code')) != "0":
            error_msg = f"mes接口异常，error：{ret.get('message')}"

        del alarm_global_data[k]
        log.info(f"异常：{k} 已解除！")

    return error_msg


class Engine(ErrorMapEngine):
    version = {
        "title": "xinwangda_moluoge release v1.0.0.13",
        "device": "501-C",
        "feature": ["条码校验", "设备状态", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-11-30 18:08  条码校验，上传数据，上传设备状态  
date: 2023-12-04 09:57  增加非标接口调用
date: 2023-12-04 16:29  所有接口URL和serviceName单独配置
date: 2023-12-04 18:11  optimized code
date: 2023-12-12 16:24  去除参数里的空格
date: 2023-12-12 17:13  修改报警的参数
date: 2023-12-21 16:20  修改上传参数
date: 2024-01-08 09:30  优化界面
date: 2024-01-09 10:09  需求变更
date: 2024-01-10 09:20  优化界面
date: 2024-01-17 17:51  删掉多余的参数
date: 2024-01-30 15:00  增加解除设备报警功能
""", }

    form = {
        "device_sn": {
            "ui_name": "设备编码",
            "value": "",
        },
        "username": {
            "ui_name": "工号",
            "value": "",
        },
        "password": {
            "ui_name": "登录密码",
            "value": "",
        },
        "product_code": {
            "ui_name": "产品编码",
            "value": "",
        },
        "process_code": {
            "ui_name": "工序编码",
            "value": "",
        },
        "device_pos": {
            "ui_name": "设备位置",
            "value": "",
        },
        "operator_id": {
            "ui_name": "操作员",
            "value": "",
        },
        "order_no": {
            "ui_name": "制令单",
            "value": "",
        },
    }

    button = {
        "connect_mes": {
            "ui_name": "连接mes"
        },
        "login_mes": {
            "ui_name": "登录mes"
        },
        "upload_device_status": {
            "ui_name": "上传设备状态"
        },
        "remove_alarm_btn": {
            "ui_name": "解除设备报警"
        }
    }

    other_form = {
        "connect_mes_url": {
            "ui_name": "检查服务器状态接口URL",
            "value": "",
        },
        "connect_mes_name": {
            "ui_name": "检查服务器状态serviceName",
            "value": "mes_common_request.serviceStatus",
        },
        "login_mes_url": {
            "ui_name": "登录接口URL",
            "value": "",
        },
        "login_mes_name": {
            "ui_name": "登录serviceName",
            "value": "",
        },
        "get_product_url": {
            "ui_name": "获取产品信息接口URL",
            "value": "",
        },
        "get_product_service_name": {
            "ui_name": "获取产品信息serviceName",
            "value": "trc_unit.reflowPass",
        },
        "check_url": {
            "ui_name": "产品进站检查(CheckIn)接口URL",
            "value": "",
        },
        "check_service_name": {
            "ui_name": "产品进站检查(CheckIn)serviceName",
            "value": "trc_unit.beforeAOIPass",
        },
        "check_out_url": {
            "ui_name": "产品进站检查(CheckOut)接口URL",
            "value": "",
        },
        "check_out_service_name": {
            "ui_name": "产品进站检查(CheckOut)serviceName",
            "value": "trc_unit.beforeAOIPass",
        },
        "device_url": {
            "ui_name": "设备状态接口URL",
            "value": "",
        },
        "device_service_name": {
            "ui_name": "设备状态serviceName",
            "value": "",
        },
        "alarm_url": {
            "ui_name": "设备报警接口URL",
            "value": "",
        },
        "alarm_service_name": {
            "ui_name": "设备报警serviceName",
            "value": "",
        },
        "send_out_board_url": {
            "ui_name": "设备参数及工艺参数接口URL",
            "value": "",
        },
        "send_out_board_service_name": {
            "ui_name": "设备参数及工艺参数serviceName",
            "value": "",
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "value": "/MES/SMT"
        },
    }

    path = {
        "image_path": {
            "ui_name": "图片路径",
            "value": "",
        },
        # "comp_image_path": {
        #     "ui_name": "器件图路径",
        #     "value": "",
        # }
    }

    combo = {
        "is_upload_ftp": {
            "ui_name": "是否上传图片到ftp",
            "item": ["上传", "不上传"],
            "value": "上传",
        }
    }

    def __init__(self):
        self.custom_str_to_chinese()
        self.common_config["sendmes_setting1"] = xenum.SendMesSetting1.SaveInspectNG

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        check_url = other_vo.get_value_by_cons_key("check_url")
        check_service_name = other_vo.get_value_by_cons_key("check_service_name")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        process_code = other_vo.get_value_by_cons_key("process_code")
        username = other_vo.get_value_by_cons_key("username")
        order_no = other_vo.get_value_by_cons_key("order_no")

        if not global_data.get('is_login'):
            return self.x_response("false", f"未登录，请先登录！")

        sn_list = other_vo.list_sn()

        error_msg = ""
        for ix, sn in enumerate(sn_list):
            ix += 1
            body_data = {
                "serviceName": check_service_name,
                "values": {
                    "workplaceID": device_sn,
                    "operation": process_code,
                    "productSn": sn,
                    "operatorID": username,
                    "orderNo": order_no,
                    # "result": "",
                    # "Params": [],
                    # "subBoardID": ix,
                }
            }

            ret = xrequest.RequestUtil.post_json(check_url, body_data)
            if str(ret.get('code')) != "0":
                error_msg = f"mes接口异常，条码校验失败，error：{ret.get('message')}"

        if error_msg:
            return self.x_response('false', error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        image_path = data_vo.get_value_by_cons_key("image_path")

        check_out_url = data_vo.get_value_by_cons_key("check_out_url")
        check_out_service_name = data_vo.get_value_by_cons_key("check_out_service_name")
        device_sn = data_vo.get_value_by_cons_key("device_sn")
        process_code = data_vo.get_value_by_cons_key("process_code")
        operator_id = data_vo.get_value_by_cons_key("operator_id")
        order_no = data_vo.get_value_by_cons_key("order_no")

        is_upload_ftp = data_vo.get_value_by_cons_key("is_upload_ftp")
        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        try:
            ftp_port = int(ftp_port)
        except Exception as err:
            return self.x_response("false", f"FTP Port 必须为数字！err: {err}")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        if not global_data.get('is_login'):
            return self.x_response("false", f"未登录，请先登录！")

        # pcb_sn = pcb_entity.pcb_barcode

        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        ftp_time_file = pcb_entity.get_start_time().strftime(xcons.FMT_DATE1)
        ftp_full_path = f"{ftp_path}/{ftp_time_file}"
        if is_upload_ftp == '上传':
            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()
            ftp_client.cd_or_mkdir(ftp_full_path)
        else:
            ftp_client = None

        error_msg = ""

        t_image = pcb_entity.get_pcb_t_image()
        b_image = pcb_entity.get_pcb_b_image()

        t_report = pcb_entity.get_pcb_t_report_xml()
        # b_report = pcb_entity.get_pcb_b_report_xml()

        t_geometry = xutil.XmlUtil.dict_board_geometry(t_report)

        # if b_report:
        #     b_geometry = xutil.XmlUtil.dict_board_geometry(b_report)
        # else:
        #     b_geometry = {}

        pcb_result = pcb_entity.get_repair_result("PASS", "NG")

        if t_image:
            t_pcb_filename = f"T_{time_file}_{pcb_result}.jpg"
            t_dst_image = f"{image_path}/{t_pcb_filename}"

            xutil.FileUtil.copy_file(t_image, t_dst_image)

            if ftp_client:
                ftp_client.upload_file(t_dst_image, t_pcb_filename)
                t_api_filepath = ftp_full_path
            else:
                t_api_filepath = image_path
        else:
            t_api_filepath = ''
            t_pcb_filename = ''

        if b_image:
            b_pcb_filename = f"B_{time_file}_{pcb_result}.jpg"
            b_dst_image = f"{image_path}/{b_pcb_filename}"
            xutil.FileUtil.copy_file(b_image, b_dst_image)

            if ftp_client:
                ftp_client.upload_file(b_image, b_pcb_filename)
                b_api_filepath = ftp_full_path
            else:
                b_api_filepath = image_path
        else:
            b_api_filepath = ''
            b_pcb_filename = ''

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            width_list = []
            height_list = []
            angle_list = []

            t_geometry_data = t_geometry.get(board_no, {})
            width_list.append(t_geometry_data.get('width', ''))
            height_list.append(t_geometry_data.get('height', ''))
            angle_list.append(t_geometry_data.get('angle', ''))

            # if b_geometry:
            #     b_geometry_data = t_geometry.get(board_no, {})
            #     width_list.append(b_geometry_data.get('width', ''))
            #     height_list.append(b_geometry_data.get('height', ''))
            #     angle_list.append(b_geometry_data.get('angle', ''))

            result = ""
            comp_img_list = []

            if t_api_filepath:
                comp_img_list.append({
                    "filetype": "WHOLE",
                    "fileName": t_pcb_filename,
                    "filePath": t_api_filepath
                })

            if b_api_filepath:
                comp_img_list.append({
                    "filetype": "WHOLE",
                    "fileName": b_pcb_filename,
                    "filePath": b_api_filepath
                })

            error_info_list = []
            for comp_entity in board_entity.yield_comp_entity():

                comp_src_image = comp_entity.image_path
                comp_tag = comp_entity.designator
                comp_type = comp_entity.type
                comp_result = comp_entity.get_final_result("PASS", "PASS", "FAIL")

                if comp_src_image:
                    # dst_filename = f"1_{barcode}_{time_file}_{board_no}_{comp_tag}_{comp_type}_{comp_result}.png"
                    dst_filename = f"{comp_entity.board_side}_{board_no}_{barcode}_{time_file}_{comp_tag}_{comp_result}.png"
                    comp_dst_image = f"{image_path}/{dst_filename}"
                    xutil.FileUtil.copy_file(comp_src_image, comp_dst_image)

                    if ftp_client:
                        ftp_client.upload_file(comp_src_image, dst_filename)
                        api_comp_dst_path = ftp_full_path
                    else:
                        api_comp_dst_path = image_path

                    comp_img_list.append({
                        "filetype": "NGFILE",
                        "fileName": dst_filename,
                        "filePath": api_comp_dst_path
                    })

                if comp_entity.is_repair_ng():
                    result = comp_entity.repair_ng_str

                    error_info_list.append(f"AIS50x-{comp_entity.repair_ng_code}-{result}")

            # 按拼板上传数据
            data_param = {
                "serviceName": check_out_service_name,
                "values": {
                    "workplaceID": device_sn,
                    "operatorID": operator_id,
                    "orderNo": order_no,
                    "operation": process_code,
                    "productSn": barcode,
                    "result": board_entity.get_final_result("Passed", "Passed", "Failed"),
                    "subBoardID": board_no,
                    # "eoperationtime": "",
                    # "maintenancetime": "",
                    # "sparetime": "",
                    "reason": result,
                    # "params": {
                    #     "xValue": "",
                    #     "yValue": "",
                    #     "zValue": ""
                    # },
                    "file": comp_img_list,
                    "testData": [
                        {
                            "unit": "°",  # 单位，固定
                            "testItem": "detectAngle",  # 参数名称，固定
                            "testItemTrans": "检测角度",  # 参数中文名称，固定
                            "testValue": ",".join(angle_list),  # 参数值
                            "testResult": "PASS"  # 参数判断结果，固定
                        },
                        {
                            "unit": "mm",
                            "testItem": "boardLength",
                            "testItemTrans": "板长",
                            "testValue": ",".join(width_list),
                            "testResult": "PASS"
                        },
                        {
                            "unit": "mm",
                            "testItem": "boardWidth",
                            "testItemTrans": "板宽",  # 单板卡的板宽
                            "testValue": ",".join(height_list),
                            "testResult": "PASS"
                        },
                        # {
                        #     "unit": "mm²",
                        #     "testItem": "totalCoatingArea",
                        #     "testItemTrans": "涂敷总面积",
                        #     "testValue": "",
                        #     "testResult": "PASS"
                        # },
                        # {
                        #     "unit": "mm",
                        #     "testItem": "coatingThicknessValue",
                        #     "testItemTrans": "涂覆厚度值",
                        #     "testValue": "",
                        #     "testResult": "PASS"
                        # },
                        {
                            "unit": "",  # 该项目若产品非NG，可不传递或testValue为空
                            "testItem": "detectedDefects",
                            "testItemTrans": "检测不良项/类",
                            "testValue": ";".join(error_info_list),  # 多个不良用英文分号隔开，格式：设备型号-不良代码-中文的不良描述
                            "testResult": "PASS"
                        }
                    ]
                }
            }

            ret = xrequest.RequestUtil.post_json(check_out_url, data_param)
            if str(ret.get('code')) != "0":
                error_msg = f"mes接口异常，上传数据失败，error：{ret.get('message')}"

        if ftp_client:
            ftp_client.close()

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        connect_mes_url = btn_vo.get_value_by_cons_key("connect_mes_url")
        connect_mes_name = btn_vo.get_value_by_cons_key("connect_mes_name")
        device_sn = btn_vo.get_value_by_cons_key("device_sn")
        device_pos = btn_vo.get_value_by_cons_key("device_pos")

        device_url = btn_vo.get_value_by_cons_key("device_url")
        device_service_name = btn_vo.get_value_by_cons_key("device_service_name")
        login_mes_url = btn_vo.get_value_by_cons_key("login_mes_url")
        login_mes_name = btn_vo.get_value_by_cons_key("login_mes_name")
        username = btn_vo.get_value_by_cons_key("username")
        password = btn_vo.get_value_by_cons_key("password")
        order_no = btn_vo.get_value_by_cons_key("order_no")

        btn_key = btn_vo.get_btn_key()

        if btn_key == "connect_mes":
            body_data = {
                "serviceName": connect_mes_name,
                "values": {
                    "workplaceID": device_sn  # 设备编码
                }
            }

            ret = xrequest.RequestUtil.post_json(connect_mes_url, body_data)
            if str(ret.get('code')) != "0":
                return self.x_response('false', f"mes连接异常，error：{ret.get('message')}")

        elif btn_key == "upload_device_status":
            dialog = MyDialog()

            ret = dialog.exec_()

            if ret == 1:
                current_combo = dialog.combo_box.currentText()
                self.log.info(f"手动上传设备状态中，设备状态：{current_combo}")
                # status_desc = other_dao.get_status_desc()
                # old_device_status_code = other_dao.get_old_device_status_code()

                device_param = {
                    "serviceName": device_service_name,
                    "values": {
                        "workplaceID": device_sn,
                        "statusCode": "18",
                        "statusReasonCode": device_map.get(current_combo),
                        "statusDescription": "停机",
                        "statusReasonDescription": current_combo,
                        "position": device_pos,
                        "statusTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                        "statusReasonTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
                    }
                }

                ret = xrequest.RequestUtil.post_json(device_url, device_param)
                if str(ret.get('code')) != "0":
                    return self.x_response('false', f"mes接口异常，error：{ret.get('message')}")

        elif btn_key == "login_mes":
            body_data = {
                "serviceName": login_mes_name,
                "values": {
                    "workplaceID": device_sn,  # 设备编码
                    "operatorID": username,
                    "password": password,
                    "orderNo": order_no,
                }
            }
            ret = xrequest.RequestUtil.post_json(login_mes_url, body_data)
            if str(ret.get('code')) != "0":
                return self.x_response('false', f"mes连接异常，error：{ret.get('message')}")

            global_data['is_login'] = True

        elif btn_key == "remove_alarm_btn":
            alarm_url = btn_vo.get_value_by_cons_key("alarm_url")
            error_msg = remove_alarm_by_api(alarm_url)

            if error_msg:
                return self.x_response("false", error_msg)

        return self.x_response()

    @filter_v3_status_code
    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        device_url = other_vo.get_value_by_cons_key("device_url")
        alarm_url = other_vo.get_value_by_cons_key("alarm_url")
        device_sn = other_vo.get_value_by_cons_key("device_sn")
        device_service_name = other_vo.get_value_by_cons_key("device_service_name")
        alarm_service_name = other_vo.get_value_by_cons_key("alarm_service_name")
        device_pos = other_vo.get_value_by_cons_key("device_pos")

        status_desc_description = other_vo.get_status_desc()
        old_device_status_code = other_vo.get_old_device_status_code()

        if not global_data.get('is_login'):
            return self.x_response("false", f"未登录，请先登录！")

        if old_device_status_code in ["02", "01", "04"]:
            status_desc = "运行"
            status_code = "7"
        elif old_device_status_code == "03":
            status_desc = "停机"
            status_code = "18"
        else:
            status_desc = "故障"
            status_code = "9"

        device_param = {
            "serviceName": device_service_name,
            "values": {
                "workplaceID": device_sn,
                "statusCode": status_code,
                "statusReasonCode": old_device_status_code,
                "statusDescription": status_desc,
                "statusReasonDescription": status_desc_description,
                "position": device_pos,
                "statusTime": xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT),
                "statusReasonTime": ""
            }
        }

        ret = xrequest.RequestUtil.post_json(device_url, device_param)

        error_msg = ""
        if str(ret.get('code')) != "0":
            error_msg = f"mes接口异常，error：{ret.get('message')}"

        if old_device_status_code == "02":
            # 开始检测，如果有异常，则需要移除
            error_msg = remove_alarm_by_api(alarm_url)

        elif old_device_status_code not in ["01", "02", "03", "04"]:
            # 报警
            alarm_time = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)
            alarm_param = {
                "serviceName": alarm_service_name,
                "values": {
                    "workplaceID": device_sn,
                    "removeAlarm": "false",
                    "position": device_pos,
                    "statusTime": alarm_time,
                    "alarmAddress": old_device_status_code,
                    "alarmMsg": status_desc
                }
            }

            # alarm_history_map = alarm_global_data.get(old_device_status_code)
            if old_device_status_code in alarm_global_data:
                self.log.warning(f"发生过该报警【{old_device_status_code}】，并且还解除该报警，本次的报警不上传到mes")
                return self.x_response()

            ret = xrequest.RequestUtil.post_json(alarm_url, alarm_param)
            if str(ret.get('code')) != "0":
                error_msg = f"mes接口异常，error：{ret.get('message')}"

            alarm_global_data[old_device_status_code] = alarm_param
            self.log.info(f"该报警已缓存！--->{old_device_status_code}")

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_project_info_to_mes(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        get_product_url = other_vo.get_value_by_cons_key('get_product_url')
        device_sn = other_vo.get_value_by_cons_key('device_sn')
        product_code = other_vo.get_value_by_cons_key('product_code')
        get_product_service_name = other_vo.get_value_by_cons_key('get_product_service_name')

        if not global_data.get('is_login'):
            return self.x_response("false", f"未登录，请先登录！")

        username = other_vo.get_origin_param("userName")
        pwd = other_vo.get_origin_param("pwd")
        project_name = other_vo.get_origin_param("projectName")

        data_param = {
            "serviceName": get_product_service_name,
            "values": {
                "workplaceID": device_sn,
                "operation": username,
                "Password": pwd,
                "productCode": product_code,
                "Program": project_name,
            }
        }

        ret = xrequest.RequestUtil.post_json(get_product_url, data_param)

        if str(ret.get('code')) != "0":
            return self.x_response("false", f"mes接口异常，发送板式数据失败，error：{ret.get('message')}")

        return self.x_response()

    def send_out_board_info_to_mes(self, other_vo: OtherVo, main_window, other_param: Any = ""):
        send_out_board_url = other_vo.get_value_by_cons_key('send_out_board_url')
        device_sn = other_vo.get_value_by_cons_key('device_sn')
        device_pos = other_vo.get_value_by_cons_key('device_pos')
        send_out_board_service_name = other_vo.get_value_by_cons_key('send_out_board_service_name')

        data_param = {
            "serviceName": send_out_board_service_name,
            "values": {
                "workplaceID": device_sn,
                "statusTime": other_vo.get_origin_param('timestamp'),
                "trackWidth": str(other_vo.get_origin_param('trackWidth')),
                "runningTime": other_vo.get_origin_param('runTime'),
                "position": device_pos,
                "devicePower": "null",
                "deviceAirPressure": "合格",
            }
        }

        ret = xrequest.RequestUtil.post_json(send_out_board_url, data_param)
        if str(ret.get('code')) != "0":
            return self.x_response('false', f"mes接口异常，error：{ret.get('message')}")

        return self.x_response()
