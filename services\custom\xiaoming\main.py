# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : engine.py
# Time       ：2023/7/27 下午3:08
# Author     ：sch
# version    ：python 3.8
# Description：小铭
"""
import os
import xml.etree.cElementTree as XmlTree
from typing import Any

from common import xcons, xutil, xsql, xrequest
from common.xutil import log
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine


class Engine(BaseEngine):
    version = {
        "title": "xiaoming release v1.0.0.10",
        "device": "203、303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-07-27 15:22  init
date: 2023-08-07 16:31  增加补录条码功能
date: 2023-08-15 09:55  修复txt条码不对问题
date: 2023-09-06 18:24  使用补录的第一个条码命名json文档
date: 2024-01-02 17:43  调试版本
date: 2024-01-10 14:43  增加上传数据接口
date: 2024-01-24 09:40  接口上传的数据修改为标准版的json格式
date: 2024-01-24 14:26  上传数据改成测试完发送
date: 2024-01-25 15:10  增加参数device_name
""", }

    path = {
        "data_path": {
            "ui_name": "数据文本",
            "value": ""
        },
        "barcode_data_robot": {
            "ui_name": "条码文本-检测",
            "value": ""
        },

        "barcode_data_repair": {
            "ui_name": "条码文本-维修",
            "value": ""
        }
    }

    form = {
        "order_number": {
            "ui_name": "订单号",
            "value": "1P00044731907230089"
        },
    }
    other_form = {
        "api_url_data": {
            "ui_name": "接口url(上传数据)",
            "value": ""
        },
        "device_name": {
            "ui_name": "机台号",
            "value": "13"
        },
    }

    combo = {
        "is_fill_barcode": {
            "ui_name": "是否补录条码",
            "item": ["是", "否"],
            "value": "是"
        },
        # "face_type": {
        #     "ui_name": "faceType",
        #     "item": ["元件面", "焊点面"],
        #     "value": "元件面"
        # },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        data_path = data_vo.get_value_by_cons_key("data_path")
        barcode_data_robot = data_vo.get_value_by_cons_key("barcode_data_robot")
        barcode_data_repair = data_vo.get_value_by_cons_key("barcode_data_repair")
        order_number = data_vo.get_value_by_cons_key("order_number")
        device_name = data_vo.get_value_by_cons_key("device_name")
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        # face_type = data_dao.get_value_by_cons_key("face_type")

        # face_type_map = {
        #     "元件面": 1,
        #     "焊点面": 2,
        # }
        #
        # face_type_int = face_type_map.get(face_type)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        self.log.info(f"other data: {other_data}")

        inspect_type = other_data.get('inspect_type', 'repair')

        pcb_sn = pcb_entity.pcb_barcode

        board_ids = pcb_entity.list_board_ids()

        board_data = []
        board_data1 = []
        comp_number = 0
        comp_user_ng_number = 0
        comp_robot_ng_number = 0

        board_robot_ng_count = 0
        board_repair_ng_count = 0

        robot_result = []
        repair_result = []

        start_time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        only_one_sn = pcb_entity.pcb_barcode

        fill_barcode_map = {}

        json_barcode = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)
            board_uuid = board_entity.board_uuid
            barcode = board_entity.barcode
            board_no = board_entity.board_no

            robot_result.append(board_entity.get_robot_result("01", "02"))
            r2 = board_entity.get_repair_result("01", "02")
            repair_result.append(r2)

            fill_barcode_map[board_uuid] = {
                "r2": r2,
                "no": board_no,
            }

            if not json_barcode:
                tmp_no = board_no.zfill(2)
                json_barcode = f"{tmp_no}{order_number}{device_name}{start_time_file}{r2}"
                self.log.info(f"json barcode: {json_barcode}")

            if not only_one_sn and barcode:
                only_one_sn = barcode

            if not pcb_sn and barcode:
                pcb_sn = barcode

            if not board_entity.robot_result:
                board_robot_ng_count += 1

            if not board_entity.repair_result:
                board_repair_ng_count += 1

            comp_number += board_entity.comp_total_number
            comp_user_ng_number += board_entity.comp_repair_ng_number
            comp_robot_ng_number += board_entity.comp_robot_ng_number
            comp_data = []
            comp_data1 = []

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                item = {
                    "comp_designator": comp_tag,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                }

                comp_data.append(item)

                item["comp_image"] = comp_entity.image_path
                comp_data1 = item

            board_data.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data,
            })

            board_data1.append({
                "board_sn": barcode,
                "board_no": board_no,
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data1,
            })

        pcb_data = {
            "OrderNum": order_number,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_test_time": str(pcb_entity.get_start_time()),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_user_ng_number": board_repair_ng_count,
            "pcb_board_robot_ng_number": board_robot_ng_count,
            "pcb_comp_number": comp_number,
            "pcb_comp_user_ng_number": comp_user_ng_number,
            "pcb_comp_robot_ng_number": comp_robot_ng_number,
            "board_data": board_data
        }

        if not only_one_sn:
            only_one_sn = start_time_file

        if not json_barcode:
            json_barcode = only_one_sn

        if inspect_type == "repair":
            # 复判后发送
            # 1. 先生成json文档
            json_path = f"{data_path}/{json_barcode}.json"
            xutil.FileUtil.dump_json_to_file(json_path, pcb_data)

            # 2. 生成条码文档
            txt_list = []
            for ix, result in enumerate(repair_result):
                no = board_ids[ix]

                tmp_no = no.zfill(2)
                txt_barcode = f"{tmp_no}{order_number}{device_name}{start_time_file}{result}"
                self.log.info(f"txt barcode: {txt_barcode}")

                txt_list.append(txt_barcode)

            filepath_repair = f"{barcode_data_repair}/{only_one_sn}.txt"
            txt_content = "\n".join(txt_list)
            xutil.FileUtil.write_content_to_file(filepath_repair, txt_content)

            if pcb_entity.get_final_result() == "PASS":
                # 机器发送
                # 2. 生成条码文档
                txt_list = []
                for ix, result in enumerate(robot_result):
                    ix += 1
                    tmp_no = str(ix).zfill(2)
                    txt_list.append(f"{tmp_no}{order_number}{device_name}{start_time_file}{result}")

                filepath_repair = f"{barcode_data_robot}/{only_one_sn}.txt"
                txt_content = "\n".join(txt_list)
                xutil.FileUtil.write_content_to_file(filepath_repair, txt_content)

                pcb_data2 = {
                    "device_name": device_name,
                    "pcb_sn": pcb_sn,
                    "pcb_track_line": pcb_entity.track_index,
                    "pcb_test_time": str(pcb_entity.get_start_time()),
                    "pcb_project_name": pcb_entity.project_name,
                    "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                    "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                    "pcb_final_result": pcb_entity.get_final_result(),
                    "pcb_repair_user": pcb_entity.repair_user,
                    "pcb_board_number": pcb_entity.board_count,
                    "pcb_board_user_ng_number": board_repair_ng_count,
                    "pcb_board_robot_ng_number": board_robot_ng_count,
                    "pcb_comp_number": comp_number,
                    "pcb_comp_user_ng_number": comp_user_ng_number,
                    "pcb_comp_robot_ng_number": comp_robot_ng_number,
                    "pcb_comp_repass_number": comp_robot_ng_number - comp_user_ng_number,
                    "board_data": board_data
                }

                ret = xrequest.RequestUtil.post_json(api_url_data, pcb_data2)
                if str(ret.get('statusCode')) != '200':
                    return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

            is_fill_barcode = data_vo.get_value_by_cons_key("is_fill_barcode")
            if is_fill_barcode == "是":
                log.info("--------------------")
                # 将数据写入数据库，report.xml, man.xml,robot.xml
                # 补录条码
                sql_obj1 = xsql.MySQLService("127.0.0.1", "AIS400")
                sql_obj2 = xsql.MySQLService("127.0.0.1", "makerray")

                review_path = pcb_entity.get_pcb_pcb_t_review_path()
                review_report_xml = pcb_entity.get_pcb_t_report_xml()
                try:
                    self.log.info(f"开始补录条码...")
                    report_barcode = []

                    for uid, item in fill_barcode_map.items():
                        board_no = item.get('no', 'XX')
                        r2 = item.get('r2', "XX")

                        tmp_no = board_no.zfill(2)
                        new_barcode = f"{tmp_no}{order_number}{device_name}{start_time_file}{r2}"

                        report_barcode.append(new_barcode)
                        update_xml_barcode_by_board_no(review_path, board_no, new_barcode, sql_obj1, sql_obj2)

                    # 更新report.xml里的条码
                    update_report_barcode(review_report_xml, report_barcode)

                finally:
                    sql_obj1.close()
                    sql_obj2.close()

                log.info("补录成功")
                log.info("--------------------")

            # log.info(f"开始上传数据到mes....")
            # data_param = {
            #     "orderNum": order_number,
            #     "code": pcb_sn,
            #     "status": pcb_entity.get_repair_result("OK", "NG"),
            #     "machineCode": device_name,
            #     "faceType": face_type_int,
            # }

            # del pcb_data['orderNum']

        else:
            # 机器发送
            # 2. 生成条码文档
            txt_list = []
            for ix, result in enumerate(robot_result):
                ix += 1
                tmp_no = str(ix).zfill(2)
                txt_list.append(f"{tmp_no}{order_number}{device_name}{start_time_file}{result}")

            filepath_repair = f"{barcode_data_robot}/{only_one_sn}.txt"
            txt_content = "\n".join(txt_list)
            xutil.FileUtil.write_content_to_file(filepath_repair, txt_content)

            pcb_data2 = {
                "device_name": device_name,
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_test_time": str(pcb_entity.get_start_time()),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_board_user_ng_number": board_repair_ng_count,
                "pcb_board_robot_ng_number": board_robot_ng_count,
                "pcb_comp_number": comp_number,
                "pcb_comp_user_ng_number": comp_user_ng_number,
                "pcb_comp_robot_ng_number": comp_robot_ng_number,
                "pcb_comp_repass_number": comp_robot_ng_number - comp_user_ng_number,
                "board_data": board_data
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, pcb_data2)
            if str(ret.get('statusCode')) != '200':
                return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('message')}")

        return self.x_response()


def update_xml_barcode_by_board_no(review_path: str, board_no: str, new_barcode: str,
                                   sql_obj1=None,
                                   sql_obj2=None,
                                   ):
    """
    更新report.xml,man.xml,robot.xml的条码
    包含更新数据库

    :param review_path:
    :param board_no:
    :param new_barcode:
    :param sql_obj1: 旧版数据库
    :param sql_obj2: 新版数据库
    :return:
    """
    robot_xml = f"{review_path}/mes/{board_no}/robot.xml"
    man_xml = f"{review_path}/mes/{board_no}/man.xml"

    def _update_barcode_by_xml(xml_path):
        if os.path.exists(xml_path):
            tree = XmlTree.parse(xml_path)
            root = tree.getroot()
            board_uuid = root.get("BoardID")
            log.info(f"old barcode: {root.get('Barcode')}")

            root.set('Barcode', new_barcode)
            tree.write(xml_path)
            log.info(f"{xml_path} new barcode:{new_barcode} xml更新成功！")
        else:
            log.warning(f"xml:{xml_path} 找不到，无需更新！")
            board_uuid = ""

        return board_uuid

    tmp_uuid = _update_barcode_by_xml(robot_xml)
    _update_barcode_by_xml(man_xml)

    if tmp_uuid:
        # 更新数据库
        if sql_obj1:
            sql_obj1.update_barcode_by_board_uuid(tmp_uuid, new_barcode)

        if sql_obj2:
            sql_obj2.update_barcode_by_board_uuid_v2(tmp_uuid, new_barcode)


def update_report_barcode(report_xml: str, barcode_list: list):
    """
    更新report.xml里的barcode
    :return:
    """
    tree = XmlTree.parse(report_xml)
    root = tree.getroot()

    boards = root.find('boards')

    if len(boards) != len(barcode_list):
        log.warning(f"report len: {len(boards)}  input len:{barcode_list}  条码数量不匹配不符！")

    for ix, item in enumerate(boards):
        b = item.find('barcode')
        b.text = barcode_list[ix]

    tree.write(report_xml)
    log.info(f"report: {report_xml} updated successfully.")


if __name__ == '__main__':
    # 1.
    # r = "/home/<USER>/aoi/run/results/333.001/20230322/T_20230322090120793_1_NG"
    # update_xml_barcode_by_board_no(r, "1", "barcode1")
    # update_xml_barcode_by_board_no(r, "2", "barcode2")

    # 2.
    # x2 = "/home/<USER>/aoi/run/results/333.001/20230805/T_20230805093037869_1_NG/report.xml"
    # update_report_barcode(x2, ["barcode1", "barcode2"])
    pass
