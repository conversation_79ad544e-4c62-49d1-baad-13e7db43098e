import base64
import os
from datetime import datetime

from flask import Flask, request, jsonify

app = Flask(__name__)

# 存储接收到的数据
UPLOAD_FOLDER = 'received_data'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)


@app.route('/iot/checkSn', methods=['POST'])
def check_sn():
    """模拟条码接口（同时处理获取条码和条码校验）"""
    data = request.json
    print("\n=== 接收到的条码请求 ===")
    print(f"完整URL: {request.url}")
    print(f"请求方法: {request.method}")
    print(f"请求头: {dict(request.headers)}")
    print(f"请求参数: {data}")

    # 根据subType区分不同功能
    sub_type = data.get('subType', '')

    if sub_type == 'GETPCB':
        print(">>> 这是获取条码请求")
        # 模拟获取条码响应
        response = {
            "code": "0",
            "message": "success",
            "小板条码": {
                "rows": [
                    {"lb_code": "SB001"},
                    {"lb_code": "SB002"},
                    {"lb_code": "SB003"}
                ]
            }
        }
    else:
        print(">>> 这是条码校验请求")
        # 模拟条码校验响应
        response = {
            "code": "0",
            "message": f"条码 {data.get('barCode', '')} 校验成功"
        }

    print(f"<<< 返回响应: {response}")
    return jsonify(response)


@app.route('/iot/uploadData', methods=['POST'])
def upload_data():
    """模拟MES数据上传接口"""
    data = request.json
    print("\n=== 接收到的MES数据上传 ===")
    print(f"完整URL: {request.url}")
    print(f"请求方法: {request.method}")
    print(f"请求头: {dict(request.headers)}")
    print("请求数据内容:")

    # 打印结构化数据
    print(f"ID: {data.get('id')}")
    print(f"组织编码: {data.get('orgCode')}")
    print(f"设备编码: {data.get('iotDevCode')}")
    print(f"时间: {data.get('time')}")

    body = data.get('body', {})
    print("\nBody内容:")
    print(f"子类型: {body.get('subType')}")
    print(f"配方名称: {body.get('recipeName')}")
    print(f"标签打印类型: {body.get('crtPrtType')}")
    print(f"标签组过站: {body.get('isLbwpByGrp')}")

    # 处理PCB检查数据
    print("\nPCB检查数据:")
    for pcb in body.get('pcbChecks', []):
        print(f"条码: {pcb.get('barCode')}, 结果: {pcb.get('checkResult')}")
        print("不良项:", pcb.get('badItems', []))

    # 处理图片附件
    if 'atts' in data and data['atts']:
        print("\n=== 接收到的图片附件 ===")
        for att in data['atts']:
            try:
                # 解码并保存图片
                img_data = base64.b64decode(att['data'])
                filename = os.path.join(UPLOAD_FOLDER, att['name'])
                with open(filename, 'wb') as f:
                    f.write(img_data)
                print(f"成功保存图片: {att['name']} (大小: {len(img_data)} bytes)")
            except Exception as e:
                print(f"处理图片 {att['name']} 失败: {str(e)}")

    # 模拟响应
    response = {
        "code": "0",
        "message": "数据上传成功",
        "receivedTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    print(f"<<< 返回响应: {response}")
    return jsonify(response)


@app.route('/test', methods=['GET'])
def test_connection():
    """测试连接"""
    return jsonify({
        "status": "success",
        "message": "测试端运行正常",
        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "接口说明": {
            "获取条码": "POST /iot/checkSn (subType=GETPCB)",
            "条码校验": "POST /iot/checkSn",
            "上传数据": "POST /iot/uploadData"
        }
    })


if __name__ == '__main__':
    print("MES模拟测试端启动")
    print(f"访问 http://127.0.0.1:5000/test 查看接口说明")
    print(f"数据将保存在 {UPLOAD_FOLDER} 目录下")
    app.run(host='0.0.0.0', port=5000, debug=True)
