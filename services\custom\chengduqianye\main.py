# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/07/15 15:38
# Author     ：chencb
# version    ：python 3.8
# Description：成都千页 https://jira.cvte.com/browse/ATAOI_2019-40257
"""
import json
from typing import Any
from common import xrequest, xutil
from services.custom.chengduqianye.task_selection_dialog import TaskSelectionDialog
from vo.mes_vo import DataVo, ButtonVo, ComboVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "customer": ["成都千页", "chengduqianye"],
        "version": "release v1.0.0.1",
        "device": " AIS50X, AIS40X, AIS43X, AIS63X",
        "feature": ["绑定账号，获取工站，获取任务，创建报工数据"],
        "author": "chenchongbing",
        "release": """
date: 2025-07-18 15:07  jira->40257: 绑定账号，获取工站，获取任务，创建报工数据
""", }

    combo = {
        "work_station": {
            "item": [],
            "value": "",
            "ui_name": "工站"
        },
    }

    form = {
        "task_code": {
            "ui_name": "任务号",
            "value": "",
            "is_read_only": True
        },
        "user": {
            "ui_name": "操作人员",
            "value": "",
        },
        "api_url": {
            "ui_name": "接口地址",
            "value": "",
        },
    }

    button = {
        "task_select": {
            "ui_name": "任务选择"
        },
    }

    def __init__(self):
        self.user = None
        self.selected_task = xutil.CacheUtil.get("selected_task")

    def _bind_user_account(self, data_vo: DataVo):
        api_url = data_vo.get_value_by_cons_key("api_url")
        user = data_vo.get_value_by_cons_key("user")

        if not user:
            return self.x_response("false", f"请正确填写操作人员！")

        # 此用户已经登录过，返回成功
        if self.user and self.user.get("name") == user and self.user.get("id"):
            return self.x_response()

        login_url = f'{api_url}/hra/v1/staff_info/query'
        param = {
            "paging": {
                "size": 10,
                "current": 1
            },
            "where": {
                "name": {
                    "_type": "like",
                    "_value": user
                }
            },
        }
        try:
            ret = xrequest.RequestUtil.post_json(login_url, param)
            data = ret.get("result", {}).get("data", [])
            if not ret.get("success"):
                return self.x_response("false", f"绑定账号【{user}】失败，error：{ret.get('message')}")
            else:
                if not data:
                    return self.x_response("false", f"绑定账号失败，操作人员【{user}】不存在！")
                else:
                    user_id = data[0].get("id", '')
                    if not user_id:
                        return self.x_response("false", f"绑定账号失败，操作人员【{user}】不存在！")
                    else:
                        self.user = {
                            "name": user,
                            "id": user_id
                        }
                        return self.x_response()
        except Exception as e:
            return self.x_response("false", f"本地网络异常，绑定账号失败，error:{e}")

    def _get_work_station_list(self, api_url):
        work_station_url = f'{api_url}/mes/v1/workstation/query'
        param = {
            "paging": {
                "size": 9999,
                "current": 1
            }
        }

        try:
            ret = xrequest.RequestUtil.post_json(work_station_url, param)
            data = ret.get("result", {}).get("data", [])
            if not ret.get("success"):
                return self.x_response("false", f"从MES获取工站列表失败，error：{ret.get('message')}")
            else:
                if not data:
                    return self.x_response("false", f"从MES获取工站列表为空，请核实！")
                else:
                    station_list = []
                    for station in data:
                        station_name = station.get("name")
                        if station_name:
                            station_list.append(station_name)
                    return self.x_response("true", json.dumps({"new_items": station_list}))
        except Exception as e:
            return self.x_response("false", f"本地网络异常，获取工站列表失败，error:{e}")

    def _get_task_list(self, api_url, work_station):
        if not work_station:
            return self.x_response("false", "请先选择工站！")

        work_station_url = f'{api_url}/api/product_order_task/query'
        param = {
            "paging": {
                "size": 9999,
                "current": 1
            },
            "where": {
                "production_status": [
                    "待开始",
                    "生产中"
                ],
                "work_station_name": work_station
            }
        }

        try:
            ret = xrequest.RequestUtil.post_json(work_station_url, param)
            data = ret.get("result", {}).get("data", [])
            if not ret.get("success"):
                return self.x_response("false", f"从MES获取任务列表失败，error：{ret.get('message')}")
            else:
                if not data:
                    return self.x_response("false", f"从MES获取任务列表为空，请核实！")
                else:
                    task_list = []
                    for task in data:
                        base = task.get("base", {})
                        task_list.append({
                            "work_station_name": work_station,  # 工站名
                            "process_name": task.get("process_name", ""),  # 工序名
                            "task_code": task.get("task_code", ""),  # 任务号
                            "material_code": base.get("material_code", ""),  # 产品料号
                            "specification": base.get("specification", ""),  # 规格型号
                            "material_name": base.get("material_name", ""),  # 产品名称
                            "suggest_number": task.get("suggest_number", ""),  # 数量
                            "work_station_id": task.get("workstation_id", ""),  # 工站id
                            "product_order_id": base.get("id", ""),  # 订单id
                            "task_id": task.get("id", ""),  # 任务id
                        })
                    return self.x_response("true", json.dumps(task_list))
        except Exception as e:
            return self.x_response("false", f"本地网络异常，获取任务列表失败，error:{e}")

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        if btn_vo.get_btn_key() == "task_select":
            api_url = btn_vo.get_value_by_cons_key("api_url", not_null=True)
            work_station = btn_vo.get_value_by_cons_key("work_station")
            ret = self._get_task_list(api_url, work_station)
            if not ret.get("result"):
                return ret

            task_list = json.loads(ret.get("string"))
            self.log.info(f"获取到任务列表信息：{task_list}")

            task_dialog = TaskSelectionDialog()
            task_dialog.set_data(task_list)
            # 显示对话框并等待结果
            if task_dialog.exec_():
                # 获取选中结果并显示
                selected_task_index = task_dialog.get_selected_task()
                if selected_task_index is None:
                    return self.x_response("false", "未正常选择任务！")
                else:
                    self.selected_task = task_list[selected_task_index]
                    other_param.config_data["form"]["task_code"]["value"] = self.selected_task.get("task_code", "")
                    other_param.save_config_data_to_file()
                    xutil.CacheUtil.set("selected_task", self.selected_task)
                    other_param.reload_param_key_to_ui()
                    return self.x_response()
            else:
                return self.x_response("false", "未正常选择任务！")

    def combo_index_changed(self, combo_vo: ComboVo, main_window: Any):
        combo_key = combo_vo.get_combo_key()
        select_value = combo_vo.get_combo_value()
        if combo_key == "work_station":
            main_window.config_data['combo'][combo_key]['value'] = select_value
            main_window.save_config_data_to_file()

    def combo_mouse_press(self, combo_vo: ComboVo, other_param: Any):
        combo_key = combo_vo.get_combo_key()
        if combo_key == 'work_station':
            api_url = combo_vo.get_value_by_cons_key("api_url", not_null=True)
            ret = self._get_work_station_list(api_url)
            return ret

    def _send_data_to_mes(self, data_vo: DataVo):
        api_url = data_vo.get_value_by_cons_key("api_url")
        if not api_url:
            return self.x_response("false", "请正确填写接口地址！")

        # 步骤1：绑定账号，绑定失败直接返回
        ret = self._bind_user_account(data_vo)
        if not ret.get("result"):
            return ret

        # 步骤2：确认已选择工站
        work_station = data_vo.get_value_by_cons_key("work_station")
        if not work_station:
            return self.x_response("false", "请选选择工站！")

        # 步骤3：确认已选择任务
        if not self.selected_task:
            return self.x_response("false", "请选选择任务！")

        if self.selected_task['work_station_name'] != work_station:
            return self.x_response("false", "工站已更改，请重新选择任务！")

        # 步骤4：创建报工数据
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        err_list = []
        try:
            for board_entity in pcb_entity.yield_board_entity():
                self.log.info(board_entity)
                ng_comp_list = []
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_robot_ng():
                        ng_comp_list.append(f'{comp_entity.designator}:{comp_entity.robot_ng_str}')

                param = {
                    "data": {
                        "SN": board_entity.barcode,
                        "workstationId": self.selected_task["work_station_id"],
                        "productOrderId": self.selected_task["product_order_id"],
                        "taskId": self.selected_task["task_id"],
                        "qualified_quantity": board_entity.get_robot_result("1", "0"),
                        "unqualified_quantity": board_entity.get_robot_result("0", "1"),
                        "bad_causes": ";".join(ng_comp_list),
                        "parameter": "",
                        "staff": self.user["id"]
                    }
                }
                send_data_url = f"{api_url}/api/scanReport/open"
                ret = xrequest.RequestUtil.post_json(send_data_url, param)
                if not ret.get("success"):
                    err_list.append(
                        f"拼板【{board_entity.board_no},{board_entity.barcode}】发送报工数据失败，error：{ret.get('message')}")
        except Exception as e:
            err_list.append(f"本地网络异常，报工数据失败，error:{e}")

        if err_list:
            return self.x_response("false", "\n".join(err_list))
        else:
            return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        ret = self._send_data_to_mes(data_vo)
        if not ret.get("result"):
            # 返回错误需停机报警
            xrequest.send_device_start_or_stop("0", '1', ret.get("string"))

        return ret
