# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/2 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：深圳欣旺达    速博达供应商，设备在4楼 机型：590
"""
import json
from datetime import datetime
from typing import Any

from common import xrequest, xutil, xcons
from common.xglobal import global_data
from common.xutil import x_response, log
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo, ButtonVo, ComboVo

from services.custom.shenzhenxinwangda_sbd_4l_590.xwd_module import upload_image_to_s3


def refresh_token(api_url: str, user_id: str, password: str, device_id: str, timeout=5):
    """
    刷新token
    :param api_url:
    :param user_id:
    :param password:
    :param device_id:
    :param timeout:
    :return:
    """
    login_param = {
        "userId": user_id,
        "password": password,
        "deviceId": device_id
    }
    ret = xrequest.RequestUtil.post_json(api_url, login_param, timeout=timeout)
    if str(ret.get("code")) != "200":
        return x_response("false", f"MES接口响应异常，登录失败，error：{ret.get('msg')}")

    global_data["is_login"] = True


check_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTestCommon xmlns="WWW.SUNWODA.COM">
            <M_SN>{sn}</M_SN>
            <M_MACHINCENO>{machine_no}</M_MACHINCENO>
            <M_EMP>{emp}</M_EMP>
            <M_MO>{mo}</M_MO>
        </GroupTestCommon>
    </soap:Body>
</soap:Envelope>"""

data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <WIPTEST xmlns="WWW.SUNWODA.COM">
            <M_SN>{M_SN}</M_SN>
            <M_RESULT>{M_RESULT}</M_RESULT>
            <M_USERNO>{M_USERNO}</M_USERNO>
            <M_MACHINENO>{M_MACHINENO}</M_MACHINENO>
            <M_ERROR>{M_ERROR}</M_ERROR>
            <M_ITEMVALUE>{M_ITEMVALUE}</M_ITEMVALUE>
        </WIPTEST>
    </soap:Body>
</soap:Envelope>"""


def x_request_device_status(
        api_url_status: str,
        status_code_v3: str,
        req_cycle: int,
        device_id: str,
):
    data_list = []
    warn_list = []
    if status_code_v3 in ["1003", "1005", "3001"]:
        state = "0"  # 待机

        data_list.append({
            "extendCode": status_code_v3,
            "extendValue": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    elif status_code_v3 in ["1001", "1002", "1004"]:
        state = "1"  # 运行

        data_list.append({
            "extendCode": status_code_v3,
            "extendValue": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    elif status_code_v3 in ["2001", "2002", "3002", "3003", "3004", "3005", "3006", "3007",
                            "4001", "4002", "4003", "5001", "5002"]:
        state = "2"  # 报警

        warn_list.append({
            "warnCode": status_code_v3,
            "warnMsg": xcons.DEVICE_STATUS_V3.get(status_code_v3, {}).get("name", ""),
        })
    else:
        log.warning(f"未知的设备状态，本次不上传！")
        state = "99"

    send_count = global_data.get("send_status_count", 0)
    send_count += 1

    if state != "99":
        # 上传设备状态
        d1 = datetime.now()

        time_file = d1.strftime(xcons.FMT_TIME_FILE)
        f2 = d1.strftime(xcons.FMT_TIME_DEFAULT)
        device_param = {
            "taskId": f"DEV_{time_file}_{str(send_count).zfill(4)}",
            "reqTime": f2,
            "reqCycle ": req_cycle * 1000,
            "deviceCode": device_id,
            "statusCode": state,
            "dataList": data_list,
            "warnList": warn_list,
        }

        xrequest.RequestUtil.post_json(api_url_status, device_param)
        global_data["last_device_code_v3"] = status_code_v3


class Engine(ErrorMapEngine):
    version = {
        "title": "shenzhenxinwangda_sbd_4l_590 release v1.0.0.2",
        "device": "50x",
        "feature": ["从Mes获取条码", "上传数据", "设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2025-01-06 16:04  从Mes获取条码，上传数据，登录，获取界面信息，设备状态
date: 2025-03-17 15:19  不良列表改成按照器件点位传递
""", }

    other_combo = {
        "device_cron": {
            "ui_name": "定时上传设备状态",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "device_sequence": {
            "ui_name": "定时上传频率(s)",
            "item": ["30", "60", "180", "360", "600", "1200", "2400", "3000"],
            "value": "180",
        },
    }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(登录)",
            "value": "http://127.0.0.1:8081/common/Login",
        },
        "api_url_factory": {
            "ui_name": "接口URL(获取工厂)",
            "value": "http://127.0.0.1:8081/common/FactoryInfoDownLoadV1",
        },
        "api_url_workstation": {
            "ui_name": "接口URL(获取车间)",
            "value": "http://127.0.0.1:8081/common/WorkStationDownLoadV1",
        },
        "api_url_line": {
            "ui_name": "接口URL(获取线体)",
            "value": "http://127.0.0.1:8081/common/LineDownLoadV1",
        },
        "api_url_station": {
            "ui_name": "接口URL(获取工站)",
            "value": "http://127.0.0.1:8081/common/StationDownloadV1",
        },
        "api_url_mo": {
            "ui_name": "接口URL(获取工单)",
            "value": "http://127.0.0.1:8081/common/MoInfoDownLoad",
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取小板SN条码)",
            "value": "http://127.0.0.1:8081/commonApi/GetSmallPanelSn.ashx",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据/过站)",
            "value": "http://127.0.0.1:8081/commonApi/TackProduct.ashx",
        },
        "api_url_status": {
            "ui_name": "接口URL(上传设备状态)",
            "value": "http://127.0.0.1:8081/common/deviceServices",
        },

        "aws_endpoint_url": {
            "ui_name": "AWS Service Url",
            "value": "http://zzzxhcp.hzhcp.sunwoda.com",
        },
        "aws_access_key_id": {
            "ui_name": "AWS Access Key ID",
            "value": "enp6eGhjcA==",
        },
        "aws_secret_access_key": {
            "ui_name": "AWS Secret Access Key",
            "value": "24785287ae393901fe8c08477dbf7450",
        },
        "bucket_name": {
            "ui_name": "AWS BucketName",
            "value": "ZZZX-BL-5D1L-BL-SMT-01-YS-DEK-01",
        },
    }

    form = {
        "device_id": {
            "ui_name": "设备Id/设备编号",
            "value": "",
        },
        "user_id": {
            "ui_name": "工号",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "value": "",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登录",
        },
    }

    combo = {
        "is_upload_img": {
            "ui_name": "是否上传图片",
            "item": ["Yes", "No"],
            "value": "Yes",
        },
        "factory_list": {
            "ui_name": "工厂",
            "item": [],
            "value": "",
        },
        "workstation_list": {
            "ui_name": "车间",
            "item": [],
            "value": "",
        },
        "line_list": {
            "ui_name": "线体",
            "item": [],
            "value": "",
        },
        "station_list": {
            "ui_name": "工站",
            "item": [],
            "value": "",
        },
        "mo_list": {
            "ui_name": "工单",
            "item": [],
            "value": "",
        },
    }

    def init_main_window(self, main_window, other_dao: OtherVo):
        api_url_login = other_dao.get_value_by_cons_key("api_url_login")
        device_id = other_dao.get_value_by_cons_key("device_id")
        user_id = other_dao.get_value_by_cons_key("user_id")
        password = other_dao.get_value_by_cons_key("password")

        device_cron = other_dao.get_value_by_cons_key("device_cron")
        device_sequence = other_dao.get_value_by_cons_key("device_sequence", to_int=True)

        main_window.set_cron_setting(device_cron == "Yes", device_sequence)

        try:
            refresh_token(api_url_login, user_id, password, device_id, timeout=1)
        except Exception as err:
            self.log.warning(f"自动登录失败，error：{err}")

    def check_sn(self, other_dao: OtherVo, other_param: Any) -> dict:

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        pcb_sn = other_vo.get_pcb_sn()

        get_param = {
            "pcbSN": pcb_sn
        }

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, get_param)

        if str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常，error：{ret.get('msg')}")

        small_panel_sn_list = ret.get("smallPanelSNList", [])
        if not small_panel_sn_list:
            return self.x_response("false", f"mes接口异常，未获取到条码！")

        ret_sn_list = [i.get('smallPanelSN', '') for i in small_panel_sn_list]

        return self.x_response('true', ','.join(ret_sn_list))

    def send_data_to_mes(self, data_dao: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_dao.get_value_by_cons_key("api_url_data")
        user_id = data_dao.get_value_by_cons_key("user_id")
        device_id = data_dao.get_value_by_cons_key("device_id")
        mo_list = data_dao.get_value_by_cons_key("mo_list")
        aws_access_key_id = data_dao.get_value_by_cons_key("aws_access_key_id")
        aws_secret_access_key = data_dao.get_value_by_cons_key("aws_secret_access_key")
        is_upload_img = data_dao.get_value_by_cons_key("is_upload_img")

        station_list = data_dao.get_value_by_cons_key("station_list")

        aws_endpoint_url = data_dao.get_value_by_cons_key("aws_endpoint_url")
        bucket_name_ui = data_dao.get_value_by_cons_key("bucket_name")

        cache_data = xutil.CacheUtil.get_cache_data()

        station_map = cache_data.get("station_map", {})

        station_select = station_map.get(station_list, "")

        pcb_entity = data_dao.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        err_msg_list = []

        date_tmp = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date1 = date_tmp[:8]
        date2 = date_tmp[8:]

        board_panel_url_list = []

        if is_upload_img == "Yes":
            # 上传整板图
            upload_img_list = pcb_entity.list_all_pcb_image()
            self.log.info(f"开始上传整板图，上传数量：{len(upload_img_list)}")

            pcb_result = pcb_entity.get_repair_result("OK", "NG")

            only_one_ng_code = ""

            for board_entity in pcb_entity.yield_board_entity():
                for comp_entity in board_entity.yield_comp_entity():
                    if comp_entity.is_repair_ng():
                        if not only_one_ng_code:
                            only_one_ng_code = f"_{comp_entity.repair_ng_code}"

            for pcb_src_filename in upload_img_list:
                if "/T_" in pcb_src_filename:
                    board_side = "TOP"
                else:
                    board_side = "BOT"

                dst_filename = f"{date1}/{pcb_sn}_{date2}_{board_side}_{pcb_result}{only_one_ng_code}.jpg"

                try:
                    ret_url = upload_image_to_s3(
                        aws_endpoint_url,
                        aws_access_key_id,
                        aws_secret_access_key,
                        pcb_src_filename,
                        bucket_name_ui,
                        dst_filename,
                    )

                    if ret_url:
                        board_panel_url_list.append({
                            "url": ret_url
                        })

                except Exception as err:
                    err_msg_list.append(f"上传整板图失败，boardSide:{board_side}！err: {err}")

        board_data_list = []

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            comp_ng_img_list = []
            upload_data_item = []

            def add_alg_data_to_list(alg_name_, alg_val_):
                upload_data_item.append({
                    "testCode": comp_entity.designator,
                    "tetstItem": alg_name_,
                    "testValue": alg_val_,
                    "badInfoList": [{
                        "badcode": comp_entity.repair_ng_code,
                        "badDes": f"{comp_entity.repair_ng_str}({alg_val_})",
                        "badPostion": f"{comp_entity.designator}",
                    }],
                })

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_src_img = comp_entity.image_path
                    comp_tag = comp_entity.designator

                    if comp_src_img and is_upload_img == "Yes":
                        dst_filename = f"{date1}/{barcode}_{date2}_{board_no}_{comp_tag}_NG_{comp_entity.repair_ng_code}.png"

                        try:
                            ret_url = upload_image_to_s3(
                                aws_endpoint_url,
                                aws_access_key_id,
                                aws_secret_access_key,
                                comp_src_img,
                                bucket_name_ui,
                                dst_filename
                            )

                            if ret_url:
                                comp_ng_img_list.append({
                                    "url": ret_url
                                })

                        except Exception as err:
                            err_msg_list.append(f"上传器件图失败，位号：{comp_tag} err: {err}")

                    alg_name = ""
                    alg_val = ""
                    for alg_entity in comp_entity.yield_alg_entity():
                        if alg_entity.result != "0":
                            alg_name = alg_entity.test_name
                            alg_val = alg_entity.test_val

                    add_alg_data_to_list(alg_name, alg_val)

            board_data_list.append({
                "smallPanelId": board_no,
                "smallPanelTestResult": board_entity.get_repair_result("pass", "ng"),
                "smallPanelUrlList": comp_ng_img_list,
                "smallPanelSN": barcode,
                "processFace": pcb_entity.board_side,
                "extend1": "",
                "extend2": "",
                "extend3": "",
                "extend4": "",
                "testItemList": upload_data_item,
            })

        data_param = {
            "deviceId": device_id,
            "stationId": station_select,
            "userId": user_id,
            "moNumber": mo_list,
            "pcbSN": pcb_sn,
            "carrierSN": pcb_sn,
            "Result": pcb_entity.get_repair_result("PASS", "NG"),
            "bigPanelUrlList": board_panel_url_list,
            "dataList": board_data_list,
        }

        try:
            ret = xrequest.RequestUtil.post_json(api_url_data, data_param)

            if str(ret.get("code")) != "200":
                err_msg_list.append(f"mes接口异常，上传过站数据失败，error：{ret.get('msg')}")
        except Exception as err:
            err_msg = f"mes网络异常，error：{err}"
            return self.x_response("false", err_msg)

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"MES响应异常，error：{err_str}")

        return self.x_response()

    def custom_button_clicked(self, btn_dao: ButtonVo, other_param: Any):
        api_url_login = btn_dao.get_value_by_cons_key("api_url_login")
        device_id = btn_dao.get_value_by_cons_key("device_id")
        user_id = btn_dao.get_value_by_cons_key("user_id")
        password = btn_dao.get_value_by_cons_key("password")

        if btn_dao.get_btn_key() == 'login_btn':
            if x_res := refresh_token(api_url_login, user_id, password, device_id):
                return x_res

        return self.x_response()

    def combo_mouse_press(self, combo_dao: ComboVo, main_window: Any):
        combo_key = combo_dao.get_combo_key()

        api_url_factory = combo_dao.get_value_by_cons_key("api_url_factory")
        api_url_workstation = combo_dao.get_value_by_cons_key("api_url_workstation")
        api_url_line = combo_dao.get_value_by_cons_key("api_url_line")
        api_url_station = combo_dao.get_value_by_cons_key("api_url_station")
        api_url_mo = combo_dao.get_value_by_cons_key("api_url_mo")
        device_id = combo_dao.get_value_by_cons_key("device_id")
        user_id = combo_dao.get_value_by_cons_key("user_id")

        if combo_key == "factory_list":

            if not global_data.get("is_login"):
                return self.x_response("false", f"未登录，请先登录！")

            get_factory_param = {
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_factory, get_factory_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_factory_li = ret.get("factoryList", [])

            if not ret_factory_li:
                return self.x_response("false", f"未获取到工厂列表！")

            factory_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_factory_li}

            xutil.CacheUtil.set("factory_map", factory_map)

            return self.x_response("true", json.dumps({
                "new_items": list(factory_map.keys())
            }))

        elif combo_key == "workstation_list":
            factory_select = getattr(main_window, f"combo_factory_list").currentText()
            factory_sn = xutil.CacheUtil.get("factory_map", {}).get(factory_select)

            if not factory_sn:
                return self.x_response("false", "未选中工厂，请先选中工厂再获取车间！")

            get_workstation_param = {
                "factorySN": factory_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_workstation, get_workstation_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_area_list = ret.get("areaList", [])

            if not ret_area_list:
                return self.x_response("false", f"未获取到车间列表！")

            workstation_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_area_list}
            xutil.CacheUtil.set("workstation_map", workstation_map)

            return self.x_response("true", json.dumps({
                "new_items": list(workstation_map.keys())
            }))

        elif combo_key == "line_list":
            workstation_select = getattr(main_window, f"combo_workstation_list").currentText()
            workstation_sn = xutil.CacheUtil.get("workstation_map", {}).get(workstation_select)

            if not workstation_sn:
                return self.x_response("false", "未选中车间，请先选中车间再获取线体！")

            get_workstation_param = {
                "areaSN": workstation_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_line, get_workstation_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_line_list = ret.get("lineList", [])

            if not ret_line_list:
                return self.x_response("false", f"未获取到线体列表！")

            line_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_line_list}

            xutil.CacheUtil.set("line_map", line_map)

            return self.x_response("true", json.dumps({
                "new_items": list(line_map.keys())
            }))

        elif combo_key == "station_list":
            line_select = getattr(main_window, f"combo_line_list").currentText()
            line_sn = xutil.CacheUtil.get("line_map", {}).get(line_select)

            if not line_sn:
                return self.x_response("false", "未选中线体，请先选中线体再获取工站！")

            get_station_param = {
                "lineSN": line_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_station, get_station_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_station_list = ret.get("stationList", [])

            if not ret_station_list:
                return self.x_response("false", f"未获取到工站列表！")

            station_map = {i.get("baseModelName"): i.get("baseModelCode") for i in ret_station_list}

            xutil.CacheUtil.set("station_map", station_map)

            return self.x_response("true", json.dumps({
                "new_items": list(station_map.keys())
            }))

        elif combo_key == "mo_list":
            station_select = getattr(main_window, f"combo_station_list").currentText()
            station_sn = xutil.CacheUtil.get("station_map", {}).get(station_select)

            if not station_sn:
                return self.x_response("false", "未选中工站，请先选中工站再获取工单！")

            get_mo_param = {
                "stationId": station_sn,
                "userId": user_id,
                "deviceId": device_id
            }
            ret = xrequest.RequestUtil.post_json(api_url_mo, get_mo_param)
            if str(ret.get("code")) != "200":
                return self.x_response("false", f"MES响应异常，error：{ret.get('msg')}")

            ret_mo_list = ret.get("moList", [])

            if not ret_mo_list:
                return self.x_response("false", f"未获取到工单列表！")

            mo_map = {i.get("moNumber"): i for i in ret_mo_list}

            xutil.CacheUtil.set("mo_map", mo_map)

            return self.x_response("true", json.dumps({
                "new_items": list(mo_map.keys())
            }))

    def send_device_status_to_mes(self, other_dao: OtherVo, main_window: Any) -> dict:
        api_url_status = other_dao.get_value_by_cons_key("api_url_status")
        device_sequence = other_dao.get_value_by_cons_key("device_sequence", to_int=True)
        device_id = other_dao.get_value_by_cons_key("device_id")

        status_code_v3 = other_dao.get_status_code_v3()
        x_request_device_status(api_url_status, status_code_v3, device_sequence, device_id)

        return self.x_response()

    def custom_cron_function(self, other_dao: OtherVo, main_window, other_param: Any):
        api_url_status = other_dao.get_value_by_cons_key("api_url_status")
        device_sequence = other_dao.get_value_by_cons_key("device_sequence", to_int=True)
        device_id = other_dao.get_value_by_cons_key("device_id")

        status_code_v3 = global_data.get("last_device_code_v3")
        x_request_device_status(api_url_status, status_code_v3, device_sequence, device_id)


if __name__ == '__main__':
    ret_str1 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTestCommonResponse xmlns="WWW.SUNWODA.COM">
            <GroupTestCommonResult>string</GroupTestCommonResult>
        </GroupTestCommonResponse>
    </soap:Body>
</soap:Envelope>"""

    root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1)
    print(root1[0][0][0].text)
