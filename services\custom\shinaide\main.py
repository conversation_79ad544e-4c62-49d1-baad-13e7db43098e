# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/1 下午3:50
# Author     ：sch
# version    ：python 3.8
# Description：施耐德
"""
import json
from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "shinaide release v1.0.0.2",
        "device": "203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-04 09:38  保存txt文档，图片
date: 2024-03-28 10:13  txt文档格式改为：拼板条码_yyyymmddhhss(时间).txt
""", }

    path = {
        "txt_path": {
            "ui_name": "文档路径",
            "value": "",
        },
        "img_path": {
            "ui_name": "NG器件图路径",
            "value": "",
        }
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        txt_path = data_vo.get_value_by_cons_key("txt_path", not_null=True)
        img_path = data_vo.get_value_by_cons_key("img_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode

        t1 = pcb_entity.get_start_time()
        test_time = t1.strftime(xcons.FMT_TIME_DEFAULT)
        time_file = t1.strftime(xcons.FMT_TIME_FILE)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = pcb_sn

            comp_data = []
            for comp_entity in board_entity.yield_comp_entity():
                comp_data.append({
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                })

                comp_src_image = comp_entity.image_path

                if comp_entity.is_robot_ng() and comp_src_image:
                    comp_tag = comp_entity.designator
                    ng_str = comp_entity.robot_ng_str
                    dst_img = f"{img_path}/{barcode}_{board_no}_{comp_tag}_{ng_str}"
                    xutil.FileUtil.copy_file(comp_src_image, dst_img, is_auto_add_suffix=True)

            board_data = {
                "pcb_sn": pcb_sn,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_test_time": test_time,
                "pcb_project_name": pcb_entity.project_name,
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "board_comp_number": board_entity.comp_total_number,
                "board_co_ng_number": board_entity.comp_repair_ng_number,
                "board_final_result": board_entity.get_final_result(),
                "comp_data": comp_data
            }

            filepath = f"{txt_path}/{barcode}_{time_file}.txt"
            xutil.FileUtil.write_content_to_file(filepath, json.dumps(board_data,
                                                                      ensure_ascii=False,
                                                                      indent=4
                                                                      ))

        return self.x_response()
