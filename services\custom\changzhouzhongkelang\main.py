"""
# File       : main.py
# Time       ：25/04/27 11:24
# Author     ：wxc
# version    ：python 3.8
# Description：常州中科朗
"""
from datetime import datetime
from typing import Any

from common import xrequest, xcons, xutil, xglobal
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import OtherVo, DataVo


class Engine(ErrorMapEngine):
    version = {
        "version": "release v1.0.0.1",
        "customer": ["常州中科朗", "changzhouzhongkelang"],
        "device": "AIS303",
        "feature": ["获取条码", "条码校验", "上传数据"],
        "author": "wxc",
        "release": """
date: 2025-04-28 14:29  ATAOI_2019-38993 获取条码+条码校验+发送mes
 """, }

    other_form = {
        "api_id": {
            "ui_name": "API分类",
            "value": "6"
        },
        "api_login": {
            "ui_name": "登录接口URL",
            "value": "http://***********:8000/api/admin/mes/login"
        },
        "api_get_sn": {
            "ui_name": "获取条码接口URL",
            "value": ""
        },
        "api_check": {
            "ui_name": "条码校验接口URL",
            "value": "http://***********:8000/api/admint/mes/sn-check"
        },
        "api_upload": {
            "ui_name": "上传数据接口URL",
            "value": "http://***********:8000/api/admin/mes/uploaddata"
        }
    }
    form = {
        "device_id": {
            "ui_name": "设备编号",
            "value": "",
        },
        "terminal_name": {
            "ui_name": "工作站名称",
            "value": "",
        },
        "worker_id": {
            "ui_name": "工单",
            "value": "",
        },
        "test_user": {
            "ui_name": "测试人员",
            "value": "",
        },
        "factory_code": {
            "ui_name": "工厂",
            "value": "",
        },
        "stage_code": {
            "ui_name": "车间",
            "value": "",
        },
        "process_code": {
            "ui_name": "工序",
            "value": "",
        },
        "line_number": {
            "ui_name": "线别",
            "value": "",
        },
        "machine_no": {
            "ui_name": "机种",
            "value": "",
        },
        "product_name": {
            "ui_name": "料号/产品名称",
            "value": "",
        },
        "shift": {
            "ui_name": "班次",
            "value": "",
        },

    }
    combo = {
        "board_side_ui": {
            "ui_name": "板面",
            "item": ["T面", "B面"],
            "value": "T面",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        item_combo = getattr(main_window, "combo_board_side_ui")
        item_combo.setEditable(True)

    def login_mes(self, other_dao: OtherVo, other_param: Any):
        username, password = other_dao.get_login_info()
        api_login = other_dao.get_value_by_cons_key("api_login", not_null=True)
        device_id = other_dao.get_value_by_cons_key("device_id")
        terminal_name = other_dao.get_value_by_cons_key("terminal_name")

        login_parma = {
            "userId": username,
            "password": password,
            "machineCode": device_id,
            "terminalame": terminal_name,
        }
        ret = xrequest.RequestUtil.post_json(api_login, login_parma)

        if not ret.get("success") or str(ret.get("code")) != "200":
            return self.x_response("false", f"接口异常，登录失败，error：{ret.get('msg')}")

        xglobal.global_data["is_login"] = True

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_get_sn = other_vo.get_value_by_cons_key("api_get_sn", not_null=True)

        pcb_sn = other_vo.get_pcb_sn()
        if not xglobal.global_data.get("is_login"):
            return self.x_response("false", "未登录，请先登录")

        ret = xrequest.RequestUtil.post_json(api_get_sn, {"containerNo": pcb_sn})

        if not ret.get("success") or str(ret.get("code")) != "200":
            return self.x_response("false", f"mes接口异常, message={ret.get('msg')}")

        barcode_str = ret.get('data')
        return self.x_response('true', barcode_str)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        username = xutil.CacheUtil.get("login_username")
        password = xutil.CacheUtil.get("login_password")
        device_id = other_vo.get_value_by_cons_key("device_id")
        terminal_name = other_vo.get_value_by_cons_key("terminal_name")
        worker_id = other_vo.get_value_by_cons_key("worker_id")
        api_check = other_vo.get_value_by_cons_key("api_check")

        if not xglobal.global_data.get("is_login"):
            return self.x_response("false", "未登录，请先登录")

        sn_list = other_vo.list_sn()

        if not sn_list:
            return self.x_response("false", "未扫到条码！")

        err_list = []
        ix = 0
        for sn in sn_list:
            ix += 1
            check_param = {
                "userId": username,
                "password": password,
                "machineCode": device_id,
                "sn": sn,
                "terminalame": terminal_name,
                "workorder": worker_id
            }

            ret = xrequest.RequestUtil.post_json(api_check, check_param)
            if not ret.get("success") or str(ret.get("code")) != "200":
                err_list.append(f"No:{ix} SN:{sn} Error:{ret.get('msg')}")

        if err_list:
            err_msg = "\n".join(err_list)
            return self.x_response("false", f"mes接口异常，error：{err_msg}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        if not xglobal.global_data.get("is_login"):
            return self.x_response("false", "未登录，请先登录")

        worker_id = data_vo.get_value_by_cons_key("worker_id")
        api_upload = data_vo.get_value_by_cons_key("api_upload")
        test_user = data_vo.get_value_by_cons_key("test_user")
        factory_code = data_vo.get_value_by_cons_key("factory_code")
        stage_code = data_vo.get_value_by_cons_key("stage_code")
        process_code = data_vo.get_value_by_cons_key("process_code")
        line_number = data_vo.get_value_by_cons_key("line_number")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        product_name = data_vo.get_value_by_cons_key("product_name")
        shift = data_vo.get_value_by_cons_key("shift")
        board_side_ui = data_vo.get_value_by_cons_key("board_side_ui")
        api_id = data_vo.get_value_by_cons_key("api_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        pcb_sn = pcb_entity.pcb_barcode
        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            comp_data_list = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    for alg_entity in comp_entity.yield_alg_entity():
                        if alg_entity.result != "0":
                            comp_data_list.append({
                                "Location": comp_entity.designator,
                                "Paramcode": alg_entity.test_name,
                                "Testvalue": alg_entity.test_val,
                                "USL": alg_entity.max_threshold,
                                "LSL": alg_entity.min_threshold,
                                "TESTRESULT": comp_entity.get_final_result()
                            })

            board_data = {
                "APIID": int(api_id),
                "Date": datetime.now().strftime(xcons.FMT_TIME_DEFAULT),
                "SN": pcb_sn,
                "ProductName": product_name,
                "shift": shift,
                "workhour": pcb_entity.get_cycle_time(),
                "workorder": worker_id,
                "testprogramfile": pcb_entity.project_name,
                "testtime": test_time,
                "testusert": test_user,
                "Angleacv": "",
                "Istate": "",
                "factorycode": factory_code,
                "statgecode": stage_code,
                "processcode": process_code,
                "Line": line_number,
                "tb": board_side_ui,
                "MACHINENO": machine_no,
                "TESTRESULT": pcb_entity.get_repair_result(),
                "DataValue": comp_data_list
            }
            ret = xrequest.RequestUtil.post_json(api_upload, board_data)

            if not ret.get("success") or str(ret.get("code")) != "200":
                return self.x_response("false", f"上传数据失败，error：{ret.get('msg')}")

        return self.x_response()
