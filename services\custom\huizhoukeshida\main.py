# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/3/28 上午9:38
# Author     ：sch
# version    ：python 3.8
# Description：惠州科士达
"""
from typing import Any

from common import xrequest, xenum
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoukeshida release v1.0.0.7",
        "device": "AIS203",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-03-28 11:26  条码校验，上传数据
date: 2024-04-07 16:06  上传数据时，M_TYPE传2
date: 2024-04-07 16:28  增加接口地址配置项
date: 2024-04-07 17:26  M_WORK_STATIONID参数改为M_WORK_STATION
date: 2024-04-08 16:50  M_EC_STR参数PASS传空
""", }

    form = {
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "value": "",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "",
        },
        "work_station": {
            "ui_name": "工作中心",
            "value": "",
        },
        "operator": {
            "ui_name": "员工号",
            "value": "",
        },
    }

    def __init__(self):
        self.common_config['check_barcode_setting1'] = xenum.CheckSetting1.CheckFirst

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        work_station = other_vo.get_value_by_cons_key("work_station")
        operator = other_vo.get_value_by_cons_key("operator")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "M_TYPE": "1",
                "M_SN": sn,
                "M_WORK_STATION": work_station,
                "M_RES": "",
                "M_EC_STR": "",
                "M_EMP": operator
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, check_param)

            if ret.get('result') != 'OK':
                ret_res = self.x_response(f"false", f"mes接口异常，条码校验失败，error：{ret.get('msg')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        work_station = data_vo.get_value_by_cons_key("work_station")
        operator = data_vo.get_value_by_cons_key("operator")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        ret_res = self.x_response()

        board_result = "PASS"
        comp_result = ""
        pcb_barcode = pcb_entity.pcb_barcode

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            if not pcb_barcode and barcode:
                pcb_barcode = barcode

            if board_entity.is_repair_ng():
                board_result = "FAIL"
                comp_result = "AOI-99"

        pcb_param = {
            "M_TYPE": "2",
            "M_SN": pcb_barcode,
            "M_WORK_STATION": work_station,
            "M_RES": board_result,
            "M_EC_STR": comp_result,
            "M_EMP": operator
        }

        ret = xrequest.RequestUtil.post_json(api_url_data, pcb_param)
        if ret.get('result') != 'OK':
            ret_res = self.x_response(f"false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return ret_res
