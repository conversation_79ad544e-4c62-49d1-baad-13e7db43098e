# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/8/15 上午10:04
# Author     ：sch
# version    ：python 3.8
# Description：昆山元崧
"""
from typing import Any

from suds.client import Client

from common import xrequest, xutil, xcons
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

xml_template2 = """<reflow_aoi>
    <station_id name="{station_id}">
        <station_header>
            <line_id>{line_id}</line_id>
            <production_date>{test_time}</production_date>
            <result>{result}</result>
            <error_code />
            <program_name>{project_name}</program_name>
            <station_supplier>{station_supplier}</station_supplier>
            <station_type>{station_type}</station_type>
            <vtpid>{vtpid}</vtpid>
        </station_header>
        <process_data>
            <Item name="WorkPart.FailCount" value="{comp_ng_number}" />{comp_data_str}
        </process_data>
    </station_id>
</reflow_aoi>"""

xml_template_bak = """<?xml version="1.0" encoding="utf-8"?>
<station_id name="{station_id}">
  <station_header>
    <line_id>{line_id}</line_id>
    <production_date>{test_time}</production_date>
    <result>{result}</result>
    <error_code />
    <program_name>{project_name}</program_name>
    <station_supplier>{station_supplier}</station_supplier>
    <station_type>{station_type}</station_type>
    <vtpid>{vtpid}</vtpid>
  </station_header>
  <process_data>
    <Item name="WorkPart.FailCount" value="{comp_ng_number}" />{comp_data_str}
  </process_data>
</station_id>"""

comp_template = """
    <Item position="{comp_ix}" designator="{comp_tag}" shape="" material_no="{comp_part}" pin="1" window="" pos_x="{pos_x}" pox_y="{pos_y}" test_mode="{alg_name}" test_sub_mode="" status="{status}" err_vision="{comp_robot_ng_str}" err_repair="{comp_repair_ng_str}" />"""

check_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <CheckBarCode xmlns="http://tempuri.org/">
      <ASN>{ASN}</ASN>
      <Op>{Op}</Op>
      <Res>{Res}</Res>
      <Lot>{Lot}</Lot>
      <Uid>{Uid}</Uid>
    </CheckBarCode>
  </soap:Body>
</soap:Envelope>"""

get_barcode_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetUnitMappingList xmlns="http://tempuri.org/">
      <APID>{APID}</APID>
      <ALot>{ALot}</ALot>
    </GetUnitMappingList>
  </soap:Body>
</soap:Envelope>"""

upload_test_data_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <UpLoadTestData xmlns="http://tempuri.org/">
      <ATestData>{ATestData}</ATestData>
      <Op>{Op}</Op>
      <Res>{Res}</Res>
      <Lot>{Lot}</Lot>
      <Uid>{Uid}</Uid>
    </UpLoadTestData>
  </soap:Body>
</soap:Envelope>"""

upload_process_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <Upload_process_data xmlns="http://tempuri.org/">
      <ALot>{ALot}</ALot>
      <AUnit>{AUnit}</AUnit>
      <ASTATION>{ASTATION}</ASTATION>
      <AXMLData>{AXMLData}</AXMLData>
    </Upload_process_data>
  </soap:Body>
</soap:Envelope>"""


class Engine(ErrorMapEngine):
    version = {
        "title": "kunshanyuansong release v1.0.0.15",
        "device": "AIS203",
        "feature": ["条码校验", "从MES获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-08-15 17:15  条码校验，从MES获取条码，上传数据
date: 2024-09-26 14:32  修改请求参数
date: 2024-09-26 15:18  bugfix: OP--->Op
date: 2024-09-29 11:23  修改ATestData参数 
date: 2024-10-09 11:10  修改ATestData参数  1
date: 2024-11-19 15:15  bugfix: 接口上传成功了，还报错
date: 2025-01-21 16:16  jira->32408: 增加整板上传模式
date: 2025-01-22 15:03  jira->32408: bugfix: 发送数据时，无法获取整板条码
date: 2025-01-22 17:29  jira->32408: bugfix: 修复数据异常
""",
    }

    other_form = {
        "api_host": {
            "ui_name": "接口URL",
            "value": "http://**********:8002/TRIAOIService.asmx"
        },
    }

    form = {
        "op": {
            "ui_name": "制程",
            "value": ""
        },
        "res": {
            "ui_name": "资源",
            "value": ""
        },
        "lot": {
            "ui_name": "工单",
            "value": ""
        },
        "uid": {
            "ui_name": "作业人员编号",
            "value": "admin"
        },
        "station": {
            "ui_name": "设备编号",
            "value": ""
        },
        "line_id": {
            "ui_name": "线体ID",
            "value": ""
        },
        "station_supplier": {
            "ui_name": "station_supplier",
            "value": ""
        },
        "station_type": {
            "ui_name": "station_type",
            "value": ""
        },
        "vtpid": {
            "ui_name": "vtpid",
            "value": ""
        },
    }

    combo = {
        "send_type": {
            "ui_name": "发送类型",
            "item": ["整板发送", "拼板发送"],
            "value": "拼板发送"
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        op = other_vo.get_value_by_cons_key("op")
        res = other_vo.get_value_by_cons_key("res")
        lot = other_vo.get_value_by_cons_key("lot")
        uid = other_vo.get_value_by_cons_key("uid")

        check_url = f"{api_host}"
        err_msg_list = []

        ix = 0
        for sn in other_vo.list_sn():
            ix += 1
            check_param = {
                "ASN": sn,
                "Op": op,
                "Res": res,
                "Lot": lot,
                "Uid": uid,
            }

            check_content = check_template.format(**check_param)

            res_api = xrequest.RequestUtil.post_soap(
                check_url,
                check_content,
                soap_action="http://tempuri.org/CheckBarCode"
            )
            res_text = xutil.XmlUtil.get_xml_root_by_str(res_api)[0][0][0].text

            self.log.info(f"ret text: {res_text}")

            ret1, ret2 = res_text.split(";")

            if ret1 != "P":
                err_msg_list.append(f"No:{ix} SN:{sn} Error:{ret2}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{err_str}")

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")

        lot = other_vo.get_value_by_cons_key("lot")

        get_sn_url = f"{api_host}"

        pcb_sn = other_vo.get_pcb_sn()

        get_sn_param = {
            "APID": pcb_sn,
            "ALot": lot,
        }

        get_content = get_barcode_template.format(**get_sn_param)

        res_api = xrequest.RequestUtil.post_soap(
            get_sn_url, get_content, soap_action="http://tempuri.org/GetUnitMappingList"
        )
        res_text = xutil.XmlUtil.get_xml_root_by_str(res_api)[0][0][0].text

        ret1, ret2 = res_text.split(";")
        self.log.info(f"ret text: {res_text}")

        if ret1 != "P":
            return self.x_response("false", f"mes接口异常，获取条码失败，error：{ret2}")

        # ret3, ret4 = ret2.split(";")

        return self.x_response("true", ret2)

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        op = data_vo.get_value_by_cons_key("op")
        res = data_vo.get_value_by_cons_key("res")
        lot = data_vo.get_value_by_cons_key("lot")
        uid = data_vo.get_value_by_cons_key("uid")
        station = data_vo.get_value_by_cons_key("station")

        line_id = data_vo.get_value_by_cons_key("line_id")
        station_supplier = data_vo.get_value_by_cons_key("station_supplier")
        station_type = data_vo.get_value_by_cons_key("station_type")
        vtpid = data_vo.get_value_by_cons_key("vtpid")

        send_type = data_vo.get_value_by_cons_key("send_type")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        api_error_msg_list = []

        project_name = pcb_entity.project_name
        repair_user = pcb_entity.repair_user

        d_time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = d_time_file[:8]
        time_file = d_time_file[8:]

        board_side = pcb_entity.get_board_side()

        test_time2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)

        # 1. 整板发送
        comp_des_list_pcb = []
        comp_type_list_pcb = []
        comp_err_code_list_pcb = []
        ng_number_pcb = 0
        comp_ng_str2_pcb = ""

        pcb_sn = pcb_entity.pcb_barcode
        comp_ng_number_pcb = 0

        self.log.info(f"发送类型：{send_type}")

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_number_pcb += board_entity.comp_robot_ng_number

            if not pcb_sn and barcode:
                pcb_sn = barcode

            # 2. 拼板发送
            comp_des_list = []
            comp_type_list = []
            comp_err_code_list = []

            ng_number = 0

            comp_ng_str2 = ""
            comp_ix2 = 0
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():

                    for alg_entity in comp_entity.yield_alg_entity():
                        comp_ix2 += 1

                        # 接口2的器件列表数据
                        row2 = comp_template.format(**{
                            "comp_ix": board_no,
                            "comp_tag": comp_entity.designator,
                            "comp_part": comp_entity.part,
                            "pos_x": comp_entity.geometry.cx,
                            "pos_y": comp_entity.geometry.cy,
                            "alg_name": alg_entity.test_name,
                            "status": comp_entity.get_final_result("0", "0", "2"),
                            "comp_robot_ng_str": comp_entity.robot_ng_str,
                            "comp_repair_ng_str": comp_entity.repair_ng_str
                        })

                        comp_ng_str2 += row2
                        comp_ng_str2_pcb += row2

                    if comp_entity.is_repair_ng():
                        comp_des_list.append(f"{board_no}_{comp_entity.designator}")
                        comp_type_list.append(comp_entity.type)
                        comp_err_code_list.append(comp_entity.repair_ng_code)

                        comp_des_list_pcb.append(f"{board_no}_{comp_entity.designator}")
                        comp_type_list_pcb.append(comp_entity.type)
                        comp_err_code_list_pcb.append(comp_entity.repair_ng_code)

                        ng_number += 1
                        ng_number_pcb += 1

            if send_type == "拼板发送":
                self.log.info(f"拼板发送中...")
                result = board_entity.get_repair_result("0;0;1;0", "0;0;0;1")
                comp_number = board_entity.comp_total_number

                if ng_number != 0:
                    comp_des_str = ",".join(comp_des_list)
                    comp_type_str = ",".join(comp_type_list)
                    comp_err_code_str = ",".join(comp_err_code_list)

                    comp_err_str = f"{comp_des_str};{comp_type_str};{comp_err_code_str};0;0;0;"
                else:
                    comp_err_str = ""

                a_test_data = f"{project_name};{barcode};;SHIP;{repair_user};{lot};{date_file};" \
                              f"{time_file};{result};{board_side};{comp_number};{ng_number};{comp_err_str}"

                data_param1 = {
                    "ATestData": a_test_data,
                    "Op": op,
                    "Res": res,
                    "Lot": lot,
                    "Uid": uid,
                }

                data_param1_content = upload_test_data_template.format(**data_param1)

                data_url1 = f"{api_host}"
                res_api = xrequest.RequestUtil.post_soap(
                    data_url1, data_param1_content, soap_action="http://tempuri.org/UpLoadTestData"
                )
                res_text = xutil.XmlUtil.get_xml_root_by_str(res_api)[0][0][0].text
                self.log.info(f"ret text: {res_text}")

                ret1, ret2 = res_text.split(";")

                if ret1 != "P":
                    api_error_msg_list.append(f"上传数据失败[UpLoadTestData]，error：{ret2}")

                xml_data_str = xml_template2.format(**{
                    "station_id": station,
                    "line_id": line_id,
                    "test_time": test_time2,
                    "result": board_entity.get_repair_result("0", "-1"),
                    "project_name": project_name,
                    "station_supplier": station_supplier,
                    "station_type": station_type,
                    "vtpid": vtpid,
                    "comp_ng_number": board_entity.comp_robot_ng_number,
                    "comp_data_str": comp_ng_str2
                })

                # data_url2 = f"{api_host}"
                #
                # data_param2 = {
                #     "ALot": lot,
                #     "AUnit": barcode,
                #     "ASTATION": station,
                #     "AXMLData": xml_data_str,
                # }

                # upload_process_data = upload_process_template.format(**data_param2)
                #
                # res_api = xrequest.RequestUtil.post_soap(
                #     data_url2, upload_process_data, soap_action="http://tempuri.org/Upload_process_data"
                # )
                # res_text = xutil.XmlUtil.get_xml_root_by_str(res_api)[0][0][0].text
                # self.log.info(f"ret text: {res_text}")
                #
                # ret1, ret2 = res_text.split(";")
                client = Client(f"{api_host}?wsdl")

                self.log.info(f"正在调用接口：Upload_process_data....")
                self.log.info(f"参数：ALot:{lot} AUnit:{barcode} ASTATION:{station} AXMLData:{xml_data_str}")
                res_text = client.service.Upload_process_data(lot, barcode, station, xml_data_str)
                self.log.info(f"接口返回：{res_text}")
                ret1, ret2 = res_text.split(";")

                if ret1 != "P":
                    api_error_msg_list.append(f"上传数据失败[Upload_process_data]，error：{ret2}")

        if send_type == "整板发送":
            self.log.info(f"整板发送中...")
            result = pcb_entity.get_repair_result("0;0;1;0", "0;0;0;1")
            comp_number = pcb_entity.comp_count

            if ng_number_pcb != 0:
                comp_des_str = ",".join(comp_des_list_pcb)
                comp_type_str = ",".join(comp_type_list_pcb)
                comp_err_code_str = ",".join(comp_err_code_list_pcb)

                comp_err_str = f"{comp_des_str};{comp_type_str};{comp_err_code_str};0;0;0;"
            else:
                comp_err_str = ""

            a_test_data = f"{project_name};{pcb_sn};;SHIP;{repair_user};{lot};{date_file};" \
                          f"{time_file};{result};{board_side};{comp_number};{ng_number_pcb};{comp_err_str}"

            data_param1 = {
                "ATestData": a_test_data,
                "Op": op,
                "Res": res,
                "Lot": lot,
                "Uid": uid,
            }

            data_param1_content = upload_test_data_template.format(**data_param1)

            data_url1 = f"{api_host}"
            res_api = xrequest.RequestUtil.post_soap(
                data_url1, data_param1_content, soap_action="http://tempuri.org/UpLoadTestData"
            )
            res_text = xutil.XmlUtil.get_xml_root_by_str(res_api)[0][0][0].text
            self.log.info(f"ret text: {res_text}")

            ret1, ret2 = res_text.split(";")

            if ret1 != "P":
                api_error_msg_list.append(f"上传数据失败[UpLoadTestData]，error：{ret2}")

            xml_data_str = xml_template2.format(**{
                "station_id": station,
                "line_id": line_id,
                "test_time": test_time2,
                "result": pcb_entity.get_repair_result("0", "-1"),
                "project_name": project_name,
                "station_supplier": station_supplier,
                "station_type": station_type,
                "vtpid": vtpid,
                "comp_ng_number": comp_ng_number_pcb,
                "comp_data_str": comp_ng_str2_pcb
            })

            client = Client(f"{api_host}?wsdl")

            self.log.info(f"正在调用接口：Upload_process_data....")
            self.log.info(f"参数：ALot:{lot} AUnit:{pcb_sn} ASTATION:{station} AXMLData:{xml_data_str}")
            res_text = client.service.Upload_process_data(lot, pcb_sn, station, xml_data_str)
            self.log.info(f"接口返回：{res_text}")
            ret1, ret2 = res_text.split(";")

            if ret1 != "P":
                api_error_msg_list.append(f"上传数据失败[Upload_process_data]，error：{ret2}")

        if api_error_msg_list:
            err_msg_str = "\n".join(api_error_msg_list)
            return self.x_response("false", f"mes接口异常，{err_msg_str}")

        return self.x_response()
