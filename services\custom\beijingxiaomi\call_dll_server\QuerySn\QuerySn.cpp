﻿#include <windows.h>
#include <iostream>
#include <string> 
#include <tchar.h> 
#include <stdio.h>    
#include <fstream>
#include <sstream>
#include <filesystem> // C++17 引入的文件系统库

using namespace std;

typedef int (InitRoute)(int platform);
typedef int (QueryPlateSN)(const char* PlateSN, char* BoardSN4);

InitRoute* p_InitRoute = nullptr;
QueryPlateSN* p_QueryPlateSN = nullptr;


// 获取当前时间并格式化为 "YYYY-MM-DD HH:MM:SS.sss" 格式
string getCurrentTimestamp() {
    // 获取当前时间点
    auto now = chrono::system_clock::now();
    auto now_ms = chrono::time_point_cast<chrono::milliseconds>(now);

    // 转换为时间结构体
    time_t now_c = chrono::system_clock::to_time_t(now);
    tm local_time;
    localtime_s(&local_time, &now_c); // Windows 使用 localtime_s

    // 获取毫秒部分
    auto milliseconds = now_ms.time_since_epoch().count() % 1000;

    // 格式化时间字符串
    ostringstream oss;
    oss << put_time(&local_time, "%Y-%m-%d %H:%M:%S") << '.'
        << setfill('0') << setw(3) << milliseconds;

    return oss.str();
}

void outputLog(const string& message, bool error = true) {
    // 获取当前可执行文件的路径
    TCHAR exePath[MAX_PATH + 1] = { 0 };
    GetModuleFileName(NULL, exePath, MAX_PATH);
    *_tcsrchr(exePath, _T('\\')) = 0; //删除文件名, 只获得路径字串
    *_tcsrchr(exePath, _T('\\')) = 0; //再次截断路径, 获取上级目录

    string logHeader = getCurrentTimestamp() + " ---Info--- ";
    string logMessage = "";

    // 创建 LOG 文件夹
    wstring logFolderPath = wstring(exePath) + L"\\Log";
    if (CreateDirectory(logFolderPath.c_str(), NULL)) {
        logMessage = logHeader + "Log directory created successfully.";
        cout << logMessage << endl;
    }
    else {
        DWORD error = GetLastError();
        if (error != ERROR_ALREADY_EXISTS) {
            logMessage = logHeader + "Failed to create Log directory!";
            cerr << logMessage << endl;
        }
    }

    logMessage = logHeader + message;
    // 输出到控制台
    if (error) {
        cerr << logMessage << endl;
    }
    else {
        cout << logMessage << endl;
    }

    // 打开日志文件（追加模式）  --- 日志统一在querySnServer.exe中保存
    //wstring logFilePath = logFolderPath + L"\\query_sn.log";
    //ofstream logFile(logFilePath, ios::app);
    //if (!logFile) {
    //    wcerr << L"打开日志文件失败: " << logFilePath << endl;
    //    return;
    //}
    //logFile << logMessage << endl;
    //logFile.close();
}

// 自动转换TCHAR[]为std::string（兼容Unicode和多字节）
std::string TcharToString(const TCHAR* tcharStr) {
#ifdef _UNICODE
    // Unicode模式：宽字符 → UTF-8多字节
    int len = WideCharToMultiByte(CP_UTF8, 0, tcharStr, -1, nullptr, 0, nullptr, nullptr);
    if (len == 0) return "";

    std::string result(len, 0);
    WideCharToMultiByte(CP_UTF8, 0, tcharStr, -1, &result[0], len, nullptr, nullptr);
    return result;
#else
    // 多字节模式：直接转换
    return std::string(tcharStr);
#endif
}

bool loadDll()
{
    TCHAR exePath[MAX_PATH + 1] = { 0 };
    GetModuleFileName(NULL, exePath, MAX_PATH);
    *_tcsrchr(exePath, _T('\\')) = 0; //删除文件名, 只获得路径字串

    TCHAR szModulePath[512] = { 0 };
    _stprintf_s(szModulePath, 512, _T("%s\\RouteCommunication.dll"), exePath);
    // 宽字符串转UTF-8多字节字符串（Unicode字符集）
    std::string dllPath = "DLL加载路径:" + TcharToString(szModulePath);
    outputLog(dllPath);

    SetDllDirectory(szModulePath);
    HMODULE m_hDataModule;
    m_hDataModule = LoadLibrary(szModulePath);
    if (nullptr == m_hDataModule)
    {
        DWORD errorCode = GetLastError();
        string message = "Load RouteCommunication.dll error, GetLastError(): " + to_string(errorCode);
        outputLog(message);
        return false;
    }
    else
    {
        outputLog("Load RouteCommunication.dll sucess",false);
    }


#pragma region 加载普通Dll
    p_InitRoute = (InitRoute*)GetProcAddress(m_hDataModule, "Ex_InitRoute");
    if (nullptr == p_InitRoute)
    {
        DWORD errorCode = GetLastError();
        string message = "Load RouteCommunication/Ex_InitRoute() error, GetLastError(): " + to_string(errorCode);
        outputLog(message);
        FreeLibrary(m_hDataModule);
        m_hDataModule = nullptr;
        return false;
    }
    else
    {
        outputLog("Load RouteCommunication/Ex_InitRoute() sucess",false);
    }

    p_QueryPlateSN = (QueryPlateSN*)GetProcAddress(m_hDataModule, "EX_QueryPlateSN4");
    if (nullptr == p_QueryPlateSN)
    {
        DWORD errorCode = GetLastError();
        string message = "Load RouteCommunication/Ex_QueryPlateSN() error, GetLastError(): " + to_string(errorCode);
        outputLog(message);
        FreeLibrary(m_hDataModule);
        m_hDataModule = nullptr;
        return false;
    }
    else
    {
        outputLog("Load RouteCommunication/Ex_QueryPlateSN() sucess",false);
    }
#pragma endregion

    return true;
}

// 解析命令行参数
const char* parseArgument(int argc, char* argv[], const char* option)
{
    for (int i = 1; i < argc; ++i) {
        if (strncmp(argv[i], option, strlen(option)) == 0) {
            return argv[i] + strlen(option); // 返回等号后的内容
        }
    }
    return nullptr; // 如果未找到对应的参数，返回 nullptr
}

int main(int argc, char* argv[])
{
    // 解析 -PlateSn 参数
    const char* plateSn = parseArgument(argc, argv, "-PlateSn=");
    if (!plateSn) {
        outputLog("命令行发送参数错误，请使用：QuerySn.exe -PlateSn=<value>");
        return 1; // 返回错误码
    }
    else
    {
        string message = "查询 PlateSn:" + string(plateSn);
        outputLog(message,false);
    }

    if (!loadDll())
        return 2;

    int ret = p_InitRoute(0);
    if (ret != 0){
        string message = "Ex_InitRoute 初始化执行失败,函数返回值：" + to_string(ret);
        outputLog(message);
        return 3;
    }
    else
    {
        outputLog("Ex_InitRoute 初始化执行成功",false);
    }

    // 调用查询 SN 函数
    char SN[256] = { 0 };
    ret = p_QueryPlateSN(plateSn, SN);
    if (ret == 0){
        string message = "EX_QueryPlateSN4 查询成功，{BoardSN4:" + string(SN) + "}";
        outputLog(message,false);
        return 0;
    }
    else{
        string message = "EX_QueryPlateSN4 查询失败,函数返回值：" + to_string(ret);
        outputLog(message);
        return 4;
    }
}