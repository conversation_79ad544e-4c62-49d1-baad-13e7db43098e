# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : test_api.py
# Time       ：2023/12/11 下午2:31
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json

# f = '{"SN_ORDER":"1","SN":"V1EBW001207"},{"SN_ORDER":"2","SN":"V1EBW001208"},{"SN_ORDER":"3","SN":"V1EBW001205"},{"SN_ORDER":"4","SN":"V1EBW001206"}'
#
# f1 = '[' + f + ']'
# print(f1)
#
# data_json = json.loads(f1)
# print(data_json, type(data_json))

ret = '{"result":[{"RET_CODE":"1","RET_VALUE":[{"SN_ORDER":"1","SN":"V1ECG000076"},{"SN_ORDER":"2","SN":"V1ECG000075"}]}]}'
ret_json = json.loads(ret)
print(ret_json)

print("-----bad-----")
print(ret)
print("-----good-----")
print(json.dumps(ret_json, separators=(",", ":")))

