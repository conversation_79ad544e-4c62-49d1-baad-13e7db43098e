# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/15 上午10:12
# Author     ：sch
# version    ：python 3.8
# Description：长沙利亚德
"""
import time
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo, ButtonVo
from engine.MesEngine import ErrorMapEngine

global_data = {}


def upload_device_data(api_url_eap,
                       device_code, state, status_code_v3, status_desc_v3,
                       alert_status="1"):
    """
    上传设备状态
    :return:
    """
    time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

    param = {
        "Token": "",
        "Paras": [
            device_code,
            {
                "AssetsCode": device_code,
                "Barcode": "",
                "EQPState": state,  # 设备状态..       int:  1,运行  2,报警  3,待机  4,停机
                "AlertCode": status_code_v3,  # 报警代码
                "AlertInfo": status_desc_v3,  # 报警信息
                "AlertStatus": alert_status,  # 报警状态
                "AlertDuration": "",  # 报警持续时间
                "UpdateTime": time_now,  # 上传时间
                "TimeStamp": time_now,  # 设备心跳
                "Qty": "",  # 生产总数
                "DownTime": "",  # 停机时间
                "TotalRunTime": ""  # 总运行时间(开机)
            }
        ]
    }

    return xrequest.RequestUtil.post_json(api_url_eap, param)


class Engine(ErrorMapEngine):
    version = {
        "title": "changshaliyade release v1.0.0.4",
        "device": "AIS50X",
        "feature": ["条码校验", "上传数据", "上传设备状态"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-15 17:01  条码校验，上传数据，上传设备状态
date: 2024-08-05 10:20  修改请求参数
date: 2024-09-19 09:41  修改请求参数 + 1 
date: 2024-09-19 09:41  ATAOI_2019-31449:发送设备状态接口（UpLoadEapData）修改参数,发送测试数据接口（SMTWipMove）修改参数
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "接口URL(校验)",
            "value": "http://127.0.0.1:7666/dataService/webapi/ScadaApi/WipMoveValidate",
        },
        "api_url_data": {
            "ui_name": "接口URL(过站)",
            "value": "http://127.0.0.1:7666/dataService/webapi/ScadaApi/SMTWipMove",
        },
        "api_url_eap": {
            "ui_name": "接口URL(设备状态)",
            "value": "http://127.0.0.1:9003/apigateway/invoke/deviceid/IO/UpLoadEapData",
        },
    }

    other_combo = {
        "is_cron_upload": {
            "ui_name": "是否开启定时上传(重启MES配置器生效)",
            "item": ["Yes", "No"],
            "value": "No",
        },
        "cron_upload_freq": {
            "ui_name": "定时上传频率(秒)",
            "item": ["5", "10", "30", "60", "120", "300", "600", "1200", "3000", "6000", "7200", "14400"],
            "value": "5",
        }
    }

    form = {
        "device_code": {
            "ui_name": "设备编码/设备ID号",
            "value": "",
        },
        "assets_code": {
            "ui_name": "设备工位账号",
            "value": "",
        },
        "inv_org_id": {
            "ui_name": "库存组织",
            "value": "",
        },
    }

    button = {
        "release_alarm": {
            "ui_name": "解除报警",
        },
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        is_cron_upload = other_vo.get_value_by_cons_key("is_cron_upload")
        cron_upload_freq = other_vo.get_value_by_cons_key("cron_upload_freq")

        main_window.set_cron_setting(
            is_cron_upload == "Yes",
            int(cron_upload_freq)
        )

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        assets_code = other_vo.get_value_by_cons_key("assets_code")
        device_code = other_vo.get_value_by_cons_key("device_code")
        inv_org_id = other_vo.get_value_by_cons_key("inv_org_id")

        sn_data_list = []

        for sn in other_vo.list_sn():
            # sn_data_list.append({
            #     "AssetsCode": assets_code,
            #     "Barcode": sn,
            #     "InvOrgId": inv_org_id
            # })
            sn_data_list.append({
                "Value": {
                    "EquipCode": device_code,
                    "Barcode": sn
                }
            })

        try:
            inv_org_id = int(inv_org_id)
        except Exception as err:
            self.log.warning(f"库存数字无法转换成数字，err:{err}")

        check_param = [{
            "Parameters": sn_data_list,
            "Context": {
                "InvOrgId": inv_org_id
            }
        }]

        ret = xrequest.RequestUtil.post_json(api_url_check, check_param)
        if not ret.get("Success"):
            return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('Message')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        device_code = data_vo.get_value_by_cons_key("device_code")
        inv_org_id = data_vo.get_value_by_cons_key("inv_org_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        test_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        review_time = pcb_entity.get_review_time().strftime(xcons.FMT_TIME_DEFAULT)

        board_data_v2 = []
        time_now = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            comp_data_v2 = []

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():
                    comp_data_v2.append({
                        "BothSideCode": "Top" if comp_entity.board_side == "T" else "Bot",
                        "CreateDate": test_time,
                        "ReviseEndDate": review_time,
                        "TestResult": comp_entity.robot_ng_str,
                        "ReviseResult": comp_entity.repair_ng_str,
                        "Barcode": barcode,
                        "PartsName": comp_entity.designator,
                        "FaultCode": comp_entity.robot_ng_code,
                        "RevisedFaultCode": comp_entity.repair_ng_code,
                        "ImageUrl": "",
                    })

            board_data_v2.append({
                "Value": {
                    "ProgramName": pcb_entity.project_name,
                    "Qty": str(board_entity.comp_total_number),
                    "EquipCode": device_code,
                    "Barcode": barcode,
                    "TestResult": board_entity.get_robot_result("PASS", "FAIL"),
                    "ReviseResult": board_entity.get_repair_result("PASS", "FAIL"),
                    "TimeStamp": time_now,
                    "PartDetectionInfos": comp_data_v2,
                    "ImageInfos": []
                }
            })

        try:
            inv_org_id = int(inv_org_id)
        except Exception as err:
            self.log.warning(f"库存组织无法转换为数字，err：{err}")

        data_param = [{
            "Parameters": board_data_v2,
            "Context": {
                "InvOrgId": inv_org_id
            }
        }]

        ret = xrequest.RequestUtil.post_json(api_url_data, data_param)
        if not ret.get("Success"):
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('Message')}")

        return self.x_response()

    def send_device_status_to_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_eap = other_vo.get_value_by_cons_key("api_url_eap")
        device_code = other_vo.get_value_by_cons_key("device_code")
        cron_upload_freq = other_vo.get_value_by_cons_key("cron_upload_freq")

        status_code_v3 = other_vo.get_status_code_v3()
        status_desc_v3 = other_vo.get_status_desc_v3()

        if status_code_v3 not in xcons.DEVICE_STATUS_V3:
            self.log.warning(f"未知的设备状态码！{status_code_v3}")
            return self.x_response()

        if status_code_v3 in ["1001", "1002", "1003", "1004", "1005",
                              "3003", "3004", "3005", "3006", "3007"]:
            state = 1

            global_data["last_status_code"] = ""
            global_data["last_status_desc"] = ""

        elif status_code_v3 == "0001":
            last_status_code = global_data.get("last_status_code", "")
            last_status_desc = global_data.get("last_status_desc", "")

            if not last_status_code:
                return self.x_response("false", f"没有异常状态需要解除！")

            ret = upload_device_data(api_url_eap,
                                     device_code,
                                     global_data.get("last_state", 3),
                                     last_status_code,
                                     last_status_desc,
                                     alert_status="2")

            global_data["last_status_code"] = ""
            global_data["last_status_desc"] = ""

            if not ret.get("IsSuccess"):
                return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('Message')}")

            return self.x_response()

        elif status_code_v3 in ["3001", "3002"]:
            state = 3

            global_data["last_status_code"] = ""
            global_data["last_status_desc"] = ""
        elif status_code_v3 in ["2001", "2002"]:
            state = 2

            global_data["last_status_code"] = status_code_v3
            global_data["last_status_desc"] = status_desc_v3

        elif status_code_v3 in ["5001", "5002"]:
            state = 4

            global_data["last_status_code"] = status_code_v3
            global_data["last_status_desc"] = status_desc_v3
        else:
            self.log.warning(f"未知的设备状态码！{status_code_v3}")

            global_data["last_status_code"] = ""
            global_data["last_status_desc"] = ""
            return self.x_response()

        global_data["last_state"] = state

        ret = upload_device_data(api_url_eap,
                                 device_code,
                                 state,
                                 status_code_v3,
                                 status_desc_v3,
                                 )

        if not ret.get("IsSuccess"):
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('Message')}")

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_eap = other_vo.get_value_by_cons_key("api_url_eap")
        device_code = other_vo.get_value_by_cons_key("device_code")
        cron_upload_freq = other_vo.get_value_by_cons_key("cron_upload_freq")

        ret = upload_device_data(api_url_eap,
                                 device_code,
                                 global_data.get("last_state", 3),
                                 "3001",
                                 "定时触发"
                                 )

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):
        api_url_eap = btn_vo.get_value_by_cons_key("api_url_eap")
        device_code = btn_vo.get_value_by_cons_key("device_code")
        cron_upload_freq = btn_vo.get_value_by_cons_key("cron_upload_freq")

        last_status_code = global_data.get("last_status_code", "")

        if not last_status_code:
            return self.x_response("false", f"没有异常状态需要解除！")

        ret = upload_device_data(api_url_eap,
                                 device_code,
                                 global_data.get("last_state", 3),
                                 last_status_code,
                                 global_data.get("last_status_desc", ""),
                                 alert_status="2")

        if not ret.get("IsSuccess"):
            return self.x_response("false", f"mes接口异常，上传设备状态失败，error：{ret.get('Message')}")

        return self.x_response()
