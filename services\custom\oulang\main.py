# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/12/25 下午4:58
# Author     ：sch
# version    ：python 3.8
# Description： 苏州欧朗
"""
from typing import Any

from common import xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "oulang release v1.0.0.2",
        "device": "501",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-12-26 09:38  条码校验，上传数据
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": "",
        },
        "workstation": {
            "ui_name": "工位",
            "value": "",
        },
        "username": {
            "ui_name": "员工工号",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
    }

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        workstation = other_vo.get_value_by_cons_key("workstation")
        username = other_vo.get_value_by_cons_key("username")

        error_msg = ""
        for sn in other_vo.list_sn():
            check_param = {
                "functionType": "checkData",
                "sn": sn,
                "wcsn": workstation,
                "emp": username
            }

            ret = xrequest.RequestUtil.post_json(api_url, check_param)
            if str(ret.get('code')) != '200':
                error_msg = f"mes接口异常，条码校验失败，error：{ret.get('msg')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        workstation = data_vo.get_value_by_cons_key("workstation")
        username = data_vo.get_value_by_cons_key("username")
        order_id = data_vo.get_value_by_cons_key("order_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_tag_list = []
            comp_ng_code = []

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_tag_list.append(comp_entity.designator)
                    comp_ng_code.append(comp_entity.repair_ng_code)

            board_param = {
                "functionType": "mes_esop_webservice",
                "processPara1": barcode,
                "processPara2": workstation,
                "processPara3": username,
                "processPara4": order_id,
                "processPara6": board_entity.get_repair_result("OK", "KO"),
                "processPara7": '|'.join(comp_ng_code),
                "processPara8": '|'.join(comp_tag_list),
                "processPara9": str(board_entity.comp_repair_ng_number),
                "processPara10": board_no
            }

            ret = xrequest.RequestUtil.post_json(api_url, board_param)
            if str(ret.get('code')) != '200':
                error_msg = f"mes接口异常，上传数据失败，error：{ret.get('msg')}"

        if error_msg:
            return self.x_response("false", error_msg)

        return self.x_response()
