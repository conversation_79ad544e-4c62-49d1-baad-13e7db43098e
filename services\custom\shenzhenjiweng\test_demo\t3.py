# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : t3.py
# Time       ：2025/5/22 下午3:47
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import binascii
import struct
import zlib


def hex_image_path(image_path):
    print(f"upload image: {image_path}")
    with open(image_path, 'rb') as file:
        image_data = file.read()  # 读取二进制数据

    compressed_data = zlib.compress(image_data)  # zlib 压缩
    hex_str = binascii.hexlify(compressed_data)
    print(hex_str.decode('utf8'))


def compress_file_to_hex(file_path: str) -> str:
    """压缩文件并返回十六进制字符串（等效于原Qt代码）"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            compressed_data = zlib.compress(raw_data)  # 默认压缩级别6
            header = struct.pack(">I", len(raw_data))
            compressed_data = header + compressed_data

            return compressed_data.hex()  # 转为十六进制字符串
    except IOError as e:
        print(f"打开文件失败: {e}")
        return ""


if __name__ == '__main__':
    print(compress_file_to_hex("/home/<USER>/Downloads/jiweng/2/b1.jpg"))
