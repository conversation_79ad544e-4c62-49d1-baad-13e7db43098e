# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/06/11 16:40
# Author     ：chencb
# version    ：python 3.8
# Description：天津振而达 https://jira.cvte.com/browse/ATAOI_2019-40085
"""
from typing import Any
from common import xutil, xcons
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine

board_data_template = "MainSN:{pcb_sn}\nPanelSN:{board_sn}\nPanelID:{board_no}\nModelName:{model_name}\nSide:{board_side}\n" \
                      "MachineName:{machine_name}\nCustomerName:{customer_name}\nOperator:{operator}\nProgramer:{programer}\n" \
                      "InspectionDate:{date}\nBeginTime:{start_time}\nEndTime:{end_time}\nCycleTimeSec:{cycle_time}\n" \
                      "InspectionBatch:{order}\nReportResult:{robot_result}\nConfirmedResult:{repair_result}\n" \
                      "TotalComponent:{total_comp_cnt}\nReportFailComponent:{robot_fail_comp_cnt}\nConfirmedFailComponent:{repair_fail_comp_cnt}"

comp_header = "ComponentName,MainSN,PanelSN,SubBoardID,LibraryModel,PN,Package,Angle,ReportResult,ReportResultCode,ConfirmedResult,ConfirmedResultCode\n"
comp_data_template = "{designator},{pcb_sn},{board_sn},{board_no},{library_model},{part},{package},{angle}," \
                     "{robot_ng_str},{robot_ng_code},{repair_result},{repair_ng_code}\n"


class Engine(ErrorMapEngine):
    version = {
        "customer": ["天津振而达", "tianjinzhenerda"],
        "version": "release v1.0.0.2",
        "device": " AIS50X",
        "feature": ["生成txt文档"],
        "author": "chenchongbing",
        "release": """
date: 2025-06-12 11:50  jira:40085 生成txt文档
date: 2025-06-13 10:00  一些参数填写下默认值；默认双面合并
""", }

    path = {
        "save_txt_path": {
            "ui_name": "txt保存路径",
            "value": "",
        },
    }

    form = {
        "model_name": {
            "ui_name": "产品型号",
            "value": "AIS501B"
        },
        "machine_name": {
            "ui_name": "机器序号",
            "value": ""
        },
        "customer_name": {
            "ui_name": "客户名称",
            "value": ""
        },
        "operator": {
            "ui_name": "维修站操作账号",
            "value": "ADMIN"
        },
        "programer": {
            "ui_name": "设备操作账号",
            "value": "admin"
        },
    }

    def __init__(self):
        # 双面数据默认合并
        self.app_setting["merge_send"] = True

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        inspect_type = data_vo.get_inspect_type()
        # 只生成复判后的数据
        if inspect_type == xcons.INSPECTOR:
            self.log.info("复判后才生成txt，这次为机器检测发送，直接返回不处理")
            return self.x_response()

        model_name = data_vo.get_value_by_cons_key("model_name")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        customer_name = data_vo.get_value_by_cons_key("customer_name")
        operator = data_vo.get_value_by_cons_key("operator")
        programer = data_vo.get_value_by_cons_key("programer")
        save_txt_path = data_vo.get_value_by_cons_key("save_txt_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time = pcb_entity.get_start_time()
        date_str = start_time.date().strftime("%Y/%m/%d")
        start_time_str = start_time.time().strftime("%H:%M:%S")
        end_time = pcb_entity.get_end_time()
        end_time_str = end_time.time().strftime("%H:%M:%S")
        cycle_time = pcb_entity.get_cycle_time()

        board_side = 'T&B' if pcb_entity.board_side == 'T+B' else pcb_entity.board_side

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_data = {
                "pcb_sn": pcb_entity.pcb_barcode,
                "board_sn": board_entity.barcode,
                "board_no": board_entity.board_no,
                "model_name": model_name,
                "board_side": board_side,
                "machine_name": machine_name,
                "customer_name": customer_name,
                "operator": operator,
                "programer": programer,
                "date": date_str,
                "start_time": start_time_str,
                "end_time": end_time_str,
                "cycle_time": cycle_time,
                "order": pcb_entity.order_id,
                "robot_result": board_entity.get_robot_result('P', 'F'),
                "repair_result": board_entity.get_repair_result('P', 'F'),
                "total_comp_cnt": board_entity.comp_total_number,
                "robot_fail_comp_cnt": board_entity.comp_robot_ng_number,
                "repair_fail_comp_cnt": board_entity.comp_repair_ng_number,
            }
            txt_content = board_data_template.format(**board_data)
            # 默认13行空格
            txt_content += '\n' * 13
            txt_content += comp_header
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.robot_result:
                    continue

                comp_data = {
                    "designator": comp_entity.designator,
                    "pcb_sn": pcb_entity.pcb_barcode,
                    "board_sn": board_entity.barcode,
                    "board_no": board_entity.board_no,
                    "library_model": '',
                    "part": comp_entity.part,
                    "package": comp_entity.package,
                    "angle": comp_entity.geometry.angle,
                    "robot_ng_str": comp_entity.robot_ng_str,
                    "robot_ng_code": comp_entity.robot_ng_code,
                    "repair_result": comp_entity.get_final_result("OK", "OK", "NG"),
                    "repair_ng_code": comp_entity.repair_ng_code,
                }
                txt_content += comp_data_template.format(**comp_data)

            # 文件命名：整板条码_拼版号_日期_时间.txt
            date_str = start_time.date().strftime("%Y%m%d")
            start_time_str = start_time.time().strftime("%H%M%S")
            file_path = f"{save_txt_path}/{pcb_entity.pcb_barcode}_{board_entity.board_no}_{date_str}_{start_time_str}.txt"
            xutil.FileUtil.write_content_to_file(file_path, txt_content)

        return self.x_response()
