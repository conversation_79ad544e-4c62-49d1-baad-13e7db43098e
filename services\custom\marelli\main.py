# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/31 下午15:38
# Author     ：chencb
# version    ：python 3.8
# Description：marelli https://jira.cvte.com/browse/ATAOI_2019-38356
"""
import os
from typing import Any

from common import xcons
from common.xutil import log, x_response, XmlUtil
from engine.MesEngine import ErrorMapEngine
from services.custom.marelli.mar_module import parse_board_side
from vo.mes_vo import DataVo


class Engine(ErrorMapEngine):
    version = {
        "title": "marelli release v1.0.0.7",
        "device": "AIS43X, AIS63X",
        "feature": ["生成机器检测xml", "生成复判xml"],
        "author": "chenchongbing",
        "release": """
date: 2025-03-31 20:00  jira:38356 生成机器检测xml、生成复判xml
date: 2025-04-02 14:00  BUGFIX:机器检测xml中有些拼板StatusCode="FAIL"但没有Symptom数据
date: 2025-04-26 12:01  ATAOI_2019-38504：根据板面传递OperationNumber字段参数+PassRemainingUnits固定传true
date: 2025-04-27 11:31  更新翻译文件
date: 2025-05-06 17:20  bugfix: 板面信息修复
date: 2025-05-16 16:12  ATAOI_2019-38356: 界面增加：设备名称,文件命名方式:设备名称_整板条码_板面_板卡检测结果（板面是TOP/BOT）/设备名称_整板条码_REPARACION_板面_板卡复判结果（板面是TOP/BOT）
date: 2025-06-12 10:00  复判OperationNumbertop和bottom值反了；复判文件名和StatusCode全部都为PASS
""",
    }

    path = {
        "aoi_path": {
            "ui_name": "AOI Xml Path",
            "value": "",
        },
        "review_path": {
            "ui_name": "Review Xml Path",
            "value": "",
        },
    }
    form = {
        "device_name": {
            "ui_name": "Device Name",
            "value": "",
        }
    }

    # combo = {
    #     "operation_num": {
    #         "ui_name": "OperationNumber",
    #         "item": ["AOI_TOP", "AOI_BOT"],
    #         "value": "AOI_TOP",
    #     }
    # }

    xmlns_attrs = 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" Version="4.0" xmlns="Valor.vCheckTester.xsd"'
    unit_pass_attrs = 'Timestamp="{inspect_time}" SerialNumber="{barcode}" StatusCode="{final_result}"'
    unit_fail_attrs = f'''Sequence="{{board_no}}" {unit_pass_attrs}'''

    save_xml_template = f'''
    <?xml version="1.0" encoding="utf-8"?>
    <vCheckTester {xmlns_attrs} EquipmentNumber="93" OperationNumber="{{operation_num}}" PassRemainingUnits="true" >
        {{units}}
    </vCheckTester>'''
    unit_pass_template = f'''<Unit {unit_pass_attrs} />'''
    unit_fail_template = f'''
    <Unit {unit_fail_attrs}>
        {{symptoms}}
    </Unit>
    '''
    symptom_template = f'''
    <Symptom Name="{{ng_code}}" Type="Component">
      <Message>{{ng_str}}</Message>
      <Component Name="{{designator}}" />
    </Symptom>
    '''

    def __init__(self):
        # 默认需为英文版
        self.set_lang_to_en()

    def _save_xml_to_file(self, xml_data, file_path):
        formatted_xml = XmlUtil.format_xml(xml_data)
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(formatted_xml)
                self.log.info(f'save xml path:{file_path}')
                self.log.info(f'save xml data:\n {formatted_xml}')
            return x_response()
        except Exception as e:
            return x_response('false', f'save xml fail, error:{e}')

    def _generate_aoi_xml(self, data_vo, board_side_product: str = "0"):
        aoi_path = data_vo.get_value_by_cons_key('aoi_path')
        device_name = data_vo.get_value_by_cons_key('device_name')

        if not aoi_path:
            return x_response('false', 'The aoi xml save path is empty.  Please select it first')

        pcb_entity = data_vo.pcb_entity
        pcb_sn = pcb_entity.pcb_barcode
        project_name = pcb_entity.project_name
        # 2025-03-11T05:54:21
        inspect_time_1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT6)
        # 20250311055421
        inspect_time_2 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        pcb_robot_result = pcb_entity.get_robot_result('true', 'false')
        pcb_final_result = pcb_entity.get_robot_result('PASS', 'FAIL')

        operation_num = "AOI_TOP" if board_side_product == "0" else "AOI_BOT"
        if pcb_robot_result == 'true':
            pcb_param = {
                'operation_num': operation_num,
                'robot_result': pcb_robot_result,
                'inspect_time': inspect_time_1,
                'barcode': pcb_sn,
                'final_result': pcb_final_result
            }
            unit = self.unit_pass_template.format(**pcb_param)
            pcb_param['units'] = unit
            save_xml = self.save_xml_template.format(**pcb_param)
        else:
            board_data_list = []
            for board_entity in pcb_entity.yield_board_entity():
                log.info(board_entity)
                if not board_entity.is_robot_ng():
                    continue

                ng_comp_list = []
                for comp_entity in board_entity.yield_comp_entity():
                    if not comp_entity.is_robot_ng():
                        continue

                    comp_param = {
                        'ng_code': comp_entity.robot_ng_code,
                        'ng_str': comp_entity.robot_ng_str,
                        'designator': comp_entity.designator
                    }
                    ng_comp = self.symptom_template.format(**comp_param)
                    ng_comp_list.append(ng_comp)

                board_final_result = board_entity.get_robot_result('PASS', 'FAIL')
                board_param = {
                    'inspect_time': inspect_time_1,
                    'barcode': board_entity.barcode,
                    'final_result': board_final_result,
                    "board_no": board_entity.board_no,
                    'symptoms': '\n'.join(ng_comp_list)
                }
                board_data = self.unit_fail_template.format(**board_param)
                board_data_list.append(board_data)

            pcb_param = {
                'operation_num': operation_num,
                'robot_result': pcb_robot_result,
                'units': '\n'.join(board_data_list),
            }
            save_xml = self.save_xml_template.format(**pcb_param)

        # 文件命名方式:设备名称_整板条码_板面_板卡检测结果（板面是TOP/BOT）
        file_name = f'{device_name}_{pcb_sn}_{operation_num[4:]}_{pcb_final_result}.xml'
        file_path = os.path.join(aoi_path, file_name)
        ret = self._save_xml_to_file(save_xml, file_path)
        return ret

    def _generate_review_xml(self, data_vo, board_side_product: str = "0"):
        review_path = data_vo.get_value_by_cons_key('review_path')
        device_name = data_vo.get_value_by_cons_key('device_name')
        if not review_path:
            return x_response('false', 'The review xml save path is empty.  Please select it first')

        pcb_entity = data_vo.pcb_entity
        pcb_sn = pcb_entity.pcb_barcode
        operation_num = "TOP" if board_side_product == "0" else "BOT"
        # 2025-03-11T05:54:21
        inspect_time_1 = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT6)

        # 客户反馈复判表示板已操作结束，文件名和StatusCode都必须为PASS这样才能正常过站
        final_result = 'PASS'
        pcb_param = {
            'operation_num': "53" if board_side_product == "0" else "54",
            'inspect_time': inspect_time_1,
            'barcode': pcb_sn,
            'final_result': final_result
        }

        unit = self.unit_pass_template.format(**pcb_param)
        pcb_param['units'] = unit
        save_xml = self.save_xml_template.format(**pcb_param)
        # 文件命名方式:设备名称_整板条码_REPARACION_板面_板卡复判结果（板面是TOP/BOT）
        file_name = f'{device_name}_{pcb_sn}_REPARACION_{operation_num}_{final_result}.xml'
        file_path = os.path.join(review_path, file_name)
        ret = self._save_xml_to_file(save_xml, file_path)
        return ret

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)
        inspect_type = pcb_entity.get_insect_mes_type()

        t_review_path = pcb_entity.get_pcb_pcb_t_review_path()
        board_side_product = parse_board_side(t_review_path)  # 0,1

        if inspect_type == 'inspector':
            ret = self._generate_aoi_xml(data_vo, board_side_product)
        else:
            ret = self._generate_review_xml(data_vo, board_side_product)

        return ret
