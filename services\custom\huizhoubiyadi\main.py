# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/4/7 下午2:33
# Author     ：sch
# version    ：python 3.8
# Description：惠州比亚迪  2楼
"""
import json
from typing import Any

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine


def xx_request(api_url: str, req_param: dict) -> dict:
    ret_str1 = xrequest.RequestUtil.post_form(api_url, req_param, to_json=False)
    ret_data1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1).text
    return json.loads(ret_data1)


csv_pcb_panel_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
拼板检测NG数量,{pcb_board_robot_ng_number}
拼板复判NG数量,{pcb_board_user_ng_number}
拼板误报数量,{pcb_board_repass_number}
器件总数,{pcb_comp_number}
器件检测NG总数,{pcb_comp_robot_ng_number}
器件复判NG总数,{pcb_comp_user_ng_number}
器件误报总数,{pcb_comp_repass_number}

拼板号,拼板条码,拼板检测结果,拼板复判结果,拼板最终结果,器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_panel_template = """
{board_no},{board_sn},{board_robot_result},{board_user_result},{board_final_result},{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""


class Engine(ErrorMapEngine):
    version = {
        "title": "huizhoubiyadi release v1.0.0.6",
        "device": "AIS40X",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-04-08 15:08  条码校验，上传数据
date: 2024-04-10 09:20  部分参数修改
date: 2024-04-10 11:03  bugfix：csv文件
date: 2024-04-10 11:56  上传csv成功后也需要调用AddTestLogInfo接口
date: 2024-04-10 16:01  兼容AddTestLogInfo接口的返回值
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "条码校验接口URL",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/CheckSNCanBeTest",
        },
        "api_url_data1": {
            "ui_name": "物料绑定接口URL",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/bindNoneBomData",
        },
        "api_url_data2": {
            "ui_name": "测试数据上传接口URL",
            "value": "http://**************:28820/blaketestws/BlakeTestService.asmx/uploadTestData",
        },
        "api_url_data3": {
            "ui_name": "上传记录接口URL",
            "value": "http://**********:20020/MES2/Service.action",
        },
        "ftp_host1": {
            "ui_name": "FTP Host1",
            "value": "127.0.0.1"
        },
        "ftp_port1": {
            "ui_name": "FTP Port1",
            "value": "21"
        },
        "ftp_user1": {
            "ui_name": "FTP 账号1",
            "value": "sch"
        },
        "ftp_password1": {
            "ui_name": "FTP 密码1",
            "value": "123456"
        },
        "ftp_path1": {
            "ui_name": "FTP 路径1",
            "value": "/logfile/MACHINE"
        },
        "ftp_host2": {
            "ui_name": "FTP Host2",
            "value": "127.0.0.1"
        },
        "ftp_port2": {
            "ui_name": "FTP Port2",
            "value": "21"
        },
        "ftp_user2": {
            "ui_name": "FTP 账号2",
            "value": "sch"
        },
        "ftp_password2": {
            "ui_name": "FTP 密码2",
            "value": "123456"
        },
        "ftp_path2": {
            "ui_name": "FTP 路径2",
            "value": "/logfile/MACHINE"
        },
    }

    form = {
        "line_id": {
            "ui_name": "线别",
            "value": "",
        },
        "station_id": {
            "ui_name": "工站",
            "value": "",
        },
        "fixture_no": {
            "ui_name": "fixtureNo",
            "value": "",
        },
        "operator": {
            "ui_name": "operator",
            "value": "",
        },
        "machine_no": {
            "ui_name": "machineNo",
            "value": "",
        },
    }

    password_style = [
        "ftp_password2", "ftp_password1"
    ]

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        station_id = other_vo.get_value_by_cons_key("station_id")
        line_id = other_vo.get_value_by_cons_key("line_id")
        fixture_no = other_vo.get_value_by_cons_key("fixture_no")
        operator = other_vo.get_value_by_cons_key("operator")

        ret_res = self.x_response()
        for sn in other_vo.list_sn():
            check_param = {
                "sn": sn,
                "processName": station_id,
                "processData": json.dumps({
                    "line": line_id,
                    "fixtureNo": fixture_no,
                    "operator": operator,
                }, ensure_ascii=False)
            }

            ret = xx_request(api_url_check, check_param)
            if ret.get('Result') != 'PASS':
                ret_res = self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('Message')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data1 = data_vo.get_value_by_cons_key("api_url_data1")
        api_url_data2 = data_vo.get_value_by_cons_key("api_url_data2")
        api_url_data3 = data_vo.get_value_by_cons_key("api_url_data3")
        ftp_host1 = data_vo.get_value_by_cons_key("ftp_host1")
        ftp_port1 = data_vo.get_value_by_cons_key("ftp_port1", to_int=True)
        ftp_user1 = data_vo.get_value_by_cons_key("ftp_user1")
        ftp_password1 = data_vo.get_value_by_cons_key("ftp_password1")
        ftp_path1 = data_vo.get_value_by_cons_key("ftp_path1")
        ftp_host2 = data_vo.get_value_by_cons_key("ftp_host2")
        ftp_port2 = data_vo.get_value_by_cons_key("ftp_port2", to_int=True)
        ftp_user2 = data_vo.get_value_by_cons_key("ftp_user2")
        ftp_password2 = data_vo.get_value_by_cons_key("ftp_password2")
        ftp_path2 = data_vo.get_value_by_cons_key("ftp_path2")

        station_id = data_vo.get_value_by_cons_key("station_id")
        line_id = data_vo.get_value_by_cons_key("line_id")
        fixture_no = data_vo.get_value_by_cons_key("fixture_no")
        operator = data_vo.get_value_by_cons_key("operator")
        machine_no = data_vo.get_value_by_cons_key("machine_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_sn = pcb_entity.pcb_barcode
        start_1 = pcb_entity.get_start_time()

        start_time = start_1.strftime(xcons.FMT_TIME_DEFAULT)
        time_file = start_1.strftime(xcons.FMT_TIME_DEFAULT7)
        time_file1 = start_1.strftime(xcons.FMT_TIME_FILE)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        all_pcb_image = pcb_entity.list_all_pcb_image()

        comp_ng_data = {}
        ng_code = "PASS"
        comp_ng_data2 = []

        ret_res = self.x_response()

        pcb_result = pcb_entity.get_repair_result("PASS", "FAIL")

        ftp_client1 = FTPClient(ftp_host1, ftp_user1, ftp_password1, ftp_port1)
        ftp_client1.login()

        ftp_client2 = FTPClient(ftp_host2, ftp_user2, ftp_password2, ftp_port2)
        ftp_client2.login()

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        only_one_comp = ""
        comp_data_str = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not pcb_sn and barcode:
                pcb_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                comp_tag = comp_entity.designator
                if not only_one_comp:
                    only_one_comp = comp_tag

                if comp_entity.is_repair_ng():
                    comp_ng_data[comp_tag] = comp_entity.repair_ng_str

                    if ng_code == "PASS":
                        ng_code = comp_entity.repair_ng_code

                    if not comp_ng_data2:
                        comp_ng_data2.append({
                            "name": comp_tag,
                            "value": comp_entity.repair_ng_code
                        })

                comp_data_str += csv_comp_panel_template.format(**{
                    "board_sn": barcode,
                    "board_no": board_no,
                    "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                    "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                    "board_final_result": board_entity.get_final_result(),
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            # if not cd_ftp_path:
            #     ftp_client1.cd_or_mkdir(f"{ftp_path1}/BYDMESLOG/{time_file}")
            #     ftp_client2.cd_or_mkdir(f"{ftp_path2}/BYDMESLOG/{time_file}")
            #     cd_ftp_path = True
            #
            # board_result = board_entity.get_repair_result("PASS", "FAIL")
            # filename = f"BYD_PN_{barcode}_{board_result}_{station_id}_{time_file1}.jpg"
            #
            # ftp_client1.upload_content(filename, board_data)
            # ftp_client2.upload_content(filename, board_data)

        self.log.info(f"1.调用物料不良绑定接口...")

        if not comp_ng_data:
            comp_ng_data[only_one_comp] = "OK"

        data_param1 = {
            "sn": pcb_sn,
            "processData": json.dumps(comp_ng_data, ensure_ascii=False)
        }

        ret1 = xx_request(api_url_data1, data_param1)
        if ret1.get('Result') != 'PASS':
            ret_res = self.x_response("false", f"mes接口异常，物料绑定失败，error：{ret1.get('Message')}")

        self.log.info(f"2.测试数据上传及过站...")
        data_param2 = {
            "sn": pcb_sn,
            "processName": station_id,
            "processData": json.dumps({
                "projectName": pcb_entity.project_name,
                "line": line_id,
                "fixtureNo": fixture_no,
                "machineNo": machine_no,
                "operator": operator,
                "logfilePath": "123",
                "testResult": pcb_entity.get_repair_result("PASS", "FAIL"),
                "startTime": start_time,
                "stopTime": end_time,
                "ncCode": ng_code,
                "testDataList": comp_ng_data2
            }, ensure_ascii=False)
        }
        ret1 = xx_request(api_url_data2, data_param2)
        if ret1.get('Result') != 'PASS':
            ret_res = self.x_response("false", f"mes接口异常，测试数据上传及过站失败，error：{ret1.get('Message')}")

        self.log.info(f"3.开始上传csv文件...")
        pcb_data = {
            "device_name": machine_no,
            "pcb_sn": pcb_sn,
            "pcb_track_line": pcb_entity.track_index,
            "pcb_board_side": pcb_entity.board_side,
            "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
            "pcb_cycle_time": pcb_entity.get_cycle_time(),
            "pcb_project_name": pcb_entity.project_name,
            "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
            "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
            "pcb_final_result": pcb_entity.get_final_result(),
            "pcb_repair_user": pcb_entity.repair_user,
            "pcb_board_number": pcb_entity.board_count,
            "pcb_board_robot_ng_number": pcb_board_robot_ng_number,
            "pcb_board_user_ng_number": pcb_board_user_ng_number,
            "pcb_board_repass_number": pcb_board_robot_ng_number - pcb_board_user_ng_number,  # 误报数量
            "pcb_comp_number": pcb_entity.comp_count,
            "pcb_comp_robot_ng_number": pcb_comp_robot_ng_number,
            "pcb_comp_user_ng_number": pcb_comp_user_ng_number,
            "pcb_comp_repass_number": pcb_comp_robot_ng_number - pcb_comp_user_ng_number,  # 误报数量
            "CompData": comp_data_str
        }

        pcb_content = csv_pcb_panel_template.format(**pcb_data)
        ftp_client1.cd_or_mkdir(f"{ftp_path1}/BYDMESLOG/{time_file}")
        ftp_client2.cd_or_mkdir(f"{ftp_path2}/BYDMESLOG/{time_file}")

        filename = f"BYD_PN_{pcb_sn}_{pcb_result}_{station_id}_{time_file1}.csv"
        ftp_client1.upload_content(filename, pcb_content)
        ftp_client2.upload_content(filename, pcb_content)

        self.log.info(f"5. 接口发送上传记录...")
        data_param3 = {
            "LOGIN_ID": -1,
            "CLIENT_ID": 1,
            "DATA": {
                "SFC": pcb_sn,
                "LOG_PATH": f"/MACHINE/BYDMESLOG/{time_file}/{filename}",
                "RESULT": pcb_entity.get_repair_result(1, 0),
                "STATION": station_id,
                "TIME": start_time,
                "LINE": line_id,
                "EQ_NO": "LOG",
                "FIXTURE_NO": fixture_no,
            }
        }

        ret5 = xrequest.RequestUtil.get(api_url_data3, {
            'method': 'AddTestLogInfo',
            'param': json.dumps(
                data_param3, ensure_ascii=False, separators=(",", ":")
            )}, to_json=True)
        if ret5.get('RESULT') != 'PASS':
            ret_res = self.x_response("false", f"mes接口异常，AddTestLogInfo接口调用失败，error：{ret5.get('MESSAGE')}")

        self.log.info(f"4.开始上传整板图...")
        self.log.info(f"pcb image len: {len(all_pcb_image)}")
        if len(all_pcb_image) >= 1:
            src_img1 = all_pcb_image[0]
            dst_filename = f"BYD_PN_{pcb_sn}_{pcb_result}_{station_id}_{time_file1}.jpg"

            ftp_client1.cd_or_mkdir(f"{ftp_path1}/BYDIMGLOG/{time_file}")
            ftp_client2.cd_or_mkdir(f"{ftp_path2}/BYDIMGLOG/{time_file}")

            ftp_client1.upload_file(src_img1, dst_filename)
            ftp_client2.upload_file(src_img1, dst_filename)

            self.log.info(f"5. 接口发送上传记录...")
            data_param3 = {
                "LOGIN_ID": -1,
                "CLIENT_ID": 1,
                "DATA": {
                    "SFC": pcb_sn,
                    "LOG_PATH": f"/MACHINE/BYDIMGLOG/{time_file}/{dst_filename}",
                    "RESULT": pcb_entity.get_repair_result(1, 0),
                    "STATION": station_id,
                    "TIME": start_time,
                    "LINE": line_id,
                    "EQ_NO": "IMG",
                    "FIXTURE_NO": fixture_no,
                }
            }

            ret5 = xrequest.RequestUtil.get(api_url_data3, {
                'method': 'AddTestLogInfo',
                'param': json.dumps(
                    data_param3, ensure_ascii=False, separators=(",", ":")
                )}, to_json=True)

            if ret5.get('RESULT') != 'PASS':
                ret_res = self.x_response("false", f"mes接口异常，AddTestLogInfo接口调用失败，error：{ret5.get('MESSAGE')}")

        else:
            self.log.warning(f"找不到需要上传的整板图！！！")

        ftp_client1.close()
        ftp_client2.close()

        return ret_res


if __name__ == '__main__':
    # ret_str1 = """<string xmlns="http://tempuri.org/">{"Result":"FAIL","Code":0,"Message":"NGNG,CheckSNCanBeTest接口异常:线体锁不允许测试!","DATA":null}</string>"""
    # ret_1 = xutil.XmlUtil.get_xml_root_by_str(ret_str1).text
    # ret = json.loads(ret_1)
    # print(ret)
    pass
