# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/10/18 下午3:36
# Author     ：sch
# version    ：python 3.8
# Description：越南富士康
"""
import json
import time
from typing import Any

from common import xutil, xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine

get_data_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:aoi="http://schemas.datacontract.org/2004/07/AOI_API.WebService.Models" xmlns:arr="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:getData>
         <tem:param>
            <!--Optional:-->
            <aoi:panelSN>
               <!--Zero or more repetitions:-->
               <arr:string>{panel_sn}</arr:string>
            </aoi:panelSN>
            <!--Optional:-->
            <aoi:random>{random}</aoi:random>
            <!--Optional:-->
            <aoi:timestamp>{timestamp}</aoi:timestamp>
         </tem:param>
      </tem:getData>
   </soapenv:Body>
</soapenv:Envelope>"""

upload_data_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:aoi="http://schemas.datacontract.org/2004/07/AOI_API.WebService.Models" xmlns:aoi1="http://schemas.datacontract.org/2004/07/AOI_API.Core.Model">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:uploadData>
         <tem:name>
            <!--Optional:-->
            <aoi:data>
               <!--Zero or more repetitions:-->
               <aoi1:DtoUploadDataAOIData>
                  <!--Optional:-->
                  <aoi1:aoiPoint>{aoiPoint}</aoi1:aoiPoint>
                  <!--Optional:-->
                  <aoi1:aoiType>{aoiType}</aoi1:aoiType>
                  <!--Optional:-->
                  <aoi1:line>{line}</aoi1:line>
                  <!--Optional:-->
                  <aoi1:machine>{machine}</aoi1:machine>
                  <!--Optional:-->
                  <aoi1:opPoint>{opPoint}</aoi1:opPoint>
                  <!--Optional:-->
                  <aoi1:pcbSN>{pcbSN}</aoi1:pcbSN>
                  <!--Optional:-->
                  <aoi1:totalPoint>{totalPoint}</aoi1:totalPoint>
               </aoi1:DtoUploadDataAOIData>
            </aoi:data>
            <!--Optional:-->
            <aoi:random>{random}</aoi:random>
            <!--Optional:-->
            <aoi:timestamp>{timestamp}</aoi:timestamp>
         </tem:name>
      </tem:uploadData>
   </soapenv:Body>
</soapenv:Envelope>
"""

txt_template = """[Project]
Module={Module}
InspectDate={InspectDate}
Line Name={Line Name}
Station Name={Station Name}
Machine Name={Machine Name}
Cycle Time={Cycle Time}
Total Boards={Total Boards}{board_data_str}
"""

board_template = """
[Board_{board_no_flag}]
PanelID=1
BoardID={BoardID}
Barcode={Barcode}
Skipped={Skipped}
N Components={N Components}
N Inspected Components={N Inspected Components}
AOI Result={AOI Result}
OP Result={OP Result}
N Defect Parts={N Defect Parts}{comp_data_str}"""


class Engine(ErrorMapEngine):
    version = {
        "customer": ["越南富士康", "yuenanfushikang"],
        "version": "release v1.0.0.11",
        "device": "203-hw",
        "feature": ["从mes获取条码", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-10-18 15:34  从mes获取条码，上传数据
date: 2024-10-25 10:59  修改请求参数
date: 2024-10-25 10:59  修改请求参数 1
date: 2024-10-26 12:00  修改请求参数 2
date: 2024-10-28 19:42  修改请求参数 3
date: 2024-11-21 17:09  输出NG器件+输出txt文档
date: 2025-01-02 17:26  当一个整板全部板都是复判OK了才发送mes
date: 2025-01-13 18:23  整版复判OK发送mes同时保持本地生成txt逻辑不变
date: 2025-04-22 16:30  totalPoint和aoiPoint这两个字段互换
date: 2025-05-08 17:58  jira:ATAOI_2019-33842，修改aoiType为拼版复判结果
date: 2025-06-06 14:55  jira:ATAOI_2019-33842，去掉逻辑：当一个整板全部板都是复判OK了才发送mes
""", }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "http://127.0.0.1:8081/api/center.asmx",
        },
        "api_url_upload_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/api/center.asmx",
        },
    }

    form = {
        "line": {
            "ui_name": "线别",
            "ui_name_en": "Line",
            "value": "AOI",
        },
        "machine": {
            "ui_name": "机器名称",
            "ui_name_en": "Machine",
            "value": "",
        },
        # "aoi_type": {
        #     "ui_name": "aoiType",
        #     "ui_name_en": "aoiType",
        #     "value": "",
        # },

        "station_name": {
            "ui_name": "设备/机台名称(txt)",
            "ui_name_en": "StationName(txt)",
            "value": "",
        },
        "machine_name": {
            "ui_name": "机器型号(txt)",
            "ui_name_en": "MachineName(txt)",
            "value": "",
        },
    }

    path = {
        "comp_ng_img_path": {
            "ui_name": "NG器件图保存路径",
            "ui_name_ui": "CompNGImageSavePath",
            "value": "",
        },
        "txt_save_path": {
            "ui_name": "txt文档保存路径",
            "ui_name_ui": "txtSavePath",
            "value": "",
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")

        pcb_sn = other_vo.get_pcb_sn()

        req_param = get_data_template.format(**{
            "panel_sn": pcb_sn,
            "random": xutil.OtherUtil.get_origin_uuid4_str(),
            "timestamp": int(time.time())
        })

        ret_str = xrequest.RequestUtil.post_xml(api_url_get_sn, req_param)

        root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str)
        ret_str = root1[0][0][0].text
        ret_json = json.loads(ret_str)

        if not ret_json.get('Succeeded'):
            return self.x_response("false", f"mes接口异常，error：{ret_json.get('Errors')}")

        ret_data = ret_json.get('Data', [])

        if not ret_data:
            return self.x_response("false", f"mes接口异常，未获取到pcbSN！")

        ret0 = ret_data[0]

        pcb_sn_list = ret0.get("pcbSN")

        if not pcb_sn_list:
            return self.x_response("false", f"mes接口异常，未获取到pcbSN！")

        return self.x_response("true", ",".join(pcb_sn_list))

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_upload_data = data_vo.get_value_by_cons_key("api_url_upload_data")
        line = data_vo.get_value_by_cons_key("line")
        machine = data_vo.get_value_by_cons_key("machine")
        # aoi_type = data_vo.get_value_by_cons_key("aoi_type")

        comp_ng_img_path = data_vo.get_value_by_cons_key("comp_ng_img_path", not_null=True)
        station_name = data_vo.get_value_by_cons_key("station_name")
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        txt_save_path = data_vo.get_value_by_cons_key("txt_save_path", not_null=True)

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        file_date = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)

        full_path = f"{comp_ng_img_path}/{file_date}"
        xutil.FileUtil.ensure_dir_exist(full_path)
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        date_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)

        # final_result = pcb_entity.get_final_result()

        # if final_result == "NG":
        #     self.log.warning(f"整板全部板都是复判OK，本次不发送！")
        #     return self.x_response()

        x_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_ng_str = ""

            comp_ix = 0

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_src_img = comp_entity.image_path
                    comp_tag = comp_entity.designator
                    robot_ng_str = comp_entity.robot_ng_str
                    repair_ng_code = comp_entity.repair_ng_code

                    if repair_ng_code == "0":
                        repair_ng_code = "OK"

                    if comp_src_img:
                        comp_result = comp_entity.get_final_result("P", "P", "F")
                        dst_filename = f"{comp_result}-{station_name}-{barcode}-{comp_tag}-{robot_ng_str}-{repair_ng_code}.png"
                        dst_filepath = f"{full_path}/{dst_filename}"
                        xutil.FileUtil.copy_file(comp_src_img, dst_filepath)

                    comp_ix += 1
                    comp_part = comp_entity.part
                    repair_ng_str = comp_entity.repair_ng_str
                    comp_ng_str += f"\nNG Part {str(comp_ix).zfill(4)}={comp_tag}_{comp_part}_{repair_ng_str}"

            upload_data_xml = upload_data_template.format(**{
                "aoiPoint": board_entity.comp_robot_ng_number,
                "totalPoint": board_entity.comp_total_number,
                "aoiType": board_entity.get_repair_result("P", "F"),
                "line": line,
                "machine": machine,
                "opPoint": board_entity.comp_repair_ng_number,
                "pcbSN": barcode,
                "random": xutil.OtherUtil.get_origin_uuid4_str(),
                "timestamp": int(time.time())
            })

            # if final_result != "NG":
            # pass,repass
            ret_str2 = xrequest.RequestUtil.post_xml(api_url_upload_data, upload_data_xml)
            root2 = xutil.XmlUtil.get_xml_root_by_str(ret_str2)
            ret_str = root2[0][0][0].text
            ret_json = json.loads(ret_str)

            if not ret_json.get('Succeeded'):
                x_res = self.x_response("false", f"mes接口异常，error：{ret_json.get('Errors')}")

            board_no_1 = board_no.zfill(3)

            board_skipped = "Yes" if board_entity.get_final_result() == "BadBoard" else "No"

            board_data = board_template.format(**{
                "board_no_flag": board_no_1,
                "BoardID": board_no,
                "Barcode": barcode,
                "Skipped": board_skipped,
                "N Components": board_entity.comp_total_number,
                "N Inspected Components": board_entity.comp_total_number,
                "AOI Result": board_entity.get_robot_result("OK", "NG"),
                "OP Result": board_entity.get_repair_result("OK", "NG"),
                "N Defect Parts": board_entity.comp_repair_ng_number,
                "comp_data_str": comp_ng_str
            })

            txt_content = txt_template.format(**{
                "Module": pcb_entity.project_name,
                "InspectDate": time_file,
                "Cycle Time": pcb_entity.get_cycle_time(),
                "Line Name": line,
                "Station Name": station_name,
                "Machine Name": machine_name,
                "Total Boards": pcb_entity.board_count,
                "board_data_str": board_data
            })

            full_path_txt = f"{txt_save_path}/{line}/{date_file}"
            xutil.FileUtil.ensure_dir_exist(full_path_txt)

            board_result = board_entity.get_repair_result("P", "F")
            comp_number1 = board_entity.comp_total_number
            comp_number2 = board_entity.comp_robot_ng_number
            comp_number3 = board_entity.comp_repair_ng_number
            txt_filepath = f"{full_path_txt}/{board_result}-{line}-{barcode}-{comp_number1}-{comp_number2}-{comp_number3}-AOI.txt"
            xutil.FileUtil.write_content_to_file_pro(txt_filepath, txt_content)

        return x_res


if __name__ == '__main__':
    #     ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    #     <s:Body>
    #         <getDataResponse xmlns="http://tempuri.org/">
    #             <getDataResult>{"Succeeded":true,"Errors":"","StatusCode":"200","Data":[{"panelSN":"PALM-W-00228-003","pcbSN":["X170BMA","X170BM8","X170BNC","X170BMZ","X170BN9","X170BND"]}]}</getDataResult>
    #         </getDataResponse>
    #     </s:Body>
    # </s:Envelope>"""

    #     ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    #     <s:Body>
    #         <uploadDataResponse xmlns="http://tempuri.org/">
    #             <uploadDataResult>{"Succeeded":false,"Errors":"ORA-00936: missing expression;传入的参数:{\"timestamp\":\"1730087995\",\"random\":\"sdfsd\",\"data\":[{\"pcbSN\":\"X170BMA\",\"line\":\"PTH1\",\"aoiType\":\"AOI\",\"totalPoint\":\"100\",\"aoiPoint\":\"100\",\"opPoint\":\"10\",\"machine\":\"AOI\"}]}","StatusCode":"400","Data":null}</uploadDataResult>
    #         </uploadDataResponse>
    #     </s:Body>
    # </s:Envelope>"""
    #
    #     root1 = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    #     ret_str = root1[0][0][0].text
    #     ret_json = json.loads(ret_str)
    #
    #     print(ret_json)

    pass
