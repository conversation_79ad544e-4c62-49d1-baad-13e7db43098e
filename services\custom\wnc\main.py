# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/22 上午11:34
# Author     ：sch
# version    ：python 3.8
# Description：wnc
"""
import json
from typing import Any

from common import xcons, xutil
from vo.mes_vo import DataVo
from engine.MesEngine import BaseEngine

repair_template = """{barcode}
{comp_repair_content}
{comp_robot_ng_count}
"""

AIS_30X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "不合格", "custom_code": "1", "custom_str": "NG"},
    "2": {"standard": "多锡", "custom_code": "2", "custom_str": "ExcessSolder"},
    "3": {"standard": "连锡", "custom_code": "3", "custom_str": "Bridge"},
    "4": {"standard": "少锡", "custom_code": "4", "custom_str": "InsufficientSolder"},
    "5": {"standard": "孔洞", "custom_code": "5", "custom_str": "PinHole"},
    "6": {"standard": "未出脚", "custom_code": "6", "custom_str": "NoPin"},
    "7": {"standard": "异常出脚", "custom_code": "7", "custom_str": "ExceptionPin"},
    "8": {"standard": "缺件", "custom_code": "8", "custom_str": "MissingPart"},
    "9": {"standard": "偏位", "custom_code": "9", "custom_str": "ShiftPart"},
    "10": {"standard": "露铜", "custom_code": "10", "custom_str": "ExposeCopper"},
    "11": {"standard": "错件", "custom_code": "11", "custom_str": "WrongPart"},
    "12": {"standard": "极性错误", "custom_code": "12", "custom_str": "ReversePart"},
    "13": {"standard": "条码识别错误", "custom_code": "13", "custom_str": "BarcodeRecognition"},
    "14": {"standard": "数据错误", "custom_code": "14", "custom_str": "CountError"},
    "15": {"standard": "定位错误", "custom_code": "15", "custom_str": "Locate"},
    "16": {"standard": "流程错误", "custom_code": "16", "custom_str": "ProcessError"},
    "17": {"standard": "锡珠", "custom_code": "17", "custom_str": "SolderBall"},
    "18": {"standard": "拼版特征不匹配", "custom_code": "18", "custom_str": "FeatureMismatch"},
}


class Engine(BaseEngine):
    version = {
        "title": "wnc release v1.0.0.6",
        "device": "303",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-22 11:37  init
date: 2023-05-29 17:02  检测完后生成的json文件不需要格式化
date: 2023-12-19 18:24  分拼版上传
date: 2023-12-21 9:15  修复通用模块bug
date: 2024-01-23 15:05  优化直通的数据的数据格式
""", }

    form = {
        "machine_name": {
            "ui_name": "机台名称",
            "value": "",
        },
        "line": {
            "ui_name": "线别",
            "value": "",
        },
        "user_id": {
            "ui_name": "员工账号",
            "value": ""
        }
    }

    path = {
        "inspector_save_path": {
            "ui_name": "检测完保存路径",
            "value": ""
        },
        "repair_save_path": {
            "ui_name": "复判完保存路径",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        machine_name = data_vo.get_value_by_cons_key("machine_name")
        line = data_vo.get_value_by_cons_key("line")
        user_id = data_vo.get_value_by_cons_key("user_id")
        inspector_save_path = data_vo.get_value_by_cons_key("inspector_save_path")
        repair_save_path = data_vo.get_value_by_cons_key("repair_save_path")

        inspect_type = other_data.get("inspect_type")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # pcb_sn = pcb_entity.pcb_barcode
        bad_info_map = pcb_entity.dict_t_board_info()

        time_now = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)
        # if not pcb_sn:
        #     pcb_sn = time_now

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            board_ng_count = 0
            board_ok_count = 0
            board_bad_count = 0

            comp_total_count = 0
            comp_ng_count = 0

            comp_ng_list = []
            comp_ng_list_repair = []

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            if not barcode:
                barcode = time_now

            comp_total_count += board_entity.comp_total_number
            comp_ng_count += board_entity.comp_robot_ng_number

            bad_info = bad_info_map.get(board_no)

            is_bad_board = False

            if bad_info.get('bad_board') != '0':
                is_bad_board = True

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.robot_ng_code == '25':
                    is_bad_board = True

                if not comp_entity.robot_result:
                    # 检测NG
                    comp_robot_ng_code = comp_entity.robot_ng_code

                    # comp_robot_ng_str = comp_entity.robot_ng_str
                    comp_robot_ng_str = AIS_30X_ERROR_MAP.get(comp_robot_ng_code, {}).get("custom_str", "99")

                    comp_ng_list.append({
                        "MachineName": machine_name,
                        "Mbsn": barcode,
                        "CbSeq": 1,
                        "Location": comp_entity.designator,
                        "ComponentType": comp_entity.part,
                        "ConfirmErrCode": comp_robot_ng_str,
                        "Side": pcb_entity.board_side
                    })

                    repair_result = "OK" if comp_entity.repair_result else "NG"
                    error_code = comp_entity.repair_ng_code
                    comp_tag = comp_entity.designator

                    repair_ng_content = f"{repair_result},{error_code},{comp_tag},{comp_robot_ng_str}"
                    comp_ng_list_repair.append(repair_ng_content)

            if is_bad_board:
                board_bad_count += 1
            else:
                if board_entity.repair_result:
                    board_ok_count += 1
                else:
                    board_ng_count += 1

            # if not pcb_sn and barcode:
            #     pcb_sn = barcode

            pcb_param = {
                "MachineName": machine_name,
                "Line": line,
                "ProgramName": pcb_entity.project_name,
                "Mbsn": barcode,
                "DetectResult": 1,
                "DetectDate": time_now[:8],
                "DetectTime": time_now[8:],
                "UserId": user_id,
                "Side": pcb_entity.board_side,
                "CbCnt": pcb_entity.board_count,
                "CbPassCnt": board_ok_count,
                "CbNgCnt": board_ng_count,
                "SkipCnt": board_bad_count,
                "TotalComponent": comp_total_count,
                "TotalError": comp_ng_count,
                "smtMachinesErrDetailList": comp_ng_list
            }

            if inspect_type == xcons.INSPECTOR:
                # 检测完发送
                file_path = f"{inspector_save_path}/JSON_JSON_EQP-AOI_{time_now}.txt"
                # xutil.FileUtil.dump_json_to_file(file_path, pcb_param)
                with open(file_path, "w") as f:
                    json.dump(pcb_param, f, ensure_ascii=False, separators=(",", ':'))

                self.log.info(f">>文件已保存成功： {file_path}")
            else:
                # 复判完发送
                file_path = f"{repair_save_path}/SFCS_MAC_{barcode}.txt"
                comp_repair_content = ";".join(comp_ng_list_repair)

                if comp_repair_content:
                    comp_repair_content = f"{comp_repair_content};"

                if not comp_ng_list_repair:
                    comp_repair_content = "OK"
                    comp_ng_count = board_entity.comp_total_number

                repair_content = repair_template.format(**{
                    "barcode": barcode,
                    "comp_repair_content": comp_repair_content,
                    "comp_robot_ng_count": comp_ng_count
                })
                xutil.FileUtil.write_content_to_file(file_path, repair_content)

        return self.x_response()
