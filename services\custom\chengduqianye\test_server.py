import random

from flask import Flask, request, jsonify

app = Flask(__name__)


@app.route('/hra/v1/staff_info/query', methods=['POST'])
def account():
    data = request.get_json()
    print(f'请求参数为:{data}')

    id = random.randint(1000, 9999)

    response_ok = {
        "success": True,
        "result": {
            "data": [{
                "id": id,
                },
            ],

        }
    }

    response_no = {
        "success": False,
        "message": "账号不存在系统中"
    }

    return jsonify(response_ok)

@app.route('/mes/v1/workstation/query', methods=['POST'])
def workstation():
    data = request.get_json()
    print(f'请求参数为:{data}')

    response_ok = {
        "success": True,
        "result": {
            "data": [{
                "name": "工站1",
            },
                {
                    "name": "工站2",
                },
                {
                    "name": "工站3",
                },
            ],

        }
    }

    response_no = {
        "success": False,
        "message": "暂无没有可用工站"
    }

    return jsonify(response_ok)


@app.route('/api/product_order_task/query', methods=['POST'])
def task():
    data = request.get_json()
    print(f'请求参数为:{data}')

    response_ok = {
        "success": True,
        "result": {
            "data": [{
                "base": {
                    "id": "27",
                    "material_name": "黑色版三合一-PCB-001",
                    "material_code": "B-A016-0021-001",
                    "specification": "QYHJ_PCB_2312_主板PCB_001",
                },
                "id": "161",
                "process_name": "印刷工序001",
                "suggest_number": "50",
                "task_code": "task_001",
                "workstation_id": "1"
            }, {
                "base": {
                    "id": "28",
                    "material_name": "黑色版三合一-PCB-002",
                    "material_code": "B-A016-0021-002",
                    "specification": "QYHJ_PCB_2312_主板PCB_002",
                },
                "id": "162",
                "process_name": "印刷工序002",
                "suggest_number": "70",
                "task_code": "task_002",
                "workstation_id": "2"
            }, {
                "base": {
                    "id": "29",
                    "material_name": "黑色版三合一-PCB-003",
                    "material_code": "B-A016-0021-003",
                    "specification": "QYHJ_PCB_2312_主板PCB_003",
                },
                "id": "162",
                "process_name": "印刷工序003",
                "suggest_number": "90",
                "task_code": "task_003",
                "workstation_id": "3"
            },
            ],
        }
    }

    response_no = {
        "success": False,
        "message": "暂无没有可用任务"
    }

    return jsonify(response_ok)

@app.route('/api/scanReport/open', methods=['POST'])
def scanReport():
    data = request.get_json()
    print(f'请求参数为:{data}')

    response_ok = {
        "success": True,
        "message": "数据错误，账号不存在"
    }

    response_no = {
        "success": False,
        "message": "数据异常"
    }

    return jsonify(response_no)



if __name__ == '__main__':
    app.run()  # 运行app，默认端口为5000
