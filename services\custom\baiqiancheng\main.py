# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/8/30 下午4:21
# Author     ：sch
# version    ：python 3.8
# Description：百千成，从C++那边的脚本迁移过来

主要为了解决C++ Mes配置器卡死问题。
"""

from typing import Any

from common import xrequest, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

global_data = {}


class Engine(BaseEngine):
    version = {
        "title": "baiqiancheng release v1.0.0.3",
        "device": "203",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-08-30 16:24  init
date: 2023-08-30 16:46  从C++那边迁移过来
date: 2023-09-04 20:51  优化代码
""", }

    form = {
        "api_url": {
            "ui_name": "接口URL",
            "value": ""
        },
        "operator_id": {
            "ui_name": "员工工号",
            "value": ""
        },
        "order_id": {
            "ui_name": "工单号",
            "value": ""
        },
        "station_id": {
            "ui_name": "站位编号",
            "value": ""
        },
        "fixture_id": {
            "ui_name": "治具编号",
            "value": ""
        },
        "board_number": {
            "ui_name": "拼板数量",
            "value": ""
        },
    }

    combo = {
        "device_type": {
            "ui_name": "设备类型",
            "item": ["AIS20X", "AIS40X"],
            "value": "AIS40X"
        },
        "re_test": {
            "ui_name": "是否允许重复测试",
            "item": ["是", "否"],
            "value": "是"
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url = data_vo.get_value_by_cons_key("api_url")
        operator_id = data_vo.get_value_by_cons_key("operator_id")
        order_id = data_vo.get_value_by_cons_key("order_id")
        fixture_id = data_vo.get_value_by_cons_key("fixture_id")
        station_id = data_vo.get_value_by_cons_key("station_id")
        device_type = data_vo.get_value_by_cons_key("device_type")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        data_url = f"{api_url}/mrs/createRoute"

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            cache_sn_list = global_data.get("cache_sn_list", [])
            if barcode in cache_sn_list:
                cache_sn_map = global_data.get("cache_sn_map", {})

                error_msg = cache_sn_map.get(barcode, "")

                return self.x_response("false", f"该条码校验失败，error：{error_msg}，本次不上传数据到mes！")

            comp_ng_code = []
            comp_ng_str = []
            comp_ng_tag = []

            for comp_entity in board_entity.yield_comp_entity():

                if not comp_entity.repair_result:
                    repair_ng_code = comp_entity.repair_ng_code

                    comp_ng_code.append(f"{device_type}{repair_ng_code}")
                    comp_ng_tag.append(comp_entity.designator)

                    ng_str = comp_entity.repair_ng_str
                    custom_ng_str = xcons.AIS_40X_ERROR_MAP.get(comp_entity.repair_ng_code, {}).get("standard", ng_str)
                    comp_ng_str.append(custom_ng_str)

            data_param = {
                "pcbSeq": barcode,
                "prodNo": order_id,
                "stationNo": station_id,
                "result": "PASS" if board_entity.repair_result else "FAIL",
                "remark": "{ng_code}{ng_str}{ng_tag}"
                    .replace("ng_code", ",".join(comp_ng_code))
                    .replace("ng_str", ','.join(comp_ng_str))
                    .replace("ng_tag", ','.join(comp_ng_tag)),
                "testItem": "",
                "userNo": operator_id,
                "weight": 0,
                "packNo": "",
                "rmk1": fixture_id,
                "rmk2": "",
                "rmk3": "",
                "rmk4": "",
            }

            ret = xrequest.RequestUtil.get(data_url, data_param)

            if str(ret.get("msgId")) != "0":
                return self.x_response("false", f"接口响应异常, 上传数据失败, error: {ret.get('msgStr')}")

        return self.x_response()

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        board_number = other_vo.get_value_by_cons_key("board_number")
        try:
            board_number = int(board_number)
        except Exception as err:
            return self.x_response("false", f"拼板数量必须为数字，error：{err}")

        pcb_sn = other_vo.get_pcb_sn()
        self.log.info(f"使用条码：{pcb_sn} 去生成条码...")

        if board_number == 1:
            self.log.info(f"单拼板，直接返回原条码")
            return self.x_response("true", pcb_sn)

        if "_" in pcb_sn:
            pcb_sn = pcb_sn.rsplit("_", 1)[0]
            self.log.info(f"pcb sn 已切割: pcb sn: {pcb_sn}")

        sn_list = []
        for i in range(board_number):
            i += 1
            sn_list.append(f"{pcb_sn}_{i}")

        sn_str = ",".join(sn_list)
        return self.x_response("true", sn_str)

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url = other_vo.get_value_by_cons_key("api_url")
        station_id = other_vo.get_value_by_cons_key("station_id")
        order_id = other_vo.get_value_by_cons_key("order_id")
        re_test = other_vo.get_value_by_cons_key("re_test")

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            param = {
                "pcbSeq": sn,
                "prodNo": order_id,
                "stationNo": station_id,
                "retest": 1 if re_test == "是" else 0
            }

            check_url = f"{api_url}/mrs/checkRoute"
            ret = xrequest.RequestUtil.get(check_url, params=param)
            if str(ret.get("msgId")) != "0":
                if sn:
                    cache_sn_list = global_data.get("cache_sn_list", [])
                    cache_sn_map = global_data.get("cache_sn_map", {})

                    cache_sn_list.insert(0, sn)
                    cache_sn_map[sn] = ret.get('msgStr')

                    # 删除多余条码, 只缓存最近1000个条码
                    new_sn_list = cache_sn_list[:1000]
                    global_data["cache_sn_list"] = new_sn_list

                    tmp_list = list(cache_sn_map.keys())

                    for k in tmp_list:
                        if k not in new_sn_list:
                            del cache_sn_map[k]

                    global_data["cache_sn_map"] = cache_sn_map
                    self.log.info(f"条码已经缓存")

                return self.x_response("false", f"接口响应异常, 条码校验失败, error: {ret.get('msgStr')}")
            else:
                # 校验成功，需要把以前的记录清除
                cache_sn_list = global_data.get("cache_sn_list", [])
                cache_sn_map = global_data.get("cache_sn_map", {})

                if sn in cache_sn_list:
                    cache_sn_list.remove(sn)

                if sn in cache_sn_map:
                    del cache_sn_map[sn]

                global_data["cache_sn_list"] = cache_sn_list
                global_data["cache_sn_map"] = cache_sn_map

        return self.x_response()
