#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MES业务场景分析模块
专门识别和分析MES系统中的常见业务场景
"""

import re
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

try:
    from .log_parser import LogEntry
except ImportError:
    from log_parser import LogEntry


@dataclass
class BusinessScenario:
    """业务场景"""
    name: str
    description: str
    category: str
    severity: str
    count: int = 0
    examples: List[str] = None
    solutions: List[str] = None
    
    def __post_init__(self):
        if self.examples is None:
            self.examples = []
        if self.solutions is None:
            self.solutions = []


class BusinessAnalyzer:
    """MES业务场景分析器"""
    
    def __init__(self):
        self.business_patterns = self._build_business_patterns()
        self.error_solutions = self._build_error_solutions()
        
    def _build_business_patterns(self) -> Dict[str, Dict]:
        """构建业务场景识别模式"""
        return {
            # 复判相关
            'recheck_scenarios': {
                'patterns': [
                    (re.compile(r'复判.*发送.*mes', re.IGNORECASE), '复判数据发送MES'),
                    (re.compile(r'recheck.*send.*mes', re.IGNORECASE), '复判数据发送MES'),
                    (re.compile(r'复判.*上传', re.IGNORECASE), '复判数据上传'),
                    (re.compile(r'复判.*失败', re.IGNORECASE), '复判操作失败'),
                    (re.compile(r'复判.*成功', re.IGNORECASE), '复判操作成功'),
                    (re.compile(r'review.*data.*upload', re.IGNORECASE), '复判数据上传'),
                ],
                'category': '复判流程',
                'description': '产品复判相关的业务操作'
            },
            
            # 条码校验相关
            'barcode_scenarios': {
                'patterns': [
                    (re.compile(r'条码.*校验.*失败', re.IGNORECASE), '条码校验失败'),
                    (re.compile(r'条码.*不存在', re.IGNORECASE), '条码不存在'),
                    (re.compile(r'条码.*格式.*错误', re.IGNORECASE), '条码格式错误'),
                    (re.compile(r'barcode.*validation.*failed', re.IGNORECASE), '条码校验失败'),
                    (re.compile(r'barcode.*not.*found', re.IGNORECASE), '条码不存在'),
                    (re.compile(r'sn.*校验', re.IGNORECASE), 'SN码校验'),
                    (re.compile(r'二维码.*扫描', re.IGNORECASE), '二维码扫描'),
                ],
                'category': '条码管理',
                'description': '产品条码识别和校验相关操作'
            },
            
            # MES通信相关
            'mes_communication': {
                'patterns': [
                    (re.compile(r'post_json.*失败', re.IGNORECASE), 'MES接口调用失败'),
                    (re.compile(r'post_json.*超时', re.IGNORECASE), 'MES接口超时'),
                    (re.compile(r'mes.*连接.*失败', re.IGNORECASE), 'MES连接失败'),
                    (re.compile(r'mes.*服务器.*无响应', re.IGNORECASE), 'MES服务器无响应'),
                    (re.compile(r'http.*request.*failed', re.IGNORECASE), 'HTTP请求失败'),
                    (re.compile(r'connection.*timeout', re.IGNORECASE), '连接超时'),
                    (re.compile(r'网络.*异常', re.IGNORECASE), '网络异常'),
                ],
                'category': 'MES通信',
                'description': 'MES系统通信和接口调用相关问题'
            },
            
            # 数据上传相关
            'data_upload': {
                'patterns': [
                    (re.compile(r'上传.*数据.*失败', re.IGNORECASE), '数据上传失败'),
                    (re.compile(r'发送.*数据.*mes', re.IGNORECASE), '发送数据到MES'),
                    (re.compile(r'upload.*failed', re.IGNORECASE), '上传失败'),
                    (re.compile(r'数据.*同步.*失败', re.IGNORECASE), '数据同步失败'),
                    (re.compile(r'检测.*结果.*上传', re.IGNORECASE), '检测结果上传'),
                ],
                'category': '数据传输',
                'description': '检测数据和结果上传相关操作'
            },
            
            # 设备状态相关
            'device_status': {
                'patterns': [
                    (re.compile(r'设备.*离线', re.IGNORECASE), '设备离线'),
                    (re.compile(r'设备.*故障', re.IGNORECASE), '设备故障'),
                    (re.compile(r'相机.*异常', re.IGNORECASE), '相机异常'),
                    (re.compile(r'光源.*异常', re.IGNORECASE), '光源异常'),
                    (re.compile(r'温度.*异常', re.IGNORECASE), '温度异常'),
                    (re.compile(r'压力.*异常', re.IGNORECASE), '压力异常'),
                    (re.compile(r'device.*offline', re.IGNORECASE), '设备离线'),
                ],
                'category': '设备监控',
                'description': '生产设备状态和异常监控'
            },
            
            # 生产流程相关
            'production_flow': {
                'patterns': [
                    (re.compile(r'工单.*不存在', re.IGNORECASE), '工单不存在'),
                    (re.compile(r'工艺.*路线.*错误', re.IGNORECASE), '工艺路线错误'),
                    (re.compile(r'生产.*计划.*异常', re.IGNORECASE), '生产计划异常'),
                    (re.compile(r'物料.*缺料', re.IGNORECASE), '物料缺料'),
                    (re.compile(r'批次.*异常', re.IGNORECASE), '批次异常'),
                    (re.compile(r'工序.*锁定', re.IGNORECASE), '工序锁定'),
                ],
                'category': '生产管理',
                'description': '生产计划和工艺流程相关问题'
            },
            
            # 质量检测相关
            'quality_control': {
                'patterns': [
                    (re.compile(r'检测.*ng', re.IGNORECASE), '检测NG'),
                    (re.compile(r'缺陷.*检出', re.IGNORECASE), '缺陷检出'),
                    (re.compile(r'漏件.*检测', re.IGNORECASE), '漏件检测'),
                    (re.compile(r'错件.*检测', re.IGNORECASE), '错件检测'),
                    (re.compile(r'偏移.*检测', re.IGNORECASE), '偏移检测'),
                    (re.compile(r'质量.*异常', re.IGNORECASE), '质量异常'),
                ],
                'category': '质量控制',
                'description': '产品质量检测和缺陷识别'
            }
        }
    
    def _build_error_solutions(self) -> Dict[str, List[str]]:
        """构建错误解决方案"""
        return {
            '条码校验失败': [
                '1. 检查条码是否清晰可读',
                '2. 确认条码格式是否正确',
                '3. 检查扫码设备是否正常工作',
                '4. 联系生产管理员确认条码信息'
            ],
            '条码不存在': [
                '1. 确认产品是否已在MES系统中建档',
                '2. 检查条码是否录入正确',
                '3. 联系IT人员检查数据库连接',
                '4. 确认工单是否已下达'
            ],
            'MES接口调用失败': [
                '1. 检查网络连接是否正常',
                '2. 确认MES服务器是否运行正常',
                '3. 检查接口参数是否正确',
                '4. 联系IT人员检查系统状态'
            ],
            'MES连接失败': [
                '1. 检查网络连接',
                '2. 确认MES服务器IP地址和端口',
                '3. 检查防火墙设置',
                '4. 联系网络管理员'
            ],
            '数据上传失败': [
                '1. 检查网络连接状态',
                '2. 确认数据格式是否正确',
                '3. 检查存储空间是否充足',
                '4. 重试上传操作'
            ],
            '设备离线': [
                '1. 检查设备电源连接',
                '2. 检查网络连接线',
                '3. 重启设备',
                '4. 联系设备维护人员'
            ],
            '工单不存在': [
                '1. 确认工单号是否正确',
                '2. 检查工单是否已下达',
                '3. 联系生产计划员',
                '4. 确认产品型号是否匹配'
            ],
            '检测NG': [
                '1. 检查产品质量是否符合要求',
                '2. 确认检测参数设置',
                '3. 检查设备校准状态',
                '4. 联系质量工程师'
            ]
        }
    
    def analyze_business_scenarios(self, entries: List[LogEntry]) -> Dict[str, BusinessScenario]:
        """分析业务场景"""
        scenarios = {}
        
        for entry in entries:
            message = entry.message
            
            # 遍历所有业务模式
            for scenario_type, config in self.business_patterns.items():
                for pattern, scenario_name in config['patterns']:
                    if pattern.search(message):
                        key = f"{config['category']}_{scenario_name}"
                        
                        if key not in scenarios:
                            scenarios[key] = BusinessScenario(
                                name=scenario_name,
                                description=config['description'],
                                category=config['category'],
                                severity=self._determine_severity(entry.level),
                                count=0,
                                examples=[],
                                solutions=self.error_solutions.get(scenario_name, [])
                            )
                        
                        scenarios[key].count += 1
                        
                        # 添加示例（限制数量）
                        if len(scenarios[key].examples) < 3:
                            time_str = entry.timestamp.strftime('%H:%M:%S') if entry.timestamp else '未知时间'
                            scenarios[key].examples.append(f"[{time_str}] {message[:100]}")
        
        return scenarios
    
    def _determine_severity(self, log_level: str) -> str:
        """根据日志级别确定严重程度"""
        severity_map = {
            'CRITICAL': '严重',
            'ERROR': '错误', 
            'WARNING': '警告',
            'INFO': '信息',
            'DEBUG': '调试'
        }
        return severity_map.get(log_level, '未知')
    
    def get_business_summary(self, entries: List[LogEntry]) -> Dict:
        """获取业务摘要"""
        scenarios = self.analyze_business_scenarios(entries)
        
        summary = {
            'total_scenarios': len(scenarios),
            'category_stats': defaultdict(int),
            'severity_stats': defaultdict(int),
            'top_issues': [],
            'recent_issues': [],
            'recommendations': []
        }
        
        # 统计分类和严重程度
        for scenario in scenarios.values():
            summary['category_stats'][scenario.category] += scenario.count
            summary['severity_stats'][scenario.severity] += scenario.count
        
        # 获取最频繁的问题
        sorted_scenarios = sorted(scenarios.values(), key=lambda x: x.count, reverse=True)
        summary['top_issues'] = [
            {
                'name': s.name,
                'category': s.category,
                'count': s.count,
                'severity': s.severity,
                'solutions': s.solutions[:2]  # 只显示前2个解决方案
            }
            for s in sorted_scenarios[:5]
        ]
        
        # 获取最近的严重问题
        critical_scenarios = [s for s in scenarios.values() if s.severity in ['严重', '错误']]
        summary['recent_issues'] = sorted(critical_scenarios, key=lambda x: x.count, reverse=True)[:3]
        
        # 生成建议
        summary['recommendations'] = self._generate_recommendations(summary)
        
        return summary
    
    def _generate_recommendations(self, summary: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于问题频率的建议
        if summary['category_stats'].get('MES通信', 0) > 5:
            recommendations.append('建议检查MES服务器稳定性和网络连接质量')
        
        if summary['category_stats'].get('条码管理', 0) > 3:
            recommendations.append('建议检查条码扫描设备和条码质量')
        
        if summary['category_stats'].get('设备监控', 0) > 2:
            recommendations.append('建议加强设备维护和预防性检查')
        
        if summary['severity_stats'].get('严重', 0) > 0:
            recommendations.append('发现严重问题，建议立即联系技术支持')
        
        if not recommendations:
            recommendations.append('系统运行正常，建议继续保持')
        
        return recommendations
    
    def generate_business_report(self, entries: List[LogEntry]) -> str:
        """生成业务报告"""
        scenarios = self.analyze_business_scenarios(entries)
        summary = self.get_business_summary(entries)
        
        report = []
        report.append("=" * 50)
        report.append("MES系统运行状况报告")
        report.append("=" * 50)
        report.append(f"报告时间: {self._get_current_time()}")
        report.append(f"分析日志条数: {len(entries)}")
        report.append(f"发现业务场景: {summary['total_scenarios']}种")
        report.append("")
        
        # 业务分类统计
        report.append("📊 业务模块统计:")
        for category, count in summary['category_stats'].items():
            report.append(f"  • {category}: {count}次")
        report.append("")
        
        # 问题严重程度
        report.append("⚠️  问题严重程度:")
        for severity, count in summary['severity_stats'].items():
            icon = self._get_severity_icon(severity)
            report.append(f"  {icon} {severity}: {count}次")
        report.append("")
        
        # 主要问题
        if summary['top_issues']:
            report.append("🔥 主要问题 (按频率排序):")
            for i, issue in enumerate(summary['top_issues'], 1):
                report.append(f"  {i}. {issue['name']} ({issue['category']})")
                report.append(f"     发生次数: {issue['count']}次")
                report.append(f"     严重程度: {issue['severity']}")
                if issue['solutions']:
                    report.append(f"     建议措施: {issue['solutions'][0]}")
                report.append("")
        
        # 改进建议
        if summary['recommendations']:
            report.append("💡 改进建议:")
            for i, rec in enumerate(summary['recommendations'], 1):
                report.append(f"  {i}. {rec}")
            report.append("")
        
        # 详细场景分析
        report.append("📋 详细场景分析:")
        for scenario in sorted(scenarios.values(), key=lambda x: x.count, reverse=True):
            if scenario.count > 0:
                report.append(f"  场景: {scenario.name}")
                report.append(f"  分类: {scenario.category}")
                report.append(f"  次数: {scenario.count}")
                report.append(f"  严重程度: {scenario.severity}")
                
                if scenario.examples:
                    report.append("  示例:")
                    for example in scenario.examples:
                        report.append(f"    - {example}")
                
                if scenario.solutions:
                    report.append("  解决方案:")
                    for solution in scenario.solutions:
                        report.append(f"    - {solution}")
                
                report.append("")
        
        return "\n".join(report)
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def _get_severity_icon(self, severity: str) -> str:
        """获取严重程度图标"""
        icons = {
            '严重': '🔴',
            '错误': '🟠', 
            '警告': '🟡',
            '信息': '🔵',
            '调试': '⚪'
        }
        return icons.get(severity, '❓')
    
    def get_quick_diagnosis(self, entries: List[LogEntry]) -> Dict:
        """快速诊断"""
        scenarios = self.analyze_business_scenarios(entries)

        # 找出最严重的问题
        critical_issues = [s for s in scenarios.values() if s.severity in ['严重', '错误'] and s.count > 0]
        critical_issues.sort(key=lambda x: x.count, reverse=True)

        diagnosis = {
            'status': '正常',
            'main_issue': None,
            'urgent_actions': [],
            'summary': '系统运行正常'
        }

        if critical_issues:
            main_issue = critical_issues[0]
            diagnosis['status'] = '异常' if main_issue.severity == '严重' else '警告'
            diagnosis['main_issue'] = {
                'name': main_issue.name,
                'category': main_issue.category,
                'count': main_issue.count,
                'solutions': main_issue.solutions[:3]
            }
            diagnosis['urgent_actions'] = main_issue.solutions[:2]
            diagnosis['summary'] = f"发现{main_issue.category}问题: {main_issue.name}，已发生{main_issue.count}次"

        return diagnosis

    def monitor_business_flow(self, entries: List[LogEntry]) -> Dict:
        """监控业务流程"""
        flow_monitor = {
            'process_health': {},
            'bottlenecks': [],
            'flow_efficiency': {},
            'anomalies': [],
            'recommendations': []
        }

        # 分析各业务流程的健康状况
        scenarios = self.analyze_business_scenarios(entries)

        for scenario in scenarios.values():
            category = scenario.category
            if category not in flow_monitor['process_health']:
                flow_monitor['process_health'][category] = {
                    'status': '正常',
                    'issue_count': 0,
                    'efficiency': 100
                }

            # 根据问题数量评估流程健康度
            if scenario.count > 0:
                flow_monitor['process_health'][category]['issue_count'] += scenario.count

                if scenario.severity == '严重':
                    flow_monitor['process_health'][category]['status'] = '异常'
                    flow_monitor['process_health'][category]['efficiency'] -= scenario.count * 20
                elif scenario.severity == '错误':
                    if flow_monitor['process_health'][category]['status'] == '正常':
                        flow_monitor['process_health'][category]['status'] = '警告'
                    flow_monitor['process_health'][category]['efficiency'] -= scenario.count * 10

        # 识别瓶颈
        for category, health in flow_monitor['process_health'].items():
            if health['issue_count'] > 5:
                flow_monitor['bottlenecks'].append({
                    'process': category,
                    'issue_count': health['issue_count'],
                    'impact': '高' if health['issue_count'] > 10 else '中'
                })

        # 生成改进建议
        if flow_monitor['bottlenecks']:
            flow_monitor['recommendations'].append('优先处理瓶颈流程，提高整体效率')

        for category, health in flow_monitor['process_health'].items():
            if health['status'] == '异常':
                flow_monitor['recommendations'].append(f'立即检查{category}流程，解决关键问题')

        return flow_monitor
