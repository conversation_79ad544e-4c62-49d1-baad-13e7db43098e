# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2025/3/13 上午9:34
# Author     ：sch
# version    ：python 3.8
# Description：成都一博
"""
import json
import os
from typing import Any

from PIL import Image

from common import xutil, xrequest, xcons
from common.xsql import SqlLiteHelper
from engine.FtpEngine import FTPClient
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo

check_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:excute>
            <functionId>lbCheck</functionId>
            <body>
{data_param}
            </body>
        </tem:excute>
    </soapenv:Body>
</soapenv:Envelope>"""

data_template = """<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:excute>
            <functionId>uploadResult</functionId>
            <body>
{data_param}
            </body>
        </tem:excute>
    </soapenv:Body>
</soapenv:Envelope>"""

csv_board_board_template = """设备名称,{device_name}
整板条码,{pcb_sn}
轨道,{pcb_track_line}
面别,{pcb_board_side}
测试时间,{pcb_test_time}
测试耗时,{pcb_cycle_time}
程序名,{pcb_project_name}
整板检测结果,{pcb_robot_result}
整板复判结果,{pcb_user_result}
整板最终结果,{pcb_final_result}
复判操作员,{pcb_repair_user}
拼板数量,{pcb_board_number}
整板器件总数,{pcb_comp_number}
拼板序号,{board_no}
拼板条码,{board_sn}
拼板检测结果,{board_robot_result}
拼板复判结果,{board_user_result}
拼板最终结果,{board_final_result}
拼板器件数量,{board_comp_number}
拼板器件检测NG总数,{board_comp_robot_ng_number}
拼板器件复判NG总数,{board_comp_user_ng_number}
拼板器件误报总数,{board_comp_repass_number}

器件位号,器件料号,器件封装,器件类型,器件检测不良代码,器件检测结果,器件复判不良代码,器件复判结果,器件图片路径{CompData}
"""

csv_comp_board_template = """
{comp_designator},{comp_part},{comp_package},{comp_type},{comp_robot_code},{comp_robot_result},{comp_user_code},{comp_user_result},{comp_image}"""

cache_path = f"{os.getcwd()}/cache_data"


class Engine(ErrorMapEngine):
    version = {
        "title": "chengduyibo release v1.0.0.1",
        "device": "AIS431",
        "feature": ["条码校验", "上传数据"],
        "author": "sch",
        "release": """
date: 2025-03-13 09:35  init
date: 2025-03-14 10:59  条码校验，上传数据  
""", }

    other_form = {
        "api_url_check": {
            "ui_name": "条码过站校验接口",
            "value": "http://127.0.0.1:8081/ims-cust/Service/SuportMIAoiService",
        },
        "api_url_data": {
            "ui_name": "过站信息上传接口",
            "value": "http://127.0.0.1:8081/ims-cust/Service/SuportMIAoiService",
        },
        "ftp_host": {
            "ui_name": "FTP Host",
            "value": "127.0.0.1"
        },
        "ftp_port": {
            "ui_name": "FTP Port",
            "value": "21"
        },
        "ftp_user": {
            "ui_name": "FTP 账号",
            "ui_name_en": "FTP User",
            "value": "sch"
        },
        "ftp_password": {
            "ui_name": "FTP 密码",
            "ui_name_en": "FTP Password",
            "value": "123456"
        },
        "ftp_path": {
            "ui_name": "FTP 路径",
            "ui_name_en": "FTP Path",
            "value": "/AOI"
        },
    }

    form = {
        "dev_code": {
            "ui_name": "设备编码",
            "value": "3F-SMT01-SPI",
        },
        "wp_code": {
            "ui_name": "工序编码",
            "value": "SPI",
        },
        "user_code": {
            "ui_name": "用户编码",
            "value": "IOT",
        },
    }

    def __init__(self):
        xutil.FileUtil.ensure_dir_exist(cache_path)

    def yb_request(self, api_url: str, param: str) -> dict:
        ret_xml = xrequest.RequestUtil.post_xml(api_url, param)
        root = xutil.XmlUtil.get_xml_root_by_str(ret_xml)
        ret_str = root[0][0][0].text

        ret_json = json.loads(ret_str)

        if ret_json.get("code") != "0000":
            return self.x_response("false", f"mes接口异常，error：{ret_json.get('message')}")

        return {}

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        dev_code = other_vo.get_value_by_cons_key("dev_code")
        wp_code = other_vo.get_value_by_cons_key("wp_code")
        user_code = other_vo.get_value_by_cons_key("user_code")

        project_name = other_vo.get_project_name()

        pcb_checks = []
        for sn in other_vo.list_sn():
            pcb_checks.append({
                "lbId": sn
            })

        check_param = {
            "devCode": dev_code,
            "wpCode": wp_code,
            "userCode": user_code,
            "proName": project_name,
            "pcbChecks": pcb_checks
        }

        check_str = check_template.format(data_param=xutil.OtherUtil.obj_to_json(check_param, indent=4))
        if x_res := self.yb_request(api_url_check, check_str):
            return x_res

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        dev_code = data_vo.get_value_by_cons_key("dev_code")
        wp_code = data_vo.get_value_by_cons_key("wp_code")
        user_code = data_vo.get_value_by_cons_key("user_code")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port", to_int=True)
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        # with SqlLiteHelper() as sql_helper:
        #     board_geometry_map = sql_helper.get_board_geometry()
        #
        # pixel_size431 = pcb_entity.get_pixel_size431()
        # pcb_src_img_431 = pcb_entity.get_image_path_431()
        #
        # if os.path.exists(pcb_src_img_431):
        #     pcb_image_obj = Image.open(pcb_src_img_431)
        # else:
        #     pcb_image_obj = None

        ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
        ftp_client.login()
        project_name = pcb_entity.project_name
        date_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE0)
        time_file = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_FILE)

        board_data_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no
            board_result = board_entity.get_repair_result("PASS", "FAIL")

            ftp_img_full_path = f"{ftp_path}/{project_name}/{date_file}/picture"
            ftp_client.cd_or_mkdir(ftp_img_full_path)

            comp_list = []
            csv_data_str = ""
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_src_img = comp_entity.image_path

                    if comp_src_img:
                        comp_filename = f"{barcode}_{comp_entity.designator}_{comp_entity.repair_ng_str}.png"
                        ftp_client.upload_file(comp_src_img, comp_filename)

                        comp_full_filename = f"{ftp_img_full_path}/{comp_filename}"
                    else:
                        comp_full_filename = ""

                    comp_list.append({
                        "badItemCode": comp_entity.repair_ng_code,
                        "badItemName": comp_entity.repair_ng_str,
                        "badPoint": comp_entity.designator,
                        "imagespath": comp_full_filename
                    })

                csv_data_str += csv_comp_board_template.format(**{
                    "comp_designator": comp_entity.designator,
                    "comp_part": comp_entity.part,
                    "comp_package": comp_entity.package,
                    "comp_type": comp_entity.type,
                    "comp_robot_code": comp_entity.robot_ng_code,
                    "comp_robot_result": comp_entity.robot_ng_str,
                    "comp_user_code": comp_entity.repair_ng_code,
                    "comp_user_result": comp_entity.repair_ng_str,
                    "comp_image": comp_entity.image_path,
                })

            # if pixel_size431 and pcb_image_obj:
            #     # 保存拼板图
            #     board_geometry = board_geometry_map.get(board_no)
            #     box = xutil.OtherUtil.geometry_to_box(
            #         pixel_size431[0], pixel_size431[1], pixel_size431[2],
            #         board_geometry.get("cx", 0),
            #         board_geometry.get("cy", 0),
            #         board_geometry.get("width", 0),
            #         board_geometry.get("height", 0),
            #     )
            #
            #     board_dst_img = f"{save_path_img}/{barcode}_{board_no}_{board_result}.jpg"
            #     cropped_image = pcb_image_obj.crop(box)
            #     cropped_image.save(board_dst_img)

            board_data = csv_board_board_template.format(**{
                "device_name": dev_code,
                "pcb_sn": pcb_entity.pcb_barcode,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": barcode,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "CompData": csv_data_str
            })

            ftp_log_full_path = f"{ftp_path}/{project_name}/{date_file}/log"
            ftp_client.cd_or_mkdir(ftp_log_full_path)
            csv_filename = f"{barcode}_{time_file}_{board_no}_{board_result}.csv"
            ftp_client.upload_content(csv_filename, board_data)

            board_data_list.append({
                "lbId": barcode,
                "pcbNum": board_no,
                "finalResult": board_result,
                "logpath": f"{ftp_log_full_path}/{csv_filename}",
                "lbChecks": comp_list
            })

        ftp_client.close()

        data_param = {
            "devCode": dev_code,
            "wpCode": wp_code,
            "userCode": user_code,
            "proName": pcb_entity.project_name,
            "pcbChecks": board_data_list,
        }

        data_str = data_template.format(data_param=xutil.OtherUtil.obj_to_json(data_param, indent=4))
        if x_res := self.yb_request(api_url_data, data_str):
            return x_res

        return self.x_response()
