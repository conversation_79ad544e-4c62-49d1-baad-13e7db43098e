# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/6 上午9:22
# Author     ：sch
# version    ：python 3.8
# Description：武汉海康THT工厂
"""

from typing import Any

from common import xcons, xutil, xrequest
from vo.mes_vo import DataVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "wuhanhaikang_tht release v1.0.0.10",
        "device": "AIS501",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-06 10:11  上传数据
date: 2024-09-10 11:09  修改上传参数
date: 2024-09-10 15:03  修改上传参数 +1
date: 2024-09-11 10:49  修改上传参数 +2
date: 2024-09-28 11:25  修改上传参数 +3
date: 2024-10-16 15:30  误报的器件都要上传，且ngtype 要传机器报的报错类型 ngpoint传位号
date: 2024-11-09 12:18  增加输出`MACHINERESULT`字段
date: 2024-11-09 12:18  bugfix:NG时,没有输出`MACHINERESULT`字段
date: 2024-11-26 10:47  新增字段 SEQID
date: 2024-12-30 14:10  没有识别到条码的时候，barcode=NONE
""", }

    other_form = {
        "api_url_hk": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/dc/process",
        },
        "process_type": {
            "ui_name": "工艺参数类型",
            "value": "DC_THT_AOI_LC",
        },
        "authorization": {
            "ui_name": "Authorization头",
            "value": "Basic ODkwMC0wMTxxxxx",
        },
    }

    form = {
        "machine_id": {
            "ui_name": "设备ID(MachineId)",
            "value": "8900-0111-220541A",
        },
        "class_ui": {
            "ui_name": "班次",
            "value": "A",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_hk = data_vo.get_value_by_cons_key("api_url_hk")
        class_ui = data_vo.get_value_by_cons_key("class_ui")

        process_type = data_vo.get_value_by_cons_key("process_type")
        authorization = data_vo.get_value_by_cons_key("authorization")
        machine_id = data_vo.get_value_by_cons_key("machine_id")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_time1 = pcb_entity.get_start_time().strftime(xcons.FMT_DATE)
        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        headers = {
            "MachineId": machine_id,
            "Authorization": authorization
        }

        create_date = xutil.DateUtil.get_datetime_now(xcons.FMT_TIME_DEFAULT)

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            if board_entity.barcode:
                barcode = board_entity.barcode
            else:
                barcode = "NONE"

            board_no = board_entity.board_no

            is_need_upload = True

            seq_id = 0
            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_robot_ng():

                    seq_id += 1

                    comp_param = {
                        "msgId": xutil.OtherUtil.get_origin_uuid4_str(),
                        "cldate": create_date,
                        "processType": process_type,
                        "processData": {
                            "BARCD": barcode,
                            "PROGRAMENAME": pcb_entity.project_name,
                            "CLASS": class_ui,
                            "COVER": pcb_entity.board_side,
                            "TESTDATE": start_time1,
                            "BEGINTESTDATE": start_time,
                            "ENDTESTDATE": end_time,
                            "TESTELAPSED": str(pcb_entity.get_cycle_time()),
                            "TESTPOINTS": board_entity.comp_total_number,
                            "NGTYPE": comp_entity.robot_ng_str,
                            "NGPOINT": comp_entity.designator,
                            "SEQID": f"J{seq_id}",
                            "RESULT": comp_entity.get_final_result("OK", "OK", "NG"),
                            "MACHINERESULT": comp_entity.get_final_result("OK", "NG", "NG"),
                        }
                    }
                    ret = xrequest.RequestUtil.post_json(api_url_hk, comp_param, headers=headers)
                    if ret.get("code") != "000000":
                        err_msg_list.append(f"BoardNo:{board_no} Barcode:{barcode} {ret.get('message')}")

                    is_need_upload = False

            if is_need_upload:
                # 如果上传过NG的器件，则不需要单独上传一条记录了
                comp_param = {
                    "msgId": xutil.OtherUtil.get_origin_uuid4_str(),
                    "cldate": create_date,
                    "processType": process_type,
                    "processData": {
                        "BARCD": barcode,
                        "PROGRAMENAME": pcb_entity.project_name,
                        "CLASS": class_ui,
                        "COVER": pcb_entity.board_side,
                        "TESTDATE": start_time1,
                        "BEGINTESTDATE": start_time,
                        "ENDTESTDATE": end_time,
                        "TESTELAPSED": str(pcb_entity.get_cycle_time()),
                        "TESTPOINTS": board_entity.comp_total_number,
                        "NGTYPE": "OK",
                        "NGPOINT": "NULL",
                        "SEQID": f"J{seq_id}",
                        "RESULT": "OK",
                        "MACHINERESULT": "OK",
                    }
                }
                ret = xrequest.RequestUtil.post_json(api_url_hk, comp_param, headers=headers)
                if ret.get("code") != "000000":
                    err_msg_list.append(f"BoardNo:{board_no} Barcode:{barcode} error:{ret.get('message')}")

        if err_msg_list[:10]:  # 最多输出10个错误就好
            err_str = "\n".join(err_msg_list)
            return self.x_response("false", f"MES接口响应异常，{err_str}")

        return self.x_response()
