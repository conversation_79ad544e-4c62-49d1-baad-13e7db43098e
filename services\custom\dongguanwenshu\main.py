# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/12/27 下午3:33
# Author     ：sch
# version    ：python 3.8
# Description：东莞文殊
"""

import json
import os
from datetime import datetime
from typing import Any

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QComboBox, QWidget, QScrollArea
)

from common import xrequest, xutil, xcons, xconfig
from common.xglobal import global_data
from common.xutil import log, x_response
from engine.MesEngine import ErrorMapEngine
from vo.mes_vo import DataVo, OtherVo, ButtonVo


class OrderConfigDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("工单配置")
        self.setFixedSize(900, 500)

        # 存储所有的下拉框引用
        self.order_combos = []
        self.site_combos = []
        self.line_combos = []
        self.row_layouts = []
        # 配置文件路径
        self.config_file = f"{xconfig.leichen_dir}/order_config.json"

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 创建顶部容器，用于放置滚动区域和按钮
        top_container = QWidget()
        top_layout = QHBoxLayout(top_container)
        top_layout.setContentsMargins(0, 0, 0, 0)

        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_content)
        self.scroll_layout.setSpacing(20)
        self.scroll_layout.setContentsMargins(20, 20, 20, 20)

        # 加载已保存的配置
        self.load_config()

        # 创建按钮容器
        btn_container = QWidget()
        btn_layout = QVBoxLayout(btn_container)
        btn_layout.setSpacing(10)
        btn_layout.setContentsMargins(0, 0, 0, 0)

        # 添加按钮
        add_btn = QPushButton("增加")
        add_btn.setFixedSize(80, 35)
        add_btn.clicked.connect(self.on_add_clicked)

        # 删除按钮
        del_btn = QPushButton("删除")
        del_btn.setFixedSize(80, 35)
        del_btn.clicked.connect(self.on_delete_clicked)

        # 将按钮添加到按钮布局
        btn_layout.addWidget(add_btn)
        btn_layout.addWidget(del_btn)
        btn_layout.addStretch()  # 添加弹性空间，使按钮靠上对齐

        # 将滚动区域和按钮容器添加到顶部布局
        scroll.setWidget(scroll_content)
        top_layout.addWidget(scroll)
        top_layout.addWidget(btn_container)

        # 确定取消按钮
        bottom_btn_layout = QHBoxLayout()
        ok_btn = QPushButton("确定")
        cancel_btn = QPushButton("取消")
        ok_btn.setMinimumHeight(40)
        cancel_btn.setMinimumHeight(40)
        ok_btn.clicked.connect(self.on_ok_clicked)
        cancel_btn.clicked.connect(self.reject)
        bottom_btn_layout.addWidget(ok_btn)
        bottom_btn_layout.addWidget(cancel_btn)

        # 将所有部件添加到主布局
        main_layout.addWidget(top_container)
        main_layout.addLayout(bottom_btn_layout)

        self.setLayout(main_layout)
        self.row_count = len(self.order_combos) or 1

    def load_config(self):
        """从配置文件加载配置"""

        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                for i, row in enumerate(config, 1):
                    if i > 5:  # 最多5行
                        break
                    self.add_config_row(i, row.get('order', ''),
                                        row.get('site', ''),
                                        row.get('line', ''))
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self.add_config_row(1)  # 添加一个空行
        else:
            # 如果文件不存在，创建一个空的配置文件
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                self.add_config_row(1)  # 添加一个空行
            except Exception as e:
                print(f"创建配置文件失败: {e}")
                self.add_config_row(1)  # 添加一个空行

    def save_config(self):
        """保存配置到文件"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        config = []
        for i in range(len(self.order_combos)):
            row = {
                'order': self.order_combos[i].currentText(),
                'site': self.site_combos[i].currentText(),
                'line': self.line_combos[i].currentText()
            }
            config.append(row)

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def add_config_row(self, index, order_value="", site_value="", line_value=""):
        # 创建一个容器widget来包含这一行的所有内容
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setSpacing(30)
        row_layout.setContentsMargins(0, 0, 0, 0)
        self.row_layouts.append(row_widget)  # 保存行widget的引用

        # 工单号输入框
        order_label = QLabel(f"工单{index}")
        order_label.setFixedWidth(50)
        order_combo = QComboBox()
        order_combo.setEditable(True)
        order_combo.setFixedWidth(150)
        order_combo.setMinimumHeight(35)
        # 完全隐藏下拉按钮区域
        order_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #CCCCCC;
                padding: 1px 1px 1px 3px;
            }
            QComboBox::drop-down {
                width: 0px;
                border: none;
            }
            QComboBox::down-arrow {
                width: 0px;
                height: 0px;
                border: none;
            }
        """)
        if order_value:
            order_combo.addItem(order_value)
            order_combo.setCurrentText(order_value)
        self.order_combos.append(order_combo)

        # 站点输入框
        site_label = QLabel(f"站点{index}")
        site_label.setFixedWidth(50)
        site_combo = QComboBox()
        site_combo.setEditable(True)
        site_combo.setFixedWidth(150)
        site_combo.setMinimumHeight(35)
        # 完全隐藏下拉按钮区域
        site_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #CCCCCC;
                padding: 1px 1px 1px 3px;
            }
            QComboBox::drop-down {
                width: 0px;
                border: none;
            }
            QComboBox::down-arrow {
                width: 0px;
                height: 0px;
                border: none;
            }
        """)
        if site_value:
            site_combo.addItem(site_value)
            site_combo.setCurrentText(site_value)
        self.site_combos.append(site_combo)

        # 线别输入框
        line_label = QLabel(f"线别{index}")
        line_label.setFixedWidth(50)
        line_combo = QComboBox()
        line_combo.setEditable(True)
        line_combo.setFixedWidth(150)
        line_combo.setMinimumHeight(35)
        # 完全隐藏下拉按钮区域
        line_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #CCCCCC;
                padding: 1px 1px 1px 3px;
            }
            QComboBox::drop-down {
                width: 0px;
                border: none;
            }
            QComboBox::down-arrow {
                width: 0px;
                height: 0px;
                border: none;
            }
        """)
        if line_value:
            line_combo.addItem(line_value)
            line_combo.setCurrentText(line_value)
        self.line_combos.append(line_combo)

        # 将组件添加到行布局中
        row_layout.addWidget(order_label)
        row_layout.addWidget(order_combo)
        row_layout.addWidget(site_label)
        row_layout.addWidget(site_combo)
        row_layout.addWidget(line_label)
        row_layout.addWidget(line_combo)
        row_layout.addStretch()  # 添加弹性空间

        # 将行widget添加到滚动区域布局
        self.scroll_layout.addWidget(row_widget)

    def on_delete_clicked(self):
        """删除最后一行"""
        if self.row_count > 1:  # 保证至少保留一行
            # 获取最后一行的widget
            row_widget = self.row_layouts.pop()

            # 从列表中移除最后一个组件的引用
            self.order_combos.pop()
            self.site_combos.pop()
            self.line_combos.pop()

            # 删除widget
            row_widget.deleteLater()

            # 更新行数
            self.row_count -= 1

    def on_add_clicked(self):
        if self.row_count < 5:
            self.row_count += 1
            self.add_config_row(self.row_count)

    def on_ok_clicked(self):
        """点击确定按钮时保存配置并关闭对话框"""
        self.save_config()
        self.accept()


def refresh_token(api_url, user_no, password):
    """
    刷新token
    """
    log.info(f"没有获取到token，开始去获取token...")

    param = {
        "userNo": user_no,
        "password": password
    }

    ret = xrequest.RequestUtil.post_json(api_url, param)

    if ret.get("msgId") != 0:
        return x_response("false", f"接口响应异常, 登录失败, error: {ret.get('msgStr')}")

    global_data["token"] = ret.get("token")


class Engine(ErrorMapEngine):
    version = {
        "title": "dongguanwenshu release v1.0.0.5",
        "device": "AIS203",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-12-27 15:35  从mes获取条码，条码校验，上传数据
date: 2025-01-22 14:40  jira:35589,增加工单配置按钮点击之后出现新的UI界面,用于填写工单号,站点,线别
date: 2025-03-03 17:31  bugfix:修复原有bug 不生成对应的order_config.json文件
date: 2025-03-04 10:54  bugfix:修复获取不到工单号的bug
date: 2025-03-05 09:27  jira:37681,条码为空时,传时间戳_拼版号
""", }

    other_form = {
        "api_url_login": {
            "ui_name": "接口URL(登录)",
            "ui_name_en": "APIURL(clientLogin)",
            "value": "http://127.0.0.1:8081/swiftmom/api/clientLogin",
        },
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "ui_name_en": "APIURL(getRelationPcbSeq)",
            "value": "http://127.0.0.1:8081/swiftmom/api/getRelationPcbSeq",
        },
        "api_url_check": {
            "ui_name": "接口URL(条码校验)",
            "ui_name_en": "APIURL(checkRoute)",
            "value": "http://127.0.0.1:8081/swiftmom/api/checkRoute",
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "ui_name_en": "APIURL(createAOIData)",
            "value": "http://127.0.0.1:8081/swiftmom/api/createAOIData",
        },
        "type_ui": {
            "ui_name": "type",
            "value": "1",
        }
    }

    form = {
        "user_no": {
            "ui_name": "工号",
            "ui_name_en": "UserNo",
            "value": "",
        },
        "password": {
            "ui_name": "密码",
            "ui_name_en": "Password",
            "value": "",
        },
        "org_id": {
            "ui_name": "公司名",
            "ui_name_en": "orgId",
            "value": "ZS",
        },
        # "station_no": {
        #     "ui_name": "站位编号",
        #     "ui_name_en": "stationNo",
        #     "value": "",
        # },
        "machine_no": {
            "ui_name": "机器编号",
            "ui_name_en": "machineNo",
            "value": "2-A.AOI",
        },
        # "thread_no": {
        #     "ui_name": "线别编号",
        #     "ui_name_en": "threadNo",
        #     "value": "2-A",
        # },
        "company_no": {
            "ui_name": "公司编号",
            "ui_name_en": "companyNo",
            "value": "ZS",
        },
    }

    combo = {
        "is_retest": {
            "ui_name": "重复测试",
            "ui_name_en": "retest",
            "item": ["true", "false"],
            "value": "true",
        },
        "board_side_ui": {
            "ui_name": "板面",
            "ui_name_en": "BoardSide",
            "item": ["T", "B", "T+B"],
            "value": "T",
        },
    }

    button = {
        "login_btn": {
            "ui_name": "登录",
            "ui_name_en": "LoginUser",
        },
        "order_config_btn": {
            "ui_name": "工单配置",
            "ui_name_en": "Order Config"
        }
    }

    def init_main_window(self, main_window, other_vo: OtherVo):
        main_window.set_cron_setting(True, 60 * 60 * 1)

    def get_config_values(self, prod_no):
        """
        根据工单号获取对应的站点和线别配置
        """
        try:
            # 使用正确的配置文件路径
            config_file = f"{xconfig.leichen_dir}/order_config.json"
            self.log.info(f"尝试读取配置文件: {config_file}")

            if not os.path.exists(config_file):
                self.log.error(f"配置文件不存在: {config_file}")
                return None, None

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 遍历配置找到匹配的工单
            for row in config:
                if row.get('order') == prod_no:
                    self.log.info(f"找到工单 {prod_no} 的配置: 站点={row.get('site')}, 线别={row.get('line')}")
                    return row.get('site'), row.get('line')

            self.log.warning(f"未找到工单 {prod_no} 的配置")
        except Exception as e:
            self.log.error(f"读取配置文件失败: {e}")

        return None, None

    def get_sn_by_mes(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_no = other_vo.get_value_by_cons_key("user_no")
        password = other_vo.get_value_by_cons_key("password")

        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        type_ui = other_vo.get_value_by_cons_key("type_ui")

        org_id = other_vo.get_value_by_cons_key("org_id")
        # 获取工单号
        prod_no = other_vo.get_order_id()

        param = {
            "pcbSeq": other_vo.get_pcb_sn(),
            "prodNo": prod_no,
            "orgId": org_id,
            "type": type_ui
        }

        token = global_data.get("token")

        if not token:
            if x_res := refresh_token(api_url_login, user_no, password):
                return x_res

        token = global_data.get("token")

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, param, headers={"token": token})
        if ret.get("msgId") != 0:
            return x_response("false", f"接口响应异常, 获取条码失败, error: {ret.get('msgStr')}")

        data = ret.get("data")
        ret_sn = [item.get("pcbSeq") for item in data]

        return self.x_response("true", ",".join(ret_sn))

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_no = other_vo.get_value_by_cons_key("user_no")
        password = other_vo.get_value_by_cons_key("password")

        api_url_check = other_vo.get_value_by_cons_key("api_url_check")
        # station_no = other_vo.get_value_by_cons_key("station_no")
        is_retest = other_vo.get_value_by_cons_key("is_retest")

        token = global_data.get("token")

        if not token:
            if x_res := refresh_token(api_url_login, user_no, password):
                return x_res

        token = global_data.get("token")
        sn_list = other_vo.list_sn()
        # 获取工单号
        prod_no = other_vo.get_order_id()

        # 从配置文件获取站点信息
        station_no, _ = self.get_config_values(prod_no)
        if not station_no:
            return x_response(f"未找到工单号: {prod_no} 对应的站点信息")

        err_msg_list = []

        for ix, sn in enumerate(sn_list):
            ix += 1
            param = {
                "pcbSeq": sn,
                "prodNo": prod_no,
                "stationNo": station_no,
                "retest": 1 if is_retest == "true" else 0
            }

            ret = xrequest.RequestUtil.post_json(api_url_check, param, headers={"token": token})
            if ret.get("msgId") != 0:
                err_msg_list.append(f"No:{ix} SN:{sn} Error:{ret.get('msgStr')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return x_response("false", f"接口响应异常, 条码校验失败, {err_str}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_login = data_vo.get_value_by_cons_key("api_url_login")
        user_no = data_vo.get_value_by_cons_key("user_no")
        password = data_vo.get_value_by_cons_key("password")

        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        company_no = data_vo.get_value_by_cons_key("company_no")
        # thread_no = data_vo.get_value_by_cons_key("thread_no")
        board_side_ui = data_vo.get_value_by_cons_key("board_side_ui")
        # station_no = data_vo.get_value_by_cons_key("station_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        token = global_data.get("token")

        if not token:
            if x_res := refresh_token(api_url_login, user_no, password):
                return x_res

        token = global_data.get("token")

        start_time = pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT)
        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        # 获取工单号
        prod_no = pcb_entity.order_id

        # 从配置文件获取站点和线别信息
        station_no, thread_no = self.get_config_values(prod_no)
        if not station_no or not thread_no:
            return x_response(f"未找到工单号 {prod_no} 对应的站点和线别信息")

        self.log.info(f"已找到工单: {prod_no} 对应的站点: {station_no} 和线别: {thread_no}")

        if not station_no or not thread_no:
            return x_response(f"未找到工单号 {prod_no} 对应的站点和线别信息")

        err_msg_list = []
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            time_data = datetime.now().strftime(xcons.FMT_TIME_FILE)

            barcode = board_entity.barcode if board_entity.barcode else f"{time_data}_{board_entity.board_no}"
            remark_list = []

            for comp_entity in board_entity.yield_comp_entity():

                if comp_entity.is_repair_ng():
                    comp_des = comp_entity.designator
                    repair_ng_code = comp_entity.repair_ng_code

                    remark_list.append(f'{comp_des}_{repair_ng_code}')

            now = xutil.DateUtil.get_datetime_now("%Y-%m-%d %H:%M:%S")
            data_param = {
                "pcbSeq": barcode,
                "createdDateTime": now,
                "prodNo": prod_no,
                "result": board_entity.get_repair_result("PASS", "FAIL"),
                "oriResult": board_entity.get_robot_result("PASS", "FAIL"),
                "machineNo": machine_no,
                "threadNo": thread_no,
                "beginTime": start_time,
                "endTime": end_time,
                "board": board_side_ui,
                "remark": ",".join(remark_list),
                "companyNo": company_no,
                "mainPcbSeq": pcb_entity.pcb_barcode,
                "partSn": board_entity.board_no,
                "trackName": str(pcb_entity.track_index),
                "badPointQty": str(board_entity.comp_repair_ng_number),
                "pointQty": str(board_entity.comp_total_number),
                "siteNo": station_no,
                "programName": pcb_entity.project_name,
                "picDir": pcb_entity.get_unknown_t_pcb_image(),
            }

            res = xrequest.RequestUtil.post_json(api_url_data, data_param, headers={"token": token})
            if res.get("result") != 0:
                err_msg_list.append(f"No:{board_entity.board_no} SN:{barcode} Error:{res.get('message')}")

        if err_msg_list:
            err_str = "\n".join(err_msg_list)
            return x_response("false", f"接口响应异常, 上传数据失败, {err_str}")

        return self.x_response()

    def custom_button_clicked(self, btn_vo: ButtonVo, other_param: Any):

        if btn_vo.get_btn_key() == "login_btn":
            api_url_login = btn_vo.get_value_by_cons_key("api_url_login")
            user_no = btn_vo.get_value_by_cons_key("user_no")
            password = btn_vo.get_value_by_cons_key("password")

            if res := refresh_token(api_url_login, user_no, password):
                return res

        elif btn_vo.get_btn_key() == "order_config_btn":
            # 显示工单配置对话框
            dialog = OrderConfigDialog(other_param)
            if dialog.exec_() == QDialog.Accepted:
                pass

        return self.x_response()

    def custom_cron_function(self, other_vo: OtherVo, main_window, other_param: Any):
        api_url_login = other_vo.get_value_by_cons_key("api_url_login")
        user_no = other_vo.get_value_by_cons_key("user_no")
        password = other_vo.get_value_by_cons_key("password")

        refresh_token(api_url_login, user_no, password)
