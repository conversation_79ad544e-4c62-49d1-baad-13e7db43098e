# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : taida_module.py
# Time       ：2024/9/19 下午2:58
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import os
import time

from common import xutil, xrequest, xcons
from common.xglobal import global_data
from common.xutil import log, XmlUtil

circle_list1 = xutil.CircularList(200)


error_code_map = {
    "0101": "0",
    "0202": "2",
    "0303": "3",
    "0404": "4",
    "0505": "5",
    "0707": "7",
    "1010": "10",
    "1111": "11",
    "1212": "12",
    "1414": "93",
    "2121": "11",
    "2323": "11",
    "9913": "13",
    "9914": "14",
    "9915": "15",
    "9916": "16",
    "9917": "17",
    "9918": "18",
    "9919": "19",
    "9920": "20",
    "9921": "21",
    "9922": "22",
    "9923": "23",
    "9924": "24",
    "9925": "25",
    "9926": "26",
    "9927": "27",
    "9928": "28",
    "9929": "29",
    "9930": "30",
    "9931": "31",
    "9932": "32",
    "9933": "33",
    "9934": "34",
    "9935": "35",
    "9936": "36",
    "9937": "37",
    "9938": "38",
    "9939": "39",
    "9940": "40",
    "9945": "41",
    "9946": "42",
    "9947": "43",
    "9948": "44",
    "9949": "45",
    "9950": "46",
    "9951": "47",
    "9952": "48",
    "9953": "49",
    "9954": "50",
    "9957": "51",
    "9958": "52",
    "9959": "54",
    "9961": "55",
    "9962": "56",
    "9963": "57",
    "9964": "58",
    "9966": "60",
    "9968": "62",
    "9969": "63",
    "9970": "64",
    "9971": "65",
    "9972": "66",
    "9973": "67",
    "9974": "68",
    "9975": "69",
    "9976": "70",
    "9977": "71",
    "9978": "72",
    "9979": "73",
    "9980": "74",
    "9981": "75",
    "9982": "76",
    "9983": "77",
    "9984": "78",
    "9985": "82",
    "994": "84",
    "995": "85",
    "996": "86",
    "997": "87",
    "998": "88",
    "999": "89",
    "9910": "90",
    "9911": "91",
    "9912": "92",
    "990": "93",
    "9941": "94",
    "9942": "95",
    "9943": "96",
    "9944": "97",
    "9955": "98",
    "9956": "99",
    "9960": "100",
    "9965": "101",
    "9967": "102",
    "9986": "103",
    "9987": "104",
    "9988": "105",
    "9989": "106",
    "9990": "107",
    "9991": "108",
    "9992": "109",
    "9993": "110",
    "9994": "111",
    "9995": "112",
    "9996": "113",
    "9997": "114",
    "9998": "115",
    "9999": "116",
    "99100": "117",
    "99101": "118",
    "99102": "119",
    "99103": "120",
    "99104": "121",
    "99105": "122",
    "99106": "123",
    "99107": "124",
}


def get_device_run_time():
    """
    获取设备本次运行时间
    :return:
    """
    run_time = int(time.time() - global_data['device_run_time'])

    green_time = global_data.get("green_time", 0)

    if not green_time:
        green_time = run_time

    return green_time


def upload_device_status_v2(
        socket_host,
        device_id,
        status_code,
        cache_data: dict,
        cycle_time=0,
        wait_time=0,
        ct_q="0",
        model_ui="",
        error_code="",
):
    """
    上传设备状态

    1. 1、10、11  是红灯，红灯时，在待机信号进来之前，其他信号都不发送到mes，但是红灯状态需要每隔一分钟上传一次
    2. status=1故障时，statusCode需要有具体的errorCode值

    :param socket_host:
    :param device_id:
    :param status_code:
    :param cycle_time: 生产时间
    :param cache_data:
    :param ct_q: 是否有品质不良 (0,未发生) （1，发生）
    :param wait_time: 本次等待的总时间
    :param model_ui
    :param error_code
    :return:
    """
    log.info(f"--->发送设备状态中： {status_code}  errorCode:{error_code}")

    if not status_code:
        status_code = global_data.get("last_send_device_status_code", "03")
        error_code = global_data.get("last_send_device_error_code", "03")
        log.info(f"使用上一次的状态码发送：code:{status_code}  errorCode:{error_code}")

    ct_m = "0"  # 是否发生设备故障

    self_check = 1  # 设备自检 （1,OK）（2,FAIL）

    if status_code == "01":
        # 正常
        status = 0
    elif status_code == "03":
        # 待机中
        status = 3
    elif status_code in ["02"]:
        # 暂停
        status = 2
    elif status_code in ["99-02", "99-01", "99-1", "99-2", "10"]:
        status = 10  # 安全停机	急停按钮触发

    elif status_code == "12":
        status = 12  # 调机

    # --------20231130 新增 start
    elif status_code == "04":
        # 待料
        status = 4
    elif status_code == "05":
        # 满料
        status = 5
    elif status_code == "07":
        # 换线
        status = 7
    elif status_code == "11":
        # 品质停机
        status = 11
        ct_q = "1"
    # --------202301011 新增 end

    else:
        # 故障报警中
        status = 1
        ct_m = "1"
        self_check = 2

    # 1、10、11  是红灯，红灯时，在开始检测/待机信号进来之前，
    # 其他信号都不发送到mes，但是红灯状态需要每隔一分钟上传一次
    last_status = global_data.get("last_status", "")
    log.info(f"last status: {last_status}  current status: {status}")

    if last_status in [1, 10, 11]:
        if status not in [1, 10, 11, 3]:
            log.warning(f"红灯时，在待机信号进来之前，其他信号都不发送到mes，但是红灯状态需要每隔一分钟上传一次")
            return

    # 1. 从本地缓存的数据中获取 保养维护信息、直通率等信息、故障和时间
    maintain_camera = len(cache_data.get("maintain_camera", []))
    maintain_light_source = len(cache_data.get("maintain_light_source", []))
    maintain_lens = len(cache_data.get("maintain_lens", []))
    maintain_track_belt = len(cache_data.get("maintain_track_belt", []))
    maintain_servo_motor = len(cache_data.get("maintain_servo_motor", []))

    maintain_cylinder = cache_data.get("maintain_cylinder", 0)
    maintain_solenoid_valve = cache_data.get("maintain_solenoid_valve", 0)

    total_input_qty = cache_data.get("total_input_qty", 0)  # 检测总数
    total_pass_qty = cache_data.get("total_pass_qty", 0)  # 直通
    total_fail_qty = cache_data.get("total_fail_qty", 0)  # 不良
    total_repass_qty = cache_data.get("total_repass_qty", 0)  # 误判

    pass_qty_rate = xutil.MathUtil.cal_rate(total_pass_qty, total_input_qty)
    fail_qty_rate = xutil.MathUtil.cal_rate(total_fail_qty, total_input_qty)
    repass_qty_rate = xutil.MathUtil.cal_rate(total_repass_qty, total_input_qty)

    time_except_count = cache_data.get("time_except_count", 0)
    except_number = cache_data.get("except_number", 0)

    # 2. 从全局变量获取本次的运行时间
    run_time = get_device_run_time()

    last_barcode = global_data.get("last_barcode", None)
    last_project_name = global_data.get("last_project_name", model_ui)

    param = [{
        "interfaceID": device_id,
        "status": status,
        "statusCode": error_code_map.get(f"{status_code}{error_code}"),
        "passQty": total_pass_qty + total_repass_qty,  # 良品数量 = 直通 + 误判   主软件传过来的
        "failQty": total_fail_qty,  # 主软件传过来的
        "errorCnt": except_number,
        "errorTimes": time_except_count,
        "cycleTime": float(cycle_time),
        "runningTime": run_time,
        "waitingTime": wait_time,
        "selfCheck": self_check,
        "inputQty": total_input_qty,  # 主软件传过来的
        "barcode": last_barcode,
        "model": last_project_name,
        "paramList": [
            {"paramCode": "CT_M", "paramValue": ct_m},
            {"paramCode": "CT_Q", "paramValue": ct_q},
            {"paramCode": "maintain_camera", "paramValue": maintain_camera},
            {"paramCode": "maintain_light_source", "paramValue": maintain_light_source},
            {"paramCode": "maintain_lens", "paramValue": maintain_lens},
            {"paramCode": "maintain_track_belt", "paramValue": maintain_track_belt},
            {"paramCode": "maintain_servo_motor", "paramValue": maintain_servo_motor},
            {"paramCode": "maintain_cylinder", "paramValue": maintain_cylinder},
            {"paramCode": "maintain_solenoid_valve", "paramValue": maintain_solenoid_valve},
            {"paramCode": "pass_qty_rate", "paramValue": pass_qty_rate},
            {"paramCode": "fail_qty_rate", "paramValue": fail_qty_rate},
            {"paramCode": "repass_qty_rate", "paramValue": repass_qty_rate},
        ],
    }]

    req_param = {
        "type": 3,
        "request_header": {},
        "request_param": {"sensorId": "UploadMachineData"},
        "request_body": param
    }

    # 发送数据给 window socket  （上传设备状态）
    ret = xrequest.SocketUtil.x_socket_send_data(socket_host, req_param)
    if not ret.get('result'):
        msg = ret.get('string')
        raise Exception(f"upload device status error: {msg}")

    global_data["last_status"] = status

    global_data["last_send_device_status_code"] = status_code
    global_data["last_send_device_error_code"] = error_code


def cache_maintain_info(cycle_time, origin_review_path):
    """
    缓存维修保养信息
    维护保养信息, 改成使用了多少天
    :param cycle_time: CT时间
    :param origin_review_path
    :return:
    """
    if origin_review_path:
        if circle_list1.is_exist_item(origin_review_path):
            log.warning(f"该数据包数据已统计保养信息!")
            return
        else:
            circle_list1.add_item(origin_review_path)

    # 记录CT时间和发送次数
    cache_data = xutil.CacheUtil.get_cache_data()

    total_cycle_time = cache_data.get("total_cycle_time", 0)
    total_qty = cache_data.get("total_qty", 0)

    cache_data["total_cycle_time"] = total_cycle_time + cycle_time
    cache_data["total_qty"] = total_qty + 1

    maintain_cylinder = cache_data.get("maintain_cylinder", 0)  # 气缸，10万次
    maintain_solenoid_valve = cache_data.get("maintain_solenoid_valve", 0)  # 电磁阀，10万次

    date_str = xutil.DateUtil.get_datetime_now(xcons.FMT_DATE)

    def append_date_str_to_cache_data(key_str: str):
        """
        不存在则添加到列表
        """
        old_list = cache_data.get(key_str, [])

        if date_str not in old_list:
            old_list.append(date_str)
            cache_data[key_str] = old_list

    append_date_str_to_cache_data("maintain_all_used_date")
    append_date_str_to_cache_data("maintain_camera")
    append_date_str_to_cache_data("maintain_light_source")
    append_date_str_to_cache_data("maintain_lens")
    append_date_str_to_cache_data("maintain_track_belt")
    append_date_str_to_cache_data("maintain_servo_motor")

    cache_data["maintain_cylinder"] = maintain_cylinder + 1
    cache_data["maintain_solenoid_valve"] = maintain_solenoid_valve + 1

    xutil.CacheUtil.save_cache_data(cache_data)
    log.info(
        f"缓存保养信息成功： total_qty: {cache_data['total_qty']}  total_cycle_time: {cache_data['total_cycle_time']}"
    )
    return cache_data


def get_barcode_by_robot_xml(data_path, board_no):
    """
    获取条码
    """
    if ";" in data_path:
        data_path = data_path.split(";")[0]

    file_path = f"{data_path}/mes/{board_no}/robot.xml"

    if os.path.exists(file_path):
        root = XmlUtil.get_xml_root_by_file(file_path)
        return root.attrib.get("Barcode")
    else:
        return ""
