# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/7/5 下午2:52
# Author     ：sch
# version    ：python 3.8
# Description：辰竹仪表
"""
from typing import Any

from common import xcons, xrequest
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "chenzhuyibiao release v1.0.0.5",
        "device": "AIS203",
        "feature": ["上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-07-05 16:47  上传数据到MES
date: 2024-07-22 09:47  修改请求参数
date: 2024-11-15 15:18  增加上传开始/停止检测设备状态
date: 2024-11-22 11:33  bugfix:在开始状态触发时，才调用开工接口
""", }

    form = {
        "api_url_data": {
            "ui_name": "接口URL",
            "value": "",
        },
        "device_name": {
            "ui_name": "设备名称",
            "value": "",
        },
        "work_station": {
            "ui_name": "工位",
            "value": "",
        },
        "api_url_process_start": {
            "ui_name": "工序开工接口",
            "value": "http://127.0.0.1:8081/api/TechGroupStart",
        },
        "api_url_process_end": {
            "ui_name": "工序完工接口",
            "value": "http://127.0.0.1:8081/api/TechGroupComplete",
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        device_name = data_vo.get_value_by_cons_key("device_name")
        work_station = data_vo.get_value_by_cons_key("work_station")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        pcb_board_user_ng_number = 0
        pcb_board_robot_ng_number = 0
        pcb_comp_user_ng_number = 0
        pcb_comp_robot_ng_number = 0

        pcb_sn = pcb_entity.pcb_barcode

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            if board_entity.is_repair_ng():
                pcb_board_user_ng_number += 1

            if board_entity.is_robot_ng():
                pcb_board_robot_ng_number += 1

            pcb_comp_user_ng_number += board_entity.comp_repair_ng_number
            pcb_comp_robot_ng_number += board_entity.comp_robot_ng_number

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            comp_data_list = []
            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_robot_ng():
                    comp_data_list.append({
                        "comp_designator": comp_entity.designator,
                        "comp_part": comp_entity.part,
                        "comp_package": comp_entity.package,
                        "comp_type": comp_entity.type,
                        "comp_robot_code": comp_entity.robot_ng_code,
                        "comp_robot_result": comp_entity.robot_ng_str,
                        "comp_user_code": comp_entity.repair_ng_code,
                        "comp_user_result": comp_entity.repair_ng_str,
                        "comp_image": comp_entity.image_path,
                    })

            board_data_fmt = {
                "device_name": device_name,
                "pcb_sn": barcode,
                "pcb_track_line": pcb_entity.track_index,
                "pcb_board_side": pcb_entity.board_side,
                "pcb_test_time": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                "pcb_cycle_time": pcb_entity.get_cycle_time(),
                "pcb_project_name": pcb_entity.project_name,
                "pcb_robot_result": pcb_entity.get_robot_result("PASS", "FAIL"),
                "pcb_user_result": pcb_entity.get_repair_result("PASS", "FAIL"),
                "pcb_final_result": pcb_entity.get_final_result(),
                "pcb_repair_user": pcb_entity.repair_user,
                "pcb_board_number": pcb_entity.board_count,
                "pcb_comp_number": pcb_entity.comp_count,

                "board_sn": pcb_sn,
                "board_no": board_no,
                "board_robot_result": board_entity.get_robot_result("PASS", "FAIL"),
                "board_user_result": board_entity.get_repair_result("PASS", "FAIL"),
                "board_final_result": board_entity.get_final_result(),

                "board_comp_number": board_entity.comp_total_number,
                "board_comp_robot_ng_number": board_entity.comp_robot_ng_number,
                "board_comp_user_ng_number": board_entity.comp_repair_ng_number,
                "board_comp_repass_number": board_entity.comp_robot_ng_number - board_entity.comp_repair_ng_number,
                "SfcNo": pcb_entity.order_id,
                "Workstation": work_station,
                "comp_data": comp_data_list,
            }

            ret = xrequest.RequestUtil.post_json(api_url_data, board_data_fmt)
            if not ret.get("success"):
                ret_res = self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msg')}")

        return ret_res

    def send_device_status_to_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_process_start = other_vo.get_value_by_cons_key("api_url_process_start")
        api_url_process_end = other_vo.get_value_by_cons_key("api_url_process_end")
        work_station = other_vo.get_value_by_cons_key("work_station")

        status_code_v3 = other_vo.get_status_code_v3()
        username = other_vo.get_device_username()
        order_id = other_vo.get_order_id()

        status_param = {
            "SfcNo": order_id,
            "WorkStationSN": work_station,
            "UserID": username
        }

        if status_code_v3 == "1002":
            # 开始检测
            self.log.info(f"---工序开工")
            ret = xrequest.RequestUtil.post_json(api_url_process_start, status_param)
            if str(ret.get("Result")) != "True":
                return self.x_response("false", f"上传工序开工状态失败，error：{ret.get('Msg')}")

        elif status_code_v3 == "1003":
            # 暂停
            self.log.info(f"---工序完工")
            ret = xrequest.RequestUtil.post_json(api_url_process_end, status_param)
            if str(ret.get("Result")) != "True":
                return self.x_response("false", f"上传工序完工状态失败，error：{ret.get('Msg')}")

        return self.x_response()
