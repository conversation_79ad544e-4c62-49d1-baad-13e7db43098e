# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.
import json
import math
import time

# import cv2
import uvicorn
from fastapi import FastAPI, Body, Form, Header, <PERSON><PERSON>
from fastapi import Request
from fastapi.params import Param, File
from starlette.responses import Response

app = FastAPI()

default_res = {"status": 1001, "message": "success"}


@app.post("/api/v1/acl/operation/insert")
def insert(*,
           data=Body(...),
           ):
    print("上传测试数据------------")
    print(json.dumps(data, ensure_ascii=False, indent=4))

    return {"code": "200", "message": "ok", "data": {
        "type": "OVER_STATION", "list": [
            {"sn": "123", "status": "Y", "message": "success"}
        ]
    }}


@app.post("/api/v1/acl/operation/query")
def query(*,
          data=Body(...),
          ):
    print("条码过站------------")
    print(json.dumps(data, ensure_ascii=False, indent=4))

    return {"code": "200", "message": "ok", "data": {
        "type": "OVER_STATION", "list": [
            {"sn": "123", "status": "Y", "message": "success"}
        ]
    }}


@app.post("/test_form")
def test_form(data=Form(...)):
    print("test_form---------------------")
    print(data)
    return default_res


@app.post("/test_json")
def test_json(data=Form("default data"), age=Body(66), content_type=Header(...)):
    print("test_json---------------------")
    # time.sleep(4)
    print("data", data, type(data))
    print("age", age, type(age))
    print("content type", content_type)
    # return locals()
    return {"code": "1", "msg": "ok", "Data": "ok", "message": "ok"}


@app.post("/ping")
def ping(p1=Param("p1 default"), p2=Param("p2 default")):
    print("----------------pong")
    print("p1 p2:", p1, p2)
    time.sleep(3.4)
    return default_res


@app.get("/ping1")
def ping(p1=Param("p1 default"), p2=Param("p2 default")):
    print("----------------pong")
    print("p1 p2:", p1, p2)
    return default_res


@app.post("/upload_data")
def huafeng_test(pParameter=Form(...)):
    print("------------huafeng test")
    print(pParameter)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="CyntecMES">{"Result": "success", "Message": "登录成功sch_test", "UserID": "745678", "UserName": "xiaoming"}</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/huafeng_test")
def huafeng_test(pParameter=Form(...)):
    print("------------huafeng test")
    print(pParameter)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="CyntecMES">{"Result": "success", "Message": "登录成功sch_test", "UserID": "745678", "UserName": "xiaoming"}</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/eTrace_OracleERP/eTraceOracleERP.asmx")
def yada_1():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <OTO_GetIntSerialNoResponse xmlns="http://eTraceOracleERP.org/">
      <OTO_GetIntSerialNoResult>sn1,sn2,sn3</OTO_GetIntSerialNoResult>
    </OTO_GetIntSerialNoResponse>
  </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/yada_1")
def yada_1():
    return {
        "result": True,
        "msg": "ok"
    }


@app.post("/yada_2")
def yada_2():
    return {
        "result": True,
        "msg": "ok"
    }


li_res = {"EventID": "MES_Check", "Result": "OK", "MSG": "", "Need_Work": "PASS",
          "Return_Data": {"WorkOrder": "FT228W020C", "PanelSN": "P220805000A01084",
                          "SUBSN": "231216M02651010MPP:1-1,231216M02651010MPQ:1-2,231216M02651010MPR:1-3,231216M02651010MPS:1-4,231216M02651010MPT:1-5,231216M02651010MPK:1-6,231216M02651010MPL:1-7,231216M02651010MPN:1-8,231216M02651010MPM:1-9,231216M02651010MQ3:1-10,231216M02651010MQ2:1-11,231216M02651010MQ1:1-12,231216M02651010MQ0:1-13,231216M02651010MPY:1-14,231216M02651010MPX:1-15,231216M02651010MPW:1-16,231216M02651010MQ7:1-17,231216M02651010MQ6:1-18,231216M02651010MQ5:1-19,231216M02651010MQ4:1-20"}}


@app.post("/EAPtoMES")
def lijing():
    print("-------------立景接口参数")

    return li_res


@app.post("/kezhuoer")
def kezhuoer(commandString=Form(...)):
    print("commandString--------------")
    print(commandString)
    return "OK"


@app.post("/xinwangda")
def xinwangda(data=Body("default data")):
    print("欣旺达接口测试-----------------------------")
    print(data)
    print(type(data))
    print(data.decode('utf8'))
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <CheckUserDoResponse xmlns="WWW.SUNWODA.COM">
            <CheckUserDoResult>TRUE:成功</CheckUserDoResult>
        </CheckUserDoResponse>
    </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/test_arr")
def test_arr():
    print("-------------test arr")
    return {"status": "ok"}


@app.post("/weisidun")
def weisidun(data=Body(...)):
    print("--------------威思顿接口测试")
    print("data", data)

    return {"RESULT": "OK", "MSG": "SUCCESS", "req": data}


@app.post("/weisidun/test")
def weisidun1(data=Body(...)):
    print("--------------威思顿接口测试")
    print("data", data)

    return {"code": "0", "msg": "SUCCESS", "req": data}


@app.post("/test_cookie")
def test_cookie(response: Response, cookie=Header("default cookie"), ):
    """
    测试cookie
    :return:
    """
    print(cookie)
    print("------------response")
    print(response.set_cookie("new_cookie", "12345678"))

    return default_res


@app.post("/test_cookie1")
def test_cookie1(cookie=Header("default cookie")):
    print("=====================cookie1")
    print(cookie)
    return default_res


@app.post("/mes/login")
def diankongchang1(content_type=Header(...)):
    print("长沙比亚迪登录中....")
    print("content-type:", content_type)

    return {"success": True, "msg": "ok"}


@app.post("/mes/proOperate/toOperate")
def diankongchang2():
    return {"success": True, "msg": "ok"}


@app.post("/mes/proOperate/commonAjax")
def diankongchang2():
    return {"success": True, "msg": "ok"}


@app.post("/mes/fangtai")
def fangtai():
    return {"success": True, "error_msg": "ok"}


@app.post("/mes/http/deteck")
def diankongchang3(data=Body("default")):
    print("------------比亚迪数据")
    print(data)
    return {"flag": True, "msg": "ok"}


@app.post("/sanxing")
def sanxing():
    return "ok"


peng_yuan_sheng = {
    "type": "default",
    "res": False,
    "reason": "该数据已上传！"
}


@app.post("/api/AssemblyOperation/CheckMac")
def check_mac(data=Body(...)):
    print("ok")
    print('data...', data)
    print(data)
    return peng_yuan_sheng


@app.post("/api/AssemblyOperation/CheckSn")
def check_sn(data=Body(...)):
    print('-----鹏元晟...')
    print(data)
    return peng_yuan_sheng


@app.post("/api/AssemblyOperation/TestReport")
def test_report(data=Body(...)):
    print('--------鹏元晟...')
    print(data)
    return peng_yuan_sheng


@app.post("/imageUpload")
def chuangwei(data=Body("default")):
    print("图片上传")
    print(data)
    return {
        "ErrorInfo": {
            "Status": False,
            "Message": ""
        },
        "Result": "/upload/20221228/202212281615594232341.png",
        "TotalCount": 0
    }


@app.post("/ningbojianan")
def test_report(data=Body(...)):
    print("ok")
    print('data...', data)
    return {"status": 0, "msg": "ok"}


@app.post("/huacheng")
def hua_cheng(data=Body(...)):
    print("---------------华骋测试中............")
    print(data)
    return {"code": "SUCCESS", "message": "OK"}


@app.post("/shangyan/mc/http/interface.ms")
def shangyan(data=Body(...)):
    print("---------------尚研............")
    print(data)
    return {"code": "OK", "msg": "OK"}


@app.post("/xinsheng")
def xinsheng(data=Body(...)):
    print("------------新昇-----------")
    print(data)
    return {"result": "200", "message": "ok"}


@app.post("/dingsen")
def dingsen(data=Body(...)):
    print("------------鼎森-----------")
    print(data)
    return {"success": True, "message": "ok"}


@app.post("/test001")
def test001(content_type=Header("default content type"), data=Body("default data")):
    print("content-type", content_type)
    print("data", data)

    return {"status": 1000, "message": "ok"}


@app.post("/tianbangda")
def tianbangda(data=Body("")):
    print("----------------天邦达测试")
    print("data", data)

    print("decoding........")
    print(data.decode("utf8"))

    ret_str = """<DataRoot>
    <ReturnMsg>check success</ReturnMsg>
    <ReturnValue>0</ReturnValue>
</DataRoot>"""
    return Response(content=ret_str, media_type="application/xml")


xiaoxiong_default = {
    "res": True,
    "reason": "ok"
}


@app.post("/api/AssemblyOperation/TestReport")
def xiaoxiong1(data=Body(...)):
    print("--------------------小熊1")
    print(data)

    return xiaoxiong_default


@app.post("/api/AssemblyOperation/CheckSn")
def xiaoxiong2(data=Body(...)):
    print("--------------------小熊2")
    print(data)

    return xiaoxiong_default


@app.post("/api/Auth/GetToken")
def xiaoxiong3(data=Body(...)):
    print("--------------------小熊3")
    print(data)

    return {
        "ErrorInfo": {
            "Status": False,
            "Message": "login success"
        },
        "Result": {
            "Token": "fake_token_202208191555"
        }
    }


@app.post("/api/AssemblyOperation/UploadDeviceStatus")
def xiaoxiong3(data=Body(...)):
    print("--------------------小熊4")
    print(data)

    return xiaoxiong_default


@app.post("/api/AssemblyOperation/SendWarnData")
def xiaoxiong3(data=Body(...)):
    print("--------------------小熊4")
    print(data)

    return xiaoxiong_default


@app.post("/suzhououpu")
def suzhououpu(data=Body(...)):
    print("--------------------小熊4")
    print(data)

    return {
        "payload": json.dumps({
            "code": 200,
            "msg": "ok"
        })
    }


@app.post("/mrs/createAOIData")
def huaqiu(data=Body(...)):
    print("-----------华秋")
    print(data)
    return {"result": 0, "message": "ok"}


saierkang_ret = {
    "bResult": True,
    "sMsg": "ok",
    "blocks": [
        {"block": "fake1"},
        {"block": "fake2"},
        {"block": "fake3"},
    ]
}


@app.get("/api/vCheckAPI/GetBlocksByPanel")
def saierkang1(param=Param("default param")):
    print("----------赛尔康1")
    print(param)

    return saierkang_ret


@app.get("/api/vCheckAPI/CheckRouteForSerialNumber")
def saierkang2(param=Param("default param")):
    print("----------赛尔康2")
    print(param)

    return saierkang_ret


@app.post("/api/vCheckAPI/SaveResult")
def saierkang3(param=Body("default param")):
    print("----------赛尔康3")
    print(param)

    return saierkang_ret


@app.get("/api/vCheckAPI/CheckRouteWithPanel")
def saierkang3(param=Body("default param")):
    print("----------赛尔康4")
    print(param)

    return saierkang_ret


@app.post("/sanling")
def sanling(param=Body("default param")):
    print("----------上海三菱")
    print(param)
    return {"STATUS": "Y", "ERRCODE": "OK"}


@app.post("/langneng")
def langneng(param=Body("default param")):
    print("----------朗能")
    print(param)
    return {"Status": "1", "ReturnMsg": "OK"}


@app.post("/WebAPI_AOI/Api/AOI/AOI_Insert")
def langneng(Jason=Form("default param")):
    print("----------欧普照明 上传测试数据")
    print(Jason)
    return 1


@app.post("/WebAPI_AOI/Api/AOI/AOI_State")
def langneng(Jason=Form("default param")):
    print("----------欧普照明 上传设备状态")
    print(Jason)
    return 1


@app.post("/WebService1.asmx")
def yonghuijingchuang(data=Body("default")):
    print("----------永惠精创")
    print(data)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <GetApiCollResponse xmlns="http://tempuri.org/">
      <GetApiCollResult>true</GetApiCollResult>
      <StrReturn>msg</StrReturn>
    </GetApiCollResponse>
  </soap:Body>
</soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/WebService1.asmx/CheckLotSNByPCB")
def ningbojianan1(data=Body("default")):
    print("-------宁波迦南1-----")
    print(data)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string>{"status": 0, "msg": "成功"}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/WebService1.asmx/SubmitTestData")
def ningbojianan2(MachinePara=Form("...")):
    print("-------宁波迦南2-----")
    print(MachinePara)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string>{"status": 0, "msg": "成功"}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/soap/IWebService")
def wuhanzhixin(data=Body(...)):
    print("-----------武汉智新")
    print(data)
    ret_str = """<?xml version="1.0"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"
                   xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/">
    <SOAP-ENV:Body SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:NS1="http://tempuri.org/">
        <NS1:getIMSDataBase64Response>
            <return xsi:type="xsd:boolean">true</return>
            <ResultDTOJson xsi:type="xsd:string"></ResultDTOJson>
            <ResultMsg xsi:type="xsd:string">标签“1006803S00AM031QD2203211000”应该在工序“返修工序[dip_repair]”中操作。[LBCK017]</ResultMsg>
        </NS1:getIMSDataBase64Response>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/SaveInfo")
def qunchuang1(data=Body(...)):
    print("-----------群创1-----------")
    print(data)

    return {"code": 0, "msg": "ok"}


@app.post("/api")
def qunchuang1(data=Body(...)):
    print("-----------群创2-----------")
    print(data)

    return {"ret_code": "0", "ret_mesg": "ok"}


@app.post("/UploadImg")
def qunchuang1(data=Body(...)):
    print("-----------群创3-----------")
    # print(data)
    print(data.get('type'))
    print(data.get('name'))
    print(data.get('path'))
    print(data.get('strBase64')[:20])

    return {"code": 200, "message": "ok"}


@app.post("/smt/prodInfo")
def changshahuaqiu1(data=Body("...")):
    print("长沙华秋上传信息...")
    print(data)

    return {"code": 200, "message": "ok"}


@app.post("/smt/alarmInfo")
def changshahuaqiu2(data=Body("...")):
    print("长沙华秋上传设备报警信息...")
    print(data)

    return {"code": 200, "message": "ok"}


@app.post("/WebService/MesATEApi.asmx/ATECommandCode")
def jinghua_v2_1(commandString=Form("default data")):
    print("-----------精华 v2")
    print(commandString)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
        <string xmlns="CyntecMES">OK;OK</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/WebService/LaserCarvingWebService.asmx/ATECommandCode")
def huizhoushitou(commandString=Form("default data")):
    print("huizhoushitou")
    print(commandString)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
        <string xmlns="CyntecMES">OK;OK</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/UploadFileToFTP")
def jinghua_v2_2(data=Body(...)):
    print("-----------精华 上传图片")
    print(data[:1000])

    ret_str = """OK"""
    return Response(content=ret_str, media_type="application/xml")


@app.get("/xike1")
def xike1(Email=Param("default"), Pwd=Param("d")):
    print("西可1")
    print(Email)
    print(Pwd)

    return {"result": "this is my token"}


@app.post("/xike2")
def xike2(data=Body("default")):
    print("西可2")
    print(data)

    return {"result": "OK", "msg": "OK"}


@app.post("/AOICheck")
def xinzhaodian1(data=Body("default")):
    print("新兆电1")
    print(data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<AOIReutrn xmlns="http://scd.webservice.cn/">
  <Status>0</Status>
  <msg>ok</msg>
  <wo>sdfsdf</wo>
  <skuno>sdfsdf</skuno>
</AOIReutrn>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/AOISave")
def xinzhaodian2(data=Body("default")):
    print("新兆电2")
    print(data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <AOIReutrn xmlns="http://scd.webservice.cn/">
      <Status>0</Status>
      <msg>ok</msg>
      <wo>sdfsdf</wo>
      <skuno>sdfsdf</skuno>
    </AOIReutrn>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/MesWebServer.asmx")
def xinzhaodian3(data=Body("default")):
    print("新兆电3")
    print(data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <AOIReutrn xmlns="http://scd.webservice.cn/">
      <a><b>
      <Status>0</Status>
      <msg>ok</msg>
      <wo>sdfsdf</wo>
      <skuno>sdfsdf</skuno>
      <b/></a>
    </AOIReutrn>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/http")
def http1(data=Body("default")):
    print("http------------")
    print(data)

    return {"status": "0", "msg": "ok"}


@app.post("/qiancheng")
def qiancheng(data=Body("default")):
    print("http------------")
    print(data)

    return {"Status": "1", "_ReturnMsg": "ok"}


@app.post("/qiancheng")
def qiancheng(data=Body("default")):
    print("http------------")
    print(data)

    return {"Status": "1", "_ReturnMsg": "ok"}


@app.post("/MakerWebService.asmx")
def chongqingjinhong(data=Body("default")):
    print("重庆金宏------------")
    print(data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <PCBTestResponse xmlns="http://tempuri.org/">
      <PCBTestResult>boolean</PCBTestResult>
    </PCBTestResponse>
  </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/single")
def single(data=Body("default")):
    print("合肥海尔.....")
    print(data)
    ret_str = "OK:OK"

    return Response(content=ret_str, media_type="application/xml")


@app.post("/swiftmom/api/clientLogin")
def lesheng1(data=Body("default")):
    print("乐生.....登录")
    print(data)

    return {
        "msgStr": "管理员|Swiftmes|edwin755473ABC|ZS|智硕|249||None|1",
        "msgId": 0,
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2NjY2ODA4NjcsInVzZXJuYW1lIjoiMTIifQ.Od_UO4N3nJodps_6m_sAk_gWp02cdE1bA7LR9w2puQU"
    }


@app.post("/swiftmom/api/getRelationPcbSeq")
def lesheng2(data=Body("default")):
    print("乐生.....获取条码")
    print(data)

    return {
        "msgStr": "ok",
        "msgId": 0,
        "data": [
            {
                "pcbSeq": "barcode02"
            },
            {
                "pcbSeq": "barcode03"
            }
        ]
    }


@app.post("/swiftmom/api/checkRoute")
def lesheng3(data=Body("default")):
    print("乐生.....条码校验")
    print(data)

    return {
        "msgStr": "ok",
        "msgId": 0
    }


@app.post("/swiftmom/api/createAOIData")
def lesheng4(data=Body("default")):
    print("乐生.....上传数据")
    print(data)

    return {
        "message": "ok",
        "result": 0
    }


@app.post("/swiftmom/api/createRoute")
def yuenandinghong():
    return {
        "msgId": 0,
        "msgStr": "ok"
    }


@app.post("/mrs/createRoute")
def aixun(data=Body("default")):
    print("爱讯.....上传数据")
    print(data)

    return {
        "msgStr": "ok",
        "msgId": 0
    }


@app.get("/mrs/createRoute")
def aixun(data=Body("default")):
    print("爱讯.....上传数据")
    print(data)

    return {
        "msgStr": "ok",
        "msgId": 0
    }


@app.get("/mrs/uploadSn")
def aixun(data=Body("default")):
    print("上传sn")
    print(data)

    return {
        "msgStr": "ok",
        "msgId": 0
    }


@app.get("/mrs/checkRoute")
def aixun(data=Body("default")):
    print("条码校验")
    print(data)

    # return {
    #     "msgStr": "[DIP-CJ]插件测试结果不通过！",
    #     "msgId": 1
    # }

    return {
        "msgStr": "ok",
        "msgId": 0
    }


def getRotatedAngleFromM33(M):
    """
    获取旋转角度
    :param M:
    :return:
    """
    tmpX = pow(M[0][0], 2) + pow(M[1][0], 2)
    if (tmpX <= 0):
        tmpX = 0
    else:
        tmpX = math.sqrt(tmpX)
    tmpY = pow(M[0][1], 2) + pow(M[1][1], 2)
    if (tmpX <= 0):
        tmpY = 0
    else:
        tmpY = math.sqrt(tmpY)
    angleBySin = 0
    if (0.0 == tmpY):
        angleBySin = 0
    else:
        angleBySin = math.asin(M[0][1] / tmpY)
    angleByCos = 0
    if (0.0 == tmpX):
        if (angleBySin < 0):
            angleByCos = -0.5 * math.pi
        else:
            angleByCos = 0.5 * math.pi
    else:
        angleByCos = math.acos(M[0][0] / tmpX)
    if (angleBySin < 0):
        angleByCos = - angleByCos
    angleByCos = (round(360 + (180 * angleByCos / math.pi))) % 360
    return angleByCos


# @app.get("/test001")
# def open_cv_test():
#     print("-------------test 001")
#     # ret = cv2.imread("/home/<USER>/Desktop/mes_code/common_mes_project/test_data/COMP1436_1436.png")
#     # print(ret)
#     xy_origin = [[157.3015, 53.2901], [153.5461, 57.0397], [-0.4099, 1.1808], [-1.5544, -1.043], [-1.3683, 1.1715],
#                  [-0.0005, -0.0009], [-0.2983, -1.0244], [-0.2424, 0.027]]
#
#     xy_offset = [[157.2559, 53.1261], [153.4281, 57.1191], [-0.4099, 1.1808], [-1.6996, -0.643], [-1.3683, 1.1715],
#                  [-0.1122, 0.8668], [-0.2983, -1.0244], [-0.2424, 0.027]]
#
#     pts1 = np.float32(xy_origin)
#     pts2 = np.float32(xy_offset)
#
#     M = cv2.findHomography(pts1, pts2)
#     ret = getRotatedAngleFromM33(M[0])
#     print("ret", ret)
#
#     return {"ok": "ok"}
#
#
# @app.post("/cal_theta_v3")
# def cal_theta_v3(data=Body("default")):
#     """
#     计算旋转角度
#     """
#     print("---------计算旋转角度")
#     print("data", data)
#     xy_origin = data.get("xy_origin")
#     xy_offset = data.get("xy_offset")
#
#     pts1 = np.float32(xy_origin)
#     pts2 = np.float32(xy_offset)
#     #
#     print("角度计算前")
#     M = cv2.findHomography(pts1, pts2)
#     print("角度计算后")
#     theta = getRotatedAngleFromM33(M[0])
#
#     return {"status": "ok", "data": theta}


hengrun_ret = {
    "status": "PASS",
    "message": "OK"
}


@app.post("/mes/service/auto/authToken/queryWorkOrderInfo")
def hengrun1(data=Body("default")):
    print("恒润1-----------", data)

    # ret = {
    #     "status": "PASS",
    #     "message": "获取产品工单信息成功",
    #     "content": {
    #         "pdLineList": [
    #             {
    #                 "pdList": [
    #                     {
    #                         "name": "Inalfa Ford U375 SCU|55003504D00|600600107",
    #                         "id": "HD0B2661EDB6C4585A5E9746F72474A20",
    #                         "workOrderList": [
    #                             {
    #                                 "name": "11028222",
    #                                 "id": "H2E33083074D14277AA0BA47919040E9C"
    #                             },
    #                             {
    #                                 "name": "1880000101",
    #                                 "id": "HCDEC471E39BA4428B5DA0CFFD45D32D5"
    #                             },
    #                             {
    #                                 "name": "621092201",
    #                                 "id": "H395D4006A9264D2EAF51CA52A757D60E"
    #                             }
    #                         ]
    #                     },
    #                     {
    #                         "name": "Inalfa Geely CX11 SCU总成|55005889B00|600600423",
    #                         "id": "H3E49971BB7D44E59A6864217C06B239E",
    #                         "workOrderList": [
    #                             {
    #                                 "name": "11028223",
    #                                 "id": "HC3D501BC2DB6415A9DBD432E3CCA3B36"
    #                             }
    #                         ]
    #                     },
    #                     {
    #                         "name": "Inalfa Geely CX11 SCU总成|55005889B00|600600412",
    #                         "id": "H3E49971BB7D44E59A6864217C06B239E",
    #                         "workOrderList": [
    #                             {
    #                                 "name": "11028223",
    #                                 "id": "HC3D501BC2DB6415A9DBD432E3CCA3B36"
    #                             }
    #                         ]
    #                     }
    #                 ],
    #                 "name": "APCU",
    #                 "id": "HC3FDCC9BD10346E480C781A9E05EF541"
    #             },
    #             {
    #                 "pdList": [
    #                     {
    #                         "name": "左门控开关控制器（豪华、遥控）|600110092|600110092",
    #                         "id": "H2C793B09364A434F852D1A1D52D89B89",
    #                         "workOrderList": [
    #                             {
    #                                 "name": "11028381",
    #                                 "id": "H27DC21E7D5AC465F8A24342F519E596F"
    #                             },
    #                             {
    #                                 "name": "31001947",
    #                                 "id": "H281502A7083248A7A8DDE3E481C7D000"
    #                             }
    #                         ]
    #                     },
    #                     {
    #                         "name": "遥控器_遥控端总成|600110096|600110096",
    #                         "id": "H5A201D296E034A9A87FFE37A6FADC21A",
    #                         "workOrderList": [
    #                             {
    #                                 "name": "11028374",
    #                                 "id": "H89600452996E46E8B23FBECA2DA6DDC7"
    #                             },
    #                             {
    #                                 "name": "31001948",
    #                                 "id": "HAEABACF0318F41A19A1E2A9F4FAE84F1"
    #                             }
    #                         ]
    #                     },
    #                     {
    #                         "name": "集成式司机侧开关控制器（Driver3 两门）|4061966C4|600600311",
    #                         "id": "H6D93BD07934343B997205647A906158A",
    #                         "workOrderList": [
    #                             {
    #                                 "name": "0000001004",
    #                                 "id": "H007F4D68001840839D283A7A0492F365"
    #                             },
    #                             {
    #                                 "name": "11011912",
    #                                 "id": "H20C5D42AE8AE497E8A93E930BE4CDC13"
    #                             }
    #                         ]
    #                     }
    #                 ],
    #                 "name": "DES",
    #                 "id": "HF839D8577299479AA701C44951EF881C"
    #             }
    #         ]
    #     }
    # }
    # ret = {
    #     "status": "PASS",
    #     "message": "获取产品工单信息成功",
    #     "content": {
    #         "pdLineList": [{
    #             "pdList": [{
    #                 "name": "600160001|CD4",
    #                 "workOrderList": [{
    #                     "name": "6006001654",
    #                     "id": "H2A87EF672AC24A55A64C74C2AD48647A"
    #                 }, {
    #                     "name": "6006001655",
    #                     "id": "HDD57D8883E1C4489B25D792E5AF51A85"
    #                 }],
    #                 "id": "HCC93C34B199942D9810D683D00CB3A97"
    #             }],
    #             "name": "天窗产品",
    #             "id": "H907063FF1EB74024BFA300E2D669F499"
    #         }]
    #     }
    # }

    with open("./data.json", "r") as f:
        data = json.load(f)

    return data


@app.post("/mes/service/auto/authToken/queryDeviceProcessInfo")
def hengrun2(data=Body("default")):
    print("恒润2----------", data)

    ret = {
        "status": "PASS",
        "message": "获取工序信息成功",
        "content": {
            "processList": [
                {
                    "workstationCode": "220002A07W07S01",
                    "workCenterCode": "220002A07W07",
                    "processId": "HB4FFC00B0C0E4DBFABAAAF6ED6841914",
                    "processName": "EOL测试",
                    "workstationName": "ADAS_EOL测试工作站1",
                    "deviceCode": "HRSHSYS0020001",
                    "workCenterName": "ADAS_EOL测试工作中心",
                    "deviceName": "ADAS_EOL测试设备1"
                },
                {
                    "workstationCode": "220002A07W07S01",
                    "workCenterCode": "220002A07W07",
                    "processId": "HFA7BFFC99C6A4E7095F6B192030D4AD6",
                    "processName": "高温测试",
                    "workstationName": "ADAS_EOL测试工作站1",
                    "deviceCode": "HRSHSYS0020001",
                    "workCenterName": "ADAS_EOL测试工作中心",
                    "deviceName": "ADAS_EOL测试设备1"
                }
            ]
        }
    }

    return ret


@app.post("/mes/service/auto/authToken/userInfoAuth")
def hengrun3(data=Body("default")):
    print("恒润3-------", data)

    return {
        "status": "PASS",
        "message": "用户信息认证成功",
        "content": {
            "userInfo": {
                "deptName": "大数据平台部",
                "updateDate": "2018-06-21 00:00:25",
                "mobilePhone": "18288888888",
                "phone": "-2517",
                "name": "张三",
                "roleName": "系统管理员",
                "id": "HA9F4705ABEC942708F6A2EF06A22BDEF",
                "jobNumber": "*********",
                "email": "<EMAIL>",
                "createDate": "2018-04-20 11:09:38",
                "username": "san.zhang"
            },
            "IOVTOKEN": "-5gof2cbckdf26jpou9iacnuc98e0n6bu"
        }
    }


@app.post("/mes/service/auto/configProcess")
def hengrun4(data=Body("default")):
    print("恒润4-------", data)

    return {
        "status": "PASS",
        "message": "设置工序成功",
        "content": {
            "processKey": "-5dv290t5r8gq819bcj3v2jsk6mkkkane"
        }
    }


@app.post("/mes/service/auto/queryProductionInfo")
def hengrun5(data=Body("default")):
    print("恒润5----获取生产信息---", data)
    return {
        "content": {
            "productionInfo": [
                {
                    "name": "产品料号",
                    "value": "*********-V1500A",
                    "key": "pdPartNumber"
                },
                {
                    "name": "产品名称",
                    "value": "J6_车门控制器总成_低配_有铅",
                    "key": "pdName"
                },
                {
                    "name": "产品ERP",
                    "value": "*********",
                    "key": "pdCode"
                },
                {
                    "name": "BOM版本",
                    "value": "V150011034206",
                    "key": "bomVersion"
                },
                {
                    "name": "工单号",
                    "value": "1103671701",
                    "key": "workOrderNum"
                },
                {
                    "name": "工单总数",
                    "value": "600",
                    "key": "workOrderTotal"
                },
                {
                    "name": "生产批次号",
                    "value": "200304AA",
                    "key": "batchNum"
                },
                {
                    "name": "客户零件号",
                    "value": "3746070AA01/B",
                    "key": "partNo"
                },
                {
                    "name": "841100410",
                    "value": "1900",
                    "key": "841100410"
                },
                {
                    "name": "890005328",
                    "value": "2.4.0",
                    "key": "890005328"
                },
                {
                    "name": "890005327",
                    "value": "2.3.0",
                    "key": "890005327"
                },
                {
                    "name": "890005326",
                    "value": "2.3.0",
                    "key": "890005326"
                },
                {
                    "name": "890005325",
                    "value": "2.4.0",
                    "key": "890005325"
                },
                {
                    "name": "890003525",
                    "value": "1.0",
                    "key": "890003525"
                },
                {
                    "name": "890003516",
                    "value": "1.0",
                    "key": "890003516"
                },
                {
                    "name": "890000536",
                    "value": "2.4",
                    "key": "890000536"
                },
                {
                    "name": "890000113",
                    "value": "2.3",
                    "key": "890000113"
                },
                {
                    "name": "工序名称",
                    "value": "激光打标",
                    "key": "processName"
                },
                {
                    "name": "本工序合格数量",
                    "value": "33",
                    "key": "processSuccess"
                },
                {
                    "name": "本工序失败数量",
                    "value": "0",
                    "key": "processFail"
                }
            ]
        },
        "message": "获取生产信息成功",
        "status": "PASS"
    }


@app.post("/mes/service/auto/validateNumber")
def hengrun6(data=Body("default")):
    print("恒润6-------", data)
    return {
        "status": "FAIL",
        "message": "条码校验失败,产品任务不在此工序，当前任务【PCBA上料检验】number【841600010170628WSAA0001】",
        "content": {
            "result": "0"
        }
    }


@app.post("/mes/service/auto/completeTask")
def hengrun7(data=Body("default")):
    print("恒润7-------", data)

    return {
        "status": "PASS",
        "message": "软件注入过站成功",
        "content": {
            "result": True
        }
    }


@app.post("/mes/imsapi/rest/actions/regLogin")
def faleiao1(data=Body("default")):
    print("法雷奥 登录...", data)

    return {
        "result": {
            "return_value": 0,
            "sessionContext": {
                "sessionId": 2139220206309172,
                "persId": 128,
                "locale": "en_GB"
            }
        }
    }


@app.post("/mes/imsapi/rest/actions/trGetSerialNumberInfo")
def faleiao1(data=Body("default")):
    print("法雷奥 校验板式...", data)

    return {
        "result": {
            "return_value": 0,
            "serialNumberResultValues": [
                "VA58008",
                "1",
                "COMBO-Mahindra-VA58008-OP600-20220908"
            ]
        }
    }


@app.post("/mes/imsapi/rest/actions/customFunction")
def faleiao1(data=Body("default")):
    print("法雷奥 条码校验、上传测试数据", data)

    return {
        "result": {
            "return_value": 0,
            "outArgs": [
                "MES|ON",
                "SNR|!222440014!VA0068334B|1|0"
            ],
            "customErrorString": "CF : PASS"
        }
    }


@app.post("/stage-api/me/syncData/productUpdate")
def weimaisi1(data=Body("default")):
    print("威迈斯 条码校验", data)

    return {
        "code": "200",
        "msg": "success",
        "data": {
            "item": "2022916-04",
            "shopOrder": "2022916-04",
            "itemCode": "131066336",
            "itemBo": "ITEM:1001,131066336,1",
            "itemName": "制成板-VAILS60599C5Z1-滤波板",
            "scheduleNo": "None"
        }
    }


@app.post("/stage-api/auth/oauth/token")
def weimaisi0(data=Body("default")):
    print("威迈斯 登录", data)

    return {
        "access_token": "eyJ0eXAiOiJKV1QiL..........",
        "expires_in": 86400000
    }


@app.post("/stage-api/me/syncData/programTest")
def weimaisi2(data=Body("default")):
    print("威迈斯 上传测试数据", data)

    return {
        "code": "200",
        "msg": "success",
        "data": {
            "shopOrder": "2022916-04",
            "itemCode": "131066336",
            "itemBo": "ITEM:1001,131066336,1",
            "itemName": "制成板-VAILS60599C5Z1-滤波板",
            "scheduleNo": "None"
        }
    }


@app.post("/TestStationCycleStart")
def rujing1(data=Body("default")):
    print("儒竞data上传1", data)

    return {
        "Result": "OK",
        "ErrorCode": "OK",
        "Message": "OK"
    }


@app.post("/TestStationCycleEnd")
def rujing2(data=Body("default")):
    print("儒竞data上传2", data)

    return {
        "Result": "OK",
        "ErrorCode": "OK",
        "Message": "OK"
    }


@app.post("/TestStationDataCycleEnd")
def rujing3(data=Body("default")):
    print("儒竞data上传3", data)

    return {
        "Result": "OK",
        "ErrorCode": "OK",
        "Message": "OK"
    }


@app.post("/MachineStatus")
def rujing3(data=Body("default")):
    print("儒竞data上传4", data)

    return {
        "Result": "OK",
        "ErrorCode": "OK",
        "Message": "OK"
    }


@app.post("/NHAPI/api/AIPInsertAOIData")
def ninghong(data=Body("default")):
    print("宁虹", data)

    return {
        "IsOK": "true",
        "Message": "OK"
    }


@app.get("/api/mes.common/dsWipJobreport/getJobId")
def yueyangdongsong(data=Param("default")):
    print("岳阳东颂", data)

    return {"success": True,
            "message": "",
            "code": 200,
            "result": {"date": "2022-12-06 00:00:00", "jobId": 1297, "mo": "MO222120200007", "pline": "L01"},
            "timestamp": 1670301847174}


@app.post("/api/mes.common/dsWipJobreport/data")
def yueyangdongsong2(data=Body("default")):
    print("岳阳东颂2", json.dumps(data, ensure_ascii=False, indent=4))

    return {"success": True,
            "message": "",
            "code": 200,
            "result": {"date": "2022-12-06 00:00:00", "jobId": 1297, "mo": "MO222120200007", "pline": "L01"},
            "timestamp": 1670301847174}


@app.get("/api/mes.common/dsWipJobreport/getFullPCBA")
def yueyangdongsong2(data=Body("default")):
    print("岳阳东颂2", json.dumps(data, ensure_ascii=False, indent=4))

    return {"success": True, "message": "", "code": 200,
            "result": ["A00007596MO223121600022002415", "A00007596MO223121600022002416",
                       "A00007596MO223121600022002417", "A00007596MO223121600022002418",
                       "A00007596MO223121600022002419", "A00007596MO223121600022002420",
                       "A00007596MO223121600022002421", "A00007596MO223121600022002422"], "timestamp": 1702958016156}


@app.post("/GetAOITestReult")
def suzhoulangchao(data=Body("defualt")):
    print("苏州浪潮")
    print(data)
    return {"result": "OK", "resultMessage": "OK"}


@app.post("/SAPWeb.asmx")
def jingquanhua(data=Body("default")):
    print("京泉华")
    print(data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <syncAddSNWorkInfoResponse xmlns="http://tempuri.org/">
      <syncAddSNWorkInfoResult>OK</syncAddSNWorkInfoResult>
    </syncAddSNWorkInfoResponse>
  </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/api/datacollection/l_facelist/AddList")
def jinshengyang1(data=Body("default")):
    print("金升阳1")
    print(data)
    return {"code": 0, "data": "ok"}


@app.post("/api/datacollection/l_ngdetail/AddList")
def jinshengyang2(data=Body("default")):
    print("金升阳2")
    print(data)
    return {"code": 0, "data": "ok"}


@app.post("/stationservice.asmx")
def mianyangjiuzhou(data=Body("default")):
    print("绵阳九州")
    print(data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
        <ML_SS_DY_AOITestResponse xmlns="http://jiuzhoutech.com/">
          <ML_SS_DY_AOITestResult>true</ML_SS_DY_AOITestResult>
          <errMsg>ok</errMsg>
        </ML_SS_DY_AOITestResponse>
      </soap:Body>
    </soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.get("/mc/http/interface.ms")
def lanwei1(data=Param("default")):
    print("蓝微登录")
    print(data)

    return {"checkoutRes": "1", "name": "ok"}


@app.post("/haojiang/mc/http/interface.ms")
def haojiang(data=Param("default")):
    print("蓝微登录")
    print(data)

    return {"MSG": "OK", "RESULT": "OK"}


@app.post("/mc/http/interface.ms")
def lanwei2(data=Body("default"), model=Param("Device"), method=Param("")) -> dict:
    print('------------------------------')
    print(data)
    print("model", model)
    print("method", method)

    if method == "GetWorkstation":
        print("蓝微 获取工作中心")

        return {
            "result": "OK",
            "message": "",
            "roomList": [
                {
                    "roomCode": "SMT001",
                    "roomName": "SMT 车间",
                    "lineList": [
                        {
                            "lineCode": "SMT-28",
                            "lineName": "SMT-28",
                            "workList": [
                                {
                                    "workStationCode": "SMT-28-02",
                                    "workStationName": "SPI"
                                },
                                {
                                    "workStationCode": "SMT-28-02-122",
                                    "workStationName": "SPI-11"
                                },
                            ]
                        },
                        {
                            "lineCode": "SMT-28-1",
                            "lineName": "SMT-28-1",
                            "workList": [
                                {
                                    "workStationCode": "SMT-28-02-1",
                                    "workStationName": "SPI-1"
                                }
                            ]
                        }
                    ]
                },
                {
                    "roomCode": "SMT001-88",
                    "roomName": "SMT 车间-88",
                    "lineList": [
                        {
                            "lineCode": "SMT-28-88",
                            "lineName": "SMT-28-88",
                            "workList": [
                                {
                                    "workStationCode": "SMT-28-02-88-1",
                                    "workStationName": "SPI-88-1"
                                },
                                {
                                    "workStationCode": "SMT-28-02-1-88-2",
                                    "workStationName": "SPI-1-88-2"
                                },
                            ]
                        },
                        {
                            "lineCode": "SMT-28-1-88",
                            "lineName": "SMT-28-1-88",
                            "workList": [
                                {
                                    "workStationCode": "SMT-28-02-1-88-3",
                                    "workStationName": "SPI-1-88-3"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    elif method == "GetWO":
        return {
            "result": "OK",
            "dataList": [
                {
                    "PM_MO_NUMBER": "08-200000026623-SMT 第一面",
                    "PM_PROJECT_ID": "1",
                    "PM_AREA_SN": "SMT-8",
                    "PM_MODEL_CODE": "21001-1",
                    "CPM_PRODUCT_MODEL": "ZB1898-1",
                    "PM_STATUS": "0"
                },
                {
                    "PM_MO_NUMBER": "08-200000026623-SMT 第二面",
                    "PM_PROJECT_ID": "2",
                    "PM_AREA_SN": "SMT-8",
                    "PM_MODEL_CODE": "21001-2",
                    "CPM_PRODUCT_MODEL": "ZB1898-2",
                    "PM_STATUS": "0"
                },
                {
                    "PM_MO_NUMBER": "08-200000024121-SMT 第二面",
                    "PM_PROJECT_ID": "3",
                    "PM_AREA_SN": "SMT-8",
                    "PM_MODEL_CODE": "21001-3",
                    "CPM_PRODUCT_MODEL": "ZB1898-3",
                    "PM_STATUS": "4"
                }
            ],
            "message": ""
        }

    elif method == "GetMO":
        return {
            "result": "OK",
            "dataList": [
                {
                    "PROJECT_ID": "2000000267391",
                    "PRODUCT_COUNT": 1258,
                    "CPM_PRODUCT_MODEL": "P2921-10S1P1",
                    "PRODUCT_MATERIAL": "21098-00080551",
                    "PRODUCT_NAME": "离线烧录 IC,贴片,261H005(P2921-10S1P)-07,组件 11"
                },
                {
                    "PROJECT_ID": "2000000267392",
                    "PRODUCT_COUNT": 1258,
                    "CPM_PRODUCT_MODEL": "P2921-10S1P2",
                    "PRODUCT_MATERIAL": "21098-00080552",
                    "PRODUCT_NAME": "离线烧录 IC,贴片,261H005(P2921-10S1P)-07,组件 12"
                },
                {
                    "PROJECT_ID": "2000000267393",
                    "PRODUCT_COUNT": 1258,
                    "CPM_PRODUCT_MODEL": "P2921-10S1P3",
                    "PRODUCT_MATERIAL": "21098-00080553",
                    "PRODUCT_NAME": "离线烧录 IC,贴片,261H005(P2921-10S1P)-07,组件 13"
                }
            ],
            "message": ""
        }

    elif method == "CheckSN":
        return {
            "result": "OK",
            "message": "不符合流程管控",
            "eventCode": "thisisafakeeventcode",
            "pcList": [
                {
                    "seq": "1",
                    "barcode": "小板二维码",
                    "result": "NG",
                    "message": "不符合流程管控"
                }
            ]
        }

    elif method == "MDataSave":
        print("不过站不校验")
    elif method == "GetProc":
        return {
            "result": "OK",
            "dataList": [
                {
                    "procCode": "KXN",
                    "procName": "PACK性能",
                },
                {
                    "procCode": "ICT",
                    "procName": "ICT测试",
                },
                {
                    "procCode": "XLH",
                    "procName": "PCM老化",
                },
            ],
            "message": ""
        }

    return {
        "result": "OK",
        "message": ""
    }


@app.post("/SavePackData")
def lanwei_package(data=Body("default")):
    print("上传包装数据")
    print(data)

    return {
        "Result": "OK",
        "Message": "保存成功"
    }


gd_data = {}


@app.post("/CheckPackStatus")
def lanwei_package(data=Body("default")):
    print("检查包装状态")
    print(data)

    count = gd_data.get("count", 0)
    count += 1
    gd_data['count'] = count
    print(f"count: {count}")

    if count >= 2:
        gd_data["count"] = 0
        return {
            "Result": "OK",
            "Message": "保存成功"
        }

    return {
        "Result": "NG",
        "Message": "保存成功"
    }


@app.post("/webservice/mesateapi.asmx/ATECommandCode")
def langte(commandString=Form("default")):
    print("朗特......")
    print(commandString)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://mesateapi.com/">OK;</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/uploadFile")
def uploadFile(file: bytes = File(...), code=Form("default"), content_type=Header("default")):
    print("上传文件")
    print(file, type(file))
    print(code)

    print(content_type)

    with open("2222.jpg", "wb") as f:
        f.write(file)

    # with open('/home/<USER>/Desktop/2345.jpg', "wb") as f:
    #     f.write(file)

    return {"result": "ok"}


# @app.post("/mc/http/interface.ms")
# def dalanminghai2(
#         file: bytes = File(b'default data'),
#         model=Param("default model"),
#         method=Param("default method"),
#         data=Body("default body"),
#
# ):
#     print('--------------德兰明海----------')
#     # print(file)
#     print("len", len(file))
#     print(model)
#     print(method)
#     print(data)
#
#     return {
#         "MSG": "文件上传成功",
#         "STATUS": "OK"
#     }


@app.post("/sip/equipmentIntegrate/login")
def xinwangda1(data=Body("default")):
    print("欣旺达 登录")
    print(data)

    return {
        "CODE": 200,
        "DATA": "F4186986C5B65A66E055000000000001",
        "MESSAGE": None,
        "RESULT": "true"
    }


@app.post("/sip/equipmentIntegrate/checkSn")
def xinwangda2(data=Body("default")):
    print("欣旺达 条码校验")
    print(data)

    return {
        "CODE": 200,
        "DATA": "OK",
        "MESSAGE": None,
        "RESULT": "true"
    }


@app.get("/sip/sfcTest/getTestId")
def xinwangda3(data=Body("default")):
    print("欣旺达 get test id")
    print(data)

    return {
        "CODE": 200,
        "DATA": "17527321727",
        "MESSAGE": None,
        "RESULT": "true"
    }


@app.post("/sip/equipmentIntegrate/doTooling")
def xinwangda4(data=Body("default")):
    print("欣旺达 条码绑定")
    print(data)

    return {
        "CODE": 200,
        "DATA": "绑定成功，当前已绑定条码数量：1",
        "MESSAGE": None,
        "RESULT": "true"
    }


@app.get("/sip/equipmentIntegrate/emappingCheck/{sn}")
def xinwangda5(sn, data=Body("default")):
    print("欣旺达 获取不良点位")
    print(data)
    print("sn", sn)

    return {
        "CODE": 200,
        "DATA": [
            {
                "sipNo": 12,
                "status": 1
            }
        ],
        "MESSAGE": None,
        "RESULT": "true"
    }


@app.post("/sip/sfcTest/doRecordData")
def xinwangda6(data=Body("default")):
    print("欣旺达 上传测试数据")
    print(data)

    return {
        "CODE": 200,
        "DATA": "OK",
        "MESSAGE": None,
        "RESULT": "true"

    }


@app.post("/sip/sfcTest/doReceivingImgPath")
def xinwangda7(data=Body("default")):
    print("欣旺达 上传测试图片")
    print(data)

    return {
        "CODE": 200,
        "DATA": "OK",
        "MESSAGE": None,
        "RESULT": "true"
    }


@app.post("/LoginCheck")
def xinwangdatufu1(data=Body("default")):
    print("欣旺达 登录")
    print(data)

    return {
        "status": "true",
        "sessionid": "123456",
        "description": "上传成功",
        "machineID": "Ls0001"
    }


@app.post("/StationCheck")
def xinwangdatufu2(data=Body("default")):
    print("欣旺达 产品进站检查接口")
    print(data)

    return {
        "productSn": "Barcode123456",
        "description": "工序检查成功",
        "status": "true",
    }


@app.post("/ProductUpload")
def xinwangdatufu3(data=Body("default")):
    print("欣旺达 产品数据上传过站")
    print(data)

    return {
        "productSn": "Barcode123456",
        "description": "过站成功",
        "ngItem": None,
        "status": "true"
    }


@app.post("/DeviceStatusAndon")
def xinwangdatufu3(data=Body("default")):
    print("欣旺达 DeviceStatusAndon")
    print(data)

    return {
        "Status": "true",
        "Result": "上传成功",
        "TestResult": "上传成功"
    }


@app.post("/DeviceParameterAlarmAndon")
def xinwangdatufu3(data=Body("default")):
    print("欣旺达 DeviceParameterAlarmAndon")
    print(data)

    return {
        "Status": "true",
        "Result": "上传成功",
        "TestResult": "上传成功"
    }


@app.post("/api/blade-auth/oauth/token")
def pudexinxing1(data=Body("default")):
    print(f"普德新星1: {data}")

    return {
        "info": {
            "_postman_id": "caaa66d2-582f-4bf5-b9df-d26cb993be79",
            "name": "oauth2",
            "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        "item": [
            {
                "name": "获取token",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Authorization",
                            "value": "Basic c2FiZXI6c2FiZXJfc2VjcmV0",
                            "type": "text"
                        },
                        {
                            "key": "Tenant-Id",
                            "value": "888888",
                            "type": "text"
                        },
                        {
                            "key": "Content-Type",
                            "value": "application/x-www-form-urlencoded",
                            "type": "default"
                        }
                    ],
                    "body": {
                        "mode": "urlencoded",
                        "urlencoded": [
                            {
                                "key": "grant_type",
                                "value": "password",
                                "type": "text"
                            },
                            {
                                "key": "username",
                                "value": "admin",
                                "type": "text"
                            },
                            {
                                "key": "password",
                                "value": "e10adc3949ba59abbe56e057f20f883e",
                                "type": "text"
                            },
                            {
                                "key": "scope",
                                "value": "all",
                                "type": "text"
                            },
                            {
                                "key": "tenantId",
                                "value": "888888",
                                "type": "text"
                            }
                        ]
                    },
                    "url": {
                        "raw": "http://*************:8000/api/blade-auth/oauth/token",
                        "protocol": "http",
                        "host": [
                            "192",
                            "168",
                            "1",
                            "196"
                        ],
                        "port": "8000",
                        "path": [
                            "api",
                            "blade-auth",
                            "oauth",
                            "token"
                        ]
                    }
                },
                "response": []
            }
        ]
    }


@app.post("/api/mes-product/public/station/center/test")
def pudexinxing2(data=Body("default")):
    print(f"普德新星2: {data}")
    return {
        "code": 200,
        "success": True,
        "data": None,
        "msg": "产品序列号xxx过站成功"
    }


@app.post("/IMS/GET_CHECK_LB")
def shenzhensongsheng(data=Body("default")):
    print("深圳崧盛....", data)
    return {
        "ERR_CODE": "NG",
        "ERR_MSG": "条码错误",
        "LB_ID": "20210206004"
    }


@app.post("/IMS/RECEIVE_LB")
def shenzhensongsheng1(data=Body("default")):
    print("深圳崧盛....", data)
    return {
        "ERR_CODE": "OK",
        "ERR_MSG": "",
        "LB_ID": "20210206004"
    }


@app.post("/IMS/UploadStatus")
def shenzhensongsheng2(data=Body("default")):
    print("深圳崧盛....", data)
    return {
        "ERR_CODE": "OK",
        "ERR_MSG": "",
        "LB_ID": "20210206004"
    }


@app.post("/mes/api/upload_data")
def kangguan(data=Body("default")):
    """"""
    print("康冠", data)
    return {
        "code": 200,
        "success": True,
        "message": "ok"
    }


@app.post("/mes")
def kangguan(data=Body("default")):
    """"""
    print("mes消息", data)
    return {
        "code": 200,
        "success": True,
        "message": "ok"
    }


@app.post("/WebService/AOITestService.asmx/SC_MES_SaveQuantityData")
def chongqingjiuzhou(xmlData=Form("default")):
    """"""
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <SC_MES_SaveQuantityDataResponse xmlns="http://tempuri.org/">
      <SC_MES_SaveQuantityDataResult>OK</SC_MES_SaveQuantityDataResult>
    </SC_MES_SaveQuantityDataResponse>
  </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/boda/v2")
def boda_v2(data=Body(default="")):
    print("boda")
    print(data)

    return {"result": True, "msg": "数据上传成功", "DATA": None, "status": "0", "count": 0}


@app.get('/api/v1/login/{username}/{pwd}')
def juming1(username, pwd):
    print("username", username)
    print("pwd", pwd)

    return {
        "resultCode": "200",
        "resultMsg": "ok",
        "userName": "leichen-test",
        "accessTocken": "fake-token-123456"
    }


@app.put('/api/v1/production_status_test')
def juming2(data=Body("default"), token=Param("default")):
    print("data", data)
    print("token", token)

    return {
        "resultCode": "200",
        "resultMsg": "ok",
    }


@app.post('/test001')
def juming2(data=Body("default"), token=Param("default")):
    print("data", data)
    print("token", token)

    return {
        "resultCode": "200",
        "resultMsg": "ok",
    }


@app.post("/mesinterface.asmx")
def xinwangda21(data=Body("default")):
    print("data", data)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
 <soap:Body>
 <WIPTESTResponse xmlns="WWW.SUNWODA.COM">
  <WIPTESTResult>TRUE:OKKKKK</WIPTESTResult>
 </WIPTESTResponse>
 </soap:Body>
</soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/xiongmao")
def xiongmao(data=Body("default")):
    print("data", data)
    return {
        "HEAD":
            {
                "H_ID": "A001",
                "H_RET": "00000",
                "H_MSG": "OK"
            },
        "MAIN":
            {
                "G_WOID": "",
                "G_MBSN": ""
            }
    }


@app.post("/mes/cloud/v1/auth/oauth/token")
def zhuoruiyuan():
    print("登录中。。。")
    return {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MTU3NTk4MTIsInVzZXJfbmFtZSI6IjQ4MjMiLCJhdXRob3JpdGllcyI6WyJFVkVSWU9ORSJdLCJqdGkiOiJkYmNiNjU1Zi00NWY3LTQ2MTQtOTAzNC1hNmM3OGYyZTlkNmQiLCJjbGllbnRfaWQiOiJtZXMtYXBwIiwic2NvcGUiOlsiYXBwIl19.6_Xjm5zDmhMIQbRq_hH2PSxEPOhpLBqASzTGWwf-WLY",
        "token_type": "bearer",
        "expires_in": 31622399,
        "scope": "app",
        "jti": "dbcb655f-45f7-4614-9034-a6c78f2e9d6d"
    }


@app.get("/mes/cloud/v1/module/product/api/get-Sn-Tooling")
def zhuoruiyuan1(authorization=Header("default")):
    print("获取条码中", authorization)
    return {
        "data": [
            "WO202305-01-016",
            "WO202305-01-017"
        ],
        "message": "查询成功",
        "stateCode": "SYSTEM_SUCCESS"
    }


@app.post("/mes/cloud/v1/module/common/api/oss/minio/image/upload/files")
def zhuoruiyuan2(file: bytes = File(...)):
    print("上传图片", len(file))
    return [
        {
            "fileName": "022cfedc39b24e978929220145ebdfe1.png",
            "url": "http://192.168.1.209:8788/unsafe/image/022cfedc39b24e978929220145ebdfe1.png"
        }
    ]


@app.post("/mes/cloud/v1/module/product/api/station/center/machine/save")
def zhuoruiyuan3(body=Body("default")):
    print("上传测试数据", body)
    return {"message": "WO202305-01-017过站成功！", "messageCode": "COMMON.OVER_STATION_SUCCESS|WO202305-01-017",
            "stateCode": "SYSTEM_SUCCESS"}


@app.post("/cookie1")
def test_cookie(res: Response):
    res.set_cookie("user_id", "12345")

    return {"message": "ok"}


@app.post("/cookie2")
def test_cookie1(user_id=Cookie(None)):
    print("user_id", user_id)
    return {"message": "ok", "user_id": user_id}


@app.get("/manufacturing-app/ResourceAPIController/checkIn")
def guruiwate():
    ret_str = """<Response Result="S" ErrorMsg=" 产 品 [112050] 不 能 在 当 前 工 序 生 产 , 应 流 向 [WIFI 功 能 测 试 ] 工 序 "
AssociatedBarcode= "112050;112051;112052;112053"
ShopOrder="202212220001"
Item="MR00.0011000"
ItemDesc="监控设备-国际 ShineWiFi-X 整机" />"""

    return Response(content=ret_str, media_type="application/xml")


@app.get("/manufacturing-app/ResourceAPIController/checkOut")
def guruiwate2():
    ret_str = """<Response Result="S" ErrorMsg=" 产 品 [112050] 不 能 在 当 前 工 序 生 产 , 应 流 向 [WIFI 功 能 测 试 ] 工 序 "
AssociatedBarcode= "112050;112051;112052;112053"
ShopOrder="202212220001"
Item="MR00.0011000"
ItemDesc="监控设备-国际 ShineWiFi-X 整机" />"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/Service.asmx/DataCollection")
def guruiwate3(parameters=Form(""), site=Form("")):
    print("------------------")
    print(parameters)
    print("------------------")
    print(site)

    p_json = json.loads(parameters)
    print(type(p_json))
    ret_str = """<Response Result="S" ErrorMsg=" 产 品 [112050] 不 能 在 当 前 工 序 生 产 , 应 流 向 [WIFI 功 能 测 试 ] 工 序 "
AssociatedBarcode= "112050;112051;112052;112053"
ShopOrder="202212220001"
Item="MR00.0011000"
ItemDesc="监控设备-国际 ShineWiFi-X 整机" />"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/manufacturing-app/ResourceAPIController.asmx/uploadResrceStandardURL")
def guruiwate4():
    ret_str = """<Response Result="S" ErrorMsg=" 产 品 [112050] 不 能 在 当 前 工 序 生 产 , 应 流 向 [WIFI 功 能 测 试 ] 工 序 "
AssociatedBarcode= "112050;112051;112052;112053"
ShopOrder="202212220001"
Item="MR00.0011000"
ItemDesc="监控设备-国际 ShineWiFi-X 整机" />"""

    return Response(content=ret_str, media_type="application/xml")


@app.get("/manufacturing-app/ResourceAPIController.asmx/uploadResrceStandardURL")
def guruiwate4():
    ret_str = """<Response Result="S" ErrorMsg=" 产 品 [112050] 不 能 在 当 前 工 序 生 产 , 应 流 向 [WIFI 功 能 测 试 ] 工 序 "
AssociatedBarcode= "112050;112051;112052;112053"
ShopOrder="202212220001"
Item="MR00.0011000"
ItemDesc="监控设备-国际 ShineWiFi-X 整机" />"""

    return Response(content=ret_str, media_type="application/xml")


@app.get("/WebForm1.aspx")
def changzhoudaoqi():
    return {"code": 200, "message": "ok"}


@app.post("/taicangtongwei2")
def taicangtongweiv2():
    ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
            <ns2:callMesServiceResponse xmlns:ns2="http://twsz.com/">
                <callMesServiceResult>OK</callMesServiceResult>
            </ns2:callMesServiceResponse>
        </soap:Body>
    </soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.get("/OrBitWCFServiceR13test/OrBitWebAPI.ashx")
def yuhua1():
    return "fake_token"


@app.post("/OrBitWCFServiceR13/OrBitWebAPI.ashx")
def yuhua2():
    return {
        "SQLDataSet": [
            {
                "LotSN": "22105170002990;22105170002996;22105170002997",
                "ReturnMessage": "成功",
                "ReturnNum": "200"
            }
        ]
    }


@app.post("/MyService.asmx/ADCSubmitTestData")
def yuhua3():
    ret_str = '<string xmlns="http://tempuri.org/">-1_[MES]IP[127.0.0.1]不存在绑定的规程，请检查</string>'
    return Response(content=ret_str, media_type="application/xml")


@app.post("/bodav2/test")
def bodav2(body=Body("111")):
    print(body)
    print(f"接收到的数据长度：{len(body)}")
    return {"code": 200, "msg": "ok"}


@app.get("/OrBitWCFServiceR16/OrBitWebAPI.ashx")
def shouhang1(API=Param("login")):
    if API == "login":
        return "BB2F31094752D25CA184A721CD13070C490BD5BBDE5EAAEFB5EBAF3496E722F288882ACC73785D2FFD478C1D52D7880BFF052483E673526FAD3668DDD47DE482092088476109992C6D4FD59000AEADFA1A59D94336F37535"

    elif API == "CheckRouter":
        return {
            "SQLDataSet": [{"Result": "OK", "ErrorMSG": "执行成功"}]
        }
    elif API == "UploadTestResult":
        return {
            "SQLDataSet": [{"Result": "OK", "ErrorMSG": "执行成功"}]
        }
    elif API == "UploadAlarmInfo":
        return {
            "SQLDataSet": [{"Result": "OK", "ErrorMSG": "执行成功"}]
        }

    return {
        "error": "未定义的接口"
    }


@app.post("/leanmes/WebService/BasalWebservice.asmx/AOICheck")
def shiyida1():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">{"Status": "0", "msg": "OK"}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/leanmes/WebService/BasalWebservice.asmx/AOISave")
def shiyida2():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string xmlns="http://tempuri.org/">{"Status": "0", "msg": "OK"}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/Bobcat/sfc_response.aspx")
def lixun1(c=Form("default")):
    if c == "ADD_RECORD":
        return Response(content="0 sfc_ok", media_type="application/xml")
    elif c == "QUERY_RECORD":

        str_data = """0 SFC_OK
tsid::DIP_三防AOI-1-1::unit_process_check=OK"""
        return Response(content=str_data, media_type="application/xml")

    return {"code": 200, "msg": "ok"}


@app.post("/rest/v1/sfc/getpalletcode/component-sn")
def juyi():
    return {
        "data": {
            "palletLongCode": "16",
            "componentSn": "S021421911680308432,S021421911680308433"
        },
        "status": "success",
        "code": 0,
        "msg": "总成信息查询成功"
    }


@app.post("/integrate/smtdata/saveAOIPCBResult")
def mengxun():
    return {"code": 200, "msg": "ok"}


@app.post("/mes/imsapi/rest/actions/trUploadFailureAndResultData")
def faleiao():
    return {
        "result": {
            "return_value": 0,
            "returnMsg": "ok"
        }
    }


@app.post("/MesService.asmx/CheckBarcode")
async def silianxianjin(request: Request):
    print("request-------------")
    xml_data = await request.body()
    print(xml_data)
    print("----")
    ret_str = """<data>
    <result>OK</result>
    <error></error>
</data>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/MesService.asmx/CreateWork")
async def silianxianjin(request: Request):
    print("request-------------")
    xml_data = await request.body()
    print(xml_data)
    print("----")
    ret_str = """<data>
    <result>OK</result>
    <error></error>
</data>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/api/MES/AOISPISngo")
async def dongguanlixun(body=Body("...")):
    print("--------------东莞立讯..........")
    print(body)

    return {"msg": "OK,Pass Qty:18,1次PASS，过站成功"}


@app.post("/TDC/DELTA_DEAL_TEST_DATA_I")
async def dongguantaida(body=Body("...")):
    print("--------------东莞台达..........")
    print(body)

    return {"code": 200, "msg": "ok"}


@app.post("/WIP/Complete2")
def changshazhongche():
    return {
        "errorcode": 0,
        "content": None,
        "info": None
    }


@app.post("/AMC/Complete2")
def yajingyuan1():
    return {
        "errorcode": 0,
        "content": None,
        "info": None
    }


@app.post("/AMC/check2")
def yajingyuan2():
    return {
        "errorcode": 0,
        "content": None,
        "info": None
    }


@app.post("/WebService/MesATEApi.asmx/CollectATESN")
def zhongqinag():
    ret_str = """OK"""

    return Response(content=ret_str, media_type="application/text")


@app.post("/SLEnergyGetSN.asmx")
def chuzhoulixun2():
    ret_str = """<string xmlns="MesWebService">{"Result": "OK", "Barcode": [{"BoardBarcode": "fake-sn1"}, {"BoardBarcode": "fake-sn2"}, {"BoardBarcode": "fake-sn3"}], "Message": "OK"}</string>"""

    return Response(content=ret_str, media_type="application/text")


@app.post("/lixun/data")
def chuzhoulixun1():
    ret_str = """0 sfc ok"""

    return Response(content=ret_str, media_type="application/text")


@app.get("/mrs/CheckRoute")
def shengtai1():
    print("shengtai")

    return {
        "msgStr": "ng",
        "msgId": 1
    }


@app.post("/mrs/createRoute")
def shengtai2():
    print("shengtai")

    return {
        "msgStr": "ok",
        "msgId": 0
    }


@app.post("/mrs/getRelationPcbSeq")
def xindahui3():
    print("xindahui3")

    return {
        "msgStr": "OK",
        "data": [
            {
                "pcbSeq": "10_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "11_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "12_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "13_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "14_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "15_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "16_T21E0515-SMT-VN100001"
            },
            {
                "pcbSeq": "17_T21E0515-SMT-VN100001"
            }
        ],
        "msgId": 0
    }


@app.post("/tracking/saveProductOutBound")
def jingkang():
    print("jingkang")

    return {
        "code": 200,
        "msg": "OK:过站成功"
    }


@app.post("/api/Home/GetSerialNumberByCarrierCode")
def wuhubiyadi1():
    return {
        "ReturnCode": 0,
        "Data": "fake1",
        "ErrorMessage": "OK"
    }


@app.post("/api/Home/GetProductTypeBySerialNumber")
def wuhubiyadi1():
    return {
        "ReturnCode": 0,
        "Data": "1",
        "ErrorMessage": "OK"
    }


@app.post("/api/Home/CheckSerialNumberState")
def wuhubiyadi2():
    return {
        "ReturnCode": -101,
        "Data": "fake1",
        "ErrorMessage": "OK"
    }


@app.post("/api/Home/UploadStateAndMeasurementData")
def wuhubiyadi3():
    return {
        "ReturnCode": 0,
        "Data": "fake1",
        "ErrorMessage": "OK"
    }


@app.post("/api/Home/UploadFactoryMesRecordData")
def wuhubiyadi3():
    return {
        "ReturnCode": 0,
        "Data": "fake1",
        "ErrorMessage": "OK"
    }


@app.post("/rest/wip/bool/IsCurrentStation/string/{sn}/string/{username}/int64/{station}/rest")
def lihe1():
    return {
        "result": "1",
        "error": "ok"
    }


@app.post(
    "/rest/wip/bool/Test/string/{barcode}/string/{username}/int64/{station}/string/{test_result}/string//string/{remark}/rest")
def lihe2():
    return {
        "result": "1",
        "error": "ok"
    }


@app.post(
    "/rest/wip/bool/Test/string/{barcode}/string/{username}/int64/{station}/string/{test_result}/string//string//rest")
def lihe3():
    return {
        "result": "1",
        "error": "ok"
    }


@app.post("/rest/wip/bool/Test/string/{barcode}/string/{username}/int64/{station}/string//string//string//rest")
def lihe4():
    return {
        "result": "1",
        "error": "ok"
    }


@app.post("/test_json22")
def test111():
    return {
        "SQLDataSet": [{
            "Result": "OK",
            "ErrorMSG": "OK"
        }]
    }


@app.post("/chongqingyuxin")
def chongqingyuxin():
    return {
        "resultCode": "0000",
        "resultMsg": "ok"
    }


@app.post("/yongqingmaitian")
def yongqingmaitian():
    return {
        "Result": "OK",
        "ErrorMSG": "OK",
        "SN_LIST": ["fff1", "fff2"]
    }


@app.post("/xingchuan")
def xingchuan():
    return {
        "resultCode": "0000",
        "resultMsg": "ok"
    }


@app.post("/tianhai")
def tianhai():
    return {
        "code": 200,
        "message": "ok"
    }


@app.post("/test_json2")
def test_json2():
    return {
        "result": {
            "code": 0,
            "msg": "ok"
        }
    }


@app.post("/test_json3")
def test_json3():
    return {
        "code": 1,
        "message": "ok"
    }


@app.get("/InfraredCounting/GetFirstArticleNoticeInfo")
def get_order_id():
    return {
        "result": 1, "OrderNumber": "W09496"
    }


@app.post("/collet/aoi/normal/savebadmark")
def maojia1():
    return {
        "result": "OK",
        "eventCode": 0,
        "message": ""
    }


@app.post("/collet/aoi/normal/barcodecheck")
def maojia2():
    return {
        "result": "OK",
        "eventCode": 0,
        "message": ""
    }


@app.post("/collet/aoi/normal/mesoutput")
def maojia3():
    return {
        "result": "OK",
        "eventCode": 0,
        "message": ""
    }


@app.post("/collet/aoi/normal/updatedevicestatus")
def maojia4():
    return {
        "result": "OK",
        "eventCode": 0,
        "message": ""
    }


@app.get("/api/SMT/CheckLogin")
def weixin1():
    return {
        "result": "0",
        "message": "ok"
    }


@app.post("/Api/SMT/CheckEntry")
def weixin1():
    return {
        "result": "0",
        "message": "ok"
    }


@app.post("/Api/EAP/SaveKySpi")
def weixin2():
    return {
        "result": "0",
        "message": "ok"
    }


@app.post("/Api/SMT/CheckExit")
def weixin3():
    return {
        "result": "0",
        "message": "ok"
    }


@app.post("/WebService/MesATEApi.asmx/WS_UserPassValid")
def ningbodahua():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string xmlns="CyntecMES">OK;OK</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/yanteng")
def yanteng():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string xmlns="CyntecMES">200</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/changshabiyadi")
def changshabiyadi():
    return {
        "code": "000000",
        "mesg": "ok",
        "data": {
            "userName": "admin",
            "url": "http://testing.com/fake_img"
        }
    }


@app.post("/eam/test")
def weirui1():
    print("weirui1")

    return {
        "sucsess": True,
        "message": "ok",
        "code": 200,
        "result": {"info": "nothing"}
    }


@app.post("/upload_data/test")
def weirui2():
    print("weirui2")
    return {
        "success": True,
        "message": "ok",
        "code": 200,
        "result": {"info": "nothing"}
    }


@app.post("/WebService/CommonService.asmx/RoutingCheck")
def kaiguo1():
    return {
        "Result": "OK",
        "Msg": "OK"
    }


@app.post("/WebService/CommonService.asmx/SaveTestResult")
def kaiguo3():
    return {
        "Result": "OK",
        "Msg": "OK"
    }


@app.post("/WebService/FileService.asmx/FileUpload")
def kaiguo2():
    return {
        "Result": "OK",
        "Msg": "OK"
    }


@app.post("/test_json/test1")
def tiexinhao():
    return {
        "flag": True,
        "dataList": [
            {"snNumber": "fake1"},
            {"snNumber": "fake2"},
            {"snNumber": "fake3"},
        ],
        "msg": "OK"
    }


@app.post("/test_json3")
def test_json3(authorization=Header("default")):
    print("auth ,", authorization)

    return {
        "flag": True,
        "dataList": [
            {"snNumber": "fake1"},
            {"snNumber": "fake2"},
            {"snNumber": "fake3"},
        ],
        "msg": "OK"
    }


@app.post("/Api/SMT/MES_SaveWIResult")
def weixin5():
    return {
        "result": True,
        "message": "ok"
    }


@app.post("/Service/SuportMlAoiService/lbCheck")
def yibo1():
    return {
        "code": "0000",
        "message": "ok"
    }


@app.post("/Service/SuportMlAoiService/uploadResult")
def yibo2():
    return {
        "code": "0000",
        "message": "ok"
    }


@app.post("/iot/standard/{device_id}/cmd/login")
def wanbang1():
    return {
        "success": True,
        "code": "ok",
        "message": "ok",
        "model": {
            "token": "fake token"
        }
    }


@app.post("/iot/standard/{device_id}/cmd/check/v2")
def wanbang2():
    return {
        "success": True,
        "code": "ok",
        "message": "ok",
    }


@app.post("/iot/standard/{device_id}/cmd/data")
def wanbang3():
    return {
        "success": True,
        "code": "ok",
        "message": "ok",
    }


@app.get("/rest/wip/factlist/GetData/factlist/{param_str}/rest")
def lihe3(param_str):
    print("param_str", param_str)

    return {
        "rows": [
            {
                "ACTION_IKEY": "5",
                "BARCODE_IKEY": "16516",
                "COMPUTATION_IKEY": "1",
                "CUSTOMER": "",
                "CUSTOMERCOL": "LZZW0900030004-BSS11A-V4.0                   ",
                "CUSTOMER_ID": "",
                "CUSTOMER_IKEY": "0",
                "CUSTOMER_SN": "",
                "C_PO_IKEY": "0",
                "DESCRIPTION": "DIP载具绑定",
                "IKEY": "36640",
                "ITEM_DES": "主板PCB-(BSS11A-V4.0)-60*31*1.6mm-FR4-2层-TG150-94V0 ROHS",
                "ITEM_IKEY": "2",
                "ITEM_NO": "10-P2M0BSS11A-V40",
                "LINE": "DIP_LINEA线",
                "LINE_IKEY": "30",
                "NAME_FLAG": "",
                "PASS_FLAG": "1",
                "PN": "主板PCB",
                "PO": "",
                "PRODUCT_DATA_IKEY": "36640",
                "PROD_DATE": "2023-10-18T16:40:18.746",
                "RUNCARD": "CSBS11A00016",
                "RUN_QTY": "1",
                "STAFF": "admin/admin",
                "STATION_IKEY": "88",
                "S_QTY": "1",
                "TEST_RESULT": "0",
                "WORK_ORDER": "CS20230921001",
                "WO_IKEY": "111",
                "WO_STATION_IKEY": "75",
                "object": "FView_products_backData"
            },
            {
                "ACTION_IKEY": "5",
                "BARCODE_IKEY": "16516",
                "COMPUTATION_IKEY": "1",
                "CUSTOMER": "",
                "CUSTOMERCOL": "LZZW0900030004-BSS11A-V4.0                   ",
                "CUSTOMER_ID": "",
                "CUSTOMER_IKEY": "0",
                "CUSTOMER_SN": "",
                "C_PO_IKEY": "0",
                "DESCRIPTION": "DIP载具绑定",
                "IKEY": "36640",
                "ITEM_DES": "主板PCB-(BSS11A-V4.0)-60*31*1.6mm-FR4-2层-TG150-94V0 ROHS",
                "ITEM_IKEY": "2",
                "ITEM_NO": "10-P2M0BSS11A-V40",
                "LINE": "DIP_LINEA线",
                "LINE_IKEY": "30",
                "NAME_FLAG": "",
                "PASS_FLAG": "1",
                "PN": "主板PCB",
                "PO": "",
                "PRODUCT_DATA_IKEY": "36640",
                "PROD_DATE": "2023-10-18T16:40:18.746",
                "RUNCARD": "CSBS11A00017",
                "RUN_QTY": "1",
                "STAFF": "admin/admin",
                "STATION_IKEY": "88",
                "S_QTY": "1",
                "TEST_RESULT": "0",
                "WORK_ORDER": "CS20230921001",
                "WO_IKEY": "111",
                "WO_STATION_IKEY": "75",
                "object": "FView_products_backData"
            }
        ]
    }


@app.post("/SetTestDetail")
def haiying():
    return {
        "code": "ok",
        "message": "ok"
    }


@app.post("/tsmt")
def tsmt():
    return {"success": 0,
            "message": "ng",
            "mesresults": [{"barcode": "", "serialno": "1", "result": "ng",
                            "message": "ORA-20011: 所指定的生產機種/條碼尚未在產線上, 請查明..\nORA-06512: 在 \"XMTMIS.PRO_POST_APIS\", line 88\nORA-06512: 在 line 1",
                            "messagecode": -1},
                           {"barcode": "", "serialno": "2", "result": "ng",
                            "message": "ORA-20011: 所指定的生產機種/條碼尚未在產線上, 請查明..\nORA-06512: 在 \"XMTMIS.PRO_POST_APIS\", line 88\nORA-06512: 在 line 1",
                            "messagecode": -1},
                           {"barcode": "", "serialno": "3", "result": "ng",
                            "message": "ORA-20011: 所指定的生產機種/條碼尚未在產線上, 請查明..\nORA-06512: 在 \"XMTMIS.PRO_POST_APIS\", line 88\nORA-06512: 在 line 1",
                            "messagecode": -1},
                           {"barcode": "", "serialno": "4", "result": "ng",
                            "message": "ORA-20011: 所指定的生產機種/條碼尚未在產線上, 請查明..\nORA-06512: 在 \"XMTMIS.PRO_POST_APIS\", line 88\nORA-06512: 在 line 1",
                            "messagecode": -1},
                           {"barcode": "", "serialno": "5", "result": "ng",
                            "message": "ORA-20011: 所指定的生產機種/條碼尚未在產線上, 請查明..\nORA-06512: 在 \"XMTMIS.PRO_POST_APIS\", line 88\nORA-06512: 在 line 1",
                            "messagecode": -1},
                           {"barcode": "", "serialno": "6", "result": "ng",
                            "message": "ORA-20011: 所指定的生產機種/條碼尚未在產線上, 請查明..\nORA-06512: 在 \"XMTMIS.PRO_POST_APIS\", line 88\nORA-06512: 在 line 1",
                            "messagecode": -1}], "timestamp": 1700811777528,
            "task": "TSMT-XM-APIS VER.1.0"}


@app.get("/Automation/sfc_automation.aspx")
def jiashanlixun():
    ret_str = "0 SFC_OK\nGetSNByCarrierSN=DLCH0J009SF0000GUK"

    return Response(content=ret_str, media_type="application/text")


@app.post("/API/MESStandard/StationProcess")
def jingkerui():
    return {
        "ReturnStatusType": 1,
        "Message": "ok"
    }


# @app.post("/API/MESStandard/CheckRoute")
# def jingkerui():
#     return {
#         "ReturnStatusType": 1,
#         "Message": "ok"
#     }


@app.post("/test_json/hetaiyun")
def hetaiyun1():
    return {
        "Data": True,
        "Message": "ok"
    }


@app.post("/xinwangda/moluoge")
def moluoge():
    return {
        "code": 0,
        "message": "hm.paas.ok",
        "messageData": [],
        "data": {
            "status": "true",
            "description": "WebAPI服务正常!"
        }
    }


@app.post("/API/MESStandard/CheckRoute")
def jingkerui_getsn():
    return {
        "Lot_SN": "23112800007",
        "Right_Wost": "GX004",
        "SNList": [
            {
                "SN": "23112800007",
                "Cust_SN": "n/a",
                "NO": "1",
                "Result": "PASS",
                "Defect_Code": "",
                "Defect_CodeName": "",
                "Defect_Wost": "",
                "Defect_WostName": "",
                "Defect_Location": "",
                "Cur_Wost": "GX001",
                "Next_Wost": "GX004",
                "Next_WostName": "T-SPI",
                "Pt_No": "JKR00350074",
                "Remark_Location": "1",
                "Remark_Surface": "",
                "Keypart_List": []
            },
            {
                "SN": "23112800008",
                "Cust_SN": "n/a",
                "NO": "2",
                "Result": "PASS",
                "Defect_Code": "",
                "Defect_CodeName": "",
                "Defect_Wost": "",
                "Defect_WostName": "",
                "Defect_Location": "",
                "Cur_Wost": "GX001",
                "Next_Wost": "GX004",
                "Next_WostName": "T-SPI",
                "Pt_No": "JKR00350074",
                "Remark_Location": "2",
                "Remark_Surface": "",
                "Keypart_List": [
                    {
                        "Keypart_pt_no": None,
                        "Keypart_pt_class": None,
                        "Keypart_pkg_sn": None,
                        "sub_pkg_sn": None,
                        "sub_pt_no": None,
                        "sub_pt_name": None,
                        "banding_wost_name": None,
                        "pkg_used_qty": None,
                        "Pt_no": None,
                        "Pkg_sn": None
                    }
                ]
            }
        ],
        "ReturnStatusType": 1,
        "StatusCode": "",
        "ErrorMessage": "OK",
        "TimeStamp": "2023-11-30 16:22:37"
    }


@app.post("/taizhouyunyong")
def yunyong(BoardImage=Body("")):
    print("board image", BoardImage)
    return {
        "success": True,
        "msg": "ok"
    }


@app.get("/WebService_VN.asmx/{func_id}")
def yaxu_get_sn(func_id: str):
    print(f"{func_id=}")
    return {"result": [{"RET_CODE": "1",
                        "RET_VALUE": '{"SN_ORDER":"1","SN":"V1EBW001207"},{"SN_ORDER":"2","SN":"V1EBW001208"},{"SN_ORDER":"3","SN":"V1EBW001205"},{"SN_ORDER":"4","SN":"V1EBW001206"}'}]}


@app.post("/huiyanshixun/get_token")
def huiyanshixun():
    return {
        "access_token": "11111111"
    }


@app.post("/huiyanshixun/upload_data")
def huiyanshixun():
    return {
        "code": 200,
        "success": True,
        "data": {"link": "http://***********:9000/bladex/upload/20230904/4953fd2844a56276224c727a4d297888.jpeg",
                 "domain": "http://***********:9000/bladex",
                 "name": "upload/20230904/4953fd2844a56276224c727a4d297888.jpeg", "originalName": "U6@1_side.jpeg",
                 "attachId": "1698590636965412866"},
        "msg": "操作成功"
    }


@app.post("/maike/GetWorkshop")
def maike1():
    return {
        "Result": 1,
        "ErrMsg": "ok",
        "ResultContent": {
            "SH": "SH",
            "DIP": "DIP",
            "ASSY": "ASSY",
            "WT_CXWX": "WT_CXWX",
            "WT_CXWX2": "WT_CXWX2",
            "WT_CXWX3": "WT_CXWX23",
        }
    }


@app.post("/maike/GetProductLine")
def maike2():
    return {
        "Result": 1,
        "ErrMsg": "ok",
        "ResultContent": {"SH5": "5线", "SH4": "4线", "SH3": "3线", "SH2": "2线", "SH1": "1线", "SH": "售后线体"}
    }


@app.post("/maike/CheckBarcode")
def maike3():
    return {
        "Result": 1,
        "ErrMsg": "ok",
        "ResultContent": ""
    }


@app.post("/maike/UploadTestResult")
def maike3():
    return {
        "Result": 1,
        "ErrMsg": "ok",
        "ResultContent": ""
    }


@app.get("/yidelong/1")
def yidelong():
    return {
        "ErrorCode": "0",
        "ErrorMessage": "OK",
    }


@app.get("/yidelong/2")
def yidelong():
    return {
        "ErrorCode": "0",
        "ErrorMessage": "OK",
    }


@app.post("/yidelong/3")
def yidelong(body=Body(...)):
    print(json.dumps(body, ensure_ascii=False, indent=4))
    return {
        "ErrorCode": "0",
        "ErrorMessage": "OK",
    }


@app.post("/yidelong/4")
def yidelong(body=Body(...)):
    print(json.dumps(body, ensure_ascii=False, indent=4))
    return {
        "ErrorCode": "0",
        "ErrorMessage": "OK",
    }


@app.post('/test_file')
def test_file(file: bytes = File(...)):
    print(file.decode('utf-8'))
    return {
        "ok": "ok"
    }


@app.post("/hailipu/test_api")
def hailipu():
    ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GET_TRAYOutput xmlns="http://xmlns.oracle.com/orawsv/PDC_WS/PDC_WS_AOI">
            <P_OUT>
                <AOI_GET_TRAY_RESP>
                    <MESSAGEID>1EAE9B59-0AC2-1C20-E054-3C4A923CDF36</MESSAGEID>
                    <TIMESTAMP>2015-12-02T11:37:42.310+01:00</TIMESTAMP>
                    <INTERFACEID>IF0000</INTERFACEID>
                    <PROCESSINGSTATUS>
                        <RETURNVALUE>ACCEPTED</RETURNVALUE>
                        <RETURNCODE>0</RETURNCODE>
                        <RETURNDESCRIPTION/>
                    </PROCESSINGSTATUS>
                    <TRAY_CONTENT>
                        <WORKSTATION>VT-RNS-5311</WORKSTATION>
                        <PCB_LIST>
                            <AOI_GET_TRAY_PCB>
                                <PCB>130B8184040136G415</PCB>
                                <POSITION>1</POSITION>
                            </AOI_GET_TRAY_PCB>
                            <AOI_GET_TRAY_PCB>
                                <PCB>130B8184040236G415</PCB>
                                <POSITION>2</POSITION>
                            </AOI_GET_TRAY_PCB>
                            <AOI_GET_TRAY_PCB>
                                <PCB>130B8184040336G415</PCB>
                                <POSITION>3</POSITION>
                            </AOI_GET_TRAY_PCB>
                        </PCB_LIST>
                    </TRAY_CONTENT>
                </AOI_GET_TRAY_RESP>
            </P_OUT>
        </GET_TRAYOutput>
    </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/text")


@app.post("/rongzhao")
def rongzhao():
    return {
        "Success": True,
        "MsgContent": "123"
    }


@app.post('/maike/device_type')
def get_device_type():
    return {
        "Result": 1,
        "ErrMsg": "ok",
        "ResultContent": ["SN763", "SN764", "SN765"]
    }


@app.post("/xiaoming")
def xiaoming():
    return {
        "statusCode": 200,
        "value": True,
        "message": "成功",
        "result": True
    }


@app.post("/xinan")
def xinan():
    return {
        "statusCode": 200,
        "value": True,
        "message": "成功",
        "result": True
    }


@app.get('/mo_list')
def dongsong():
    return {
        "success": True,
        "message": '',
        "code": 200,
        "result": [
            {"thisid": 7926, 'mo': 'Mo2240106001'},
            {"thisid": 7927, 'mo': 'Mo2240106002'},
            {"thisid": 7928, 'mo': 'Mo2240106003'},
            {"thisid": 5555, 'mo': 'Mo2240106004'},
        ]
    }


@app.get("/api/System/TestConnection")
def xinaoxun():
    return {
        'code': 'success',
        'msg': '连接mes成功',
        'data': None
    }


@app.post("/api/Collection/SendJudgeFileAOIInterface")
def xinaoxun():
    return {
        'code': 'success',
        'msg': '连接mes成功',
        'data': None
    }


@app.get("/api/WorkOrderSchedule/GetForTestMachine")
def xinaoxun():
    return {
        "code": "success",
        "msg": "响应成功",
        "data": {
            "code": 0,
            "msg": None,
            "count": 175,
            "data": [
                {
                    "id": "4268147155614564352",
                    "order_code": "1188002-A1",
                    "work_order_code": "W01411",
                    "production_order_code": "20240114001-1188007-A",
                    "product_code": "3.01.0004",
                    "product_name": "PCBA",
                    "product_spec": "3.01.0004 X6主板",
                    "page_type_desc": "T面",
                    "customer_code": "1188",
                    "customer_name": "深圳市汇腾丰科技有限公司",
                    "workline_code": "7",
                    "workline_name": "L7线",
                    "workshop_code": "SMT01",
                    "workshop_name": "SMT---",
                    "process_code": "SMT",
                    "process_name": "SMT",
                    "plan_num": 2000.0,
                    "real_num": 0.0,
                    "create_date": "2024-01-17 20:13:01",
                    "remark": ""
                },
                {
                    "id": "4268146957081378816",
                    "order_code": "1188002-A",
                    "work_order_code": "W01410",
                    "production_order_code": "20240114001-1188007-A",
                    "product_code": "3.01.0004",
                    "product_name": "PCBA",
                    "product_spec": "3.01.0004 X6主板",
                    "page_type_desc": "B面",
                    "customer_code": "1188",
                    "customer_name": "深圳市汇腾丰科技有限公司",
                    "workline_code": "7",
                    "workline_name": "L7线",
                    "workshop_code": "SMT01",
                    "workshop_name": "SMT---",
                    "process_code": "SMT",
                    "process_name": "SMT",
                    "plan_num": 2000.0,
                    "real_num": 0.0,
                    "create_date": "2024-01-17 20:12:13",
                    "remark": ""
                },
                {
                    "id": "4268144439496540160",
                    "order_code": "1168018-D16",
                    "work_order_code": "W01397",
                    "production_order_code": "CGDD000768-1168018D",
                    "product_code": "200100032",
                    "product_name": "半成品",
                    "product_spec": "PCBA,REV1.0,LT500-OutdoorREV1.0,MT7628AN+MT7613BEN+EC200AEU,[欧规]",
                    "page_type_desc": "T面",
                    "customer_code": "1168",
                    "customer_name": "深圳市多好制造有限公司",
                    "workline_code": "1",
                    "workline_name": "L1线",
                    "workshop_code": "SMT01",
                    "workshop_name": "SMT---",
                    "process_code": "SMT",
                    "process_name": "SMT",
                    "plan_num": 2000.0,
                    "real_num": 0.0,
                    "create_date": "2024-01-17 20:02:13",
                    "remark": ""
                }

            ]
        }
    }


@app.get("/api/Collection/GetPCBGroupsInfo")
def xinaoxun():
    return {
        'code': 'success',
        'msg': '连接mes成功',
        'data': [
            {
                "sn_code": "XK06001",
                "groups": "001",
                "groups_sort": 1
            },
            {
                "sn_code": "XK06002",
                "groups": "001",
                "groups_sort": 2
            },
            {
                "sn_code": "XK06003",
                "groups": "001",
                "groups_sort": 3
            }
        ]
    }


@app.post("/api/SfcsEcndoc/ActivationRateData")
def jiadong():
    return {"code": 200, "message": "OK"}


@app.post("/zhonghe")
def zhonghe():
    ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Header>
        <ActivityId CorrelationId="90870c3f-7f88-4b40-879a-8f0c0b910807"
                    xmlns="http://schemas.microsoft.com/2004/09/ServiceModel/Diagnostics">
            00000000-0000-0000-0000-000000000000
        </ActivityId>
    </s:Header>
    <s:Body>
        <GetMeterInfoBySNResponse xmlns="http://tempuri.org/">
            <GetMeterInfoBySNResult xmlns:a="http://schemas.datacontract.org/2004/07/Soas.Model.Entities"
                                    xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:AccuracyLevel>有功2.0级</a:AccuracyLevel>
                <a:Constant>2000</a:Constant>
                <a:Current>0.25-0.5(60)</a:Current>
                <a:DoWhatList/>
                <a:Frequency>50</a:Frequency>
                <a:MaterialNo>2301SZ003-10</a:MaterialNo>
                <a:MeterModel>DDZY131</a:MeterModel>
                <a:OrderName>DDZY131C-Z型单相费控智能电能表(CPU卡-开关内置，A级)</a:OrderName>
                <a:OrderNo>2301FJSZ001</a:OrderNo>
                <a:Phase i:nil="true"/>
                <a:SN>2301PP110007</a:SN>
                <a:SNType>@@</a:SNType>
                <a:SchemeList i:nil="true"/>
                <a:Voltage>220</a:Voltage>
                <a:WorkOrder>240002_01</a:WorkOrder>
            </GetMeterInfoBySNResult>
            <basedata>true</basedata>
            <result>true</result>
            <message/>
        </GetMeterInfoBySNResponse>
    </s:Body>
</s:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/mianyangjiuzhou/check")
def mianyangjiuzhou():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ML_SS_BeforeTestWithStationResponse xmlns="http://jiuzhoutech.com/">
      <ML_SS_BeforeTestWithStationResult>true</ML_SS_BeforeTestWithStationResult>
      <errMsg>true</errMsg>
      <curStation>ok</curStation>
    </ML_SS_BeforeTestWithStationResponse>
  </soap:Body>
</soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/weichuangli/data")
def weichuangli():
    ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><SaveResultResponse xmlns="http://tempuri.org/"><SaveResultResult xmlns:a="http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><a:Data i:nil="true"/><a:FFPartID i:nil="true"/><a:FFTestID i:nil="true"/><a:FFUnitID i:nil="true"/><a:Id>*********</a:Id><a:Value>One of the DUT in panel has routing error [uspTSTSaveResult]</a:Value></SaveResultResult></SaveResultResponse></s:Body></s:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/api/AssemblyOperation/TestReportV2")
def letu():
    return {
        "type": "TestReport",
        "res": True,
        "reason": " "
    }


@app.post("/MesInterac/CheckMac")
def chuangwei():
    return {
        "res": True,
        "reason": " "
    }


@app.post("/MesInterac/CheckSn")
def chuangwei():
    return {
        "res": True,
        "reason": " "
    }


@app.post("/MesInterac/TestReport")
def chuangwei():
    return {
        "res": True,
        "reason": " "
    }


@app.post("/new_test22")
def new_test22(body=Body("default")):
    print("body-----------")
    print(body)
    ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body><SaveResultResponse xmlns="http://tempuri.org/"><SaveResultResult xmlns:a="http://schemas.datacontract.org/2004/07/Flex.FFTester.Utilities" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><a:Data i:nil="true"/><a:FFPartID i:nil="true"/><a:FFTestID i:nil="true"/><a:FFUnitID i:nil="true"/><a:Id>*********</a:Id><a:Value>One of the DUT in panel has routing error [uspTSTSaveResult]</a:Value></SaveResultResult></SaveResultResponse></s:Body></s:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.get('/tms/api/public/aging/checkProcess')
def yangguangdiandongli(process=Form("defulat...")):
    print(process)
    return {
        "code": 200,
        "message": "",
        "result": {},
        "success": True,
        "timestamp": 0
    }


@app.post('/tms/api/public/aging/saveTestInfo')
def yangguangdiandongli2(process=Form("defulat...")):
    print(process)
    return {
        "code": 200,
        "message": "",
        "result": {},
        "success": True,
        "timestamp": 0
    }


@app.post('/zhongche')
def zhongche():
    ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ns2:OPC_Send_Data_To_MesResponse xmlns:ns2="http://tempuri.org/">
            <OPC_Send_Data_To_MesResult>{"CODE":"OK","MESSAGE":"SUCCESS","DATA": [{"LOT_ID": "FAKE1"},{"LOT_ID": "FAKE2"},{"LOT_ID": "FAKE3"}]}</OPC_Send_Data_To_MesResult>
        </ns2:OPC_Send_Data_To_MesResponse>
    </soap:Body>
</soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/byd/test')
def zhongche():
    ret_str = """<string xmlns="http://tempuri.org/">{"Result":"PASS","Code":0,"Message":"ok!","DATA":None}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.get('/byd/test')
def zhongche():
    ret_str = """<string xmlns="http://tempuri.org/">{"Result":"PASS","Code":0,"Message":"ok!","DATA":None}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/Services/MachineServices.asmx/MES_TransData')
def changcheng():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">OK,</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/WS_UpdUnitRecord')
def haochengkeji():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/WebService/BasalWebService.asmx/WS_UpdUnitRecord')
def haochengkeji():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/WS_UserValid')
def haochengkeji():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/WebService/BasalWebService.asmx/WS_BcValid')
def haochengkeji():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/WS_BcValid')
def haochengkeji():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">&lt;ReturnData&gt;&lt;RtnString&gt;1&lt;/RtnString&gt;&lt;ErrMsg&gt;OK&lt;/ErrMsg&gt;&lt;/ReturnData&gt;</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/UploadFileNameToFTP')
def haochengkeji():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://mesateapi.com/">ftp://172.16.11.55/AOILOG/20240519</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post('/op_result/')
def lixunv2(body=Body('...')):
    print(f"body, {body}")
    return {
        "error_code": 0,
        "error_describe": "ok",
    }


@app.post('/predict_data/')
def lixunv2(body=Body('...')):
    print(f"body, {body}")

    return {
        "error_code": 0,
        "error_describe": "ok",
    }


@app.post('/tongfan/test')
def tongfan():
    return {"resultCode": 0, "message": "ok"}


@app.post('/ruijie/test')
def tongfan():
    return {"Result": "OK", "ErrorException": "ok"}


@app.post('/weimeng')
def tongfan():
    return {"StatusCode": 0, "Message": "ok"}


@app.post('/hengtong/test')
def hengtong():
    return {"id": 0, "message": "ok", 'data': {'sid': 'SR24031400002'}}


@app.post('/weilinma/test')
def weilinma():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
        <CheckAOI2InfoResponse xmlns="http://www.cnlean.net/">
          <CheckAOI2InfoResult>{"result":"Succeed","FreqCode": "12","Data":"111"}</CheckAOI2InfoResult>
        </CheckAOI2InfoResponse>
      </soap:Body>
    </soap:Envelope>"""

    return Response(media_type="application/xml", content=ret_str)


@app.post("/lianrui")
def lianrui():
    return {
        "header":
            {
                "info":
                    {
                        "messageClass": "1100",
                        "transferTime": "20130101000000"
                    },
                "location":
                    {
                        "machineID": "1LCAOI01",
                        "groupName": "S_AOI",
                        "laneNo": "1"
                    }
            },
        "body":
            {
                "result":
                    {
                        "errorCode": "0",
                        "errorText": "",
                        "actionCode": "0"
                    },
                "program": {
                    "name": "XXXXXXXXXXXXXX"
                },
                "pcb": {"id": "P1234567890"}
            }
    }


@app.post('/api/dap/event')
def taiguoqundian():
    return {
        "result": True,
        "code": "OK",
        "message": ""
    }


@app.post('/api/dap/alarm')
def taiguoqundian():
    return {
        "result": True,
        "code": "OK",
        "message": ""
    }


@app.post('/api/dap/productionresult')
def taiguoqundian():
    return {
        "result": True,
        "code": "OK",
        "message": ""
    }


@app.post('/api/dap/parameter')
def taiguoqundian():
    return {
        "result": True,
        "code": "OK",
        "message": ""
    }


@app.post('/api/dap/heartbeat')
def taiguoqundian():
    return {
        "result": True,
        "code": "OK",
        "message": ""
    }


@app.post("/baonasheng/get_sn")
def baonashegn():
    return [
        {
            "ContainerLevelName": "PANEL",
            "ContainerName": "4000105 1101 000226 2329",
            "ES_PrimarySerialNumber": "4000105 1101 000226 2329",
            "ES_SerialNumber2": "",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "0",
            "Qty": "6"
        },
        {
            "ContainerLevelName": "PCB",
            "ContainerName": "4000105 1101 T 001351 2329",
            "ES_PrimarySerialNumber": "4000105 1101 T 001351 2329",
            "ES_SerialNumber2": "4000105 1101 B 001351 2329",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "1",
            "Qty": "1"
        },
        {
            "ContainerLevelName": "PCB",
            "ContainerName": "4000105 1101 T 001352 2329",
            "ES_PrimarySerialNumber": "4000105 1101 T 001352 2329",
            "ES_SerialNumber2": "4000105 1101 B 001352 2329",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "2",
            "Qty": "1"
        },
        {
            "ContainerLevelName": "PCB",
            "ContainerName": "4000105 1101 T 001353 2329",
            "ES_PrimarySerialNumber": "4000105 1101 T 001353 2329",
            "ES_SerialNumber2": "4000105 1101 B 001353 2329",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "3",
            "Qty": "1"
        },
        {
            "ContainerLevelName": "PCB",
            "ContainerName": "4000105 1101 T 001354 2329",
            "ES_PrimarySerialNumber": "4000105 1101 T 001354 2329", "ES_SerialNumber2": "4000105 1101 B 001354 2329",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "4",
            "Qty": "1"
        },
        {
            "ContainerLevelName": "PCB",
            "ContainerName": "4000105 1101 T 001355 2329",
            "ES_PrimarySerialNumber": "4000105 1101 T 001355 2329",
            "ES_SerialNumber2": "4000105 1101 B 001355 2329",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "5",
            "Qty": "1"
        },
        {
            "ContainerLevelName": "PCB",
            "ContainerName": "4000105 1101 T 001356 2329",
            "ES_PrimarySerialNumber": "4000105 1101 T 001356 2329",
            "ES_SerialNumber2": "4000105 1101 B 001356 2329",
            "ES_SerialNumber3": "",
            "ES_PCBNumber": "6",
            "Qty": "1"
        }
    ]


@app.get("/test_json")
def test_json1():
    return {"code": "200", "message": "ok"}


@app.post('/dongguanlengling')
def dongguanlengling():
    return {
        "SUCCESS": 1,
        "MESSAGE": "",
        "TIMESTAMP": 1682589777580,
        "RESULT": [
            {
                "BARCODE": "TS5531M03S0627T0431501",
                "SERIALNO": "1a",
                "RESULT": 1,
                "MESSAGE": "",
                "MESSAGECODE": ""
            },
            {
                "BARCODE": "TS5531M03S0627T0432301",
                "SERIALNO": "2b",
                "RESULT": 0,
                "MESSAGE": "NG信息",
                "MESSAGECODE": ""
            }
        ]
    }


@app.post("/test_json/oupu")
def oupu():
    return {
        "code": 200,
        "message": "ok",
        "payload": json.dumps({
            "code": "200",
            "msg": "ok"
        })
    }


@app.post("/Api/EAP/CheckInfo")
def weixin():
    return {"result": True, "message": "", "panel": None, "vritualBarcode": None,
            "currentTime": "2023-10-22T10:45:19.4678305+08:00", "badMarkLists": [
            {"panel": "55065100425B0223255806334", "pcsBarcode": "55065100425B0223255806334", "position": "1",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255806519", "pcsBarcode": "55065100425B0223255806519", "position": "2",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255801675", "pcsBarcode": "55065100425B0223255801675", "position": "3",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255801665", "pcsBarcode": "55065100425B0223255801665", "position": "4",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"}]}


@app.post("/Api/EAP/SaveInfo")
def weixin():
    return {"result": True, "message": "", "panel": None, "vritualBarcode": None,
            "currentTime": "2023-10-22T10:45:19.4678305+08:00", "badMarkLists": [
            {"panel": "55065100425B0223255806334", "pcsBarcode": "55065100425B0223255806334", "position": "1",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255806519", "pcsBarcode": "55065100425B0223255806519", "position": "2",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255801675", "pcsBarcode": "55065100425B0223255801675", "position": "3",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255801665", "pcsBarcode": "55065100425B0223255801665", "position": "4",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"}]}


@app.post("/Api/SMT/MES_SaveWIResult")
def weixin():
    return {"result": True, "message": "", "panel": None, "vritualBarcode": None,
            "currentTime": "2023-10-22T10:45:19.4678305+08:00", "badMarkLists": [
            {"panel": "55065100425B0223255806334", "pcsBarcode": "55065100425B0223255806334", "position": "1",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255806519", "pcsBarcode": "55065100425B0223255806519", "position": "2",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255801675", "pcsBarcode": "55065100425B0223255801675", "position": "3",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"},
            {"panel": "55065100425B0223255801665", "pcsBarcode": "55065100425B0223255801665", "position": "4",
             "toolName": "RAS12388Y451BM0019", "badmark": "OK"}]}


@app.post("/andepu/test1")
def andepu1():
    return {
        "id": 0,
        "message": "ok",
        "data": ["sn1", "sn2", "sn55"]
    }


@app.post("/andepu/test2")
def andepu2():
    return {
        "id": 0,
        "message": "ok",
        "data": {"sid": "22"}
    }


@app.post("/api/ApiConnect")
def junchuang1():
    return {
        "status": "200",
        "msg": "ok"
    }


@app.post("/api/ReturnLaserResult")
def junchuang1():
    return {
        "status": "200",
        "msg": "ok"
    }


@app.get("/api/CheckDipTaskProcessBarCode")
def junchuang2():
    return {
        "status": "200",
        "msg": "ok"
    }


@app.post("/api/huizhoujinbaize")
def huizhoujinbaize(data=Body("default")):
    if data.get('Code') == 321:

        ret_list = []
        for i in range(10):
            ret_list.append(f"filename{i}")

        ret_msg = ",".join(ret_list)
    else:
        ret_msg = "OK"

    return {
        "status": 200,
        "message": ret_msg,
    }


@app.post("/API/V1.ashx")
def jinbaize1(data=Body("default")):
    return {
        "ReturnCode": 1,
        "ReturnMsg": "OK",
    }


@app.post("/huaxiang")
def huaxiang():
    # ret1 = {"Status": "OK", "CheckResult": "校验通过"}
    #
    # ret2 = json.dumps(ret1, ensure_ascii=False)
    # print("ret2", ret2)

    r = {"Status": "OK", "CheckResult": "校验通过"}

    r1 = json.dumps(r, ensure_ascii=False)
    print(r1)

    b = {'result': 'Succeed', 'FReqCode': '202405161701333', 'FDeviceNo': 'HJAOI01',
         'data': json.dumps(r, ensure_ascii=False)}

    ret22 = json.dumps(b, ensure_ascii=False)
    print(ret22)

    ret_str = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <CheckHJAOIInfoResponse xmlns="http://www.cnlean.net/">
            <CheckHJAOIInfoResult>{ret22}</CheckHJAOIInfoResult>
        </CheckHJAOIInfoResponse>
    </soap:Body>
</soap:Envelope>"""
    print(ret_str)
    return Response(content=ret_str)


@app.post("/changxiang")
def changxiang():
    return {
        "ErrCode": 0,
        "ErrMsg": "OK",
        "Data": {
            "token": "fake token!"
        }
    }


@app.post("/smtcore/api/WPReport/ReportAPI")
def changxiang():
    return {
        "ErrCode": 0,
        "ErrMsg": "OK",
        "Data": {
            "token": "fake token!"
        }
    }


@app.post("/xinnengan")
def changxiang(body=Body("default"), content_type=Header("default1")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {"Result": 1, "Msg": ""}


@app.post("/sfcs/vehicle_query/")
def changxiang(body=Body("default"), content_type=Header("default1")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {"Result": "OK", "Msg": "", "Message": "OK"}


@app.post("/sfcs/connected_board_posting/")
def changxiang(body=Body("default"), content_type=Header("default1")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {"Result": "OK", "Msg": "", "Message": "OK"}


@app.post("/api/Txn_SMTEquipment/Txn_SMTEquipment_AOI_CHECK")
def changxiang1(body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {"code": "0", "msg": "ok"}


@app.post("/api/Txn_SMTEquipment/Txn_SMTEquipment_AOI_SAVE")
def changxiang2(body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {"code": "0", "msg": "ok"}


@app.get("/api/Account/CreateToken")
def cvte1(body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {
        "result": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJVc2VySWQiOjQsIlRlbmFudElkIjo5LCJJbXBlcnNvbmF0b3JUZW5hbnRJZCI6bnVsbCwiSW1wZXJzb25hdG9yVXNlcklkIjpudWxsLCJVc2VyTmFtZSI6IueuoeeQhuWRmC1RWCIsIkFjY291bnQiOiJBZG1pbiIsIkV4cGlyZXNUaW1lIjoiMjAyMi0xMS0wN1QxMToxOTo0OS4wOTY4MTA2KzA4OjAwIiwiSWQiOiIwYThjNGI1ZWZlZmM0Yjc0ODQyM2JlYjBkZmJiOTVjZiIsIkxvZ2luVHlwZSI6MSwiRW1haWwiOiI5OTM5ODIwNzdAcXEuY29tIiwiVGVuYW5jeU5hbWUiOiJRWCJ9.XU9aW8JyE7hgf6tiTOL6ncxvvHK9_oFFClAKw_Gus5k",
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/api/services/mf/SmdManufacture/MoveInByHand")
def cvte1(body=Body("default")):
    # print(content_type)
    return {"result": {
        "taskOrderNo": "*******************",
        "procedureCode": "SC360",
        "lane": None,
        "machineCode": "",
        "controlType": "",
        "totalCount": 1,
        "uniqueId": "fake-uniqueid-001",
        "items": [
            {
                "puzzleNo": 1,
                "whetherForkPlate": 0,
                "barcode": "23100053-0A00017"
            }
        ]
    }, "targetUrl": None, "success": True, "error": {
        "message": "条码【S24060366-0A02935$004.057.0056343$3854398AD0BB】当前工序【总检(SC520)】不允许重复扫描，请过下一站工序【板卡装箱(SC530)】【跟踪ID f4e1e77c3dfa4233a1f737122d0e3896】",
        "code": 0, "details": None, "validationErrors": None}, "unAuthorizedRequest": False, "__abp": True}


@app.post("/api/services/mf/SmdManufacture/MoveOutStd")
def cvte1(body=Body("default")):
    # print(content_type)
    return {
        "result": {
            "taskOrderNo": "*******************",
            "procedureCode": "SC360",
            "lane": None,
            "machineCode": "",
            "controlType": "",
            "totalCount": 1,
            "uniqueId": "fake-uniqueid-001",
            "items": [
                {
                    "puzzleNo": 1,
                    "whetherForkPlate": 0,
                    "barcode": "23100053-0A00017"
                }
            ]
        },
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/api/services/mf/SmdManufactureProduction/UploadProductionInfo")
def cvte1(body=Body("default")):
    # print(content_type)
    return {
        "result": {
            "taskOrderNo": "*******************",
            "procedureCode": "SC360",
            "lane": None,
            "machineCode": "",
            "controlType": "",
            "totalCount": 1,
            "uniqueId": "fake-uniqueid-001",
            "items": [
                {
                    "puzzleNo": 1,
                    "whetherForkPlate": 0,
                    "barcode": "23100053-0A00017"
                }
            ]
        },
        "targetUrl": None,
        "success": False,
        "error": {
            "message": "上传稼动信息失败, testing"
        },
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.get("/api/services/bas/TaskOrder/GetTaskListByMo")
def cvte1(req: Request, body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    print('req', req.query_params)

    mo_lot_no = req.query_params.get('moLotNo')

    if mo_lot_no == "RCBJ-SKZBU23080012":
        result = [
            {
                "isSplited": False,
                "currentSectionSequenceNo": 60,
                "id": "3a0de1c1b52e4663a984d4d6cbf166a1",
                "taskNo": "A001"
            },
            {
                "isSplited": False,
                "currentSectionSequenceNo": 60,
                "id": "3a0de1c1b52e4663a984d4d6cbf166a1",
                "taskNo": "A002"
            },
        ]
    else:
        result = [
            {
                "isSplited": True,
                "currentSectionSequenceNo": 60,
                "id": "3a0de1c1b52e4663a984d4d6cbf166a1",
                "taskNo": "B001"
            },
            {
                "isSplited": True,
                "currentSectionSequenceNo": 60,
                "id": "3a0de1c1b52e4663a984d4d6cbf166a1",
                "taskNo": "B002"
            },
        ]

    return {
        "result": result,
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/api/services/test/TestScan/CheckLotSnCode")
def cvte2(body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {
        "result": {
            "isSuccess": True,
            "code": "200",
            "msg": ""
        },
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/api/services/testapi/TestLog/InsertProcessItem")
def cvte4(body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {
        "result": {
            "isSuccess": True,
            "code": "200",
            "msg": ""
        },
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/api/services/test/TestScan/SaveProduceTestByAutoTest")
def cvte3(body=Body("default")):
    print("-----------接口传过的参数")
    print(body)
    # print(content_type)
    return {
        "result": {
            "isSuccess": True,
            "code": "200",
            "msg": ""
        },
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/renhe/api")
def renhe():
    return {
        "Code": "0", "MSG": "ok"
    }


@app.post("/MES/CheckPanelGetSnListFile")
def jiangxilixun():
    return {
        "status": 0, "message": "ok", "snOrders": [
            {"snSort": 1, "serialNumber": "sn1"},
            {"snSort": 2, "serialNumber": "sn2"},
            {"snSort": 3, "serialNumber": "sn3"}
        ]
    }


@app.post("/MES/SnListGoFile")
def jiangxilixun():
    return {
        "status": 0, "message": "ok"
    }


@app.post("/GoldSoftWCF/UploadResult")
def lianyuhua():
    return {
        "Result": True, "Msg": "Ok"
    }


@app.post("/GoldSoftWCF/CheckStationName")
def lianyuhua():
    return {
        "Result": True, "Msg": "Ok"
    }


@app.post("/shouxin")
def shouxin():
    return {
        "Error": False, "Msg": "Ok"
    }


@app.post("/weikete")
def shouxin():
    return {
        "requestId": "erp-jfq0caiqzf4codlax3s6hv-pangus89",
        "requestNo": "erp-jfq0caiqzf4codlax3s6hv-pangus89",
        "requestType": "HTTP",
        "sourceId": "AOI",
        "targetId": "IMS",
        "requestTime": 1620368607622,
        "reponseTime": 1623304813062,
        "resultCode": "0000",
        "resultMsg": "过站成功",
        "count": 1,
        "data": None,
        "extend": {}
    }


@app.post("/dataService/webapi/Dynamic/IO/UpLoadEapData")
def liyade2():
    return {
        "IsSuccess": True,
        "Message": "成功",
        "Data": None
    }


@app.post("/liyade")
def liyade():
    return {
        "Success": True,
        "Message": "成功",
        "Data": None
    }


@app.post("/liding")
def liding():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="MesWebService">[{"Result":"NG","Message":"B-2DAOI-A5-3F-S08-2TerminalID 不存在!"}]</string>"""

    return Response(content=ret_str, media_type="application/xml")
    # return [{
    #     "Result": "OK",
    #     "MESSAGE": "成功",
    # }]


@app.post("/api/process/scan/pass_station_check")
def foshanyinxing():
    return {
        "success": True,
        "message": {
            "code": "00",
            "content": None
        },
        "data": None
    }


@app.post("/api/process/scan/post_test_data")
def foshanyinxing():
    return {
        "success": True,
        "message": {
            "code": "00",
            "content": None
        },
        "data": None
    }


@app.post("/smtcore/api/Authentication/RequestToken")
def maiteng():
    return {
        "ErrCode": 0,
        "Data": {
            "token": "sdfwerfsd"
        }
    }


@app.get("/api/interface/equipment/querysplic")
def yinxing():
    return {
        "success": True,
        "message": {
            "code": "00",
            "content": None
        },
        "data": {
            "STANDAR_QTY": 3,
            "SERIALS": ["sn1", "sn2", "sn3"]
        }
    }


@app.get("/api/Ats/AtsGetArea")
def haixin():
    return {
        "code": "OK",
        "result": "OK:获取线体列表成功",
        "data": [{
            "areaId": 263,
            "areaName": "L301线"
        }, {
            "areaId": 252,
            "areaName": "M101线"
        }, {
            "areaId": 257,
            "areaName": "M104"
        }]
    }


@app.get("/api/Ats/AtsGetWorkStations")
def haixin():
    return {
        "code": "OK",
        "result": "OK:获取工作中心成功",
        "data": [{
            "workStationId": 1418,
            "workStationSn": "WT101PXNCS01",
            "workStationName": "T101PCBA性能测试"
        }, {
            "workStationId": 1428,
            "workStationSn": "WT101WXNCS01",
            "workStationName": "T101WIFI性能测试"
        }, {
            "workStationId": 1285,
            "workStationSn": "WT101SB01",
            "workStationName": "T101上板站"
        }]
    }


@app.post("/api/Ats/AtsCheckRun")
def haixin():
    return {
        "code": "OK",
        "result": "OK:XXXXXXXXX",
        "data": ""
    }


@app.post("/api/Ats/AtsTestRun")
def haixin():
    return {
        "code": "OK",
        "result": "OK:XXXXXXXXX",
        "data": ""
    }


@app.post("/api/snapshot/line1/deviceid1")
def qunguang():
    return {
        "code": "OK",
        "result": "OK:XXXXXXXXX",
        "data": ""
    }


@app.post("/apriso/HttpServices/api/extensions/1/DataCollection/MoveInVerify")
def nuoboqiche():
    return {
        "Result": "200",
        "Message": "OK",
    }


@app.post("/apriso/HttpServices/api/extensions/1/DataCollection/MoveOut")
def nuoboqiche():
    return {
        "Result": "200",
        "Message": "OK",
    }


@app.post("/tuopu/test1")
def nuoboqiche():
    return {
        "type": "success",
        "message": "SUCCESS.车间作业控制 SFC000001在工序DIP  DICT1操作1(D01ICT1)上活动 "
    }


@app.post("/tuopu/test2")
def nuoboqiche():
    return [
        {
            "type": "success",
            "message": "SUCCESS.车间作业控制 SFC000001在工序DIP  DICT1操作1(D01ICT1)上活动 "
        }
    ]


@app.get("/Authentication/Login")
def get_token_hongfa():
    bad_ret = {
        "errcode": "4044",
        "errmsg": "用户名或密码错误"
    }
    good_ret = {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.ZhJUryX3uR92LkPgbUbQ0AAulovii8Bt0wRQLmdCt1w",
        "createdate": "2020/5/9 10:56:49",
        "expires_in": 300
    }
    return good_ret


@app.post("/Equipment/AutoCallProgram")
def get_token_hongfa1():
    # time.sleep(1)
    return {"ProductProgram": "6670170000", "ErrCode": "0000", "ErrMsg": "调用成功。"}


@app.post("/Equipment/BarcodeScan")
def get_token_hongfa1():
    return {
        "productprogram": "40137580021",
        "ErrCode": "0000",
        "ErrMsg": "OK"
    }


@app.post("/Equipment/UploadAOIData")
def get_token_hongfa1():
    return {
        "productprogram": "40137580021",
        "ErrCode": "0000",
        "ErrMsg": "OK"
    }


@app.post("/api/Login")
def zhifengwei():
    return {
        "status": 200,
        "success": True,
        "msg": "登录成功",
        "msgDev": None,
        "response": {
            "UserID": "023311",
            "UserName": "肖雄杰",
            "DeptName": "工程部", "NextDeptName": "軟件組",
            "GroupName": "軟件組",
            "JobName": "工程師",
            "EnglishName": None,
            "Language": "中文简体",
            "Token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJOYW1lIjoi6IKW6ZuE5p2wIiwiVXNlcklEIjoiMDIzMzExIiwiQ29tcGFueUNvZGUiOiIiLCJIZWxsbyI6IndvcmxkIiwiRXhwaXJhdGlvbiI6IjIwMjQvMS84IDE4OjM3OjI2IiwiUm9sZSI6WyIyMjUiLCI0NyIsIjEiLCI2NSJdLCJuYmYiOjE3MDQ2ODE0NDYsImV4cCI6MTcwNDcxMDI0NiwiaXNzIjoiVE1JQ1MuV2ViQVBJIiwiYXVkIjoid3IifQ.kNmP-RqnnjOhvC9InJBApyA_C4TxaqreRnfQwDOr-sQ",
            "Success": True,
            "expires_in": 28800
        }
    }


@app.post("/api/GetUUTLabelNo")
def zhifengwei1():
    return {
        "status": 200,
        "success": True,
        "msg": "获取数据成功。",
        "response": {
            "ClampNO": " PA-6LA11940-01A",
            "UUTLabelNo": "10002540001100001;10002540001100002;10002540001100003;10002540001100004"
        }
    }


@app.post("/api/UploadAOITestData")
def zhifengwei1():
    return {
        "status": 200,
        "success": True,
        "msg": "上传数据和解绑成功。",
        "response": None
    }


@app.post("/TRIAOIService.asmx/CheckBarCode")
def yuansong(data=Body("default")):
    print("data", data)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">OK:P</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/TRIAOIService.asmx/GetUnitMappingList")
def yuansong(data=Body("default")):
    print("data", data)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">OK:P;SN1,SN2,SN3</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/TRIAOIService.asmx/UpLoadTestData")
def yuansong(data=Body("default")):
    print("data", data)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">OK:P</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/TRIAOIService.asmx/Upload_process_data")
def yuansong(data=Body("default")):
    print("data", data)
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">OK:P</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.get("/tuobang/get_sn")
def tuobang1():
    return {
        "type": "success",
        "code": "0",
        "message": "处理成功",
        "data": [
            {
                "shop_order": "50861093",
                "sfc": "3^4008^156^50861093^280086392^02^20230801^0004789",
                "site": "1000",
                "barcode": "20238282322",
                "azm": None
            },
            {
                "shop_order": "50861093",
                "sfc": "3^4008^156^50861093^280086392^02^20230801^0004790",
                "site": "1000",
                "barcode": "20238282322",
                "azm": None
            },
            {
                "shop_order": "50861093",
                "sfc": "3^4008^156^50861093^280086392^02^20230801^0004791",
                "site": "1000",
                "barcode": "20238282322",
                "azm": None
            },
            {
                "shop_order": "50861093",
                "sfc": "3^4008^156^50861093^280086392^02^20230801^0004792",
                "site": "1000",
                "barcode": "20238282322",
                "azm": None
            }
        ]
    }


@app.post("/SmartMES/CheckRoute")
def youyue1():
    return {
        "RESULT": "OK",
        "MSG": "OK"
    }


@app.post("/TestData/UploadData")
def youyue1():
    return {
        "RESULT": "OK",
        "MSG": "OK"
    }


@app.post("/webService/DeviceOutBoundFlowCheck")
def xiangbin():
    return {
        "RESULT_FLAG": "OK",
        "ERROR_MSG": "OK"
    }


@app.post("/webService/checkDeviceOutBound")
def xiangbin():
    return {
        "RESULT_FLAG": "OK",
        "ERROR_MSG": "OK"
    }


@app.post("/common/Login")
def xinwangda1():
    return {
        "code": "200",
        "flag": "true",
        "msg": "登录成功",
        "role": 1
    }


@app.post("/common/FactoryInfoDownLoadV1")
def xinwangda2():
    return {
        "code": "200",
        "flag": "true",
        "msg": "请求成功",
        "factoryList": [
            {
                "baseModelCode": "BL",
                "baseModelName": "博罗工厂"
            },
            {
                "baseModelCode": "BL1",
                "baseModelName": "博罗工厂1"
            },
        ]
    }


@app.post("/common/WorkStationDownLoadV1")
def xinwangda2():
    return {
        "code": "200",
        "flag": "true",
        "msg": "请求成功",
        "areaList": [{
            "baseModelCode": "BL-21-2",
            "baseModelName": "博罗21栋2楼"
        }, {
            "baseModelCode": "BL-21-5",
            "baseModelName": "博罗21栋5楼"
        }, {
            "baseModelCode": "BL-21-6",
            "baseModelName": "博罗22栋6楼"
        }]
    }


@app.post("/common/LineDownLoadV1")
def xinwangda2():
    return {
        "code": "200",
        "flag": "true",
        "msg": "请求成功",
        "lineList": [{
            "baseModelCode": "line-5",
            "baseModelName": "5线"
        }, {
            "baseModelCode": "line-6",
            "baseModelName": "6线"
        }, {
            "baseModelCode": "line-7",
            "baseModelName": "7线"
        }]
    }


@app.post("/common/StationDownloadV1")
def xinwangda2():
    return {
        "code": "200",
        "flag": "true",
        "msg": "请求成功",
        "stationList": [{
            "baseModelCode": "PCM-610C-072",
            "baseModelName": "设备1"
        }, {
            "baseModelCode": "PCM-610C-041",
            "baseModelName": "设备2"
        }, {
            "baseModelCode": "PCM-610C-035",
            "baseModelName": "设备3"
        }]
    }


@app.post("/common/MoInfoDownLoad")
def xinwangda2():
    return {"code": "200", "flag": "true", "msg": "请求成功", "moList": [
        {"moNumber": "MO201307110019-1401", "productSpec": "1005000001222", "model": None, "qty": 20, "restQty": 20,
         "dateBegin": "2013/7/12 17:09:44", "dateComplete": "2013/7/12 18:09:44", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306180024-1401", "productSpec": "1005000001192", "model": None, "qty": 2, "restQty": 2,
         "dateBegin": "2013/6/20 17:17:01", "dateComplete": "2013/6/20 18:17:01", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304020005-1401", "productSpec": "1005020000192", "model": None, "qty": 5, "restQty": 5,
         "dateBegin": "2013/4/5 10:47:53", "dateComplete": "2013/4/5 11:47:53", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201302280031-1401", "productSpec": "1005000001242", "model": None, "qty": 557, "restQty": 557,
         "dateBegin": "2013/3/1 8:31:09", "dateComplete": "2013/3/1 9:31:09", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303290007-1401", "productSpec": "1005000001242", "model": None, "qty": 195, "restQty": 195,
         "dateBegin": "2013/4/5 11:01:06", "dateComplete": "2013/4/5 12:01:06", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212270013-1401", "productSpec": "1005000001252", "model": None, "qty": 380, "restQty": 380,
         "dateBegin": "2013/1/5 10:09:02", "dateComplete": "2013/1/5 11:09:02", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301180036-1401", "productSpec": "1005000001252", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/1/23 20:19:57", "dateComplete": "2013/1/23 21:19:57", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308130008-1401", "productSpec": "1005000001252", "model": None, "qty": 10, "restQty": 10,
         "dateBegin": "2013/8/16 8:52:50", "dateComplete": "2013/8/16 9:52:50", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311200039-1401", "productSpec": "1005000001252", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/11/20 18:43:56", "dateComplete": "2013/11/20 19:43:56", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308090006-1401", "productSpec": "1005000001322", "model": None, "qty": 26, "restQty": 26,
         "dateBegin": "2013/8/12 20:05:57", "dateComplete": "2013/8/12 21:05:57", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307190059-1401", "productSpec": "1005000001322", "model": None, "qty": 60, "restQty": 60,
         "dateBegin": "2013/7/24 11:47:18", "dateComplete": "2013/7/24 12:47:18", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309040026-1401", "productSpec": "1005000001322", "model": None, "qty": 130, "restQty": 130,
         "dateBegin": "2013/9/6 18:56:28", "dateComplete": "2013/9/6 19:56:28", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312060011-1401", "productSpec": "1005000001322", "model": None, "qty": 180, "restQty": 180,
         "dateBegin": "2013/12/7 18:39:44", "dateComplete": "2013/12/7 19:39:44", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303290001-1401", "productSpec": "1005000001182", "model": None, "qty": 326, "restQty": 326,
         "dateBegin": "2013/3/29 11:22:07", "dateComplete": "2013/3/29 12:22:07", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303250010-1401", "productSpec": "1005000001182", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/3/25 11:56:32", "dateComplete": "2013/3/25 12:56:32", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304010020-1401", "productSpec": "1005000001182", "model": None, "qty": 144, "restQty": 144,
         "dateBegin": "2013/4/7 17:00:41", "dateComplete": "2013/4/7 18:00:41", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307090014-1401", "productSpec": "1005000001182", "model": None, "qty": 45, "restQty": 45,
         "dateBegin": "2013/7/12 16:53:40", "dateComplete": "2013/7/12 17:53:40", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311070037-1401", "productSpec": "1005000001182", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/11/8 20:05:29", "dateComplete": "2013/11/8 21:05:29", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312060073-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/12/7 18:50:02", "dateComplete": "2013/12/7 19:50:02", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309130019-1401", "productSpec": "1005000001182", "model": None, "qty": 80, "restQty": 80,
         "dateBegin": "2013/9/23 20:44:00", "dateComplete": "2013/9/23 21:44:00", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403050007-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2014/3/11 16:57:55", "dateComplete": "2014/3/11 17:57:55", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309130025-1401", "productSpec": "1005000001222", "model": None, "qty": 179, "restQty": 179,
         "dateBegin": "2013/9/18 16:40:13", "dateComplete": "2013/9/18 17:40:13", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305030001-1401", "productSpec": "1005020000092", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/5/8 16:39:20", "dateComplete": "2013/5/8 17:39:20", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303090006-1401", "productSpec": "1005020000192", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/3/22 10:47:21", "dateComplete": "2013/3/22 11:47:21", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307040018-1401", "productSpec": "1005020000192", "model": None, "qty": 201, "restQty": 201,
         "dateBegin": "2013/7/5 19:03:30", "dateComplete": "2013/7/5 20:03:30", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312090037-1401", "productSpec": "1005020000192", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/12/11 20:52:58", "dateComplete": "2013/12/11 21:52:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403030019-1401", "productSpec": "1005020000192", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2014/3/12 10:50:34", "dateComplete": "2014/3/12 11:50:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403040022-1401", "productSpec": "1005020000192", "model": None, "qty": 160, "restQty": 160,
         "dateBegin": "2014/3/12 10:53:03", "dateComplete": "2014/3/12 11:53:03", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401210028-1401", "productSpec": "1005020000652", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2014/2/17 14:54:15", "dateComplete": "2014/2/17 15:54:15", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403110014-1401", "productSpec": "1005020000652", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2014/3/17 14:15:49", "dateComplete": "2014/3/17 15:15:49", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301170029-1401", "productSpec": "1005000001242", "model": None, "qty": 668, "restQty": 668,
         "dateBegin": "2013/1/23 12:20:16", "dateComplete": "2013/1/23 13:20:16", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303050005-1401", "productSpec": "1005000001242", "model": None, "qty": 350, "restQty": 350,
         "dateBegin": "2013/3/11 12:13:18", "dateComplete": "2013/3/11 13:13:18", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307030041-1401", "productSpec": "1005000001242", "model": None, "qty": 23, "restQty": 23,
         "dateBegin": "2013/7/5 16:34:55", "dateComplete": "2013/7/5 17:34:55", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310300050-1401", "productSpec": "1005000001242", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/6 20:30:15", "dateComplete": "2013/11/6 21:30:15", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312270009-1401", "productSpec": "1005000001242", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2014/1/4 20:47:01", "dateComplete": "2014/1/4 21:47:01", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401100049-1401", "productSpec": "1005000001242", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2014/1/11 17:43:58", "dateComplete": "2014/1/11 18:43:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309130026-1401", "productSpec": "1005000001272", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/9/16 9:22:51", "dateComplete": "2013/9/16 10:22:51", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305140047-1401", "productSpec": "1005000001252", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/5/18 10:51:46", "dateComplete": "2013/5/18 11:51:46", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309300006-1401", "productSpec": "1005000001252", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/10/7 16:53:01", "dateComplete": "2013/10/7 17:53:01", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310150015-1401", "productSpec": "1005020000182", "model": None, "qty": 3, "restQty": 3,
         "dateBegin": "2013/10/21 19:37:19", "dateComplete": "2013/10/21 20:37:19", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212210029-1401", "productSpec": "1005000001322", "model": None, "qty": 215, "restQty": 215,
         "dateBegin": "2012/12/29 10:03:12", "dateComplete": "2012/12/29 11:03:12", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305230031-1401", "productSpec": "1005000001322", "model": None, "qty": 140, "restQty": 140,
         "dateBegin": "2013/5/30 12:07:00", "dateComplete": "2013/5/30 13:07:00", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201302200006-1401", "productSpec": "1005020000212", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/2/20 14:44:00", "dateComplete": "2013/2/20 15:44:00", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307080031-1401", "productSpec": "1005020000212", "model": None, "qty": 8, "restQty": 8,
         "dateBegin": "2013/7/10 9:07:42", "dateComplete": "2013/7/10 10:07:42", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310290008-1401", "productSpec": "1005020000212", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/11/1 8:31:24", "dateComplete": "2013/11/1 9:31:24", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311150039-1401", "productSpec": "1005020000212", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/16 19:03:53", "dateComplete": "2013/11/16 20:03:53", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311260050-1401", "productSpec": "1005020000212", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/27 21:26:02", "dateComplete": "2013/11/27 22:26:02", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303070007-1401", "productSpec": "1005000001262", "model": None, "qty": 624, "restQty": 624,
         "dateBegin": "2013/3/7 10:11:04", "dateComplete": "2013/3/7 11:11:04", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301110003-1401", "productSpec": "1005000001182", "model": None, "qty": 434, "restQty": 434,
         "dateBegin": "2013/1/16 14:14:14", "dateComplete": "2013/1/16 15:14:14", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303220005-1401", "productSpec": "1005000001182", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/3/22 13:01:04", "dateComplete": "2013/3/22 14:01:04", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307190067-1401", "productSpec": "1005000001182", "model": None, "qty": 70, "restQty": 70,
         "dateBegin": "2013/7/24 11:49:34", "dateComplete": "2013/7/24 12:49:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308090023-1401", "productSpec": "1005000001182", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/8/16 8:57:33", "dateComplete": "2013/8/16 9:57:33", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301240001-1401", "productSpec": "1005000001222", "model": None, "qty": 26, "restQty": 26,
         "dateBegin": "2013/1/24 10:49:22", "dateComplete": "2013/1/24 11:49:22", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304250014-1401", "productSpec": "1005020000092", "model": None, "qty": 250, "restQty": 250,
         "dateBegin": "2013/5/8 8:49:03", "dateComplete": "2013/5/8 9:49:03", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402270054-1401", "productSpec": "1005020000192", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2014/3/1 16:10:03", "dateComplete": "2014/3/1 17:10:03", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403060043-1401", "productSpec": "1005020000652", "model": None, "qty": 182, "restQty": 182,
         "dateBegin": "2014/3/12 11:48:37", "dateComplete": "2014/3/12 12:48:37", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309060018-1401", "productSpec": "1005000001232", "model": None, "qty": 20, "restQty": 20,
         "dateBegin": "2013/9/6 19:23:34", "dateComplete": "2013/9/6 20:23:34", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306280036-1401", "productSpec": "1005000001242", "model": None, "qty": 10, "restQty": 10,
         "dateBegin": "2013/7/2 16:22:00", "dateComplete": "2013/7/2 17:22:00", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308020025-1401", "productSpec": "1005000001242", "model": None, "qty": 98, "restQty": 98,
         "dateBegin": "2013/8/8 20:07:19", "dateComplete": "2013/8/8 21:07:19", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308260018-1401", "productSpec": "1005000001242", "model": None, "qty": 70, "restQty": 70,
         "dateBegin": "2013/8/29 19:09:07", "dateComplete": "2013/8/29 20:09:07", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401060027-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2014/1/8 20:32:58", "dateComplete": "2014/1/8 21:32:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402260023-1401", "productSpec": "1005000001242", "model": None, "qty": 138, "restQty": 138,
         "dateBegin": "2014/3/1 16:07:50", "dateComplete": "2014/3/1 17:07:50", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201302200007-1401", "productSpec": "1005000001252", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/2/20 14:45:35", "dateComplete": "2013/2/20 15:45:35", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305100003-1401", "productSpec": "1005000001252", "model": None, "qty": 19, "restQty": 19,
         "dateBegin": "2013/5/13 18:12:37", "dateComplete": "2013/5/13 19:12:37", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401140077-1401", "productSpec": "1005000001252", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2014/1/21 16:22:06", "dateComplete": "2014/1/21 17:22:06", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401200064-1401", "productSpec": "1005020000881", "model": None, "qty": 610, "restQty": 610,
         "dateBegin": "2014/1/28 17:31:48", "dateComplete": "2014/1/28 18:31:48", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303090005-1401", "productSpec": "1005000001322", "model": None, "qty": 80, "restQty": 80,
         "dateBegin": "2013/3/11 14:10:01", "dateComplete": "2013/3/11 15:10:01", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308290030-1401", "productSpec": "1005000001322", "model": None, "qty": 110, "restQty": 110,
         "dateBegin": "2013/9/3 16:14:07", "dateComplete": "2013/9/3 17:14:07", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310280040-1401", "productSpec": "1005000001322", "model": None, "qty": 20, "restQty": 20,
         "dateBegin": "2013/10/28 20:31:35", "dateComplete": "2013/10/28 21:31:35", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304110006-1401", "productSpec": "1005020000212", "model": None, "qty": 18, "restQty": 18,
         "dateBegin": "2013/4/11 11:22:53", "dateComplete": "2013/4/11 12:22:53", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310210080-1401", "productSpec": "1005020000212", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/10/26 15:12:19", "dateComplete": "2013/10/26 16:12:19", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301290045-1401", "productSpec": "1005000001262", "model": None, "qty": 148, "restQty": 148,
         "dateBegin": "2013/1/31 9:29:58", "dateComplete": "2013/1/31 10:29:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306180001-1401", "productSpec": "1005000001262", "model": None, "qty": 5, "restQty": 5,
         "dateBegin": "2013/6/20 17:07:43", "dateComplete": "2013/6/20 18:07:43", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306180002-1401", "productSpec": "1005020000152", "model": None, "qty": 15, "restQty": 15,
         "dateBegin": "2013/6/20 17:25:32", "dateComplete": "2013/6/20 18:25:32", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301290049-1401", "productSpec": "1005000001182", "model": None, "qty": 550, "restQty": 550,
         "dateBegin": "2013/1/29 18:55:50", "dateComplete": "2013/1/29 19:55:50", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306280035-1401", "productSpec": "1005000001182", "model": None, "qty": 11, "restQty": 11,
         "dateBegin": "2013/7/2 16:15:14", "dateComplete": "2013/7/2 17:15:14", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312260051-1401", "productSpec": "1005000001182", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/12/25 10:02:21", "dateComplete": "2013/12/25 11:02:21", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402240029-1401", "productSpec": "1005000001182", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2014/2/25 19:56:41", "dateComplete": "2014/2/25 20:56:41", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304190001-1401", "productSpec": "1005000001292", "model": None, "qty": 180, "restQty": 180,
         "dateBegin": "2013/4/24 15:51:13", "dateComplete": "2013/4/24 16:51:13", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303130043-1401", "productSpec": "1005000001222", "model": None, "qty": 28, "restQty": 28,
         "dateBegin": "2013/3/20 15:38:26", "dateComplete": "2013/3/20 16:38:26", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310280041-1401", "productSpec": "1005000001222", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/10/28 20:32:16", "dateComplete": "2013/10/28 21:32:16", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312270051-1401", "productSpec": "1005020000192", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/12/31 12:24:37", "dateComplete": "2013/12/31 13:24:37", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301300005-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/30 14:03:24", "dateComplete": "2013/1/30 15:03:24", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401020022-1401", "productSpec": "1005000001242", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2014/1/7 14:50:08", "dateComplete": "2014/1/7 15:50:08", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312030006-1401", "productSpec": "1005000001242", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/12/4 21:42:10", "dateComplete": "2013/12/4 22:42:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307250044-1401", "productSpec": "1005000001252", "model": None, "qty": 15, "restQty": 15,
         "dateBegin": "2013/7/27 17:34:34", "dateComplete": "2013/7/27 18:34:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312180058-1401", "productSpec": "1005000001252", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/12/22 17:24:46", "dateComplete": "2013/12/22 18:24:46", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401210001-1401", "productSpec": "1005000001252", "model": None, "qty": 600, "restQty": 600,
         "dateBegin": "2014/2/8 15:56:44", "dateComplete": "2014/2/8 16:56:44", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311270008-1401", "productSpec": "1005000001252", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/11/27 21:29:19", "dateComplete": "2013/11/27 22:29:19", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306050014-1401", "productSpec": "1005020000182", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/6/7 17:58:45", "dateComplete": "2013/6/7 18:58:45", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312250011-1401", "productSpec": "1005000001322", "model": None, "qty": 310, "restQty": 312,
         "dateBegin": "2013/12/28 18:11:24", "dateComplete": "2013/12/28 19:11:24", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305090004-1401", "productSpec": "1005000001262", "model": None, "qty": 37, "restQty": 37,
         "dateBegin": "2013/5/14 14:51:47", "dateComplete": "2013/5/14 15:51:47", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307010013-1401", "productSpec": "1005000001262", "model": None, "qty": 40, "restQty": 40,
         "dateBegin": "2013/7/2 16:23:51", "dateComplete": "2013/7/2 17:23:51", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311200040-1401", "productSpec": "1005000001262", "model": None, "qty": 60, "restQty": 60,
         "dateBegin": "2013/11/20 18:45:11", "dateComplete": "2013/11/20 19:45:11", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309060015-1401", "productSpec": "1005020000152", "model": None, "qty": 57, "restQty": 57,
         "dateBegin": "2013/9/6 19:22:20", "dateComplete": "2013/9/6 20:22:20", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303160007-1401", "productSpec": "1005000001182", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/3/21 20:18:54", "dateComplete": "2013/3/21 21:18:54", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305150014-1401", "productSpec": "1005000001182", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/5/18 16:29:41", "dateComplete": "2013/5/18 17:29:41", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306070018-1401", "productSpec": "1005000001182", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/6/14 20:05:16", "dateComplete": "2013/6/14 21:05:16", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311290026-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/30 19:06:49", "dateComplete": "2013/11/30 20:06:49", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402280039-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2014/3/1 16:12:36", "dateComplete": "2014/3/1 17:12:36", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402190001-1401", "productSpec": "1005000001182", "model": None, "qty": 60, "restQty": 60,
         "dateBegin": "2014/2/22 17:00:21", "dateComplete": "2014/2/22 18:00:21", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310170040-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/10/19 10:57:11", "dateComplete": "2013/10/19 11:57:11", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306040041-1401", "productSpec": "1005000001282", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/6/4 19:19:20", "dateComplete": "2013/6/4 20:19:20", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309270012-1401", "productSpec": "1005000001282", "model": None, "qty": 50, "restQty": 50,
         "dateBegin": "2013/9/29 19:57:10", "dateComplete": "2013/9/29 20:57:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308120021-1401", "productSpec": "1008000000622", "model": None, "qty": 200, "restQty": 17,
         "dateBegin": "2013/8/16 14:17:23", "dateComplete": "2013/8/16 15:17:23", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307090012-1401", "productSpec": "1005020000092", "model": None, "qty": 54, "restQty": 54,
         "dateBegin": "2013/7/12 16:55:10", "dateComplete": "2013/7/12 17:55:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301180034-1401", "productSpec": "1005000001192", "model": None, "qty": 50, "restQty": 50,
         "dateBegin": "2013/2/25 21:09:52", "dateComplete": "2013/2/25 22:09:52", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212270017-1401", "productSpec": "1005020000192", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2012/12/29 10:04:35", "dateComplete": "2012/12/29 11:04:35", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301070006-1401", "productSpec": "1005020000192", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/1/9 14:12:23", "dateComplete": "2013/1/9 15:12:23", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308090008-1401", "productSpec": "1005020000192", "model": None, "qty": 163, "restQty": 163,
         "dateBegin": "2013/8/15 8:41:15", "dateComplete": "2013/8/15 9:41:15", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311280018-1401", "productSpec": "1005020000192", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/11/30 19:02:14", "dateComplete": "2013/11/30 20:02:14", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402110010-1401", "productSpec": "1005020000192", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2014/2/15 15:51:39", "dateComplete": "2014/2/15 16:51:39", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303200013-1401", "productSpec": "1005000001232", "model": None, "qty": 17, "restQty": 17,
         "dateBegin": "2013/3/22 10:58:34", "dateComplete": "2013/3/22 11:58:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301100002-1401", "productSpec": "1005000001242", "model": None, "qty": 182, "restQty": 182,
         "dateBegin": "2013/1/14 20:05:34", "dateComplete": "2013/1/14 21:05:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301150022-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/19 19:18:14", "dateComplete": "2013/1/19 20:18:14", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303290009-1401", "productSpec": "1005000001242", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/4/7 21:55:59", "dateComplete": "2013/4/7 22:55:59", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305110003-1401", "productSpec": "1005000001242", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/5/14 16:08:20", "dateComplete": "2013/5/14 17:08:20", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311120042-1401401", "productSpec": "1005000001242", "model": None, "qty": 400,
         "restQty": 400, "dateBegin": "2013/11/14 11:32:36", "dateComplete": "2013/11/14 12:32:36",
         "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402180016-1401", "productSpec": "1005000001242", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2014/2/27 14:22:37", "dateComplete": "2014/2/27 15:22:37", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311140043-1401", "productSpec": "1005000001242", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/11/16 18:53:15", "dateComplete": "2013/11/16 19:53:15", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306190021-1401", "productSpec": "1005000001252", "model": None, "qty": 35, "restQty": 35,
         "dateBegin": "2013/6/24 19:41:16", "dateComplete": "2013/6/24 20:41:16", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309040013-1401", "productSpec": "1005000001252", "model": None, "qty": 80, "restQty": 80,
         "dateBegin": "2013/9/9 15:48:22", "dateComplete": "2013/9/9 16:48:22", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311110064-1401", "productSpec": "1005000001252", "model": None, "qty": 220, "restQty": 220,
         "dateBegin": "2013/11/11 20:06:24", "dateComplete": "2013/11/11 21:06:24", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307080033-1401", "productSpec": "1005020000182", "model": None, "qty": 55, "restQty": 55,
         "dateBegin": "2013/7/10 9:03:21", "dateComplete": "2013/7/10 10:03:21", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306170016-1401", "productSpec": "1005000001322", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/6/21 9:48:39", "dateComplete": "2013/6/21 10:48:39", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305230033-1401", "productSpec": "1005020000212", "model": None, "qty": 114, "restQty": 114,
         "dateBegin": "2013/5/28 16:32:10", "dateComplete": "2013/5/28 17:32:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403190050-1401", "productSpec": "1005020000222", "model": None, "qty": 80, "restQty": 80,
         "dateBegin": "2014/3/21 19:34:59", "dateComplete": "2014/3/21 20:34:59", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307040010-1401", "productSpec": "1005000001262", "model": None, "qty": 90, "restQty": 90,
         "dateBegin": "2013/7/5 18:59:58", "dateComplete": "2013/7/5 19:59:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309090009-1401", "productSpec": "1005000001262", "model": None, "qty": 187, "restQty": 187,
         "dateBegin": "2013/9/11 19:08:23", "dateComplete": "2013/9/11 20:08:23", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309240019-1401", "productSpec": "1005000001262", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/9/25 19:11:07", "dateComplete": "2013/9/25 20:11:07", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401160016-1401", "productSpec": "1005000001262", "model": None, "qty": 12, "restQty": 12,
         "dateBegin": "2014/1/21 16:34:59", "dateComplete": "2014/1/21 17:34:59", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304070027-1401", "productSpec": "1005000001182", "model": None, "qty": 226, "restQty": 226,
         "dateBegin": "2013/4/7 17:03:01", "dateComplete": "2013/4/7 18:03:01", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307250047-1401", "productSpec": "1005000001182", "model": None, "qty": 30, "restQty": 30,
         "dateBegin": "2013/7/27 17:37:03", "dateComplete": "2013/7/27 18:37:03", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309130014-1401", "productSpec": "1005020000761", "model": None, "qty": 36, "restQty": 36,
         "dateBegin": "2013/9/17 20:18:21", "dateComplete": "2013/9/17 21:18:21", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309060009-1401", "productSpec": "1005000001292", "model": None, "qty": 30, "restQty": 30,
         "dateBegin": "2013/9/6 19:14:39", "dateComplete": "2013/9/6 20:14:39", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311010009-1401", "productSpec": "1005000001292", "model": None, "qty": 199, "restQty": 199,
         "dateBegin": "2013/11/4 20:07:59", "dateComplete": "2013/11/4 21:07:59", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306170010-1401", "productSpec": "1005000001282", "model": None, "qty": 80, "restQty": 80,
         "dateBegin": "2013/6/18 18:24:38", "dateComplete": "2013/6/18 19:24:38", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401160015-1401", "productSpec": "1005000001282", "model": None, "qty": 29, "restQty": 29,
         "dateBegin": "2014/1/21 16:33:04", "dateComplete": "2014/1/21 17:33:04", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403190024-1401", "productSpec": "1005000001222", "model": None, "qty": 20, "restQty": 20,
         "dateBegin": "2014/3/21 19:33:27", "dateComplete": "2014/3/21 20:33:27", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306060010-1401", "productSpec": "1005020000092", "model": None, "qty": 70, "restQty": 70,
         "dateBegin": "2013/6/9 18:05:52", "dateComplete": "2013/6/9 19:05:52", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312050011-1401", "productSpec": "1005000001192", "model": None, "qty": 198, "restQty": 198,
         "dateBegin": "2013/12/7 18:30:10", "dateComplete": "2013/12/7 19:30:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303210028-1401", "productSpec": "1005020000192", "model": None, "qty": 133, "restQty": 133,
         "dateBegin": "2013/3/26 15:17:05", "dateComplete": "2013/3/26 16:17:05", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312230004-1401", "productSpec": "1005020000192", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/12/28 9:43:56", "dateComplete": "2013/12/28 10:43:56", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401210056-1401", "productSpec": "1005020000192", "model": None, "qty": 600, "restQty": 600,
         "dateBegin": "2014/2/7 20:04:32", "dateComplete": "2014/2/7 21:04:32", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212210011-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/7 8:54:07", "dateComplete": "2013/1/7 9:54:07", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201302210024-1401", "productSpec": "1005000001242", "model": None, "qty": 796, "restQty": 796,
         "dateBegin": "2013/2/21 12:11:15", "dateComplete": "2013/2/21 13:11:15", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303150062-1401", "productSpec": "1005000001242", "model": None, "qty": 374, "restQty": 374,
         "dateBegin": "2013/3/20 9:21:43", "dateComplete": "2013/3/20 10:21:43", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305130021-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/5/14 14:27:34", "dateComplete": "2013/5/14 15:27:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305300005-1401", "productSpec": "1005000001242", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/6/5 16:23:20", "dateComplete": "2013/6/5 17:23:20", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309180063-1401", "productSpec": "1005000001242", "model": None, "qty": 380, "restQty": 380,
         "dateBegin": "2013/9/18 20:45:30", "dateComplete": "2013/9/18 21:45:30", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401080031-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2014/1/9 19:23:43", "dateComplete": "2014/1/9 20:23:43", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310090006-1401", "productSpec": "1005000001252", "model": None, "qty": 149, "restQty": 149,
         "dateBegin": "2013/10/15 14:42:56", "dateComplete": "2013/10/15 15:42:56", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403070050-1401", "productSpec": "1005000001252", "model": None, "qty": 70, "restQty": 70,
         "dateBegin": "2014/3/11 16:54:39", "dateComplete": "2014/3/11 17:54:39", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301080051-1401", "productSpec": "1005020000671", "model": None, "qty": 52, "restQty": 52,
         "dateBegin": "2013/1/15 16:33:43", "dateComplete": "2013/1/15 17:33:43", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301290001-1401", "productSpec": "1005000001322", "model": None, "qty": 155, "restQty": 155,
         "dateBegin": "2013/2/5 11:02:58", "dateComplete": "2013/2/5 12:02:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304010035-1401", "productSpec": "1005000001322", "model": None, "qty": 258, "restQty": 258,
         "dateBegin": "2013/4/2 18:17:20", "dateComplete": "2013/4/2 19:17:20", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306060009-1401", "productSpec": "1005000001322", "model": None, "qty": 60, "restQty": 60,
         "dateBegin": "2013/6/9 8:38:47", "dateComplete": "2013/6/9 9:38:47", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201403180048-1401", "productSpec": "1005000001322", "model": None, "qty": 94, "restQty": 94,
         "dateBegin": "2014/3/21 19:34:19", "dateComplete": "2014/3/21 20:34:19", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212260056-1401", "productSpec": "1005020000212", "model": None, "qty": 292, "restQty": 292,
         "dateBegin": "2012/12/29 10:07:10", "dateComplete": "2012/12/29 11:07:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311220050-1401", "productSpec": "1005020000212", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/25 21:17:50", "dateComplete": "2013/11/25 22:17:50", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310300003-1401", "productSpec": "1005020000222", "model": None, "qty": 50, "restQty": 50,
         "dateBegin": "2013/11/1 9:33:13", "dateComplete": "2013/11/1 10:33:13", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212050029-1401", "productSpec": "1005000001182", "model": None, "qty": 120, "restQty": 120,
         "dateBegin": "2013/1/16 19:53:57", "dateComplete": "2013/1/16 20:53:57", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305090003-1401", "productSpec": "1005000001182", "model": None, "qty": 32, "restQty": 32,
         "dateBegin": "2013/5/13 18:17:46", "dateComplete": "2013/5/13 19:17:46", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305230008-1401", "productSpec": "1005000001182", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/5/28 17:56:09", "dateComplete": "2013/5/28 18:56:09", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308260015-1401", "productSpec": "1005000001292", "model": None, "qty": 120, "restQty": 120,
         "dateBegin": "2013/8/29 9:54:25", "dateComplete": "2013/8/29 10:54:25", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311080008-1401", "productSpec": "1005000001222", "model": None, "qty": 140, "restQty": 140,
         "dateBegin": "2013/11/8 20:20:48", "dateComplete": "2013/11/8 21:20:48", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310300004-1401", "productSpec": "1005020000092", "model": None, "qty": 250, "restQty": 250,
         "dateBegin": "2013/11/7 10:44:40", "dateComplete": "2013/11/7 11:44:40", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301080033-1401", "productSpec": "1005020000192", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/9 14:04:53", "dateComplete": "2013/1/9 15:04:53", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307170052-1401", "productSpec": "1005020000192", "model": None, "qty": 13, "restQty": 13,
         "dateBegin": "2013/7/22 16:22:00", "dateComplete": "2013/7/22 17:22:00", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309060014-1401", "productSpec": "1005020000192", "model": None, "qty": 125, "restQty": 125,
         "dateBegin": "2013/9/6 19:17:46", "dateComplete": "2013/9/6 20:17:46", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402180015-1401", "productSpec": "1005020000192", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2014/2/21 16:43:32", "dateComplete": "2014/2/21 17:43:32", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201401160061-1401", "productSpec": "1005020000652", "model": None, "qty": 1000, "restQty": 1000,
         "dateBegin": "2014/1/23 9:24:35", "dateComplete": "2014/1/23 10:24:35", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212260067-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/9 14:08:09", "dateComplete": "2013/1/9 15:08:09", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201302260001-1401", "productSpec": "1005000001242", "model": None, "qty": 439, "restQty": 439,
         "dateBegin": "2013/2/26 9:20:21", "dateComplete": "2013/2/26 10:20:21", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304020008-1401", "productSpec": "1005000001242", "model": None, "qty": 50, "restQty": 50,
         "dateBegin": "2013/4/18 16:27:42", "dateComplete": "2013/4/18 17:27:42", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306170037-1401", "productSpec": "1005000001242", "model": None, "qty": 7, "restQty": 7,
         "dateBegin": "2013/6/20 17:13:51", "dateComplete": "2013/6/20 18:13:51", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309120021-1401", "productSpec": "1005000001242", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/9/18 17:59:18", "dateComplete": "2013/9/18 18:59:18", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310120009-1401", "productSpec": "1005000001242", "model": None, "qty": 154, "restQty": 154,
         "dateBegin": "2013/10/13 12:12:05", "dateComplete": "2013/10/13 13:12:05", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305090034-1401", "productSpec": "1005000001252", "model": None, "qty": 110, "restQty": 110,
         "dateBegin": "2013/5/13 18:11:12", "dateComplete": "2013/5/13 19:11:12", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402250037-1401", "productSpec": "1005020003511", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2014/3/7 9:57:06", "dateComplete": "2014/3/7 10:57:06", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309090011-1401", "productSpec": "1005020000182", "model": None, "qty": 70, "restQty": 70,
         "dateBegin": "2013/9/11 19:04:23", "dateComplete": "2013/9/11 20:04:23", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312250046-1401", "productSpec": "1005020000182", "model": None, "qty": 40, "restQty": 40,
         "dateBegin": "2013/12/29 21:45:42", "dateComplete": "2013/12/29 22:45:42", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301180022-1401", "productSpec": "1005000001322", "model": None, "qty": 130, "restQty": 130,
         "dateBegin": "2013/1/22 19:11:38", "dateComplete": "2013/1/22 20:11:38", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307080025-1401", "productSpec": "1005000001322", "model": None, "qty": 60, "restQty": 60,
         "dateBegin": "2013/7/10 9:00:31", "dateComplete": "2013/7/10 10:00:31", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301290059-1401", "productSpec": "1005020000212", "model": None, "qty": 350, "restQty": 350,
         "dateBegin": "2013/1/29 18:57:34", "dateComplete": "2013/1/29 19:57:34", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201402140062-1401", "productSpec": "1005020000212", "model": None, "qty": 75, "restQty": 75,
         "dateBegin": "2014/2/19 11:34:40", "dateComplete": "2014/2/19 12:34:40", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303200014-1401", "productSpec": "1005000001302", "model": None, "qty": 21, "restQty": 21,
         "dateBegin": "2013/3/27 19:39:14", "dateComplete": "2013/3/27 20:39:14", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303190033-1401", "productSpec": "1005000001262", "model": None, "qty": 50, "restQty": 50,
         "dateBegin": "2013/3/22 10:49:08", "dateComplete": "2013/3/22 11:49:08", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201312110044-1401", "productSpec": "1005000001262", "model": None, "qty": 45, "restQty": 45,
         "dateBegin": "2013/12/11 21:04:16", "dateComplete": "2013/12/11 22:04:16", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301300001-1401", "productSpec": "1005000001182", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/30 14:05:38", "dateComplete": "2013/1/30 15:05:38", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201302220029-1401", "productSpec": "1005000001182", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/3/3 20:24:46", "dateComplete": "2013/3/3 21:24:46", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306210011-1401", "productSpec": "1005000001182", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/6/26 14:33:16", "dateComplete": "2013/6/26 15:33:16", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306240009-1401", "productSpec": "1005000001182", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/7/2 16:25:23", "dateComplete": "2013/7/2 17:25:23", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310230015-1401", "productSpec": "1005000001182", "model": None, "qty": 400, "restQty": 400,
         "dateBegin": "2013/10/29 20:35:00", "dateComplete": "2013/10/29 21:35:00", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311140026-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/16 18:50:15", "dateComplete": "2013/11/16 19:50:15", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306280031-1401", "productSpec": "1005020000761", "model": None, "qty": 120, "restQty": 120,
         "dateBegin": "2013/7/2 18:13:44", "dateComplete": "2013/7/2 19:13:44", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306130052-1401", "productSpec": "1005000001292", "model": None, "qty": 45, "restQty": 45,
         "dateBegin": "2013/6/19 16:09:56", "dateComplete": "2013/6/19 17:09:56", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307010014-1401", "productSpec": "1005000001292", "model": None, "qty": 290, "restQty": 290,
         "dateBegin": "2013/7/5 16:27:09", "dateComplete": "2013/7/5 17:27:09", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304010018-1401", "productSpec": "1005000001222", "model": None, "qty": 9, "restQty": 9,
         "dateBegin": "2013/4/7 17:23:41", "dateComplete": "2013/4/7 18:23:41", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311180053-1401", "productSpec": "1005020000092", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/11/20 14:25:35", "dateComplete": "2013/11/20 15:25:35", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306040040-1401", "productSpec": "1005000001192", "model": None, "qty": 58, "restQty": 58,
         "dateBegin": "2013/6/4 19:15:53", "dateComplete": "2013/6/4 20:15:53", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308290027-1401", "productSpec": "1005000001192", "model": None, "qty": 151, "restQty": 151,
         "dateBegin": "2013/9/3 12:16:09", "dateComplete": "2013/9/3 13:16:09", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305200015-1401", "productSpec": "1005020000192", "model": None, "qty": 120, "restQty": 120,
         "dateBegin": "2013/5/23 11:34:32", "dateComplete": "2013/5/23 12:34:32", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301250006-1401", "productSpec": "1005000001242", "model": None, "qty": 500, "restQty": 500,
         "dateBegin": "2013/1/30 13:29:55", "dateComplete": "2013/1/30 14:29:55", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306030046-1401", "productSpec": "1005000001242", "model": None, "qty": 129, "restQty": 129,
         "dateBegin": "2013/6/4 19:13:48", "dateComplete": "2013/6/4 20:13:48", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201308210045-1401", "productSpec": "1005000001242", "model": None, "qty": 300, "restQty": 300,
         "dateBegin": "2013/8/26 16:15:59", "dateComplete": "2013/8/26 17:15:59", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309060080-1401", "productSpec": "1005000001242", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/9/11 17:05:27", "dateComplete": "2013/9/11 18:05:27", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201212240016-1401", "productSpec": "1005000001252", "model": None, "qty": 314, "restQty": 314,
         "dateBegin": "2012/12/27 12:08:46", "dateComplete": "2012/12/27 13:08:46", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305280009-1401", "productSpec": "1005000001252", "model": None, "qty": 290, "restQty": 290,
         "dateBegin": "2013/6/3 14:08:50", "dateComplete": "2013/6/3 15:08:50", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311010062-1401", "productSpec": "1005000001252", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/2 18:22:12", "dateComplete": "2013/11/2 19:22:12", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301280008-1401", "productSpec": "1005000001322", "model": None, "qty": 345, "restQty": 345,
         "dateBegin": "2013/1/28 14:47:28", "dateComplete": "2013/1/28 15:47:28", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201303190008-1401", "productSpec": "1005000001322", "model": None, "qty": 100, "restQty": 100,
         "dateBegin": "2013/3/19 14:19:26", "dateComplete": "2013/3/19 15:19:26", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201304100005-1401", "productSpec": "1005000001322", "model": None, "qty": 329, "restQty": 329,
         "dateBegin": "2013/4/10 13:49:32", "dateComplete": "2013/4/10 14:49:32", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306200013-1401", "productSpec": "1005000001322", "model": None, "qty": 25, "restQty": 25,
         "dateBegin": "2013/6/24 19:44:10", "dateComplete": "2013/6/24 20:44:10", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201307250043-1401", "productSpec": "1005000001262", "model": None, "qty": 10, "restQty": 10,
         "dateBegin": "2013/7/27 17:31:58", "dateComplete": "2013/7/27 18:31:58", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201301140003-1401", "productSpec": "1005000001182", "model": None, "qty": 25, "restQty": 25,
         "dateBegin": "2013/1/23 20:22:13", "dateComplete": "2013/1/23 21:22:13", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311050025-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/7 10:53:30", "dateComplete": "2013/11/7 11:53:30", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311040045-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/7 10:54:38", "dateComplete": "2013/11/7 11:54:38", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201311180054-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/11/20 14:28:00", "dateComplete": "2013/11/20 15:28:00", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201310170072-1401", "productSpec": "1005000001182", "model": None, "qty": 200, "restQty": 200,
         "dateBegin": "2013/10/19 16:11:21", "dateComplete": "2013/10/19 17:11:21", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201306130056-1401", "productSpec": "1005020000761", "model": None, "qty": 318, "restQty": 318,
         "dateBegin": "2013/7/2 16:17:41", "dateComplete": "2013/7/2 17:17:41", "moStatus": "计划生效",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309280041-1401", "productSpec": "1005000001292", "model": None, "qty": 97, "restQty": 97,
         "dateBegin": "2013/9/30 16:23:33", "dateComplete": "2013/9/30 17:23:33", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201305230032-1401", "productSpec": "1005000001282", "model": None, "qty": 140, "restQty": 140,
         "dateBegin": "2013/5/29 15:25:29", "dateComplete": "2013/5/29 16:25:29", "moStatus": "生产执行",
         "defaultPrintQty": 0},
        {"moNumber": "MO201309050032-1401", "productSpec": "1005000001282", "model": None, "qty": 150, "restQty": 150,
         "dateBegin": "2013/9/6 19:01:40", "dateComplete": "2013/9/6 20:01:40", "moStatus": "生产执行",
         "defaultPrintQty": 0}]}


@app.post("/common/deviceServices")
def xinwangda2():
    return {
        "taskId": "DEV20231127111156080001",
        " code ": 200,
        "flag": "true",
        "msg": "推送成功",
        "extend1": "",
        "extend2": "",
        "extend3": ""
    }


@app.post("/commonApi/TackProduct.ashx")
def xinwangda_data():
    return {
        "code": 200,
        "msg": "ok"
    }


@app.post("/commonApi/GetSmallPanelSn.ashx")
def xinwangda_data():
    return {
        "code": "200",
        "flag": "true",
        "msg": "请求成功",
        "smallPanelSNList": [{
            "sortID": 1,
            "smallPanelSN": "0HS27GAPH38L635654001"
        },
            {
                "sortID": 2,
                "smallPanelSN": "0HS27GAPH38L635654002"
            },
            {
                "sortID": 3,
                "smallPanelSN": "0HS27GAPH38L635654003"
            }

        ]
    }


@app.post("/test/MesWebServer.asmx")
def xinwangda122(data=Body("default")):
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <GroupTestCommonResponse xmlns="WWW.SUNWODA.COM">
            <GroupTestCommonResult>TRUE:xxx</GroupTestCommonResult>
        </GroupTestCommonResponse>
    </soap:Body>
</soap:Envelope>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/test_xml1")
def xinwangda122(data=Body("default")):
    ret_str = """<Cluster>
<Name></Name>
<NumElts>4</NumElts>
<I32>
<Name>Status</Name>
<Val>200</Val>
</I32>
<Boolean>
<Name>Success</Name>
<Val>1</Val>
</Boolean>
<String>
<Name>Msg</Name>
<Val></Val>
</String>
<Cluster>
<Name>Response</Name>
<NumElts>2</NumElts>
<String>
<Name>ClampNO</Name>
<Val>pcb_barcode001</Val>
</String>
<Array>
<Name>UUTLabelNo</Name>
<Dimsize>2</Dimsize>
<String>
<Name>UUTLabelNo</Name>
<Val>123</Val>
</String>
<String>
<Name>UUTLabelNo</Name>
<Val>456</Val>
</String>
</Array>
</Cluster>
</Cluster>"""
    #     ret_str = "jiandandian"

    return Response(content=ret_str, media_type="application/xml")


@app.post("/test_xml2")
def xinwangda122(data=Body("default")):
    ret_str = """<Cluster>
    <Name></Name>
    <NumElts>4</NumElts>
    <I32>
        <Name>Status</Name>
        <Val>200</Val>
    </I32>
    <Boolean>
        <Name>Success</Name>
        <Val>1</Val>
    </Boolean>
    <String>
        <Name>Msg</Name>
        <Val>该数据已上传！</Val>
    </String>
    <Cluster>
        <Name>Response</Name>
        <NumElts>2</NumElts>
        <String>
            <Name>ClampNO</Name>
            <Val>123456</Val>
        </String>
        <Array>
            <Name>UUTLabelNo</Name>
            <Dimsize>0</Dimsize>
            <String>
                <Name>UUTLabelNo</Name>
                <Val></Val>
            </String>
        </Array>
    </Cluster>
</Cluster>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/mes")
def xinwangda122(data=Body("default")):
    return {"code": "200", "string": ""}


@app.post("/api/Product/CheckSNStatus")
def lvlian(data=Body("default")):
    return {
        "IsSuccess": False,
        "Code": 200,
        "Message": "SN[1233456]不存在",
        "Content": ""
    }


@app.post("/api/Product/PassTerminalBySN")
def lvlian(data=Body("default")):
    return {
        "IsSuccess": False,
        "Code": 200,
        "Message": "SN[1233456]不存在",
        "Content": ""
    }


@app.post("/dc/process")
def dc_process(data=Body("default")):
    return {
        "code": "000001",
        "message": "err"
    }


@app.get("/manufacturing-app/ResourceAPIController.asmx/getProductPN")
def guruiwate(data=Body("default")):
    ret_str = """<Response Result="S" ErrorMsg="获取 PN 码成功" ShopOrder="202212220001" Item="333.999bak" ItemDesc="
监控设备-国际 ShineWiFi-X 整机" />"""
    return Response(content=ret_str, media_type="application/xml")


@app.get("/manufacturing-app/ResourceAPIController.asmx/uploadResrceStandardParam")
def guruiwate(data=Body("default")):
    ret_str = """<Response Result="S" ErrorMsg="获取 PN 码成功" ShopOrder="202212220001" Item="333.999bak" ItemDesc="
监控设备-国际 ShineWiFi-X 整机" />"""
    return Response(content=ret_str, media_type="application/xml")


@app.get("/manufacturing-app/ResourceAPIController.asmx/returnResrceStandardParam")
def guruiwate(data=Body("default")):
    ret_param = {"product": "FtpPath", "task": [{"step0": "0", "sudu": "/test3_test6/333.999bak"}]}

    ret_str2 = json.dumps(ret_param, separators=(",", ":"))
    ret_str3 = json.dumps(ret_str2, separators=(",", ":"))

    ret_str = f"""<Response Result="S" ErrorMsg="<Response Result="S" ErrorMsg="设备参数下发成功!" paramList="{ret_str3}"/>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/WebService/MesWebApi.asmx/GetFCT_ArrangeThePlateWebApi")
def jinghua():
    return {
        "stu": "OK",
        "msg": [{
            "SN": "WORK-20240730000413991",
            "Status": "OK"
        },
            {
                "SN": "WORK-20240730000414000",
                "Status": "OK"
            }]
    }


# @app.get("/AoiDataSfcCheck.aspx")
# def jingcheng():
#     return {
#         "code": 200,
#         "msg": "ok"
#     }


@app.post("/AoiDataSfcCheck.aspx")
def jingcheng():
    return {
        "code": 200,
        "msg": "ok"
    }


@app.get("/test005")
def test2222(data=Body("default")):
    print("data", data)

    return {
        "code": 200,
        "msg": "ok"
    }


@app.post("/api/UserApi/Login")
def hongfan(data=Body("default")):
    return {
        "Success": True,
        "Message": "ok",
        "Result": "fakeresult"
    }


@app.post("/api/M3Logic/Execute/SubmitInspectionResults")
def hongfan(data=Body("default")):
    return {
        "Success": True,
        "Message": "ok",
        "Result": "fakeresult"
    }


@app.post("/api/services/app/BarCodeReady/execute")
def songxiawanbao(data=Body("default")):
    return {
        "result":
            "{\"LineIntCode\":1,\"StationIntCode\":1,\"QueryResult\":16, \"ProductId\":\"fakeproductid\", \"FailedDevicecipeVersion\":0,\"ReOnlineStation\":0}",
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/api/services/app/ResultReady/execute")
def songxiawanbao(data=Body("default")):
    return {
        "result":
            "{\"LineIntCode\":1,\"StationIntCode\":1,\"QueryResult\":1, \"ProductId\":\"fakeproductid\", \"FailedDevicecipeVersion\":0,\"ReOnlineStation\":0}",
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/haixin")
def haixin1():
    return {
        "code": "ok",
        "message": "ok",
        "result": "ok",
    }


@app.post("/Aoi/AoiBarCodeCheck")
def hailinyizao():
    return {
        "result": "OK",
        "message": "OK"
    }


@app.post("/Aoi/AoiMesOutPut")
def hailinyizao():
    return {
        "result": "OK",
        "message": "OK"
    }


# @app.post("/boliwei")
# def boliwei():
#     return {
#         "result": "OK",
#         "message": "OK"
#     }


@app.post("/boliwei")
def boliwei():
    ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ns2:excuteResponse xmlns:ns2="http://tempuri.org/">
            <return>&lt;DATA&gt;&lt;CODE&gt;0000&lt;/CODE&gt;&lt;MESSAGE&gt;标签A24AHA0003780不能在此工序过站，可过站工序编码为[SMT03]，可过站工序名称为[SMT维修]&lt;/MESSAGE&gt;&lt;TESTNAME&gt;&lt;/TESTNAME&gt;&lt;/DATA&gt;</return>
        </ns2:excuteResponse>
    </soap:Body>
</soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/maigemite.asmx")
def maigemite(body=Body("..."), SOAPAction=Header("...")):
    print("-----------")
    print(body)
    print("-----------")
    print(SOAPAction)

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string xmlns="http://tempuri.org/">OK:P</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.get("/maigemite.asmx")
def maigemite(body=Body("...")):
    print("-----------222")
    print(body)
    print("----------222-")

    ret_str = """<?xml version="1.0" encoding="utf-8"?>
    <string xmlns="http://tempuri.org/">OK:P</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/CarFPCheckRoute")
def qiutaiwei():
    return {
        "code": 200,
        "Msg": "OK"
    }


@app.post("/CarFPPassStation")
def qiutaiwei():
    return {
        "code": 200,
        "Msg": "OK"
    }


@app.post("/api/center.asmx")
def yuenanfushikang(body=Body("...")):
    print("----body---------")
    print(body)

    return {
        "code": 200,
        "Msg": "OK"
    }


@app.get("/api/center.asmx")
def yuenanfushikang(body=Body("...")):
    print("----body 1---------")
    print(body)

    return {
        "code": 200,
        "Msg": "OK"
    }


@app.post("/standard")
def standard(body=Body("...")):
    print("----body 1---------")
    print(body)

    return {
        "code": 200,
        "message": "ok"
    }


@app.post("/spiaoi/Luxshare_AOISPIService.asmx/getsn")
def kunshanlixun(body=Body("...")):
    str1 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <getsnResponse xmlns="MesWebService">
      <getsnResult>{
	"PanelBarCode": "AP303STF1119D00003",
	"Result": "OK",
	"Boards": [{
		"Blocknumber": "001",
		"BoardBarCode": "AP303STF1119D00003001",
		"SKIP": "Y"
	}, {
		"Blocknumber": "002",
		"BoardBarCode": "AP303STF1119D00003002",
		"SKIP": "Y"
	}],
	"MESSAGE": ""
}</getsnResult>
    </getsnResponse>
  </soap:Body>
</soap:Envelope>"""
    return Response(media_type="application/xml", content=str1)


@app.post("/spiaoi/Luxshare_AOISPIService.asmx/AOIPostDataFianl")
def kunshanlixun(body=Body("...")):
    str1 = """<?xml version="1.0" encoding="utf-8" ?>
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                   xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
        <soap:Body>
            <AOIPostDataFianlResponse xmlns="MesWebService">
                <AOIPostDataFianlResult>
                    [{"Result":"OK","MESSAGE":"AP702ARCC14AH00002 : 0#OK,Pass Qty : 175"}]
                </AOIPostDataFianlResult>
            </AOIPostDataFianlResponse>
        </soap:Body>
    </soap:Envelope>"""
    str1 = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <AOIPostDataFianlResponse xmlns="MesWebService">
      <AOIPostDataFianlResult> [{"Result":"OK","MESSAGE":"AP702ARCC14AH00002 : 0#OK,Pass Qty : 175"}]</AOIPostDataFianlResult>
    </AOIPostDataFianlResponse>
  </soap:Body>
</soap:Envelope>"""
    return Response(media_type="application/xml", content=str1)


@app.post("/mgmt/check")
def mgmt_check():
    return {
        "Code": 200,
        "Message": "",
    }


@app.post("/mgmt/data")
def mgmt_data():
    return {
        "Code": 200,
        "Message": "",
    }


@app.post("/mgmt/getsn")
def mgmt_data():
    return {
        "Code": 200,
        "ObjectData": "fake,fake2,fake3",
        "Message": "",
    }


@app.post("/api/TechGroupStart")
def chenzhuyibiao():
    return {
        "Result": "True",
        "Msg": "",
    }


@app.post("/api/TechGroupComplete")
def chenzhuyibiao():
    return {
        "Result": "True",
        "Msg": "",
    }


@app.post("/WebAPIService/WebAPIService.asmx")
def nuozheng():
    ret_str = f"""<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
        <ExecuteEAPRequestResponse xmlns="http://www.deltaww.com/DIAWorks">
          <ExecuteEAPRequestResult>data_ssssssssssssssss</ExecuteEAPRequestResult>
        </ExecuteEAPRequestResponse>
      </soap:Body>
    </soap:Envelope>"""

    return Response(media_type="application/xml", content=ret_str)


@app.post("/test/MesWebServer.asmx")
def shenzhenxinwangda():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
  <soap12:Body>
    <BindCarrierAndPcbV2Response xmlns="WWW.SUNWODA.COM">
      <BindCarrierAndPcbV2Result>FALSENG:获取制令单/设备的工艺流程信息出错:11</BindCarrierAndPcbV2Result>
    </BindCarrierAndPcbV2Response>
  </soap12:Body>
</soap12:Envelope>"""

    return Response(media_type="application/xml", content=ret_str)


@app.post("/api/bobcat/dopost/plugins/panel/ToolGetPanelNo")
def kunshanlixun():
    return {
        "code": 0,
        "msg": "OK",
        "data": {
            "tool_sn": "27S049",
            "panel_no": "AP408UT12A31J000X8"
        }
    }


@app.post("/upload2")
def kunshanlixun():
    return Response(content="OK:", media_type="application/text")


@app.post("/foshanhaier")
def foshanhaier():
    return {
        "success": True,
        "code": "200",
        "message": "成功"
    }


@app.post("/suoyuan")
def suoyuan():
    return {
        "resultCode": 1,
        "resultText": "ok"
    }


@app.post("/baijunbai")
def baijunbai():
    return Response(media_type="application/text", content="OK;")


@app.post("/blaketestws/BlakeTestService.asmx/uploadTestData")
def huizhoubiyaid():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
        <string xmlns="CyntecMES">{"Result": "PASS","Code": 0,"Message": "OKOK"}</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.post("/blaketestws/BlakeTestService.asmx/getNoneNVSnInfo")
def huizhoubiyaid():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
        <string xmlns="CyntecMES">{"Result": "PASS","Code": 0,"Message": "OKOK,","DATA": {"BYDPN": "N001","MO": "9B-06","FinalMO": null,"DIAGPROGRAM": null,"STATIONID": 45860001,"FinalPN": null,"DIAG": null,"NVPBR": null,"CUST_MAT_NO": "NNNN","ISROM": null,"PROGRAM_TYPE": "过站","CUSTOMER_NAME": "ICT","BIOS": "Q","ROUTENAME": "NV 显卡","LINEID": "12345","CSV_NAME": null,"BIOS_PROGRAM": null,"DIAGVER": null,"STATIONNAME": "ICT","FinalCustPN": null,"SN": "ZX24","TYPE": 1}}</string>"""

    return Response(content=ret_str, media_type="application/xml")


@app.get("/MES2/Service.action")
def huizhoubiyaid():
    return {
        "RESULT": "PASS"
    }


@app.post("/foshankaixiang")
def foshankaixiang():
    return {
        "errno": "0",
        "errmsg": "ok",
        "data": {
            "block_sns": [
                "sn1",
                "sn2"
            ]
        }
    }


@app.post("/shanghaiyipai")
def shanghaiyipai():
    return {
        "Status": True,
        "Message": "OK"
    }


@app.post("/api/Car/carSMTAOINGlogSave")
def qiutaiwei():
    return {
        "code": 200,
        "msg": "OK",
        "data": None
    }


@app.post("/SMT/CheckPanel")
def jiangxilixun_ny():
    return {
        "status": 0,
        "msg": "OK",
        "data": None
    }


@app.post("/SMT/AoiSnGo")
def jiangxilixun_ny():
    return {
        "status": 0,
        "msg": "OK",
        "data": None
    }


@app.post("/api/v1/move_into_station")
def kaerku():
    return {
        "code": 200,
        "message": "",
    }


@app.post("/api/v1/move_out_station")
def kaerku():
    return {
        "code": 200,
        "message": "",
    }


@app.get("/WebService/AOITestService.asmx/LC_AOISaveTestData1")
def aiketong():
    return {
        "result": 1,
        "OrderNumber": "A123",
    }


@app.post("/jianke/check")
def jianke():
    return {
        "code": 200,
        "message": "check ok",
    }


@app.post("/jianke/data")
def jianke():
    return {
        "code": 200,
        "message": "upload ok",
    }


@app.post("/wbm/login")
def wanbaomei_login():
    return {
        "Success": True,
        "Message": None,
        "Context": {
            "Ticket": "LSq34golQOPshoEbxILuzsrH9RJd/xrOYpmusZNRSL+ulek/sR7a8Gob4aQ3LjF1h2wt9n+LZLo=",
            "InvOrgId": None
        }
    }


@app.post("/wbm/data")
def wanbaomei_data():
    return {
        "Success": True,
        "Message": None,
        "Context": {
            "Ticket": "LSq34golQOPshoEbxILuzsrH9RJd/xrOYpmusZNRSL+ulek/sR7a8Gob4aQ3LjF1h2wt9n+LZLo=",
            "InvOrgId": None
        }
    }


@app.get("/yuanchuang/get_sn")
def yuanchuang():
    return {
        "result": {
            "pcbList": [
                {
                    "index": 1,
                    "sn": "26822F9O1120071AB0100"
                },
                {
                    "index": 4,
                    "sn": "2682413O7180491A01201"
                },
                {
                    "index": 2,
                    "sn": "26823B7ONEO5070085C004"
                },
                {
                    "index": 3,
                    "sn": "26822J8OA171019AA1201"
                }
            ],
            "programName": "",
            "isFirstItem": False,
            "result": True,
            "msg": ""
        },
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/mes/api/auto/pcb/aoi/uploadTestResult")
def fulaisi():
    return {
        "code": "200",
        "msg": "ok"
    }


@app.get("/mes/api/auto/pcb/aoi/getStationOpts")
def fulaisi():
    return {
        "msg": "success",
        "code": "200",
        "data": [
            {
                "stationKey": 2,
                "stationName": "3F1"
            },
            {
                "stationKey": 3,
                "stationName": "3F2"
            }
        ]
    }


@app.get("/mes/api/auto/pcb/aoi/getTestTypeOpts")
def fulaisi():
    return {
        "msg": "success",
        "code": "200",
        "data": [
            {
                "testType": "MP",
                "description": "量产"
            },
            {
                "testType": "EVT",
                "description": "EVT(Q1)"
            },
            {
                "testType": "DVT",
                "description": "DVT(Q2)"
            },
            {
                "testType": "PVT",
                "description": "PVT(Q3)"
            }
        ]
    }


@app.get("/mes/api/auto/pcb/aoi/getInspectionLots")
def fulaisi():
    return {
        "msg": "success",
        "code": "200",
        "data": [
            {
                "flowNo": "ZMO00003584",
                "lotKey": 9733,
                "lotNo": "V20250120147",
                "processSectionName": "Assembly",
                "productKey": 7570,
                "productName": "PASSIVE P30 Peltor Shipment Packing1",
                "productNo": "FA060A00",
                "shiftName": "Day Shift"
            },
            {
                "flowNo": "ZMO00003585",
                "lotKey": 9734,
                "lotNo": "V20250120148",
                "processSectionName": "Assembly",
                "productKey": 7570,
                "productName": "PASSIVE P30 Peltor Shipment Packing2",
                "productNo": "FA060A00",
                "shiftName": "Day Shift"
            }
        ]
    }


@app.get("/mes/api/auto/pcb/aoi/getInspectionLotItems")
def fulaisi():
    return {
        "msg": "success",
        "code": "200",
        "data": [
            {
                "detailKey": 2,
                "inspectClassName": "成品检验",
                "operationKey": 36,
                "operationName": "Assy - Cosmetic 组装-外观"
            },
            {
                "detailKey": 1,
                "inspectClassName": "成品检验",
                "operationKey": 35,
                "operationName": "Assy - FG Function Test 组装-成品功能测试"
            },
            {
                "detailKey": 2,
                "inspectClassName": "成品检验",
                "operationKey": 36,
                "operationName": "Assy - Cosmetic 组装-外观"
            }
        ]
    }


@app.get("/mes/api/auto/pcb/aoi/getVerifyItems")
def fulaisi():
    return {
        "msg": "success",
        "code": "200",
        "data": [
            {
                "operationKey": 35,
                "operationName": "Assy - FG Function Test 组装-成品功能测试",
                "productKey": 7570,
                "verifyAttrib": "MRTP-Passives_P30",
                "verifyKey": 1,
                "verifyName": "NRR抽检",
                "verifyNo": "01",
                "verifyRequest": "NRR抽检"
            }
        ]
    }


@app.post("/mes/restapi/common/workorder/getSnByPartSn")
def huichuan2():
    return {
        "code": 1,
        "success": True,
        "data": {
            "partSn": "111",
            "sn": "222"
        },
        "message": None,
        "path": None,
        "exception": None,
    }


@app.post("/mes/api/auto/pcb/aoi/createInspectionSheet")
def fulaisi():
    return {
        "msg": "success",
        "code": "200",
        "data": {
            "sheetKey": 1,
            "token": "TOKEN"
        }
    }


@app.post("/api/v2/CheckIn")
def hefeibiyadi():
    return {
        "LineIntCode": 0,
        "StationIntCode": 0,
        "HandleResult": 1,
        "FailedDevice": 0,
        "ModelMark": 0,
        "ProductModel": "EV9600021B",
        "ProductId": "38538829594f4007bfbddcab8f2bd103",
        "BarCode": "",
        "RecipeVersion": 0,
        "ReOnlineStation": 0,
        "ReOnlineInfo": ""
    }


@app.post("/api/v2/ParameterUpload")
def hefeibiyadi():
    return {
        "HandleResult": 1,
    }


@app.post("/api/v2/CheckOut")
def hefeibiyadi():
    return {
        "HandleResult": 1,
    }


@app.post("/api/v2/DeviceStatus")
def hefeibiyadi():
    return {
        "HandleResult": 1,
    }


@app.post("/api/v2/AlarmReport")
def hefeibiyadi():
    return {
        "HandleResult": 1,
    }


@app.post("/pec/framework-webproxy/proxy")
def hefeilianbao(request: Request, body=Body(...)):
    headers = request.headers
    ret_res = {
        "header": {
            "info": {
                "messageClass": "1100",
                "transferTime": "20130101000000"
            },
            "location": {
                "machineID": "2LSFIN01",
                "groupName": "S_FINAOI",
                "laneNo": "1"
            }
        },
        "body": {
            "result": {
                "errorCode": "0",
                "errorText": "",
                "actionCode": "0"
            },
            "pcb": {
                "id": "P1234567890"
            }
        }
    }

    if body.get("header", {}).get("info", {}).get("messageClass") in [
        "1300", "4100"
    ]:
        ret_res = {
            "RetCode": "OK",
            "ErrCode": "0",
            "ErrMsg": "OK",
            "Obj": None
        }

    if headers.get("X-Fn") == "Machine-AOI1400Process":
        ret_res = {
            "header":
                {
                    "info": {
                        "messageClass": "1400", "transferTime": "20250306163432"
                    },
                    "location": {
                        "machineID": "1QFINAOI01",
                        "groupName": "FINAOI",
                        "laneNo": "1"
                    }
                },
            "body": {
                "ignorekey": [
                    {
                        "result": {
                            "errorCode": "1",
                            "errorText": "F/T",
                            "boardID": "BOARD1"
                        },
                        "pcb": {
                            "id": "2101359101351"
                        }
                    }
                ]
            }
        }

    elif headers.get("X-Fn") == "Machine-AOI1300Process":
        ret_res = {
            "retCode": "OK",
            "errCode": None,
            "errMsg": None
        }

    return ret_res


@app.post("/ims-cust/api/spi/BarCodeCheck")
def huizhoujunya():
    return {
        "Result": "OK",
        "EventCode": 0,
        "Message": "发生异常！"
    }


@app.post("/ims-cust/api/spi/UpdateDeviceStatus")
def huizhoujunya():
    return {
        "Result": "OK",
        "EventCode": 0,
        "Message": "发生异常！"
    }


@app.post("/MesOutPut")
def huizhoujunya():
    return {
        "result": "OK",
        "eventCode": 0,
        "message": "发生异常！"
    }


@app.post("/ims-cust/Service/SuportMIAoiService")
def cdyb():
    ret_str = """<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <ns2:excuteResponse xmlns:ns2="http://tempuri.org/">
            <return>
                {
                "code":"0000",
                "message":"xxxx",
                "pcbChecks":[
                {
                "lbId":"LB1001001",
                "num":"1"
                },
                {"lbId":"LB1001002",
                "num":"2"
                },
                {"lbId":"skip",
                "num":"3"
                }
                ]
                }
            </return>
        </ns2:excuteResponse>
    </soap:Body>
</soap:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/api/checkBarcode")
def huichuan_check_barcode():
    return {
        "code": 1,
        "message": "ok"
    }


@app.get("/api/services/mf/MacSn/GetMacs")
def zhishicheng5():
    return {
        "result": [
            {
                "id": "03801a2de1904f968ac73c783782f506",
                "moBindId": "c9d708e37ff84a2892fb3bc5fde16636",
                "moLotNo": "RCBJ-SKZBU23080018",
                "wareId": "3fd2392491494dceb3436d774b28e053",
                "tagName": "MAC",
                "keyTypeId": "fde3193657b248558377f429d25d43b9",
                "keyTypeName": "LANMAC",
                "unitQty": 2,
                "isBindSn": False,
                "limitOrderNo": "",
                "isDownByCustomServer": False
            },
            {
                "id": "710ae8bddaea496c8bef2620f29c9861",
                "moBindId": "c9d708e37ff84a2892fb3bc5fde16636",
                "moLotNo": "RCBJ-SKZBU23080018",
                "wareId": "5bb7d7bcdf6a42a592725b9490dbfa2a",
                "tagName": "MAC",
                "keyTypeId": "386fd5d7ee084772abe45a7aee7e45eb",
                "keyTypeName": "BTMAC",
                "unitQty": 1,
                "isBindSn": False,
                "limitOrderNo": "",
                "isDownByCustomServer": False
            }
        ],
        "targetUrl": None,
        "success": True,
        "error": None,
        "unAuthorizedRequest": False,
        "__abp": True
    }


@app.post("/UnitConfirmDataSetOut")
def beijingxiaomi1():
    return {
        "header": {
            "code": "200",
            "desc": "OK"
        }
    }


@app.post("/x5/file/upload/mqtt")
def beijingxiaomi():
    return {
        "header": {
            "code": "200",
            "desc": "OK"
        }
    }


@app.post("/Service1.svc")
def barcode_relation():
    ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <BarcodeRelationResponse xmlns="http://tempuri.org/">
            <BarcodeRelationResult xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:string>true</a:string>
                <a:string>OK</a:string>
            </BarcodeRelationResult>
        </BarcodeRelationResponse>
    </s:Body>
</s:Envelope>"""
    return Response(media_type="application/xml", content=ret_str)


@app.post("/api/mes/QcxCommonValidateAOI")
def gaokerui1():
    return {
        "Result": "1",
        "Message": "OK"
    }


@app.post("/api/mes/QcxPassingStationAOI")
def gaokerui2():
    return {
        "Result": "1",
        "Message": "OK"
    }


@app.post("/SaveAOITestResult")
def xinnengan():
    return {
        "Result": 1,
        "Msg": ""
    }


@app.post("/haikang/AutoProject")
def haikangauto(item: dict):
    print(item)

    return {
        "message": "操作成功",
        "code": "000000",
        "data": {
            "aufnr": "10037405",
            "matnr": "300200021",
            "maktx": "YGW2Sides.001",
            "bomFlag": "Y"
        }
    }


@app.post("/zhongkelang/login")
def zhongkelang():
    return {
        "success": True,
        "code": 200,
        "msg": "",
        "data": [
        ]
    }


@app.post("/zhongkelang/sum")
def zhongkelang():
    return {
        "success": False,
        "code": 200,
        "msg": "test123",
        "data": [
        ]
    }


@app.post("/zhongkelang/get_sn")
def zhongkelang():
    return {
        "success": False,
        "code": 200,
        "msg": "not found sn",
        "data": "sn1,sn2,sn3"
    }


@app.post("/soap/yirui")
def aicangyirui(data=Body(...)):
    print("-----------太仓奕瑞")
    print(data)
    ret_str = """<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body>
        <CmosSpiResponse xmlns="http://tempuri.org/">
            <CmosSpiResult xmlns:a="http://schemas.datacontract.org/2004/07/BiLin.R2E.Framework.Result" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:code>100</a:code>
                <a:content i:nil="true"/>
                <a:logID i:nil="true"/>
                <a:msg>OK</a:msg>
                <a:token i:nil="true"/>
            </CmosSpiResult>
        </CmosSpiResponse>
    </s:Body>
</s:Envelope>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/api/wip/Test")
def shenzhenwengwei(body=Body(...)):
    print(body)

    return {
        "result": "1",
        "error": "ok"
    }


@app.post("/Machine/IDCMachineParam")
def huizhoubiyadi_3l(body=Body(...)):
    print(body)

    return {"RESULT": "PASS", "MESSAGE": ""}


@app.get("/blaketestws/BlakeTestService.asmx/getOneBySn")
def huizhoubiyadi3():
    ret_str = """<?xml version="1.0" encoding="utf-8"?>
<string xmlns="http://tempuri.org/">{
	"Result": "PASS",
	"Code": 0,
	"Message": "OKOK,",
	"DATA": {
		"id": 685614045,
		"status": "IN_WORK",
		"createTime": "2025-04-23 12:27:38",
		"qaProcess": null,
		"shoporder": "4509518782",
		"qaStatus": "NORMAL",
		"newShoporder": null,
		"isForce": 1,
		"routeVersion": "A",
		"lineName": "A503_H6",
		"resourceCode": "70:85:C2:A6:63:83",
		"updateTime": "2025-04-23 14:14:33",
		"shard": null,
		"modelType": "显卡-PG172",
		"processBomName": "14548160-00组装",
		"qaOpName": null,
		"matVersion": "A",
		"resourceName": "NBL",
		"sn": "1421625040176",
		"qaOpTime": null,
		"updateName": "6517645",
		"batchNo": null,
		"shoporderSap": "4509518782",
		"sectionName": "组装",
		"processBomVersion": "A5",
		"printSeq": null,
		"processName": "M_CHECK_MODULE",
		"routeName": "14548160-00-组装-A0",
		"remark": null,
		"qty": 1,
		"createName": "FLAMES",
		"customerCode": "19543",
		"previousProcess": "ICT",
		"matNo": "14548160-00"
	}
}</string>"""
    return Response(content=ret_str, media_type="application/xml")


@app.post("/Machine/Productdown")
def huizhoubiyadi_3_2():
    return {
        "RESULT": "PASS",
        "DATA": {
            "ftpAddress": "ftp://192 168.11.96/1ogfile",
            "productName": "333.001.rar"
        },
        "MESSAGE": ""
    }


if __name__ == '__main__':
    # uvicorn.run(app="main:app", host="0.0.0.0", port=8081)
    uvicorn.run(app, host="0.0.0.0", port=8081)
