# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : qiutaiwei_module.py
# Time       ：2024/11/12 下午4:46
# Author     ：sch
# version    ：python 3.8
# Description：
"""
import json
from typing import List

from common.xutil import XmlUtil


def get_specs_by_report_xml(file_path: str) -> dict:
    """
    获取器件的算法值列表
    // 高度倾斜
    {
        "heightDiffResult": "1057.62",
        "heightDiffResultThre": "50",
        "specName": "InspHeightTilted"
    },
    // 尺寸检测
    {
        "OFFSET_X_RATIO": "18.4035",
        "OFFSET_Y_RATIO": "76.3043",
        "REAL_OFFSET_X": "1258.54",
        "REAL_OFFSET_Y": "2661.12",
        "angle": "0",
        "angleL": "6",
        "angleU": "6",
        "specName": "InspDimensionDetect",
        "x_offset_L": "60",
        "x_offset_U": "60",
        "x_offset_ratio_L": "10",
        "x_offset_ratio_U": "10",
        "y_offset_L": "60",
        "y_offset_U": "60",
        "y_offset_ratio_L": "10",
        "y_offset_ratio_U": "10"
    },
    // 智能算法2
    {
        "angle": "0.246673",
        "offsetX": "-85",
        "offsetY": "-166",
        "specName": "InspSkewAiObb",
        "xSkewResult": "81",
        "xSkewThreshold": "66",
        "ySkewResult": "63",
        "ySkewThreshold": "66"
    }

    :param file_path: report.xml 路径
    :return:
    """
    root = XmlUtil.get_xml_root_by_file(file_path)

    boards = root.find("boards").findall("board")

    # 每一块小板的位置信息  {board_id: <位置信息>}

    comp_spec_data = {}

    for board in boards:

        comps = board.find("components").findall("component")
        for comp in comps:
            uuid = comp.find("uuid").text
            uuid = uuid.replace("{", "").replace("}", "")

            all_specs = comp.find("allSpecs")

            spec_list = []

            if all_specs:
                for spec in all_specs:
                    spec_list.append(json.loads(spec.text))

            for child in comp.find("children"):
                child_all_spec = child.find("allSpecs")

                if child_all_spec:
                    for spec in child_all_spec:
                        spec_list.append(json.loads(spec.text))

            comp_spec_data[uuid] = {
                "spec_list": spec_list,
            }

    return comp_spec_data


def concat_item_by_threshold(all_data_list: list, threshold=20, delimiter='|'):
    """
    根据给定的长度阈值合并列表中的字符串项，并使用指定的分隔符连接。

    :param all_data_list: 包含多个字符串的列表
    :param threshold: 合并后的字符串的最大长度，默认为20
    :param delimiter: 字符串之间的分隔符，默认为'\n'
    :return: 新的字符串列表，其中每个字符串的长度（包括分隔符）都不超过阈值

    示例：
    >>> data_list = ["hello", "world", "this", "is", "a", "test"]
    >>> threshold = 10
    >>> delimiter = '-'
    >>> result = concat_item_by_threshold(data_list, threshold, delimiter)
    >>> print(result)
    ['hello', 'world-this', 'is-a-test']
    """
    new_data_list = []
    current_concat = ''
    current_length = 0  # 用于跟踪当前拼接字符串的实际长度，包括分隔符

    for item in all_data_list:
        # 计算添加新字符串和分隔符后的总长度
        # 注意：只有在current_concat非空时才加上分隔符的长度
        potential_length = current_length + len(item) + (len(delimiter) if current_concat else 0)

        if potential_length <= threshold:
            # 如果没有超过阈值，加上分隔符（如果需要的话）和新字符串
            if current_concat:
                current_concat += delimiter
                current_length += len(delimiter)
            current_concat += item
            current_length += len(item)
        else:
            # 如果超过了阈值，先保存当前的拼接结果
            new_data_list.append(current_concat)
            # 开始新的拼接
            current_concat = item
            current_length = len(item)

    # 最后一次拼接的结果也需要保存
    if current_concat:
        new_data_list.append(current_concat)

    return new_data_list


def list_chunks(_list: list, n) -> List[list]:
    """
    将一个列表切分成长度为N的若干份
    """
    for i in range(0, len(_list), n):
        yield _list[i:i + n]


if __name__ == '__main__':
    ret = list_chunks([1, 2, 3, 4, 5, 6], 8)
    print(list(ret))
