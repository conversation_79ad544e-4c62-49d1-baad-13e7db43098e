# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/6/12 上午10:03
# Author     ：sch
# version    ：python 3.8
# Description：古瑞瓦特 ----现在使用EAP功能，已改成c++版本。此版本deprecated
"""
import json
from typing import Any

import requests

from common import xrequest, xutil, xcons
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import BaseEngine

global_data = {}


def common_request(url: str, param: dict) -> dict:
    """
    统一的请求入口
    :param url:
    :param param:
    :return:
    """
    ret_str = xrequest.RequestUtil.get(url, param, to_json=False)
    ret_root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
    return ret_root.attrib


class Engine(BaseEngine):
    version = {
        "title": "guruiwate release v1.0.0.3",
        "device": "203P",
        "feature": ["条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-06-13 09:24  条码校验，上传数据，上传图片
date: 2023-06-16 17:56  调整接口url
""", }

    form = {
        "api_host": {
            "ui_name": "接口地址",
            "value": "http://**********:8088"
        },
        "user_no": {
            "ui_name": "人员编码",
            "value": ""
        },
        "site_no": {
            "ui_name": "站点",
            "value": ""
        },
        "resource": {
            "ui_name": "资源",
            "value": ""
        },
        "operation": {
            "ui_name": "工序",
            "value": ""
        },
        "machine_version": {
            "ui_name": "机器版本",
            "value": ""
        },
    }

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        site_no = data_vo.get_value_by_cons_key("site_no")
        resource = data_vo.get_value_by_cons_key("resource")
        operation = data_vo.get_value_by_cons_key("operation")
        machine_version = data_vo.get_value_by_cons_key("machine_version")
        user_no = data_vo.get_value_by_cons_key("user_no")

        pcb_entity = data_vo.pcb_entity

        upload_image_url = f"{api_host}/manufacturing-app/ResourceAPIController.asmx/uploadPicAOI"

        self.log.info(pcb_entity)

        error_msg = ""
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            board_part = global_data.get(barcode, "")  # 料号
            if not board_part:
                self.log.warning(f"条码：{barcode} 未获取到料号！")

            parameters = [
                {
                    "GroupName": "1",
                    "DC_INFO": [
                        {
                            "K": "机器版本",
                            "V": machine_version,
                        },
                        {
                            "K": "测试开始时间",
                            "V": pcb_entity.get_start_time().strftime(xcons.FMT_TIME_DEFAULT),
                        },
                        {
                            "K": "测试结束时间",
                            "V": pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT),
                        },
                        {
                            "K": "测试总时间",
                            "V": f"{pcb_entity.get_cycle_time()} S",
                        },
                        {
                            "K": "料号",
                            "V": board_part,
                        },
                    ]
                }
            ]

            count_ix = 1
            comp_ng_code_list = []
            for comp_entity in board_entity.yield_comp_entity():
                count_ix += 1

                comp_image = ""
                comp_tag = comp_entity.designator
                comp_image_src_file = comp_entity.image_path

                if comp_entity.is_repair_ng() and comp_image_src_file:
                    comp_image = f"{barcode}_{comp_tag}.png"
                    comp_ng_code_list.append(comp_entity.repair_ng_code)

                    self.log.info(f"开始上传[{comp_image}]不良图片....")

                    base_param1 = {
                        "site": site_no,
                        "resource": resource,
                        "operation": operation,
                        "sfc": barcode,
                        "userNo": user_no,
                        "picFileName": comp_image
                    }

                    self.log.info(f"接口url：{upload_image_url}  基本参数：{json.dumps(base_param1, ensure_ascii=False)}")
                    base_param1["picBase64Data"] = xutil.ImageUtil.file_to_base64_content(comp_image_src_file)
                    image_res = requests.post(upload_image_url, base_param1)
                    self.log.info(f"接口响应：{image_res}  text:{image_res.text}")

                    ret_root = xutil.XmlUtil.get_xml_root_by_str(image_res.text)
                    ret_json = ret_root.attrib
                    if ret_json.get("Result") != "S":
                        self.log.warning(f"上传图片失败，error：{ret_json.get('ErrorMsg')}")

                comp_item = [
                    {
                        "K": "comp_designator",
                        "V": comp_tag,
                    },
                    {
                        "K": "comp_part",
                        "V": comp_entity.part,
                    },
                    {
                        "K": "comp_package",
                        "V": comp_entity.package,
                    },
                    {
                        "K": "comp_type",
                        "V": comp_entity.type,
                    },
                    {
                        "K": "comp_robot_code",
                        "V": comp_entity.robot_ng_code,
                    },
                    {
                        "K": "comp_robot_result",
                        "V": comp_entity.robot_ng_str,
                    },
                    {
                        "K": "comp_user_code",
                        "V": comp_entity.repair_ng_code,
                    },
                    {
                        "K": "comp_user_result",
                        "V": comp_entity.repair_ng_str,
                    },
                    {
                        "K": "ImageName",
                        "V": comp_image,
                    },
                ]

                parameters.append({
                    "GroupName": str(count_ix),
                    "DC_INFO": comp_item
                })

            data_param = {
                "site": site_no,
                "resource": resource,
                "operation": operation,
                "sfc": barcode,
                "parameters": json.dumps(parameters, ensure_ascii=False)
            }

            data_url = f"{api_host}/Service.asmx/DataCollection"
            self.log.info(f"开始上传测试数据...")
            ret_str = xrequest.RequestUtil.post_form(data_url, data_param, to_json=False)
            ret_root = xutil.XmlUtil.get_xml_root_by_str(ret_str)
            ret_json = ret_root.attrib

            if ret_json.get("Result") != "S":
                error_msg += f"{ret_json.get('ErrorMsg', '')} \n"

            self.log.info(f"开始上传出站数据...")
            check_out_param = {
                "site": site_no,
                "resource": resource,
                "operation": operation,
                "sfc": barcode,
                "status": board_entity.get_repair_result("1", "0"),
                "ncDesc": ",".join(comp_ng_code_list)
            }
            check_out_url = f"{api_host}/manufacturing-app/ResourceAPIController/checkOut"
            ret_json1 = common_request(check_out_url, check_out_param)
            if ret_json1.get("Result") != "S":
                error_msg += f"{ret_json1.get('ErrorMsg', '')} \n"

        if error_msg:
            return self.x_response("false", f"mes接口异常，上传数据失败，error：{error_msg}")

        return self.x_response()

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        site_no = other_vo.get_value_by_cons_key("site_no")
        resource = other_vo.get_value_by_cons_key("resource")
        operation = other_vo.get_value_by_cons_key("operation")

        check_url = f"{api_host}/manufacturing-app/ResourceAPIController/checkIn"

        error_msg = ""
        for sn in other_vo.list_sn():
            check_param = {
                "site": site_no,
                "resource": resource,
                "operation": operation,
                "sfc": sn
            }
            self.log.info(f"开始上传进站数据...")
            ret = common_request(check_url, check_param)
            if ret.get("Result") != "S":
                error_msg += f"{ret.get('ErrorMsg', '')} \n"

            if sn:
                ret_item = ret.get("Item", "")
                global_data[sn] = ret_item
                self.log.info(f"sn: {sn} 物料代码[{ret_item}]已缓存")

        if error_msg:
            return self.x_response("false", f"mes接口异常，进站失败，error：{error_msg}")

        return self.x_response()
