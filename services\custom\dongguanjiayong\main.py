# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2023/5/4 上午11:16
# Author     ：sch
# version    ：python 3.8
# Description：东莞佳永
"""
from typing import Any

from common import xrequest, xutil
from vo.mes_vo import OtherVo, DataVo
from engine.FtpEngine import FTPClient
from engine.MesEngine import BaseEngine


AIS_40X_ERROR_MAP = {
    "0": {"standard": "OK", "custom_code": "0", "custom_str": "OK"},
    "1": {"standard": "漏件", "custom_code": "51", "custom_str": "MissingPart"},
    "2": {"standard": "错件", "custom_code": "52", "custom_str": "WrongPart"},
    "3": {"standard": "反件", "custom_code": "53", "custom_str": "ReversePart"},
    "4": {"standard": "立碑", "custom_code": "54", "custom_str": "Tomstone"},
    "5": {"standard": "偏移", "custom_code": "55", "custom_str": "ShiftPart"},
    "6": {"standard": "翻转", "custom_code": "56", "custom_str": "UpsideDown"},
    "7": {"standard": "浮高", "custom_code": "57", "custom_str": "LiftedPackage"},
    "8": {"standard": "损件", "custom_code": "58", "custom_str": "Broken"},
    "9": {"standard": "露铜", "custom_code": "59", "custom_str": "ExposeCopper"},
    "10": {"standard": "少锡", "custom_code": "60", "custom_str": "InsufficientSolder"},
    "11": {"standard": "多锡", "custom_code": "61", "custom_str": "ExcessSolder"},
    "12": {"standard": "未出脚", "custom_code": "62", "custom_str": "NoPin"},
    "13": {"standard": "孔洞", "custom_code": "63", "custom_str": "PinHole"},
    "14": {"standard": "连锡", "custom_code": "64", "custom_str": "Bridge"},
    "15": {"standard": "锡珠", "custom_code": "65", "custom_str": "SolderBall"},
    "16": {"standard": "翘脚", "custom_code": "66", "custom_str": "LiftedLead"},
    "17": {"standard": "弯脚", "custom_code": "67", "custom_str": "ShiftedLead"},
    "18": {"standard": "异物", "custom_code": "68", "custom_str": "ForeignMaterial"},
    "19": {"standard": "条码识别", "custom_code": "69", "custom_str": "BarcodeRecognition"},
    "20": {"standard": "Marker搜索", "custom_code": "70", "custom_str": "MarkerSearch"},
    "21": {"standard": "多件", "custom_code": "71", "custom_str": "ForeignPart"},
    "22": {"standard": "溢胶", "custom_code": "72", "custom_str": "Overflow"},
    "23": {"standard": "虚焊", "custom_code": "73", "custom_str": "IncompleteWeld"},
    "24": {"standard": "脏污", "custom_code": "74", "custom_str": "Dirty"},
    "25": {"standard": "坏板", "custom_code": "75", "custom_str": "BadPanel"},
    "26": {"standard": "定位", "custom_code": "76", "custom_str": "Locate"},
    "27": {"standard": "数目错误", "custom_code": "77", "custom_str": "CountError"},
    "28": {"standard": "少涂/多涂", "custom_code": "78", "custom_str": "LessMoreCoating"},
    "29": {"standard": "少涂", "custom_code": "79", "custom_str": "LessCoating"},
    "30": {"standard": "多涂", "custom_code": "80", "custom_str": "MoreCoating"},
    "31": {"standard": "气泡", "custom_code": "81", "custom_str": "Bubble"},
    "32": {"standard": "划痕", "custom_code": "82", "custom_str": "Scratch"},
    "33": {"standard": "距离", "custom_code": "83", "custom_str": "Distance"},
    "34": {"standard": "锡膏检测", "custom_code": "84", "custom_str": "SPIDetect"},
    "35": {"standard": "共线性", "custom_code": "85", "custom_str": "Collinearity"},
    "36": {"standard": "CPU插针检测", "custom_code": "86", "custom_str": "CPUPinDetect"}
}


class JiaYongEngine(BaseEngine):
    version = {
        "title": "dongguanjiayong release v1.0.0.3",
        "device": "50x",
        "feature": ["上传条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2023-05-04 18:21  init
date: 2023-05-05 11:30  上传条码，条码校验，上传数据
date: 2023-05-20 12:03  接口调用方式改为get
date: 2023-06-01 09:50  uploadSN上传次数改为配置项
""",
    }

    form = {
        "api_host": {
            "ui_name": "接口地址",
            "value": "",
        },
        "order_id": {
            "ui_name": "工单号",
            "value": "",
        },
        "machine_code": {
            "ui_name": "机器编号",
            "value": "",
        },
        "company_name": {
            "ui_name": "公司名称",
            "value": "",
        },
        "username": {
            "ui_name": "员工工号",
            "value": "",
        },
        "station_no": {
            "ui_name": "站位编号",
            "value": "",
        },
    }

    combo = {
        "retest": {
            "ui_name": "是否重复测试",
            "item": [
                "false",
                "true"
            ],
            "value": "false"
        },
        "upload_sn_number": {
            "ui_name": "uploadSN次数",
            "item": [
                "多次",
                "一次"
            ],
            "value": "多次"
        }
    }

    route_remark_template = "{ng_code}{ng_str}{comp_desi}"

    def check_sn(self, other_vo: OtherVo, other_param: Any) -> dict:
        api_host = other_vo.get_value_by_cons_key("api_host")
        order_id = other_vo.get_value_by_cons_key("order_id")
        station_no = other_vo.get_value_by_cons_key("station_no")
        retest = other_vo.get_value_by_cons_key("retest")
        machine_code = other_vo.get_value_by_cons_key("machine_code")
        company_name = other_vo.get_value_by_cons_key("company_name")
        username = other_vo.get_value_by_cons_key("username")
        upload_sn_number = other_vo.get_value_by_cons_key("upload_sn_number")

        sn_list = other_vo.list_sn()

        for sn in sn_list:
            # 1. 上传条码
            upload_sn_url = f"{api_host}/mrs/uploadSn"
            upload_sn_param = {
                "prodNo": order_id,
                "pcbSeq": "",
                "mpcbSeq": sn,
                "machineNo": machine_code,
                "companyNo": company_name,
                "createdUser": username
            }

            ret = xrequest.RequestUtil.get(upload_sn_url, upload_sn_param)
            if ret.get("msgId") != 0:
                return self.x_response("false", f"mes接口异常，上传条码失败，error：{ret.get('msgStr')}")

            if upload_sn_number == "一次":
                break

        # 2. 条码校验
        check_url = f"{api_host}/mrs/checkRoute"

        for sn in sn_list:
            check_param = {
                "pcbSeq": sn,
                "prodNo": order_id,
                "stationNo": station_no,
                "retest": 1 if retest == "true" else 0
            }

            ret = xrequest.RequestUtil.get(check_url, check_param)
            if ret.get("msgId") != 0:
                return self.x_response("false", f"mes接口异常，条码校验失败，error：{ret.get('msgStr')}")

        return self.x_response()

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_host = data_vo.get_value_by_cons_key("api_host")
        order_id = data_vo.get_value_by_cons_key("order_id")
        station_no = data_vo.get_value_by_cons_key("station_no")
        username = data_vo.get_value_by_cons_key("username")

        ftp_host = data_vo.get_value_by_cons_key("ftp_host")
        ftp_port = data_vo.get_value_by_cons_key("ftp_port")
        ftp_user = data_vo.get_value_by_cons_key("ftp_user")
        ftp_password = data_vo.get_value_by_cons_key("ftp_password")
        ftp_path = data_vo.get_value_by_cons_key("ftp_path")

        if ftp_host:
            try:
                ftp_port = int(ftp_port)
            except Exception as err:
                return self.x_response("false", f"端口号必须为数字，error：{err}")

            ftp_client = FTPClient(ftp_host, ftp_user, ftp_password, ftp_port)
            ftp_client.login()
            ftp_client.cd_or_mkdir(ftp_path)

        else:
            ftp_client = None

        data_url = f"{api_host}/mrs/createRoute"

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        time_now = xutil.DateUtil.get_datetime_now()

        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode

            ng_code = []
            ng_str = []
            comp_desi = []

            for comp_entity in board_entity.yield_comp_entity():
                user_ng_code = comp_entity.repair_ng_code
                comp_des = comp_entity.designator

                if user_ng_code != "0":
                    custom_ng_code = AIS_40X_ERROR_MAP.get(user_ng_code, {}).get("custom_code")
                    ng_code.append(custom_ng_code)
                    ng_str.append(comp_entity.repair_ng_str)
                    comp_desi.append(comp_des)

                    comp_image = comp_entity.image_path

                    dst_name = f"{barcode}_{time_now}_{comp_des}.png"

                    if comp_image and ftp_client is not None:
                        ftp_client.upload_file(comp_image, dst_name)

            route_remark = self.route_remark_template.replace(
                "ng_code", ",".join(ng_code)).replace(
                "ng_str", ",".join(ng_str)).replace(
                "comp_desi", ",".join(comp_desi))

            data_param = {
                "pcbSeq": barcode,
                "prodNo": order_id,
                "stationNo": station_no,
                "result": board_entity.get_robot_result("PASS", "FAIL"),
                "remark": route_remark,
                "testItem": "",
                "userNo": username,
                "weight": 0,
                "packNo": "",
                "rmk1": "",
                "rmk2": "",
                "rmk3": "",
                "rmk4": ""
            }

            ret = xrequest.RequestUtil.get(data_url, data_param)

            if ret.get("msgId") != 0:
                return self.x_response("false", f"mes接口异常，上传数据失败，error：{ret.get('msgStr')}")

        if ftp_client is not None:
            ftp_client.close()

        return self.x_response()
