<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:excute>
            <functionId>uploadResult</functionId>
            <body>
                {
                "devCode":"DEVICE0001",
                "wpCode":"SY",
                "userCode":"HB",
                "proName":"测试程序名称1",
                "pcbChecks":[
                {
                "lbId":"LB1001001",
                "pcbNum":"b2324231",
                "finalResult":"FAIL",
                "logpath":"/dev/spi/xxxxxxx.csv",
                "lbChecks":[ //传入对应条码不良信息
                {
                "badItemCode":"bad-01",
                "badItemName":"不良项目名称01",
                "badPoint":"1-23,1-01",
                "imagespath":"/dev/spi/xxxxxxx.jpg"
                },
                {
                "badItemCode":"bad-02",
                "badItemName":"不良项目名称02",
                "badPoint":"2-2,2-01",
                "imagespath":"/dev/spi/xxxxxxx.jpg"
                }
                .....
                ]
                },
                {
                "lbId":"LB1001002",
                "pcbNum":"b2324232",
                "finalResult":"PASS",
                "logpath":"/dev/spi/xxxxxxx.csv",
                "lbChecks":[
                //合格时空值
                ]
                }
                .....
                ]
                }
            </body>
        </tem:excute>
    </soapenv:Body>
</soapenv:Envelope>