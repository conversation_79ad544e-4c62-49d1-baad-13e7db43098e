# !/usr/bin/env python
# -*-coding:utf-8 -*-

"""
# File       : main.py
# Time       ：2024/9/24 下午5:31
# Author     ：sch
# version    ：python 3.8
# Description：东莞元昌二厂  ---> 福瑞康,世通恒利，MES供应商：智硕系列
"""
from typing import Any

from common import xrequest, xcons, xutil
from vo.mes_vo import DataVo, OtherVo
from engine.MesEngine import ErrorMapEngine


class Engine(ErrorMapEngine):
    version = {
        "title": "yuanchang2 release v1.0.0.1",
        "device": "AIS203,AIS303",
        "feature": ["从mes获取条码", "条码校验", "上传数据"],
        "author": "sunchangheng",
        "release": """
date: 2024-09-26 17:31  从mes获取条码，条码校验，上传数据
""", }

    other_form = {
        "api_url_get_sn": {
            "ui_name": "接口URL(获取条码)",
            "value": "http://127.0.0.1:8081/mrs/getRelationPcbSeq"
        },
        "api_url_check_sn": {
            "ui_name": "接口URL(条码校验)",
            "value": "http://127.0.0.1:8081/mrs/checkRoute"
        },
        "api_url_data": {
            "ui_name": "接口URL(上传数据)",
            "value": "http://127.0.0.1:8081/mrs/createAOIData"
        },
        "type_ui": {
            "ui_name": "type",
            "value": "1"
        }
    }

    form = {
        "company_no": {
            "ui_name": "公司名/公司编号",
            "value": ""
        },
        "machine_no": {
            "ui_name": "机器编号",
            "value": ""
        },
        "line_no": {
            "ui_name": "线别编号",
            "value": ""
        },
        "station_no": {
            "ui_name": "站点编号",
            "value": "AOI"
        },
        "prod_no": {
            "ui_name": "工单",
            "value": ""
        },
    }

    combo = {
        "board_side": {
            "ui_name": "板面",
            "value": "T",
            "item": ["T", "B", "T+B"]
        },
        "retest": {
            "ui_name": "允许重复测试",
            "value": "Yes",
            "item": ["Yes" "No"]
        },
    }

    def get_sn_by_mes(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_get_sn = other_vo.get_value_by_cons_key("api_url_get_sn")
        prod_no = other_vo.get_value_by_cons_key("prod_no")
        company_no = other_vo.get_value_by_cons_key("company_no")
        type_ui = other_vo.get_value_by_cons_key("type_ui")

        pcb_sn = other_vo.get_pcb_sn()

        param = {
            "pcbSeq": pcb_sn,
            "prodNo": prod_no,
            "companyNo": company_no,
            "type": type_ui
        }

        ret = xrequest.RequestUtil.post_json(api_url_get_sn, param)

        data = ret.get("data")

        # 兼容模式
        data_str = str(data)
        if '"msgId":1' in data_str or '没有子母码' in data_str:
            return self.x_response("false", f"mes接口异常，获取条码失败，error: {data}")

        if str(ret.get("msgId")) != "0":
            return self.x_response("false", f"mes接口异常, 获取条码失败, error: {ret.get('msgStr')}")

        ret_sn = [item.get("pcbSeq") for item in data]

        sn_str = ",".join(ret_sn)
        return self.x_response("true", sn_str)

    def check_sn(self, other_vo: OtherVo, main_window: Any) -> dict:
        api_url_check_sn = other_vo.get_value_by_cons_key("api_url_check_sn")
        prod_no = other_vo.get_value_by_cons_key("prod_no")
        station_no = other_vo.get_value_by_cons_key("station_no")
        retest = other_vo.get_value_by_cons_key("retest")

        sn_list = other_vo.list_sn()

        ret_res = self.x_response()
        for sn in sn_list:
            param = {
                "pcbSeq": sn,
                "prodNo": prod_no,
                "stationNo": station_no,
                "retest": 0 if retest != "Yes" else 1
            }

            ret = xrequest.RequestUtil.get(api_url_check_sn, params=param)
            if str(ret.get("msgId")) != "0":
                ret_res = self.x_response("false", f"接口响应异常, 条码校验失败, error: {ret.get('msgStr')}")

        return ret_res

    def send_data_to_mes(self, data_vo: DataVo, other_data: dict, other_param: Any) -> dict:
        api_url_data = data_vo.get_value_by_cons_key("api_url_data")
        line_no = data_vo.get_value_by_cons_key("line_no")
        station_no = data_vo.get_value_by_cons_key("station_no")
        prod_no = data_vo.get_value_by_cons_key("prod_no")
        board_side = data_vo.get_value_by_cons_key("board_side")
        machine_no = data_vo.get_value_by_cons_key("machine_no")
        company_no = data_vo.get_value_by_cons_key("company_no")

        pcb_entity = data_vo.pcb_entity
        self.log.info(pcb_entity)

        start_datetime = pcb_entity.get_start_time()
        start_time = start_datetime.strftime(xcons.FMT_TIME_DEFAULT)

        time_file = start_datetime.strftime(xcons.FMT_TIME_FILE)
        pcb_sn = pcb_entity.pcb_barcode

        sn_list = []
        if pcb_sn and pcb_sn not in sn_list:
            sn_list.append(pcb_sn)

        for board_entity in pcb_entity.yield_board_entity():
            # self.log.info(board_entity)

            barcode = board_entity.barcode
            if barcode and barcode not in sn_list:
                sn_list.append(barcode)

        sn_count = len(sn_list)
        self.log.info(f"条码数量：{sn_count}")

        end_time = pcb_entity.get_end_time().strftime(xcons.FMT_TIME_DEFAULT)

        ret_res = self.x_response()
        for board_entity in pcb_entity.yield_board_entity():
            self.log.info(board_entity)

            barcode = board_entity.barcode
            board_no = board_entity.board_no

            remark = []

            now = xutil.DateUtil.get_datetime_now("%Y-%m-%d %H:%M:%S")

            if sn_count == 0:
                board_sn = f"{time_file}-{board_no}"
            else:
                board_sn = barcode

            for comp_entity in board_entity.yield_comp_entity():
                if comp_entity.is_repair_ng():
                    comp_des = comp_entity.designator
                    user_ng_code = comp_entity.repair_ng_code
                    remark.append(f"{comp_des}_{user_ng_code}")

            param = {
                "pcbSeq": board_sn,
                "createdDateTime": now,
                "prodNo": prod_no,
                "result": "PASS" if board_entity.repair_result else "FAIL",
                "oriResult": "PASS" if board_entity.robot_result else "FAIL",
                "machineNo": machine_no,
                "threadNo": line_no,
                "beginTime": start_time,
                "endTime": end_time,
                "board": board_side,
                "remark": ",".join(remark),
                "companyNo": company_no,
                "mainPcbSeq": pcb_sn,
                "partSn": board_no,
                "trackName": str(pcb_entity.track_index),
                "badPointQty": str(board_entity.comp_repair_ng_number),
                "pointQty": str(board_entity.comp_total_number),
                "siteNo": station_no
            }

            # 2. 调用接口上传数据
            res = xrequest.RequestUtil.post_json(api_url_data, param)
            if str(res.get("result")) != "0":
                ret_res = self.x_response("false", f"接口响应异常, 上传数据失败, error: {res.get('message')}")

        return ret_res
